{"QRHardwareInvalidTransactionTitle": {"message": "Lỗi"}, "QRHardwareMismatchedSignId": {"message": "<PERSON><PERSON> liệu giao dịch không đồng nhất. <PERSON><PERSON> lòng kiểm tra chi tiết giao dịch."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "<PERSON>hông còn tài khoản nào. <PERSON><PERSON><PERSON> bạn muốn truy cập một tài khoản khác không được liệt kê bên dưới, vui lòng kết nối lại với ví cứng và chọn tài khoản đó."}, "QRHardwareScanInstructions": {"message": "Đặt mã QR phía trước máy ảnh. M<PERSON>n hình bị mờ nhưng không ảnh hưởng đến khả năng đọc."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON> chối"}, "QRHardwareSignRequestDescription": {"message": "<PERSON>u khi bạn đã ký bằng ví của mình, nh<PERSON><PERSON> và<PERSON> \"<PERSON><PERSON>y chữ ký\" để nhận chữ ký"}, "QRHardwareSignRequestGetSignature": {"message": "<PERSON><PERSON><PERSON> chữ ký"}, "QRHardwareSignRequestSubtitle": {"message": "Quét mã QR bằng ví của bạn"}, "QRHardwareSignRequestTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u chữ ký"}, "QRHardwareUnknownQRCodeTitle": {"message": "Lỗi"}, "QRHardwareUnknownWalletQRCode": {"message": "Mã QR không hợp lệ. <PERSON><PERSON> lòng quét mã QR đồng bộ của ví cứng."}, "QRHardwareWalletImporterTitle": {"message": "Quét mã QR"}, "QRHardwareWalletSteps1Description": {"message": "Bạn có thể chọn các đối tác hỗ trợ mã QR chính thức từ danh sách bên dướ<PERSON>."}, "QRHardwareWalletSteps1Title": {"message": "<PERSON>ết n<PERSON>i với ví cứng QR của bạn"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Ẩn $1 tài k<PERSON>n", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Ẩn 1 tài k<PERSON>n"}, "SrpListShowAccounts": {"message": "Hiển thị $1 tài kho<PERSON>n", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Hiển thị 1 tà<PERSON> k<PERSON>n"}, "about": {"message": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u"}, "accept": {"message": "<PERSON><PERSON><PERSON>"}, "acceptTermsOfUse": {"message": "Tôi đã đọc và đồng ý với $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "<PERSON><PERSON> truy cập m<PERSON>..."}, "account": {"message": "<PERSON><PERSON><PERSON>"}, "accountActivity": {"message": "<PERSON><PERSON><PERSON> động của tài <PERSON>n"}, "accountActivityText": {"message": "<PERSON>ọn tài khoản mà bạn muốn nhận thông báo:"}, "accountDetails": {"message": "<PERSON> tiết tài k<PERSON>n"}, "accountIdenticon": {"message": "<PERSON><PERSON><PERSON><PERSON> tượng nhận dạng tài kho<PERSON>n"}, "accountIsntConnectedToastText": {"message": "$1 không đ<PERSON><PERSON><PERSON> kết nối với $2"}, "accountName": {"message": "<PERSON><PERSON><PERSON> tà<PERSON>"}, "accountNameDuplicate": {"message": "Tên tài khoản này đã tồn tại", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "<PERSON>ên tài kho<PERSON>n nà<PERSON> đ<PERSON><PERSON><PERSON> b<PERSON><PERSON> l<PERSON>u", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n tài <PERSON>n"}, "accountPermissionToast": {"message": "<PERSON><PERSON> cập nhật quyền đối với tài k<PERSON>n"}, "accountSelectionRequired": {"message": "Bạn cần chọn một tài k<PERSON>n!"}, "accountTypeNotSupported": {"message": "<PERSON><PERSON>i tài khoản không được hỗ trợ"}, "accounts": {"message": "<PERSON><PERSON><PERSON>"}, "accountsConnected": {"message": "<PERSON><PERSON> kết nối tài k<PERSON>n"}, "accountsPermissionsTitle": {"message": "<PERSON>em tài k<PERSON>n của bạn và đề xuất giao dịch"}, "accountsSmallCase": {"message": "t<PERSON><PERSON>"}, "active": {"message": "<PERSON><PERSON> ho<PERSON>t động"}, "activity": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "activityLog": {"message": "<PERSON><PERSON><PERSON><PERSON> ký hoạt động"}, "add": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addACustomNetwork": {"message": "<PERSON>hê<PERSON> mạng tùy chỉnh"}, "addANetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>"}, "addANickname": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>nh"}, "addAUrl": {"message": "Thêm URL"}, "addAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "addAccountFromNetwork": {"message": "Thê<PERSON> tài k<PERSON> $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "<PERSON><PERSON><PERSON><PERSON> tài <PERSON>n vào MetaMask"}, "addAcquiredTokens": {"message": "Thêm token mà bạn đã mua bằng MetaMask"}, "addAlias": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>nh"}, "addBitcoinAccountLabel": {"message": "Tài khoản Bitcoin"}, "addBlockExplorer": {"message": "<PERSON>h<PERSON><PERSON> một trình khám phá khối"}, "addBlockExplorerUrl": {"message": "Thêm URL trình khám phá khối"}, "addContact": {"message": "<PERSON><PERSON><PERSON><PERSON> liên hệ"}, "addCustomNetwork": {"message": "<PERSON>hê<PERSON> mạng tùy chỉnh"}, "addEthereumChainWarningModalHeader": {"message": "Chỉ thêm nhà cung cấp RPC này nếu bạn chắc chắn bạn có thể tin tưởng. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "<PERSON><PERSON><PERSON> nhà cung cấp lừa đảo có thể nói dối về trạng thái của chuỗi khối và ghi lại hoạt động của bạn trên mạng."}, "addEthereumChainWarningModalListHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> quan trọng là nhà cung cấp của bạn phải đáng tin cậy vì họ có khả năng:"}, "addEthereumChainWarningModalListPointOne": {"message": "Xem tài khoản và địa chỉ IP của bạn và liên kết chúng lại với nhau"}, "addEthereumChainWarningModalListPointThree": {"message": "Hi<PERSON>n thị số dư tài khoản và các trạng thái khác trên chuỗi"}, "addEthereumChainWarningModalListPointTwo": {"message": "<PERSON><PERSON><PERSON> c<PERSON>c giao d<PERSON>ch c<PERSON>a b<PERSON>n"}, "addEthereumChainWarningModalTitle": {"message": "Bạn đang thêm một nhà cung cấp RPC mới cho Ethereum Mainnet"}, "addEthereumWatchOnlyAccount": {"message": "<PERSON> t<PERSON>n Ethereum (Beta)"}, "addFriendsAndAddresses": {"message": "Thê<PERSON> bạn bè và địa chỉ mà bạn tin tưởng"}, "addHardwareWalletLabel": {"message": "<PERSON><PERSON> c<PERSON>"}, "addIPFSGateway": {"message": "<PERSON><PERSON><PERSON><PERSON> cổng IPFS <PERSON>a thích của bạn"}, "addImportAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON> hoặc ví cứng"}, "addMemo": {"message": "<PERSON><PERSON><PERSON><PERSON> bản ghi nhớ"}, "addNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>"}, "addNetworkConfirmationTitle": {"message": "Thêm $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "<PERSON>h<PERSON><PERSON> tài k<PERSON>ản Ethereum mới"}, "addNewEthereumAccountLabel": {"message": "Tài khoản Ethereum"}, "addNewSolanaAccountLabel": {"message": "<PERSON><PERSON><PERSON>"}, "addNft": {"message": "Thêm NFT"}, "addNfts": {"message": "Thêm NFT"}, "addNonEvmAccount": {"message": "Thê<PERSON> tài k<PERSON> $1", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "<PERSON><PERSON> kích hoạt mạng $1, bạn cần tạo tài kho<PERSON>n $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Thêm URL RPC"}, "addSnapAccountToggle": {"message": "<PERSON><PERSON><PERSON> \"<PERSON>hê<PERSON> tài <PERSON> (Beta)\""}, "addSnapAccountsDescription": {"message": "Bật tính năng này sẽ cho phép bạn thêm tài khoản <PERSON>nap (Beta) mới ngay từ danh sách tài khoản của bạn. Nếu bạn cài đặt tài khoản Snap, hãy nhớ rằng đây là một dịch vụ của bên thứ ba."}, "addSuggestedNFTs": {"message": "Thêm NFT được đề xuất"}, "addSuggestedTokens": {"message": "Thêm token đư<PERSON><PERSON> đề xuất"}, "addToken": {"message": "Thêm token"}, "addTokenByContractAddress": {"message": "Bạn không tìm thấy token? Bạn có thể dán địa chỉ của bất kỳ token nào để thêm token đó theo cách thủ công. Bạn có thể tìm thấy địa chỉ hợp đồng token trên $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Thêm URL"}, "addingAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "addingCustomNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>"}, "additionalNetworks": {"message": "<PERSON><PERSON><PERSON> b<PERSON> sung"}, "address": {"message": "Địa chỉ"}, "addressCopied": {"message": "Đã sao chép địa chỉ!"}, "addressMismatch": {"message": "Địa chỉ trang web không khớp"}, "addressMismatchOriginal": {"message": "URL hiện tại: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Phiên bản <PERSON>unycode: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "<PERSON><PERSON><PERSON> cao"}, "advancedBaseGasFeeToolTip": {"message": "<PERSON><PERSON> các giao dịch của bạn được đưa vào khố<PERSON>, mọi phần chênh lệch giữa phí cơ sở tối đa và phí cơ sở thực tế đều sẽ được hoàn lại. Tổng số tiền sẽ được tính bằng phí cơ sở tối đa (theo GWEI) * hạn mức phí gas."}, "advancedDetailsDataDesc": {"message": "<PERSON><PERSON> liệu"}, "advancedDetailsHexDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> phân"}, "advancedDetailsNonceDesc": {"message": "Số nonce"}, "advancedDetailsNonceTooltip": {"message": "Đây là số giao dịch của một tài k<PERSON>n. Số nonce cho giao dịch đầu tiên là 0 và tăng dần theo thứ tự."}, "advancedGasFeeDefaultOptIn": {"message": "<PERSON><PERSON><PERSON> c<PERSON>c giá trị này làm giá trị mặc định cho mạng $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Phí gas nâng cao"}, "advancedGasPriceTitle": {"message": "Giá gas"}, "advancedPriorityFeeToolTip": {"message": "Phí ưu tiên (hay còn được gọi là \"tiền thưởng cho thợ đào\") được chuyển trực tiếp cho các thợ đào và khuyến khích họ ưu tiên giao dịch của bạn."}, "airDropPatternDescription": {"message": "<PERSON><PERSON><PERSON> sử trên chuỗi của token cho thấy trước đây đã từng có hoạt động tặng thưởng đáng ngờ."}, "airDropPatternTitle": {"message": "<PERSON><PERSON> hình tặng thưởng"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o"}, "alertAccountTypeUpgradeMessage": {"message": "Bạn đang nâng cấp tài khoản lên tài khoản thông minh. Địa chỉ tài khoản hiện tại của bạn sẽ được giữ nguyên, nh<PERSON><PERSON> giao dịch sẽ nhanh hơn và phí mạng sẽ thấp hơn. $1."}, "alertAccountTypeUpgradeTitle": {"message": "<PERSON><PERSON><PERSON> tà<PERSON>"}, "alertActionBuyWithNativeCurrency": {"message": "Mua $1"}, "alertActionUpdateGas": {"message": "<PERSON><PERSON><PERSON> nhập hạn mức phí gas"}, "alertActionUpdateGasFee": {"message": "<PERSON><PERSON><PERSON> nhật phí"}, "alertActionUpdateGasFeeLevel": {"message": "<PERSON><PERSON><PERSON> nhật tùy chọn phí gas"}, "alertDisableTooltip": {"message": "Bạn có thể thay đổi trong phần \"Cài đặt > C<PERSON><PERSON> báo\""}, "alertMessageAddressMismatchWarning": {"message": "Kẻ tấn công đôi khi bắt chước các trang web bằng cách thực hiện những thay đổi nhỏ trong địa chỉ trang web. <PERSON><PERSON><PERSON> bảo bạn đang tương tác với trang web dự định trước khi tiếp tục."}, "alertMessageChangeInSimulationResults": {"message": "<PERSON><PERSON><PERSON> thay đổi ước tính cho giao dịch này đã đư<PERSON>c cập nhật. H<PERSON>y xem xét kỹ trước khi tiếp tục."}, "alertMessageFirstTimeInteraction": {"message": "Bạn đang tương tác với địa chỉ này lần đầu tiên. <PERSON><PERSON>y đảm bảo địa chỉ này chính xác trước khi tiếp tục."}, "alertMessageGasEstimateFailed": {"message": "Chúng tôi không thể cung cấp phí chính xác và ước tính này có thể cao. Chúng tôi khuyên bạn nên nhập hạn mức phí gas tùy chỉnh, nhưng vẫn có rủi ro giao dịch sẽ thất bại."}, "alertMessageGasFeeLow": {"message": "<PERSON><PERSON> chọn phí thấp, h<PERSON><PERSON> lưu ý giao dịch sẽ chậm hơn và thời gian chờ đợi lâu hơn. <PERSON><PERSON> giao dị<PERSON> h<PERSON>, h<PERSON><PERSON> chọn các tùy chọn phí Thị trường hoặc Cao."}, "alertMessageGasTooLow": {"message": "<PERSON><PERSON> tiếp tục giao d<PERSON>, bạn cần tăng giới hạn phí gas lên 21000 hoặc cao hơn."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Bạn không có đủ $1 trong tài khoản để thanh toán phí mạng."}, "alertMessageNetworkBusy": {"message": "Phí gas cao và ước t<PERSON>h kém ch<PERSON>h xác h<PERSON>n."}, "alertMessageNoGasPrice": {"message": "<PERSON><PERSON><PERSON> tôi không thể tiếp tục giao dịch này cho đến khi bạn cập nhật phí thủ công."}, "alertMessageSignInDomainMismatch": {"message": "Trang web đưa ra yêu cầu không phải là trang web bạn đang đăng nhập. <PERSON><PERSON><PERSON> có thể là một nỗ lực đánh cắp thông tin đăng nhập của bạn."}, "alertMessageSignInWrongAccount": {"message": "Trang web này yêu cầu bạn đăng nhập bằng tài khoản không đúng."}, "alertModalAcknowledge": {"message": "T<PERSON>i đã nhận thức được rủi ro và vẫn muốn tiếp tục"}, "alertModalDetails": {"message": "<PERSON> tiết cảnh báo"}, "alertModalReviewAllAlerts": {"message": "<PERSON><PERSON> lại tất cả cảnh báo"}, "alertReasonChangeInSimulationResults": {"message": "<PERSON><PERSON><PERSON> quả đã thay đổi"}, "alertReasonFirstTimeInteraction": {"message": "<PERSON><PERSON><PERSON><PERSON> tác lần đầu tiên"}, "alertReasonGasEstimateFailed": {"message": "<PERSON><PERSON> không ch<PERSON>h xác"}, "alertReasonGasFeeLow": {"message": "<PERSON><PERSON><PERSON> độ chậm"}, "alertReasonGasTooLow": {"message": "<PERSON><PERSON><PERSON> mức phí gas thấp"}, "alertReasonInsufficientBalance": {"message": "<PERSON><PERSON><PERSON><PERSON> đủ tiền"}, "alertReasonNetworkBusy": {"message": "<PERSON><PERSON><PERSON> đang bận"}, "alertReasonNoGasPrice": {"message": "Ước tính phí không có sẵn"}, "alertReasonPendingTransactions": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đang chờ xử lý"}, "alertReasonSignIn": {"message": "<PERSON><PERSON><PERSON> cầu đăng nhập đáng ngờ"}, "alertReasonWrongAccount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n không đ<PERSON>g"}, "alertSelectedAccountWarning": {"message": "<PERSON><PERSON><PERSON> cầu này dành cho một tài khoản khác với tài khoản được chọn trong ví của bạn. <PERSON><PERSON> sử dụng tài khoản kh<PERSON>c, hã<PERSON> kết nối tài khoản đó với trang web."}, "alerts": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o"}, "all": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "allNetworks": {"message": "<PERSON><PERSON><PERSON> cả mạng"}, "allPermissions": {"message": "<PERSON><PERSON><PERSON> cả các quyền"}, "allTimeHigh": {"message": "<PERSON> nh<PERSON>t lịch sử"}, "allTimeLow": {"message": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON><PERSON> lịch sử"}, "allowNotifications": {"message": "<PERSON> phép thông báo"}, "allowWithdrawAndSpend": {"message": "Cho phép $1 rút và chi tiêu tối đa số tiền sau đây:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "<PERSON><PERSON> tiền"}, "amountReceived": {"message": "<PERSON><PERSON> tiền đã nhận"}, "amountSent": {"message": "Số tiền đã gửi"}, "andForListItems": {"message": "$1, và $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 và $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "<PERSON><PERSON> tiền mã hóa đáng tin cậy nhất thế giới", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "<PERSON><PERSON>"}, "approve": {"message": "<PERSON><PERSON><PERSON> thuận hạn mức chi tiêu"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "T<PERSON>ng hạn mức chi tiêu $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "<PERSON><PERSON><PERSON><PERSON> hạn mức chi tiêu $1", "description": "The token symbol that is being approved"}, "approved": {"message": "<PERSON><PERSON> chấp th<PERSON>n"}, "approvedOn": {"message": "<PERSON><PERSON> chấp thuận vào $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Đã phê duyệt vào $1 cho $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Bạn có chắc chắn không?"}, "asset": {"message": "<PERSON><PERSON><PERSON>"}, "assetChartNoHistoricalPrices": {"message": "<PERSON><PERSON><PERSON> tôi không thể tìm nạp bất kỳ dữ liệu lịch sử nào"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "<PERSON><PERSON><PERSON> chọn tài sản"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "<PERSON><PERSON><PERSON>"}, "assetsDescription": {"message": "Tự động phát hiện token trong ví của bạn, hiển thị NFT và nhận hàng loạt thông tin cập nhật về số dư tài k<PERSON>n"}, "attemptToCancelSwapForFree": {"message": "<PERSON><PERSON> gắng hủy hoán đổi miễn phí"}, "attributes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "attributions": {"message": "<PERSON><PERSON> <PERSON>h<PERSON>n đóng góp"}, "auroraRpcDeprecationMessage": {"message": "URL Infura RPC không còn hỗ trợ Aurora nữa."}, "authorizedPermissions": {"message": "Bạn đã cấp các quyền sau đây"}, "autoDetectTokens": {"message": "Tự động phát hiện token"}, "autoDetectTokensDescription": {"message": "<PERSON>úng tôi sử dụng API của bên thứ ba để phát hiện và hiển thị các token mới được gửi đến ví của bạn. Hãy tắt nếu bạn không muốn ứng dụng tự động lấy dữ liệu từ các dịch vụ đó. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Hẹn giờ tự động khóa (phút)"}, "autoLockTimeLimitDescription": {"message": "Đặt khoảng thời gian không hoạt động tính bằng phút trước khi MetaMask khóa."}, "average": {"message": "<PERSON>rung bình"}, "back": {"message": "Quay lại"}, "backupAndSync": {"message": "Sao l<PERSON>u và đồng bộ"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "<PERSON><PERSON><PERSON> n<PERSON> c<PERSON> bản"}, "backupAndSyncEnable": {"message": "Bật sao lưu và đồng bộ"}, "backupAndSyncEnableConfirmation": {"message": "<PERSON>hi bạn bật sao lưu và đồng bộ, bạn cũng sẽ bật $1. Bạn có muốn tiếp tục không?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "Sao lưu và đồng bộ cho phép chúng tôi lưu trữ dữ liệu được mã hóa cho các chế độ cài đặt và tính năng tùy chỉnh của bạn. Điều này giúp trải nghiệm sử dụng MetaMask của bạn nhất quán trên các thiết bị, đồng thời giúp khôi phục chế độ cài đặt và tính năng nếu bạn cần cài đặt lại MetaMask. Thao tác này không sao lưu Cụm từ khôi phục bí mật của bạn. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Bạn có thể cập nhật tùy chọn của mình bất cứ lúc nào trong phần $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Cài đặt > <PERSON><PERSON> l<PERSON><PERSON> và đồng bộ."}, "backupAndSyncFeatureAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "backupAndSyncManageWhatYouSync": {"message": "<PERSON><PERSON><PERSON><PERSON> lý những gì đư<PERSON>c đồng bộ"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "<PERSON><PERSON>t những gì được đồng bộ giữa các thiết bị của bạn."}, "backupAndSyncPrivacyLink": {"message": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> c<PERSON>ch chúng tôi bảo vệ quyền riêng tư của bạn"}, "backupAndSyncSlideDescription": {"message": "<PERSON><PERSON> lưu tài khoản và đồng bộ các chế độ cài đặt của bạn."}, "backupAndSyncSlideTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> thiệu về sao lưu và đồng bộ"}, "backupApprovalInfo": {"message": "<PERSON><PERSON><PERSON> là mã bí mật bắt buộc phải dùng để khôi phục ví trong trường hợp bạn bị mất thiết bị, quên mật khẩu, phải cài đặt lại MetaMask hoặc muốn truy cập ví của mình trên một thiết bị khác."}, "backupApprovalNotice": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> từ khôi phục bí mật để đảm bảo an toàn cho ví và tiền của bạn."}, "backupKeyringSnapReminder": {"message": "<PERSON><PERSON><PERSON> bảo bạn có thể tự mình truy cập bất kỳ tài khoản nào được tạo bởi Snap này trước khi xóa nó"}, "backupNow": {"message": "<PERSON><PERSON> l<PERSON><PERSON> ngay"}, "balance": {"message": "Số dư"}, "balanceOutdated": {"message": "Số dư có thể đã cũ"}, "baseFee": {"message": "<PERSON><PERSON> cơ sở"}, "basic": {"message": "<PERSON><PERSON> bản"}, "basicConfigurationBannerTitle": {"message": "<PERSON><PERSON><PERSON> n<PERSON>ng cơ bản đã tắt"}, "basicConfigurationDescription": {"message": "MetaMask cung cấp các tính năng cơ bản như chi tiết token và cài đặt gas thông qua các dịch vụ Internet. Khi bạn sử dụng các dịch vụ Internet, địa chỉ IP của bạn sẽ được chia sẻ, trong trường hợp này là với MetaMask. Điều này giống như khi bạn truy cập bất kỳ trang web nào. MetaMask sử dụng dữ liệu này tạm thời và không bao giờ bán dữ liệu của bạn. Bạn có thể sử dụng VPN hoặc tắt các dịch vụ này, nhưng nó có thể ảnh hưởng đến trải nghiệm sử dụng MetaMask của bạn. Đ<PERSON> tìm hiểu thêm, vui lòng đọc $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "<PERSON><PERSON><PERSON> n<PERSON> c<PERSON> bản"}, "basicConfigurationModalCheckbox": {"message": "T<PERSON><PERSON> hiểu rõ và muốn tiếp tục"}, "basicConfigurationModalDisclaimerOff": {"message": "<PERSON>i<PERSON>u này có nghĩa là bạn sẽ không thể tối ưu hoàn toàn thời gian sử dụng MetaMask. <PERSON><PERSON><PERSON> tính năng cơ bản (chẳng hạn như chi tiết token, cài đặt gas tối ưu và các tính năng khác) sẽ không khả dụng."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Tắt tùy chọn này cũng sẽ vô hiệu hóa tất cả các tính năng trong $1 và $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "bảo mật và quyền riêng tư, sao lưu và đồng bộ"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "thông báo"}, "basicConfigurationModalDisclaimerOn": {"message": "<PERSON><PERSON> tối ưu thời gian sử dụng <PERSON>, bạn cần bật tính năng này. <PERSON><PERSON><PERSON> chức năng c<PERSON> bản (chẳng hạn như chi tiết token, cài đặt gas tối ưu và các chức năng kh<PERSON>c) rất quan trọng đối với trải nghiệm web3."}, "basicConfigurationModalHeadingOff": {"message": "<PERSON><PERSON><PERSON> chứ<PERSON> n<PERSON>ng c<PERSON> bản"}, "basicConfigurationModalHeadingOn": {"message": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> n<PERSON>ng c<PERSON> bản"}, "bestPrice": {"message": "<PERSON><PERSON><PERSON> t<PERSON>t nh<PERSON>t"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Đ<PERSON>y là một phiên bản beta. <PERSON><PERSON> lòng báo cáo lỗi $1"}, "betaMetamaskVersion": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "betaTerms": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>n sử dụng phiên bản <PERSON>"}, "billionAbbreviation": {"message": "Tỷ", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "URL trình khám phá khối"}, "blockExplorerUrlDefinition": {"message": "URL được dùng làm trình khám phá khối cho mạng này."}, "blockExplorerView": {"message": "<PERSON>em tài k<PERSON>n tại $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> t<PERSON>, tất cả tài sản bạn đã niêm yết trên Blur có thể gặp rủi ro."}, "blockaidAlertDescriptionMalicious": {"message": "Bạn đang tương tác với một trang web độc hại. <PERSON><PERSON><PERSON> tiế<PERSON> tục, bạn sẽ bị mất tài sản."}, "blockaidAlertDescriptionOpenSea": {"message": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON>, tất cả tài sản bạn đã niêm yết trên OpenSea có thể gặp rủi ro."}, "blockaidAlertDescriptionOthers": {"message": "<PERSON><PERSON>u bạn xác nhận yêu cầu này, bạn có thể bị mất tài sản. <PERSON><PERSON>g tôi khuyên bạn nên hủy yêu cầu này."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Bạn đang gửi tài sản của mình cho kẻ lừa đảo. <PERSON><PERSON><PERSON> tiế<PERSON> tục, bạn sẽ bị mất những tài sản đó."}, "blockaidAlertDescriptionWithdraw": {"message": "Nếu bạn xác nhận yêu cầu này, bạn đang cho phép kẻ lừa đảo rút và chi tiêu tài sản của mình. Bạn sẽ không lấy lại được tài sản."}, "blockaidDescriptionApproveFarming": {"message": "<PERSON><PERSON><PERSON> bạn chấp thuận yêu cầu nà<PERSON>, một bên thứ ba nổi tiếng là lừa đảo có thể lấy hết tài sản của bạn."}, "blockaidDescriptionBlurFarming": {"message": "<PERSON><PERSON><PERSON> bạn chấp thuận yêu cầu <PERSON>, ng<PERSON><PERSON><PERSON> khác có thể đánh cắp tài sản đư<PERSON><PERSON> niêm yết trên Blur của bạn."}, "blockaidDescriptionErrored": {"message": "<PERSON> có lỗi, chúng tôi không thể kiểm tra các cảnh báo bảo mật. Chỉ tiếp tục nếu bạn tin tưởng tất cả các địa chỉ liên quan."}, "blockaidDescriptionMaliciousDomain": {"message": "Bạn đang tương tác với một tên miền độc hại. <PERSON><PERSON>u bạn chấp thuận yêu cầu nà<PERSON>, bạn có thể mất tài sản của mình."}, "blockaidDescriptionMightLoseAssets": {"message": "<PERSON><PERSON>u bạn chấp thuận yêu cầu nà<PERSON>, bạn có thể mất tài sản của mình."}, "blockaidDescriptionSeaportFarming": {"message": "<PERSON><PERSON><PERSON> bạn chấp thuận yêu cầ<PERSON>, ng<PERSON><PERSON><PERSON> khác có thể đánh cắp tài sản đư<PERSON><PERSON> niêm yết trên OpenSea của bạn."}, "blockaidDescriptionTransferFarming": {"message": "<PERSON><PERSON>u bạn chấp thuận yêu cầu nà<PERSON>, một bên thứ ba nổi tiếng là lừa đảo sẽ lấy hết tài sản của bạn."}, "blockaidMessage": {"message": "Bảo vệ quyền riêng tư - không có dữ liệu nào được chia sẻ với các bên thứ ba. Có sẵn trên Arbitrum, Avalanche, BNB chain, Ethereum Mainnet, Linea, Optimism, Polygon, Base và Sepolia."}, "blockaidTitleDeceptive": {"message": "<PERSON><PERSON><PERSON> là một yêu cầu lừa đảo"}, "blockaidTitleMayNotBeSafe": {"message": "<PERSON><PERSON><PERSON> cẩn thận"}, "blockaidTitleSuspicious": {"message": "<PERSON><PERSON><PERSON> là một yêu cầu đáng ngờ"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Đã vay"}, "boughtFor": {"message": "Đã mua với giá"}, "bridge": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeAllowSwappingOf": {"message": "Cho phép truy cập ch<PERSON>h xác vào $1 $2 trên $3 để thực hiện cầu nối", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "<PERSON>ê duyệt $1 cho cầu nối", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Bạn đang cho phép truy cập vào số tiền cụ thể, $1 $2. H<PERSON><PERSON> đồng sẽ không truy cập thêm bất kỳ khoản tiền nào kh<PERSON>c."}, "bridgeApprovalWarningForHardware": {"message": "Bạn sẽ cần cho phép truy cập vào $1 $2 để thực hiện cầu nối, sau đó hãy phê duyệt việc thực hiện cầu nối sang $2. Điều này sẽ yêu cầu hai lần xác nhận riêng biệt."}, "bridgeBlockExplorerLinkCopied": {"message": "Đã sao chép liên kết trình khám phá khối!"}, "bridgeCalculatingAmount": {"message": "<PERSON><PERSON> t<PERSON> toán..."}, "bridgeConfirmTwoTransactions": {"message": "Bạn sẽ cần xác nhận 2 giao dịch trên ví cứng của mình:"}, "bridgeCreateSolanaAccount": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "bridgeCreateSolanaAccountDescription": {"message": "<PERSON><PERSON> ho<PERSON> đổi sang mạng <PERSON>, bạn cần có tài khoản và địa chỉ nhận."}, "bridgeCreateSolanaAccountTitle": {"message": "Bạn sẽ cần có tài khoản Solana trước."}, "bridgeDetailsTitle": {"message": "<PERSON> ti<PERSON>t c<PERSON>u n<PERSON>i", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "<PERSON><PERSON><PERSON> số tiền"}, "bridgeEnterAmountAndSelectAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> số tiền và chọn tài khoản đích"}, "bridgeExplorerLinkViewOn": {"message": "Xem trên $1"}, "bridgeFetchNewQuotes": {"message": "Tìm nạp báo giá mới?"}, "bridgeFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> từ"}, "bridgeFromTo": {"message": "<PERSON><PERSON><PERSON> n<PERSON> $1 $2 sang $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "<PERSON><PERSON>t kỳ khoản phí mạng nào được báo trên màn hình trước đó đều bao gồm cả hai giao dịch và sẽ được chia nhỏ."}, "bridgeNetCost": {"message": "Chi phí ròng"}, "bridgeQuoteExpired": {"message": "<PERSON><PERSON>o giá của bạn đã hết hạn."}, "bridgeSelectDestinationAccount": {"message": "<PERSON><PERSON><PERSON> tài k<PERSON>n đích"}, "bridgeSelectNetwork": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "bridgeSelectTokenAmountAndAccount": {"message": "<PERSON><PERSON>n token, số tiền và tài khoản đích"}, "bridgeSelectTokenAndAmount": {"message": "Chọn token và số tiền"}, "bridgeSolanaAccountCreated": {"message": "Đã tạo tài k<PERSON>"}, "bridgeStatusComplete": {"message": "<PERSON><PERSON><PERSON> t<PERSON>t", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "<PERSON><PERSON> ti<PERSON>n hành", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "Đã nhận $1 trên $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Đang n<PERSON>n $1 trên $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "Đ<PERSON> hoán đổi $1 thành $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "<PERSON><PERSON> hoán đổi $1 thành $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "bridgeTimingMinutes": {"message": "$1 phút", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON>n"}, "bridgeToChain": {"message": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> đến $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Nếu bạn đã thêm token này theo cách thủ công, hãy đảm bảo rằng bạn nhận thức được các rủi ro đối với tiền của mình trước khi thực hiện cầu nối."}, "bridgeTokenCannotVerifyTitle": {"message": "<PERSON><PERSON>g tôi không thể xác minh token này."}, "bridgeTransactionProgress": {"message": "Giao d<PERSON> $1/2"}, "bridgeTxDetailsBridging": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeTxDetailsDelayedDescription": {"message": "<PERSON><PERSON><PERSON> v<PERSON>i"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Hỗ trợ <PERSON>"}, "bridgeTxDetailsDelayedTitle": {"message": "Đã quá 3 giờ chưa?"}, "bridgeTxDetailsNonce": {"message": "Số nonce"}, "bridgeTxDetailsStatus": {"message": "<PERSON><PERSON><PERSON><PERSON> thái"}, "bridgeTxDetailsTimestamp": {"message": "<PERSON><PERSON><PERSON> thời gian"}, "bridgeTxDetailsTimestampValue": {"message": "$1 lúc $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 trên", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Tổng phí gas"}, "bridgeTxDetailsYouReceived": {"message": "Bạn đã nhận"}, "bridgeTxDetailsYouSent": {"message": "Bạn đã gửi"}, "bridgeValidationInsufficientGasMessage": {"message": "Bạn không có đủ $1 để thanh toán phí gas cho cầu nối này. Nhập số tiền nhỏ hơn hoặc mua thêm $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Cần thêm $1 để trả phí gas"}, "bridging": {"message": "<PERSON><PERSON><PERSON>"}, "browserNotSupported": {"message": "Trì<PERSON> du<PERSON> của bạn không được hỗ trợ..."}, "buildContactList": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ng danh sách liên hệ của bạn"}, "builtAroundTheWorld": {"message": "MetaMask đư<PERSON><PERSON> thiết kế và xây dựng trên khắp thế giới."}, "bulletpoint": {"message": "·"}, "busy": {"message": "<PERSON><PERSON> b<PERSON>n"}, "buyAndSell": {"message": "Mua/Bán"}, "buyMoreAsset": {"message": "<PERSON>a thêm $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "<PERSON><PERSON> ngay"}, "bytes": {"message": "Byte"}, "canToggleInSettings": {"message": "Bạn có thể bật lại thông báo này trong phần Cài đặt > <PERSON><PERSON><PERSON> báo."}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "cancelPopoverTitle": {"message": "<PERSON><PERSON><PERSON> giao d<PERSON>ch"}, "cancelSpeedUpLabel": {"message": "Phí gas này sẽ $1 phí gas ban đầu.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Để $1 một giao dịch, phí gas phải tăng tối thiểu 10% để mạng nhận ra giao dịch này.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "<PERSON><PERSON> hủy"}, "chainId": {"message": "ID chuỗi"}, "chainIdDefinition": {"message": "ID chuỗi được dùng để ký các giao dịch cho mạng này."}, "chainIdExistsErrorMsg": {"message": "Mạng $1 hiện đang sử dụng ID chuỗi này."}, "chainListReturnedDifferentTickerSymbol": {"message": "Biể<PERSON> tượng token này không trùng khớp với tên mạng hoặc ID chuỗi đã nhập. Nhiều token phổ biến sử dụng các biểu tượng tương tự, do đó những kẻ lừa đảo có thể lợi dụng điều này để lừa bạn gửi cho chúng một token có giá trị hơn. Nhớ xác minh mọi thông tin trước khi tiếp tục."}, "chooseYourNetwork": {"message": "<PERSON><PERSON><PERSON> mạng của bạn"}, "chooseYourNetworkDescription": {"message": "<PERSON><PERSON> bạn sử dụng các chế độ cài đặt và cấu hình mặc định của chúng tôi, chúng tôi sử dụng Infura làm nhà cung cấp gọi hàm từ xa (RPC) mặc định để cung cấp quyền truy cập dữ liệu Ethereum đáng tin cậy và riêng tư nhất có thể. Trong một số trường hợp giớ<PERSON> hạn, chúng tôi có thể sử dụng các nhà cung cấp RPC khác nhằm mang đến trải nghiệm tốt nhất cho người dùng. Bạn có thể chọn RPC của riêng mình, nhưng hãy nhớ rằng bất kỳ RPC nào cũng sẽ nhận được địa chỉ IP và ví Ethereum của bạn để thực hiện giao dịch. <PERSON>ể tìm hiểu thêm về cách Infura xử lý dữ liệu đối với các tài khoản EVM, hãy đọc $1, và đối với các tài khoản Solana, hãy đọc $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "nhấp vào đ<PERSON>y"}, "chromeRequiredForHardwareWallets": {"message": "Bạn cần sử dụng MetaMask trên Google Chrome để kết nối với Ví cứng của bạn."}, "circulatingSupply": {"message": "<PERSON><PERSON> lưu h<PERSON>nh"}, "clear": {"message": "Xóa"}, "clearActivity": {"message": "Xóa dữ liệu hoạt động và số nonce"}, "clearActivityButton": {"message": "<PERSON><PERSON><PERSON> dữ liệu thẻ hoạt động"}, "clearActivityDescription": {"message": "<PERSON><PERSON> tác này sẽ đặt lại số nonce của tài khoản và xóa dữ liệu khỏi thẻ hoạt động trong ví của bạn. Chỉ có tài khoản và mạng hiện tại bị ảnh hưởng. Số dư và các giao dịch đến của bạn sẽ không thay đổi."}, "click": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "clickToConnectLedgerViaWebHID": {"message": "<PERSON>hấ<PERSON> vào đây để kết nối với ví Ledger của bạn qua WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "Đ<PERSON><PERSON>"}, "closeExtension": {"message": "<PERSON><PERSON><PERSON> tiện ích mở rộng"}, "closeWindowAnytime": {"message": "<PERSON><PERSON><PERSON> có thể đóng cửa sổ này bất kỳ lúc nào."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "<PERSON><PERSON><PERSON> bộ s<PERSON>u tập"}, "comboNoOptions": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tùy chọn nào", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "Phần lớn nguồn cung token được những người nắm giữ token hàng đầu nắm giữ, g<PERSON><PERSON> rủi ro thao túng giá tập trung"}, "concentratedSupplyDistributionTitle": {"message": "<PERSON><PERSON> bổ nguồn cung tập trung"}, "configureSnapPopupDescription": {"message": "Bây giờ bạn đang rời MetaMask để định cấu hình <PERSON>nap này."}, "configureSnapPopupInstallDescription": {"message": "Bây giờ bạn đang rời MetaMask để cài đặt Snap này."}, "configureSnapPopupInstallTitle": {"message": "Cài đặt Snap"}, "configureSnapPopupLink": {"message": "<PERSON><PERSON><PERSON><PERSON> vào liên kết này để tiếp tục:"}, "configureSnapPopupTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh <PERSON>"}, "confirm": {"message": "<PERSON><PERSON><PERSON>"}, "confirmAccountTypeSmartContract": {"message": "<PERSON><PERSON><PERSON>n thông minh"}, "confirmAccountTypeStandard": {"message": "<PERSON><PERSON><PERSON>n ti<PERSON><PERSON> ch<PERSON>n"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Tôi đã hiểu rõ các cảnh báo và vẫn muốn tiếp tục"}, "confirmAlertModalAcknowledgeSingle": {"message": "Tôi đã hiểu rõ cảnh báo và vẫn muốn tiếp tục"}, "confirmFieldPaymaster": {"message": "<PERSON><PERSON> đ<PERSON>h toán bởi"}, "confirmFieldTooltipPaymaster": {"message": "<PERSON><PERSON> cho giao dịch này sẽ được thanh toán bởi hợp đồng thông minh Paymaster."}, "confirmGasFeeTokenBalance": {"message": "Số dư:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "<PERSON><PERSON><PERSON><PERSON> đủ tiền"}, "confirmGasFeeTokenMetaMaskFee": {"message": "Bao gồm phí $1"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "MetaMask đang ước tính số dư để hoàn tất giao dịch này."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "<PERSON><PERSON> toán phí mạng bằng số dư trong ví của bạn."}, "confirmGasFeeTokenModalPayETH": {"message": "Thanh toán bằng ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Thanh toán bằng token khác"}, "confirmGasFeeTokenModalTitle": {"message": "<PERSON><PERSON><PERSON> m<PERSON>t token"}, "confirmGasFeeTokenToast": {"message": "Bạn đang thanh toán phí mạng này bằng $1"}, "confirmGasFeeTokenTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON> này đư<PERSON>h toán cho mạng để xử lý giao dịch của bạn. <PERSON><PERSON> gồm phí $1 của MetaMask cho các token không phải ETH hoặc ETH được nạp sẵn."}, "confirmInfoAccountNow": {"message": "Bây giờ"}, "confirmInfoSwitchingTo": {"message": "<PERSON><PERSON><PERSON><PERSON> sang"}, "confirmNestedTransactionTitle": {"message": "Giao dịch $1"}, "confirmPassword": {"message": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u"}, "confirmRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON> nhận <PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "confirmSimulationApprove": {"message": "<PERSON><PERSON><PERSON> chấp thu<PERSON>n"}, "confirmTitleAccountTypeSwitch": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t tà<PERSON>"}, "confirmTitleApproveTransactionNFT": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u rút tiền"}, "confirmTitleDeployContract": {"message": "<PERSON><PERSON><PERSON> khai hợp đồng"}, "confirmTitleDescApproveTransaction": {"message": "Trang web này muốn đư<PERSON><PERSON> cấp quyền để rút NFT của bạn"}, "confirmTitleDescDelegationRevoke": {"message": "Bạn đang chuyển về tài khoản tiêu chuẩ<PERSON> (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "<PERSON><PERSON><PERSON> đang chuy<PERSON> sang tài khoản thông minh"}, "confirmTitleDescDeployContract": {"message": "Trang web này muốn bạn triển khai hợp đồng"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Trang web này muốn đ<PERSON><PERSON><PERSON> cấp quyền để rút token của bạn"}, "confirmTitleDescPermitSignature": {"message": "Trang web này muốn đư<PERSON><PERSON> cấp quyền để chi tiêu số token của bạn."}, "confirmTitleDescSIWESignature": {"message": "Một trang web yêu cầu bạn đăng nhập để chứng minh quyền sở hữu tài khoản này."}, "confirmTitleDescSign": {"message": "<PERSON>em lại thông tin yêu cầu trư<PERSON>c khi xác nhận."}, "confirmTitlePermitTokens": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u hạn mức chi tiêu"}, "confirmTitleRevokeApproveTransaction": {"message": "<PERSON><PERSON><PERSON> q<PERSON>"}, "confirmTitleSIWESignature": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u đ<PERSON>ng <PERSON>h<PERSON>p"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "<PERSON><PERSON><PERSON> q<PERSON>"}, "confirmTitleSignature": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u chữ ký"}, "confirmTitleTransaction": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u giao d<PERSON>ch"}, "confirmationAlertDetails": {"message": "<PERSON><PERSON> bảo vệ tài sản củ<PERSON> bạn, chúng tôi đề nghị bạn từ chối yêu cầu này."}, "confirmationAlertModalTitleDescription": {"message": "<PERSON><PERSON><PERSON> sản của bạn có thể gặp rủi ro"}, "confirmed": {"message": "<PERSON><PERSON> x<PERSON>c <PERSON>n"}, "confusableUnicode": {"message": "\"$1\" tương tự với \"$2\"."}, "confusableZeroWidthUnicode": {"message": "<PERSON><PERSON><PERSON> thấy ký tự có độ rộng bằng 0."}, "confusingEnsDomain": {"message": "<PERSON><PERSON>g tôi đã phát hiện thấy một ký tự có thể gây nhầm lẫn trong tên ENS. Hãy kiểm tra tên ENS để tránh nguy cơ bị lừa đảo."}, "connect": {"message": "<PERSON><PERSON><PERSON>"}, "connectAccount": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n"}, "connectAccountOrCreate": {"message": "<PERSON>ết nối tài khoản hoặc tạo tài khoản mới"}, "connectAccounts": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n"}, "connectAnAccountHeader": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n"}, "connectManually": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i thủ công với trang web hiện tại"}, "connectMoreAccounts": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i thêm tài k<PERSON>n"}, "connectSnap": {"message": "Kết n<PERSON> $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>"}, "connectedAccounts": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n đã kết nối"}, "connectedAccountsDescriptionPlural": {"message": "Bạn có $1 tài khoản kết nối với trang web này.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Bạn có 1 tài khoản kết nối với trang web này."}, "connectedAccountsEmptyDescription": {"message": "MetaMask chưa kết nối với trang web này. <PERSON><PERSON> kết nối với một trang web trên web3, hãy tìm nút kết nối trên trang web của họ."}, "connectedAccountsListTooltip": {"message": "$1 có thể xem số dư tà<PERSON>, đị<PERSON> chỉ, hoạt động và gợi ý các giao dịch cần phê duyệt cho tài khoản được kết nối.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "<PERSON><PERSON> cập nhật các tài khoản đ<PERSON><PERSON><PERSON> kết nối"}, "connectedSites": {"message": "Trang web đã kết nối"}, "connectedSitesAndSnaps": {"message": "Trang web và Snap đã kết nối"}, "connectedSitesDescription": {"message": "$1 đã đư<PERSON><PERSON> kết nối với các trang web này. Các trang web này có thể xem địa chỉ tài khoản của bạn.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 chưa đ<PERSON><PERSON><PERSON> kết nối với bất kỳ trang web nào.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMask được kết nối với trang web này, nhưng chưa có tài khoản nào được kết nối"}, "connectedSnaps": {"message": "Snap đã kết nối"}, "connectedWithAccount": {"message": "Đã kết nối $1 tài khoản", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Đã kết nối với $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "Đã kết nối $1 mạng", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Đã kết nối với $1", "description": "$1 represents network name"}, "connecting": {"message": "<PERSON><PERSON> kết n<PERSON>i"}, "connectingTo": {"message": "<PERSON><PERSON> kết nối với $1"}, "connectingToDeprecatedNetwork": {"message": "\"$1\" đang được loại bỏ dần và có thể không hoạt động. <PERSON><PERSON> lòng thử một mạng khác."}, "connectingToGoerli": {"message": "<PERSON><PERSON> kết nối với mạng thử nghiệm <PERSON>"}, "connectingToLineaGoerli": {"message": "<PERSON><PERSON> kết nối với mạng thử nghiệm Linea Goerli"}, "connectingToLineaMainnet": {"message": "<PERSON><PERSON> kết nối với mạng ch<PERSON>h thức của <PERSON>a"}, "connectingToLineaSepolia": {"message": "<PERSON><PERSON> kết nối với mạng thử nghiệm Linea Sepolia"}, "connectingToMainnet": {"message": "<PERSON><PERSON> kết nối với Ethereum Mainnet"}, "connectingToSepolia": {"message": "<PERSON><PERSON> kết nối với mạng thử nghiệm <PERSON>"}, "connectionDescription": {"message": "<PERSON><PERSON>t n<PERSON>i trang web này với MetaMask"}, "connectionFailed": {"message": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại"}, "connectionFailedDescription": {"message": "Tìm nạp $1 thất bại, hã<PERSON> kiểm tra mạng của bạn và thử lại.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Đ<PERSON> kết nối với trang web, hã<PERSON> chọn nút kết nối. MetaMask chỉ có thể kết nối với các trang web web3."}, "connectionRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u kết n<PERSON>i"}, "contactUs": {"message": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi"}, "contacts": {"message": "<PERSON><PERSON> b<PERSON>"}, "contentFromSnap": {"message": "Nội dung từ $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contract": {"message": "<PERSON><PERSON><PERSON>"}, "contractAddress": {"message": "Đ<PERSON>a chỉ hợp đồng"}, "contractAddressError": {"message": "Bạn đang gửi token đến địa chỉ hợp đồng của token. Điều này có thể khiến bạn bị mất số token này."}, "contractDeployment": {"message": "<PERSON><PERSON><PERSON> khai hợp đồng"}, "contractInteraction": {"message": "<PERSON><PERSON><PERSON><PERSON> tác với hợp đồng"}, "convertTokenToNFTDescription": {"message": "<PERSON><PERSON>g tôi phát hiện tài sản này là một NFT. MetaMask hiện đã hỗ trợ toàn diện và đầy đủ cho NFT. Bạn có muốn xóa tài sản khỏi danh sách token và thêm tài sản dưới dạng NFT không?"}, "convertTokenToNFTExistDescription": {"message": "<PERSON>úng tôi phát hiện tài sản này đã được thêm dưới dạng NFT. Bạn có muốn xóa tài sản khỏi danh sách token không?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Đã sao chép."}, "copyAddress": {"message": "<PERSON>o chép địa chỉ vào bộ nhớ đệm"}, "copyAddressShort": {"message": "<PERSON>o ch<PERSON>p địa chỉ"}, "copyPrivateKey": {"message": "<PERSON>o ch<PERSON>p khóa riêng tư"}, "copyToClipboard": {"message": "<PERSON>o ch<PERSON><PERSON> vào bộ nhớ đệm"}, "copyTransactionId": {"message": "Sao chép ID giao d<PERSON>ch"}, "create": {"message": "Tạo"}, "createNewAccountHeader": {"message": "<PERSON><PERSON><PERSON> tài k<PERSON>n mới"}, "createPassword": {"message": "<PERSON><PERSON><PERSON> mật k<PERSON>u"}, "createSnapAccountDescription": {"message": "$1 muốn thêm một tài khoản mới vào MetaMask."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "createSolanaAccount": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "creatorAddress": {"message": "Đ<PERSON>a chỉ của người tạo"}, "crossChainSwapsLink": {"message": "<PERSON><PERSON> đổi trên các mạng với MetaMask Portfolio"}, "crossChainSwapsLinkNative": {"message": "<PERSON><PERSON> đổi trên các mạng với C<PERSON>u nối"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "currencyRateCheckToggle": {"message": "<PERSON><PERSON><PERSON> thị trình kiểm tra giá token và số dư"}, "currencyRateCheckToggleDescription": {"message": "Chúng tôi sử dụng API của $1 và $2 để hiện thị giá token và số dư của bạn. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "<PERSON><PERSON> hi<PERSON>u tiền tệ"}, "currencySymbolDefinition": {"message": "<PERSON><PERSON> chứng khoán hiển thị cho tiền tệ của mạng này."}, "currentAccountNotConnected": {"message": "<PERSON><PERSON><PERSON> khoản hiện tại của bạn chưa đư<PERSON><PERSON> kết nối"}, "currentExtension": {"message": "Trang tiện ích mở rộng hiện tại"}, "currentLanguage": {"message": "<PERSON><PERSON><PERSON> ngữ hiện tại"}, "currentNetwork": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>n tại", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "Đường dẫn URL RPC hiện tại cho mạng này không còn được dùng nữa."}, "currentTitle": {"message": "<PERSON><PERSON><PERSON> tại:"}, "currentlyUnavailable": {"message": "<PERSON><PERSON><PERSON>ng có sẵn trên mạng này"}, "curveHighGasEstimate": {"message": "<PERSON><PERSON> thị ư<PERSON>c t<PERSON>h phí gas cao"}, "curveLowGasEstimate": {"message": "<PERSON><PERSON> thị <PERSON><PERSON><PERSON> t<PERSON>h phí gas thấp"}, "curveMediumGasEstimate": {"message": "<PERSON><PERSON> thị ư<PERSON><PERSON> t<PERSON>h phí gas theo thị trường"}, "custom": {"message": "<PERSON><PERSON><PERSON> cao"}, "customGasSettingToolTipMessage": {"message": "Sử dụng $1 để tùy chỉnh giá gas. Việc này có thể gây nhầm lẫn nếu bạn không quen thuộc. Bạn phải tự chịu trách nhiệm nếu thực hiện.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "<PERSON><PERSON><PERSON> chỉnh"}, "customSpendLimit": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu tùy chỉnh"}, "customToken": {"message": "Token tùy chỉnh"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "<PERSON><PERSON><PERSON> năng phát hiện token hiện chưa khả dụng trong mạng này. <PERSON><PERSON> lòng nhập token theo cách thủ công và đảm bảo bạn tin tưởng. Tìm hiểu về $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Bất kỳ ai cũng có thể tạo token, bao gồm cả việc tạo phiên bản gi<PERSON> mạo của token hiện có. Tìm hiểu về $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "<PERSON><PERSON><PERSON> bảo bạn tin tưởng token trư<PERSON><PERSON> khi nhập token đó. Tìm hiểu cách phòng tránh $1. Bạn cũng có thể bật tính năng phát hiện token $2."}, "customerSupport": {"message": "hỗ trợ khách hàng"}, "customizeYourNotifications": {"message": "<PERSON><PERSON><PERSON> chỉnh thông báo"}, "customizeYourNotificationsText": {"message": "<PERSON><PERSON><PERSON> c<PERSON>c lo<PERSON>i thông báo mà bạn muốn nhận:"}, "dappSuggested": {"message": "Trang web gợi ý"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 đã gợi ý mức giá này.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Trang web gợi ý"}, "dappSuggestedHighShortLabel": {"message": "Trang web (cao)"}, "dappSuggestedShortLabel": {"message": "Trang web"}, "dappSuggestedTooltip": {"message": "$1 đã đề xuất mức giá này.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "<PERSON><PERSON><PERSON>"}, "data": {"message": "<PERSON><PERSON> liệu"}, "dataCollectionForMarketing": {"message": "<PERSON>hu thập dữ liệu cho mục đích tiếp thị"}, "dataCollectionForMarketingDescription": {"message": "Chúng tôi sẽ sử dụng MetaMetrics để tìm hiểu cách bạn tương tác với các thông tin tiếp thị. Chúng tôi có thể chia sẻ tin tức liên quan (chẳng hạn như tính năng sản phẩm và các tài liệu khác)."}, "dataCollectionWarningPopoverButton": {"message": "Đồng ý"}, "dataCollectionWarningPopoverDescription": {"message": "Bạn đã tắt tùy chọn thu thập dữ liệu cho mục đích tiếp thị của chúng tôi. Thao tác này chỉ áp dụng cho thiết bị này. Nếu bạn sử dụng MetaMask trên các thiết bị khác, nhớ chọn không tham gia trên các thiết bị đó."}, "dataUnavailable": {"message": "d<PERSON> liệu không khả dụng"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON>"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "<PERSON><PERSON><PERSON> chọn mua bằng thẻ ghi nợ hoặc thẻ tín dụng"}, "decimal": {"message": "<PERSON><PERSON> thập phân của token"}, "decimalsMustZerotoTen": {"message": "<PERSON><PERSON> thập phân ít nhất phải bằng 0 và không đư<PERSON><PERSON> quá 36."}, "decrypt": {"message": "G<PERSON><PERSON><PERSON> mã"}, "decryptCopy": {"message": "<PERSON>o ch<PERSON>p thông báo đã mã hóa"}, "decryptInlineError": {"message": "Không thể giải mã thông báo này do lỗi: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 muốn đọc thông báo này để hoàn tất hành động của bạn", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "G<PERSON>ải mã thông báo"}, "decryptRequest": {"message": "G<PERSON>ải mã yêu cầu"}, "defaultRpcUrl": {"message": "URL RPC mặc định"}, "defaultSettingsSubTitle": {"message": "MetaMask sử dụng chế độ cài đặt mặc định để cân bằng tốt nhất giữa tính an toàn và dễ sử dụng. Thay đổi các chế độ cài đặt này để nâng cao quyền riêng tư của bạn hơn nữa."}, "defaultSettingsTitle": {"message": "<PERSON>ài đặt quyền riêng tư mặc định"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "<PERSON><PERSON><PERSON> thử truy cập lại sau."}, "defiTabErrorTitle": {"message": "<PERSON><PERSON><PERSON> tôi không thể tải trang này."}, "delete": {"message": "Xóa"}, "deleteContact": {"message": "<PERSON><PERSON><PERSON> địa chỉ liên hệ"}, "deleteMetaMetricsData": {"message": "Xóa dữ liệu MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "<PERSON>hao tác này sẽ xóa dữ liệu MetaMetrics trước đây liên quan đến quá trình sử dụng của bạn trên thiết bị này. Ví và tài khoản của bạn sẽ vẫn như hiện tại sau khi dữ liệu này bị xóa. Quá trình này có thể mất đến 30 ngày. Xem $1 của chúng tôi.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tất yêu cầu này ngay bây giờ do sự cố máy chủ hệ thống phân tích, vui lòng thử lại sau"}, "deleteMetaMetricsDataErrorTitle": {"message": "<PERSON><PERSON>g tôi không thể xóa dữ liệu này bây giờ"}, "deleteMetaMetricsDataModalDesc": {"message": "<PERSON>úng tôi sắp xóa tất cả dữ liệu MetaMetrics của bạn. Bạn có chắc không?"}, "deleteMetaMetricsDataModalTitle": {"message": "Xóa dữ liệu MetaMetrics?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Bạn đã khởi tạo hành động này trên $1. Quá trình này có thể mất đến 30 ngày. Xem $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "<PERSON><PERSON>u xóa mạng này, bạn sẽ cần thêm lại mạng này để xem các tài sản của mình trong mạng này"}, "deleteNetworkTitle": {"message": "Xóa mạng $1?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "<PERSON><PERSON><PERSON> tiền mã hóa từ tài khoản khác bằng địa chỉ ví hoặc mã QR."}, "deprecatedGoerliNtwrkMsg": {"message": "<PERSON> cá<PERSON> cập nhật củ<PERSON> hệ thống Ethereum, mạng thử nghiệm Goerli sẽ sớm được lo<PERSON> bỏ dần."}, "deprecatedNetwork": {"message": "M<PERSON>ng này đã ngừng sử dụng"}, "deprecatedNetworkButtonMsg": {"message": "<PERSON><PERSON> hiểu"}, "deprecatedNetworkDescription": {"message": "Mạng bạn đang cố gắng kết nối không còn được MetaMask hỗ trợ. $1"}, "description": {"message": "<PERSON><PERSON>"}, "descriptionFromSnap": {"message": "Mô tả từ $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài khoản đủ điều kiện"}, "destinationAccountPickerNoMatching": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài khoản trùng khớp"}, "destinationAccountPickerReceiveAt": {"message": "<PERSON><PERSON><PERSON><PERSON> tại"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Địa chỉ nhận hoặc ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Địa chỉ nhận"}, "destinationTransactionIdLabel": {"message": "ID giao d<PERSON><PERSON>ch", "description": "Label for the destination transaction ID field."}, "details": {"message": "<PERSON> ti<PERSON>"}, "developerOptions": {"message": "<PERSON><PERSON><PERSON> phát triển"}, "disabledGasOptionToolTipMessage": {"message": "“$1” bị vô hiệu hóa vì không đạt mức tăng tối thiểu 10% so với phí gas ban đầu.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>"}, "disconnectAllAccounts": {"message": "<PERSON><PERSON><PERSON> kết nối tất cả các tài kho<PERSON>n"}, "disconnectAllAccountsConfirmationDescription": {"message": "Bạn có chắc chắn muốn ngắt kết nối không? Bạn có thể bị mất chức năng của trang web."}, "disconnectAllAccountsText": {"message": "t<PERSON><PERSON>"}, "disconnectAllDescriptionText": {"message": "N<PERSON>u bạn ngắt kết nối khỏi trang web này, bạn sẽ cần kết nối lại tài khoản và mạng của bạn để sử dụng lại trang web này."}, "disconnectAllSnapsText": {"message": "Snap"}, "disconnectMessage": {"message": "<PERSON><PERSON><PERSON> động này sẽ ngắt kết nối bạn khỏi trang web này"}, "disconnectPrompt": {"message": "Ngắt kết n<PERSON> $1"}, "disconnectThisAccount": {"message": "<PERSON><PERSON><PERSON> kết nối tài k<PERSON>n này"}, "disconnectedAllAccountsToast": {"message": "<PERSON>ã ngắt kết nối tất cả các tài kho<PERSON>n khỏi $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "Đ<PERSON> ngắt kết nối $1 khỏi $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Khám phá"}, "discoverSnaps": {"message": "Khám phá Snap", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Đ<PERSON><PERSON>"}, "dismissReminderDescriptionField": {"message": "Bật tùy chọn này để tắt thông báo nhắc sao lưu Cụm từ khôi phục bí mật. Bạn nên sao lưu Cụm từ khôi phục bí mật của mình để tránh mất tiền"}, "dismissReminderField": {"message": "Tắt lời nhắc sao lưu <PERSON> từ khôi phục bí mật"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Bật tùy chọn này để không còn thấy gợi ý \"<PERSON><PERSON><PERSON><PERSON> sang Tài khoản thông minh\" trên bất kỳ tài khoản nào. Tài khoản thông minh mở khóa giao dịch <PERSON> hơn, phí mạng thấp hơn và linh hoạt hơn trong việc thanh toán."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "<PERSON><PERSON>t gợi ý \"<PERSON><PERSON><PERSON><PERSON> sang Tài khoản thông minh\""}, "displayNftMedia": {"message": "<PERSON><PERSON><PERSON> thị phương tiện NFT"}, "displayNftMediaDescription": {"message": "Việc hiển thị dữ liệu và phương tiện NFT sẽ làm lộ địa chỉ IP của bạn với OpenSea hoặc các bên thứ ba khác. Điều này có thể cho phép kẻ tấn công liên kết địa chỉ IP với địa chỉ Ethereum của bạn. Tính năng tự động phát hiện NFT dựa trên chế độ cài đặt này và sẽ không khả dụng khi chế độ cài đặt này bị tắt."}, "doNotShare": {"message": "Không chia sẻ thông tin này với bất kỳ ai"}, "domain": {"message": "<PERSON><PERSON><PERSON>"}, "done": {"message": "<PERSON><PERSON><PERSON> t<PERSON>t"}, "dontShowThisAgain": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị lại"}, "downArrow": {"message": "m<PERSON>i tên xu<PERSON>ng"}, "downloadGoogleChrome": {"message": "Tải Google Chrome xuống"}, "downloadNow": {"message": "<PERSON><PERSON><PERSON> x<PERSON> ngay"}, "downloadStateLogs": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t ký trạng thái xuống"}, "dragAndDropBanner": {"message": "<PERSON><PERSON>n có thể kéo các mạng để sắp xếp lại thứ tự. "}, "dropped": {"message": "<PERSON><PERSON> ng<PERSON>"}, "duplicateContactTooltip": {"message": "Tên liên hệ này trùng với một tài khoản hoặc liên hệ đã có"}, "duplicateContactWarning": {"message": "<PERSON><PERSON><PERSON> có liên hệ bị trùng"}, "durationSuffixDay": {"message": "<PERSON><PERSON><PERSON>", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "Giờ", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "<PERSON><PERSON>", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "<PERSON><PERSON><PERSON>", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "Giây", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "<PERSON><PERSON><PERSON>", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "Năm", "description": "Shortened form of 'year'"}, "earn": {"message": "<PERSON><PERSON><PERSON> lợ<PERSON>"}, "edit": {"message": "Chỉnh sửa"}, "editANickname": {"message": "Chỉnh sửa bi<PERSON>t danh"}, "editAccounts": {"message": "Chỉnh sửa tài k<PERSON>n"}, "editAddressNickname": {"message": "Chỉnh sửa biệt danh địa chỉ"}, "editCancellationGasFeeModalTitle": {"message": "Chỉnh sửa phí gas hủy"}, "editContact": {"message": "Chỉnh sửa liên hệ"}, "editGasFeeModalTitle": {"message": "Chỉnh sửa phí gas"}, "editGasLimitOutOfBounds": {"message": "<PERSON><PERSON><PERSON> mức phí gas tối thiểu phải là $1"}, "editGasLimitOutOfBoundsV2": {"message": "Hạn mức phí gas phải lớn hơn $1 và nhỏ hơn $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "<PERSON>ạn mức phí gas là đơn vị gas tối đa mà bạn sẵn sàng sử dụng. Đơn vị gas là hệ số nhân của \"Phí ưu tiên tối đa\" và \"Phí tối đa\"."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "<PERSON><PERSON> cơ sở tối đa không thể thấp hơn phí ưu tiên"}, "editGasMaxBaseFeeHigh": {"message": "<PERSON><PERSON> cơ sở tối đa cao hơn cần thiết"}, "editGasMaxBaseFeeLow": {"message": "<PERSON><PERSON> cơ sở tối đa thấp so với tình trạng mạng hiện tại"}, "editGasMaxFeeHigh": {"message": "<PERSON><PERSON> tối đa cao hơn cần thiết"}, "editGasMaxFeeLow": {"message": "<PERSON>í tối đa quá thấp so với tình trạng mạng"}, "editGasMaxFeePriorityImbalance": {"message": "<PERSON>í tối đa không thể thấp hơn phí ưu tiên tối đa"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "<PERSON><PERSON> <PERSON><PERSON> tiên tối đa phải lớn hơn 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "<PERSON>í <PERSON>u tiên phải lớn hơn 0."}, "editGasMaxPriorityFeeHigh": {"message": "<PERSON><PERSON> ưu tiên tối đa cao hơn cần thiết. <PERSON><PERSON><PERSON> có thể phải trả nhiều hơn mức cần thiết."}, "editGasMaxPriorityFeeHighV2": {"message": "<PERSON><PERSON> <PERSON>u tiên cao hơn cần thiết. <PERSON><PERSON><PERSON> có thể phải trả nhiều hơn mức cần thiết"}, "editGasMaxPriorityFeeLow": {"message": "<PERSON><PERSON> <PERSON>u tiên tối đa thấp so với tình trạng mạng hiện tại"}, "editGasMaxPriorityFeeLowV2": {"message": "<PERSON><PERSON> <PERSON><PERSON> tiên thấp so với tình trạng mạng hiện tại"}, "editGasPriceTooLow": {"message": "Giá gas phải lớn hơn 0"}, "editGasPriceTooltip": {"message": "Mạng này yêu cầu trườ<PERSON> \"Gi<PERSON> gas\" khi gửi giao dịch. Giá gas là số tiền bạn sẽ trả cho mỗi đơn vị gas."}, "editGasSubTextFeeLabel": {"message": "<PERSON><PERSON> tối đa:"}, "editGasTitle": {"message": "Chỉnh sửa ưu tiên"}, "editGasTooLow": {"message": "<PERSON>hờ<PERSON> gian xử lý không rõ"}, "editInPortfolio": {"message": "Chỉnh sửa trong Portfolio"}, "editNetworkLink": {"message": "chỉnh sửa mạng gốc"}, "editNetworksTitle": {"message": "Chỉnh sửa mạng"}, "editNonceField": {"message": "Chỉnh sửa số nonce"}, "editNonceMessage": {"message": "<PERSON><PERSON><PERSON> là t<PERSON>h năng nâng cao, h<PERSON><PERSON> dùng một cách thận trọng."}, "editPermission": {"message": "Chỉnh sửa quyền"}, "editPermissions": {"message": "Chỉnh sửa quyền"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Chỉnh sửa phí gas tăng tốc"}, "editSpendingCap": {"message": "Chỉnh sửa hạn mức chi tiêu"}, "editSpendingCapAccountBalance": {"message": "Số dư tài <PERSON>: $1 $2"}, "editSpendingCapDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> số tiền mà bạn cảm thấy thoải mái khi được chi tiêu thay mặt cho bạn."}, "editSpendingCapError": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu không đư<PERSON><PERSON> vư<PERSON><PERSON> quá $1 chữ số thập phân. <PERSON><PERSON><PERSON> bớt chữ số thập phân để tiếp tục."}, "editSpendingCapSpecialCharError": {"message": "Chỉ nhập số"}, "enableAutoDetect": {"message": " <PERSON><PERSON>t tự động phát hiện"}, "enableFromSettings": {"message": " <PERSON><PERSON><PERSON> trong <PERSON>ài Đặt."}, "enableSnap": {"message": "<PERSON><PERSON><PERSON>"}, "enableToken": {"message": "bật $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "<PERSON><PERSON> bật"}, "enabledNetworks": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>t"}, "encryptionPublicKeyNotice": {"message": "$1 muốn biết khóa mã hóa công khai của bạn. Bằng việc đồng ý, trang web này sẽ có thể gửi thông báo được mã hóa cho bạn.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "<PERSON><PERSON><PERSON> cầu khóa mã hóa công khai"}, "endpointReturnedDifferentChainId": {"message": "URL RPC bạn đã nhập trả về một ID chuỗi khác ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "<PERSON><PERSON><PERSON> năng ph<PERSON>t hiện token nâng cao hiện có sẵn trên $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "MetaMask cho phép bạn xem các tên miền ENS ngay trên thanh địa chỉ của trình duyệt. Sau đây là cách hoạt động:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "<PERSON><PERSON><PERSON> ý rằng việc sử dụng tính năng này sẽ làm lộ địa chỉ IP của bạn với các dịch vụ bên thứ ba IPFS."}, "ensDomainsSettingDescriptionPart1": {"message": "MetaMask sẽ kiểm tra hợp đồng ENS của Ethereum để tìm mã được kết nối với tên ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "<PERSON><PERSON>u mã liên kết đến IPFS, bạn có thể xem nội dung được liên kết với nó (thường là một trang web)."}, "ensDomainsSettingTitle": {"message": "Hiển thị tên miền ENS trong thanh địa chỉ"}, "ensUnknownError": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> thất bại."}, "enterANameToIdentifyTheUrl": {"message": "<PERSON><PERSON><PERSON><PERSON> tên để xác đ<PERSON>nh <PERSON>"}, "enterChainId": {"message": "Nhập ID chuỗi"}, "enterMaxSpendLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> hạn mức chi tiêu tối đa"}, "enterNetworkName": {"message": "<PERSON><PERSON><PERSON><PERSON> tên mạng"}, "enterOptionalPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u tùy chọn"}, "enterPasswordContinue": {"message": "<PERSON><PERSON><PERSON><PERSON> mật khẩu để tiếp tục"}, "enterRpcUrl": {"message": "Nhập URL RPC"}, "enterSymbol": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>u"}, "enterTokenNameOrAddress": {"message": "<PERSON><PERSON><PERSON><PERSON> tên token hoặc dán địa chỉ"}, "enterYourPassword": {"message": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u của bạn"}, "errorCode": {"message": "Mã: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Lỗi khi lấy danh sách chuỗi an toàn, vui lòng tiếp tục một cách thận trọng."}, "errorMessage": {"message": "Thông báo: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Mã: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "<PERSON><PERSON><PERSON> hệ bộ phận hỗ trợ", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "<PERSON><PERSON> tả những gì đã xảy ra", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "<PERSON><PERSON><PERSON>ng thể hiển thị thông tin của bạn. <PERSON><PERSON><PERSON> lo, ví và tiền của bạn vẫn an toàn.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "<PERSON>h<PERSON>ng báo lỗi", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "<PERSON><PERSON> tả những gì đã xảy ra", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "<PERSON>a sẻ thông tin chi tiết như cách chúng tôi có thể tái hiện lỗi để giúp chúng tôi khắc phục vấn đề.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Cảm ơn! Chúng tôi sẽ sớm tiến hành xem xét.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMask đã gặp lỗi", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "<PERSON><PERSON><PERSON> lại", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Cụm:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Gặp lỗi khi kết nối với mạng tùy chỉnh."}, "errorWithSnap": {"message": "Lỗi với $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "<PERSON><PERSON> <PERSON>"}, "estimatedFeeTooltip": {"message": "<PERSON><PERSON> tiền đư<PERSON><PERSON> chi trả để xử lý giao dịch trên mạng."}, "ethGasPriceFetchWarning": {"message": "Giá gas dự phòng được cung cấp vì dịch vụ ước tính giá gas chính hiện không hoạt động."}, "ethereumProviderAccess": {"message": "<PERSON><PERSON><PERSON> quyền truy cập $1 cho nhà cung cấp Ethereum", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Địa chỉ công khai trên Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "<PERSON><PERSON> tài <PERSON>n trên <PERSON>"}, "etherscanViewOn": {"message": "<PERSON><PERSON> tr<PERSON>n <PERSON>"}, "existingChainId": {"message": "Thông tin bạn đã nhập đư<PERSON><PERSON> liên kết với một ID chuỗi hiện có."}, "expandView": {"message": "<PERSON><PERSON> độ xem mở rộng"}, "experimental": {"message": "<PERSON><PERSON><PERSON>"}, "exploreweb3": {"message": "Khám phá web3"}, "exportYourData": {"message": "<PERSON><PERSON><PERSON> dữ liệu của bạn"}, "exportYourDataButton": {"message": "<PERSON><PERSON><PERSON>"}, "exportYourDataDescription": {"message": "Bạn có thể xuất các dữ liệu như địa chỉ liên hệ và tùy chọn của bạn."}, "extendWalletWithSnaps": {"message": "Khám phá các Snap do cộng đồng xây dựng để tùy chỉnh trải nghiệm web3 của bạn", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n bên ngo<PERSON>i"}, "externalExtension": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ch bên ngo<PERSON>i"}, "externalNameSourcesSetting": {"message": "<PERSON><PERSON><PERSON><PERSON> danh đư<PERSON><PERSON> đề xuất"}, "externalNameSourcesSettingDescription": {"message": "<PERSON>úng tôi sẽ tìm nạp những biệt danh được đề xuất cho các địa chỉ mà bạn tương tác từ các nguồn bên thứ ba nh<PERSON>scan, Infura và Lens Protocol. Các nguồn này sẽ có thể nhìn thấy các địa chỉ đó và địa chỉ IP của bạn. Địa chỉ tài khoản của bạn sẽ không bị lộ với các bên thứ ba."}, "failed": {"message": "<PERSON><PERSON><PERSON><PERSON> thành công"}, "failedToFetchChainId": {"message": "Không thể tìm nạp ID chuỗi. URL RPC của bạn có chính xác không?"}, "failover": {"message": "Chuyển đổi dự phòng"}, "failoverRpcUrl": {"message": "URL RPC chuyển đổi dự phòng"}, "failureMessage": {"message": "<PERSON><PERSON> xảy ra sự cố và chúng tôi không thể hoàn tất hành động"}, "fast": {"message": "<PERSON><PERSON><PERSON>"}, "feeDetails": {"message": "<PERSON> tiết phí"}, "fileImportFail": {"message": "T<PERSON>h năng nhập tập tin không hoạt động? Nhấp vào đây!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "bạn nên gỡ cài đặt tiện ích mở rộng này", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask là nơi để các lập trình viên thí nghiệm những API mới không ổn định. Nếu bạn không phải là lập trình viên hay người tham gia thử nghiệm giai đoạn beta, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "<PERSON>úng tôi không đảm bảo tính an toàn hay ổn định của tiện ích mở rộng này. Các API mới do Flask cung cấp không đủ sức chống lại các cuộc tấn công lừa đảo, có nghĩa rằng bất kỳ trang web hay Snap nào yêu cầu truy cập vào Flask đều có thể mang mục đích xấu nhằm đánh cắp tài sản của bạn.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Tất cả các API của Flask đều là thử nghiệm. <PERSON>úng có thể bị thay đổi hoặc xóa mà không cần thông báo, hoặc chúng có thể ở trên Flask vô thời hạn mà không bao giờ được chuyển sang phiên bản MetaMask ổn định. Bạn phải tự chịu rủi ro khi sử dụng chúng.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "<PERSON><PERSON><PERSON> bảo bạn đã tắt tiện ích mở rộng MetaMask thông thường khi sử dụng Flask.", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "<PERSON><PERSON><PERSON> chấp nhận những rủi ro này", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Số lượng Token phải là số nguyên"}, "followUsOnTwitter": {"message": "<PERSON> chúng tôi trên Twitter"}, "forbiddenIpfsGateway": {"message": "Cổng kết nối IPFS không được phép: <PERSON><PERSON> lòng chỉ định một cổng kết nối CID"}, "forgetDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> bị n<PERSON>y"}, "forgotPassword": {"message": "<PERSON>uên mật khẩu?"}, "form": {"message": "mẫu"}, "from": {"message": "Từ"}, "fromAddress": {"message": "Từ: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "Từ danh sách token: $1"}, "function": {"message": "<PERSON>ức năng: $1"}, "fundingMethod": {"message": "<PERSON><PERSON><PERSON><PERSON> thức n<PERSON> tiền"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Chỉnh sửa phí gas gợi ý"}, "gasDisplayDappWarning": {"message": "Phí gas này đã được gợi ý bởi $1. Việc sửa đổi có thể khiến giao dịch của bạn gặp sự cố. Vui lòng liên hệ với $1 nếu bạn có câu hỏi.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Phí gas"}, "gasLimit": {"message": "<PERSON><PERSON><PERSON> mức phí gas"}, "gasLimitRecommended": {"message": "<PERSON>ạn mức phí gas được đề xuất là $1. <PERSON><PERSON> thể thất bại nếu hạn mức phí gas thấp hơn."}, "gasLimitTooLow": {"message": "<PERSON><PERSON><PERSON> mức phí gas ít nhất phải là 21000"}, "gasLimitV2": {"message": "<PERSON><PERSON><PERSON> mức phí gas"}, "gasOption": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n phí gas"}, "gasPriceExcessive": {"message": "Bạn đã đặt phí gas cao một cách không cần thiết. <PERSON><PERSON>y cân nhắc giảm mức phí này."}, "gasPriceFetchFailed": {"message": "Không ước tính được giá gas do lỗi mạng."}, "gasTimingHoursShort": {"message": "$1 giờ", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "<PERSON><PERSON><PERSON>"}, "gasTimingMinutesShort": {"message": "$1 phút", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 giây", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gas đã dùng"}, "general": {"message": "<PERSON>"}, "generalCameraError": {"message": "<PERSON><PERSON><PERSON> tôi không thể truy cập máy <PERSON>nh của bạn. <PERSON><PERSON> lòng thử lại một lần nữa."}, "generalCameraErrorTitle": {"message": "<PERSON><PERSON> x<PERSON>y ra sự cố..."}, "generalDescription": {"message": "Đồng bộ chế độ cài đặt trên các thiết bị, chọn mạng ưa thích và theo dõi dữ liệu token"}, "genericExplorerView": {"message": "<PERSON>em tài k<PERSON>n trên $1"}, "goToSite": {"message": "<PERSON><PERSON><PERSON> trang web"}, "goerli": {"message": "<PERSON><PERSON><PERSON> thử nghi<PERSON>"}, "gotIt": {"message": "<PERSON><PERSON> hiểu"}, "grantExactAccess": {"message": "<PERSON><PERSON><PERSON> quyền truy cập ch<PERSON>h x<PERSON>c"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "<PERSON><PERSON><PERSON> c<PERSON>"}, "hardwareWalletConnected": {"message": "Đã kết nối với ví cứng"}, "hardwareWalletLegacyDescription": {"message": "(cũ)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "<PERSON><PERSON><PERSON> cắm thiết bị $1 và chọn ứng dụng Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "<PERSON><PERSON>t \"dữ liệu hợp đồng thông minh\" hoặc \"ký mù\" trên thiết bị $1 của bạn."}, "hardwareWalletSubmissionWarningTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi n<PERSON>ấn <PERSON>:"}, "hardwareWalletSupportLinkConversion": {"message": "nhấp vào đ<PERSON>y"}, "hardwareWallets": {"message": "<PERSON><PERSON>t n<PERSON>i với một ví cứng"}, "hardwareWalletsInfo": {"message": "<PERSON><PERSON><PERSON> thành phần tích hợp của ví cứng sử dụng lệnh gọi API đến máy chủ bên ngoài, máy chủ này có thể xem địa chỉ IP của bạn và địa chỉ hợp đồng thông minh mà bạn tương tác."}, "hardwareWalletsMsg": {"message": "<PERSON><PERSON>n một ví cứng mà bạn muốn sử dụng với MetaMask."}, "here": {"message": "tại đây", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "<PERSON><PERSON> li<PERSON>u thập lục phân"}, "hiddenAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "hide": {"message": "Ẩn"}, "hideAccount": {"message": "Ẩn tài k<PERSON>n"}, "hideAdvancedDetails": {"message": "Ẩn chi tiết nâng cao"}, "hideSentitiveInfo": {"message": "Ẩn thông tin nh<PERSON>y cảm"}, "hideTokenPrompt": {"message": "Ẩn token?"}, "hideTokenSymbol": {"message": "Ẩn $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Ẩn các token không có số dư"}, "high": {"message": "<PERSON>"}, "highGasSettingToolTipMessage": {"message": "<PERSON><PERSON><PERSON>, ngay cả trong thị trường biến động. Sử dụng $1 để bù đắp khi lưu lượng mạng lưới tăng vọt trong những trường hợp như phát hành NFT nổi tiếng.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "cao"}, "highestCurrentBid": {"message": "<PERSON><PERSON><PERSON> thầu hiện tại cao nhất"}, "highestFloorPrice": {"message": "<PERSON><PERSON><PERSON> sàn cao nhất"}, "history": {"message": "<PERSON><PERSON><PERSON> s<PERSON>"}, "holdToRevealContent1": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật của bạn cung cấp $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "to<PERSON>n quyền truy cập vào ví và tiền của bạn.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Không chia sẻ với bất kỳ ai. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "<PERSON>ộ phận Hỗ trợ của MetaMask sẽ không yêu cầu điều này,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "nhưng những kẻ lừa đảo qua mạng thì có.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Khóa riêng tư của bạn cung cấp $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "to<PERSON>n quyền truy cập vào ví và tiền của bạn.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "giữ để hiển thị vòng tròn bị khóa"}, "holdToRevealPrivateKey": {"message": "<PERSON><PERSON><PERSON> để hiển thị Khóa riêng tư"}, "holdToRevealPrivateKeyTitle": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o an toàn cho khóa riêng tư của bạn"}, "holdToRevealSRP": {"message": "Giữ để hiển thị Cụm từ khôi phục bí mật"}, "holdToRevealSRPTitle": {"message": "<PERSON><PERSON><PERSON> bảo an toàn cho Cụm từ khôi phục bí mật của bạn"}, "holdToRevealUnlockedLabel": {"message": "giữ để hiển thị vòng tròn được mở khóa"}, "honeypotDescription": {"message": "Token này có thể tiềm ẩn rủi ro honeypot (bẫy trong bẫy). Bạn nên thẩm định kỹ trước khi tương tác để tránh thiệt hại tài ch<PERSON>h."}, "honeypotTitle": {"message": "Honey Pot (Bẫy trong bẫy)"}, "howNetworkFeesWorkExplanation": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> t<PERSON>h cần thiết để xử lý giao dịch. <PERSON>í tối đa là $1."}, "howQuotesWork": {"message": "<PERSON><PERSON><PERSON> báo giá hoạt động"}, "howQuotesWorkExplanation": {"message": "Báo giá này có lợi nhuận cao nhất trong các báo giá mà chúng tôi đã tìm kiếm. Báo giá này dựa trên tỷ giá hoán đổi, bao gồm phí cầu nối và $1% phí MetaMask, trừ phí gas. Phí gas phụ thuộc vào mức độ bận rộn của mạng và độ phức tạp của giao dịch."}, "id": {"message": "ID"}, "ignoreAll": {"message": "Bỏ qua tất cả"}, "ignoreTokenWarning": {"message": "<PERSON><PERSON>u bạn ẩn token, chúng sẽ không hiển thị trong ví của bạn. <PERSON><PERSON>, bạn vẫn có thể thêm chúng thông qua chức năng tìm kiếm."}, "imToken": {"message": "imToken"}, "import": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Lỗi khi nhập tà<PERSON>."}, "importAccountErrorIsSRP": {"message": "Bạn đã nhập <PERSON><PERSON><PERSON> từ khôi phục bí mật (hoặc thông tin gợi nhớ). <PERSON><PERSON> nhập tài khoản tại đây, bạn phải nhập kh<PERSON><PERSON> riêng tư, là một chuỗi thập lục phân có độ dài 64 ký tự."}, "importAccountErrorNotAValidPrivateKey": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư này không hợp lệ. <PERSON>ạn đã nhập một chuỗi thập lục phân, nhưng phải dài 64 ký tự."}, "importAccountErrorNotHexadecimal": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư này không hợp lệ. Bạn phải nhập một chuỗi thập lục phân có độ dài 64 ký tự."}, "importAccountJsonLoading1": {"message": "Dự kiến quá trình nhập JSON này sẽ mất vài phút và đóng băng MetaMask."}, "importAccountJsonLoading2": {"message": "<PERSON><PERSON> thành xin lỗi bạn, chúng tôi sẽ cải thiện để quá trình này nhanh hơn trong tương lai."}, "importAccountMsg": {"message": "Tài khoản đã nhập sẽ không được liên kết với Cụm từ khôi phục bí mật MetaMask của bạn. Tìm hiểu thêm về tài khoản đã nhập"}, "importNFT": {"message": "Nhập NFT"}, "importNFTAddressToolTip": {"message": "Ví dụ: trê<PERSON>, trên trang của NFT bên dưới mục Chi tiết, có một giá trị siêu liên kết màu xanh dương gắn nhãn \"Địa chỉ hợp đồng\". Nếu bạn nhấp vào đây, nó sẽ dẫn bạn đến địa chỉ của hợp đồng trên Etherscan; ở phía trên cùng bên trái của trang đó, sẽ có một biểu tượng gắn nhãn \"Hợp đồng\" và ở bên phải là một chuỗi dài các chữ cái và số. Đây là địa chỉ của hợp đồng đã tạo NFT của bạn. Nhấp vào biểu tượng \"sao chép\" ở bên phải của địa chỉ để sao chép nó vào bộ nhớ đệm."}, "importNFTPage": {"message": "Nhập trang NFT"}, "importNFTTokenIdToolTip": {"message": "ID của NFT là một mã nhận dạng duy nhất vì không có hai NFT nào giống hệt nhau. <PERSON><PERSON><PERSON> lầ<PERSON> nữ<PERSON>, trên OpenSea, mã số này nằm bên dưới mục \"Chi tiết\". Hãy ghi chú lại hoặc sao chép vào bộ nhớ đệm."}, "importNWordSRP": {"message": "T<PERSON><PERSON> có cụm từ khôi phục gồm $1 từ", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư"}, "importSRPDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ví hiện có bằng cụm từ khôi phục gồm 12 hoặc 24 từ của bạn."}, "importSRPNumberOfWordsError": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật gồm 12 hoặc 24 từ"}, "importSRPWordError": {"message": "Từ $1 không đúng hoặc sai chính tả.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Từ $1 và $2 không đúng hoặc sai chính tả.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "importSecretRecoveryPhraseUnknownError": {"message": "<PERSON><PERSON> xảy ra lỗi không xác định."}, "importSelectedTokens": {"message": "<PERSON><PERSON><PERSON><PERSON> các token đã chọn?"}, "importSelectedTokensDescription": {"message": "Chỉ những token đã chọn mới xuất hiện trong ví của bạn. <PERSON><PERSON> <PERSON><PERSON>, bạn luôn có thể nhập các token đã ẩn thông qua chức năng tìm kiếm."}, "importTokenQuestion": {"message": "Bạn muốn nhập token?"}, "importTokenWarning": {"message": "Bất kỳ ai cũng tạo được token bằng bất kỳ tên nào, kể cả phiên bản gi<PERSON> của token hiện có. Bạn tự chịu rủi ro khi thêm và giao dịch!"}, "importTokensCamelCase": {"message": "Nhập token"}, "importTokensError": {"message": "<PERSON><PERSON>g tôi không thể nhập token. <PERSON><PERSON> lòng thử lại sau."}, "importWallet": {"message": "Nhập ví"}, "importWalletOrAccountHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> ví hoặc tài k<PERSON>n"}, "importWalletSuccess": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật $1 đã đư<PERSON><PERSON> nhập", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Nhập $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "<PERSON><PERSON> nh<PERSON>p", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "trong phần <PERSON> đặt"}, "included": {"message": "đ<PERSON> bao gồm"}, "includesXTransactions": {"message": "Bao gồm $1 giao dịch"}, "infuraBlockedNotification": {"message": "MetaMask không thể kết nối với máy chủ chuỗi khối. Hãy xem xét các lý do tiềm ẩn $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Mạng đã xác nhận giao dịch ban đầu của bạn. <PERSON>hấp OK để quay lại."}, "insightsFromSnap": {"message": "Thông tin chi tiết từ $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Cài đặt"}, "installOrigin": {"message": "Cài đặt nguồn gốc"}, "installRequest": {"message": "Thêm vào MetaMask"}, "installedOn": {"message": "Đã cài đặt vào $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "<PERSON><PERSON><PERSON>ng đủ số dư."}, "insufficientFunds": {"message": "<PERSON><PERSON><PERSON><PERSON> đủ tiền."}, "insufficientFundsForGas": {"message": "<PERSON><PERSON>ông đủ tiền thanh toán phí gas"}, "insufficientLockedLiquidityDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> thiếu tính thanh khoản bị khóa hoặc đốt hợp lý khiến token dễ bị rút thanh khoản đột ngột, có thể gây bất <PERSON>n thị trường."}, "insufficientLockedLiquidityTitle": {"message": "<PERSON><PERSON><PERSON> <PERSON>h k<PERSON>n bị khóa không đủ"}, "insufficientTokens": {"message": "Không đủ token."}, "interactWithSmartContract": {"message": "<PERSON><PERSON><PERSON> đồng thông minh"}, "interactingWith": {"message": "<PERSON><PERSON> tư<PERSON>ng tác với"}, "interactingWithTransactionDescription": {"message": "<PERSON><PERSON><PERSON> là hợp đồng mà bạn đang tương tác. Nhớ xác minh các thông tin để bảo vệ bản thân trước những kẻ lừa đảo."}, "interaction": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c"}, "invalidAddress": {"message": "Địa chỉ không hợp lệ"}, "invalidAddressRecipient": {"message": "Địa chỉ người nhận không hợp lệ"}, "invalidAssetType": {"message": "Tài sản này là một NFT và cần được thêm lại trên trang Nhập NFT bên dưới thẻ NFT"}, "invalidChainIdTooBig": {"message": "ID chuỗi không hợp lệ. ID chuỗi quá lớn."}, "invalidCustomNetworkAlertContent1": {"message": "<PERSON><PERSON><PERSON> nhập lại ID chuỗi cho mạng tùy chỉnh “$1”.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "<PERSON><PERSON> bảo vệ bạn khỏi các nhà cung cấp mạng độc hại hoặc bị lỗi, ID chuỗi giờ đây là yêu cầu bắt buộc đối với tất cả các mạng tùy chỉnh."}, "invalidCustomNetworkAlertContent3": {"message": "Chuyển đến phần Cài đặt > <PERSON><PERSON><PERSON>, rồ<PERSON> nhập ID chuỗi. Bạn có thể tìm được ID chuỗi của hầu hết các mạng phổ biến trên $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "<PERSON><PERSON><PERSON> tùy chỉnh không hợp lệ"}, "invalidHexData": {"message": "<PERSON><PERSON> li<PERSON>u thập lục phân không hợp lệ"}, "invalidHexNumber": {"message": "<PERSON><PERSON> thập lục phân không hợp lệ."}, "invalidHexNumberLeadingZeros": {"message": "<PERSON><PERSON> thập lục phân không hợp lệ. <PERSON><PERSON><PERSON> mọi số 0 ở đầu."}, "invalidIpfsGateway": {"message": "Cổng kết nối IPFS không hợp lệ: <PERSON><PERSON><PERSON> trị phải là URL hợp lệ"}, "invalidNumber": {"message": "<PERSON><PERSON> không hợp lệ. <PERSON><PERSON><PERSON> nhập một số thập phân hoặc số thập lục phân bắt đầu bằng “0x”."}, "invalidNumberLeadingZeros": {"message": "<PERSON><PERSON> không hợp lệ. Xóa mọi số 0 ở đầu."}, "invalidRPC": {"message": "URL RPC không hợp lệ"}, "invalidSeedPhrase": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật không hợp lệ"}, "invalidSeedPhraseCaseSensitive": {"message": "<PERSON>ội dung nhập không hợp lệ! C<PERSON><PERSON> từ khôi phục bí mật phân biệt chữ hoa và chữ thường."}, "ipfsGateway": {"message": "Cổng IPFS"}, "ipfsGatewayDescription": {"message": "MetaMask sử dụng các dịch vụ của bên thứ ba để hiển thị hình ảnh các NFT của bạn được lưu trữ trên IPFS, hiển thị thông tin liên quan đến địa chỉ ENS được nhập trong thanh địa chỉ của trình duyệt và tìm nạp biểu tượng cho các token khác nhau. Địa chỉ IP của bạn có thể được hiển thị với các dịch vụ này khi bạn đang sử dụng chúng."}, "ipfsToggleModalDescriptionOne": {"message": "<PERSON>úng tôi sử dụng các dịch vụ của bên thứ ba để hiển thị hình ảnh các NFT của bạn được lưu trữ trên IPFS, hiển thị thông tin liên quan đến địa chỉ ENS được nhập trong thanh địa chỉ của trình duyệt và tìm nạp biểu tượng cho các token khác nhau. Địa chỉ IP của bạn có thể được hiển thị với các dịch vụ này khi bạn đang sử dụng chúng."}, "ipfsToggleModalDescriptionTwo": {"message": "<PERSON><PERSON><PERSON><PERSON> chọn <PERSON> nhận sẽ bật độ phân giải IPFS. Bạn có thể tắt nó trong $1 bất cứ lúc nào.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Cài đặt > <PERSON><PERSON><PERSON> mật và quyền riêng tư"}, "isSigningOrSubmitting": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ch trước đó vẫn đang được ký hoặc gửi"}, "jazzAndBlockies": {"message": "Jazzicons và Blockies là hai kiểu biểu tượng độc nhất khác nhau gi<PERSON>p bạn nhận ra tài khoản trong nháy mắt."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "<PERSON>ập tin JSON", "description": "format for importing an account"}, "keyringAccountName": {"message": "<PERSON><PERSON><PERSON> tà<PERSON>"}, "keyringAccountPublicAddress": {"message": "Địa chỉ công khai"}, "keyringSnapRemovalResult1": {"message": "Đã xóa $1 $2", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "kh<PERSON>ng ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Nhập $1 để xác nhận bạn muốn xóa Snap này:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Đ<PERSON>a chỉ hợp đồng đã biết."}, "knownTokenWarning": {"message": "Hành động này sẽ chỉnh sửa các token đã niêm yết trong ví của bạn, kẻ xấu có thể lợi dụng việc này để lừa đảo bạn. Chỉ chấp thuận nếu bạn chắc chắn rằng bạn muốn thay đổi giá trị mà những token này đại diện cho. Tìm hiểu thêm về $1"}, "l1Fee": {"message": "Phí L1"}, "l1FeeTooltip": {"message": "Phí gas L1"}, "l2Fee": {"message": "Phí L2"}, "l2FeeTooltip": {"message": "Phí gas L2"}, "lastConnected": {"message": "<PERSON><PERSON> kết nối lần cuối"}, "lastSold": {"message": "<PERSON><PERSON> b<PERSON> gần n<PERSON>t"}, "lavaDomeCopyWarning": {"message": "<PERSON><PERSON> sự an toàn của bạn, vi<PERSON><PERSON> chọn văn bản này hiện không khả dụng."}, "layer1Fees": {"message": "Phí Lớp 1"}, "layer2Fees": {"message": "Phí Lớp 2"}, "learnHow": {"message": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> c<PERSON>ch thức"}, "learnMore": {"message": "tìm hiểu thêm"}, "learnMoreAboutGas": {"message": "Muốn $1 về gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "<PERSON><PERSON>m hiểu thêm về các cách thức bảo mật tốt nhất."}, "learnMoreAboutSolanaAccounts": {"message": "<PERSON><PERSON><PERSON> hiểu thêm về tài k<PERSON>ản <PERSON>"}, "learnMoreKeystone": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "learnMoreUpperCaseWithDot": {"message": "<PERSON><PERSON><PERSON> hiểu thêm."}, "learnScamRisk": {"message": "lừa đảo và nguy cơ bảo mật."}, "leaveMetaMask": {"message": "Rời khỏi MetaMask?"}, "leaveMetaMaskDesc": {"message": "Bạn sắp truy cập một trang web bên ngoài MetaMask. H<PERSON><PERSON> kiểm tra kỹ đường dẫn URL trước khi tiếp tục."}, "ledgerAccountRestriction": {"message": "Bạn cần sử dụng tài khoản gần đây nhất thì mới có thể thêm một tài khoản mới."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "<PERSON><PERSON><PERSON> bất kỳ phần mềm nào khác đư<PERSON><PERSON> kết nối với thiết bị của bạn và sau đó nhấp vào đây để làm mới."}, "ledgerConnectionInstructionHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> khi nhấp xác nhận:"}, "ledgerConnectionInstructionStepFour": {"message": "<PERSON><PERSON>t \"dữ liệu hợp đồng thông minh\" hoặc \"ký mù\" trên thiết bị <PERSON>ger của bạn."}, "ledgerConnectionInstructionStepThree": {"message": "<PERSON><PERSON><PERSON> cắm thiết bị Ledger và chọn ứng dụng Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "Mở thiết bị Ledger không thành công. Ledger của bạn có thể đã được kết nối với phần mềm khác. <PERSON>ui lòng đóng Ledger Live hoặc các ứng dụng khác được kết nối với thiết bị Ledger của bạn và thử kết nối lại."}, "ledgerErrorConnectionIssue": {"message": "<PERSON><PERSON>t nối lại với <PERSON> của bạn, mở ứng dụng ETH và thử lại."}, "ledgerErrorDevicedLocked": {"message": "Ledger của bạn đã bị khóa. <PERSON><PERSON> lòng mở khóa rồi thử lại."}, "ledgerErrorEthAppNotOpen": {"message": "<PERSON><PERSON> khắc phục sự cố này, vui lòng mở ứng dụng ETH trên thiết bị của bạn và thử lại."}, "ledgerErrorTransactionDataNotPadded": {"message": "<PERSON><PERSON><PERSON><PERSON> có đủ phần đệm trong dữ liệu đầu vào của giao dịch Ethereum."}, "ledgerLiveApp": {"message": "Ứng dụng Ledger Live"}, "ledgerLocked": {"message": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với thiết bị <PERSON>. <PERSON>ui lòng đảm bảo bạn đã mở khóa thiết bị và mở ứng dụng Ethereum."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "<PERSON><PERSON> kết nối thiết bị mới, h<PERSON><PERSON> ngắt kết nối thiết bị trước đó."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Bạn chỉ có thể kết nối một thiết bị Ledger tại một thời điểm"}, "ledgerTimeout": {"message": "Ledger Live mất quá nhiều thời gian để phản hồi hoặc đã hết thời gian chờ kết nối. Hãy đảm bảo bạn đã mở ứng dụng Ledger Live và đã mở khóa thiết bị."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "<PERSON><PERSON><PERSON><PERSON> bị Ledger chưa được kết nối. <PERSON><PERSON><PERSON> bạn muốn kết nối vớ<PERSON>, vui lòng nhấp lại vào \"<PERSON><PERSON><PERSON><PERSON> tục\" và chấp thuận kết nối HID", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "m<PERSON><PERSON> tên cấp độ"}, "lightTheme": {"message": "<PERSON><PERSON><PERSON>"}, "likeToImportToken": {"message": "Bạn có muốn nhập token này không?"}, "likeToImportTokens": {"message": "Bạn có muốn nhập những token này không?"}, "lineaGoerli": {"message": "<PERSON><PERSON><PERSON> thử nghiệm <PERSON>a Goerli"}, "lineaMainnet": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>h thức c<PERSON>a <PERSON>a"}, "lineaSepolia": {"message": "<PERSON><PERSON><PERSON> thử nghiệm Linea Sepolia"}, "link": {"message": "<PERSON><PERSON><PERSON>"}, "linkCentralizedExchanges": {"message": "Liên kết tài khoản Coinbase hoặc Binance của bạn để chuyển tiền mã hóa đến MetaMask miễn phí."}, "links": {"message": "<PERSON><PERSON><PERSON>"}, "loadMore": {"message": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "loading": {"message": "<PERSON><PERSON> tả<PERSON>..."}, "loadingScreenSnapMessage": {"message": "<PERSON><PERSON> lòng hoàn tất giao dịch trên <PERSON>."}, "loadingTokenList": {"message": "<PERSON><PERSON> tải danh s<PERSON>ch <PERSON>"}, "localhost": {"message": "Máy chủ cục bộ 8545"}, "lock": {"message": "Khóa"}, "lockMetaMask": {"message": "Khóa MetaMask"}, "lockTimeInvalid": {"message": "Thời gian khóa phải là một số từ 0 đến 10080"}, "logo": {"message": "Logo $1", "description": "$1 is the name of the ticker"}, "low": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "lowEstimatedReturnTooltipMessage": {"message": "Bạn sẽ phải trả phí giao dịch cao hơn $1% số tiền ban đầu. <PERSON><PERSON><PERSON> kiểm tra số tiền nhận được và phí mạng."}, "lowEstimatedReturnTooltipTitle": {"message": "Chi phí cao"}, "lowGasSettingToolTipMessage": {"message": "Sử dụng $1 để chờ mức giá rẻ hơn. Thời gian dự kiến sẽ kém chính xác hơn nhiều do mức giá tương đối khó dự đoán.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "thấp"}, "mainnet": {"message": "Ethereum Mainnet"}, "mainnetToken": {"message": "Địa chỉ này trùng với một địa chỉ đã biết trên Ethereum Mainnet. Hãy kiểm tra lại địa chỉ hợp đồng và mạng cho token mà bạn đang muốn thêm."}, "makeAnotherSwap": {"message": "<PERSON><PERSON>o một giao dịch hoán đổi mới"}, "makeSureNoOneWatching": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> kh<PERSON>ng có ai đang nh<PERSON>n", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "<PERSON><PERSON><PERSON>n lý chế độ cài đặt quyền riêng tư mặc định"}, "manageInstitutionalWallets": {"message": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> tổ chức"}, "manageInstitutionalWalletsDescription": {"message": "<PERSON><PERSON>t tùy chọn này để sử dụng ví tổ chức."}, "manageNetworksMenuHeading": {"message": "<PERSON><PERSON><PERSON><PERSON> lý mạng"}, "managePermissions": {"message": "<PERSON><PERSON><PERSON><PERSON> lý quyền"}, "marketCap": {"message": "<PERSON><PERSON><PERSON> hóa thị trường"}, "marketDetails": {"message": "<PERSON> tiết thị trường"}, "max": {"message": "<PERSON><PERSON><PERSON> đa"}, "maxBaseFee": {"message": "<PERSON><PERSON> cơ sở tối đa"}, "maxFee": {"message": "<PERSON><PERSON> tối đa"}, "maxFeeTooltip": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n phí tối đa đư<PERSON><PERSON> cung cấp để thanh toán cho giao dịch."}, "maxPriorityFee": {"message": "<PERSON><PERSON> <PERSON><PERSON> tiên tối đa"}, "medium": {"message": "<PERSON><PERSON><PERSON> trường"}, "mediumGasSettingToolTipMessage": {"message": "Sử dụng $1 để xử lý nhanh theo giá thị trường hiện tại.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "b<PERSON>n ghi nhớ"}, "message": {"message": "<PERSON><PERSON><PERSON><PERSON> báo"}, "metaMaskConnectStatusParagraphOne": {"message": "<PERSON><PERSON><PERSON>, bạn có nhiều quyền kiểm soát hơn đối với các kết nối của tài khoản trong MetaMask."}, "metaMaskConnectStatusParagraphThree": {"message": "<PERSON><PERSON><PERSON><PERSON> vào để quản lý các tài khoản đã kết nối của bạn."}, "metaMaskConnectStatusParagraphTwo": {"message": "<PERSON><PERSON>t trạng thái kết nối sẽ hiển thị nếu trang web mà bạn đang truy cập được kết nối với tài khoản bạn đang chọn."}, "metaMetricsIdNotAvailableError": {"message": "<PERSON><PERSON> bạn chưa bao giờ chọn tham gia MetaMetrics, nên ở đây không có dữ liệu nào để xóa."}, "metadataModalSourceTooltip": {"message": "$1 đượ<PERSON> lưu trữ trên npm và $2 là mã định danh duy nhất của Snap này.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "<PERSON>h<PERSON><PERSON> báo ví hiện không hoạt động."}, "metamaskSwapsOfflineDescription": {"message": "<PERSON><PERSON><PERSON> năng <PERSON>án đổi trên MetaMask đang được bảo trì. <PERSON>ui lòng kiểm tra lại sau."}, "metamaskVersion": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "methodData": {"message": "<PERSON><PERSON><PERSON><PERSON> thức"}, "methodDataTransactionDesc": {"message": "<PERSON><PERSON><PERSON> năng đư<PERSON><PERSON> thực hiện dựa trên dữ liệu đầu vào đã giải mã."}, "methodNotSupported": {"message": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ với tài khoản này."}, "metrics": {"message": "Chỉ số"}, "millionAbbreviation": {"message": "<PERSON><PERSON><PERSON>", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "x<PERSON><PERSON> minh thông tin về mạng", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Bạn nên $1 trước khi tiếp tục.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "<PERSON> sơ của chúng tô<PERSON>, tên mạng có thể không khớp hoàn toàn với ID chuỗi này."}, "mismatchedNetworkSymbol": {"message": "<PERSON><PERSON> hiệu đơn vị tiền tệ đã gửi không khớp với những gì chúng tôi mong đợi cho ID chuỗi này."}, "mismatchedRpcChainId": {"message": "ID chuỗi do mạng tùy chỉnh trả về không khớp với ID chuỗi đã gửi."}, "mismatchedRpcUrl": {"message": "<PERSON> sơ củ<PERSON> ch<PERSON>ô<PERSON>, gi<PERSON> trị RPC URL đã gửi không khớp với một nhà cung cấp đã biết cho ID chuỗi này."}, "missingSetting": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chế độ cài đặt?"}, "missingSettingRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u tại đây"}, "more": {"message": "thêm"}, "moreAccounts": {"message": "+ $1 tài k<PERSON> n<PERSON>a", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1 mạng n<PERSON>a", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "<PERSON><PERSON><PERSON><PERSON> báo giá"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Bạn đang thêm mạng này vào MetaMask và cấp cho trang web này quyền sử dụng mạng này."}, "multichainQuoteCardBridgingLabel": {"message": "<PERSON><PERSON><PERSON>"}, "multichainQuoteCardQuoteLabel": {"message": "Báo giá"}, "multichainQuoteCardTimeLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> gian"}, "multipleSnapConnectionWarning": {"message": "$1 muốn sử dụng $2 Snap", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "<PERSON><PERSON><PERSON> chọn ít nhất 1 token."}, "name": {"message": "<PERSON><PERSON><PERSON>"}, "nameAddressLabel": {"message": "Địa chỉ", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "<PERSON>ê<PERSON> đã đư<PERSON>c sử dụng"}, "nameInstructionsNew": {"message": "<PERSON><PERSON><PERSON> bạn biết địa chỉ này, hãy đặt biệt danh để dễ nhận biết trong tương lai.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "Địa chỉ này có một biệt danh mặc định, nhưng bạn có thể chỉnh sửa hoặc tham khảo các đề xuất khác.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Trước đây bạn đã thêm biệt danh cho địa chỉ này rồi. Bạn có thể chỉnh sửa hoặc xem các biệt danh được đề xuất khác.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "<PERSON><PERSON> thể: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Địa chỉ không xác định", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Địa chỉ đã được nhận biết", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Địa chỉ đã lưu", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "<PERSON><PERSON><PERSON><PERSON> đề xuất bởi $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "<PERSON><PERSON><PERSON> vụ đăng ký tên miền trên Ethereum (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "<PERSON><PERSON><PERSON> một biệt danh...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 đang yêu cầu sự chấp thuận của bạn cho:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Chỉnh sửa thông tin mạng"}, "nativeTokenScamWarningDescription": {"message": "Ký hiệu token gốc không khớp với ký hiệu dự kiến của token gốc dành cho mạng với ID chuỗi liên quan. Bạn đã nhập $1 trong khi ký hiệu token dự kiến là $2. Vui lòng xác minh rằng bạn đã kết nối với đúng chuỗi.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "c<PERSON><PERSON> b<PERSON><PERSON> k<PERSON>c", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tượ<PERSON> token gốc không mong muốn", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Bạn cần trợ giúp? Liên hệ $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "<PERSON><PERSON> sẻ phản hồi của bạn"}, "needHelpLinkText": {"message": "Hỗ trợ về MetaMask"}, "needHelpSubmitTicket": {"message": "<PERSON><PERSON><PERSON>"}, "needImportFile": {"message": "<PERSON><PERSON>n ph<PERSON>i chọn tập tin để nhập.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "<PERSON><PERSON><PERSON><PERSON> thể gửi số lượng ETH âm."}, "negativeOrZeroAmountToken": {"message": "<PERSON><PERSON><PERSON><PERSON> thể gửi số lượng tài sản âm hoặc bằng 0."}, "network": {"message": "Mạng"}, "networkChanged": {"message": "<PERSON><PERSON> thay đổi mạng"}, "networkChangedMessage": {"message": "Bạn hiện đang giao dịch trên $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Thông tin về mạng"}, "networkFee": {"message": "<PERSON><PERSON> m<PERSON>"}, "networkIsBusy": {"message": "Mạng đang bận. Giá gas cao và ước tính kém chính xác h<PERSON>n."}, "networkMenu": {"message": "<PERSON><PERSON><PERSON><PERSON>n mạng"}, "networkMenuHeading": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "networkName": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> liên kết với mạng này."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OP Mainnet"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "<PERSON><PERSON><PERSON> thử nghi<PERSON>m"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "<PERSON><PERSON><PERSON> ch<PERSON> m<PERSON>ng"}, "networkPermissionToast": {"message": "<PERSON><PERSON> cập nhật quyền đối với mạng"}, "networkProvider": {"message": "<PERSON><PERSON><PERSON> cung cấp mạng"}, "networkStatus": {"message": "<PERSON>r<PERSON><PERSON> thái mạng"}, "networkStatusBaseFeeTooltip": {"message": "<PERSON><PERSON> cơ sở do mạng thiết lập và thay đổi sau mỗi 13 - 14 giây. <PERSON><PERSON><PERSON> tùy chọn $1 và $2 của chúng tôi tính đến các mức tăng đột biến.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "<PERSON><PERSON><PERSON>ng phí ưu tiên (hay còn được gọi là \"tiền thưởng cho thợ đào). Phí ưu tiên sẽ được chuyển cho thợ đào và khuyến khích họ ưu tiên giao dịch của bạn."}, "networkStatusStabilityFeeTooltip": {"message": "Phí gas tương đối $1 so với 72 giờ qua.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "<PERSON><PERSON>g tôi không thể kết nối với $1", "description": "$1 represents the network name"}, "networkURL": {"message": "URL mạng"}, "networkURLDefinition": {"message": "URL dùng để truy cập vào mạng này."}, "networkUrlErrorWarning": {"message": "Kẻ tấn công đôi khi bắt chước các trang web bằng cách thực hiện những thay đổi nhỏ trong địa chỉ trang web. <PERSON><PERSON><PERSON> bảo bạn đang tương tác với trang web dự định trước khi tiếp tục. Phiên bản Punycode: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Mạng"}, "networksSmallCase": {"message": "mạng"}, "nevermind": {"message": "Bỏ qua"}, "new": {"message": "<PERSON><PERSON><PERSON>!"}, "newAccount": {"message": "<PERSON><PERSON><PERSON> k<PERSON>n mới"}, "newAccountNumberName": {"message": "Tài k<PERSON> $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "<PERSON><PERSON><PERSON> mới"}, "newContract": {"message": "<PERSON><PERSON><PERSON> đồng mới"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Cài đặt > <PERSON><PERSON><PERSON> mật và quyền riêng tư"}, "newNFTDetectedInImportNFTsMsg": {"message": "<PERSON><PERSON> sử dụng Opensea để xem NFT c<PERSON><PERSON> b<PERSON>, h<PERSON><PERSON> b<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> thị Phương tiện NFT' trong $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "<PERSON> phép MetaMask tự động phát hiện và hiển thị NFT trong ví của bạn."}, "newNFTsAutodetected": {"message": "Tự động phát hiện NFT"}, "newNetworkAdded": {"message": "“$1” đã đ<PERSON><PERSON><PERSON> thêm thành công!"}, "newNetworkEdited": {"message": "“$1” đã được chỉnh sửa thành công!"}, "newNftAddedMessage": {"message": "NFT đã đư<PERSON><PERSON> thêm thành công!"}, "newPassword": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u mới (tối thiểu 8 ký tự)"}, "newPrivacyPolicyActionButton": {"message": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "newPrivacyPolicyTitle": {"message": "<PERSON><PERSON><PERSON> tôi đã cập nhật ch<PERSON>h sách quyền riêng tư"}, "newRpcUrl": {"message": "URL RPC mới"}, "newTokensImportedMessage": {"message": "Bạn đã nhập thành công $1.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "<PERSON><PERSON> nhập token"}, "next": {"message": "<PERSON><PERSON><PERSON><PERSON> theo"}, "nftAddFailedMessage": {"message": "<PERSON>h<PERSON><PERSON> thể thêm NFT vì thông tin quyền sở hữu không trùng khớp. <PERSON><PERSON><PERSON> bảo bạn đã nhập đúng thông tin."}, "nftAddressError": {"message": "Token này là một NFT. Thêm vào $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT đã đư<PERSON><PERSON> thêm vào."}, "nftAutoDetectionEnabled": {"message": "<PERSON><PERSON><PERSON> năng tự động phát hiện NFT đã đư<PERSON><PERSON> bật"}, "nftDisclaimer": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> miễn trừ trách nhiệm: MetaMask lấy tập tin đa phương tiện từ URL nguồn. URL này đôi khi bị thay đổi bởi thị trường mà NFT được đào."}, "nftOptions": {"message": "<PERSON><PERSON><PERSON> N<PERSON>"}, "nftTokenIdPlaceholder": {"message": "<PERSON><PERSON><PERSON><PERSON> mã token"}, "nftWarningContent": {"message": "Bạn đang cấp quyền truy cập vào $1, bao gồm bất cứ tài sản nào mà bạn có thể sở hữu trong tương lai. Bê<PERSON> đư<PERSON><PERSON> cấp quyền có thể chuyển các NFT này khỏi ví của bạn bất cứ lúc nào mà không cần hỏi bạn cho đến khi bạn thu hồi sự chấp thuận này. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "tất cả NFT $1 của bạn", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "<PERSON><PERSON><PERSON> tiến hành thận trọng."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "Sở hữu trước đây"}, "nickname": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "noAccountsFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài khoản nào cho cụm từ tìm kiếm đã đưa ra"}, "noActivity": {"message": "Chưa có hoạt động nào"}, "noConnectedAccountTitle": {"message": "MetaMask không đư<PERSON><PERSON> kết nối với trang web này"}, "noConnectionDescription": {"message": "<PERSON><PERSON> kết nối với trang web, hãy tìm và chọn nút \"kết nối\". <PERSON><PERSON><PERSON> <PERSON>hớ rằng, MetaMask chỉ có thể kết nối với các trang web trên web3"}, "noConversionRateAvailable": {"message": "<PERSON><PERSON><PERSON><PERSON> có sẵn tỷ lệ quy đổi nào"}, "noDeFiPositions": {"message": "Chưa có vị thế nào"}, "noDomainResolution": {"message": "<PERSON><PERSON><PERSON><PERSON> có nội dung phân giải cho tên miền đ<PERSON><PERSON><PERSON> cung cấp."}, "noHardwareWalletOrSnapsSupport": {"message": "Snap và hầu hết các ví cứng sẽ không hoạt động với phiên bản trình duyệt hiện tại của bạn."}, "noNFTs": {"message": "Chưa có NFT"}, "noNetworksFound": {"message": "<PERSON><PERSON><PERSON>ng tìm thấy mạng nào cho truy vấn tìm kiếm"}, "noOptionsAvailableMessage": {"message": "Tuyến giao dịch này hiện không khả dụng. <PERSON><PERSON><PERSON> thử thay đổi số tiền, mạng hoặc token và chúng tôi sẽ tìm ra tùy chọn tốt nhất."}, "noSnaps": {"message": "Bạn chưa cài đặt bất kỳ Snap nào."}, "noThanks": {"message": "<PERSON><PERSON><PERSON><PERSON>, cảm ơn"}, "noTransactions": {"message": "Bạn không có giao dịch nào"}, "noWebcamFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy webcam trên máy tính của bạn. <PERSON><PERSON> lòng thử lại."}, "noWebcamFoundTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy webcam"}, "nonContractAddressAlertDesc": {"message": "Bạn đang gửi dữ liệu gọi hàm đến một địa chỉ không phải là hợp đồng. Điều này có thể khiến bạn mất tiền. Hãy đảm bảo rằng bạn đang sử dụng đúng địa chỉ và mạng trước khi tiếp tục."}, "nonContractAddressAlertTitle": {"message": "Lỗi tiềm <PERSON>n"}, "nonce": {"message": "Số nonce"}, "none": {"message": "<PERSON><PERSON><PERSON><PERSON> có"}, "notBusy": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>n"}, "notCurrentAccount": {"message": "Tài khoản này có chính xác không? Tài khoản này khác với tài khoản bạn đang chọn trong ví của mình"}, "notEnoughBalance": {"message": "<PERSON><PERSON><PERSON><PERSON> đủ số dư"}, "notEnoughGas": {"message": "Không đủ phí gas"}, "notNow": {"message": "Không phải bây giờ"}, "notificationDetail": {"message": "<PERSON> ti<PERSON>"}, "notificationDetailBaseFee": {"message": "<PERSON><PERSON> cơ sở (GWEI)"}, "notificationDetailGasLimit": {"message": "<PERSON><PERSON><PERSON> mức phí gas (đơn vị)"}, "notificationDetailGasUsed": {"message": "<PERSON>í gas đã dùng (đơn vị)"}, "notificationDetailMaxFee": {"message": "<PERSON><PERSON><PERSON> phí tối đa mỗi gas"}, "notificationDetailNetwork": {"message": "Mạng"}, "notificationDetailNetworkFee": {"message": "<PERSON><PERSON> m<PERSON>"}, "notificationDetailPriorityFee": {"message": "<PERSON><PERSON> <PERSON><PERSON> (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "<PERSON><PERSON><PERSON> tra trên Block Explorer"}, "notificationItemCollection": {"message": "<PERSON><PERSON> s<PERSON>u tập"}, "notificationItemConfirmed": {"message": "<PERSON><PERSON> x<PERSON>c <PERSON>n"}, "notificationItemError": {"message": "<PERSON><PERSON><PERSON> không thể truy xuất phí"}, "notificationItemFrom": {"message": "Từ"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Sẵn sàng rút tiền"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Bây giờ bạn có thể rút $1 chưa ký gửi của bạn"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Đã gửi yêu cầu hủy ký gửi $1 của bạn"}, "notificationItemNFTReceivedFrom": {"message": "Đã nhận NFT từ"}, "notificationItemNFTSentTo": {"message": "Đ<PERSON> gửi NFT đến"}, "notificationItemNetwork": {"message": "Mạng"}, "notificationItemRate": {"message": "Tỷ giá (đã bao gồm phí)"}, "notificationItemReceived": {"message": "Đã nhận"}, "notificationItemReceivedFrom": {"message": "<PERSON><PERSON> nhận từ"}, "notificationItemSent": {"message": "Đ<PERSON> gửi"}, "notificationItemSentTo": {"message": "<PERSON><PERSON> g<PERSON>i đến"}, "notificationItemStakeCompleted": {"message": "<PERSON><PERSON> hoàn tất ký gửi"}, "notificationItemStaked": {"message": "Đã ký gửi"}, "notificationItemStakingProvider": {"message": "<PERSON><PERSON>à cung cấp dịch vụ ký g<PERSON>i"}, "notificationItemStatus": {"message": "<PERSON><PERSON><PERSON><PERSON> thái"}, "notificationItemSwapped": {"message": "<PERSON><PERSON> ho<PERSON> đổi"}, "notificationItemSwappedFor": {"message": "<PERSON><PERSON> l<PERSON>y"}, "notificationItemTo": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemTransactionId": {"message": "ID giao d<PERSON>ch"}, "notificationItemUnStakeCompleted": {"message": "<PERSON><PERSON><PERSON> tất hủy ký g<PERSON>i"}, "notificationItemUnStaked": {"message": "<PERSON><PERSON> hủy ký gửi"}, "notificationItemUnStakingRequested": {"message": "<PERSON><PERSON> yêu cầu hủy ký g<PERSON>i"}, "notificationTransactionFailedMessage": {"message": "Giao dịch $1 không thành công! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không thành công", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Giao d<PERSON>ch $1 đã đư<PERSON>c xác nhận!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "<PERSON><PERSON> xác nh<PERSON>n giao dịch", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Xem trên $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "<PERSON><PERSON><PERSON><PERSON> báo"}, "notificationsFeatureToggle": {"message": "<PERSON><PERSON><PERSON> thông báo ví", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> này cho phép nhận thông báo ví khi gửi/nhận tiền hoặc NFT và khi có thông báo về các tính năng.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "<PERSON><PERSON><PERSON> dấu đã đọc tất cả"}, "notificationsPageEmptyTitle": {"message": "<PERSON><PERSON><PERSON>ng có thông báo nào ở đây"}, "notificationsPageErrorContent": {"message": "<PERSON><PERSON> lòng thử truy cập lại trang này."}, "notificationsPageErrorTitle": {"message": "Đ<PERSON> xảy ra lỗi"}, "notificationsPageNoNotificationsContent": {"message": "<PERSON>ạn chưa nhận đ<PERSON><PERSON><PERSON> bất kỳ thông báo nào."}, "notificationsSettingsBoxError": {"message": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON> lòng thử lại."}, "notificationsSettingsPageAllowNotifications": {"message": "<PERSON><PERSON><PERSON><PERSON> thông báo để cập nhật tình hình trong ví của bạn. <PERSON><PERSON> sử dụng tính năng thông báo, chúng tôi dùng hồ sơ để đồng bộ một số chế độ cài đặt trên các thiết bị của bạn. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> cách chúng tôi bảo vệ quyền riêng tư của bạn khi sử dụng tính năng này."}, "numberOfNewTokensDetectedPlural": {"message": "T<PERSON>m thấy $1 token mới trong tài khoản này", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "<PERSON><PERSON><PERSON> thấy 1 token mới trong tài khoản này"}, "numberOfTokens": {"message": "<PERSON><PERSON> l<PERSON>ng token"}, "ofTextNofM": {"message": "trên"}, "off": {"message": "Tắt"}, "offlineForMaintenance": {"message": "<PERSON><PERSON><PERSON><PERSON> tuyến để bảo trì"}, "ok": {"message": "Ok"}, "on": {"message": "<PERSON><PERSON><PERSON>"}, "onboardedMetametricsAccept": {"message": "<PERSON><PERSON><PERSON> đồng <PERSON>"}, "onboardedMetametricsDisagree": {"message": "<PERSON><PERSON><PERSON><PERSON>, cảm ơn"}, "onboardedMetametricsKey1": {"message": "<PERSON><PERSON><PERSON><PERSON> tiến mới nhất"}, "onboardedMetametricsKey2": {"message": "<PERSON><PERSON><PERSON> n<PERSON>ng sản phẩm"}, "onboardedMetametricsKey3": {"message": "<PERSON><PERSON><PERSON> tài liệu khu<PERSON>ến mại liên quan khác"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "Ngoài $1, chúng tôi muốn sử dụng dữ liệu để hiểu cách bạn tương tác với với các thông tin tiếp thị.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "<PERSON><PERSON><PERSON>u này sẽ gi<PERSON>p chúng tôi cá nhân hóa nội dung mà chúng tôi chia sẻ với bạn, chẳng hạn như:"}, "onboardedMetametricsParagraph3": {"message": "Hãy nhớ rằng chúng tôi không bán dữ liệu mà bạn cung cấp và bạn có thể chọn không tham gia bất kỳ lúc nào."}, "onboardedMetametricsTitle": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> chúng tôi nâng cao tr<PERSON>i nghiệm của bạn"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "Cổng IPFS cho phép truy cập và xem dữ liệu do bên thứ ba lưu trữ. Bạn có thể thêm cổng IPFS tùy chỉnh hoặc tiếp tục sử dụng cổng mặc định."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "<PERSON><PERSON> lòng nhập URL hợp lệ"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "T<PERSON>êm Cổng IPFS tùy chỉnh"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "URL cổng IPFS hợp lệ"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "<PERSON><PERSON> bạn sử dụng các chế độ cài đặt và cấu hình mặc định của chúng tôi, chúng tôi sử dụng Infura làm nhà cung cấp gọi hàm từ xa (RPC) mặc định để cung cấp quyền truy cập dữ liệu Ethereum đáng tin cậy và riêng tư nhất có thể. Trong một số trường hợp giớ<PERSON> hạn, chúng tôi có thể sử dụng các nhà cung cấp RPC khác nhằm mang đến trải nghiệm tốt nhất cho người dùng. Bạn có thể chọn RPC của riêng mình, nhưng hãy nhớ rằng bất kỳ RPC nào cũng sẽ nhận được địa chỉ IP và ví Ethereum của bạn để thực hiện giao dịch. <PERSON>ể tìm hiểu thêm về cách Infura xử lý dữ liệu đối với các tài khoản EVM, hãy đọc $1, và đối với các tài khoản Solana, hãy đọc $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "nhấn vào đây"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "<PERSON><PERSON><PERSON> mạng của bạn"}, "onboardingCreateWallet": {"message": "Tạo ví mới"}, "onboardingImportWallet": {"message": "<PERSON><PERSON><PERSON><PERSON> ví có sẵn"}, "onboardingMetametricsAgree": {"message": "<PERSON><PERSON><PERSON> đồng <PERSON>"}, "onboardingMetametricsDescription": {"message": "Chúng tôi muốn thu thập dữ liệu sử dụng và chẩn đoán cơ bản để cải tiến MetaMask. <PERSON>n lưu ý, chúng tôi không bao giờ bán dữ liệu mà bạn cung cấp ở đây."}, "onboardingMetametricsInfuraTerms": {"message": "<PERSON>úng tôi sẽ thông báo cho bạn nếu chúng tôi quyết định sử dụng dữ liệu này cho các mục đích khác. Bạn có thể xem lại $1 của chúng tôi để biết thêm thông tin. L<PERSON>u ý, bạn có thể truy cập cài đặt và chọn không tham gia bất kỳ lúc nào.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư"}, "onboardingMetametricsNeverCollect": {"message": "$1 chỉ lưu trữ các lượt nhấn và lượt xem trên ứng dụng, các thông tin khác (chẳng hạn như địa chỉ công khai của bạn) sẽ không được lưu trữ.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "<PERSON><PERSON><PERSON><PERSON> tư:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 chúng tôi tạm thời sử dụng địa chỉ IP của bạn để xác định vị trí tổng quan (chẳng hạn như quốc gia hoặc khu vực của bạn), nhưng dữ liệu này sẽ không bao giờ được lưu trữ.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Chung:"}, "onboardingMetametricsNeverSellData": {"message": "$1 bạn có thể chọn chia sẻ hoặc xóa dữ liệu sử dụng của mình trong phần cài đặt bất kỳ lúc nào.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>:"}, "onboardingMetametricsTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> chúng tôi cải thiện MetaMask"}, "onboardingMetametricsUseDataCheckbox": {"message": "Chúng tôi sẽ sử dụng dữ liệu này để tìm hiểu cách bạn tương tác với các thông tin tiếp thị. Chúng tôi có thể chia sẻ tin tức liên quan (chẳng hạn như tính năng sản phẩm)."}, "onboardingPinExtensionDescription": {"message": "<PERSON><PERSON>ask trên trình duyệt để bạn có thể truy cập và dễ dàng xem các xác nhận giao dịch."}, "onboardingPinExtensionDescription2": {"message": "Bạn có thể mở MetaMask bằng cách nhấp vào tiện ích và truy cập ví của mình chỉ với 1 cú nhấp chuột."}, "onboardingPinExtensionDescription3": {"message": "<PERSON><PERSON><PERSON><PERSON> vào biểu tượng tiện ích trên trình duyệt để truy cập tức thì", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Quá trình cài đặt MetaMask đã hoàn tất!"}, "onekey": {"message": "OneKey"}, "only": {"message": "chỉ"}, "onlyConnectTrust": {"message": "Chỉ kết nối với các trang web mà bạn tin tưởng. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Bật toàn màn hình để kết nối với thiết bị <PERSON>ger của bạn.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Mở trên trình khám phá khối"}, "optional": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c"}, "options": {"message": "<PERSON><PERSON><PERSON>"}, "origin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "originChanged": {"message": "<PERSON><PERSON> thay đổi trang web"}, "originChangedMessage": {"message": "Bạn hiện đang xem xét một yêu cầu từ $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "<PERSON><PERSON> th<PERSON>"}, "other": {"message": "lo<PERSON><PERSON> k<PERSON>c"}, "otherSnaps": {"message": "Snap khác", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "lo<PERSON><PERSON> k<PERSON>c"}, "outdatedBrowserNotification": {"message": "Trình duyệt của bạn đã cũ. <PERSON><PERSON><PERSON> không cập nhật trình duyệt, bạn sẽ không thể nhận các bản vá bảo mật và tính năng mới từ MetaMask."}, "overrideContentSecurityPolicyHeader": {"message": "<PERSON><PERSON> đè tiêu đề Nội dung-<PERSON><PERSON><PERSON> mật-<PERSON><PERSON><PERSON><PERSON> tư"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "T<PERSON><PERSON> chọn này là giải pháp cho một vấn đề đã biết trên Firefox, nơi tiêu đề Nội dung-B<PERSON><PERSON> mật-Riêng tư của một dapp có thể ngăn tiện ích mở rộng tải đúng cách. Không nên tắt tùy chọn này trừ khi cần thiết để tương thích với trang web cụ thể."}, "padlock": {"message": "Ổ khóa"}, "participateInMetaMetrics": {"message": "Tham gia MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Tham gia MetaMetrics để gi<PERSON>p chúng tôi cải thiện MetaMask"}, "password": {"message": "<PERSON><PERSON><PERSON>"}, "passwordNotLongEnough": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u không đủ dài"}, "passwordStrength": {"message": "<PERSON><PERSON> mạnh của mật khẩu: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "<PERSON><PERSON>t mật khẩu mạnh có thể giúp tăng cường bảo mật cho ví nếu thiết bị của bạn bị đánh cắp hoặc xâm phạm."}, "passwordTermsWarning": {"message": "Tôi hiểu rằng MetaMask không thể khôi phục mật khẩu này cho tôi. $1"}, "passwordsDontMatch": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp"}, "pastePrivateKey": {"message": "<PERSON><PERSON> chuỗi khóa riêng tư của bạn vào đây:", "description": "For importing an account from a private key"}, "pending": {"message": "<PERSON><PERSON> chờ xử lý"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "<PERSON><PERSON><PERSON> bạn cập nh<PERSON><PERSON> mạng, giao d<PERSON>ch $1 đang chờ xử lý từ trang web này sẽ bị hủy.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "<PERSON><PERSON><PERSON> bạn chuyển mạng, giao d<PERSON>ch $1 đang chờ xử lý từ trang web này sẽ bị hủy.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Giao dịch này sẽ không được thực hiện cho đến khi một giao dịch trước đó hoàn thành. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "<PERSON><PERSON><PERSON> hi<PERSON> c<PERSON>ch hủy hoặc tăng tốc một giao dịch.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "<PERSON> ti<PERSON>"}, "permissionFor": {"message": "<PERSON><PERSON><PERSON><PERSON> cho"}, "permissionFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> từ"}, "permissionRequested": {"message": "<PERSON><PERSON> yêu cầu ngay"}, "permissionRequestedForAccounts": {"message": "Hiện đã yêu cầu cho $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "<PERSON><PERSON> thu hồi trong bản cập nh<PERSON>t này"}, "permissionRevokedForAccounts": {"message": "<PERSON><PERSON> thu hồi trong bản cập nhật này cho $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Kết nối với $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "T<PERSON>y cập Internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Cho phép $1 truy cập Internet. <PERSON><PERSON><PERSON><PERSON> này có thể được sử dụng để gửi và nhận dữ liệu với máy chủ của bên thứ ba.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "<PERSON>ết n<PERSON>i vớ<PERSON> Snap $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Cho phép trang web hoặc Snap tương tác với $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "<PERSON><PERSON><PERSON> thị tài sản của tài khoản trong MetaMask.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Cho phép $1 cung cấp thông tin tài sản cho máy khách MetaMask. Tài sản có thể nằm trên chuỗi hoặc ngoài chuỗi.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "<PERSON><PERSON><PERSON> l<PERSON>ch và thực hiện các hành động theo đ<PERSON><PERSON> k<PERSON>.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Cho phép $1 thực hiện các hành động định kỳ vào thời gian, ngày hoặc khoảng thời gian cố định. <PERSON><PERSON><PERSON>u này có thể được sử dụng để kích hoạt các tương tác hoặc thông báo nhạy cảm với thời gian.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "<PERSON><PERSON><PERSON> thị cửa sổ hộp thoại trong MetaMask.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Cho phép $1 hiển thị cửa sổ bật lên MetaMask cùng với văn bản tùy chỉnh, trường nhập thông tin và nút để chấp nhận hoặc từ chối một hành động.\n<PERSON><PERSON> thể được sử dụng để tạo cảnh báo, xác nhận và quy trình đồng ý tham gia cho một Snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "<PERSON><PERSON>, số dư <PERSON>à<PERSON>, ho<PERSON>t động và đề xuất giao dịch để chấp thuận", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "<PERSON><PERSON><PERSON> cập nhà cung cấp Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Cho phép $1 giao tiếp trực tiếp với MetaMask để đọc dữ liệu từ chuỗi khối và đề xuất các tin nhắn và giao dịch.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "<PERSON><PERSON><PERSON> các khóa tùy ý duy nhất cho $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Cho phép $1 lấy các khóa tùy ý duy nhất cho $1 mà không để lộ. <PERSON><PERSON>c khóa này tách biệt với tài khoản MetaMask của bạn và không liên quan đến khóa riêng tư hoặc Cụm từ khôi phục bí mật của bạn. <PERSON><PERSON>c <PERSON>nap khác không thể truy cập thông tin này.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "<PERSON><PERSON> ngôn ng<PERSON> <PERSON>a thích của b<PERSON>n.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Cho phép $1 truy cập ngôn ngữ ưa thích của bạn từ cài đặt MetaMask. Điều này có thể được sử dụng để dịch và hiển thị nội dung của $1 theo ngôn ngữ của bạn.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "<PERSON><PERSON> c<PERSON>c thông tin như ngôn ngữ ưu tiên và loại tiền pháp định của bạn.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Cho phép $1 truy cập các thông tin như ngôn ngữ ưu tiên và loại tiền pháp định trong cài đặt MetaMask của bạn. Điều này giúp $1 hiển thị nội dung phù hợp với sở thích của bạn. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "<PERSON><PERSON>n thị màn hình tùy chỉnh", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Cho phép $1 hiển thị màn hình chính tùy chỉnh trong MetaMask. <PERSON><PERSON> thể được sử dụng cho giao diện người dùng, cấu hình và trang tổng quan.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "<PERSON> phép các yêu cầu thêm và kiểm soát tài k<PERSON>n Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Cho phép $1 nhận các yêu cầu thêm hoặc xóa tài kho<PERSON>n, cũng nh<PERSON> ký và giao dịch thay mặt cho các tài khoản này.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Sử dụng hook vòng đời.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Cho phép $1 sử dụng hook vòng đời để chạy mã vào những thời điểm cụ thể trong vòng đời của nó.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Thêm và kiểm soát các tài khoản Ethereum", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Cho phép $1 thêm hoặc xóa tài khoản Ethereum, sau đó thực hiện giao dịch và ký bằng các tài khoản này.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Q<PERSON>ản lý tài k<PERSON>n $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Cho phép $1 quản lý tài khoản và tài sản trên mạng được yêu cầu. <PERSON><PERSON><PERSON> tài khoản này được lấy và sao lưu bằng cụm từ khôi phục bí mật của bạn (mà không để lộ). Với khả năng lấy khóa, $1 có thể hỗ trợ nhiều giao thức chuỗi khối ngoài Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Q<PERSON>ản lý tài k<PERSON>n $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "<PERSON><PERSON><PERSON> trữ và quản lý dữ liệu trong thiết bị.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Cho phép $1 lưu trữ, cập nhật và truy xuất dữ liệu một cách an toàn bằng mã hóa. <PERSON><PERSON><PERSON>nap khác không thể truy cập thông tin này.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "<PERSON><PERSON> cấp tra cứu tên miền và địa chỉ.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "<PERSON> phép Snap tì<PERSON> n<PERSON>, hiển thị địa chỉ và tra cứu tên miền trong các phần khác nhau của Giao diện người dùng MetaMask.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "<PERSON><PERSON><PERSON> thị thông báo.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Cho phép $1 hiển thị thông báo trong MetaMask. Một văn bản thông báo ngắn có thể được kích hoạt bằng Snap đối với thông tin có thể thao tác hoặc nhạy cảm với thời gian.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "<PERSON><PERSON> cấp dữ liệu giao thức cho một hoặc nhiều chuỗi.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Cho phép $1 cung cấp dữ liệu giao thức cho <PERSON>, chẳng hạn như ước tính phí gas hoặc thông tin token.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Cho phép $1 giao tiếp trực tiếp với $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Cho phép $1 gửi tin nhắn đến $2 và nhận phản hồi từ $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 và $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "<PERSON><PERSON><PERSON> thị cửa sổ thông tin chi tiết chữ ký.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Cho phép $1 hiển thị cửa sổ thông tin chi tiết về bất kỳ yêu cầu chữ ký nào trước khi phê duyệt. <PERSON>i<PERSON>u này có thể được sử dụng cho các giải pháp bảo mật và chống lừa đảo.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "<PERSON>em nguồn gốc của các trang web đưa ra yêu cầu chữ ký", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Cho phép $1 xem nguồn gốc (URI) của các trang web đưa ra yêu cầu chữ ký. Đi<PERSON>u này có thể được sử dụng cho các giải pháp bảo mật và chống lừa đảo.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "<PERSON><PERSON><PERSON> n<PERSON>p và hiển thị thông tin chi tiết về giao dịch.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Cho phép $1 giải mã các giao dịch và hiển thị thông tin chi tiết trong giao diện người dùng MetaMask. Đi<PERSON>u này có thể được sử dụng cho các giải pháp bảo mật và chống lừa đảo.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "<PERSON>em nguồn gốc của các trang web đề xuất giao dịch", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Cho phép $1 xem nguồn gốc (URI) của các trang web đề xuất giao dịch. <PERSON><PERSON><PERSON>u này có thể được sử dụng cho các giải pháp bảo mật và chống lừa đảo.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "<PERSON><PERSON><PERSON><PERSON> không xác đ<PERSON>nh: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "<PERSON><PERSON> kh<PERSON>a công khai của bạn cho $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Cho phép $2 xem kh<PERSON>a công khai (và địa chỉ) của bạn đối với $1. Đi<PERSON>u này sẽ không cấp bất kỳ quyền kiểm soát tài khoản hoặc tài sản nào.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "<PERSON>em kh<PERSON>a công khai của bạn cho $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Sử dụng các mạng đã đư<PERSON><PERSON> kích hoạt của bạn", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Hỗ trợ dành cho WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Cho phép $1 truy cập vào các môi trường thực thi cấp thấp thông qua WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "permissionsPageEmptyContent": {"message": "<PERSON><PERSON><PERSON><PERSON> có gì ở đây"}, "permissionsPageEmptySubContent": {"message": "<PERSON><PERSON><PERSON> là nơi bạn có thể xem các quyền mà bạn đã cấp cho các Snap đã cài đặt hoặc các trang web đã kết nối."}, "permitSimulationChange_approve": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu"}, "permitSimulationChange_bidding": {"message": "Bạn đặt giá"}, "permitSimulationChange_listing": {"message": "<PERSON><PERSON><PERSON> y<PERSON>"}, "permitSimulationChange_nft_listing": {"message": "<PERSON><PERSON><PERSON>"}, "permitSimulationChange_receive": {"message": "Bạn nhận đ<PERSON>"}, "permitSimulationChange_revoke2": {"message": "<PERSON><PERSON> h<PERSON>"}, "permitSimulationChange_transfer": {"message": "<PERSON><PERSON><PERSON>"}, "permitSimulationDetailInfo": {"message": "Bạn đang cấp cho người chi tiêu quyền chi tiêu số lượng token này từ tài khoản của bạn."}, "permittedChainToastUpdate": {"message": "$1 có quyền truy cập vào $2."}, "personalAddressDetected": {"message": "<PERSON><PERSON> tìm thấy địa chỉ cá nhân. Nhập địa chỉ hợp đồng token."}, "pinToTop": {"message": "<PERSON><PERSON> lên đ<PERSON>u"}, "pleaseConfirm": {"message": "<PERSON><PERSON> lòng x<PERSON>n"}, "plusMore": {"message": "+ $1 khác", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 khác", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Một vài mạng trong số này phụ thuộc vào bên thứ ba. Kết nối có thể kém tin cậy hơn hoặc cho phép bên thứ ba theo dõi hoạt động.", "description": "Learn more link"}, "popularNetworks": {"message": "M<PERSON>ng phổ biến"}, "portfolio": {"message": "<PERSON><PERSON> mục đ<PERSON>u tư"}, "preparingSwap": {"message": "<PERSON><PERSON> ch<PERSON>n bị ho<PERSON> đổi..."}, "prev": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "price": {"message": "Giá"}, "priceUnavailable": {"message": "gi<PERSON> không khả dụng"}, "primaryType": {"message": "<PERSON><PERSON><PERSON>"}, "priorityFee": {"message": "<PERSON><PERSON> <PERSON><PERSON> tiên"}, "priorityFeeProperCase": {"message": "<PERSON><PERSON> <PERSON><PERSON> tiên"}, "privacy": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư"}, "privacyMsg": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư"}, "privateKey": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Khóa riêng tư cho $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "<PERSON><PERSON><PERSON><PERSON> riê<PERSON> tư bị <PERSON>n", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Hi<PERSON>n thị/Ẩn đầu vào khóa riêng tư", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "<PERSON><PERSON><PERSON><PERSON> riêng tư này đang đư<PERSON><PERSON> hiển thị", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Cảnh báo: <PERSON><PERSON><PERSON><PERSON> đối không để lộ khóa này. Bất kỳ ai có khóa riêng tư cũng có thể đánh cắp tài sản đư<PERSON><PERSON> lưu giữ trong tài khoản của bạn."}, "privateNetwork": {"message": "Mạng riêng"}, "proceedWithTransaction": {"message": "Tôi vẫn muốn tiếp tục"}, "productAnnouncements": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> về sản phẩm"}, "proposedApprovalLimit": {"message": "<PERSON><PERSON><PERSON><PERSON> hạn chấp thuận đề xuất"}, "provide": {"message": "<PERSON><PERSON> cấp"}, "publicAddress": {"message": "Địa chỉ công khai"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Bạn đã nhận được $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Bạn đã nhận đư<PERSON>c một vài token"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "<PERSON><PERSON> nhận đ<PERSON><PERSON><PERSON> tiền"}, "pushPlatformNotificationsFundsSentDescription": {"message": "Bạn đã gửi thành công $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Bạn đã gửi thành công một vài token"}, "pushPlatformNotificationsFundsSentTitle": {"message": "<PERSON><PERSON> gửi tiền"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Bạn đã nhận được NFT mới"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "<PERSON><PERSON> nhận đ<PERSON><PERSON><PERSON> NFT"}, "pushPlatformNotificationsNftSentDescription": {"message": "Bạn đã gửi thành công NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "Đã gửi NFT"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "<PERSON><PERSON> g<PERSON>i <PERSON> của bạn đã thành công"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "<PERSON><PERSON> g<PERSON>i hoàn tất"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "<PERSON><PERSON> g<PERSON>i <PERSON>do của bạn hiện đã sẵn sàng để rút"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "<PERSON><PERSON><PERSON> sản ký gửi đã sẵn sàng để rút"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "<PERSON><PERSON><PERSON> tiền Lido của bạn đã thành công"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "<PERSON><PERSON><PERSON> tiền đã hoàn tất"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "<PERSON><PERSON><PERSON> cầu rút tiền Lido của bạn đã đư<PERSON><PERSON> g<PERSON>i"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "<PERSON><PERSON> yêu c<PERSON>u rút tiền"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "<PERSON><PERSON> g<PERSON>i RocketPool của bạn đã thành công"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "<PERSON><PERSON> g<PERSON>i hoàn tất"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "<PERSON><PERSON><PERSON> ký gửi RocketPool của bạn đã thành công"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "<PERSON><PERSON><PERSON> ký gửi hoàn tất"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "<PERSON><PERSON> đổi MetaMask của bạn đã thành công"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "<PERSON><PERSON> đổi xong"}, "queued": {"message": "<PERSON><PERSON> đưa vào hàng đợi"}, "quoteRate": {"message": "Tỷ giá báo giá"}, "quotedReceiveAmount": {"message": "Số tiền nhận $1"}, "quotedTotalCost": {"message": "Tổng chi phí $1"}, "rank": {"message": "<PERSON><PERSON><PERSON> h<PERSON>"}, "rateIncludesMMFee": {"message": "Tỷ giá bao gồm phí $1%"}, "reAddAccounts": {"message": "thêm lại bất kỳ tài khoản nào khác"}, "reAdded": {"message": "<PERSON><PERSON> thêm lại"}, "readdToken": {"message": "Bạn có thể thêm lại token này trong tương lai bằng cách chuyển đến mục “Thêm token” trong trình đơn tùy chọn tài kho<PERSON>n."}, "receive": {"message": "Nhậ<PERSON>"}, "receiveCrypto": {"message": "<PERSON><PERSON><PERSON><PERSON> tiền mã hóa"}, "recipientAddressPlaceholderNew": {"message": "<PERSON><PERSON><PERSON><PERSON> địa chỉ công khai (0x) hoặc tên miền"}, "recommendedGasLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> xuất"}, "recoveryPhraseReminderBackupStart": {"message": "<PERSON><PERSON><PERSON> đầu tại đây"}, "recoveryPhraseReminderConfirm": {"message": "<PERSON><PERSON> hiểu"}, "recoveryPhraseReminderHasBackedUp": {"message": "<PERSON><PERSON><PERSON> lưu gi<PERSON> Cụm từ khôi phục bí mật ở nơi an toàn và bí mật"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Bạn cần sao lưu lại Cụm từ khôi phục bí mật?"}, "recoveryPhraseReminderItemOne": {"message": "Tuyệt đối không cho ai biết C<PERSON><PERSON> từ khôi phục bí mật"}, "recoveryPhraseReminderItemTwo": {"message": "Nhóm MetaMask sẽ không bao giờ hỏi Cụm từ khôi phục bí mật của bạn"}, "recoveryPhraseReminderSubText": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật sẽ kiểm soát mọi thứ trong tài khoản của bạn."}, "recoveryPhraseReminderTitle": {"message": "<PERSON><PERSON><PERSON> vệ tiền của bạn"}, "redeposit": {"message": "<PERSON><PERSON><PERSON> l<PERSON>"}, "refreshList": {"message": "<PERSON><PERSON><PERSON> mới danh s<PERSON>ch"}, "reject": {"message": "<PERSON><PERSON> chối"}, "rejectAll": {"message": "<PERSON>ừ chối tất cả"}, "rejectRequestsDescription": {"message": "Bạn chuẩn bị từ chối hàng loạt $1 yêu cầu."}, "rejectRequestsN": {"message": "Từ chối $1 yêu cầu"}, "rejectTxsDescription": {"message": "<PERSON>ạn chuẩn bị từ chối hàng loạt $1 giao dịch."}, "rejectTxsN": {"message": "Từ chối $1 giao dịch"}, "rejected": {"message": "Đ<PERSON> từ chối"}, "remove": {"message": "Xóa"}, "removeAccount": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "removeAccountDescription": {"message": "Tài khoản này sẽ được xóa khỏi ví của bạn. Hãy đảm bảo rằng bạn có Cụm từ khôi phục bí mật ban đầu hoặc khóa riêng tư cho tài khoản được nhập trước khi tiếp tục. Bạn có thể nhập hoặc tạo lại tài khoản từ trình đơn tài khoản thả xuống. "}, "removeKeyringSnap": {"message": "Xóa Snap này sẽ xóa các tài khoản này khỏi MetaMask:"}, "removeKeyringSnapToolTip": {"message": "Snap gi<PERSON><PERSON> kiểm soát các tài khoản và khi xóa Snap, các tài khoản cũng sẽ bị xóa khỏi MetaMask, nh<PERSON><PERSON> chúng sẽ vẫn tồn tại trên chuỗi khối."}, "removeNFT": {"message": "Xóa NFT"}, "removeNftErrorMessage": {"message": "<PERSON><PERSON>g tôi không thể xóa NFT này."}, "removeNftMessage": {"message": "NFT đã đư<PERSON>c xóa thành công!"}, "removeSnap": {"message": "<PERSON><PERSON><PERSON>"}, "removeSnapAccountDescription": {"message": "<PERSON><PERSON><PERSON> bạn ti<PERSON> tụ<PERSON>, tà<PERSON> kho<PERSON>n này sẽ không còn khả dụng trong MetaMask nữa."}, "removeSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "removeSnapConfirmation": {"message": "Bạn có chắc chắn muốn xóa $1 không?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "<PERSON><PERSON><PERSON> động này sẽ x<PERSON><PERSON>nap, dữ liệu và thu hồi các quyền mà bạn đã cấp."}, "replace": {"message": "thay thế"}, "reportIssue": {"message": "Báo cáo sự cố"}, "requestFrom": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u từ"}, "requestFromInfo": {"message": "<PERSON><PERSON><PERSON> là trang web yêu cầu chữ ký của bạn."}, "requestFromInfoSnap": {"message": "<PERSON><PERSON><PERSON> là <PERSON>nap yêu cầu chữ ký của bạn."}, "requestFromTransactionDescription": {"message": "<PERSON><PERSON><PERSON> là trang web yêu cầu xác nhận của bạn."}, "requestingFor": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u cho"}, "requestingForAccount": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u cho $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u cho $1", "description": "Name of Network"}, "required": {"message": "<PERSON><PERSON><PERSON> b<PERSON>"}, "reset": {"message": "Đặt lại"}, "resetWallet": {"message": "Đặt lại ví"}, "resetWalletSubHeader": {"message": "MetaMask không lưu giữ bản sao mật khẩu của bạn. Nếu bạn đang gặp sự cố khi mở khóa tài kho<PERSON>n, bạn sẽ cần đặt lại ví của mình. Bạn có thể thực hiện bằng cách cung cấp Cụm từ khôi phục bí mật mà bạn đã sử dụng khi thiết lập ví."}, "resetWalletUsingSRP": {"message": "Hành động này sẽ xóa ví hiện tại và Cụm từ khôi phục bí mật khỏi thiết bị này, cùng với danh sách các tài khoản mà bạn quản lý. Sau khi đặt lại bằng Cụm từ khôi phục bí mật, bạn sẽ thấy danh sách các tài khoản dựa trên Cụm từ khôi phục bí mật mà bạn sử dụng để đặt lại. Danh sách mới này sẽ tự động bổ sung các tài khoản có số dư. Bạn cũng sẽ có thể $1 đã tạo trước đó. Các tài khoản tùy chỉnh mà bạn đã nhập sẽ cần $2, và mọi token tùy chỉnh mà bạn thêm vào tài khoản cũng sẽ cần $3."}, "resetWalletWarning": {"message": "<PERSON><PERSON><PERSON> bảo bạn đang sử dụng đúng Cụm từ khôi phục bí mật trước khi tiếp tục vì bạn sẽ không thể hoàn tác."}, "restartMetamask": {"message": "Khởi động lại MetaM<PERSON>"}, "restore": {"message": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c"}, "restoreUserData": {"message": "<PERSON><PERSON><PERSON><PERSON> phục dữ liệu người dùng"}, "resultPageError": {"message": "Lỗi"}, "resultPageErrorDefaultMessage": {"message": "<PERSON><PERSON> tác thất bại."}, "resultPageSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON> công"}, "resultPageSuccessDefaultMessage": {"message": "<PERSON><PERSON> hoàn thành thao tác thành công."}, "retryTransaction": {"message": "<PERSON><PERSON><PERSON> lại giao dịch"}, "reusedTokenNameWarning": {"message": "Một token trong đây sử dụng lại ký hiệu của một token khác mà bạn thấy, điều này có thể gây nhầm lẫn hoặc mang tính lừa dối."}, "revealSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "revealSeedWords": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "revealSeedWordsDescription1": {"message": "$1 cung cấp $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMask là một $1. C<PERSON> nghĩa bạn là người sở hữu Cụm từ khôi phục bí mật của bạn.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "to<PERSON>n quyền truy cập vào ví và tiền của bạn.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "ví không lưu ký"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "<PERSON><PERSON><PERSON> từ khôi phục b<PERSON>ậ<PERSON> (SRP)"}, "revealSeedWordsText": {"message": "<PERSON><PERSON><PERSON>"}, "revealSeedWordsWarning": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o không có ai đang nhìn vào màn hình của bạn. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "<PERSON>ộ phận Hỗ trợ của MetaMask sẽ không bao giờ yêu cầu điều này.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON><PERSON> lộ nội dung nh<PERSON>y cảm"}, "review": {"message": "<PERSON><PERSON>"}, "reviewAlert": {"message": "<PERSON><PERSON> l<PERSON>i cảnh b<PERSON>o"}, "reviewAlerts": {"message": "<PERSON><PERSON> l<PERSON>i cảnh b<PERSON>o"}, "reviewPendingTransactions": {"message": "<PERSON>em x<PERSON>t các giao dịch đang chờ xử lý"}, "reviewPermissions": {"message": "<PERSON><PERSON> lạ<PERSON> quyền"}, "revokePermission": {"message": "<PERSON><PERSON> hồ<PERSON> quyền"}, "revokePermissionTitle": {"message": "<PERSON><PERSON><PERSON> quyền $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "Bạn đang xóa quyền chi tiêu token của người khác khỏi tài khoản của bạn."}, "reward": {"message": "Phần thưởng"}, "rpcNameOptional": {"message": "<PERSON><PERSON><PERSON> (Không bắt buộc)"}, "rpcUrl": {"message": "URL RPC"}, "safeTransferFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n an toàn từ"}, "save": {"message": "<PERSON><PERSON><PERSON>"}, "scanInstructions": {"message": "Đặt mã QR vào trước máy ảnh"}, "scanQrCode": {"message": "Quét mã QR"}, "scrollDown": {"message": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>ng"}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "searchAccounts": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>m tài k<PERSON>n"}, "searchNfts": {"message": "<PERSON><PERSON><PERSON> k<PERSON>m NFT"}, "searchTokens": {"message": "T<PERSON><PERSON> k<PERSON>m token"}, "searchTokensByNameOrAddress": {"message": "<PERSON><PERSON><PERSON> k<PERSON> token theo tên hoặc địa chỉ"}, "secretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "secretRecoveryPhrasePlusNumber": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "<PERSON><PERSON> an to<PERSON>n"}, "security": {"message": "<PERSON><PERSON><PERSON>"}, "securityAlert": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o bảo mật từ $1 và $2"}, "securityAlerts": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON> mật"}, "securityAlertsDescription": {"message": "<PERSON><PERSON><PERSON> năng này sẽ cảnh báo bạn về hoạt động độc hại hoặc bất thường bằng cách chủ động xem xét các yêu cầu giao dịch và chữ ký. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "<PERSON><PERSON><PERSON> mật và quyền riêng tư"}, "securityDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> khả năng tham gia các mạng không an toàn và bảo vệ tài khoản của bạn"}, "securityMessageLinkForNetworks": {"message": "lừa đảo mạng và rủi ro bảo mật"}, "securityProviderPoweredBy": {"message": "<PERSON><PERSON><PERSON><PERSON> cung cấp bởi $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "<PERSON><PERSON> tất cả quyền", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "<PERSON>em chi tiết"}, "seedPhraseIntroTitle": {"message": "<PERSON><PERSON><PERSON> mật cho ví của bạn"}, "seedPhraseReq": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật gồm 12, 15, 18, 21 hoặc 24 từ"}, "select": {"message": "<PERSON><PERSON><PERSON>"}, "selectAccountToConnect": {"message": "<PERSON><PERSON><PERSON> tài khoản để kết nối"}, "selectAccounts": {"message": "<PERSON><PERSON><PERSON> (các) tài khoản để sử dụng trên trang web này"}, "selectAccountsForSnap": {"message": "<PERSON><PERSON><PERSON> (các) tài khoản để sử dụng với <PERSON>nap này"}, "selectAll": {"message": "<PERSON><PERSON><PERSON> tất cả"}, "selectAnAccount": {"message": "<PERSON><PERSON><PERSON> một tài <PERSON>n"}, "selectAnAccountAlreadyConnected": {"message": "<PERSON><PERSON><PERSON> kho<PERSON>n này đã đ<PERSON><PERSON><PERSON> kết nối với MetaMask"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "<PERSON><PERSON><PERSON> thị Phương tiện NFT"}, "selectHdPath": {"message": "<PERSON><PERSON><PERSON> đường dẫn HD"}, "selectNFTPrivacyPreference": {"message": "<PERSON><PERSON><PERSON> t<PERSON>h năng Tự động phát hiện NFT"}, "selectPathHelp": {"message": "<PERSON><PERSON><PERSON> bạn không thấy các tài khoản nh<PERSON> mong đợi, h<PERSON><PERSON> chuyển sang đường dẫn HD hoặc mạng đã chọn hiện tại."}, "selectRpcUrl": {"message": "Chọn URL RPC"}, "selectSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật"}, "selectType": {"message": "<PERSON><PERSON><PERSON>"}, "selectedAccountMismatch": {"message": "<PERSON><PERSON> chọn tài k<PERSON>n kh<PERSON>c"}, "selectingAllWillAllow": {"message": "<PERSON><PERSON><PERSON><PERSON> chọn tất cả sẽ cho phép trang này xem tất cả các tài khoản hiện tại của bạn. <PERSON><PERSON><PERSON> bảo rằng bạn tin tưởng trang web này."}, "send": {"message": "<PERSON><PERSON><PERSON>"}, "sendBugReport": {"message": "<PERSON><PERSON><PERSON> báo cáo lỗi."}, "sendNoContactsConversionText": {"message": "nhấn vào đây"}, "sendNoContactsDescription": {"message": "Địa chỉ liên hệ cho phép bạn gửi giao dịch an toàn đến tài khoản khác nhiều lần. <PERSON><PERSON> tạo địa chỉ liên hệ, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "<PERSON>ạn chưa có địa chỉ liên hệ nào"}, "sendSelectReceiveAsset": {"message": "<PERSON><PERSON><PERSON> tài sản để nhận"}, "sendSelectSendAsset": {"message": "<PERSON><PERSON><PERSON> tài sản để gửi"}, "sendSpecifiedTokens": {"message": "Gửi $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Bằng cách nhấp và<PERSON> n<PERSON>, giao dịch hoán đổi của bạn sẽ ngay lập tức bắt đầu. <PERSON><PERSON> lòng xem lại chi tiết giao dịch của bạn trước khi tiếp tục."}, "sendTokenAsToken": {"message": "Gửi $1 dưới dạng $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "<PERSON><PERSON> g<PERSON>i $1"}, "sendingDisabled": {"message": "<PERSON><PERSON><PERSON> tài sản NFT ERC-1155 chưa được hỗ trợ."}, "sendingNativeAsset": {"message": "Gửi $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "C<PERSON>nh báo: bạn sắp gửi đến một hợp đồng token và điều này có thể dẫn đến nguy cơ mất tiền. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "<PERSON><PERSON><PERSON> thử nghiệm <PERSON>"}, "setApprovalForAll": {"message": "<PERSON><PERSON><PERSON><PERSON> lập chấp thuận tất cả"}, "setApprovalForAllRedesignedTitle": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u rút tiền"}, "setApprovalForAllTitle": {"message": "Chấ<PERSON> thuận $1 không có hạn mức chi tiêu", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> tà<PERSON>"}, "settings": {"message": "Cài đặt"}, "settingsSearchMatchingNotFound": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả trùng khớp."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u chữ ký và giao dịch"}, "show": {"message": "<PERSON><PERSON><PERSON> thị"}, "showAccount": {"message": "<PERSON><PERSON><PERSON> thị tài k<PERSON>n"}, "showAdvancedDetails": {"message": "<PERSON><PERSON><PERSON> thị chi tiết nâng cao"}, "showExtensionInFullSizeView": {"message": "Hiển thị tiện ích mở rộng ở chế độ xem kích thước đầy đủ"}, "showExtensionInFullSizeViewDescription": {"message": "Bật tùy chọn này để đặt chế độ xem kích thước đầy đủ làm mặc định khi bạn nhấn vào biểu tượng tiện ích mở rộng."}, "showFiatConversionInTestnets": {"message": "Hiển thị tỷ lệ quy đổi trên các mạng thử nghiệm"}, "showFiatConversionInTestnetsDescription": {"message": "<PERSON>ọn tùy chọn này để hiển thị tỷ lệ quy đổi tiền pháp định trên mạng thử nghiệm"}, "showHexData": {"message": "<PERSON><PERSON><PERSON> thị dữ liệu thập lục phân"}, "showHexDataDescription": {"message": "<PERSON>ọn tùy chọn này để hiển thị trường dữ liệu thập lục phân trên màn hình gửi"}, "showLess": {"message": "<PERSON><PERSON>"}, "showMore": {"message": "<PERSON><PERSON><PERSON> thị thêm"}, "showNativeTokenAsMainBalance": {"message": "Hiển thị token gốc làm số dư chính"}, "showNft": {"message": "<PERSON><PERSON><PERSON> thị <PERSON>"}, "showPermissions": {"message": "<PERSON><PERSON><PERSON> thị quyền"}, "showPrivateKey": {"message": "<PERSON><PERSON><PERSON> thị khóa riêng tư"}, "showSRP": {"message": "<PERSON><PERSON><PERSON> thị <PERSON>m mật khẩu khôi phục bí mật"}, "showTestnetNetworks": {"message": "<PERSON><PERSON><PERSON> thị các mạng thử nghiệm"}, "showTestnetNetworksDescription": {"message": "<PERSON>ọn tùy chọn này để hiển thị các mạng thử nghiệm trong danh sách mạng"}, "sign": {"message": "<PERSON><PERSON>"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u chữ ký"}, "signature_decoding_bid_nft_tooltip": {"message": "NFT sẽ hiển thị trong ví của bạn khi giá thầu đượ<PERSON> chấp nhận."}, "signature_decoding_list_nft_tooltip": {"message": "Chỉ thay đổi khi có người mua NFT của bạn."}, "signed": {"message": "Đã ký"}, "signing": {"message": "<PERSON><PERSON> k<PERSON>"}, "signingInWith": {"message": "<PERSON><PERSON><PERSON> nhập bằng"}, "signingWith": {"message": "<PERSON><PERSON> bằng"}, "simulationApproveHeading": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "Bạn đang cấp cho người khác quyền rút NFT khỏi tài khoản của bạn."}, "simulationDetailsERC20ApproveDesc": {"message": "Bạn đang cấp cho ngư<PERSON>i khác quyền chi tiêu số tiền này từ tài khoản của bạn."}, "simulationDetailsFiatNotAvailable": {"message": "<PERSON><PERSON><PERSON>ng có sẵn"}, "simulationDetailsIncomingHeading": {"message": "Bạn nhận đ<PERSON>"}, "simulationDetailsNoChanges": {"message": "<PERSON><PERSON><PERSON><PERSON> thay đổi"}, "simulationDetailsOutgoingHeading": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Bạn đang xóa quyền rút NFT của người khác khỏi tài khoản của bạn."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Bạn đang cấp quyền cho người khác rút NFT khỏi tài khoản của bạn."}, "simulationDetailsTitle": {"message": "<PERSON>hay đ<PERSON>i <PERSON><PERSON>h"}, "simulationDetailsTitleTooltip": {"message": "Thay đổi ước tính là những gì có thể xảy ra nếu bạn thực hiện giao dịch này. Đây chỉ là dự đoán, không phải là đảm bảo."}, "simulationDetailsTotalFiat": {"message": "Tổng = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> này có khả năng thất bại"}, "simulationDetailsUnavailable": {"message": "<PERSON><PERSON><PERSON><PERSON> khả dụng"}, "simulationErrorMessageV2": {"message": "<PERSON><PERSON>g tôi không thể ước tính gas. <PERSON><PERSON> thể đã xảy ra lỗi trong hợp đồng và giao dịch này có thể thất bại."}, "simulationsSettingDescription": {"message": "<PERSON><PERSON>t tính năng này để ước tính thay đổi số dư của các giao dịch và chữ ký trước khi bạn xác nhận. Đ<PERSON>ều này không đảm bảo cho kết quả cuối cùng. $1"}, "simulationsSettingSubHeader": {"message": "Ước t<PERSON>h thay đổi số dư"}, "singleNetwork": {"message": "1 mạng"}, "siweIssued": {"message": "<PERSON><PERSON> ph<PERSON>t hành"}, "siweNetwork": {"message": "Mạng"}, "siweRequestId": {"message": "<PERSON> yêu c<PERSON>u"}, "siweResources": {"message": "<PERSON><PERSON><PERSON>"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "Bỏ qua bảo mật tài k<PERSON>?"}, "skipAccountSecurityDetails": {"message": "Tôi hiểu rằng nếu chưa sao lưu Cụm từ khôi phục bí mật của mình, tôi có thể bị mất tài khoản và toàn bộ tài sản bên trong."}, "slideBridgeDescription": {"message": "<PERSON>uyển qua 9 chuỗi, tất cả trong ví của bạn"}, "slideBridgeTitle": {"message": "Sẵn sàng để thực hiện cầu nối?"}, "slideCashOutDescription": {"message": "<PERSON><PERSON> tiền mã hóa để lấy tiền mặt"}, "slideCashOutTitle": {"message": "<PERSON><PERSON><PERSON> ti<PERSON> v<PERSON>i MetaM<PERSON>"}, "slideDebitCardDescription": {"message": "Có sẵn ở một số khu vực được chọn"}, "slideDebitCardTitle": {"message": "Thẻ ghi nợ MetaMask"}, "slideFundWalletDescription": {"message": "Thêm hoặc chuyển token để bắt đầu"}, "slideFundWalletTitle": {"message": "<PERSON><PERSON><PERSON> tiền vào ví của bạn"}, "slideMultiSrpDescription": {"message": "Nhập và sử dụng nhiều ví trong MetaMask"}, "slideMultiSrpTitle": {"message": "<PERSON>h<PERSON><PERSON> nhiều <PERSON> từ khôi phục bí mật"}, "slideRemoteModeDescription": {"message": "Sử dụng ví lạnh không dây"}, "slideRemoteModeTitle": {"message": "<PERSON><PERSON>, t<PERSON><PERSON> c<PERSON><PERSON> n<PERSON>h"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON><PERSON> chỉ gi<PERSON> ng<PERSON>, t<PERSON><PERSON> năng thông minh hơn"}, "slideSmartAccountUpgradeTitle": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u sử dụng tài khoản thông minh"}, "slideSolanaDescription": {"message": "<PERSON><PERSON><PERSON> tài kho<PERSON>n <PERSON>ana để bắt đầu"}, "slideSolanaTitle": {"message": "Solana hiện đã được hỗ trợ"}, "slideSweepStakeDescription": {"message": "Đúc NFT ngay để có cơ hội trúng thưởng"}, "slideSweepStakeTitle": {"message": "Tham gia chương trình Tặng thưởng $5000 USDC!"}, "smartAccountAccept": {"message": "Sử dụng tài k<PERSON>n thông minh"}, "smartAccountBetterTransaction": {"message": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>n, ph<PERSON> thấp hơn"}, "smartAccountBetterTransactionDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> kiệm thời gian và tiền bạc bằng cách xử lý nhiều giao dịch cùng lúc."}, "smartAccountFeaturesDescription": {"message": "<PERSON><PERSON><PERSON> nguyên địa chỉ tài khoản và bạn có thể chuyển đổi lại bất kỳ lúc nào."}, "smartAccountLabel": {"message": "<PERSON><PERSON><PERSON>n thông minh"}, "smartAccountPayToken": {"message": "<PERSON><PERSON> to<PERSON> bằng bất kỳ token nào, bất kỳ lúc nào"}, "smartAccountPayTokenDescription": {"message": "Sử dụng token bạn đang có để thanh toán phí mạng."}, "smartAccountReject": {"message": "Không sử dụng tài kho<PERSON>n thông minh"}, "smartAccountRequestFor": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u cho"}, "smartAccountSameAccount": {"message": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> ng<PERSON>ê<PERSON>, t<PERSON><PERSON> năng thông minh hơn."}, "smartAccountSplashTitle": {"message": "Sử dụng tài khoản thông minh?"}, "smartAccountUpgradeBannerDescription": {"message": "<PERSON><PERSON><PERSON> địa chỉ. <PERSON><PERSON><PERSON> năng thông minh hơn."}, "smartAccountUpgradeBannerTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> sang tài khoản thông minh"}, "smartContracts": {"message": "<PERSON><PERSON><PERSON> đồng thông minh"}, "smartSwapsErrorNotEnoughFunds": {"message": "<PERSON><PERSON><PERSON><PERSON> có đủ tiền để thực hiện hoán đổi thông minh."}, "smartSwapsErrorUnavailable": {"message": "<PERSON><PERSON> đổi thông minh tạm thời không khả dụng."}, "smartTransactionCancelled": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn đã bị hủy"}, "smartTransactionCancelledDescription": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn không thể hoàn tất nên nó đã bị hủy để giúp bạn không phải trả phí gas không cần thiết."}, "smartTransactionError": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn đã thất bại"}, "smartTransactionErrorDescription": {"message": "Biến động đột ngột của thị trường có thể gây ra lỗi. <PERSON><PERSON>u sự cố tiế<PERSON>, h<PERSON><PERSON> liên hệ với bộ phận hỗ trợ khách hàng của MetaMask."}, "smartTransactionPending": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn đã đ<PERSON><PERSON><PERSON> g<PERSON>i"}, "smartTransactionSuccess": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> của bạn đã hoàn tất"}, "smartTransactions": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ch thông minh"}, "smartTransactionsEnabledDescription": {"message": " và bảo vệ MEV. B<PERSON>y giờ đượ<PERSON> bật theo mặc định."}, "smartTransactionsEnabledLink": {"message": "Tỷ lệ thành công cao hơn"}, "smartTransactionsEnabledTitle": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ch giờ đây đã thông minh hơn"}, "snapAccountCreated": {"message": "<PERSON><PERSON><PERSON> k<PERSON>ản đã đ<PERSON><PERSON><PERSON> tạo"}, "snapAccountCreatedDescription": {"message": "Tài khoản mới của bạn đã sẵn sàng để sử dụng!"}, "snapAccountCreationFailed": {"message": "<PERSON><PERSON><PERSON> tài kho<PERSON>n không thành công"}, "snapAccountCreationFailedDescription": {"message": "$1 không thể tạo tài khoản cho bạn.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "<PERSON><PERSON><PERSON> th<PERSON>nh ký"}, "snapAccountRedirectSiteDescription": {"message": "<PERSON><PERSON><PERSON> theo hướng dẫn từ $1"}, "snapAccountRemovalFailed": {"message": "<PERSON><PERSON><PERSON> tài kho<PERSON>n không thành công"}, "snapAccountRemovalFailedDescription": {"message": "$1 không thể xóa tài khoản này cho bạn.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Đã xóa tài k<PERSON>n"}, "snapAccountRemovedDescription": {"message": "<PERSON><PERSON><PERSON> kho<PERSON>n này sẽ không còn khả dụng để sử dụng trong MetaMask nữa."}, "snapAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "snapAccountsDescription": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>n đư<PERSON><PERSON> kiểm soát bởi Snap bên thứ ba."}, "snapConnectTo": {"message": "Kết nối với $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Cho phép $1 tự động kết nối với $2 mà không cần bạn chấp thuận.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 muốn sử dụng $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Trang web"}, "snapHomeMenu": {"message": "<PERSON><PERSON><PERSON><PERSON> đơn <PERSON>rang chủ Snap"}, "snapInstallRequest": {"message": "Cài đặt $1 sẽ cấp cho nó các quyền sau.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Cài đặt hoàn tất"}, "snapInstallWarningCheck": {"message": "$1 muốn đư<PERSON><PERSON> cấp quyền để thực hiện những điều sau:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "<PERSON><PERSON><PERSON> tiến hành thận trọng"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Cho phép $1 xem kh<PERSON>a công khai (và địa chỉ) của bạn. Điều này sẽ không cấp bất kỳ quyền kiểm soát tài khoản hoặc tài sản nào.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Cho phép Snap $1 quản lý tài khoản và tài sản trên các mạng được yêu cầu. <PERSON>ác tài khoản này được lấy và sao lưu bằng cụm từ khôi phục bí mật của bạn (mà không để lộ). Với khả năng lấy khóa, $1 có thể hỗ trợ nhiều giao thức chuỗi khối ngoài Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Q<PERSON>ản lý tài k<PERSON>n $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "<PERSON>em kh<PERSON>a công khai của bạn cho $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "Không thể cài đặt $1.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Cài đặt thất bại", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "Lỗi"}, "snapResultSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON> công"}, "snapResultSuccessDescription": {"message": "$1 đã sẵn sàng để sử dụng"}, "snapUIAssetSelectorTitle": {"message": "<PERSON><PERSON><PERSON> tài sản"}, "snapUpdateAlertDescription": {"message": "<PERSON><PERSON><PERSON> phiên bản mới nhất của $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "<PERSON><PERSON> c<PERSON>p nhật mới"}, "snapUpdateErrorDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật $1.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "<PERSON><PERSON><PERSON> nhật thất bại", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Cậ<PERSON> nhật $1 sẽ cấp cho nó các quyền sau.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t hoàn tất"}, "snapUrlIsBlocked": {"message": "Snap này muốn đưa bạn đến một trang web bị chặn. $1."}, "snaps": {"message": "Snap"}, "snapsConnected": {"message": "<PERSON><PERSON> kết n<PERSON><PERSON>"}, "snapsNoInsight": {"message": "<PERSON><PERSON><PERSON><PERSON> có thông tin chi tiết để hiển thị"}, "snapsPrivacyWarningFirstMessage": {"message": "Bạn thừa nhận rằng mọi Snap mà bạn cài đặt đều là Dịch vụ bên thứ ba, trừ khi được xác định kh<PERSON>, như được định nghĩa trong $1 của Consensys. Việc bạn sử dụng Dịch vụ bên thứ ba sẽ phải tuân theo các điều khoản và điều kiện riêng do nhà cung cấp Dịch vụ bên thứ ba quy định. Consensys không khuyến nghị bất kỳ cá nhân cụ thể nào sử dụng bất kỳ Snap nào vì bất kỳ lý do cụ thể nào. Bạn tự chịu rủi ro khi truy cập, tin tưởng hoặc sử dụng Dịch vụ bên thứ ba. Consensys từ chối mọi trách nhiệm và nghĩa vụ pháp lý đối với mọi tổn thất khi bạn sử dụng Dịch vụ bên thứ ba.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "<PERSON><PERSON><PERSON> thông tin mà bạn chia sẻ với Dịch vụ bên thứ ba sẽ được Dịch vụ bên thứ ba đó thu thập trực tiếp theo ch<PERSON>h sách quyền riêng tư của họ. Vui lòng tham khảo chính sách quyền riêng tư của họ để biết thêm thông tin.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys không có quyền truy cập vào các thông tin mà bạn chia sẻ với Dịch vụ bên thứ ba.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Cài đặt Snap"}, "snapsTermsOfUse": {"message": "<PERSON><PERSON><PERSON><PERSON>n sử dụng"}, "snapsToggle": {"message": "Snap chỉ hoạt động khi đã bật"}, "snapsUIError": {"message": "<PERSON><PERSON><PERSON> hệ với những người tạo ra $1 để được hỗ trợ thêm.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Trang web này yêu cầu tài k<PERSON>."}, "solanaAccountRequired": {"message": "<PERSON><PERSON>n có tài khoản Solana để kết nối với trang web này."}, "solanaImportAccounts": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "solanaImportAccountsDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> từ khôi phục bí mật để di chuyển tài khoản Solana từ một ví khác."}, "solanaMoreFeaturesComingSoon": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h năng khác sẽ sớm ra mắt"}, "solanaMoreFeaturesComingSoonDescription": {"message": "N<PERSON>, hỗ trợ ví cứng, v.v. sẽ sớm ra mắt."}, "solanaOnMetaMask": {"message": "<PERSON><PERSON> trên <PERSON>"}, "solanaSendReceiveSwapTokens": {"message": "<PERSON><PERSON><PERSON>, nhận và hoán đổi token"}, "solanaSendReceiveSwapTokensDescription": {"message": "Chuyển tiền và giao dịch bằng các token như SOL, USDC, v.v."}, "someNetworks": {"message": "$1 mạng"}, "somethingDoesntLookRight": {"message": "Có gì đó không <PERSON>n? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "<PERSON><PERSON> xảy ra lỗi. <PERSON><PERSON><PERSON> thử tải lại trang."}, "somethingWentWrong": {"message": "<PERSON><PERSON><PERSON> tôi không thể tải trang này."}, "sortBy": {"message": "<PERSON><PERSON><PERSON> xếp theo"}, "sortByAlphabetically": {"message": "<PERSON><PERSON>ng chữ cái (A - Z)"}, "sortByDecliningBalance": {"message": "Số dư g<PERSON><PERSON> dầ<PERSON> ($1 cao - thấp)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "spamModalBlockedDescription": {"message": "Trang web này sẽ bị chặn trong 1 phút."}, "spamModalBlockedTitle": {"message": "Bạn đã tạm thời chặn trang web này"}, "spamModalDescription": {"message": "<PERSON><PERSON>u bạn đang bị spam bởi nhiều yêu cầu, bạn có thể tạm thời chặn trang web."}, "spamModalTemporaryBlockButton": {"message": "<PERSON>ạm thời chặn trang web này"}, "spamModalTitle": {"message": "<PERSON><PERSON>g tôi đã phát hiện có nhiều yêu cầu"}, "speed": {"message": "<PERSON><PERSON><PERSON>"}, "speedUp": {"message": "<PERSON><PERSON><PERSON> tốc"}, "speedUpCancellation": {"message": "<PERSON><PERSON><PERSON> tốc l<PERSON>nh hủ<PERSON> này"}, "speedUpExplanation": {"message": "<PERSON>úng tôi đã cập nhật phí gas dựa trên tình trạng mạng hiện tại và đã tăng ít nhất 10% (theo yêu cầu của mạng)."}, "speedUpPopoverTitle": {"message": "<PERSON><PERSON><PERSON> tốc giao d<PERSON>ch"}, "speedUpTooltipText": {"message": "Phí gas mới"}, "speedUpTransaction": {"message": "<PERSON><PERSON><PERSON> tốc giao d<PERSON>ch n<PERSON>y"}, "spendLimitInsufficient": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu không đủ"}, "spendLimitInvalid": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu không hợp lệ; gi<PERSON> trị này phải là số dương."}, "spendLimitPermission": {"message": "<PERSON><PERSON><PERSON>n đối với hạn mức chi tiêu"}, "spendLimitRequestedBy": {"message": "<PERSON><PERSON><PERSON> yêu cầu hạn mức chi tiêu: $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu quá lớn"}, "spender": {"message": "<PERSON><PERSON><PERSON><PERSON> chi tiêu"}, "spenderTooltipDesc": {"message": "<PERSON><PERSON><PERSON> là địa chỉ có thể rút NFT của bạn."}, "spenderTooltipERC20ApproveDesc": {"message": "<PERSON><PERSON><PERSON> là địa chỉ sẽ có thể chi tiêu token thay mặt cho bạn."}, "spendingCap": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu"}, "spendingCaps": {"message": "<PERSON><PERSON><PERSON> mức chi tiêu"}, "srpInputNumberOfWords": {"message": "<PERSON><PERSON><PERSON> có một cụm từ gồm $1 từ", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 tài k<PERSON>n", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "Tài khoản mới của bạn sẽ được tạo từ Cụm từ khôi phục bí mật này"}, "srpListSingleOrZero": {"message": "$1 tài k<PERSON>n", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "<PERSON><PERSON> không thành công vì cụm từ có nhiều hơn 24 từ. Cụm từ khôi phục bí mật chỉ có tối đa 24 từ.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Bạn có thể dán toàn bộ cụm từ khôi phục bí mật vào bất kỳ trường nào", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u"}, "srpSecurityQuizImgAlt": {"message": "Một con mắt có lỗ khóa ở giữa và ba trường mật khẩu nổi"}, "srpSecurityQuizIntroduction": {"message": "<PERSON><PERSON> hiển thị Cụm từ khôi phục bí mật, bạn cần trả lời đúng hai câu hỏi"}, "srpSecurityQuizQuestionOneQuestion": {"message": "<PERSON><PERSON><PERSON> bạn làm mất Cụm từ khôi phục b<PERSON> mật, MetaMask..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "<PERSON><PERSON><PERSON><PERSON> thể gi<PERSON>p bạn"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "<PERSON><PERSON><PERSON> viết ra, khắc lên kim loại hoặc cất giữ ở nhiều nơi bí mật để bạn không bao giờ làm mất nó. Nếu bạn làm mất, nó sẽ bị mất vĩnh viễn."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Đúng! Không ai có thể giúp bạn lấy lại Cụm từ khôi phục bí mật"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "<PERSON><PERSON> thể lấy lại cho bạn"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "<PERSON><PERSON><PERSON> bạn làm mất Cụm từ khôi phục bí mật, nó sẽ bị mất vĩnh viễn. Dù mọi người có nói gì, thì cũng không ai có thể giúp bạn lấy lại."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Sai! Không ai có thể giúp bạn lấy lại Cụm từ khôi phục bí mật"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON><PERSON><PERSON> có bất kỳ ai, kể cả nhân viên hỗ trợ, hỏi về Cụm từ khôi phục bí mật của bạn..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "Bạn đang bị lừa đảo"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Bất kỳ ai nói rằng họ cần Cụm từ khôi phục bí mật của bạn thì đều đang nói dối bạn. Nếu bạn chia sẻ với họ thì họ sẽ đánh cắp tài sản của bạn."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Chính xác! Chia sẻ Cụm từ khôi phục bí mật chưa bao giờ là một ý hay"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "<PERSON><PERSON>n nên đ<PERSON>a nó cho họ"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Bất kỳ ai nói rằng họ cần Cụm từ khôi phục bí mật của bạn thì đều đang nói dối bạn. Nếu bạn chia sẻ với họ thì họ sẽ đánh cắp tài sản của bạn."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Không! Tuyệt đối không bao giờ chia sẻ Cụm từ khôi phục bí mật của bạn với bất kỳ ai"}, "srpSecurityQuizTitle": {"message": "<PERSON><PERSON><PERSON> hỏi bảo mật"}, "srpToggleShow": {"message": "Hiện/Ẩn từ này của cụm từ khôi phục bí mật", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "<PERSON><PERSON> này b<PERSON>n", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Từ này đang đ<PERSON><PERSON><PERSON> hiển thị", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Ổn định"}, "stableLowercase": {"message": "<PERSON><PERSON>"}, "stake": {"message": "Stake"}, "staked": {"message": "Đã ký gửi"}, "standardAccountLabel": {"message": "<PERSON><PERSON><PERSON>n ti<PERSON><PERSON> ch<PERSON>n"}, "startEarning": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u kiếm tiền"}, "stateLogError": {"message": "Lỗi khi truy xuất nhật ký trạng thái."}, "stateLogFileName": {"message": "<PERSON><PERSON><PERSON><PERSON> ký trạng thái của MetaMask"}, "stateLogs": {"message": "<PERSON><PERSON><PERSON><PERSON> ký trạng thái"}, "stateLogsDescription": {"message": "<PERSON><PERSON><PERSON>t ký trạng thái có chứa các địa chỉ tài khoản công khai của bạn và các giao dịch đã gửi."}, "status": {"message": "<PERSON><PERSON><PERSON><PERSON> thái"}, "statusNotConnected": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "statusNotConnectedAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> có tài khoản nào đ<PERSON><PERSON><PERSON> kết nối"}, "step1LatticeWallet": {"message": "<PERSON><PERSON>t n<PERSON>i v<PERSON><PERSON>1"}, "step1LatticeWalletMsg": {"message": "Bạn có thể kết nối MetaMask với Lattice1 sau khi thiết bị đã được thiết lập và trực tuyến. Mở khóa thiết bị và chuẩn bị sẵn ID Thiết Bị.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "<PERSON><PERSON><PERSON> về <PERSON>ng dụng Ledger"}, "step1LedgerWalletMsg": {"message": "<PERSON><PERSON><PERSON> về, thiết lập và nhập mật khẩu của bạn để mở khóa $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "<PERSON><PERSON><PERSON> n<PERSON> v<PERSON><PERSON>"}, "step1TrezorWalletMsg": {"message": "Cắm trực tiếp T<PERSON><PERSON> vào máy tính của bạn và mở khóa. Đ<PERSON><PERSON> bảo bạn sử dụng đúng cụm từ mật khẩu.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "<PERSON><PERSON><PERSON> n<PERSON> vớ<PERSON>"}, "step2LedgerWalletMsg": {"message": "<PERSON><PERSON><PERSON> trự<PERSON> tiếp <PERSON> vào máy tính của bạn, sau đó mở khóa và mở ứng dụng Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Vẫn nhận đư<PERSON>c thông báo này?"}, "strong": {"message": "Mạnh"}, "stxCancelled": {"message": "<PERSON><PERSON> đổi sẽ thất bại"}, "stxCancelledDescription": {"message": "<PERSON><PERSON><PERSON> dịch của bạn sẽ thất bại và đã bị hủy để bảo vệ bạn không phải trả phí gas không cần thiết."}, "stxCancelledSubDescription": {"message": "<PERSON><PERSON><PERSON> thử hoán đổi lại. <PERSON><PERSON>g tôi ở đây để bảo vệ bạn trước những rủi ro tương tự trong lần tới."}, "stxFailure": {"message": "<PERSON><PERSON> đổi không thành công"}, "stxFailureDescription": {"message": "Thị trường thay đổi đột ngột có thể gây thất bại. <PERSON><PERSON>u sự cố vẫn tiế<PERSON>, vui lòng liên hệ $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "Bật Gia<PERSON> dịch thông minh để có các giao dịch đáng tin cậy và an toàn hơn trên các mạng được hỗ trợ. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "<PERSON><PERSON> bí mật gửi yêu cầu <PERSON><PERSON> đổi của bạn..."}, "stxPendingPubliclySubmittingSwap": {"message": "<PERSON><PERSON> công khai gửi yêu cầu <PERSON><PERSON> đổi của bạn..."}, "stxSuccess": {"message": "<PERSON><PERSON> đổi hoàn tất!"}, "stxSuccessDescription": {"message": "$1 của bạn hiện đã có sẵn.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "<PERSON><PERSON> đổi sẽ hoàn tất sau <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "<PERSON><PERSON> cố gắng hủy giao dịch của bạn..."}, "stxUnknown": {"message": "<PERSON>r<PERSON><PERSON> thái không xác đ<PERSON>nh"}, "stxUnknownDescription": {"message": "Một giao dịch đã thành công nhưng chúng tôi không chắc đó là giao dịch nào. Điều này có thể do bạn đã gửi một giao dịch khác trong lúc hoán đổi này đang được xử lý."}, "stxUserCancelled": {"message": "<PERSON><PERSON> hủy ho<PERSON> đổi"}, "stxUserCancelledDescription": {"message": "<PERSON><PERSON><PERSON> dịch của bạn đã bị hủy và bạn không phải trả bất kỳ phí gas không cần thiết nào."}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "submitted": {"message": "Đ<PERSON> gửi"}, "suggestedBySnap": {"message": "<PERSON><PERSON><PERSON><PERSON> đề xuất bởi $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "<PERSON><PERSON> hiệu tiền tệ đề xuất:"}, "suggestedTokenName": {"message": "<PERSON><PERSON><PERSON> đề xuất:"}, "supplied": {"message": "<PERSON><PERSON> cung cấp"}, "support": {"message": "Hỗ trợ"}, "supportCenter": {"message": "<PERSON><PERSON><PERSON> cập trung tâm hỗ trợ của chúng tôi"}, "supportMultiRpcInformation": {"message": "<PERSON><PERSON>g tôi hiện hỗ trợ nhiều RPC cho một mạng duy nhất. RPC gần đây nhất của bạn đã được chọn làm RPC mặc định để giải quyết thông tin xung đột."}, "surveyConversion": {"message": "Tham gia kh<PERSON>o sát của chúng tôi"}, "surveyTitle": {"message": "<PERSON><PERSON><PERSON> hình tương lai của MetaMask"}, "swap": {"message": "<PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "<PERSON><PERSON><PERSON>u chỉnh mức trư<PERSON>t giá"}, "swapAggregator": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> h<PERSON>p"}, "swapAllowSwappingOf": {"message": "Cho phép hoán đổi $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON> tiền đ<PERSON><PERSON><PERSON> đả<PERSON> b<PERSON>o"}, "swapAmountReceivedInfo": {"message": "<PERSON><PERSON>y là số tiền tối thiểu mà bạn sẽ nhận được. Bạn sẽ nhận được nhiều hơn tùy thuộc vào mức trượt giá."}, "swapAndSend": {"message": "<PERSON><PERSON> đổi và gửi"}, "swapAnyway": {"message": "Vẫn ho<PERSON> đổi"}, "swapApproval": {"message": "<PERSON><PERSON><PERSON> thuận $1 cho các giao dịch ho<PERSON> đổi", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Bạn cần $1 $2 nữa để hoàn tất giao dịch hoán đổi này", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Bạn vẫn còn ở đó chứ?"}, "swapAreYouStillThereDescription": {"message": "<PERSON><PERSON>g tôi sẵn sàng cho bạn xem báo giá mới nhất khi bạn muốn tiếp tục"}, "swapConfirmWithHwWallet": {"message": "<PERSON><PERSON><PERSON>n ví cứng của bạn"}, "swapContinueSwapping": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> ho<PERSON> đổi"}, "swapContractDataDisabledErrorDescription": {"message": "Trong ứng dụng Ethereum trên <PERSON>ger của bạn, h<PERSON><PERSON> chuyển đến phần \"Cài đặt\" và cho phép dữ liệu hợp đồng. <PERSON><PERSON>, thử hoán đổi lại."}, "swapContractDataDisabledErrorTitle": {"message": "<PERSON><PERSON><PERSON> bật dữ liệu hợp đồng trên <PERSON> của bạn"}, "swapCustom": {"message": "tùy chỉnh"}, "swapDecentralizedExchange": {"message": "<PERSON><PERSON>n giao dịch phi tập trung"}, "swapDirectContract": {"message": "<PERSON><PERSON><PERSON> đồng tr<PERSON><PERSON> tiếp"}, "swapEditLimit": {"message": "Chỉnh sửa giới hạn"}, "swapEnableDescription": {"message": "<PERSON><PERSON> tác này là bắt buộc và cấp cho MetaMask quyền hoán đổi $1 của bạn.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "<PERSON>i<PERSON>u này sẽ $1 để hoán đổi", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "<PERSON><PERSON><PERSON><PERSON> số tiền"}, "swapEstimatedNetworkFees": {"message": "<PERSON><PERSON> mạng <PERSON> t<PERSON>h"}, "swapEstimatedNetworkFeesInfo": {"message": "<PERSON><PERSON><PERSON> là giá trị ước tính của phí mạng sẽ dùng để hoàn thành giao dịch hoán đổi của bạn. Số tiền thực tế có thể thay đổi tùy theo tình trạng mạng."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Đã xảy ra lỗi giao dịch và chúng tôi sẵn sàng trợ giúp bạn. Nếu vấn đề này tiế<PERSON>, bạn có thể liên hệ với bộ phận hỗ trợ khách hàng tại $1 để được hỗ trợ thêm.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "<PERSON><PERSON> đổi không thành công"}, "swapFetchingQuote": {"message": "<PERSON><PERSON><PERSON> n<PERSON> báo giá"}, "swapFetchingQuoteNofN": {"message": "<PERSON><PERSON><PERSON> n<PERSON> báo giá $1/$2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "<PERSON><PERSON><PERSON> n<PERSON> báo giá..."}, "swapFetchingQuotesErrorDescription": {"message": "R<PERSON>t tiếc... đã x<PERSON>y ra sự cố. <PERSON><PERSON><PERSON> thử lại. Nếu lỗi vẫn tiế<PERSON> di<PERSON>, hãy liên hệ với bộ phận hỗ trợ khách hàng."}, "swapFetchingQuotesErrorTitle": {"message": "Lỗi tìm nạp báo giá"}, "swapFromTo": {"message": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> ho<PERSON> đổ<PERSON> $1 sang $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "<PERSON>í gas được ước tính và sẽ dao động dựa trên lưu lượng mạng và độ phức tạp của giao dịch."}, "swapGasFeesExplanation": {"message": "MetaMask không kiếm tiền từ phí gas. <PERSON><PERSON><PERSON> khoản phí này là ước tính và có thể thay đổi dựa trên lưu lượng của mạng và độ phức tạp của giao dịch. Tìm hiểu thêm $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "tại đây", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "<PERSON><PERSON>m hiểu thêm về phí gas"}, "swapGasFeesSplit": {"message": "<PERSON>í gas trên màn hình trước đư<PERSON><PERSON> chia đôi giữa hai giao dịch nà<PERSON>."}, "swapGasFeesSummary": {"message": "Phí gas đượ<PERSON> trả cho thợ đào tiền điện tử, họ là những người xử lý các giao dịch trên mạng $1. MetaMask không thu lợi nhuận từ phí gas.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Báo giá này bao gồm phí gas bằng cách điều chỉnh số lượng token được gửi hoặc nhận. Bạn có thể nhận được ETH trong một giao dịch riêng biệt trên danh sách hoạt động của bạn."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "<PERSON><PERSON>m hiểu thêm về phí gas"}, "swapHighSlippage": {"message": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> gi<PERSON> cao"}, "swapIncludesGasAndMetaMaskFee": {"message": "Bao gồm phí gas và $1% phí của MetaMask", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Bao gồm $1% phí của MetaMask.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Báo giá thể hiện khoản phí $1% của MetaMask", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "Bao gồm $1% phí của MetaMask – $2", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "<PERSON><PERSON><PERSON> hiểu thêm về Hoán đổi"}, "swapLiquiditySourceInfo": {"message": "<PERSON><PERSON>g tôi tìm kiếm nhiều nguồn than<PERSON>ho<PERSON> (sàn gia<PERSON>, nền tảng tổng hợp thanh khoản và nhà tạo lập thị trường chuyên nghiệp) để so sánh tỷ giá và phí mạng."}, "swapLowSlippage": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> gi<PERSON> thấp"}, "swapMaxSlippage": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> giá tối đa"}, "swapMetaMaskFee": {"message": "<PERSON><PERSON> c<PERSON>"}, "swapMetaMaskFeeDescription": {"message": "Khoản phí $1% sẽ tự động được tính vào báo giá này. Bạn trả tiền để đổi lấy giấy phép sử dụng phần mềm tổng hợp thông tin từ nhà cung cấp thanh khoản của MetaMask.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 báo giá.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Báo giá mới sẽ có sau $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "<PERSON><PERSON>ông có token nào phù hợp với $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "$1 của bạn sẽ được thêm vào tài khoản sau khi xử lý xong giao dịch.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Bạn sắp ho<PERSON> đổi $1 $2 (~$3) lấy $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "<PERSON><PERSON><PERSON> l<PERSON>ch giá ~$1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> thể xác định tác động giá do thiếu dữ liệu giá thị trường. Vui lòng xác nhận rằng bạn cảm thấy thoải mái với số lượng token bạn sắp nhận được trước khi hoán đổi."}, "swapPriceUnavailableTitle": {"message": "<PERSON><PERSON><PERSON> kiểm tra tỷ giá trước khi tiếp tục"}, "swapProcessing": {"message": "<PERSON><PERSON> lý"}, "swapQuoteDetails": {"message": "<PERSON> tiết báo giá"}, "swapQuoteNofM": {"message": "$1/$2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "<PERSON><PERSON><PERSON><PERSON> báo giá"}, "swapQuotesExpiredErrorDescription": {"message": "<PERSON><PERSON> lòng yêu cầu báo giá mới để biết các tỷ giá mới nhất."}, "swapQuotesExpiredErrorTitle": {"message": "<PERSON><PERSON>t thời gian chờ báo giá"}, "swapQuotesNotAvailableDescription": {"message": "Tuyến giao dịch này hiện không khả dụng. <PERSON><PERSON><PERSON> thử thay đổi số tiền, mạng hoặc token và chúng tôi sẽ tìm ra tùy chọn tốt nhất."}, "swapQuotesNotAvailableErrorDescription": {"message": "<PERSON><PERSON><PERSON> thử điều chỉnh số tiền hoặc tùy chọn cài đặt mức trượt giá và thử lại."}, "swapQuotesNotAvailableErrorTitle": {"message": "Không có báo giá"}, "swapRate": {"message": "Tỷ giá"}, "swapReceiving": {"message": "Nhậ<PERSON>"}, "swapReceivingInfoTooltip": {"message": "Đ<PERSON>y là giá trị ước tính. Số tiền chính xác phụ thuộc vào mức trượt giá."}, "swapRequestForQuotation": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u báo giá"}, "swapSelect": {"message": "<PERSON><PERSON><PERSON>"}, "swapSelectAQuote": {"message": "<PERSON><PERSON><PERSON> một báo giá"}, "swapSelectAToken": {"message": "Ch<PERSON>n token"}, "swapSelectQuotePopoverDescription": {"message": "<PERSON>ư<PERSON><PERSON> đây là tất cả các báo giá thu thập từ nhiều nguồn thanh k<PERSON>n."}, "swapSelectToken": {"message": "Ch<PERSON>n token"}, "swapShowLatestQuotes": {"message": "<PERSON><PERSON><PERSON> thị báo giá mới nhất"}, "swapSlippageHighDescription": {"message": "<PERSON><PERSON><PERSON> trư<PERSON>t giá đã nhập ($1%) được xem là quá cao và có thể dẫn đến tỷ giá không sinh lời", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> gi<PERSON> cao"}, "swapSlippageLowDescription": {"message": "<PERSON><PERSON><PERSON> trị thấp như thế này ($1%) có thể dẫn đến hoán đổi không thành công", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> gi<PERSON> thấp"}, "swapSlippageNegativeDescription": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> giá phải lớn hơn hoặc bằng 0"}, "swapSlippageNegativeTitle": {"message": "<PERSON><PERSON><PERSON> mức tr<PERSON><PERSON><PERSON> giá để tiếp tục"}, "swapSlippageOverLimitDescription": {"message": "Giới hạn trượt giá phải từ 15% trở xuống. <PERSON><PERSON><PERSON> trượt giá cao hơn sẽ dẫn đến tỷ giá không sinh lời."}, "swapSlippageOverLimitTitle": {"message": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON> giá rất cao"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "<PERSON>hi giá giữa thời điểm đặt lệnh và thời điểm xác nhận lệnh thay đổi, hiện tượng này được gọi là \"trượt giá\". Giao dịch hoán đổi của bạn sẽ tự động hủy nếu mức trượt giá vượt quá \"mức trượt giá cho phép\" đã đặt."}, "swapSlippageZeroDescription": {"message": "<PERSON><PERSON> ít nhà cung cấp báo giá có mức trượt giá bằng 0 hơn sẽ dẫn đến báo giá kém cạnh tranh hơn."}, "swapSlippageZeroTitle": {"message": "<PERSON><PERSON><PERSON> nguồn nhà cung cấp có mức tr<PERSON><PERSON><PERSON> giá bằng 0"}, "swapSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapSuggested": {"message": "<PERSON><PERSON> đổi gợi ý"}, "swapSuggestedGasSettingToolTipMessage": {"message": "<PERSON><PERSON> đổi là các giao dịch phức tạp và nhạy cảm với thời gian. Chúng tôi đề xuất mức phí gas này để có sự cân bằng tốt giữa chi phí và tỉ lệ Hoán đổi thành công."}, "swapSwapFrom": {"message": "<PERSON><PERSON> đổi từ"}, "swapSwapSwitch": {"message": "<PERSON><PERSON><PERSON><PERSON> từ và chuyển sang token khác"}, "swapSwapTo": {"message": "<PERSON><PERSON> sang"}, "swapToConfirmWithHwWallet": {"message": "<PERSON><PERSON> xác nhận ví cứng của bạn"}, "swapTokenAddedManuallyDescription": {"message": "<PERSON><PERSON><PERSON> <PERSON>h token này trên $1 và đảm bảo đây là token mà bạn muốn giao dịch.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "<PERSON><PERSON> thêm token theo cách thủ công"}, "swapTokenAvailable": {"message": "Đã thêm $1 vào tài k<PERSON>ản của bạn.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "<PERSON><PERSON><PERSON> tôi không truy xuất đ<PERSON><PERSON><PERSON> số dư $1 của bạn", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Token không có sẵn để hoán đổi trong khu vực này"}, "swapTokenToToken": {"message": "<PERSON><PERSON> đ<PERSON> $1 sang $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 chỉ được xác minh trên 1 nguồn. <PERSON><PERSON>y xem xét xác minh nó trên $2 trước khi tiếp tục.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Token có dấu hiệu giả"}, "swapTokenVerifiedSources": {"message": "<PERSON><PERSON><PERSON><PERSON> xác nhận bởi $1 nguồn. <PERSON><PERSON><PERSON> minh trên $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 cho phép tối đa $2 số thập phân", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "<PERSON><PERSON> hoàn tất giao dịch"}, "swapTwoTransactions": {"message": "2 giao d<PERSON>ch"}, "swapUnknown": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "swapZeroSlippage": {"message": "<PERSON><PERSON><PERSON> gi<PERSON> 0%"}, "swapsMaxSlippage": {"message": "G<PERSON><PERSON><PERSON> hạn trư<PERSON>t giá"}, "swapsNotEnoughToken": {"message": "Không đủ $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "<PERSON><PERSON> ho<PERSON> động"}, "switch": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "switchEthereumChainConfirmationDescription": {"message": "<PERSON><PERSON> tác này sẽ chuyển mạng được chọn trong MetaMask sang một mạng đã thêm trước đó:"}, "switchEthereumChainConfirmationTitle": {"message": "Cho phép trang web này chuyển mạng?"}, "switchInputCurrency": {"message": "Chuyển đổi loại tiền tệ đầu vào"}, "switchNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> mạng"}, "switchNetworks": {"message": "<PERSON><PERSON><PERSON><PERSON> mạng"}, "switchToNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> sang $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> sang tài khoản này"}, "switchedNetworkToastDecline": {"message": "<PERSON><PERSON><PERSON><PERSON> hiển thị lại"}, "switchedNetworkToastMessage": {"message": "$1 hiện đang hoạt động trên $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Bạn hiện đang sử dụng $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "<PERSON><PERSON> bạn chuyển mạng, m<PERSON><PERSON> xác nhận đang chờ xử lý sẽ bị hủy"}, "symbol": {"message": "<PERSON><PERSON>"}, "symbolBetweenZeroTwelve": {"message": "<PERSON><PERSON> hiệu không đư<PERSON><PERSON> dài quá 11 ký tự."}, "tenPercentIncreased": {"message": "Tăng 10%"}, "terms": {"message": "<PERSON><PERSON><PERSON><PERSON>n sử dụng"}, "termsOfService": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ"}, "termsOfUseAgreeText": {"message": " Tôi đồng ý với Điều khoản sử dụng được áp dụng cho việc tôi sử dụng MetaMask và tất cả các tính năng của MetaMask"}, "termsOfUseFooterText": {"message": "<PERSON><PERSON> lòng cuộn để đọc tất cả các phần"}, "termsOfUseTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>n sử dụng của chúng tôi đã đư<PERSON><PERSON> cập nhật"}, "testNetworks": {"message": "<PERSON><PERSON><PERSON> thử nghi<PERSON>m"}, "testnets": {"message": "<PERSON><PERSON><PERSON> thử nghi<PERSON>m"}, "theme": {"message": "Chủ đề"}, "themeDescription": {"message": "<PERSON><PERSON><PERSON> chủ đề MetaMask yêu thích của bạn."}, "thirdPartySoftware": {"message": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o ph<PERSON>n mềm của bên thứ ba", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "<PERSON><PERSON><PERSON><PERSON> gian"}, "tipsForUsingAWallet": {"message": "<PERSON><PERSON><PERSON> k<PERSON>ên sử dụng ví"}, "tipsForUsingAWalletDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> thêm token sẽ mở ra nhiều cách sử dụng web3 hơn."}, "to": {"message": "<PERSON><PERSON><PERSON>"}, "toAddress": {"message": "Đến: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "<PERSON><PERSON>g tôi sử dụng các dịch vụ 4byte.directory và Sourcify để giải mã và hiển thị dữ liệu giao dịch dễ đọc hơn. Điều này giúp bạn hiểu rõ kết quả của các giao dịch đang chờ xử lý và đã thực hiện, nhưng có thể dẫn đến việc địa chỉ IP của bạn được chia sẻ."}, "token": {"message": "Token"}, "tokenAddress": {"message": "Địa chỉ token"}, "tokenAlreadyAdded": {"message": "<PERSON><PERSON> thêm token."}, "tokenAutoDetection": {"message": "Tự động phát hiện token"}, "tokenContractAddress": {"message": "Đ<PERSON>a chỉ hợp đồng token"}, "tokenDecimal": {"message": "<PERSON><PERSON> thập phân của token"}, "tokenDecimalFetchFailed": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u số thập phân của token. T<PERSON><PERSON> trên: $1"}, "tokenDetails": {"message": "<PERSON> tiết token"}, "tokenFoundTitle": {"message": "<PERSON><PERSON> tìm thấy 1 token mới"}, "tokenId": {"message": "ID Token"}, "tokenList": {"message": "<PERSON><PERSON> s<PERSON>"}, "tokenMarketplace": {"message": "<PERSON><PERSON><PERSON> trường token"}, "tokenScamSecurityRisk": {"message": "rủi ro về bảo mật và lừa đảo token"}, "tokenStandard": {"message": "Ti<PERSON><PERSON> ch<PERSON>n token"}, "tokenSymbol": {"message": "<PERSON><PERSON> hi<PERSON> token"}, "tokens": {"message": "Token"}, "tokensFoundTitle": {"message": "Đ<PERSON> tìm thấy $1 token mới", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Token trong bộ sưu tập"}, "tooltipApproveButton": {"message": "Tôi đã hiểu"}, "tooltipSatusConnected": {"message": "<PERSON><PERSON> kết nối"}, "tooltipSatusConnectedUpperCase": {"message": "<PERSON><PERSON> kết nối"}, "tooltipSatusNotConnected": {"message": "ch<PERSON><PERSON> k<PERSON>i"}, "total": {"message": "Tổng"}, "totalVolume": {"message": "<PERSON><PERSON><PERSON> kh<PERSON>i lư<PERSON> giao d<PERSON>ch"}, "transaction": {"message": "giao d<PERSON>ch"}, "transactionCancelAttempted": {"message": "Đã cố gắng hủy giao dịch với mức phí gas ước tính $1 lúc $2"}, "transactionCancelSuccess": {"message": "<PERSON><PERSON> hủy thành công giao dịch lúc $2"}, "transactionConfirmed": {"message": "Đã xác nhận giao dịch lúc $2."}, "transactionCreated": {"message": "Đã tạo giao dịch với giá trị $1 lúc $2."}, "transactionDataFunction": {"message": "<PERSON><PERSON><PERSON>"}, "transactionDetailGasHeading": {"message": "Phí gas <PERSON> t<PERSON>h"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Số lượng + phí"}, "transactionDropped": {"message": "Đ<PERSON> ngừng giao dịch lúc $2."}, "transactionError": {"message": "Lỗi giao dịch. <PERSON><PERSON> x<PERSON>y ra ngoại lệ trong mã hợp đồng."}, "transactionErrorNoContract": {"message": "<PERSON><PERSON> cố gắng gọi một hàm trên địa chỉ không có hợp đồng."}, "transactionErrored": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ch đã gặp lỗi."}, "transactionFlowNetwork": {"message": "Mạng"}, "transactionHistoryBaseFee": {"message": "<PERSON><PERSON> cơ sở (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Tổng phí gas L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Hạn mức phí gas L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Giá gas L2"}, "transactionHistoryMaxFeePerGas": {"message": "<PERSON>í tối đa mỗi gas"}, "transactionHistoryPriorityFee": {"message": "<PERSON><PERSON> <PERSON><PERSON> (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Tổng phí gas"}, "transactionIdLabel": {"message": "ID giao d<PERSON>ch", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>y bao gồm: $1."}, "transactionResubmitted": {"message": "Đã gửi lại giao dịch với mức phí gas ước tính tăng lên $1 lúc $2"}, "transactionSettings": {"message": "Cài đặt giao dịch"}, "transactionSubmitted": {"message": "Đã gửi giao dịch với mức phí gas ước tính $1 lúc $2."}, "transactionTotalGasFee": {"message": "Tổng phí gas", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Đ<PERSON> cập nhật giao dịch lúc $2."}, "transactions": {"message": "<PERSON><PERSON><PERSON>"}, "transfer": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "transferCrypto": {"message": "Chuyển tiền mã hóa"}, "transferFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> từ"}, "transferRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u chuyển tiền"}, "trillionAbbreviation": {"message": "Nghìn Tỷ", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "<PERSON><PERSON>g tôi đang gặp sự cố khi kết nối với Ledger của bạn. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "<PERSON>em lại hướng dẫn kết nối ví cứng của chúng tôi và thử lại.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "N<PERSON>u đang sử dụng phiên bản Firefox mới nhất, bạn có thể gặp sự cố liên quan đến việc Firefox không còn hỗ trợ U2F. Tìm hiểu cách khắc phục sự cố này $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "tại đây", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "<PERSON><PERSON>g tôi đã gặp phải sự cố khi kết nối với $1 của bạn, hãy thử xem lại $2 và thử lại.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "MetaMask đã gặp sự cố khi khởi động. Lỗi này có thể xảy ra không liên tục, vì vậy hãy thử khởi động lại tiện ích."}, "tryAgain": {"message": "<PERSON><PERSON><PERSON> lại"}, "turnOff": {"message": "Tắt"}, "turnOffMetamaskNotificationsError": {"message": "<PERSON><PERSON> xảy ra lỗi khi tắt thông báo. <PERSON><PERSON> lòng thử lại sau."}, "turnOn": {"message": "<PERSON><PERSON><PERSON>"}, "turnOnMetamaskNotifications": {"message": "<PERSON><PERSON><PERSON> thông báo"}, "turnOnMetamaskNotificationsButton": {"message": "<PERSON><PERSON><PERSON>"}, "turnOnMetamaskNotificationsError": {"message": "<PERSON><PERSON> xảy ra lỗi khi tạo thông báo. <PERSON><PERSON> lòng thử lại sau."}, "turnOnMetamaskNotificationsMessageFirst": {"message": "<PERSON><PERSON><PERSON><PERSON> thông báo để cập nhật tình hình trong ví của bạn."}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "cài đặt thông báo."}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> cách chúng tôi bảo vệ quyền riêng tư của bạn khi sử dụng tính năng này."}, "turnOnMetamaskNotificationsMessageSecond": {"message": "Để sử dụng tính năng thông báo ví, chúng tôi dùng hồ sơ để đồng bộ một số chế độ cài đặt trên các thiết bị của bạn. $1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "Bạn có thể tắt thông báo bất kỳ lúc nào trong $1"}, "turnOnTokenDetection": {"message": "<PERSON><PERSON><PERSON> ph<PERSON>t hiện token nâng cao"}, "tutorial": {"message": "Hướng dẫn"}, "twelveHrTitle": {"message": "12 giờ:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "<PERSON><PERSON><PERSON> ch<PERSON>n"}, "unexpectedBehavior": {"message": "<PERSON><PERSON><PERSON> là hành vi bất thường và cần đư<PERSON><PERSON> báo cáo như một lỗi, ngay cả khi tài khoản của bạn đã được khôi phục đúng cách. Sử dụng liên kết bên dưới để gửi báo cáo lỗi cho MetaMask."}, "units": {"message": "đơn vị"}, "unknown": {"message": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>"}, "unknownCollection": {"message": "<PERSON><PERSON> sưu tập chưa có tên"}, "unknownNetworkForKeyEntropy": {"message": "<PERSON>ạng ch<PERSON>a x<PERSON>c <PERSON>", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Lỗi: <PERSON><PERSON><PERSON> tôi không thể xác định mã QR đó"}, "unlimited": {"message": "<PERSON><PERSON><PERSON>ng gi<PERSON>i hạn"}, "unlock": {"message": "Mở khóa"}, "unpin": {"message": "Bỏ ghim"}, "unrecognizedChain": {"message": "<PERSON><PERSON><PERSON><PERSON> thể nhận diện mạng tùy chỉnh này", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "Hiện không hỗ trợ gửi token NFT (ERC-721)", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "<PERSON><PERSON><PERSON> của token này tính theo USD rất biến động, bạn có nguy cơ mất giá trị lớn khi tương tác với token này."}, "unstableTokenPriceTitle": {"message": "<PERSON><PERSON><PERSON> <PERSON> không <PERSON>n định"}, "upArrow": {"message": "mũi tên lên"}, "update": {"message": "<PERSON><PERSON><PERSON>"}, "updateEthereumChainConfirmationDescription": {"message": "Trang web này đang yêu cầu cập nhật URL mạng mặc định của bạn. Bạn có thể chỉnh sửa thông tin mặc định và mạng bất cứ lúc nào."}, "updateNetworkConfirmationTitle": {"message": "<PERSON>ậ<PERSON> nhật $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "<PERSON><PERSON><PERSON> nhật thông tin của bạn hoặc"}, "updateRequest": {"message": "<PERSON><PERSON><PERSON> c<PERSON>u cập nh<PERSON>t"}, "updatedRpcForNetworks": {"message": "<PERSON><PERSON> cập nhật RPC mạng"}, "uploadDropFile": {"message": "<PERSON><PERSON><PERSON> tập tin của bạn vào đây"}, "uploadFile": {"message": "<PERSON><PERSON><PERSON> lên tập tin"}, "urlErrorMsg": {"message": "URL phải có tiền tố HTTP/HTTPS phù hợp."}, "use4ByteResolution": {"message": "G<PERSON>ải mã hợp đồng thông minh"}, "useMultiAccountBalanceChecker": {"message": "<PERSON><PERSON> lý hàng loạt yêu cầu số dư tài k<PERSON>n"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "<PERSON>h<PERSON>n thông tin cập nhật số dư nhanh hơn bằng cách gộp các yêu cầu số dư tài khoản. <PERSON><PERSON><PERSON><PERSON> này cho phép chúng tôi tìm nạp các số dư tài khoản của bạn cùng với nhau, qua đó bạn sẽ nhận được thông tin cập nhật nhanh hơn nhằm cải thiện trải nghiệm. <PERSON>hi tắt tính năng này, các bên thứ ba có ít khả năng sẽ liên kết các tài khoản của bạn với nhau hơn."}, "useNftDetection": {"message": "Tự động phát hiện NFT"}, "useNftDetectionDescriptionText": {"message": "Cho phép MetaMask thêm các NFT mà bạn sở hữu bằng cách sử dụng dịch vụ của bên thứ ba. T<PERSON><PERSON> năng Tự động phát hiện NFT sẽ làm lộ địa chỉ IP và tài khoản của bạn với các dịch vụ này. Việc bật tính năng này có thể liên kết địa chỉ IP của bạn với địa chỉ Ethereum của bạn và hiển thị các NFT giả do những kẻ lừa đảo phát tán. Bạn c<PERSON> thể thêm token theo cách thủ công để tránh rủi ro này."}, "usePhishingDetection": {"message": "Sử dụng tính năng phát hiện lừa đảo"}, "usePhishingDetectionDescription": {"message": "<PERSON><PERSON>n thị cảnh báo đối với các tên miền lừa đảo nhắm đến người dùng Ethereum"}, "useSafeChainsListValidation": {"message": "<PERSON><PERSON><PERSON> tra thông tin mạng"}, "useSafeChainsListValidationDescription": {"message": "MetaMask sử dụng dịch vụ của bên thứ ba có tên $1 để hiển thị thông tin mạng chính xác và được tiêu chuẩn hóa. <PERSON>iều này giúp hạn chế khả năng bạn kết nối với mạng độc hại hoặc mạng không chính xác. Khi sử dụng tính năng này, địa chỉ IP của bạn sẽ được hiển thị với chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Tự động hiển thị các token được gửi vào tài khoản của bạn có liên quan đến hoạt động trao đổi thông tin với các máy chủ bên thứ ba để tìm nạp hình ảnh của token. Các máy chủ đó sẽ có quyền truy cập vào địa chỉ IP của bạn."}, "usedByClients": {"message": "<PERSON><PERSON><PERSON><PERSON> nhiều ví khác nhau sử dụng"}, "userName": {"message": "<PERSON><PERSON><PERSON> dùng"}, "userOpContractDeployError": {"message": "Triển khai hợp đồng từ tài khoản hợp đồng thông minh không được hỗ trợ"}, "version": {"message": "<PERSON><PERSON><PERSON>"}, "view": {"message": "Xem"}, "viewActivity": {"message": "<PERSON><PERSON> ho<PERSON> động"}, "viewAllQuotes": {"message": "xem tất cả báo giá"}, "viewContact": {"message": "<PERSON><PERSON> đ<PERSON> chỉ liên hệ"}, "viewDetails": {"message": "<PERSON>em chi tiết"}, "viewMore": {"message": "<PERSON><PERSON>"}, "viewOnBlockExplorer": {"message": "<PERSON>em trên trình khám phá khối"}, "viewOnCustomBlockExplorer": {"message": "Xem $1 tại $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Xem $1 trên Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "<PERSON>em trên trình khám phá"}, "viewOnOpensea": {"message": "<PERSON><PERSON> trên <PERSON>"}, "viewSolanaAccount": {"message": "<PERSON><PERSON> t<PERSON>"}, "viewTransaction": {"message": "<PERSON>em giao d<PERSON>ch"}, "viewinExplorer": {"message": "Xem $1 trong trình khám phá", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "<PERSON><PERSON><PERSON> c<PERSON>p trang web"}, "visitSupportDataConsentModalAccept": {"message": "<PERSON><PERSON><PERSON>"}, "visitSupportDataConsentModalDescription": {"message": "Bạn có muốn chia sẻ Mã định danh MetaMask và phiên bản ứng dụng của mình với Trung tâm hỗ trợ không? Điều này có thể giúp chúng tôi giải quyết vấn đề của bạn tốt hơn, nhưng không bắt buộc."}, "visitSupportDataConsentModalReject": {"message": "Không chia sẻ"}, "visitSupportDataConsentModalTitle": {"message": "<PERSON><PERSON> sẻ thông tin thiết bị với bộ phận hỗ trợ"}, "visitWebSite": {"message": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> trang <PERSON> của chúng tôi"}, "wallet": {"message": "Ví"}, "walletConnectionGuide": {"message": "hướng dẫn của chúng tôi về cách kết nối ví cứng"}, "wantToAddThisNetwork": {"message": "Bạn muốn thêm mạng này?"}, "wantsToAddThisAsset": {"message": "$1 muốn thêm tài sản này vào ví của bạn."}, "warning": {"message": "<PERSON><PERSON><PERSON> b<PERSON>o"}, "warningFromSnap": {"message": "<PERSON><PERSON><PERSON> báo từ $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "<PERSON>ật tùy chọn này sẽ cho phép bạn theo dõi tài khoản Ethereum thông qua địa chỉ công khai hoặc tên ENS. <PERSON><PERSON> phản hồi về tính năng Beta này, vui lòng hoàn thành $1 này.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "<PERSON> t<PERSON>n Ethereum (Beta)"}, "watchOutMessage": {"message": "<PERSON><PERSON><PERSON> cẩn thận với $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "<PERSON><PERSON><PERSON>"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "<PERSON><PERSON>g tôi nhận thấy rằng trang web hiện tại đã cố dùng API window.web3 đã bị xóa. Nếu trang web có vẻ như đã bị lỗi, vui lòng nhấp vào $1 để biết thêm thông tin.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "trang web", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "<PERSON><PERSON><PERSON> mừng bạn trở lại"}, "welcomeToMetaMask": {"message": "<PERSON><PERSON><PERSON> đ<PERSON>u nào"}, "whatsThis": {"message": "Đ<PERSON>y là gì?"}, "willApproveAmountForBridging": {"message": "<PERSON><PERSON> tác này sẽ phê duyệt $1 để thực hiện cầu nối."}, "willApproveAmountForBridgingHardware": {"message": "Bạn sẽ cần xác nhận hai giao dịch trên ví cứng của mình."}, "withdrawing": {"message": "<PERSON><PERSON> rút tiền"}, "wrongNetworkName": {"message": "<PERSON> sơ của chúng tô<PERSON>, tên mạng có thể không khớp chính xác với ID chuỗi này."}, "yes": {"message": "<PERSON><PERSON>"}, "you": {"message": "Bạn"}, "youDeclinedTheTransaction": {"message": "Bạn đã từ chối giao dịch."}, "youNeedToAllowCameraAccess": {"message": "<PERSON><PERSON>n cần cho phép truy cập vào máy ảnh để sử dụng tính năng này."}, "youReceived": {"message": "Bạn đã nhận", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "Bạn đã gửi", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "<PERSON><PERSON><PERSON> c<PERSON>a bạn"}, "yourActivity": {"message": "<PERSON><PERSON><PERSON> động của bạn"}, "yourBalance": {"message": "Số dư của bạn"}, "yourNFTmayBeAtRisk": {"message": "NFT của bạn có thể gặp rủi ro"}, "yourNetworks": {"message": "<PERSON><PERSON><PERSON> c<PERSON>a bạn"}, "yourPrivateSeedPhrase": {"message": "<PERSON><PERSON><PERSON> từ khôi phục bí mật của bạn"}, "yourTransactionConfirmed": {"message": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> x<PERSON>c <PERSON>n"}, "yourTransactionJustConfirmed": {"message": "<PERSON><PERSON><PERSON> tôi không thể hủy giao dịch của bạn trước khi nó được xác nhận trên chuỗi khối."}, "yourWalletIsReady": {"message": "<PERSON>í của bạn đã sẵn sàng"}}