LavaPack.loadPolicy({resources:{"@babel/runtime":{globals:{regeneratorRuntime:"write"}},"@metamask/notification-services-controller>@contentful/rich-text-html-renderer":{globals:{SuppressedError:!0}},"@ensdomains/content-hash":{globals:{"console.warn":!0},packages:{"browserify>buffer":!0,"@ensdomains/content-hash>cids":!0,"@ensdomains/content-hash>js-base64":!0,"@ensdomains/content-hash>multicodec":!0,"@ensdomains/content-hash>multihashes":!0}},"@ethereumjs/tx>@ethereumjs/common":{packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"webpack>events":!0}},"eth-lattice-keyring>gridplus-sdk>@ethereumjs/common":{packages:{"eth-lattice-keyring>gridplus-sdk>@ethereumjs/common>@ethereumjs/util":!0,"browserify>buffer":!0,"eth-lattice-keyring>gridplus-sdk>crc-32":!0,"webpack>events":!0}},"@ethereumjs/tx>@ethereumjs/rlp":{globals:{TextEncoder:!0}},"@ethereumjs/tx>@ethereumjs/util>@ethereumjs/rlp":{globals:{TextEncoder:!0}},"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":{globals:{TextEncoder:!0}},"@metamask/eth-ledger-bridge-keyring>@ethereumjs/rlp":{globals:{TextEncoder:!0}},"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@ethereumjs/rlp":{globals:{TextEncoder:!0}},"@ethereumjs/tx":{packages:{"@ethereumjs/tx>@ethereumjs/common":!0,"@ethereumjs/tx>@ethereumjs/rlp":!0,"@ethereumjs/tx>@ethereumjs/util":!0,"@ethereumjs/tx>ethereum-cryptography":!0}},"eth-lattice-keyring>gridplus-sdk>@ethereumjs/common>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@ethereumjs/tx>@ethereumjs/util":{globals:{"console.warn":!0,fetch:!0},packages:{"@ethereumjs/tx>@ethereumjs/util>@ethereumjs/rlp":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/keyring-controller>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@metamask/signature-controller>@metamask/eth-sig-util>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"eth-lattice-keyring>@ethereumjs/util":{globals:{"console.warn":!0},packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack>events":!0,"browserify>insert-module-globals>is-buffer":!0,"eth-lattice-keyring>@ethereumjs/util>micro-ftch":!0}},"@ethersproject/abi":{globals:{"console.log":!0},packages:{"ethers>@ethersproject/address":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"@ethersproject/hash":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"ethers>@ethersproject/abstract-provider":{packages:{"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0}},"ethers>@ethersproject/abstract-signer":{packages:{"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0}},"ethers>@ethersproject/address":{packages:{"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/rlp":!0}},"ethers>@ethersproject/base64":{globals:{atob:!0,btoa:!0},packages:{"@ethersproject/bytes":!0}},"ethers>@ethersproject/basex":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/properties":!0}},"@ethersproject/bignumber":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"bn.js":!0}},"@ethersproject/bytes":{packages:{"ethers>@ethersproject/logger":!0}},"ethers>@ethersproject/constants":{packages:{"@ethersproject/bignumber":!0}},"@ethersproject/contracts":{globals:{setTimeout:!0},packages:{"@ethersproject/abi":!0,"ethers>@ethersproject/abstract-provider":!0,"ethers>@ethersproject/abstract-signer":!0,"ethers>@ethersproject/address":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/transactions":!0}},"@ethersproject/hash":{packages:{"ethers>@ethersproject/address":!0,"ethers>@ethersproject/base64":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"@ethersproject/hdnode":{packages:{"ethers>@ethersproject/basex":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/pbkdf2":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/sha2":!0,"ethers>@ethersproject/signing-key":!0,"ethers>@ethersproject/strings":!0,"ethers>@ethersproject/transactions":!0,"ethers>@ethersproject/wordlists":!0}},"ethers>@ethersproject/json-wallets":{packages:{"ethers>@ethersproject/address":!0,"@ethersproject/bytes":!0,"@ethersproject/hdnode":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/pbkdf2":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/random":!0,"ethers>@ethersproject/strings":!0,"ethers>@ethersproject/transactions":!0,"ethers>@ethersproject/json-wallets>aes-js":!0,"ethers>@ethersproject/json-wallets>scrypt-js":!0}},"ethers>@ethersproject/keccak256":{packages:{"@ethersproject/bytes":!0,"eth-ens-namehash>js-sha3":!0}},"ethers>@ethersproject/logger":{globals:{console:!0}},"ethers>@ethersproject/providers>@ethersproject/networks":{packages:{"ethers>@ethersproject/logger":!0}},"@metamask/test-bundler>@ethersproject/networks":{packages:{"ethers>@ethersproject/logger":!0}},"ethers>@ethersproject/pbkdf2":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/sha2":!0}},"ethers>@ethersproject/properties":{packages:{"ethers>@ethersproject/logger":!0}},"@ethersproject/providers":{globals:{WebSocket:!0,clearInterval:!0,clearTimeout:!0,"console.log":!0,"console.warn":!0,setInterval:!0,setTimeout:!0},packages:{"ethers>@ethersproject/abstract-provider":!0,"ethers>@ethersproject/abstract-signer":!0,"ethers>@ethersproject/address":!0,"ethers>@ethersproject/base64":!0,"ethers>@ethersproject/basex":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"@ethersproject/hash":!0,"ethers>@ethersproject/logger":!0,"@metamask/test-bundler>@ethersproject/networks":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/random":!0,"ethers>@ethersproject/sha2":!0,"ethers>@ethersproject/strings":!0,"ethers>@ethersproject/transactions":!0,"@ethersproject/providers>@ethersproject/web":!0,"@ethersproject/providers>bech32":!0}},"ethers>@ethersproject/providers":{globals:{WebSocket:!0,clearInterval:!0,clearTimeout:!0,"console.log":!0,"console.warn":!0,setInterval:!0,setTimeout:!0},packages:{"ethers>@ethersproject/abstract-provider":!0,"ethers>@ethersproject/abstract-signer":!0,"ethers>@ethersproject/address":!0,"ethers>@ethersproject/base64":!0,"ethers>@ethersproject/basex":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"@ethersproject/hash":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/providers>@ethersproject/networks":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/random":!0,"ethers>@ethersproject/sha2":!0,"ethers>@ethersproject/strings":!0,"ethers>@ethersproject/transactions":!0,"ethers>@ethersproject/providers>@ethersproject/web":!0,"ethers>@ethersproject/providers>bech32":!0}},"@ethersproject/providers>@ethersproject/random":{globals:{"crypto.getRandomValues":!0}},"ethers>@ethersproject/random":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0}},"ethers>@ethersproject/rlp":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0}},"ethers>@ethersproject/sha2":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/sha2>hash.js":!0}},"ethers>@ethersproject/signing-key":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"@metamask/ppom-validator>elliptic":!0}},"ethers>@ethersproject/solidity":{packages:{"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/sha2":!0,"ethers>@ethersproject/strings":!0}},"ethers>@ethersproject/strings":{packages:{"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"ethers>@ethersproject/logger":!0}},"ethers>@ethersproject/transactions":{packages:{"ethers>@ethersproject/address":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/rlp":!0,"ethers>@ethersproject/signing-key":!0}},"ethers>@ethersproject/units":{packages:{"@ethersproject/bignumber":!0,"ethers>@ethersproject/logger":!0}},"@ethersproject/wallet":{packages:{"ethers>@ethersproject/abstract-provider":!0,"ethers>@ethersproject/abstract-signer":!0,"ethers>@ethersproject/address":!0,"@ethersproject/bytes":!0,"@ethersproject/hash":!0,"@ethersproject/hdnode":!0,"ethers>@ethersproject/json-wallets":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/random":!0,"ethers>@ethersproject/signing-key":!0,"ethers>@ethersproject/transactions":!0}},"@ethersproject/providers>@ethersproject/web":{globals:{clearTimeout:!0,fetch:!0,setTimeout:!0},packages:{"ethers>@ethersproject/base64":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"ethers>@ethersproject/providers>@ethersproject/web":{globals:{clearTimeout:!0,fetch:!0,setTimeout:!0},packages:{"ethers>@ethersproject/base64":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"ethers>@ethersproject/web":{globals:{clearTimeout:!0,fetch:!0,setTimeout:!0},packages:{"ethers>@ethersproject/base64":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"ethers>@ethersproject/wordlists":{packages:{"@ethersproject/bytes":!0,"@ethersproject/hash":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/strings":!0}},"@metamask/notification-services-controller>firebase>@firebase/app":{globals:{FinalizationRegistry:!0,"console.warn":!0},packages:{"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/component":!0,"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/logger":!0,"@metamask/notification-services-controller>firebase>@firebase/util":!0,"@metamask/notification-services-controller>firebase>@firebase/app>idb":!0}},"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/component":{packages:{"@metamask/notification-services-controller>firebase>@firebase/util":!0}},"@metamask/notification-services-controller>firebase>@firebase/installations":{globals:{BroadcastChannel:!0,Headers:!0,btoa:!0,"console.error":!0,crypto:!0,fetch:!0,msCrypto:!0,"navigator.onLine":!0,setTimeout:!0},packages:{"@metamask/notification-services-controller>firebase>@firebase/app":!0,"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/component":!0,"@metamask/notification-services-controller>firebase>@firebase/util":!0,"@metamask/notification-services-controller>firebase>@firebase/app>idb":!0}},"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/logger":{globals:{console:!0}},"@metamask/notification-services-controller>firebase>@firebase/messaging":{globals:{Headers:!0,"Notification.maxActions":!0,"Notification.permission":!0,"Notification.requestPermission":!0,"PushManager.getSubscription":!0,"PushManager.subscribe":!0,"PushSubscription.prototype.hasOwnProperty":!0,ServiceWorkerRegistration:!0,URL:!0,addEventListener:!0,atob:!0,btoa:!0,clearTimeout:!0,"clients.matchAll":!0,"clients.openWindow":!0,"console.warn":!0,document:!0,fetch:!0,indexedDB:!0,"location.href":!0,"location.origin":!0,navigator:!0,"origin.replace":!0,"registration.showNotification":!0,setTimeout:!0},packages:{"@metamask/notification-services-controller>firebase>@firebase/app":!0,"@metamask/notification-services-controller>firebase>@firebase/app>@firebase/component":!0,"@metamask/notification-services-controller>firebase>@firebase/installations":!0,"@metamask/notification-services-controller>firebase>@firebase/util":!0,"@metamask/notification-services-controller>firebase>@firebase/app>idb":!0}},"@metamask/notification-services-controller>firebase>@firebase/util":{globals:{WorkerGlobalScope:!0,atob:!0,browser:!0,btoa:!0,chrome:!0,console:!0,document:!0,indexedDB:!0,navigator:!0,process:!0,setTimeout:!0},packages:{process:!0}},"@open-rpc/schema-utils-js>@json-schema-tools/dereferencer":{packages:{"@open-rpc/schema-utils-js>@json-schema-tools/reference-resolver":!0,"@open-rpc/schema-utils-js>@json-schema-tools/dereferencer>@json-schema-tools/traverse":!0,"@metamask/rpc-errors>fast-safe-stringify":!0}},"@open-rpc/schema-utils-js>@json-schema-tools/reference-resolver":{packages:{"@open-rpc/schema-utils-js>@json-schema-tools/reference-resolver>@json-schema-spec/json-pointer":!0,"@open-rpc/test-coverage>isomorphic-fetch":!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@ethereumjs/tx":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@ethereumjs/util":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@keystonehq/bc-ur-registry-eth":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0,uuid:!0}},"@keystonehq/bc-ur-registry-eth":{packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0,uuid:!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@keystonehq/bc-ur-registry-eth":{packages:{"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@ethereumjs/util":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0,uuid:!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth":{packages:{"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth>@ethereumjs/util":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0,uuid:!0}},"@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":{globals:{define:!0},packages:{"@ngraveio/bc-ur":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,buffer:!0,"browserify>buffer":!0,tslib:!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring>@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":{globals:{define:!0},packages:{"@ngraveio/bc-ur":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,buffer:!0,"browserify>buffer":!0,tslib:!0}},"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth>@keystonehq/bc-ur-registry":{globals:{define:!0},packages:{"@ngraveio/bc-ur":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,buffer:!0,"browserify>buffer":!0,tslib:!0}},"@keystonehq/metamask-airgapped-keyring":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@ethereumjs/tx":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/base-eth-keyring":!0,"@keystonehq/metamask-airgapped-keyring>@keystonehq/bc-ur-registry-eth":!0,"@metamask/obs-store":!0,"browserify>buffer":!0,"webpack>events":!0,uuid:!0}},"chart.js>@kurkle/color":{globals:{define:!0}},"@lavamoat/lavadome-react":{globals:{"Document.prototype":!0,"DocumentFragment.prototype":!0,"Element.prototype":!0,"Event.prototype":!0,"EventTarget.prototype":!0,"NavigateEvent.prototype":!0,"NavigationDestination.prototype":!0,"Node.prototype":!0,"console.warn":!0,document:!0,navigation:!0},packages:{react:!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/domain-service":{packages:{"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/logs":!0,axios:!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/errors":{globals:{"console.warn":!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/evm-tools":{packages:{"ethers>@ethersproject/constants":!0,"@ethersproject/hash":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/cryptoassets-evm-signatures":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/evm-tools>@ledgerhq/live-env":!0,axios:!0,"@metamask/ppom-validator>crypto-js":!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth":{globals:{"console.warn":!0},packages:{"@ethersproject/abi":!0,"ethers>@ethersproject/rlp":!0,"ethers>@ethersproject/transactions":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/cryptoassets-evm-signatures":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/domain-service":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/errors":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/evm-tools":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/logs":!0,axios:!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>bignumber.js":!0,"browserify>buffer":!0,semver:!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/evm-tools>@ledgerhq/live-env":{globals:{"console.warn":!0},packages:{"wait-on>rxjs":!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>@ledgerhq/logs":{globals:{__ledgerLogsListen:"write","console.error":!0}},"@material-ui/core":{globals:{Image:!0,_formatMuiErrorMessage:!0,addEventListener:!0,clearInterval:!0,clearTimeout:!0,"console.error":!0,"console.warn":!0,document:!0,getComputedStyle:!0,getSelection:!0,innerHeight:!0,innerWidth:!0,matchMedia:!0,navigator:!0,"performance.now":!0,removeEventListener:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/styles":!0,"@material-ui/core>@material-ui/system":!0,"@material-ui/core>@material-ui/utils":!0,"@material-ui/core>clsx":!0,"react-redux>hoist-non-react-statics":!0,"@material-ui/core>popper.js":!0,"prop-types":!0,react:!0,"react-dom":!0,"react-redux>react-is":!0,"react-transition-group":!0}},"@material-ui/core>@material-ui/styles":{globals:{"console.error":!0,"console.warn":!0,"document.createComment":!0,"document.head":!0},packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/utils":!0,"@material-ui/core>clsx":!0,"react-redux>hoist-non-react-statics":!0,"@material-ui/core>@material-ui/styles>jss-plugin-camel-case":!0,"@material-ui/core>@material-ui/styles>jss-plugin-default-unit":!0,"@material-ui/core>@material-ui/styles>jss-plugin-global":!0,"@material-ui/core>@material-ui/styles>jss-plugin-nested":!0,"@material-ui/core>@material-ui/styles>jss-plugin-props-sort":!0,"@material-ui/core>@material-ui/styles>jss-plugin-rule-value-function":!0,"@material-ui/core>@material-ui/styles>jss-plugin-vendor-prefixer":!0,"@material-ui/core>@material-ui/styles>jss":!0,"prop-types":!0,react:!0}},"@material-ui/core>@material-ui/system":{globals:{"console.error":!0,"console.warn":!0},packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/utils":!0,"prop-types":!0}},"@material-ui/core>@material-ui/utils":{packages:{"@babel/runtime":!0,"prop-types":!0,"react-redux>react-is":!0}},"@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/abi-utils>@metamask/utils":!0}},"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/keyring-controller>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/signature-controller>@metamask/eth-sig-util>@metamask/abi-utils":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/account-tree-controller":{globals:{"console.warn":!0},packages:{"@metamask/base-controller":!0,"@metamask/keyring-controller":!0,"@metamask/account-tree-controller>@metamask/snaps-utils":!0}},"@metamask/accounts-controller":{packages:{"@metamask/base-controller":!0,"@metamask/eth-snap-keyring":!0,"@metamask/keyring-api":!0,"@metamask/keyring-controller":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/utils":!0,"@ethereumjs/tx>ethereum-cryptography":!0,uuid:!0}},"@metamask/address-book-controller":{packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0}},"@metamask/announcement-controller":{packages:{"@metamask/base-controller":!0}},"@metamask/approval-controller":{globals:{"console.info":!0},packages:{"@metamask/base-controller":!0,"@metamask/rpc-errors":!0,nanoid:!0}},"@metamask/assets-controllers":{globals:{AbortController:!0,Headers:!0,URL:!0,URLSearchParams:!0,clearInterval:!0,clearTimeout:!0,"console.error":!0,"console.log":!0,fetch:!0,setInterval:!0,setTimeout:!0},packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"ethers>@ethersproject/address":!0,"@ethersproject/bignumber":!0,"@ethersproject/contracts":!0,"@ethersproject/providers":!0,"@metamask/abi-utils":!0,"@metamask/base-controller":!0,"@metamask/contract-metadata":!0,"@metamask/controller-utils":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/keyring-api":!0,"@metamask/keyring-snap-client":!0,"@metamask/metamask-eth-abis":!0,"@metamask/phishing-controller":!0,"@metamask/assets-controllers>@metamask/polling-controller":!0,"@metamask/rpc-errors":!0,"@metamask/assets-controllers>@metamask/snaps-utils":!0,"@metamask/utils":!0,"@metamask/name-controller>async-mutex":!0,"bn.js":!0,lodash:!0,"@ensdomains/content-hash>multicodec>uint8arrays>multiformats":!0,"single-call-balance-checker-abi":!0,uuid:!0}},"@metamask/base-controller":{globals:{setTimeout:!0},packages:{immer:!0}},"@metamask/announcement-controller>@metamask/base-controller":{globals:{setTimeout:!0},packages:{immer:!0}},"@metamask/smart-transactions-controller>@metamask/polling-controller>@metamask/base-controller":{globals:{setTimeout:!0},packages:{immer:!0}},"@metamask/ppom-validator>@metamask/base-controller":{globals:{setTimeout:!0},packages:{immer:!0}},"@metamask/bridge-controller":{globals:{AbortController:!0,URLSearchParams:!0,"console.error":!0,"console.log":!0,"console.warn":!0},packages:{"ethers>@ethersproject/address":!0,"ethers>@ethersproject/constants":!0,"@ethersproject/contracts":!0,"@ethersproject/providers":!0,"@metamask/controller-utils":!0,"@metamask/keyring-api":!0,"@metamask/metamask-eth-abis":!0,"@metamask/bridge-controller>@metamask/multichain-network-controller":!0,"@metamask/bridge-controller>@metamask/polling-controller":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@metamask/bridge-controller>bignumber.js":!0,lodash:!0,reselect:!0,uuid:!0}},"@metamask/bridge-status-controller":{globals:{URLSearchParams:!0,"console.error":!0,"console.log":!0,setTimeout:!0},packages:{"@metamask/bridge-controller":!0,"@metamask/controller-utils":!0,"@metamask/keyring-api":!0,"@metamask/bridge-status-controller>@metamask/polling-controller":!0,"@metamask/superstruct":!0,"@metamask/transaction-controller":!0,"@metamask/utils":!0,"@metamask/bridge-status-controller>bignumber.js":!0,uuid:!0}},"@metamask/browser-passworder":{globals:{CryptoKey:!0,btoa:!0,"crypto.getRandomValues":!0,"crypto.subtle.decrypt":!0,"crypto.subtle.deriveKey":!0,"crypto.subtle.encrypt":!0,"crypto.subtle.exportKey":!0,"crypto.subtle.importKey":!0},packages:{"@metamask/browser-passworder>@metamask/utils":!0,"browserify>buffer":!0}},"eth-keyring-controller>@metamask/browser-passworder":{globals:{crypto:!0}},"@metamask/chain-agnostic-permission":{packages:{"@metamask/chain-agnostic-permission>@metamask/api-specs":!0,"@metamask/controller-utils":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0,lodash:!0}},"@metamask/multichain-api-middleware>@metamask/chain-agnostic-permission":{packages:{"@metamask/multichain-api-middleware>@metamask/api-specs":!0,"@metamask/controller-utils":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0,lodash:!0}},"@metamask/controller-utils":{globals:{URL:!0,"console.error":!0,fetch:!0,setTimeout:!0},packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/controller-utils>@metamask/ethjs-unit":!0,"@metamask/utils":!0,"@metamask/controller-utils>@spruceid/siwe-parser":!0,"bn.js":!0,"browserify>buffer":!0,cockatiel:!0,"eth-ens-namehash":!0,"eslint>fast-deep-equal":!0}},"@metamask/delegation-controller":{packages:{"@metamask/base-controller":!0,"@metamask/keyring-controller":!0,"@metamask/utils":!0}},"@metamask/ens-controller":{packages:{"@ethersproject/providers":!0,"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/utils":!0,punycode:!0}},"@metamask/eth-token-tracker>@metamask/eth-block-tracker":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/safe-event-emitter":!0,"@metamask/utils":!0,"@metamask/ppom-validator>json-rpc-random-id":!0}},"@metamask/network-controller>@metamask/eth-block-tracker":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/safe-event-emitter":!0,"@metamask/utils":!0,"@metamask/ppom-validator>json-rpc-random-id":!0}},"@metamask/keyring-controller>@metamask/eth-hd-keyring":{globals:{TextEncoder:!0},packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util":!0,"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/scure-bip39":!0,"@metamask/utils":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0}},"@metamask/eth-json-rpc-filters":{globals:{"console.error":!0},packages:{"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/json-rpc-engine":!0,"@metamask/safe-event-emitter":!0,"@metamask/name-controller>async-mutex":!0,pify:!0}},"@metamask/network-controller>@metamask/eth-json-rpc-infura":{globals:{fetch:!0,setTimeout:!0},packages:{"@metamask/eth-json-rpc-provider":!0,"@metamask/json-rpc-engine":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0}},"@metamask/eth-json-rpc-middleware":{globals:{URL:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util":!0,"@metamask/json-rpc-engine":!0,"@metamask/rpc-errors":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@metamask/eth-json-rpc-middleware>klona":!0,"@metamask/eth-json-rpc-middleware>safe-stable-stringify":!0}},"@metamask/eth-json-rpc-provider":{packages:{"@metamask/json-rpc-engine":!0,"@metamask/rpc-errors":!0,"@metamask/safe-event-emitter":!0,uuid:!0}},"@metamask/eth-ledger-bridge-keyring":{globals:{addEventListener:!0,"console.error":!0,"document.createElement":!0,"document.head.appendChild":!0,fetch:!0,removeEventListener:!0,setTimeout:!0},packages:{"@metamask/eth-ledger-bridge-keyring>@ethereumjs/rlp":!0,"@ethereumjs/tx":!0,"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth":!0,"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util":!0,"@metamask/utils":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0}},"@metamask/controller-utils>@metamask/eth-query":{packages:{"@metamask/ppom-validator>json-rpc-random-id":!0,"watchify>xtend":!0}},"@metamask/eth-sig-util":{packages:{"@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/abi-utils":!0,"@metamask/eth-sig-util>@metamask/utils":!0,"@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util":{packages:{"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@ethereumjs/rlp":!0,"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/eth-snap-keyring>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/eth-trezor-keyring>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/keyring-controller>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/keyring-controller>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/keyring-controller>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/keyring-controller>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/signature-controller>@metamask/eth-sig-util":{packages:{"@keystonehq/metamask-airgapped-keyring>@ethereumjs/rlp":!0,"@metamask/signature-controller>@metamask/eth-sig-util>@ethereumjs/util":!0,"@metamask/signature-controller>@metamask/eth-sig-util>@metamask/abi-utils":!0,"@metamask/utils":!0,"@metamask/signature-controller>@metamask/eth-sig-util>@scure/base":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,tweetnacl:!0}},"@metamask/keyring-controller>@metamask/eth-simple-keyring":{packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util":!0,"@metamask/utils":!0,"browserify>buffer":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"crypto-browserify>randombytes":!0}},"@metamask/eth-snap-keyring":{globals:{URL:!0,"console.error":!0,"console.info":!0,"console.warn":!0},packages:{"@ethereumjs/tx":!0,"@metamask/eth-snap-keyring>@metamask/eth-sig-util":!0,"@metamask/keyring-api":!0,"@metamask/keyring-internal-api":!0,"@metamask/keyring-internal-snap-client":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"webpack>events":!0,"@metamask/eth-snap-keyring>uuid":!0}},"@metamask/eth-token-tracker":{globals:{"console.warn":!0},packages:{"@babel/runtime":!0,"@ethersproject/bignumber":!0,"@ethersproject/contracts":!0,"@ethersproject/providers":!0,"@metamask/eth-token-tracker>@metamask/eth-block-tracker":!0,"@metamask/safe-event-emitter":!0,"bn.js":!0,"@metamask/eth-token-tracker>deep-equal":!0,"human-standard-token-abi":!0}},"@metamask/eth-trezor-keyring":{globals:{setTimeout:!0},packages:{"@ethereumjs/tx":!0,"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/eth-trezor-keyring>@metamask/eth-sig-util":!0,"@metamask/utils":!0,"@metamask/eth-trezor-keyring>@trezor/connect-plugin-ethereum":!0,"@trezor/connect-web":!0,"browserify>buffer":!0,"@metamask/eth-trezor-keyring>hdkey":!0}},"@metamask/etherscan-link":{globals:{URL:!0}},"eth-method-registry>@metamask/ethjs-contract":{packages:{"@babel/runtime":!0,"eth-method-registry>@metamask/ethjs-contract>@metamask/ethjs-filter":!0,"eth-method-registry>@metamask/ethjs-contract>@metamask/ethjs-util":!0,"eth-method-registry>@metamask/ethjs-contract>ethjs-abi":!0,"eth-ens-namehash>js-sha3":!0,"eth-method-registry>@metamask/ethjs-query>promise-to-callback":!0}},"eth-method-registry>@metamask/ethjs-contract>@metamask/ethjs-filter":{globals:{clearInterval:!0,setInterval:!0}},"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format":{packages:{"eth-method-registry>@metamask/ethjs-contract>@metamask/ethjs-util":!0,"@metamask/controller-utils>@metamask/ethjs-unit>@metamask/number-to-bn":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>ethjs-schema":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>strip-hex-prefix":!0}},"eth-method-registry>@metamask/ethjs-query":{globals:{console:!0},packages:{"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-rpc":!0,"eth-method-registry>@metamask/ethjs-query>promise-to-callback":!0}},"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-rpc":{packages:{"eth-method-registry>@metamask/ethjs-query>promise-to-callback":!0}},"@metamask/controller-utils>@metamask/ethjs-unit":{packages:{"@metamask/controller-utils>@metamask/ethjs-unit>@metamask/number-to-bn":!0,"bn.js":!0}},"eth-method-registry>@metamask/ethjs-contract>@metamask/ethjs-util":{packages:{"browserify>buffer":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>is-hex-prefixed":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>strip-hex-prefix":!0}},"@metamask/gas-fee-controller":{globals:{clearInterval:!0,"console.error":!0,setInterval:!0},packages:{"@metamask/controller-utils":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/assets-controllers>@metamask/polling-controller":!0,"bn.js":!0,uuid:!0}},"@metamask/jazzicon":{globals:{"document.createElement":!0,"document.createElementNS":!0},packages:{"@metamask/jazzicon>color":!0,"@metamask/jazzicon>mersenne-twister":!0}},"@metamask/json-rpc-engine":{packages:{"@metamask/rpc-errors":!0,"@metamask/safe-event-emitter":!0,"@metamask/utils":!0}},"@metamask/json-rpc-middleware-stream":{globals:{"console.warn":!0,setTimeout:!0},packages:{"@metamask/safe-event-emitter":!0,"@metamask/utils":!0,"readable-stream":!0}},"@metamask/snaps-sdk>@metamask/key-tree":{globals:{"crypto.subtle":!0},packages:{"@metamask/scure-bip39":!0,"@metamask/utils":!0,"viem>ox>@noble/curves":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0}},"@metamask/keyring-api":{packages:{"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"bitcoin-address-validation":!0}},"@metamask/multichain-transactions-controller>@metamask/keyring-api":{packages:{"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"bitcoin-address-validation":!0}},"@metamask/keyring-controller":{globals:{"console.error":!0},packages:{"@ethereumjs/tx>@ethereumjs/util":!0,"@metamask/base-controller":!0,"@metamask/browser-passworder":!0,"@metamask/keyring-controller>@metamask/eth-hd-keyring":!0,"@metamask/keyring-controller>@metamask/eth-sig-util":!0,"@metamask/keyring-controller>@metamask/eth-simple-keyring":!0,"@metamask/utils":!0,"@metamask/name-controller>async-mutex":!0,"@metamask/keyring-controller>ethereumjs-wallet":!0,lodash:!0,"@metamask/keyring-controller>ulid":!0}},"@metamask/keyring-internal-api":{packages:{"@metamask/keyring-api":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0}},"@metamask/keyring-internal-snap-client":{packages:{"@metamask/keyring-api":!0,"@metamask/keyring-internal-api":!0,"@metamask/keyring-snap-client":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0}},"@metamask/keyring-snap-client":{packages:{"@metamask/keyring-api":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0,"@metamask/keyring-snap-client>uuid":!0}},"@metamask/multichain-transactions-controller>@metamask/keyring-snap-client":{packages:{"@metamask/multichain-transactions-controller>@metamask/keyring-api":!0,"@metamask/keyring-api>@metamask/keyring-utils":!0,"@metamask/superstruct":!0,"@metamask/multichain-transactions-controller>@metamask/keyring-snap-client>uuid":!0}},"@metamask/keyring-api>@metamask/keyring-utils":{globals:{URL:!0},packages:{"@metamask/superstruct":!0,"@metamask/utils":!0,"bitcoin-address-validation":!0}},"@metamask/logging-controller":{packages:{"@metamask/base-controller":!0,uuid:!0}},"@metamask/logo":{globals:{addEventListener:!0,"document.body.appendChild":!0,"document.createElementNS":!0,innerHeight:!0,innerWidth:!0,requestAnimationFrame:!0},packages:{"@metamask/logo>gl-mat4":!0,"@metamask/logo>gl-vec3":!0}},"@metamask/message-manager":{packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/utils":!0,"browserify>buffer":!0,"webpack>events":!0,uuid:!0}},"@metamask/multichain-api-middleware":{globals:{"console.error":!0},packages:{"@metamask/multichain-api-middleware>@metamask/api-specs":!0,"@metamask/multichain-api-middleware>@metamask/chain-agnostic-permission":!0,"@metamask/controller-utils":!0,"@metamask/eth-json-rpc-filters":!0,"@metamask/json-rpc-engine":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/safe-event-emitter":!0,"@metamask/utils":!0,"@open-rpc/schema-utils-js":!0,"@metamask/message-manager>jsonschema":!0}},"@metamask/multichain-network-controller":{globals:{URL:!0},packages:{"@metamask/base-controller":!0,"@metamask/keyring-api":!0,"@metamask/network-controller":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@metamask/multichain-network-controller>@solana/addresses":!0,lodash:!0}},"@metamask/bridge-controller>@metamask/multichain-network-controller":{globals:{URL:!0},packages:{"@metamask/base-controller":!0,"@metamask/keyring-api":!0,"@metamask/network-controller":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@metamask/bridge-controller>@metamask/multichain-network-controller>@solana/addresses":!0,lodash:!0}},"@metamask/multichain-transactions-controller":{globals:{"console.error":!0},packages:{"@metamask/base-controller":!0,"@metamask/multichain-transactions-controller>@metamask/keyring-api":!0,"@metamask/multichain-transactions-controller>@metamask/keyring-snap-client":!0,"@metamask/multichain-transactions-controller>@metamask/snaps-utils":!0,"@metamask/utils":!0}},"@metamask/name-controller":{globals:{fetch:!0},packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/utils":!0,"@metamask/name-controller>async-mutex":!0}},"@metamask/network-controller":{globals:{URL:!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/network-controller>@metamask/eth-block-tracker":!0,"@metamask/network-controller>@metamask/eth-json-rpc-infura":!0,"@metamask/eth-json-rpc-middleware":!0,"@metamask/eth-json-rpc-provider":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/json-rpc-engine":!0,"@metamask/rpc-errors":!0,"@metamask/network-controller>@metamask/swappable-obj-proxy":!0,"@metamask/utils":!0,"addons-linter>deepmerge":!0,"eslint>fast-deep-equal":!0,immer:!0,lodash:!0,reselect:!0,"uri-js":!0,uuid:!0}},"@metamask/transaction-controller>@metamask/nonce-tracker":{packages:{"@ethersproject/providers":!0,"browserify>assert":!0,"@metamask/transaction-controller>@metamask/nonce-tracker>async-mutex":!0}},"@metamask/notification-services-controller":{globals:{"Intl.NumberFormat":!0,addEventListener:!0,fetch:!0,registration:!0,removeEventListener:!0},packages:{"@metamask/notification-services-controller>@contentful/rich-text-html-renderer":!0,"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/keyring-controller":!0,"@metamask/utils":!0,"@metamask/notification-services-controller>bignumber.js":!0,"@metamask/notification-services-controller>firebase":!0,loglevel:!0,uuid:!0}},"@metamask/controller-utils>@metamask/ethjs-unit>@metamask/number-to-bn":{packages:{"bn.js":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>strip-hex-prefix":!0}},"@metamask/object-multiplex":{globals:{"console.warn":!0},packages:{"@metamask/object-multiplex>once":!0,"readable-stream":!0}},"@metamask/obs-store":{packages:{"@metamask/safe-event-emitter":!0,"readable-stream":!0}},"@metamask/permission-controller":{globals:{"console.error":!0},packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/json-rpc-engine":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0,"deep-freeze-strict":!0,immer:!0,nanoid:!0}},"@metamask/permission-log-controller":{packages:{"@metamask/base-controller":!0,"@metamask/utils":!0}},"@metamask/phishing-controller":{globals:{TextEncoder:!0,URL:!0,"console.error":!0,fetch:!0},packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@noble/hashes":!0,"@ethereumjs/tx>ethereum-cryptography":!0,"webpack-cli>fastest-levenshtein":!0,punycode:!0}},"@metamask/assets-controllers>@metamask/polling-controller":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,uuid:!0}},"@metamask/bridge-controller>@metamask/polling-controller":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,uuid:!0}},"@metamask/bridge-status-controller>@metamask/polling-controller":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,uuid:!0}},"@metamask/smart-transactions-controller>@metamask/polling-controller":{globals:{clearTimeout:!0,"console.error":!0,setTimeout:!0},packages:{"@metamask/smart-transactions-controller>@metamask/polling-controller>@metamask/base-controller":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,uuid:!0}},"@metamask/post-message-stream":{globals:{"MessageEvent.prototype":!0,WorkerGlobalScope:!0,addEventListener:!0,browser:!0,chrome:!0,"location.origin":!0,postMessage:!0,removeEventListener:!0},packages:{"@metamask/utils":!0,"readable-stream":!0}},"@metamask/ppom-validator":{globals:{URL:!0,"console.error":!0,crypto:!0},packages:{"@metamask/ppom-validator>@metamask/base-controller":!0,"@metamask/controller-utils":!0,"await-semaphore":!0,"browserify>buffer":!0,"@metamask/ppom-validator>crypto-js":!0,"@metamask/ppom-validator>elliptic":!0,"@metamask/ppom-validator>json-rpc-random-id":!0}},"@metamask/preferences-controller":{packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0}},"@metamask/profile-sync-controller":{globals:{Event:!0,Headers:!0,TextDecoder:!0,TextEncoder:!0,URL:!0,URLSearchParams:!0,addEventListener:!0,"console.error":!0,"console.warn":!0,dispatchEvent:!0,fetch:!0,removeEventListener:!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/keyring-controller":!0,"@metamask/network-controller":!0,"@metamask/profile-sync-controller>@noble/ciphers":!0,"@noble/hashes":!0,"browserify>buffer":!0,loglevel:!0,"@metamask/profile-sync-controller>siwe":!0}},"@metamask/providers":{globals:{CustomEvent:!0,Event:!0,addEventListener:!0,"chrome.runtime.connect":!0,console:!0,dispatchEvent:!0,"document.createElement":!0,"document.readyState":!0,ethereum:"write","location.hostname":!0,removeEventListener:!0,web3:!0},packages:{"@metamask/json-rpc-engine":!0,"@metamask/json-rpc-middleware-stream":!0,"@metamask/object-multiplex":!0,"@metamask/rpc-errors":!0,"@metamask/safe-event-emitter":!0,"@metamask/utils":!0,"@metamask/providers>detect-browser":!0,"@metamask/providers>extension-port-stream":!0,"eslint>fast-deep-equal":!0,"@metamask/providers>is-stream":!0,"readable-stream":!0}},"@metamask/rate-limit-controller":{globals:{setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0}},"@metamask/remote-feature-flag-controller":{packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,uuid:!0}},"@metamask/rpc-errors":{packages:{"@metamask/utils":!0,"@metamask/rpc-errors>fast-safe-stringify":!0}},"@metamask/safe-event-emitter":{globals:{setTimeout:!0},packages:{"webpack>events":!0}},"@metamask/scure-bip39":{globals:{TextEncoder:!0},packages:{"@metamask/scure-bip39>@noble/hashes":!0,"@metamask/scure-bip39>@scure/base":!0}},"@metamask/selected-network-controller":{packages:{"@metamask/base-controller":!0,"@metamask/network-controller>@metamask/swappable-obj-proxy":!0}},"@metamask/signature-controller":{globals:{fetch:!0},packages:{"@metamask/approval-controller":!0,"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/signature-controller>@metamask/eth-sig-util":!0,"@metamask/keyring-controller":!0,"@metamask/logging-controller":!0,"@metamask/utils":!0,"browserify>buffer":!0,"webpack>events":!0,"@metamask/message-manager>jsonschema":!0,uuid:!0}},"@metamask/smart-transactions-controller":{globals:{URLSearchParams:!0,clearInterval:!0,"console.error":!0,"console.log":!0,fetch:!0,setInterval:!0},packages:{"@ethereumjs/tx":!0,"@ethereumjs/tx>@ethereumjs/util":!0,"@ethersproject/bytes":!0,"@metamask/controller-utils":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/smart-transactions-controller>@metamask/polling-controller":!0,"@metamask/transaction-controller":!0,"@metamask/smart-transactions-controller>bignumber.js":!0,"browserify>buffer":!0,"fast-json-patch":!0,lodash:!0}},"@metamask/snaps-controllers":{globals:{DecompressionStream:!0,URL:!0,WebSocket:!0,clearTimeout:!0,"document.getElementById":!0,"fetch.bind":!0,setTimeout:!0},packages:{"@metamask/base-controller":!0,"@metamask/json-rpc-engine":!0,"@metamask/json-rpc-middleware-stream":!0,"@metamask/object-multiplex":!0,"@metamask/permission-controller":!0,"@metamask/post-message-stream":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-utils>@metamask/snaps-registry":!0,"@metamask/snaps-rpc-methods":!0,"@metamask/snaps-sdk":!0,"@metamask/snaps-utils":!0,"@metamask/utils":!0,"@metamask/snaps-controllers>@xstate/fsm":!0,"@metamask/name-controller>async-mutex":!0,"@metamask/snaps-controllers>concat-stream":!0,"cron-parser":!0,"eslint>fast-deep-equal":!0,"@metamask/snaps-controllers>get-npm-tarball-url":!0,immer:!0,luxon:!0,nanoid:!0,"readable-stream":!0,"@metamask/snaps-controllers>readable-web-to-node-stream":!0,semver:!0,"@metamask/snaps-controllers>tar-stream":!0}},"@metamask/snaps-execution-environments":{globals:{"document.getElementById":!0},packages:{"@metamask/post-message-stream":!0,"@metamask/snaps-utils":!0,"@metamask/utils":!0}},"@metamask/snaps-utils>@metamask/snaps-registry":{packages:{"@metamask/superstruct":!0,"@metamask/utils":!0,"viem>ox>@noble/curves":!0,"@noble/hashes":!0}},"@metamask/snaps-rpc-methods":{packages:{"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-sdk":!0,"@metamask/snaps-utils":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@noble/hashes":!0}},"@metamask/snaps-sdk":{globals:{URL:!0,fetch:!0},packages:{"@metamask/rpc-errors":!0,"@metamask/superstruct":!0,"@metamask/utils":!0}},"@metamask/snaps-utils":{globals:{File:!0,FileReader:!0,TextDecoder:!0,URL:!0,"console.error":!0,"console.log":!0,"console.warn":!0,crypto:!0,"document.body.appendChild":!0,"document.createElement":!0,fetch:!0},packages:{"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-utils>@metamask/slip44":!0,"@metamask/snaps-sdk":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,chalk:!0,"cron-parser":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,"@metamask/snaps-utils>fast-xml-parser":!0,luxon:!0,"@metamask/snaps-utils>marked":!0,"@metamask/snaps-utils>rfdc":!0,semver:!0,"@metamask/snaps-utils>validate-npm-package-name":!0}},"@metamask/account-tree-controller>@metamask/snaps-utils":{globals:{File:!0,FileReader:!0,TextDecoder:!0,URL:!0,"console.error":!0,"console.log":!0,"console.warn":!0,crypto:!0,"document.body.appendChild":!0,"document.createElement":!0,fetch:!0},packages:{"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-utils>@metamask/slip44":!0,"@metamask/snaps-sdk":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,chalk:!0,"cron-parser":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,"@metamask/snaps-utils>fast-xml-parser":!0,luxon:!0,"@metamask/snaps-utils>marked":!0,"@metamask/snaps-utils>rfdc":!0,semver:!0,"@metamask/snaps-utils>validate-npm-package-name":!0}},"@metamask/assets-controllers>@metamask/snaps-utils":{globals:{File:!0,FileReader:!0,TextDecoder:!0,URL:!0,"console.error":!0,"console.log":!0,"console.warn":!0,crypto:!0,"document.body.appendChild":!0,"document.createElement":!0,fetch:!0},packages:{"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-utils>@metamask/slip44":!0,"@metamask/snaps-sdk":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,chalk:!0,"cron-parser":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,"@metamask/snaps-utils>fast-xml-parser":!0,luxon:!0,"@metamask/snaps-utils>marked":!0,"@metamask/snaps-utils>rfdc":!0,semver:!0,"@metamask/snaps-utils>validate-npm-package-name":!0}},"@metamask/multichain-transactions-controller>@metamask/snaps-utils":{globals:{File:!0,FileReader:!0,TextDecoder:!0,TextEncoder:!0,URL:!0,"console.error":!0,"console.log":!0,"console.warn":!0,crypto:!0,"document.body.appendChild":!0,"document.createElement":!0,fetch:!0},packages:{"@metamask/snaps-sdk>@metamask/key-tree":!0,"@metamask/permission-controller":!0,"@metamask/rpc-errors":!0,"@metamask/snaps-utils>@metamask/slip44":!0,"@metamask/snaps-sdk":!0,"@metamask/superstruct":!0,"@metamask/utils":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,chalk:!0,"cron-parser":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,"@metamask/snaps-utils>fast-xml-parser":!0,"@metamask/snaps-utils>marked":!0,"@metamask/snaps-utils>rfdc":!0,semver:!0,"@metamask/snaps-utils>validate-npm-package-name":!0}},"@metamask/transaction-controller":{globals:{clearTimeout:!0,"console.error":!0,fetch:!0,setTimeout:!0},packages:{"@ethereumjs/tx>@ethereumjs/common":!0,"@ethereumjs/tx":!0,"@ethersproject/abi":!0,"@ethersproject/contracts":!0,"@ethersproject/providers":!0,"@ethersproject/wallet":!0,"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/gas-fee-controller":!0,"@metamask/metamask-eth-abis":!0,"@metamask/network-controller":!0,"@metamask/transaction-controller>@metamask/nonce-tracker":!0,"@metamask/rpc-errors":!0,"@metamask/utils":!0,"@metamask/name-controller>async-mutex":!0,"bn.js":!0,"browserify>buffer":!0,"eth-method-registry":!0,"webpack>events":!0,"fast-json-patch":!0,lodash:!0,uuid:!0}},"@metamask/user-operation-controller":{globals:{fetch:!0},packages:{"@metamask/base-controller":!0,"@metamask/controller-utils":!0,"@metamask/controller-utils>@metamask/eth-query":!0,"@metamask/gas-fee-controller":!0,"@metamask/assets-controllers>@metamask/polling-controller":!0,"@metamask/rpc-errors":!0,"@metamask/superstruct":!0,"@metamask/transaction-controller":!0,"@metamask/utils":!0,"bn.js":!0,"webpack>events":!0,lodash:!0,uuid:!0}},"@metamask/utils":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@metamask/superstruct":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,"browserify>buffer":!0,"nock>debug":!0,"@metamask/utils>pony-cause":!0,semver:!0}},"@metamask/abi-utils>@metamask/utils":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@metamask/superstruct":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,"browserify>buffer":!0,"nock>debug":!0,"@metamask/utils>pony-cause":!0,semver:!0}},"@metamask/browser-passworder>@metamask/utils":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@metamask/superstruct":!0,"@noble/hashes":!0,"@metamask/utils>@scure/base":!0,"browserify>buffer":!0,"nock>debug":!0,"@metamask/utils>pony-cause":!0,semver:!0}},"@metamask/eth-sig-util>@metamask/utils":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@metamask/superstruct":!0,"@noble/hashes":!0,"@metamask/eth-sig-util>@metamask/utils>@scure/base":!0,"browserify>buffer":!0,"nock>debug":!0,"@metamask/utils>pony-cause":!0,semver:!0}},"@ngraveio/bc-ur":{packages:{"@ngraveio/bc-ur>@keystonehq/alias-sampling":!0,"browserify>assert":!0,"@ngraveio/bc-ur>bignumber.js":!0,"browserify>buffer":!0,"@ngraveio/bc-ur>cbor-sync":!0,"@ngraveio/bc-ur>crc":!0,"@ngraveio/bc-ur>jsbi":!0,"addons-linter>sha.js":!0}},"@metamask/profile-sync-controller>@noble/ciphers":{globals:{TextDecoder:!0,TextEncoder:!0,crypto:!0}},"@ethereumjs/tx>ethereum-cryptography>@noble/curves":{globals:{TextEncoder:!0},packages:{"@ethereumjs/tx>ethereum-cryptography>@noble/hashes":!0}},"viem>ox>@noble/curves":{globals:{TextEncoder:!0},packages:{"@noble/hashes":!0}},"@noble/hashes":{globals:{TextDecoder:!0,TextEncoder:!0,crypto:!0}},"@metamask/scure-bip39>@noble/hashes":{globals:{TextEncoder:!0,crypto:!0}},"@ethereumjs/tx>ethereum-cryptography>@noble/hashes":{globals:{TextEncoder:!0,crypto:!0}},"@open-rpc/schema-utils-js":{packages:{"@open-rpc/schema-utils-js>@json-schema-tools/dereferencer":!0,"@open-rpc/schema-utils-js>@json-schema-tools/meta-schema":!0,"@open-rpc/schema-utils-js>@json-schema-tools/reference-resolver":!0,"@open-rpc/meta-schema":!0,"eslint>ajv":!0,"@metamask/rpc-errors>fast-safe-stringify":!0,"@open-rpc/schema-utils-js>is-url":!0}},"@popperjs/core":{globals:{Element:!0,HTMLElement:!0,ShadowRoot:!0,"console.error":!0,"console.warn":!0,document:!0,"navigator.userAgent":!0}},"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/codegen":{globals:{"console.log":!0}},"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/fetch":{globals:{XMLHttpRequest:!0},packages:{"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/aspromise":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/inquire":!0}},"@reduxjs/toolkit":{globals:{AbortController:!0,__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:!0,__REDUX_DEVTOOLS_EXTENSION__:!0,console:!0,queueMicrotask:!0,requestAnimationFrame:!0,setTimeout:!0},packages:{immer:!0,process:!0,redux:!0,"redux-thunk":!0,"@reduxjs/toolkit>reselect":!0}},"react-router-dom-v5-compat>@remix-run/router":{globals:{AbortController:!0,DOMException:!0,FormData:!0,Headers:!0,Request:!0,Response:!0,URL:!0,URLSearchParams:!0,console:!0,"document.defaultView":!0}},"@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/keyring-controller>@metamask/eth-hd-keyring>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/eth-json-rpc-middleware>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/eth-ledger-bridge-keyring>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/keyring-controller>@metamask/eth-simple-keyring>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/eth-snap-keyring>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/eth-trezor-keyring>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/keyring-controller>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/signature-controller>@metamask/eth-sig-util>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/scure-bip39>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/utils>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@metamask/eth-sig-util>@metamask/utils>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@ethereumjs/tx>ethereum-cryptography>@scure/bip32>@scure/base":{globals:{TextDecoder:!0,TextEncoder:!0}},"@ethereumjs/tx>ethereum-cryptography>@scure/bip32":{packages:{"@ethereumjs/tx>ethereum-cryptography>@noble/curves":!0,"@ethereumjs/tx>ethereum-cryptography>@noble/hashes":!0,"@ethereumjs/tx>ethereum-cryptography>@scure/bip32>@scure/base":!0}},"@segment/loosely-validate-event":{packages:{"browserify>assert":!0,"browserify>buffer":!0,"@segment/loosely-validate-event>component-type":!0,"@segment/loosely-validate-event>join-component":!0}},"@sentry/browser>@sentry-internal/browser-utils":{globals:{"PerformanceEventTiming.prototype":!0,PerformanceObserver:!0,"XMLHttpRequest.prototype":!0,__SENTRY_DEBUG__:!0,addEventListener:!0,clearTimeout:!0,performance:!0,removeEventListener:!0,setTimeout:!0},packages:{"@sentry/browser>@sentry/core":!0,"@sentry/utils":!0}},"@sentry/browser>@sentry-internal/feedback":{globals:{FormData:!0,HTMLFormElement:!0,__SENTRY_DEBUG__:!0,cancelAnimationFrame:!0,clearTimeout:!0,"document.createElement":!0,"document.createElementNS":!0,"document.createTextNode":!0,isSecureContext:!0,requestAnimationFrame:!0,setTimeout:!0},packages:{"@sentry/browser>@sentry/core":!0,"@sentry/utils":!0}},"@sentry/browser>@sentry-internal/replay-canvas":{globals:{Blob:!0,HTMLCanvasElement:!0,HTMLImageElement:!0,ImageData:!0,"URL.createObjectURL":!0,WeakRef:!0,Worker:!0,cancelAnimationFrame:!0,"console.error":!0,createImageBitmap:!0,document:!0},packages:{"@sentry/browser>@sentry/core":!0,"@sentry/utils":!0}},"@sentry/browser>@sentry-internal/replay":{globals:{Blob:!0,CSSConditionRule:!0,CSSGroupingRule:!0,CSSMediaRule:!0,CSSRule:!0,CSSSupportsRule:!0,Document:!0,DragEvent:!0,Element:!0,FormData:!0,HTMLElement:!0,HTMLFormElement:!0,Headers:!0,MouseEvent:!0,MutationObserver:!0,"Node.DOCUMENT_FRAGMENT_NODE":!0,"Node.prototype.contains":!0,PointerEvent:!0,TextEncoder:!0,URL:!0,URLSearchParams:!0,Worker:!0,__RRWEB_EXCLUDE_IFRAME__:!0,__RRWEB_EXCLUDE_SHADOW_DOM__:!0,__SENTRY_DEBUG__:!0,__SENTRY_EXCLUDE_REPLAY_WORKER__:!0,__rrMutationObserver:!0,addEventListener:!0,clearTimeout:!0,"console.debug":!0,"console.error":!0,"console.warn":!0,"customElements.get":!0,document:!0,innerHeight:!0,innerWidth:!0,"location.href":!0,"location.origin":!0,parent:!0,setTimeout:!0},packages:{"@sentry/browser>@sentry-internal/browser-utils":!0,"@sentry/browser>@sentry/core":!0,"@sentry/utils":!0}},"@sentry/browser":{globals:{"PerformanceObserver.supportedEntryTypes":!0,Request:!0,URL:!0,"XMLHttpRequest.prototype":!0,__SENTRY_DEBUG__:!0,__SENTRY_RELEASE__:!0,addEventListener:!0,"console.error":!0,"indexedDB.open":!0,"performance.timeOrigin":!0,setTimeout:!0},packages:{"@sentry/browser>@sentry-internal/browser-utils":!0,"@sentry/browser>@sentry-internal/feedback":!0,"@sentry/browser>@sentry-internal/replay-canvas":!0,"@sentry/browser>@sentry-internal/replay":!0,"@sentry/browser>@sentry/core":!0,"@sentry/utils":!0}},"@sentry/browser>@sentry/core":{globals:{Headers:!0,Request:!0,URL:!0,__SENTRY_DEBUG__:!0,__SENTRY_TRACING__:!0,clearInterval:!0,clearTimeout:!0,"console.log":!0,"console.warn":!0,setInterval:!0,setTimeout:!0},packages:{"@sentry/utils":!0}},"@sentry/utils":{globals:{CustomEvent:!0,DOMError:!0,DOMException:!0,EdgeRuntime:!0,Element:!0,ErrorEvent:!0,Event:!0,HTMLElement:!0,Headers:!0,Request:!0,Response:!0,TextDecoder:!0,TextEncoder:!0,URL:!0,__SENTRY_BROWSER_BUNDLE__:!0,__SENTRY_DEBUG__:!0,clearTimeout:!0,"console.error":!0,document:!0,setInterval:!0,setTimeout:!0},packages:{process:!0}},"@solana/addresses":{globals:{"Intl.Collator":!0,TextEncoder:!0,"crypto.subtle.digest":!0,"crypto.subtle.exportKey":!0},packages:{"@solana/addresses>@solana/assertions":!0,"@solana/addresses>@solana/codecs-core":!0,"@solana/addresses>@solana/codecs-strings":!0,"@solana/addresses>@solana/errors":!0}},"@metamask/multichain-network-controller>@solana/addresses":{globals:{"Intl.Collator":!0,TextEncoder:!0,"crypto.subtle.digest":!0,"crypto.subtle.exportKey":!0},packages:{"@metamask/multichain-network-controller>@solana/addresses>@solana/assertions":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-core":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-strings":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":!0}},"@metamask/bridge-controller>@metamask/multichain-network-controller>@solana/addresses":{globals:{"Intl.Collator":!0,TextEncoder:!0,"crypto.subtle.digest":!0,"crypto.subtle.exportKey":!0},packages:{"@metamask/multichain-network-controller>@solana/addresses>@solana/assertions":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-core":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-strings":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":!0}},"@solana/addresses>@solana/assertions":{globals:{crypto:!0,isSecureContext:!0},packages:{"@solana/addresses>@solana/errors":!0}},"@metamask/multichain-network-controller>@solana/addresses>@solana/assertions":{globals:{crypto:!0,isSecureContext:!0},packages:{"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":!0}},"@solana/addresses>@solana/codecs-core":{packages:{"@solana/addresses>@solana/errors":!0}},"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-core":{packages:{"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":!0}},"@solana/addresses>@solana/codecs-strings":{globals:{TextDecoder:!0,TextEncoder:!0,atob:!0,btoa:!0},packages:{"@solana/addresses>@solana/codecs-core":!0,"@solana/addresses>@solana/errors":!0}},"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-strings":{globals:{TextDecoder:!0,TextEncoder:!0,atob:!0,btoa:!0},packages:{"@metamask/multichain-network-controller>@solana/addresses>@solana/codecs-core":!0,"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":!0}},"@solana/addresses>@solana/errors":{globals:{btoa:!0}},"@metamask/multichain-network-controller>@solana/addresses>@solana/errors":{globals:{btoa:!0}},"@metamask/controller-utils>@spruceid/siwe-parser":{globals:{"console.error":!0,"console.log":!0},packages:{"@noble/hashes":!0,"@metamask/controller-utils>@spruceid/siwe-parser>apg-js":!0}},"@metamask/profile-sync-controller>siwe>@spruceid/siwe-parser":{globals:{"console.error":!0,"console.log":!0},packages:{"@noble/hashes":!0,"@metamask/controller-utils>@spruceid/siwe-parser>apg-js":!0}},"@metamask/profile-sync-controller>siwe>@stablelib/random>@stablelib/binary":{packages:{"@metamask/profile-sync-controller>siwe>@stablelib/random>@stablelib/binary>@stablelib/int":!0}},"@metamask/profile-sync-controller>siwe>@stablelib/random":{globals:{crypto:!0,msCrypto:!0},packages:{"@metamask/profile-sync-controller>siwe>@stablelib/random>@stablelib/binary":!0,"@metamask/profile-sync-controller>siwe>@stablelib/random>@stablelib/wipe":!0,"browserify>browser-resolve":!0}},"@trezor/connect-web>@trezor/connect-common":{globals:{"console.warn":!0,"localStorage.getItem":!0,"localStorage.setItem":!0,navigator:!0,setTimeout:!0,window:!0},packages:{"@trezor/connect-web>@trezor/connect-common>@trezor/env-utils":!0,"@trezor/connect-web>@trezor/utils":!0,tslib:!0}},"@metamask/eth-trezor-keyring>@trezor/connect-plugin-ethereum":{packages:{"@metamask/eth-trezor-keyring>@metamask/eth-sig-util":!0,tslib:!0}},"@trezor/connect-web":{globals:{URLSearchParams:!0,WebSocket:!0,__TREZOR_CONNECT_SRC:!0,addEventListener:!0,btoa:!0,chrome:!0,clearInterval:!0,clearTimeout:!0,"console.error":!0,"console.warn":!0,"document.body":!0,"document.createElement":!0,"document.createTextNode":!0,"document.getElementById":!0,"document.querySelectorAll":!0,location:!0,navigator:!0,open:!0,origin:!0,removeEventListener:!0,setInterval:!0,setTimeout:!0},packages:{"@trezor/connect-web>@trezor/connect-common":!0,"@trezor/connect-web>@trezor/connect":!0,"@trezor/connect-web>@trezor/utils":!0,"webpack>events":!0,tslib:!0}},"@trezor/connect-web>@trezor/connect":{packages:{"@trezor/connect-web>@trezor/connect>@trezor/protobuf":!0,"@trezor/connect-web>@trezor/connect>@trezor/schema-utils":!0,"@trezor/connect-web>@trezor/connect>@trezor/transport":!0,"@trezor/connect-web>@trezor/utils":!0,tslib:!0}},"@trezor/connect-web>@trezor/connect-common>@trezor/env-utils":{globals:{innerHeight:!0,innerWidth:!0,"location.hostname":!0,"location.origin":!0,"navigator.languages":!0,"navigator.platform":!0,"navigator.userAgent":!0,"screen.height":!0,"screen.width":!0},packages:{process:!0,tslib:!0,"@trezor/connect-web>@trezor/connect-common>@trezor/env-utils>ua-parser-js":!0}},"@trezor/connect-web>@trezor/connect>@trezor/protobuf":{packages:{"@trezor/connect-web>@trezor/connect>@trezor/schema-utils":!0,"browserify>buffer":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs":!0,tslib:!0}},"@trezor/connect-web>@trezor/connect>@trezor/schema-utils":{globals:{"console.warn":!0},packages:{"@trezor/connect-web>@trezor/connect>@trezor/schema-utils>@sinclair/typebox":!0,"browserify>buffer":!0,"ts-mixer":!0}},"@trezor/connect-web>@trezor/utils":{globals:{AbortController:!0,"Intl.NumberFormat":!0,clearInterval:!0,clearTimeout:!0,"console.error":!0,"console.info":!0,"console.log":!0,"console.warn":!0,"crypto.getRandomValues":!0,setInterval:!0,setTimeout:!0},packages:{"@trezor/connect-web>@trezor/utils>bignumber.js":!0,"browserify>browser-resolve":!0,"browserify>buffer":!0,"webpack>events":!0,tslib:!0}},"@welldone-software/why-did-you-render":{globals:{Element:!0,"console.group":!0,"console.groupCollapsed":!0,"console.groupEnd":!0,"console.log":!0,"console.warn":!0,define:!0,setTimeout:!0},packages:{lodash:!0,react:!0}},"@zxing/browser":{globals:{HTMLElement:!0,HTMLImageElement:!0,HTMLVideoElement:!0,clearTimeout:!0,"console.error":!0,"console.warn":!0,document:!0,navigator:!0,setTimeout:!0},packages:{"@zxing/library":!0}},"@zxing/library":{globals:{HTMLImageElement:!0,HTMLVideoElement:!0,TextDecoder:!0,TextEncoder:!0,"URL.createObjectURL":!0,btoa:!0,"console.log":!0,"console.warn":!0,document:!0,navigator:!0,setTimeout:!0},packages:{"@zxing/library>ts-custom-error":!0}},"@lavamoat/lavapack>readable-stream>abort-controller":{globals:{AbortController:!0}},"currency-formatter>accounting":{globals:{define:!0}},"ethers>@ethersproject/json-wallets>aes-js":{globals:{define:!0}},"eth-lattice-keyring>gridplus-sdk>aes-js":{globals:{define:!0}},"eslint>ajv":{globals:{console:!0},packages:{"eslint>fast-deep-equal":!0,"@metamask/snaps-utils>fast-json-stable-stringify":!0,"eslint>ajv>json-schema-traverse":!0,"uri-js":!0}},"chalk>ansi-styles":{packages:{"chalk>ansi-styles>color-convert":!0}},"@metamask/controller-utils>@spruceid/siwe-parser>apg-js":{packages:{"browserify>buffer":!0}},"string.prototype.matchall>es-abstract>array-buffer-byte-length":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>es-abstract>is-array-buffer":!0}},"crypto-browserify>public-encrypt>parse-asn1>asn1.js":{packages:{"bn.js":!0,"browserify>buffer":!0,"pumpify>inherits":!0,"@metamask/ppom-validator>elliptic>minimalistic-assert":!0,"browserify>vm-browserify":!0}},"browserify>assert":{globals:{Buffer:!0},packages:{"react>object-assign":!0,"browserify>assert>util":!0}},"@metamask/name-controller>async-mutex":{globals:{clearTimeout:!0,setTimeout:!0},packages:{tslib:!0}},"@metamask/transaction-controller>@metamask/nonce-tracker>async-mutex":{globals:{clearTimeout:!0,setTimeout:!0},packages:{tslib:!0}},"string.prototype.matchall>es-abstract>available-typed-arrays":{packages:{"string.prototype.matchall>es-abstract>typed-array-length>possible-typed-array-names":!0}},"await-semaphore":{packages:{process:!0,"browserify>timers-browserify":!0}},axios:{globals:{AbortController:!0,Blob:!0,FormData:!0,ReadableStream:!0,Request:!0,Response:!0,TextEncoder:!0,URL:!0,URLSearchParams:!0,WorkerGlobalScope:!0,XMLHttpRequest:!0,btoa:!0,clearTimeout:!0,"console.warn":!0,document:!0,fetch:!0,importScripts:!0,"location.href":!0,navigator:!0,queueMicrotask:!0,setTimeout:!0},packages:{"browserify>buffer":!0,process:!0,"browserify>timers-browserify":!0}},"@metamask/snaps-controllers>tar-stream>b4a":{globals:{TextDecoder:!0,TextEncoder:!0}},"@ensdomains/content-hash>multihashes>multibase>base-x":{packages:{"koa>content-disposition>safe-buffer":!0}},"base32-encode":{packages:{"base32-encode>to-data-view":!0}},"bignumber.js":{globals:{crypto:!0,define:!0}},"@metamask/eth-ledger-bridge-keyring>@ledgerhq/hw-app-eth>bignumber.js":{globals:{crypto:!0,define:!0}},"@metamask/bridge-controller>bignumber.js":{globals:{crypto:!0,define:!0}},"@metamask/bridge-status-controller>bignumber.js":{globals:{crypto:!0,define:!0}},"@metamask/notification-services-controller>bignumber.js":{globals:{crypto:!0,define:!0}},"@metamask/smart-transactions-controller>bignumber.js":{globals:{crypto:!0,define:!0}},"@ngraveio/bc-ur>bignumber.js":{globals:{crypto:!0,define:!0}},"@trezor/connect-web>@trezor/utils>bignumber.js":{globals:{crypto:!0,define:!0}},"eth-lattice-keyring>gridplus-sdk>borc>bignumber.js":{globals:{crypto:!0,define:!0}},"eth-lattice-keyring>gridplus-sdk>bignumber.js":{globals:{crypto:!0,define:!0}},"eth-lattice-keyring>gridplus-sdk>bitwise":{packages:{"browserify>buffer":!0}},blo:{globals:{btoa:!0}},"bn.js":{globals:{Buffer:!0},packages:{"browserify>browser-resolve":!0}},"eth-lattice-keyring>gridplus-sdk>borc":{globals:{console:!0},packages:{"eth-lattice-keyring>gridplus-sdk>borc>bignumber.js":!0,"browserify>buffer":!0,"buffer>ieee754":!0,"eth-lattice-keyring>gridplus-sdk>borc>iso-url":!0}},bowser:{globals:{define:!0}},"@metamask/ppom-validator>elliptic>brorand":{globals:{crypto:!0,msCrypto:!0},packages:{"browserify>browser-resolve":!0}},"ethereumjs-util>ethereum-cryptography>browserify-aes":{packages:{"ethereumjs-util>ethereum-cryptography>browserify-aes>buffer-xor":!0,"browserify>buffer":!0,"ethereumjs-util>create-hash>cipher-base":!0,"crypto-browserify>browserify-cipher>evp_bytestokey":!0,"pumpify>inherits":!0,"koa>content-disposition>safe-buffer":!0}},"crypto-browserify>browserify-cipher":{packages:{"ethereumjs-util>ethereum-cryptography>browserify-aes":!0,"crypto-browserify>browserify-cipher>browserify-des":!0,"crypto-browserify>browserify-cipher>evp_bytestokey":!0}},"crypto-browserify>browserify-cipher>browserify-des":{packages:{"browserify>buffer":!0,"ethereumjs-util>create-hash>cipher-base":!0,"crypto-browserify>browserify-cipher>browserify-des>des.js":!0,"pumpify>inherits":!0}},"crypto-browserify>public-encrypt>browserify-rsa":{packages:{"bn.js":!0,"crypto-browserify>randombytes":!0,"koa>content-disposition>safe-buffer":!0}},"crypto-browserify>browserify-sign":{packages:{"bn.js":!0,"crypto-browserify>public-encrypt>browserify-rsa":!0,"ethereumjs-util>create-hash":!0,"crypto-browserify>create-hmac":!0,"@metamask/ppom-validator>elliptic":!0,"pumpify>inherits":!0,"crypto-browserify>public-encrypt>parse-asn1":!0,"crypto-browserify>browserify-sign>readable-stream":!0,"koa>content-disposition>safe-buffer":!0}},"browserify>browserify-zlib":{packages:{"browserify>assert":!0,"browserify>buffer":!0,"browserify>browserify-zlib>pako":!0,process:!0,"stream-browserify":!0,"browserify>util":!0}},"ethereumjs-util>ethereum-cryptography>bs58check>bs58":{packages:{"@ensdomains/content-hash>multihashes>multibase>base-x":!0}},"ethereumjs-util>ethereum-cryptography>bs58check":{packages:{"ethereumjs-util>ethereum-cryptography>bs58check>bs58":!0,"ethereumjs-util>create-hash":!0,"koa>content-disposition>safe-buffer":!0}},buffer:{globals:{console:!0},packages:{"base64-js":!0,"buffer>ieee754":!0}},"terser>source-map-support>buffer-from":{packages:{"browserify>buffer":!0}},"ethereumjs-util>ethereum-cryptography>browserify-aes>buffer-xor":{packages:{"browserify>buffer":!0}},"browserify>buffer":{globals:{console:!0},packages:{"base64-js":!0,"buffer>ieee754":!0}},"@metamask/snaps-utils>validate-npm-package-name>builtins":{packages:{process:!0,semver:!0}},"string.prototype.matchall>call-bind>call-bind-apply-helpers":{packages:{"string.prototype.matchall>es-abstract>es-errors":!0,"browserify>has>function-bind":!0}},"string.prototype.matchall>call-bind":{packages:{"string.prototype.matchall>call-bind>call-bind-apply-helpers":!0,"string.prototype.matchall>call-bind>es-define-property":!0,"string.prototype.matchall>get-intrinsic":!0,"string.prototype.matchall>call-bind>set-function-length":!0}},"browserify>util>which-typed-array>call-bound":{packages:{"string.prototype.matchall>call-bind>call-bind-apply-helpers":!0,"string.prototype.matchall>get-intrinsic":!0}},"@ngraveio/bc-ur>cbor-sync":{globals:{define:!0},packages:{"browserify>buffer":!0}},chalk:{packages:{"chalk>ansi-styles":!0,"chalk>supports-color":!0}},"chart.js":{globals:{"Intl.NumberFormat":!0,MutationObserver:!0,OffscreenCanvas:!0,Path2D:!0,ResizeObserver:!0,addEventListener:!0,clearTimeout:!0,"console.error":!0,"console.warn":!0,devicePixelRatio:!0,document:!0,removeEventListener:!0,requestAnimationFrame:!0,setTimeout:!0},packages:{"chart.js>@kurkle/color":!0}},"@ensdomains/content-hash>cids":{packages:{"@ensdomains/content-hash>cids>multibase":!0,"@ensdomains/content-hash>multicodec":!0,"@ensdomains/content-hash>cids>multihashes":!0,"@ensdomains/content-hash>cids>uint8arrays":!0}},"ethereumjs-util>create-hash>cipher-base":{packages:{"pumpify>inherits":!0,"koa>content-disposition>safe-buffer":!0,"stream-browserify":!0,"browserify>string_decoder":!0}},classnames:{globals:{classNames:"write",define:!0}},"@metamask/jazzicon>color>clone":{packages:{"browserify>buffer":!0}},cockatiel:{globals:{AbortController:!0,AbortSignal:!0,WeakRef:!0,clearTimeout:!0,performance:!0,setTimeout:!0},packages:{process:!0}},"chalk>ansi-styles>color-convert":{packages:{"jest-canvas-mock>moo-color>color-name":!0}},"@metamask/jazzicon>color>color-convert":{packages:{"@metamask/jazzicon>color>color-convert>color-name":!0}},"@metamask/jazzicon>color>color-string":{packages:{"jest-canvas-mock>moo-color>color-name":!0}},"@metamask/jazzicon>color":{packages:{"@metamask/jazzicon>color>clone":!0,"@metamask/jazzicon>color>color-convert":!0,"@metamask/jazzicon>color>color-string":!0}},"@metamask/snaps-controllers>concat-stream":{packages:{"terser>source-map-support>buffer-from":!0,"browserify>buffer":!0,"pumpify>inherits":!0,"readable-stream":!0,"browserify>concat-stream>typedarray":!0}},"copy-to-clipboard":{globals:{clipboardData:!0,"console.error":!0,"console.warn":!0,"document.body.appendChild":!0,"document.body.removeChild":!0,"document.createElement":!0,"document.createRange":!0,"document.execCommand":!0,"document.getSelection":!0,"navigator.userAgent":!0,prompt:!0},packages:{"copy-to-clipboard>toggle-selection":!0}},"readable-stream-2>core-util-is":{packages:{"browserify>insert-module-globals>is-buffer":!0}},"eth-lattice-keyring>gridplus-sdk>crc-32":{globals:{DO_NOT_EXPORT_CRC:!0,define:!0}},"@ngraveio/bc-ur>crc":{packages:{"browserify>buffer":!0}},"crypto-browserify>create-ecdh":{packages:{"bn.js":!0,"browserify>buffer":!0,"@metamask/ppom-validator>elliptic":!0}},"ethereumjs-util>create-hash":{packages:{"ethereumjs-util>create-hash>cipher-base":!0,"pumpify>inherits":!0,"ethereumjs-util>create-hash>md5.js":!0,"ethereumjs-util>create-hash>ripemd160":!0,"addons-linter>sha.js":!0}},"crypto-browserify>pbkdf2>create-hash":{packages:{"browserify>buffer":!0}},"crypto-browserify>create-hmac":{packages:{"ethereumjs-util>create-hash>cipher-base":!0,"ethereumjs-util>create-hash":!0,"pumpify>inherits":!0,"ethereumjs-util>create-hash>ripemd160":!0,"koa>content-disposition>safe-buffer":!0,"addons-linter>sha.js":!0}},"cron-parser":{packages:{"browserify>browser-resolve":!0,luxon:!0}},"crypto-browserify":{packages:{"crypto-browserify>browserify-cipher":!0,"crypto-browserify>browserify-sign":!0,"crypto-browserify>create-ecdh":!0,"ethereumjs-util>create-hash":!0,"crypto-browserify>create-hmac":!0,"crypto-browserify>diffie-hellman":!0,"crypto-browserify>pbkdf2":!0,"crypto-browserify>public-encrypt":!0,"crypto-browserify>randombytes":!0,"crypto-browserify>randomfill":!0}},"@metamask/ppom-validator>crypto-js":{globals:{crypto:!0,define:!0,msCrypto:!0},packages:{"browserify>browser-resolve":!0}},"react-beautiful-dnd>css-box-model":{globals:{getComputedStyle:!0,pageXOffset:!0,pageYOffset:!0},packages:{"react-router-dom>tiny-invariant":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-vendor-prefixer>css-vendor":{globals:{"document.createElement":!0,"document.documentElement":!0,getComputedStyle:!0},packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/styles>jss>is-in-browser":!0}},"currency-formatter":{packages:{"currency-formatter>accounting":!0,"currency-formatter>locale-currency":!0,"react>object-assign":!0}},"nock>debug":{globals:{console:!0,document:!0,localStorage:!0,navigator:!0,process:!0},packages:{"mocha>ms":!0,process:!0}},"@metamask/eth-token-tracker>deep-equal":{packages:{"string.prototype.matchall>es-abstract>array-buffer-byte-length":!0,"string.prototype.matchall>call-bind":!0,"@metamask/eth-token-tracker>deep-equal>es-get-iterator":!0,"string.prototype.matchall>get-intrinsic":!0,"browserify>util>is-arguments":!0,"string.prototype.matchall>es-abstract>is-array-buffer":!0,"@metamask/eth-token-tracker>deep-equal>is-date-object":!0,"string.prototype.matchall>es-abstract>is-regex":!0,"string.prototype.matchall>es-abstract>is-shared-array-buffer":!0,"@lavamoat/lavapack>json-stable-stringify>isarray":!0,"@ngraveio/bc-ur>assert>object-is":!0,"@lavamoat/lavapack>json-stable-stringify>object-keys":!0,"gulp>vinyl-fs>object.assign":!0,"string.prototype.matchall>regexp.prototype.flags":!0,"string.prototype.matchall>side-channel":!0,"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive":!0,"@metamask/eth-token-tracker>deep-equal>which-collection":!0,"browserify>util>which-typed-array":!0}},"string.prototype.matchall>define-properties>define-data-property":{packages:{"string.prototype.matchall>call-bind>es-define-property":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>es-abstract>gopd":!0}},"string.prototype.matchall>define-properties":{packages:{"string.prototype.matchall>define-properties>define-data-property":!0,"string.prototype.matchall>es-abstract>has-property-descriptors":!0,"@lavamoat/lavapack>json-stable-stringify>object-keys":!0}},"crypto-browserify>browserify-cipher>browserify-des>des.js":{packages:{"pumpify>inherits":!0,"@metamask/ppom-validator>elliptic>minimalistic-assert":!0}},"@metamask/providers>detect-browser":{globals:{document:!0,navigator:!0},packages:{process:!0}},"crypto-browserify>diffie-hellman":{packages:{"bn.js":!0,"browserify>buffer":!0,"crypto-browserify>diffie-hellman>miller-rabin":!0,"crypto-browserify>randombytes":!0}},"react-transition-group>dom-helpers":{packages:{"@babel/runtime":!0}},dompurify:{globals:{"console.warn":!0,define:!0}},"string.prototype.matchall>get-intrinsic>get-proto>dunder-proto":{packages:{"string.prototype.matchall>call-bind>call-bind-apply-helpers":!0,"string.prototype.matchall>es-abstract>gopd":!0}},"@metamask/ppom-validator>elliptic":{packages:{"bn.js":!0,"@metamask/ppom-validator>elliptic>brorand":!0,"ethers>@ethersproject/sha2>hash.js":!0,"@metamask/ppom-validator>elliptic>hmac-drbg":!0,"pumpify>inherits":!0,"@metamask/ppom-validator>elliptic>minimalistic-assert":!0,"@metamask/ppom-validator>elliptic>minimalistic-crypto-utils":!0}},"@metamask/eth-token-tracker>deep-equal>es-get-iterator":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>get-intrinsic":!0,"string.prototype.matchall>has-symbols":!0,"browserify>util>is-arguments":!0,"@metamask/eth-token-tracker>deep-equal>es-get-iterator>is-map":!0,"@metamask/eth-token-tracker>deep-equal>es-get-iterator>is-set":!0,"eslint-plugin-react>array-includes>is-string":!0,"@lavamoat/lavapack>json-stable-stringify>isarray":!0,process:!0,"@metamask/eth-token-tracker>deep-equal>es-get-iterator>stop-iteration-iterator":!0}},"eth-lattice-keyring>gridplus-sdk>eth-eip712-util-browser":{globals:{intToBuffer:!0},packages:{"bn.js":!0,buffer:!0,"eth-ens-namehash>js-sha3":!0}},"eth-ens-namehash":{globals:{name:"write"},packages:{"browserify>buffer":!0,"eth-ens-namehash>idna-uts46-hx":!0,"eth-ens-namehash>js-sha3":!0}},"eth-lattice-keyring":{globals:{addEventListener:!0,browser:!0,clearInterval:!0,fetch:!0,open:!0,setInterval:!0},packages:{"@ethereumjs/tx":!0,"eth-lattice-keyring>@ethereumjs/util":!0,"bn.js":!0,"browserify>buffer":!0,"crypto-browserify":!0,"webpack>events":!0,"eth-lattice-keyring>gridplus-sdk":!0,"eth-lattice-keyring>rlp":!0}},"eth-method-registry":{packages:{"eth-method-registry>@metamask/ethjs-contract":!0,"eth-method-registry>@metamask/ethjs-query":!0}},"@ethereumjs/tx>ethereum-cryptography":{globals:{TextDecoder:!0,crypto:!0},packages:{"@ethereumjs/tx>ethereum-cryptography>@noble/curves":!0,"@ethereumjs/tx>ethereum-cryptography>@noble/hashes":!0,"@ethereumjs/tx>ethereum-cryptography>@scure/bip32":!0}},"ethereumjs-util>ethereum-cryptography":{packages:{"browserify>buffer":!0,"ethereumjs-util>ethereum-cryptography>keccak":!0,"crypto-browserify>randombytes":!0,"ganache>secp256k1":!0}},"@metamask/keyring-controller>ethereumjs-wallet>ethereum-cryptography":{packages:{"browserify>assert":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,"browserify>buffer":!0,"crypto-browserify>create-hmac":!0,"ethers>@ethersproject/sha2>hash.js":!0,"ethereumjs-util>ethereum-cryptography>keccak":!0,"crypto-browserify>randombytes":!0,"koa>content-disposition>safe-buffer":!0,"ganache>secp256k1":!0}},"ethereumjs-util":{packages:{"browserify>assert":!0,"bn.js":!0,"browserify>buffer":!0,"ethereumjs-util>create-hash":!0,"ethereumjs-util>ethereum-cryptography":!0,"browserify>insert-module-globals>is-buffer":!0,"ethereumjs-util>rlp":!0}},"@metamask/keyring-controller>ethereumjs-wallet>ethereumjs-util":{packages:{"browserify>assert":!0,"bn.js":!0,"browserify>buffer":!0,"ethereumjs-util>create-hash":!0,"@metamask/keyring-controller>ethereumjs-wallet>ethereum-cryptography":!0,"browserify>insert-module-globals>is-buffer":!0,"@metamask/keyring-controller>ethereumjs-wallet>ethereumjs-util>rlp":!0}},"@metamask/keyring-controller>ethereumjs-wallet":{packages:{"eth-lattice-keyring>gridplus-sdk>aes-js":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,"browserify>buffer":!0,"crypto-browserify":!0,"@metamask/keyring-controller>ethereumjs-wallet>ethereum-cryptography":!0,"@metamask/keyring-controller>ethereumjs-wallet>ethereumjs-util":!0,"crypto-browserify>randombytes":!0,"ethers>@ethersproject/json-wallets>scrypt-js":!0,"@metamask/keyring-controller>ethereumjs-wallet>utf8":!0,uuid:!0}},ethers:{packages:{"@ethersproject/abi":!0,"ethers>@ethersproject/abstract-signer":!0,"ethers>@ethersproject/address":!0,"ethers>@ethersproject/base64":!0,"ethers>@ethersproject/basex":!0,"@ethersproject/bignumber":!0,"@ethersproject/bytes":!0,"ethers>@ethersproject/constants":!0,"@ethersproject/contracts":!0,"@ethersproject/hash":!0,"@ethersproject/hdnode":!0,"ethers>@ethersproject/json-wallets":!0,"ethers>@ethersproject/keccak256":!0,"ethers>@ethersproject/logger":!0,"ethers>@ethersproject/properties":!0,"ethers>@ethersproject/providers":!0,"ethers>@ethersproject/random":!0,"ethers>@ethersproject/rlp":!0,"ethers>@ethersproject/sha2":!0,"ethers>@ethersproject/signing-key":!0,"ethers>@ethersproject/solidity":!0,"ethers>@ethersproject/strings":!0,"ethers>@ethersproject/transactions":!0,"ethers>@ethersproject/units":!0,"@ethersproject/wallet":!0,"ethers>@ethersproject/web":!0,"ethers>@ethersproject/wordlists":!0}},"eth-method-registry>@metamask/ethjs-contract>ethjs-abi":{packages:{"bn.js":!0,"browserify>buffer":!0,"eth-ens-namehash>js-sha3":!0,"eth-method-registry>@metamask/ethjs-contract>ethjs-abi>number-to-bn":!0}},"webpack>events":{globals:{console:!0}},"crypto-browserify>browserify-cipher>evp_bytestokey":{packages:{"ethereumjs-util>create-hash>md5.js":!0,"koa>content-disposition>safe-buffer":!0}},"extension-port-stream":{packages:{"browserify>buffer":!0,"extension-port-stream>readable-stream":!0}},"@metamask/providers>extension-port-stream":{packages:{"browserify>buffer":!0,"@metamask/providers>extension-port-stream>readable-stream":!0}},"fast-json-patch":{globals:{addEventListener:!0,clearTimeout:!0,removeEventListener:!0,setTimeout:!0}},"@metamask/snaps-utils>fast-xml-parser":{globals:{entityName:!0,val:!0},packages:{"@metamask/snaps-utils>fast-xml-parser>strnum":!0}},"@metamask/notification-services-controller>firebase":{packages:{"@metamask/notification-services-controller>firebase>@firebase/app":!0,"@metamask/notification-services-controller>firebase>@firebase/messaging":!0}},"react-focus-lock>focus-lock":{globals:{HTMLIFrameElement:!0,"Node.DOCUMENT_FRAGMENT_NODE":!0,"Node.DOCUMENT_NODE":!0,"Node.DOCUMENT_POSITION_CONTAINED_BY":!0,"Node.DOCUMENT_POSITION_CONTAINS":!0,"Node.ELEMENT_NODE":!0,"console.error":!0,"console.warn":!0,document:!0,getComputedStyle:!0,setTimeout:!0},packages:{tslib:!0}},"browserify>util>which-typed-array>for-each":{packages:{"string.prototype.matchall>es-abstract>is-callable":!0}},"fuse.js":{globals:{console:!0,define:!0}},"string.prototype.matchall>get-intrinsic":{globals:{AggregateError:!0,FinalizationRegistry:!0,Float16Array:!0,WeakRef:!0},packages:{"string.prototype.matchall>call-bind>call-bind-apply-helpers":!0,"string.prototype.matchall>call-bind>es-define-property":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>es-abstract>es-object-atoms":!0,"browserify>has>function-bind":!0,"string.prototype.matchall>get-intrinsic>get-proto":!0,"string.prototype.matchall>es-abstract>gopd":!0,"string.prototype.matchall>has-symbols":!0,"axios>form-data>hasown":!0,"string.prototype.matchall>get-intrinsic>math-intrinsics":!0}},"string.prototype.matchall>get-intrinsic>get-proto":{packages:{"string.prototype.matchall>get-intrinsic>get-proto>dunder-proto":!0,"string.prototype.matchall>es-abstract>es-object-atoms":!0}},"eth-lattice-keyring>gridplus-sdk":{globals:{AbortController:!0,Request:!0,URL:!0,__values:!0,caches:!0,clearTimeout:!0,"console.error":!0,"console.log":!0,"console.warn":!0,fetch:!0,setTimeout:!0},packages:{"eth-lattice-keyring>gridplus-sdk>@ethereumjs/common":!0,"@ethereumjs/tx":!0,"@ethersproject/abi":!0,"eth-lattice-keyring>gridplus-sdk>aes-js":!0,"bitcoin-address-validation>bech32":!0,"eth-lattice-keyring>gridplus-sdk>bignumber.js":!0,"eth-lattice-keyring>gridplus-sdk>bitwise":!0,"bn.js":!0,"eth-lattice-keyring>gridplus-sdk>borc":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,"browserify>buffer":!0,"eth-lattice-keyring>gridplus-sdk>crc-32":!0,"@metamask/ppom-validator>elliptic":!0,"eth-lattice-keyring>gridplus-sdk>eth-eip712-util-browser":!0,"ethers>@ethersproject/sha2>hash.js":!0,"eth-ens-namehash>js-sha3":!0,lodash:!0,"eth-lattice-keyring>rlp":!0,"ganache>secp256k1":!0,"eth-lattice-keyring>gridplus-sdk>uuid":!0}},"string.prototype.matchall>es-abstract>has-property-descriptors":{packages:{"string.prototype.matchall>call-bind>es-define-property":!0}},"koa>is-generator-function>has-tostringtag":{packages:{"string.prototype.matchall>has-symbols":!0}},"ethereumjs-util>create-hash>md5.js>hash-base":{packages:{"pumpify>inherits":!0,"readable-stream":!0,"koa>content-disposition>safe-buffer":!0}},"ethereumjs-util>create-hash>ripemd160>hash-base":{packages:{"pumpify>inherits":!0,"readable-stream":!0,"koa>content-disposition>safe-buffer":!0}},"crypto-browserify>pbkdf2>ripemd160>hash-base":{packages:{"browserify>buffer":!0,"pumpify>inherits":!0,"stream-browserify":!0}},"ethers>@ethersproject/sha2>hash.js":{packages:{"pumpify>inherits":!0,"@metamask/ppom-validator>elliptic>minimalistic-assert":!0}},"axios>form-data>hasown":{packages:{"browserify>has>function-bind":!0}},"@metamask/eth-trezor-keyring>hdkey":{packages:{"browserify>assert":!0,"ethereumjs-util>ethereum-cryptography>bs58check":!0,"crypto-browserify":!0,"ethereumjs-util>create-hash>ripemd160":!0,"koa>content-disposition>safe-buffer":!0,"ganache>secp256k1":!0}},he:{globals:{define:!0}},history:{globals:{console:!0,define:!0,"document.defaultView":!0,"document.querySelector":!0}},"react-router-dom>history":{globals:{addEventListener:!0,confirm:!0,document:!0,history:!0,location:!0,"navigator.userAgent":!0,removeEventListener:!0},packages:{"react-router-dom>history>resolve-pathname":!0,"react-router-dom>tiny-invariant":!0,"react-router-dom>tiny-warning":!0,"react-router-dom>history>value-equal":!0}},"@metamask/ppom-validator>elliptic>hmac-drbg":{packages:{"ethers>@ethersproject/sha2>hash.js":!0,"@metamask/ppom-validator>elliptic>minimalistic-assert":!0,"@metamask/ppom-validator>elliptic>minimalistic-crypto-utils":!0}},"react-redux>hoist-non-react-statics":{packages:{"react-redux>hoist-non-react-statics>react-is":!0}},"https-browserify":{packages:{"stream-http":!0,"browserify>url":!0}},"@metamask/notification-services-controller>firebase>@firebase/app>idb":{globals:{DOMException:!0,IDBCursor:!0,IDBDatabase:!0,IDBIndex:!0,IDBObjectStore:!0,IDBRequest:!0,IDBTransaction:!0,"indexedDB.deleteDatabase":!0,"indexedDB.open":!0}},"eth-ens-namehash>idna-uts46-hx":{globals:{define:!0},packages:{"browserify>punycode":!0}},"string.prototype.matchall>internal-slot":{packages:{"string.prototype.matchall>es-abstract>es-errors":!0,"axios>form-data>hasown":!0,"string.prototype.matchall>side-channel":!0}},"browserify>util>is-arguments":{packages:{"string.prototype.matchall>call-bind":!0,"koa>is-generator-function>has-tostringtag":!0}},"string.prototype.matchall>es-abstract>is-array-buffer":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>get-intrinsic":!0}},"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-bigint":{packages:{"string.prototype.matchall>es-abstract>unbox-primitive>has-bigints":!0}},"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-boolean-object":{packages:{"string.prototype.matchall>call-bind":!0,"koa>is-generator-function>has-tostringtag":!0}},"string.prototype.matchall>es-abstract>is-callable":{globals:{document:!0}},"@metamask/eth-token-tracker>deep-equal>is-date-object":{packages:{"koa>is-generator-function>has-tostringtag":!0}},"koa>is-generator-function":{packages:{"koa>is-generator-function>has-tostringtag":!0}},"@material-ui/core>@material-ui/styles>jss>is-in-browser":{globals:{document:!0}},"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-number-object":{packages:{"koa>is-generator-function>has-tostringtag":!0}},"string.prototype.matchall>es-abstract>is-regex":{packages:{"string.prototype.matchall>call-bind":!0,"koa>is-generator-function>has-tostringtag":!0}},"string.prototype.matchall>es-abstract>is-shared-array-buffer":{packages:{"string.prototype.matchall>call-bind":!0}},"eslint-plugin-react>array-includes>is-string":{packages:{"koa>is-generator-function>has-tostringtag":!0}},"string.prototype.matchall>es-abstract>es-to-primitive>is-symbol":{packages:{"string.prototype.matchall>has-symbols":!0}},"browserify>util>is-typed-array":{packages:{"browserify>util>which-typed-array":!0}},"@metamask/eth-token-tracker>deep-equal>which-collection>is-weakset":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>get-intrinsic":!0}},"eth-lattice-keyring>gridplus-sdk>borc>iso-url":{globals:{URL:!0,URLSearchParams:!0,location:!0}},"@open-rpc/test-coverage>isomorphic-fetch":{globals:{"fetch.bind":!0},packages:{"@open-rpc/test-coverage>isomorphic-fetch>whatwg-fetch":!0}},"@ensdomains/content-hash>js-base64":{globals:{Base64:"write",TextDecoder:!0,TextEncoder:!0,atob:!0,btoa:!0,define:!0},packages:{"browserify>buffer":!0}},"eth-ens-namehash>js-sha3":{globals:{define:!0},packages:{process:!0}},"@ngraveio/bc-ur>jsbi":{globals:{define:!0}},"@metamask/message-manager>jsonschema":{packages:{"browserify>url":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-camel-case":{packages:{"@material-ui/core>@material-ui/styles>jss-plugin-camel-case>hyphenate-style-name":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-default-unit":{globals:{CSS:!0},packages:{"@material-ui/core>@material-ui/styles>jss":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-global":{packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/styles>jss":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-nested":{packages:{"@babel/runtime":!0,"react-router-dom>tiny-warning":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-rule-value-function":{packages:{"@material-ui/core>@material-ui/styles>jss":!0,"react-router-dom>tiny-warning":!0}},"@material-ui/core>@material-ui/styles>jss-plugin-vendor-prefixer":{packages:{"@material-ui/core>@material-ui/styles>jss-plugin-vendor-prefixer>css-vendor":!0,"@material-ui/core>@material-ui/styles>jss":!0}},"@material-ui/core>@material-ui/styles>jss":{globals:{CSS:!0,"document.createElement":!0,"document.querySelector":!0},packages:{"@babel/runtime":!0,"@material-ui/core>@material-ui/styles>jss>is-in-browser":!0,"react-router-dom>tiny-warning":!0}},"ethereumjs-util>ethereum-cryptography>keccak":{packages:{"browserify>buffer":!0,"readable-stream":!0}},"currency-formatter>locale-currency":{globals:{countryCode:!0}},localforage:{globals:{Blob:!0,BlobBuilder:!0,FileReader:!0,IDBKeyRange:!0,MSBlobBuilder:!0,MozBlobBuilder:!0,OIndexedDB:!0,WebKitBlobBuilder:!0,atob:!0,btoa:!0,"console.error":!0,"console.info":!0,"console.warn":!0,define:!0,fetch:!0,indexedDB:!0,localStorage:!0,mozIndexedDB:!0,msIndexedDB:!0,"navigator.platform":!0,"navigator.userAgent":!0,openDatabase:!0,setTimeout:!0,webkitIndexedDB:!0}},lodash:{globals:{clearTimeout:!0,define:!0,setTimeout:!0}},loglevel:{globals:{console:!0,define:!0,"document.cookie":!0,localStorage:!0,log:"write",navigator:!0}},"lottie-web":{globals:{Blob:!0,Howl:!0,OffscreenCanvas:!0,"URL.createObjectURL":!0,Worker:!0,XMLHttpRequest:!0,bodymovin:"write",clearInterval:!0,console:!0,define:!0,"document.body":!0,"document.createElement":!0,"document.createElementNS":!0,"document.getElementsByClassName":!0,"document.getElementsByTagName":!0,"document.querySelectorAll":!0,"document.readyState":!0,"location.origin":!0,"location.pathname":!0,navigator:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0}},luxon:{globals:{Intl:!0}},"@metamask/snaps-utils>marked":{globals:{"console.error":!0,"console.warn":!0,define:!0}},"ethereumjs-util>create-hash>md5.js":{packages:{"ethereumjs-util>create-hash>md5.js>hash-base":!0,"pumpify>inherits":!0,"koa>content-disposition>safe-buffer":!0}},"@storybook/addon-docs>remark-external-links>mdast-util-definitions":{packages:{"react-markdown>unist-util-visit":!0}},"react-markdown>remark-parse>mdast-util-from-markdown":{packages:{"react-markdown>remark-parse>mdast-util-from-markdown>mdast-util-to-string":!0,"react-markdown>remark-parse>mdast-util-from-markdown>micromark":!0,"react-syntax-highlighter>refractor>parse-entities":!0,"react-markdown>remark-parse>mdast-util-from-markdown>unist-util-stringify-position":!0}},"react-markdown>remark-rehype>mdast-util-to-hast":{globals:{"console.warn":!0},packages:{"@storybook/addon-docs>remark-external-links>mdast-util-definitions":!0,"react-markdown>remark-rehype>mdast-util-to-hast>mdurl":!0,"react-markdown>remark-rehype>mdast-util-to-hast>unist-builder":!0,"react-markdown>remark-rehype>mdast-util-to-hast>unist-util-generated":!0,"react-markdown>remark-rehype>mdast-util-to-hast>unist-util-position":!0,"react-markdown>unist-util-visit":!0}},"eth-lattice-keyring>@ethereumjs/util>micro-ftch":{globals:{Headers:!0,TextDecoder:!0,URL:!0,btoa:!0,fetch:!0},packages:{"browserify>browserify-zlib":!0,"browserify>buffer":!0,"https-browserify":!0,process:!0,"stream-http":!0,"browserify>url":!0,"browserify>util":!0}},"react-markdown>remark-parse>mdast-util-from-markdown>micromark":{packages:{"react-syntax-highlighter>refractor>parse-entities":!0}},"crypto-browserify>diffie-hellman>miller-rabin":{packages:{"bn.js":!0,"@metamask/ppom-validator>elliptic>brorand":!0}},"@ensdomains/content-hash>cids>multibase":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@ensdomains/content-hash>cids>multibase>@multiformats/base-x":!0}},"@ensdomains/content-hash>multihashes>multibase":{packages:{"@ensdomains/content-hash>multihashes>multibase>base-x":!0,"browserify>buffer":!0,"@ensdomains/content-hash>multihashes>web-encoding":!0}},"@ensdomains/content-hash>multicodec":{packages:{"@ensdomains/content-hash>multicodec>uint8arrays":!0,"sass-embedded>varint":!0}},"@ensdomains/content-hash>multicodec>uint8arrays>multiformats":{globals:{TextDecoder:!0,TextEncoder:!0,"console.warn":!0,"crypto.subtle.digest":!0}},"@ensdomains/content-hash>multihashes":{packages:{"browserify>buffer":!0,"@ensdomains/content-hash>multihashes>multibase":!0,"@ensdomains/content-hash>multihashes>varint":!0,"@ensdomains/content-hash>multihashes>web-encoding":!0}},"@ensdomains/content-hash>cids>multihashes":{packages:{"@ensdomains/content-hash>cids>multibase":!0,"@ensdomains/content-hash>cids>multihashes>uint8arrays":!0,"@ensdomains/content-hash>cids>multihashes>varint":!0}},nanoid:{globals:{"crypto.getRandomValues":!0}},"@metamask/approval-controller>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/smart-transactions-controller>@metamask/controllers>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/notification-controller>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/permission-controller>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/rpc-methods>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/rpc-methods-flask>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/snaps-controllers>nanoid":{globals:{"crypto.getRandomValues":!0}},"@metamask/snaps-controllers-flask>nanoid":{globals:{"crypto.getRandomValues":!0}},"depcheck>@vue/compiler-sfc>postcss>nanoid":{globals:{"crypto.getRandomValues":!0}},"dependency-tree>precinct>detective-postcss>postcss>nanoid":{globals:{"crypto.getRandomValues":!0}},"node-fetch":{globals:{Headers:!0,Request:!0,Response:!0,fetch:!0}},"@metamask/controllers>web3-provider-engine>cross-fetch>node-fetch":{globals:{fetch:!0}},"@metamask/controllers>web3-provider-engine>eth-json-rpc-middleware>node-fetch":{globals:{fetch:!0}},"eth-method-registry>@metamask/ethjs-contract>ethjs-abi>number-to-bn":{packages:{"bn.js":!0,"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>strip-hex-prefix":!0}},"string.prototype.matchall>es-abstract>object-inspect":{globals:{HTMLElement:!0,WeakRef:!0},packages:{"browserify>browser-resolve":!0}},"@ngraveio/bc-ur>assert>object-is":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>define-properties":!0}},"gulp>vinyl-fs>object.assign":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>define-properties":!0,"string.prototype.matchall>has-symbols":!0,"@lavamoat/lavapack>json-stable-stringify>object-keys":!0}},"@metamask/object-multiplex>once":{packages:{"@metamask/object-multiplex>once>wrappy":!0}},"crypto-browserify>public-encrypt>parse-asn1":{packages:{"crypto-browserify>public-encrypt>parse-asn1>asn1.js":!0,"ethereumjs-util>ethereum-cryptography>browserify-aes":!0,"crypto-browserify>browserify-cipher>evp_bytestokey":!0,"crypto-browserify>pbkdf2":!0,"koa>content-disposition>safe-buffer":!0}},"react-syntax-highlighter>refractor>parse-entities":{globals:{"document.createElement":!0}},"path-browserify":{packages:{process:!0}},"serve-handler>path-to-regexp":{packages:{"serve-handler>path-to-regexp>isarray":!0}},"crypto-browserify>pbkdf2":{globals:{crypto:!0,process:!0,queueMicrotask:!0,setImmediate:!0,setTimeout:!0},packages:{"crypto-browserify>pbkdf2>create-hash":!0,process:!0,"crypto-browserify>pbkdf2>ripemd160":!0,"koa>content-disposition>safe-buffer":!0,"addons-linter>sha.js":!0,"crypto-browserify>pbkdf2>to-buffer":!0}},"@material-ui/core>popper.js":{globals:{MSInputMethodContext:!0,"Node.DOCUMENT_POSITION_FOLLOWING":!0,cancelAnimationFrame:!0,"console.warn":!0,define:!0,devicePixelRatio:!0,document:!0,getComputedStyle:!0,innerHeight:!0,innerWidth:!0,navigator:!0,requestAnimationFrame:!0,setTimeout:!0}},"react-tippy>popper.js":{globals:{MSInputMethodContext:!0,"Node.DOCUMENT_POSITION_FOLLOWING":!0,cancelAnimationFrame:!0,"console.warn":!0,define:!0,devicePixelRatio:!0,document:!0,getComputedStyle:!0,innerHeight:!0,innerWidth:!0,"navigator.userAgent":!0,requestAnimationFrame:!0,setTimeout:!0}},process:{globals:{clearTimeout:!0,setTimeout:!0}},"readable-stream-2>process-nextick-args":{packages:{process:!0}},"eth-method-registry>@metamask/ethjs-query>promise-to-callback":{packages:{"eth-method-registry>@metamask/ethjs-query>promise-to-callback>is-fn":!0,"eth-method-registry>@metamask/ethjs-query>promise-to-callback>set-immediate-shim":!0}},"prop-types":{globals:{console:!0},packages:{"react>object-assign":!0,"prop-types>react-is":!0}},"react-markdown>property-information":{packages:{"watchify>xtend":!0}},"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs":{globals:{process:!0,setTimeout:!0},packages:{"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/aspromise":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/base64":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/codegen":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/eventemitter":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/fetch":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/float":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/inquire":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/path":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/pool":!0,"@trezor/connect-web>@trezor/connect>@trezor/protobuf>protobufjs>@protobufjs/utf8":!0}},"crypto-browserify>public-encrypt":{packages:{"bn.js":!0,"crypto-browserify>public-encrypt>browserify-rsa":!0,"ethereumjs-util>create-hash":!0,"crypto-browserify>public-encrypt>parse-asn1":!0,"crypto-browserify>randombytes":!0,"koa>content-disposition>safe-buffer":!0}},"browserify>punycode":{globals:{define:!0}},"browserify>url>punycode":{globals:{define:!0}},"qrcode-generator":{globals:{define:!0}},"qrcode.react":{globals:{Path2D:!0,devicePixelRatio:!0},packages:{react:!0}},"browserify>url>qs":{packages:{"string.prototype.matchall>side-channel":!0}},"@metamask/snaps-controllers>tar-stream>streamx>queue-tick":{globals:{queueMicrotask:!0}},"react-beautiful-dnd>raf-schd":{globals:{cancelAnimationFrame:!0,requestAnimationFrame:!0}},"crypto-browserify>randombytes":{globals:{crypto:!0,msCrypto:!0},packages:{process:!0,"koa>content-disposition>safe-buffer":!0}},"ethereumjs-wallet>randombytes":{globals:{"crypto.getRandomValues":!0}},"crypto-browserify>randomfill":{globals:{crypto:!0,msCrypto:!0},packages:{process:!0,"crypto-browserify>randombytes":!0,"koa>content-disposition>safe-buffer":!0}},react:{globals:{console:!0},packages:{"react>object-assign":!0}},"react-beautiful-dnd":{globals:{"Element.prototype":!0,__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:!0,addEventListener:!0,cancelAnimationFrame:!0,clearTimeout:!0,console:!0,document:!0,getComputedStyle:!0,pageXOffset:!0,pageYOffset:!0,removeEventListener:!0,requestAnimationFrame:!0,scrollBy:!0,setTimeout:!0},packages:{"@babel/runtime":!0,"react-beautiful-dnd>css-box-model":!0,"react-beautiful-dnd>memoize-one":!0,"react-beautiful-dnd>raf-schd":!0,react:!0,"react-dom":!0,"react-redux":!0,redux:!0,"react-beautiful-dnd>use-memo-one":!0}},"react-chartjs-2":{globals:{setTimeout:!0},packages:{"chart.js":!0,react:!0}},"react-focus-lock>react-clientside-effect":{packages:{"@babel/runtime":!0,react:!0}},"react-devtools":{packages:{"react-devtools>react-devtools-core":!0}},"react-devtools>react-devtools-core":{globals:{WebSocket:!0,setTimeout:!0}},"react-dnd-html5-backend":{globals:{addEventListener:!0,clearTimeout:!0,removeEventListener:!0}},"react-dom":{globals:{HTMLIFrameElement:!0,MSApp:!0,__REACT_DEVTOOLS_GLOBAL_HOOK__:!0,addEventListener:!0,clearTimeout:!0,clipboardData:!0,console:!0,dispatchEvent:!0,document:!0,event:"write",jest:!0,"location.protocol":!0,"navigator.userAgent.indexOf":!0,removeEventListener:!0,self:!0,setTimeout:!0,top:!0},packages:{"react>object-assign":!0,react:!0,"react-dom>scheduler":!0}},"react-responsive-carousel>react-easy-swipe":{globals:{addEventListener:!0,define:!0,"document.addEventListener":!0,"document.removeEventListener":!0},packages:{"prop-types":!0,react:!0}},"react-popper>react-fast-compare":{globals:{Element:!0,"console.warn":!0}},"react-focus-lock":{globals:{addEventListener:!0,"console.error":!0,"console.warn":!0,document:!0,removeEventListener:!0,setTimeout:!0},packages:{"@babel/runtime":!0,"react-focus-lock>focus-lock":!0,"prop-types":!0,react:!0,"react-focus-lock>react-clientside-effect":!0,"react-focus-lock>use-callback-ref":!0,"react-focus-lock>use-sidecar":!0}},"react-idle-timer":{globals:{clearTimeout:!0,document:!0,setTimeout:!0},packages:{"prop-types":!0,react:!0}},"react-redux>hoist-non-react-statics>react-is":{globals:{console:!0}},"prop-types>react-is":{globals:{console:!0}},"react-redux>react-is":{globals:{console:!0}},"react-router-dom>react-router>react-is":{globals:{console:!0}},"react-markdown":{globals:{"console.warn":!0},packages:{"react-markdown>comma-separated-tokens":!0,"prop-types":!0,"react-markdown>property-information":!0,react:!0,"react-redux>react-is":!0,"react-markdown>remark-parse":!0,"react-markdown>remark-rehype":!0,"react-markdown>space-separated-tokens":!0,"react-markdown>style-to-object":!0,"react-markdown>unified":!0,"react-markdown>unist-util-visit":!0,"react-markdown>vfile":!0}},"react-popper":{globals:{document:!0},packages:{"@popperjs/core":!0,react:!0,"react-popper>react-fast-compare":!0,"react-popper>warning":!0}},"react-redux":{globals:{console:!0,document:!0},packages:{"@babel/runtime":!0,"react-redux>hoist-non-react-statics":!0,"prop-types":!0,react:!0,"react-dom":!0,"react-redux>react-is":!0}},"react-responsive-carousel":{globals:{HTMLElement:!0,addEventListener:!0,clearTimeout:!0,"console.warn":!0,document:!0,getComputedStyle:!0,removeEventListener:!0,setTimeout:!0},packages:{classnames:!0,react:!0,"react-dom":!0,"react-responsive-carousel>react-easy-swipe":!0}},"react-router-dom":{packages:{"react-router-dom>history":!0,"prop-types":!0,react:!0,"react-router-dom>react-router":!0,"react-router-dom>tiny-invariant":!0,"react-router-dom>tiny-warning":!0}},"react-router-dom-v5-compat":{globals:{FormData:!0,URL:!0,URLSearchParams:!0,__reactRouterVersion:"write",addEventListener:!0,confirm:!0,define:!0,document:!0,"history.scrollRestoration":!0,"location.href":!0,removeEventListener:!0,scrollTo:!0,scrollY:!0,"sessionStorage.getItem":!0,"sessionStorage.setItem":!0,setTimeout:!0},packages:{"react-router-dom-v5-compat>@remix-run/router":!0,history:!0,react:!0,"react-dom":!0,"react-router-dom":!0,"react-router-dom-v5-compat>react-router":!0}},"react-router-dom>react-router":{packages:{"react-router-dom>history":!0,"react-redux>hoist-non-react-statics":!0,"serve-handler>path-to-regexp":!0,"prop-types":!0,react:!0,"react-router-dom>react-router>react-is":!0,"react-router-dom>tiny-invariant":!0,"react-router-dom>tiny-warning":!0}},"react-router-dom-v5-compat>react-router":{globals:{"console.error":!0,define:!0},packages:{"react-router-dom-v5-compat>@remix-run/router":!0,react:!0}},"react-simple-file-input":{globals:{File:!0,FileReader:!0,"console.warn":!0},packages:{"prop-types":!0,react:!0}},"react-tippy":{globals:{Element:!0,MSStream:!0,MutationObserver:!0,addEventListener:!0,clearTimeout:!0,"console.error":!0,"console.warn":!0,define:!0,document:!0,getComputedStyle:!0,innerHeight:!0,innerWidth:!0,"navigator.maxTouchPoints":!0,"navigator.msMaxTouchPoints":!0,"navigator.userAgent":!0,performance:!0,requestAnimationFrame:!0,setTimeout:!0},packages:{"react-tippy>popper.js":!0,react:!0,"react-dom":!0}},"react-toggle-button":{globals:{clearTimeout:!0,"console.warn":!0,define:!0,performance:!0,setTimeout:!0},packages:{react:!0}},"react-transition-group":{globals:{Element:!0,setTimeout:!0},packages:{"react-transition-group>dom-helpers":!0,"prop-types":!0,react:!0,"react-dom":!0}},"readable-stream":{packages:{"browserify>browser-resolve":!0,"browserify>buffer":!0,"webpack>events":!0,"pumpify>inherits":!0,process:!0,"browserify>string_decoder":!0,"readable-stream>util-deprecate":!0}},"crypto-browserify>browserify-sign>readable-stream":{packages:{"browserify>browser-resolve":!0,"readable-stream-2>core-util-is":!0,"webpack>events":!0,"pumpify>inherits":!0,"crypto-browserify>browserify-sign>readable-stream>isarray":!0,process:!0,"readable-stream-2>process-nextick-args":!0,"crypto-browserify>browserify-sign>readable-stream>safe-buffer":!0,"crypto-browserify>browserify-sign>readable-stream>string_decoder":!0,"browserify>timers-browserify":!0,"readable-stream>util-deprecate":!0}},"extension-port-stream>readable-stream":{globals:{AbortController:!0,AbortSignal:!0,AggregateError:!0,Blob:!0,queueMicrotask:!0},packages:{"@lavamoat/lavapack>readable-stream>abort-controller":!0,"browserify>buffer":!0,"webpack>events":!0,process:!0,"browserify>string_decoder":!0}},"@metamask/providers>extension-port-stream>readable-stream":{globals:{AbortController:!0,AbortSignal:!0,AggregateError:!0,Blob:!0,queueMicrotask:!0},packages:{"@lavamoat/lavapack>readable-stream>abort-controller":!0,"browserify>buffer":!0,"webpack>events":!0,process:!0,"browserify>string_decoder":!0}},"@metamask/snaps-controllers>readable-web-to-node-stream":{packages:{"readable-stream":!0}},redux:{globals:{console:!0},packages:{"@babel/runtime":!0}},"string.prototype.matchall>regexp.prototype.flags":{packages:{"string.prototype.matchall>call-bind":!0,"string.prototype.matchall>define-properties":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>get-intrinsic>get-proto":!0,"string.prototype.matchall>es-abstract>gopd":!0,"string.prototype.matchall>regexp.prototype.flags>set-function-name":!0}},"react-markdown>remark-parse":{packages:{"react-markdown>remark-parse>mdast-util-from-markdown":!0}},"react-markdown>remark-rehype":{packages:{"react-markdown>remark-rehype>mdast-util-to-hast":!0}},"react-markdown>vfile>replace-ext":{packages:{"path-browserify":!0}},reselect:{globals:{WeakRef:!0,"console.warn":!0,unstable_autotrackMemoize:!0}},"@metamask/snaps-utils>rfdc":{packages:{"browserify>buffer":!0}},"ethereumjs-util>create-hash>ripemd160":{packages:{"browserify>buffer":!0,"ethereumjs-util>create-hash>ripemd160>hash-base":!0,"pumpify>inherits":!0}},"crypto-browserify>pbkdf2>ripemd160":{packages:{"browserify>buffer":!0,"crypto-browserify>pbkdf2>ripemd160>hash-base":!0,"pumpify>inherits":!0}},"eth-lattice-keyring>rlp":{globals:{TextEncoder:!0}},"ethereumjs-util>rlp":{packages:{"bn.js":!0,"browserify>buffer":!0}},"@metamask/keyring-controller>ethereumjs-wallet>ethereumjs-util>rlp":{packages:{"bn.js":!0,"browserify>buffer":!0}},"wait-on>rxjs":{globals:{cancelAnimationFrame:!0,clearInterval:!0,clearTimeout:!0,performance:!0,requestAnimationFrame:!0,"setInterval.apply":!0,"setTimeout.apply":!0}},"koa>content-disposition>safe-buffer":{packages:{"browserify>buffer":!0}},"crypto-browserify>browserify-sign>readable-stream>safe-buffer":{packages:{"browserify>buffer":!0}},"crypto-browserify>browserify-sign>readable-stream>string_decoder>safe-buffer":{packages:{"browserify>buffer":!0}},"react-dom>scheduler":{globals:{MessageChannel:!0,cancelAnimationFrame:!0,clearTimeout:!0,console:!0,performance:!0,requestAnimationFrame:!0,setTimeout:!0}},"ethers>@ethersproject/json-wallets>scrypt-js":{globals:{define:!0,setTimeout:!0},packages:{"browserify>timers-browserify":!0}},"ganache>secp256k1":{packages:{"@metamask/ppom-validator>elliptic":!0}},semver:{globals:{"console.error":!0},packages:{process:!0}},"string.prototype.matchall>call-bind>set-function-length":{packages:{"string.prototype.matchall>define-properties>define-data-property":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>get-intrinsic":!0,"string.prototype.matchall>es-abstract>gopd":!0,"string.prototype.matchall>es-abstract>has-property-descriptors":!0}},"string.prototype.matchall>regexp.prototype.flags>set-function-name":{packages:{"string.prototype.matchall>define-properties>define-data-property":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>es-abstract>function.prototype.name>functions-have-names":!0,"string.prototype.matchall>es-abstract>has-property-descriptors":!0}},"eth-method-registry>@metamask/ethjs-query>promise-to-callback>set-immediate-shim":{globals:{"setTimeout.apply":!0},packages:{"browserify>timers-browserify":!0}},"addons-linter>sha.js":{packages:{"pumpify>inherits":!0,"koa>content-disposition>safe-buffer":!0}},"string.prototype.matchall>side-channel>side-channel-list":{packages:{"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>es-abstract>object-inspect":!0}},"string.prototype.matchall>side-channel>side-channel-map":{packages:{"browserify>util>which-typed-array>call-bound":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>get-intrinsic":!0,"string.prototype.matchall>es-abstract>object-inspect":!0}},"string.prototype.matchall>side-channel>side-channel-weakmap":{packages:{"browserify>util>which-typed-array>call-bound":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>get-intrinsic":!0,"string.prototype.matchall>es-abstract>object-inspect":!0,"string.prototype.matchall>side-channel>side-channel-map":!0}},"string.prototype.matchall>side-channel":{packages:{"string.prototype.matchall>es-abstract>es-errors":!0,"string.prototype.matchall>es-abstract>object-inspect":!0,"string.prototype.matchall>side-channel>side-channel-list":!0,"string.prototype.matchall>side-channel>side-channel-map":!0,"string.prototype.matchall>side-channel>side-channel-weakmap":!0}},"@metamask/profile-sync-controller>siwe":{globals:{"console.error":!0,"console.warn":!0},packages:{"@metamask/profile-sync-controller>siwe>@spruceid/siwe-parser":!0,"@metamask/profile-sync-controller>siwe>@stablelib/random":!0,ethers:!0,"@metamask/controller-utils>@spruceid/siwe-parser>valid-url":!0}},"@metamask/eth-token-tracker>deep-equal>es-get-iterator>stop-iteration-iterator":{globals:{StopIteration:!0},packages:{"string.prototype.matchall>internal-slot":!0}},"stream-browserify":{packages:{"webpack>events":!0,"pumpify>inherits":!0,"readable-stream":!0}},"stream-http":{globals:{AbortController:!0,Blob:!0,MSStreamReader:!0,ReadableStream:!0,WritableStream:!0,XDomainRequest:!0,XMLHttpRequest:!0,clearTimeout:!0,fetch:!0,"location.protocol.search":!0,setTimeout:!0},packages:{"browserify>buffer":!0,"stream-http>builtin-status-codes":!0,"pumpify>inherits":!0,process:!0,"readable-stream":!0,"browserify>url":!0,"watchify>xtend":!0}},"@metamask/snaps-controllers>tar-stream>streamx":{packages:{"webpack>events":!0,"@metamask/snaps-controllers>tar-stream>fast-fifo":!0,"@metamask/snaps-controllers>tar-stream>streamx>queue-tick":!0}},"browserify>string_decoder":{packages:{"koa>content-disposition>safe-buffer":!0}},"crypto-browserify>browserify-sign>readable-stream>string_decoder":{packages:{"crypto-browserify>browserify-sign>readable-stream>string_decoder>safe-buffer":!0}},"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>strip-hex-prefix":{packages:{"eth-method-registry>@metamask/ethjs-query>@metamask/ethjs-format>is-hex-prefixed":!0}},"react-markdown>style-to-object":{packages:{"react-markdown>style-to-object>inline-style-parser":!0}},"@metamask/snaps-controllers>tar-stream":{packages:{"@metamask/snaps-controllers>tar-stream>b4a":!0,"browserify>browser-resolve":!0,"@metamask/snaps-controllers>tar-stream>fast-fifo":!0,"@metamask/snaps-controllers>tar-stream>streamx":!0}},"browserify>timers-browserify":{globals:{clearInterval:!0,clearTimeout:!0,setInterval:!0,setTimeout:!0},packages:{process:!0}},"react-router-dom>tiny-warning":{globals:{console:!0}},"crypto-browserify>pbkdf2>to-buffer":{packages:{"@lavamoat/lavapack>json-stable-stringify>isarray":!0,"koa>content-disposition>safe-buffer":!0,"string.prototype.matchall>es-abstract>typed-array-buffer":!0}},"copy-to-clipboard>toggle-selection":{globals:{"document.activeElement":!0,"document.getSelection":!0}},tslib:{globals:{SuppressedError:!0,define:!0}},tweetnacl:{globals:{crypto:!0,msCrypto:!0,nacl:"write"},packages:{"browserify>browser-resolve":!0}},"string.prototype.matchall>es-abstract>typed-array-buffer":{packages:{"browserify>util>which-typed-array>call-bound":!0,"string.prototype.matchall>es-abstract>es-errors":!0,"browserify>util>is-typed-array":!0}},"@trezor/connect-web>@trezor/connect-common>@trezor/env-utils>ua-parser-js":{globals:{define:!0}},"@ensdomains/content-hash>cids>uint8arrays":{globals:{TextDecoder:!0},packages:{"@ensdomains/content-hash>cids>multibase":!0}},"@ensdomains/content-hash>multicodec>uint8arrays":{globals:{Buffer:!0,TextDecoder:!0,TextEncoder:!0},packages:{"@ensdomains/content-hash>multicodec>uint8arrays>multiformats":!0}},"@ensdomains/content-hash>cids>multihashes>uint8arrays":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"@ensdomains/content-hash>cids>multibase":!0}},"@metamask/keyring-controller>ulid":{globals:{"console.error":!0,crypto:!0,define:!0}},"react-markdown>unified":{packages:{"react-markdown>unified>bail":!0,"react-markdown>unified>extend":!0,"react-markdown>unified>is-buffer":!0,"mocha>yargs-unparser>is-plain-obj":!0,"react-markdown>unified>trough":!0,"react-markdown>vfile":!0}},"react-markdown>unist-util-visit>unist-util-visit-parents":{packages:{"react-markdown>unist-util-visit>unist-util-is":!0}},"react-markdown>unist-util-visit":{packages:{"react-markdown>unist-util-visit>unist-util-visit-parents":!0}},"uri-js":{globals:{define:!0}},"browserify>url":{packages:{"browserify>url>punycode":!0,"browserify>url>qs":!0}},"react-focus-lock>use-callback-ref":{packages:{react:!0}},"react-beautiful-dnd>use-memo-one":{packages:{react:!0}},"react-focus-lock>use-sidecar":{globals:{"console.error":!0},packages:{"react-focus-lock>use-sidecar>detect-node-es":!0,react:!0,tslib:!0}},"readable-stream>util-deprecate":{globals:{"console.trace":!0,"console.warn":!0,localStorage:!0}},"browserify>assert>util":{globals:{"console.error":!0,"console.log":!0,"console.trace":!0,process:!0},packages:{"browserify>assert>util>inherits":!0,process:!0}},"browserify>util":{globals:{"console.error":!0,"console.log":!0,"console.trace":!0},packages:{"pumpify>inherits":!0,"browserify>util>is-arguments":!0,"koa>is-generator-function":!0,"browserify>util>is-typed-array":!0,process:!0,"browserify>util>which-typed-array":!0}},uuid:{globals:{crypto:!0,msCrypto:!0}},"@metamask/eth-snap-keyring>uuid":{globals:{crypto:!0}},"@metamask/keyring-snap-client>uuid":{globals:{crypto:!0}},"@metamask/multichain-transactions-controller>@metamask/keyring-snap-client>uuid":{globals:{crypto:!0}},"eth-lattice-keyring>gridplus-sdk>uuid":{globals:{crypto:!0}},"@metamask/snaps-utils>validate-npm-package-name":{packages:{"@metamask/snaps-utils>validate-npm-package-name>builtins":!0}},"react-markdown>vfile>vfile-message":{packages:{"react-markdown>vfile>unist-util-stringify-position":!0}},"react-markdown>vfile":{packages:{"react-markdown>vfile>is-buffer":!0,"path-browserify":!0,process:!0,"react-markdown>vfile>replace-ext":!0,"react-markdown>vfile>vfile-message":!0}},"browserify>vm-browserify":{globals:{"document.body.appendChild":!0,"document.body.removeChild":!0,"document.createElement":!0}},"react-popper>warning":{globals:{console:!0}},"@ensdomains/content-hash>multihashes>web-encoding":{globals:{TextDecoder:!0,TextEncoder:!0},packages:{"browserify>util":!0}},web3:{globals:{XMLHttpRequest:!0}},"@metamask/controllers>web3":{globals:{XMLHttpRequest:!0}},"webextension-polyfill":{globals:{browser:!0,chrome:!0,"console.error":!0,"console.warn":!0,define:!0}},"@open-rpc/test-coverage>isomorphic-fetch>whatwg-fetch":{globals:{AbortController:!0,Blob:!0,FileReader:!0,FormData:!0,"URLSearchParams.prototype.isPrototypeOf":!0,XMLHttpRequest:!0,"console.warn":!0,define:!0,setTimeout:!0}},"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive":{packages:{"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-bigint":!0,"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-boolean-object":!0,"@metamask/eth-token-tracker>deep-equal>which-boxed-primitive>is-number-object":!0,"eslint-plugin-react>array-includes>is-string":!0,"string.prototype.matchall>es-abstract>es-to-primitive>is-symbol":!0}},"@metamask/eth-token-tracker>deep-equal>which-collection":{packages:{"@metamask/eth-token-tracker>deep-equal>es-get-iterator>is-map":!0,"@metamask/eth-token-tracker>deep-equal>es-get-iterator>is-set":!0,"@metamask/eth-token-tracker>deep-equal>which-collection>is-weakmap":!0,"@metamask/eth-token-tracker>deep-equal>which-collection>is-weakset":!0}},"browserify>util>which-typed-array":{packages:{"string.prototype.matchall>es-abstract>available-typed-arrays":!0,"string.prototype.matchall>call-bind":!0,"browserify>util>which-typed-array>call-bound":!0,"browserify>util>which-typed-array>for-each":!0,"string.prototype.matchall>get-intrinsic>get-proto":!0,"string.prototype.matchall>es-abstract>gopd":!0,"koa>is-generator-function>has-tostringtag":!0}}}});