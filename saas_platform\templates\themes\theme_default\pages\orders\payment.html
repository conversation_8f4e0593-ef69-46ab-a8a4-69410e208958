{% extends "themes/theme_default/base.html" %}
{% load i18n static %}

{% block title %}{% trans "Payment" %} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.payment-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.payment-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 1.5rem;
}

.payment-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.payment-method {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: #667eea;
    background-color: #f8f9fa;
}

/* Web3 Wallet Styles */
.wallet-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #dee2e6;
}

.wallet-connect-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.wallet-connect-button:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
}

.wallet-status {
    margin-top: 1rem;
}

.network-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.network-indicator.connected {
    background-color: #28a745;
}

.network-indicator.disconnected {
    background-color: #dc3545;
}

.auto-pay-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    display: none;
}

.network-switch-section {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    display: none;
}

.payment-method.selected {
    border-color: #667eea;
    background-color: #e7f3ff;
}

.payment-method .method-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.crypto-selector {
    display: none;
}

.crypto-selector.show {
    display: block;
}

.crypto-option {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.crypto-option:hover {
    border-color: #667eea;
    background-color: #f8f9fa;
}

.crypto-option.selected {
    border-color: #667eea;
    background-color: #e7f3ff;
}

.payment-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
}

.btn-pay {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.btn-pay:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.loading-spinner {
    display: none;
}

.loading-spinner.show {
    display: inline-block;
}
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="payment-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-credit-card me-3"></i>{% trans "Payment" %}</h1>
                <p id="order-info">{% trans "Loading order information..." %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-8">


            <!-- Payment Methods -->
            <div class="payment-card card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-wallet me-2"></i>{% trans "Select Payment Method" %}</h5>
                </div>
                <div class="card-body">
                    <!-- Cryptocurrency Payment -->
                    <div class="payment-method" data-method="crypto" onclick="selectPaymentMethod('crypto')">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="method-icon text-warning">
                                    <i class="fab fa-bitcoin"></i>
                                </div>
                            </div>
                            <div class="col">
                                <h6 class="mb-1">{% trans "Cryptocurrency" %}</h6>
                                <p class="text-muted mb-0">{% trans "Pay with Bitcoin, Ethereum, and other cryptocurrencies" %}</p>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle text-success" style="display: none;"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Cryptocurrency Selector -->
                    <div class="crypto-selector" id="crypto-selector">
                        <h6 class="mb-3">{% trans "Select Cryptocurrency" %}</h6>
                        <div class="row" id="crypto-options-container">
                            <!-- 币种选项将通过JavaScript动态加载 -->
                            <div class="col-12 text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading cryptocurrencies...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details Container -->
                    <div id="payment-details-container" class="mt-4">
                        <!-- Payment details will be displayed here when a crypto is selected -->
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="payment-card card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>{% trans "Order Summary" %}</h5>
                </div>
                <div class="card-body">
                    <div id="order-summary">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">{% trans "Loading..." %}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Button -->
            <div class="d-grid gap-2">
                <button type="button" class="btn btn-primary btn-pay" id="pay-button" onclick="processPayment()" disabled>
                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                    <i class="fas fa-lock me-2"></i>{% trans "Secure Payment" %}
                </button>
                <a href="/orders/{{ order_sn }}/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Order" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Basic Web3 Libraries for MetaMask -->
<script src="https://unpkg.com/ethers@6.10.0/dist/ethers.umd.min.js"></script>

<!-- WalletConnect v2 SDK - 更新到最新稳定版本 -->
<script src="https://unpkg.com/@walletconnect/sign-client@2.13.0/dist/index.umd.js"></script>
<script src="https://unpkg.com/@walletconnect/utils@2.13.0/dist/index.umd.js"></script>
<script src="https://unpkg.com/@walletconnect/qrcode-modal@2.6.2/dist/index.umd.js"></script>

<!-- QRCode库用于生成二维码 -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<!-- QRCode.js库用于前端生成二维码 -->
<script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>

<!-- QR codes are now generated by the backend using Python qrcode library -->

<!-- Web3 Configuration and Wallet Management -->
<script src="{% static 'themes/theme_default/js/web3-config.js' %}"></script>
<script src="{% static 'themes/theme_default/js/wallet-manager.js' %}"></script>
<script src="{% static 'themes/theme_default/js/payment-integration.js' %}"></script>
<script>
let orderData = null;
let selectedPaymentMethod = null;
let selectedCrypto = null;
let currentPaymentWindow = null; // 当前显示的支付窗口数据
let isPaymentWindowOpen = false; // 支付窗口是否打开（这才是真正的"活跃"状态）

document.addEventListener('DOMContentLoaded', function() {
    const orderSn = '{{ order_sn }}';
    // 重置币种选项状态
    resetCryptoOptions();
    loadOrderDetails(orderSn);

    // 加载币种列表
    loadCryptocurrencies();

    // 默认选择加密货币支付方法
    setTimeout(() => {
        selectPaymentMethod('crypto');
    }, 100);
});

function loadCryptocurrencies() {
    fetch('/api/v1/payments/nowpayments/currencies/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderCryptocurrencies(data.data.currencies);
            } else {
                console.error('Failed to load cryptocurrencies:', data.error);
                showError('Failed to load payment options');
            }
        })
        .catch(error => {
            console.error('Error loading cryptocurrencies:', error);
            showError('Failed to load payment options');
        });
}

function renderCryptocurrencies(currencies) {
    const container = document.getElementById('crypto-options-container');
    container.innerHTML = '';

    currencies.forEach(currency => {
        const colDiv = document.createElement('div');
        colDiv.className = 'col-md-6';

        const optionDiv = document.createElement('div');
        optionDiv.className = 'crypto-option';
        optionDiv.setAttribute('data-crypto', currency.code);
        optionDiv.onclick = () => selectCrypto(currency.code);

        const flexDiv = document.createElement('div');
        flexDiv.className = 'd-flex align-items-center';

        const icon = document.createElement('i');
        icon.className = currency.icon_class + ' me-2';

        const span = document.createElement('span');
        span.textContent = currency.name;

        // 为稳定币添加标识
        if (currency.is_stablecoin) {
            const badge = document.createElement('span');
            badge.className = 'badge bg-success ms-2';
            badge.textContent = 'Stablecoin';
            span.appendChild(badge);
        }

        flexDiv.appendChild(icon);
        flexDiv.appendChild(span);
        optionDiv.appendChild(flexDiv);
        colDiv.appendChild(optionDiv);
        container.appendChild(colDiv);
    });
}

function loadOrderDetails(orderSn) {
    fetch(`/api/v1/orders/${orderSn}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                orderData = data.data;
                displayOrderInfo(orderData);
                displayOrderSummary(orderData);
            } else {
                showError(data.error || 'Order not found');
            }
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            showError('Failed to load order details');
        });
}

function displayOrderInfo(order) {
    document.getElementById('order-info').innerHTML = `
        {% trans "Complete payment for Order" %} #${order.order_sn} - $${order.amount} ${order.currency}
    `;
}

function displayOrderSummary(order) {
    const html = `
        <div class="d-flex justify-content-between mb-2">
            <span>{% trans "Product" %}:</span>
            <span>${order.product ? order.product.name : 'Unknown Product'}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>{% trans "Amount" %}:</span>
            <span>$${order.amount}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>{% trans "Tax" %}:</span>
            <span>$0.00</span>
        </div>
        <hr>
        <div class="d-flex justify-content-between fw-bold">
            <span>{% trans "Total" %}:</span>
            <span>$${order.amount} ${order.currency}</span>
        </div>
    `;
    
    document.getElementById('order-summary').innerHTML = html;
}

function selectPaymentMethod(method) {
    selectedPaymentMethod = method;
    
    // Update UI
    document.querySelectorAll('.payment-method').forEach(el => {
        el.classList.remove('selected');
        el.querySelector('.fas.fa-check-circle').style.display = 'none';
    });
    
    const selectedElement = document.querySelector(`[data-method="${method}"]`);
    selectedElement.classList.add('selected');
    selectedElement.querySelector('.fas.fa-check-circle').style.display = 'block';
    
    // Show crypto selector if crypto is selected
    if (method === 'crypto') {
        document.getElementById('crypto-selector').classList.add('show');
    } else {
        document.getElementById('crypto-selector').classList.remove('show');
    }
    
    updatePayButton();
}

function selectCrypto(crypto) {
    // 检查是否已经有活跃的支付
    if (isPaymentActive && activePayment && activePayment.pay_currency.toLowerCase() !== crypto.toLowerCase()) {
        showNotification('Warning',
            `You already have an active ${activePayment.pay_currency} payment. Please complete it or refresh the page to select a different currency.`,
            'warning');
        return;
    }

    selectedCrypto = crypto;

    // Update UI
    document.querySelectorAll('.crypto-option').forEach(el => {
        el.classList.remove('selected');
    });

    document.querySelector(`[data-crypto="${crypto}"]`).classList.add('selected');

    updatePayButton();

    // 如果选择的是当前活跃支付的币种，直接显示支付详情
    if (isPaymentActive && activePayment && activePayment.pay_currency.toLowerCase() === crypto.toLowerCase()) {
        showPaymentDetails(activePayment);
    } else {
        // 清空支付详情容器，等待用户点击PAY按钮
        const paymentContainer = document.getElementById('payment-details-container');
        if (paymentContainer) {
            paymentContainer.innerHTML = '';
        }
    }
}



function updatePayButton() {
    const payButton = document.getElementById('pay-button');
    
    if (selectedPaymentMethod === 'crypto' && selectedCrypto) {
        payButton.disabled = false;
        payButton.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
            <i class="fas fa-lock me-2"></i>{% trans "Pay with" %} ${selectedCrypto.toUpperCase()}
        `;
    } else {
        payButton.disabled = true;
        payButton.innerHTML = `
            <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
            <i class="fas fa-lock me-2"></i>{% trans "Select Payment Method" %}
        `;
    }
}

// 添加支付处理防抖机制
let isProcessingPayment = false;
let lastPaymentRequest = null;

function processPayment() {
    if (!selectedPaymentMethod || !selectedCrypto || !orderData) {
        showNotification('Error', 'Please select a payment method', 'error');
        return;
    }

    // 防止重复处理
    if (isProcessingPayment) {
        console.log('Payment processing in progress, ignoring duplicate request');
        return;
    }

    const payButton = document.getElementById('pay-button');
    const spinner = payButton.querySelector('.loading-spinner');

    // 设置处理状态锁
    isProcessingPayment = true;

    // Show loading state
    payButton.disabled = true;
    spinner.classList.add('show');
    
    // Create payment with NowPayments
    const paymentData = {
        price_amount: parseFloat(orderData.amount),
        price_currency: orderData.currency,
        pay_currency: selectedCrypto.toUpperCase(),
        order_id: orderData.order_sn,
        order_description: `${orderData.product ? orderData.product.name : 'Product'} - Order #${orderData.order_sn}`
    };
    
    fetch('/api/v1/payments/nowpayments/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Payment API response:', data.data);

            // 检查是否是现有支付
            if (data.data.is_existing) {
                console.log('Existing payment found, payment_id:', data.data.payment_id);
                showNotification('Info', 'Found existing payment for this order and currency. Showing payment details.', 'info');
            } else {
                console.log('New payment created, payment_id:', data.data.payment_id);
            }

            // 显示支付详情（无论是新支付还是现有支付）
            showPaymentDetails(data.data);
        } else {
            console.error('Payment API error:', data.error);
            showNotification('Error', data.error || 'Payment creation failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating payment:', error);
        showNotification('Error', 'Payment service temporarily unavailable', 'error');
    })
    .finally(() => {
        // Hide loading state
        payButton.disabled = false;
        spinner.classList.remove('show');

        // 释放处理状态锁
        setTimeout(() => {
            isProcessingPayment = false;
        }, 500); // 500ms延迟防止过快的重复点击
    });
}

// 添加全局状态管理
let isRenderingPaymentDetails = false;
let lastPaymentData = null;

function showPaymentDetails(paymentData) {
    // 设置活跃支付状态
    activePayment = paymentData;
    isPaymentActive = true;
    isPaymentWindowOpen = true; // 🔧 修复：设置支付窗口开启标志

    // 添加支付窗口遮罩
    addPaymentOverlay();

    // 禁用其他币种选项
    disableOtherCryptoOptions(paymentData.pay_currency);

    // 通知支付集成模块
    if (window.paymentIntegration) {
        window.paymentIntegration.setPaymentData(paymentData);
    }

    // 防止重复渲染
    if (isRenderingPaymentDetails) {
        console.log('Payment details rendering in progress, skipping...');
        return;
    }

    // 检查是否是相同的支付数据
    if (lastPaymentData && lastPaymentData.payment_id === paymentData.payment_id) {
        console.log('Same payment data, checking if already rendered...');
        const existingPaymentCard = document.querySelector('.payment-card');
        if (existingPaymentCard) {
            console.log('Payment details already rendered, skipping...');
            return;
        }
    }

    // 设置渲染状态锁
    isRenderingPaymentDetails = true;
    lastPaymentData = paymentData;

    // Hide the original payment form with smooth transition
    const mainContent = document.querySelector('main .container');
    mainContent.style.transition = 'opacity 0.3s ease';
    mainContent.style.opacity = '0';

    setTimeout(() => {
        try {
            // Replace the main content with payment details
            showPaymentDetailsInline(paymentData);

            // Fade in the new content
            mainContent.style.opacity = '1';
        } catch (error) {
            console.error('Error rendering payment details:', error);
        } finally {
            // 释放渲染状态锁
            setTimeout(() => {
                isRenderingPaymentDetails = false;
            }, 100);
        }
    }, 300);
}

function showPaymentDetailsInline(paymentData) {
    // 验证支付数据完整性
    if (!paymentData || !paymentData.payment_id || !paymentData.pay_address) {
        console.error('Invalid payment data:', paymentData);
        return;
    }

    // 获取主容器
    const mainContainer = document.querySelector('main .container');
    if (!mainContainer) {
        console.error('Main container not found');
        return;
    }

    // 构建完整的HTML内容
    const htmlContent = `
        <div class="row justify-content-center" style="position: relative; z-index: 1000;">
            <div class="col-lg-10">
                <div class="payment-card card" style="position: relative; z-index: 1001;">
                    <div class="card-header ${paymentData.is_existing ? 'bg-info' : 'bg-success'} text-white">
                        <h5 class="mb-0">
                            <i class="fas ${paymentData.is_existing ? 'fa-info-circle' : 'fa-check-circle'} me-2"></i>
                            ${paymentData.is_existing ? '{% trans "Existing Payment Found" %}' : '{% trans "Payment Created Successfully" %}'}
                        </h5>
                    </div>
                    <div class="card-body">
                        ${paymentData.is_existing ? `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Existing Payment" %}</h6>
                            <p class="mb-0">{% trans "You already have a pending payment for this order with the selected currency. Please complete this payment or wait for it to expire before creating a new one." %}</p>
                        </div>
                        ` : ''}

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>{% trans "Payment Instructions" %}</h6>
                            <p class="mb-0">{% trans "Send the exact amount to the address below to complete your payment." %}</p>
                        </div>
                        
                        <div class="row g-4">
                            <!-- Payment Details Card -->
                            <div class="col-md-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-white border-bottom">
                                        <h6 class="mb-0 fw-bold text-dark">
                                            <i class="fas fa-info-circle text-primary me-2"></i>
                                            {% trans "Payment Details" %}
                                        </h6>
                                    </div>
                                    <div class="card-body p-4">
                                        <div class="mb-3">
                                            <label class="form-label small text-muted fw-medium">{% trans "Payment ID" %}</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control font-monospace bg-light" value="${paymentData.payment_id}" readonly>
                                                <button class="btn btn-outline-primary" onclick="copyToClipboard('${paymentData.payment_id}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label small text-muted fw-medium">{% trans "Amount to Pay" %}</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control font-monospace bg-light fw-bold text-success" value="${paymentData.pay_amount} ${paymentData.pay_currency}" readonly>
                                                <button class="btn btn-outline-primary" onclick="copyToClipboard('${paymentData.pay_amount}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="mb-0">
                                            <label class="form-label small text-muted fw-medium">{% trans "Payment Address" %}</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control font-monospace small bg-light" value="${paymentData.pay_address}" readonly>
                                                <button class="btn btn-outline-primary" onclick="copyToClipboard('${paymentData.pay_address}')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- QR Code Card -->
                            <div class="col-md-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-white border-bottom">
                                        <h6 class="mb-0 fw-bold text-dark">
                                            <i class="fas fa-qrcode text-primary me-2"></i>
                                            {% trans "Mobile Wallet" %}
                                        </h6>
                                    </div>
                                    <div class="card-body p-4 text-center">
                                        <!-- QR Code Container -->
                                        <div class="mb-3">
                                            <div id="qr-code-container" class="d-flex justify-content-center">
                                                <!-- QR code will be generated here -->
                                            </div>
                                        </div>

                                        <!-- QR Code Instructions -->
                                        <div class="mb-3">
                                            <h6 class="fw-bold text-dark mb-2" style="font-size: 0.9rem;">{% trans "Scan with Mobile Wallet" %}</h6>
                                            <p class="text-muted mb-0" style="font-size: 0.75rem;">{% trans "Use your mobile wallet app to scan this QR code and connect instantly" %}</p>
                                        </div>

                                        <!-- Supported Mobile Wallets -->
                                        <div class="border-top pt-3">
                                            <p class="text-muted mb-2 fw-medium" style="font-size: 0.75rem;">{% trans "Supported Mobile Wallets" %}</p>
                                            <div class="d-flex justify-content-center flex-wrap gap-2">
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/metamask.svg' %}"
                                                         alt="MetaMask Mobile" class="wallet-icon mb-1" style="width: 18px; height: 18px;">
                                                    <div class="small text-muted" style="font-size: 8px;">MetaMask</div>
                                                </div>
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/trustwallet.svg' %}"
                                                         alt="Trust Wallet" class="wallet-icon mb-1" style="width: 18px; height: 18px;">
                                                    <div class="small text-muted" style="font-size: 8px;">Trust</div>
                                                </div>
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/coinbase.svg' %}"
                                                         alt="Coinbase Wallet" class="wallet-icon mb-1" style="width: 18px; height: 18px;">
                                                    <div class="small text-muted" style="font-size: 8px;">Coinbase</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Web3 Wallet Payment Card -->
                            <div class="col-md-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-header bg-white border-bottom">
                                        <h6 class="mb-0 fw-bold text-dark">
                                            <i class="fas fa-wallet text-primary me-2"></i>
                                            {% trans "Web3 Wallet Payment" %}
                                        </h6>
                                    </div>
                                    <div class="card-body p-4 text-center">
                                        <!-- Wallet Icon -->
                                        <div class="mb-4">
                                            <div class="wallet-icon-circle mx-auto mb-3" style="width: 70px; height: 70px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                <i class="fas fa-wallet text-white" style="font-size: 1.8rem;"></i>
                                            </div>
                                        </div>

                                        <!-- Wallet Status -->
                                        <div id="wallet-connection-status" class="mb-4">
                                            <h6 class="fw-bold text-dark mb-2">{% trans "Ready to Connect" %}</h6>
                                            <p class="text-muted small mb-0">{% trans "One-click payment with your Web3 wallet" %}</p>
                                        </div>

                                        <!-- Connected Wallet Display -->
                                        <div id="wallet-address-display" style="display: none;" class="mb-4">
                                            <div class="alert alert-success border-0 shadow-sm mb-0">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <strong>{% trans "Wallet Connected" %}</strong><br>
                                                <small class="font-monospace" id="connected-wallet-address"></small><br>
                                                <small class="text-muted" id="wallet-network-info"></small>
                                            </div>
                                        </div>

                                        <!-- Connect Button -->
                                        <div class="d-grid gap-2 mb-4">
                                            <button type="button" class="btn btn-primary btn-lg shadow-sm" id="wallet-connect-btn" onclick="connectWalletForPayment()">
                                                <i class="fas fa-link me-2"></i>{% trans "Connect Wallet" %}
                                            </button>
                                            <button type="button" class="btn btn-success btn-lg shadow-sm" id="wallet-pay-btn" style="display: none;" onclick="payWithWallet()">
                                                <i class="fas fa-bolt me-2"></i>{% trans "Pay Now" %}
                                            </button>
                                        </div>

                                        <!-- Payment Progress -->
                                        <div id="wallet-payment-progress" style="display: none;" class="mb-4">
                                            <div class="alert alert-warning border-0 shadow-sm mb-0">
                                                <div class="d-flex align-items-center justify-content-center">
                                                    <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                                                    <div>
                                                        <strong>{% trans "Processing Payment..." %}</strong><br>
                                                        <small id="payment-progress-text">{% trans "Please confirm in your wallet" %}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Network Switch Warning -->
                                        <div id="network-switch-warning" style="display: none;" class="mb-4">
                                            <div class="alert alert-warning border-0 shadow-sm mb-0">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <strong>{% trans "Network Switch Required" %}</strong><br>
                                                <small>{% trans "Please switch to the correct network" %}</small><br>
                                                <button type="button" class="btn btn-warning btn-sm mt-2 shadow-sm" id="switch-network-btn" onclick="switchToRequiredNetwork()">
                                                    <i class="fas fa-exchange-alt me-1"></i>{% trans "Switch Network" %}
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Supported Wallets -->
                                        <div class="border-top pt-3 mt-3">
                                            <p class="text-muted small mb-2 fw-medium text-center">{% trans "Supported Wallets" %}</p>
                                            <div class="d-flex justify-content-center flex-wrap gap-3">
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/metamask.svg' %}"
                                                         alt="MetaMask" class="wallet-icon mb-1" style="width: 20px; height: 20px;">
                                                    <div class="small text-muted" style="font-size: 10px;">MetaMask</div>
                                                </div>
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/walletconnect.svg' %}"
                                                         alt="WalletConnect" class="wallet-icon mb-1" style="width: 20px; height: 20px;">
                                                    <div class="small text-muted" style="font-size: 10px;">WalletConnect</div>
                                                </div>
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/trustwallet.svg' %}"
                                                         alt="Trust Wallet" class="wallet-icon mb-1" style="width: 20px; height: 20px;">
                                                    <div class="small text-muted" style="font-size: 10px;">Trust Wallet</div>
                                                </div>
                                                <div class="wallet-icon-item text-center">
                                                    <img src="{% static 'themes/theme_default/images/wallets/coinbase.svg' %}"
                                                         alt="Coinbase Wallet" class="wallet-icon mb-1" style="width: 20px; height: 20px;">
                                                    <div class="small text-muted" style="font-size: 10px;">Coinbase</div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Instructions -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <div class="alert alert-warning mb-0">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Important Notes" %}</h6>
                                    <ul class="mb-0">
                                        <li>{% trans "Send only" %} ${paymentData.pay_currency} {% trans "to this address" %}</li>
                                        <li>{% trans "Send the exact amount shown above" %}</li>
                                        <li>{% trans "Payment will be confirmed automatically" %}</li>
                                        <li>{% trans "This payment expires in 1 hour" %}</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info mb-0">
                                    <h6><i class="fas fa-mobile-alt me-2"></i>{% trans "Mobile Wallet Instructions" %}</h6>
                                    <ol class="mb-0 small">
                                        <li>{% trans "Open your mobile wallet app" %}</li>
                                        <li>{% trans "Tap 'WalletConnect' or 'Scan QR'" %}</li>
                                        <li>{% trans "Scan the QR code on the left" %}</li>
                                        <li>{% trans "Approve the connection request" %}</li>
                                        <li>{% trans "Confirm the payment in your wallet" %}</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-primary" onclick="checkPaymentStatus('${paymentData.payment_id}')">
                                <i class="fas fa-sync me-2"></i>{% trans "Check Payment Status" %}
                            </button>
                            <button type="button" class="btn btn-primary" onclick="closePaymentWindow()">
                                <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Order" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 原子性设置HTML内容
    try {
        console.log('Setting HTML content at:', new Date().toISOString());
        console.log('Setting HTML content, length:', htmlContent.length);
        console.log('HTML content preview:', htmlContent.substring(0, 200) + '...');

        mainContainer.innerHTML = htmlContent;
        console.log('Payment details HTML set successfully at:', new Date().toISOString());

        // Set current payment data for wallet integration
        savePaymentState(paymentData);

        // Generate QR code for WalletConnect
        generatePaymentQRCode();

        // 验证关键元素是否存在
        const importantNotes = document.querySelector('.alert-warning');
        const paymentDetails = document.querySelector('.payment-card');
        console.log('Important Notes element exists:', !!importantNotes);
        console.log('Payment card element exists:', !!paymentDetails);

        // 等待DOM更新
        setTimeout(() => {
            console.log('Payment details loaded successfully');

            // 再次验证元素是否还存在
            setTimeout(() => {
                const importantNotesAfter = document.querySelector('.alert-warning');
                const paymentDetailsAfter = document.querySelector('.payment-card');
                console.log('After QR generation - Important Notes exists:', !!importantNotesAfter);
                console.log('After QR generation - Payment card exists:', !!paymentDetailsAfter);

                // 启动持续监控
                startDOMMonitoring();
            }, 100);
        }, 50);

    } catch (error) {
        console.error('Error setting payment details HTML:', error);
        // 如果设置失败，尝试恢复
        location.reload();
    }
}

// DOM监控函数
function startDOMMonitoring() {
    console.log('Starting DOM monitoring...');
    let monitoringCount = 0;
    const maxMonitoring = 20; // 监控20次，每次间隔500ms，总共10秒

    const monitorInterval = setInterval(() => {
        monitoringCount++;

        const importantNotes = document.querySelector('.alert-warning');
        const paymentCard = document.querySelector('.payment-card');
        const mainContainer = document.querySelector('main .container');

        console.log(`DOM Monitor ${monitoringCount}: Important Notes=${!!importantNotes}, Payment Card=${!!paymentCard}`);

        if (mainContainer) {
            console.log(`DOM Monitor ${monitoringCount}: Main container HTML length=${mainContainer.innerHTML.length}`);
        }

        // 如果发现元素消失，记录详细信息
        if (!importantNotes || !paymentCard) {
            console.error(`DOM CHANGE DETECTED at monitor ${monitoringCount}:`);
            console.error('Important Notes missing:', !importantNotes);
            console.error('Payment Card missing:', !paymentCard);
            console.error('Current main container HTML:', mainContainer ? mainContainer.innerHTML.substring(0, 500) : 'CONTAINER MISSING');

            // 停止监控
            clearInterval(monitorInterval);
            return;
        }

        // 达到最大监控次数后停止
        if (monitoringCount >= maxMonitoring) {
            console.log('DOM monitoring completed - no issues detected');
            clearInterval(monitorInterval);
        }
    }, 500); // 每500ms检查一次
}





function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Success', 'Copied to clipboard', 'success');
    }).catch(() => {
        showNotification('Error', 'Failed to copy', 'error');
    });
}

function checkPaymentStatus(paymentId) {
    fetch(`/api/v1/payments/nowpayments/status/${paymentId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const status = data.data.payment_status;
                if (status === 'finished' || status === 'confirmed') {
                    showNotification('Success', 'Payment confirmed!', 'success');
                    setTimeout(() => {
                        window.location.href = '/orders/';
                    }, 2000);
                } else {
                    showNotification('Info', `Payment status: ${status}`, 'info');
                }
            } else {
                showNotification('Error', data.error || 'Failed to check status', 'error');
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
            showNotification('Error', 'Failed to check payment status', 'error');
        });
}

function showError(message) {
    document.querySelector('.container').innerHTML = `
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
                <div class="text-center">
                    <a href="/orders/" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Orders" %}
                    </a>
                </div>
            </div>
        </div>
    `;
}

// 禁用其他币种选项，只保留当前活跃支付的币种可选
function disableOtherCryptoOptions(activeCurrency) {
    const cryptoOptions = document.querySelectorAll('.crypto-option');

    cryptoOptions.forEach(option => {
        const crypto = option.getAttribute('data-crypto');
        if (crypto && crypto.toLowerCase() !== activeCurrency.toLowerCase()) {
            // 禁用其他币种
            option.style.opacity = '0.5';
            option.style.pointerEvents = 'none';
            option.style.cursor = 'not-allowed';

            // 添加禁用提示
            if (!option.querySelector('.disabled-overlay')) {
                const overlay = document.createElement('div');
                overlay.className = 'disabled-overlay';
                overlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    color: #666;
                    z-index: 10;
                `;
                overlay.innerHTML = '<span>Payment in progress</span>';
                option.style.position = 'relative';
                option.appendChild(overlay);
            }
        } else {
            // 确保当前币种保持可选状态
            option.style.opacity = '1';
            option.style.pointerEvents = 'auto';
            option.style.cursor = 'pointer';
        }
    });
}

// 重置币种选项状态（当用户重新进入页面时）
function resetCryptoOptions() {
    const cryptoOptions = document.querySelectorAll('.crypto-option');

    cryptoOptions.forEach(option => {
        option.style.opacity = '1';
        option.style.pointerEvents = 'auto';
        option.style.cursor = 'pointer';

        // 移除禁用覆盖层
        const overlay = option.querySelector('.disabled-overlay');
        if (overlay) {
            overlay.remove();
        }
    });

    // 重置活跃支付状态
    activePayment = null;
    isPaymentActive = false;
}

// 关闭支付窗口并返回到订单支付选择页面
function closePaymentWindow() {
    // 移除支付窗口遮罩
    removePaymentOverlay();

    // 重置支付状态
    resetCryptoOptions();

    // 重置选择状态
    selectedCrypto = null;
    selectedPaymentMethod = null;

    // 重置渲染状态
    isRenderingPaymentDetails = false;
    lastPaymentData = null;

    // 恢复原始支付选择页面
    restoreOriginalPaymentPage();
}

// 添加支付窗口遮罩
function addPaymentOverlay() {
    // 检查是否已经存在遮罩
    if (document.getElementById('payment-overlay')) {
        return;
    }

    const overlay = document.createElement('div');
    overlay.id = 'payment-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 998;
        backdrop-filter: blur(2px);
        pointer-events: none;
    `;

    // 添加遮罩到支付选择区域，而不是整个页面
    const paymentMethodSection = document.querySelector('.payment-card');
    if (paymentMethodSection) {
        paymentMethodSection.style.position = 'relative';
        paymentMethodSection.style.pointerEvents = 'none';
        paymentMethodSection.style.opacity = '0.5';
    }

    document.body.appendChild(overlay);

    // 🔧 修复：不禁用页面滚动，确保支付窗口内容完全可见
    // 原代码：document.body.style.overflow = 'hidden'; // 防止背景滚动
    // 新方案：自动滚动到支付窗口顶部，确保内容可见
    setTimeout(() => {
        scrollToPaymentWindow();
    }, 100);
}

// 移除支付窗口遮罩
function removePaymentOverlay() {
    const overlay = document.getElementById('payment-overlay');
    if (overlay) {
        overlay.remove();
    }

    // 恢复支付选择区域
    const paymentMethodSection = document.querySelector('.payment-card');
    if (paymentMethodSection) {
        paymentMethodSection.style.position = '';
        paymentMethodSection.style.pointerEvents = '';
        paymentMethodSection.style.opacity = '';
    }

    // 🔧 修复：不需要恢复overflow，因为我们没有禁用它
    // 原代码：document.body.style.overflow = ''; // 恢复滚动
}

// 🆕 新增：智能滚动到支付窗口，确保内容完全可见
function scrollToPaymentWindow() {
    const paymentCard = document.querySelector('.payment-card');
    if (!paymentCard) {
        return;
    }

    const rect = paymentCard.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    // 检查支付窗口是否完全可见
    const isFullyVisible = rect.top >= 0 && rect.bottom <= windowHeight;

    if (!isFullyVisible) {
        // 计算需要滚动的距离，确保支付窗口顶部有50px边距
        const targetScrollY = window.scrollY + rect.top - 50;

        // 平滑滚动到目标位置
        window.scrollTo({
            top: Math.max(0, targetScrollY), // 确保不滚动到负值
            behavior: 'smooth'
        });

        console.log('🔧 支付窗口滚动修复:', {
            originalTop: rect.top,
            originalBottom: rect.bottom,
            windowHeight: windowHeight,
            targetScrollY: targetScrollY,
            isFullyVisible: isFullyVisible
        });
    }
}

// 恢复原始支付选择页面
function restoreOriginalPaymentPage() {
    // 重新加载页面到支付选择状态
    window.location.reload();
}



// Web3 Wallet Payment Functions
let currentPaymentData = null;
let walletConnected = false;
let connectedWalletAddress = null;
let selectedCurrency = null;
let paymentAmount = null;
let paymentAddress = null;

// WalletConnect Configuration
const PAYMENT_WALLETCONNECT_PROJECT_ID = '1ae30962caec7ed05c3c1bfcd0f578a5';
const WALLETCONNECT_METADATA = {
    name: 'SaaS Platform Payment',
    description: 'Secure cryptocurrency payments',
    url: window.location.origin,
    icons: [window.location.origin + '/static/themes/theme_default/images/logo.png']
};

// WalletConnect v2 集成
let signClient = null;
let currentSession = null;
let currentProposal = null;

// 初始化WalletConnect v2 SignClient
async function initializeWalletConnect() {
    console.log('🔄 Initializing WalletConnect v2 SignClient...');

    try {
        // 检查WalletConnect v2 SDK是否加载
        console.log('🔍 检查WalletConnect v2 SDK加载状态...');

        // 尝试不同的全局变量名
        let SignClientClass = null;
        if (typeof window['@walletconnect/sign-client'] !== 'undefined') {
            SignClientClass = window['@walletconnect/sign-client'].SignClient;
            console.log('✅ 找到WalletConnect SignClient:', typeof SignClientClass);
        } else if (typeof window.SignClient !== 'undefined') {
            SignClientClass = window.SignClient;
            console.log('✅ 找到全局SignClient:', typeof SignClientClass);
        } else {
            console.error('❌ WalletConnect v2 SDK not loaded');
            console.log('🔍 可用的window键:', Object.keys(window).filter(key => key.includes('wallet')));
            return false;
        }

        // 初始化SignClient
        console.log('🔍 DEBUG: 初始化WalletConnect SignClient...');
        console.log('🔍 DEBUG: 项目ID:', PAYMENT_WALLETCONNECT_PROJECT_ID);
        console.log('🔍 DEBUG: 元数据:', WALLETCONNECT_METADATA);

        // 🔧 修复：添加完整的初始化配置
        signClient = await SignClientClass.init({
            projectId: PAYMENT_WALLETCONNECT_PROJECT_ID,
            metadata: WALLETCONNECT_METADATA,
            // 添加relay配置确保连接正常
            relayUrl: 'wss://relay.walletconnect.com',
            // 添加存储配置
            storageOptions: {
                database: 'walletconnect'
            }
        });

        console.log('✅ WalletConnect v2 SignClient initialized');
        console.log('🔍 DEBUG: SignClient实例:', signClient);

        // 🔍 测试WalletConnect Cloud连接
        try {
            console.log('🔍 DEBUG: 测试WalletConnect Cloud连接...');
            // 🔧 修复：安全访问relayer信息
            const coreInfo = {
                projectId: signClient.core?.projectId,
                relayUrl: signClient.core?.relayer?.relayUrl || signClient.core?.relayUrl,
                connected: signClient.core?.relayer?.connected || signClient.core?.connected
            };
            console.log('🔍 DEBUG: SignClient核心信息:', coreInfo);

            // 检查现有会话
            const existingSessions = signClient.session.getAll();
            console.log('🔍 DEBUG: 现有会话数量:', existingSessions.length);

            if (existingSessions.length > 0) {
                console.log('🔍 DEBUG: 现有会话:', existingSessions);
            }
        } catch (error) {
            console.error('❌ WalletConnect Cloud连接测试失败:', error);
        }

        // 暴露到全局以便调试和其他函数使用
        window.signClient = signClient;
        window.currentSession = currentSession;
        window.currentProposal = currentProposal;

        // 🔧 修复：设置正确的事件监听器
        console.log('🔄 设置WalletConnect事件监听器...');

        // 核心事件监听器
        console.log('🔄 注册session_proposal事件监听器...');
        signClient.on('session_proposal', onSessionProposal);

        console.log('🔄 注册session_request事件监听器...');
        signClient.on('session_request', onSessionRequest);

        console.log('🔄 注册session_delete事件监听器...');
        signClient.on('session_delete', onSessionDelete);

        console.log('🔄 注册session_expire事件监听器...');
        signClient.on('session_expire', onSessionExpire);

        console.log('🔄 注册session_update事件监听器...');
        signClient.on('session_update', onSessionUpdate);

        console.log('🔄 注册session_event事件监听器...');
        signClient.on('session_event', onSessionEvent);

        console.log('🔄 注册session_ping事件监听器...');
        signClient.on('session_ping', onSessionPing);

        // 🔧 修复：注册关键的session_approve事件监听器
        console.log('🔄 注册session_approve事件监听器...');
        signClient.on('session_approve', onSessionApprove);

        // 🔧 添加额外的调试事件监听器
        console.log('🔄 注册调试事件监听器...');

        signClient.on('session_reject', (error) => {
            console.log('⚠️ 收到session_reject事件:', error);
        });

        console.log('✅ 所有WalletConnect事件监听器已注册');

        // 🔧 使用全新的事件监听器注册方式
        try {
            console.log('🔄 尝试全新的WalletConnect v2事件监听器注册方式...');

            // 🔧 方法1：直接监听SignClient的内部事件
            if (signClient.core && signClient.core.pairing) {
                console.log('🔄 注册pairing事件监听器...');
                signClient.core.pairing.events.on('pairing_proposal', (proposal) => {
                    console.log('📱 收到pairing提案:', proposal);
                });
            }

            // 🔧 方法2：监听relayer事件
            if (signClient.core && signClient.core.relayer) {
                console.log('🔄 注册relayer事件监听器...');
                signClient.core.relayer.events.on('relayer_message', (message) => {
                    console.log('📨 收到relayer消息:', message);
                });
            }

            // 🔧 方法3：使用轮询方式检测会话变化
            console.log('🔄 启动会话轮询检测...');
            let lastSessionCount = 0;
            const sessionPolling = setInterval(() => {
                const currentSessionCount = signClient.session.length;
                if (currentSessionCount !== lastSessionCount) {
                    console.log('🎉 检测到会话数量变化:', lastSessionCount, '->', currentSessionCount);
                    lastSessionCount = currentSessionCount;

                    if (currentSessionCount > 0) {
                        console.log('🎉 发现新会话，手动触发处理...');
                        const sessions = signClient.session.getAll();
                        const latestSession = sessions[sessions.length - 1];
                        console.log('🔍 最新会话:', latestSession);

                        // 手动触发会话处理
                        currentSession = latestSession;
                        updateWalletConnectUI('connected', latestSession);
                        handleSessionApproval(latestSession);

                        clearInterval(sessionPolling);
                    }
                }
            }, 1000);

            // 🔧 方法4：监听Promise状态变化
            console.log('🔄 启动Promise状态监控...');
            const promiseMonitoring = setInterval(() => {
                // 检查是否有新的会话
                if (signClient.session.length > 0 && !currentSession) {
                    console.log('🎉 Promise监控检测到新会话！');
                    const sessions = signClient.session.getAll();
                    const latestSession = sessions[sessions.length - 1];

                    currentSession = latestSession;
                    updateWalletConnectUI('connected', latestSession);
                    handleSessionApproval(latestSession);

                    clearInterval(promiseMonitoring);
                }
            }, 500);

            // 🔧 方法5：监听pairing状态变化
            console.log('🔄 启动pairing状态监控...');
            const pairingMonitoring = setInterval(() => {
                try {
                    if (signClient.core && signClient.core.pairing) {
                        const pairings = signClient.core.pairing.getAll();
                        if (pairings.length > 0) {
                            console.log('🔍 检测到pairing:', pairings);
                            const activePairings = pairings.filter(p => p.active);
                            if (activePairings.length > 0) {
                                console.log('🎉 发现活跃的pairing，检查会话...');
                                // 强制检查会话
                                setTimeout(() => {
                                    if (signClient.session.length > 0 && !currentSession) {
                                        console.log('🎉 Pairing监控检测到新会话！');
                                        const sessions = signClient.session.getAll();
                                        const latestSession = sessions[sessions.length - 1];

                                        currentSession = latestSession;
                                        updateWalletConnectUI('connected', latestSession);
                                        handleSessionApproval(latestSession);

                                        clearInterval(pairingMonitoring);
                                    }
                                }, 2000);
                            }
                        }
                    }
                } catch (error) {
                    console.log('⚠️ Pairing监控出错:', error);
                }
            }, 1000);

            // 30秒后停止轮询
            setTimeout(() => {
                clearInterval(sessionPolling);
                clearInterval(promiseMonitoring);
                clearInterval(pairingMonitoring);
                console.log('⏰ 轮询监控已停止');
            }, 30000);

            // 🔧 方法6：监听底层连接状态变化
            console.log('🔄 启动底层连接状态监控...');
            const connectionMonitoring = setInterval(() => {
                try {
                    // 检查SignClient的连接状态
                    if (signClient.core && signClient.core.relayer) {
                        const isConnected = signClient.core.relayer.connected;
                        if (isConnected && signClient.session.length > 0 && !currentSession) {
                            console.log('🎉 底层连接监控检测到新会话！');
                            const sessions = signClient.session.getAll();
                            const latestSession = sessions[sessions.length - 1];

                            currentSession = latestSession;
                            updateWalletConnectUI('connected', latestSession);
                            handleSessionApproval(latestSession);

                            clearInterval(connectionMonitoring);
                        }
                    }

                    // 检查window对象上的WalletConnect状态
                    if (window.ethereum && window.ethereum.isConnected && window.ethereum.isConnected()) {
                        console.log('🔍 检测到window.ethereum连接状态');
                        if (signClient.session.length > 0 && !currentSession) {
                            console.log('🎉 Window.ethereum监控检测到新会话！');
                            const sessions = signClient.session.getAll();
                            const latestSession = sessions[sessions.length - 1];

                            currentSession = latestSession;
                            updateWalletConnectUI('connected', latestSession);
                            handleSessionApproval(latestSession);

                            clearInterval(connectionMonitoring);
                        }
                    }
                } catch (error) {
                    console.log('⚠️ 底层连接监控出错:', error);
                }
            }, 500);

            // 添加到清理列表
            setTimeout(() => {
                clearInterval(connectionMonitoring);
            }, 30000);

            console.log('✅ 全新事件监听器注册完成');

        } catch (error) {
            console.log('⚠️ 注册全新事件监听器时出错:', error);
        }

        console.log('✅ WalletConnect v2初始化完成，事件监听器已就绪');

        return true;
    } catch (error) {
        console.error('❌ Failed to initialize WalletConnect v2:', error);
        return false;
    }
}



// WalletConnect v2 事件处理器
function onSessionProposal(proposal) {
    console.log('📱 收到会话提案:', proposal);
    currentProposal = proposal;

    // 🔧 修复：不要自动批准会话！
    // 根据WalletConnect v2官方文档，session_proposal事件表示钱包已收到提案
    // 此时应该等待钱包用户决定是否批准，而不是dApp端自动批准
    console.log('⏳ 等待钱包用户批准连接...');

    // 更新UI显示等待状态
    updateWalletConnectUI('waiting');
}

// 🔧 添加关键的session_approve事件处理器
function onSessionApprove(session) {
    console.log('🎉 WalletConnect会话已批准!', session);
    console.log('🔍 会话详细信息:', {
        topic: session.topic,
        namespaces: session.namespaces,
        accounts: session.namespaces?.eip155?.accounts
    });

    currentSession = session;

    // 触发钱包连接成功回调
    if (typeof onWalletConnected === 'function') {
        try {
            // 从会话中提取账户地址
            if (!session.namespaces?.eip155?.accounts?.length) {
                console.error('❌ 会话中没有找到账户信息');
                return;
            }

            const account = session.namespaces.eip155.accounts[0];
            console.log('🔍 原始账户字符串:', account);

            const [namespace, chainId, address] = account.split(':');
            console.log('🔍 解析结果:', { namespace, chainId, address });

            if (!address) {
                console.error('❌ 无法解析用户地址');
                return;
            }

            console.log('🔄 触发钱包连接成功回调...');
            console.log('🔄 用户地址:', address);
            console.log('🔄 链ID:', chainId);

            // 调用钱包连接成功回调
            onWalletConnected({
                account: address,
                chainId: parseInt(chainId),
                provider: 'walletconnect',
                session: session
            });
        } catch (error) {
            console.error('❌ 处理会话批准时出错:', error);
        }
    } else {
        console.error('❌ onWalletConnected函数未定义');
    }
}

function onSessionRequest(request) {
    console.log('📱 收到会话请求:', request);
    // 处理交易请求
    handleSessionRequest(request);
}

function onSessionDelete(args) {
    console.log('📱 会话已删除:', args);
    currentSession = null;

    // 触发钱包断开连接回调
    if (typeof onWalletDisconnected === 'function') {
        onWalletDisconnected();
    }
}

function onSessionExpire(args) {
    console.log('📱 会话已过期:', args);
    currentSession = null;

    // 触发钱包断开连接回调
    if (typeof onWalletDisconnected === 'function') {
        onWalletDisconnected();
    }
}

function onSessionUpdate(session) {
    console.log('📱 会话已更新:', session);
    currentSession = session;
}

function onSessionEvent(event) {
    console.log('📱 收到会话事件:', event);

    // 处理账户变更事件
    if (event.params.event.name === 'accountsChanged') {
        console.log('🔄 账户已变更:', event.params.event.data);
        // 可以在这里更新UI显示的账户信息
    }

    // 处理链变更事件
    if (event.params.event.name === 'chainChanged') {
        console.log('🔄 链已变更:', event.params.event.data);
        // 可以在这里更新UI显示的网络信息
    }
}

function onSessionPing(event) {
    console.log('📱 收到会话ping:', event);
    // 会话保活，通常不需要特殊处理
}

// 🔧 简化的Trust Wallet兼容会话批准
async function approveSession(proposal) {
    try {
        console.log('✅ 正在批准WalletConnect会话...');
        console.log('🔍 会话提案:', proposal);

        // 🔧 Trust Wallet兼容：简化命名空间处理
        const requiredNamespaces = proposal.params.requiredNamespaces;
        console.log('🔍 提案所需命名空间:', requiredNamespaces);

        // 🔧 构建简化的命名空间响应，专门为Trust Wallet优化
        const namespaces = {};

        // 只处理必需的命名空间，避免复杂的可选命名空间
        for (const [key, namespace] of Object.entries(requiredNamespaces)) {
            console.log(`🔧 处理命名空间 ${key}:`, namespace);

            namespaces[key] = {
                accounts: [], // 让钱包自动填充账户
                methods: namespace.methods || [
                    'eth_sendTransaction',
                    'personal_sign'
                ],
                events: namespace.events || ['chainChanged', 'accountsChanged'],
                chains: namespace.chains || []
            };
        }

        console.log('🔧 Trust Wallet兼容的命名空间响应:', namespaces);

        // 🔧 使用简化的批准方式
        const session = await signClient.approve({
            id: proposal.id,
            namespaces: namespaces
        });

        currentSession = session;
        console.log('✅ WalletConnect会话已批准:', session);

        // 🔧 修复：更新UI显示连接成功
        updateWalletConnectUI('connected', session);

        // 🔧 修复：检查会话中是否有账户信息
        if (session && session.namespaces && session.namespaces.eip155 && session.namespaces.eip155.accounts.length > 0) {
            // 从会话中提取账户地址
            const account = session.namespaces.eip155.accounts[0];
            const [namespace, chainId, address] = account.split(':');

            console.log('🔄 触发钱包连接成功回调...');
            console.log('🔄 用户地址:', address);
            console.log('🔄 链ID:', chainId);

            // 🔧 新增：验证网络兼容性
            const requiredNetwork = getRequiredNetworkForCurrency(window.currentPaymentState?.currency);
            if (requiredNetwork && parseInt(chainId) !== requiredNetwork.id) {
                console.log(`⚠️ 网络不匹配: 当前=${chainId}, 需要=${requiredNetwork.id}`);

                // 显示网络切换提示
                updateWalletConnectUI('network_mismatch', session, `Please switch to ${requiredNetwork.name} (Chain ID: ${requiredNetwork.id})`);

                // 尝试自动切换网络
                try {
                    await switchNetworkViaWalletConnect(requiredNetwork);
                    console.log('✅ 网络切换成功');
                } catch (error) {
                    console.error('❌ 网络切换失败:', error);
                    return;
                }
            }

            // 调用钱包连接成功回调（使用正确的参数格式）
            if (typeof onWalletConnected === 'function') {
                onWalletConnected({
                    account: address,
                    chainId: parseInt(chainId),
                    provider: 'walletconnect',
                    session: session
                });
            }
        } else {
            console.log('⚠️ 会话已建立，但尚未获取到账户信息，等待钱包提供...');
            // 会话建立了，但账户信息可能稍后通过其他事件提供
        }

    } catch (error) {
        console.error('❌ 批准会话失败:', error);
        updateWalletConnectUI('error', null, error.message);
    }
}

// 🔧 新增：更新WalletConnect UI状态
function updateWalletConnectUI(status, session = null, errorMessage = null) {
    const qrContainer = document.getElementById('qr-code-container');
    if (!qrContainer) return;

    console.log('🔄 更新WalletConnect UI状态:', status);

    switch (status) {
        case 'connected':
            qrContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #28a745; font-size: 24px; margin-bottom: 10px;">✅</div>
                    <h6 style="color: #28a745; margin-bottom: 10px;">钱包已连接</h6>
                    <p style="color: #6c757d; font-size: 14px;">请在您的钱包中确认支付</p>
                </div>
            `;
            break;

        case 'error':
            qrContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #dc3545; font-size: 24px; margin-bottom: 10px;">❌</div>
                    <h6 style="color: #dc3545; margin-bottom: 10px;">连接失败</h6>
                    <p style="color: #6c757d; font-size: 14px;">${errorMessage || '请重试'}</p>
                </div>
            `;
            break;

        case 'waiting':
            qrContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #007bff; font-size: 24px; margin-bottom: 10px;">⏳</div>
                    <h6 style="color: #007bff; margin-bottom: 10px;">等待连接</h6>
                    <p style="color: #6c757d; font-size: 14px;">请使用钱包扫描二维码</p>
                </div>
            `;
            break;

        case 'network_mismatch':
            qrContainer.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="color: #dc3545; font-size: 24px; margin-bottom: 10px;">⚠️</div>
                    <h6 style="color: #dc3545; margin-bottom: 10px;">网络不匹配</h6>
                    <p style="color: #6c757d; font-size: 14px;">${errorMessage || '请在钱包中切换到正确的网络'}</p>
                    <button onclick="retryWalletConnection()" class="btn btn-primary btn-sm mt-2">
                        重新连接
                    </button>
                </div>
            `;
            break;
    }
}

// 🔧 修复：处理会话更新事件
function onSessionUpdate({ topic, params }) {
    console.log('🔄 会话更新:', { topic, params });
    const { namespaces } = params;
    const session = window.signClient.session.get(topic);
    if (session) {
        const updatedSession = { ...session, namespaces };
        currentSession = updatedSession;
        console.log('✅ 会话已更新:', updatedSession);
    }
}

// 🔧 新增：清理过期或无效的WalletConnect会话
function cleanupInvalidSessions() {
    console.log('🧹 清理无效的WalletConnect会话...');

    try {
        if (window.signClient && window.signClient.session) {
            const sessions = window.signClient.session.getAll();
            console.log(`🔍 发现 ${sessions.length} 个现有会话`);

            sessions.forEach(async (session, index) => {
                try {
                    // 检查会话是否仍然有效
                    const isActive = session.expiry > Date.now() / 1000;
                    console.log(`🔍 会话 ${index}: topic=${session.topic}, active=${isActive}`);

                    if (!isActive) {
                        console.log(`🗑️ 删除过期会话: ${session.topic}`);
                        await window.signClient.session.delete(session.topic, {
                            code: 6000,
                            message: "Session expired"
                        });
                    }
                } catch (error) {
                    console.error(`❌ 清理会话失败: ${error.message}`);
                }
            });
        }

        // 清理全局状态
        currentSession = null;
        window.connectedWalletSession = null;

        console.log('✅ 会话清理完成');

    } catch (error) {
        console.error('❌ 清理会话时出错:', error);
    }
}

// 🔧 新增：检查并处理网络兼容性
function validateSessionNetworkCompatibility(session, requiredChainId) {
    console.log('🔍 验证会话网络兼容性...');

    if (!session || !session.namespaces || !session.namespaces.eip155) {
        console.log('⚠️ 会话缺少网络信息');
        return false;
    }

    const supportedChains = session.namespaces.eip155.chains || [];
    const requiredChain = `eip155:${requiredChainId}`;

    console.log(`🔍 支持的链: ${supportedChains.join(', ')}`);
    console.log(`🔍 需要的链: ${requiredChain}`);

    const isCompatible = supportedChains.includes(requiredChain);
    console.log(`🔍 网络兼容性: ${isCompatible ? '✅ 兼容' : '❌ 不兼容'}`);

    return isCompatible;
}

// 🔧 新增：根据币种获取所需网络信息
function getRequiredNetworkForCurrency(currency) {
    console.log(`🔍 获取币种 ${currency} 的网络信息...`);

    if (!currency) {
        console.log('⚠️ 币种为空');
        return null;
    }

    // 定义币种与网络的映射关系
    const currencyNetworkMap = {
        // BSC网络币种
        'FDUSDBSC': { id: 56, name: 'BNB Smart Chain' },
        'BUSDBSC': { id: 56, name: 'BNB Smart Chain' },
        'USDTBSC': { id: 56, name: 'BNB Smart Chain' },
        'USDCBSC': { id: 56, name: 'BNB Smart Chain' },
        'BNB': { id: 56, name: 'BNB Smart Chain' },

        // Polygon网络币种
        'USDCMATIC': { id: 137, name: 'Polygon Mainnet' },
        'USDTMATIC': { id: 137, name: 'Polygon Mainnet' },
        'MATIC': { id: 137, name: 'Polygon Mainnet' },

        // Ethereum网络币种
        'USDT': { id: 1, name: 'Ethereum Mainnet' },
        'USDC': { id: 1, name: 'Ethereum Mainnet' },
        'DAI': { id: 1, name: 'Ethereum Mainnet' },
        'ETH': { id: 1, name: 'Ethereum Mainnet' },

        // Tron网络币种（非EVM）
        'TRX': { id: 728126428, name: 'Tron Mainnet' },
        'USDTTRC20': { id: 728126428, name: 'Tron Mainnet' },

        // Bitcoin（非EVM）
        'BTC': null, // Bitcoin不需要网络切换
        'LTC': null, // Litecoin不需要网络切换
    };

    const network = currencyNetworkMap[currency.toUpperCase()];
    console.log(`🔍 币种 ${currency} 对应网络:`, network);

    return network;
}

// 🔧 新增：重新连接钱包函数
async function retryWalletConnection() {
    console.log('🔄 重新连接钱包...');

    try {
        // 清理现有会话
        await cleanupInvalidSessions();

        // 重置UI状态
        updateWalletConnectUI('waiting');

        // 重新创建WalletConnect会话
        await connectWalletConnect();

    } catch (error) {
        console.error('❌ 重新连接失败:', error);
        updateWalletConnectUI('error', null, '重新连接失败，请刷新页面重试');
    }
}

// 🔧 新增：页面加载时清理过期会话
function initializePaymentPage() {
    console.log('🚀 初始化支付页面...');

    // 清理可能存在的过期会话
    if (window.signClient) {
        cleanupInvalidSessions();
    }

    // 重置全局状态
    currentSession = null;
    window.connectedWalletSession = null;

    console.log('✅ 支付页面初始化完成');
}

// 更新连接状态UI
function updateConnectionStatus(status) {
    const connectBtn = document.querySelector('button[onclick*="connectWalletForPayment"]');
    const statusText = document.querySelector('.wallet-connection-status');

    if (connectBtn) {
        switch (status) {
            case 'connected':
                connectBtn.innerHTML = '<i class="fas fa-check me-2"></i>Connected';
                connectBtn.disabled = true;
                connectBtn.classList.add('btn-success');
                connectBtn.classList.remove('btn-primary');
                break;
            case 'connecting':
                connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting...';
                connectBtn.disabled = true;
                break;
            case 'disconnected':
                connectBtn.innerHTML = '<i class="fas fa-link me-2"></i>Connect Wallet';
                connectBtn.disabled = false;
                connectBtn.classList.add('btn-primary');
                connectBtn.classList.remove('btn-success');
                break;
        }
    }

    console.log('🔄 UI状态已更新:', status);
}

// 处理会话请求（交易等）
async function handleSessionRequest(request) {
    try {
        console.log('🔄 处理会话请求:', request);

        // 这里可以处理不同类型的请求
        // 对于支付场景，主要是eth_sendTransaction

        // 暂时自动批准所有请求（生产环境需要用户确认）
        const response = { id: request.id, result: "0x" };

        await signClient.respond({
            topic: request.topic,
            response: response
        });

        console.log('✅ 会话请求已处理');

    } catch (error) {
        console.error('❌ 处理会话请求失败:', error);
    }
}

// 创建Ethers提供者
function createEthersProvider(session) {
    // 这里应该创建一个基于WalletConnect会话的Ethers提供者
    // 暂时返回null，后续可以实现
    console.log('🔄 创建Ethers提供者 (待实现)');
    return null;
}

// 创建真正的WalletConnect v2会话并生成URI
async function createRealWalletConnectSession() {
    console.log('🔄 创建真正的WalletConnect v2会话...');

    try {
        if (!signClient) {
            console.log('🔄 SignClient未初始化，正在初始化...');
            const initialized = await initializeWalletConnect();
            if (!initialized) {
                throw new Error('Failed to initialize WalletConnect v2');
            }
        }

        // 🔧 修复：根据当前支付币种动态设置链配置
        const paymentCurrency = currentPaymentData.pay_currency;
        const networkConfig = window.dynamicCurrencyConfigs && window.dynamicCurrencyConfigs[paymentCurrency.toUpperCase()];
        const currentChainId = networkConfig ? networkConfig.id : 1; // 根据币种获取正确的链ID
        const chainNamespace = `eip155:${currentChainId}`;

        console.log('🔍 支付币种:', paymentCurrency);
        console.log('🔍 DEBUG: window.dynamicCurrencyConfigs存在:', !!window.dynamicCurrencyConfigs);
        console.log('🔍 DEBUG: 动态配置内容:', window.dynamicCurrencyConfigs);
        console.log('🔍 DEBUG: 查找键:', paymentCurrency.toUpperCase());
        console.log('🔍 网络配置:', networkConfig);
        console.log('🔍 当前支付链ID:', currentChainId);
        console.log('🔍 链命名空间:', chainNamespace);

        // 🔧 创建Trust Wallet兼容的会话提案
        console.log('🔄 创建Trust Wallet兼容的WalletConnect配置...');

        // 🔧 修复：创建兼容多链的WalletConnect配置
        const connectConfig = {
            requiredNamespaces: {
                eip155: {
                    methods: [
                        'eth_sendTransaction',
                        'personal_sign'
                    ],
                    chains: [chainNamespace], // 要求当前支付所需的链
                    events: ['chainChanged', 'accountsChanged']
                }
            },
            optionalNamespaces: {
                eip155: {
                    methods: [
                        'eth_sendTransaction',
                        'personal_sign',
                        'wallet_switchEthereumChain',
                        'wallet_addEthereumChain',
                        'eth_signTransaction',
                        'eth_sign',
                        'eth_signTypedData'
                    ],
                    chains: [
                        'eip155:1',   // 以太坊主网
                        'eip155:56',  // BSC
                        'eip155:137', // Polygon
                        'eip155:42161', // Arbitrum
                        'eip155:10'   // Optimism
                    ],
                    events: ['chainChanged', 'accountsChanged']
                }
            }
        };

        console.log('🔍 WalletConnect配置:', connectConfig);

        const { uri, approval } = await signClient.connect(connectConfig);

        console.log('✅ WalletConnect v2 URI已创建:', uri);

        // 🔧 修复：正确处理approval对象（可能是Promise或函数）
        console.log('🔄 设置approval处理器...');
        console.log('🔍 approval类型:', typeof approval);

        // 根据WalletConnect v2实际实现，approval可能是Promise或函数
        if (approval && typeof approval.then === 'function') {
            // approval是Promise
            console.log('✅ approval是Promise，等待钱包用户批准连接...');

            approval
                .then(session => {
                    console.log('🎉 钱包用户已批准连接!', session);
                    currentSession = session;
                    updateWalletConnectUI('connected', session);
                    handleSessionApproval(session);
                })
                .catch(error => {
                    console.log('⚠️ 用户拒绝连接或连接失败:', error.message);
                    updateWalletConnectUI('error', null, error.message);
                    handleConnectionError(error);
                });

        } else if (approval && typeof approval === 'function') {
            // approval是函数，需要调用获取Promise
            console.log('✅ approval是函数，调用获取Promise...');

            try {
                const sessionPromise = approval();
                console.log('🔍 调用approval()返回:', typeof sessionPromise);
                console.log('🔍 sessionPromise对象:', sessionPromise);

                if (sessionPromise && typeof sessionPromise.then === 'function') {
                    console.log('🔄 设置sessionPromise的then/catch处理器...');

                    // 🔧 添加Promise状态监控
                    console.log('🔍 Promise状态监控开始...');

                    sessionPromise
                        .then(session => {
                            console.log('🎉 sessionPromise resolved! 钱包用户已批准连接!', session);
                            console.log('🔍 DEBUG: session对象详情:', session);
                            console.log('🔍 DEBUG: session.topic:', session?.topic);
                            console.log('🔍 DEBUG: session.namespaces:', session?.namespaces);

                            currentSession = session;
                            updateWalletConnectUI('connected', session);
                            handleSessionApproval(session);
                        })
                        .catch(error => {
                            console.log('⚠️ sessionPromise rejected! 用户拒绝连接或连接失败:', error);
                            console.log('🔍 DEBUG: error详情:', error);
                            console.log('🔍 DEBUG: error.message:', error?.message);
                            console.log('🔍 DEBUG: error.stack:', error?.stack);
                            updateWalletConnectUI('error', null, error.message);
                            handleConnectionError(error);
                        })
                        .finally(() => {
                            console.log('🔄 sessionPromise finally块执行');
                        });

                    // 🔧 添加超时监控
                    setTimeout(() => {
                        console.log('⏰ 30秒超时检查：sessionPromise状态');
                        console.log('🔍 currentSession:', !!currentSession);
                        console.log('🔍 window.connectedWalletSession:', !!window.connectedWalletSession);
                        if (!currentSession) {
                            console.log('⚠️ 30秒后仍未收到会话批准，可能存在问题');
                        }
                    }, 30000);
                } else {
                    console.error('❌ approval()返回的不是Promise:', typeof sessionPromise);
                    throw new Error('approval() did not return a Promise');
                }
            } catch (error) {
                console.error('❌ 调用approval()失败:', error);
                updateWalletConnectUI('error', null, error.message);
                handleConnectionError(error);
            }

        } else {
            console.error('❌ approval类型无效:', typeof approval);
            throw new Error('Invalid approval object from WalletConnect');
        }

        console.log('🔄 WalletConnect URI已准备就绪，等待用户扫码连接...');

        // 🔧 添加定时器检查会话状态
        const sessionCheckInterval = setInterval(() => {
            console.log('🔍 定时检查会话状态...');
            console.log('🔍 currentSession:', !!currentSession);
            console.log('🔍 window.connectedWalletSession:', !!window.connectedWalletSession);
            console.log('🔍 signClient.session.length:', signClient?.session?.length || 0);

            // 🔧 强制检查所有可能的连接状态
            try {
                // 方法1：检查SignClient是否有新会话
                if (signClient && signClient.session && signClient.session.length > 0) {
                    console.log('🎉 发现SignClient中有会话!', signClient.session);
                    const latestSession = signClient.session[signClient.session.length - 1];
                    console.log('🔍 最新会话:', latestSession);

                    if (!currentSession && latestSession) {
                        console.log('🔧 手动触发会话批准处理...');
                        clearInterval(sessionCheckInterval);
                        handleSessionApproval(latestSession);
                        return;
                    }
                }

                // 方法2：检查是否有隐藏的会话
                if (signClient && signClient.session && typeof signClient.session.getAll === 'function') {
                    const allSessions = signClient.session.getAll();
                    if (allSessions.length > 0 && !currentSession) {
                        console.log('🎉 强制检查发现隐藏会话！', allSessions);
                        const latestSession = allSessions[allSessions.length - 1];

                        clearInterval(sessionCheckInterval);
                        handleSessionApproval(latestSession);
                        return;
                    }
                }

                // 方法3：检查pairing状态
                if (signClient && signClient.core && signClient.core.pairing) {
                    const pairings = signClient.core.pairing.getAll();
                    const activePairings = pairings.filter(p => p.active);
                    if (activePairings.length > 0) {
                        console.log('🔍 发现活跃pairing，强制刷新会话...', activePairings);
                        // 尝试强制刷新会话列表
                        setTimeout(() => {
                            const sessions = signClient.session.getAll();
                            if (sessions.length > 0 && !currentSession) {
                                console.log('🎉 强制刷新后发现会话！', sessions);
                                const latestSession = sessions[sessions.length - 1];

                                clearInterval(sessionCheckInterval);
                                handleSessionApproval(latestSession);
                            }
                        }, 1000);
                    }
                }

                // 方法4：检查全局WalletConnect状态
                if (window.WalletConnect || window.walletConnect) {
                    console.log('🔍 检查全局WalletConnect状态...');
                    const globalWC = window.WalletConnect || window.walletConnect;
                    if (globalWC && globalWC.session && !currentSession) {
                        console.log('🎉 全局WalletConnect发现会话！', globalWC.session);
                        clearInterval(sessionCheckInterval);
                        handleSessionApproval(globalWC.session);
                        return;
                    }
                }

                // 方法5：检查底层连接状态
                if (signClient && signClient.core && signClient.core.relayer) {
                    const isConnected = signClient.core.relayer.connected;
                    if (isConnected) {
                        console.log('🔍 底层relayer已连接，强制检查会话...');
                        const sessions = signClient.session.getAll();
                        if (sessions.length > 0 && !currentSession) {
                            console.log('🎉 底层连接监控检测到新会话！', sessions);
                            const latestSession = sessions[sessions.length - 1];

                            clearInterval(sessionCheckInterval);
                            handleSessionApproval(latestSession);
                            return;
                        }
                    }
                }
            } catch (error) {
                console.log('⚠️ 强制检查出错:', error);
            }
        }, 2000); // 每2秒检查一次

        // 30秒后停止检查
        setTimeout(() => {
            clearInterval(sessionCheckInterval);
            console.log('🔄 会话状态检查已停止');
        }, 30000);

        return uri;

    } catch (error) {
        console.error('❌ 创建WalletConnect v2会话失败:', error);
        return null;
    }
}

// 🔧 新增：处理会话批准成功
function handleSessionApproval(session) {
    console.log('🎉 处理会话批准成功:', session);

    // 🔧 关键修复：确保会话正确保存到全局变量
    currentSession = session;
    window.connectedWalletSession = session;
    window.signClient = signClient;

    console.log('🔍 DEBUG: 会话已保存到全局变量');
    console.log('🔍 DEBUG: currentSession:', !!currentSession);
    console.log('🔍 DEBUG: window.connectedWalletSession:', !!window.connectedWalletSession);
    console.log('🔍 DEBUG: window.signClient:', !!window.signClient);

    // 🔧 关键修复：触发钱包连接成功回调
    if (session && session.namespaces && session.namespaces.eip155) {
        const account = session.namespaces.eip155.accounts[0];
        const [namespace, chainId, address] = account.split(':');

        console.log('🎉 WalletConnect连接成功!');
        console.log('🔄 用户地址:', address);
        console.log('🔄 链ID:', chainId);

        // 立即触发钱包连接成功回调
        onWalletConnected({
            account: address,
            chainId: parseInt(chainId),
            provider: 'walletconnect',
            session: session
        });
    } else {
        console.error('❌ 会话数据格式异常:', session);
    }
}

// 🔧 新增：处理连接错误
function handleConnectionError(error) {
    console.error('❌ WalletConnect连接错误:', error);

    // 更新UI显示连接失败
    const statusDiv = document.getElementById('wallet-connection-status');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-warning mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Connection cancelled or failed. Please try again.
            </div>
        `;
    }
}

// Generate QR code for payment using backend-generated QR code
function generatePaymentQRCode() {
    const qrContainer = document.getElementById('qr-code-container');
    if (!qrContainer) {
        console.error('QR code container not found');
        return;
    }

    // Clear existing QR code
    qrContainer.innerHTML = '';

    // Show loading state
    qrContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="small text-muted mt-2">Loading QR code...</div>
        </div>
    `;

    // 🔧 修改：直接显示WalletConnect二维码，不使用后端生成的支付二维码
    console.log('🔄 准备显示WalletConnect二维码');

    // 创建占位符，等待WalletConnect会话创建
    const qrContainerElement = document.querySelector('#qr-code-container');
    if (qrContainerElement) {
        qrContainerElement.innerHTML = `
            <div style="width: 220px; height: 220px; margin: 0 auto; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6; border-radius: 8px; background-color: #f8f9fa;">
                <div style="text-align: center;">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div style="margin-top: 10px; font-size: 14px; color: #6c757d;">
                        Generating WalletConnect QR Code...
                    </div>
                </div>
            </div>
        `;
        console.log('✅ WalletConnect二维码占位符已显示');
    } else {
        console.error('QR code container not found');
        showQRCodeFallback();
    }

    // Fallback when QR code is not available
    function showQRCodeFallback() {
        const paymentAddress = window.paymentData?.pay_address || 'Address not available';
        const paymentAmount = window.paymentData?.pay_amount || 'Amount not available';
        const payCurrency = window.paymentData?.pay_currency || 'Currency not available';

        qrContainer.innerHTML = `
            <div class="text-center">
                <div class="alert alert-info p-2 mb-2">
                    <i class="fas fa-info-circle me-1"></i>
                    <small>Manual Payment Information</small>
                </div>
                <div class="small text-muted">
                    <strong>Payment Details:</strong><br>
                    <strong>Amount:</strong> ${paymentAmount} ${payCurrency}<br>
                    <strong>Address:</strong><br>
                    <textarea class="form-control form-control-sm mt-1" rows="2" readonly style="font-size: 10px;">${paymentAddress}</textarea>
                    <div class="mt-2">
                        <small class="text-muted">Copy the address above and use it in your wallet app</small>
                    </div>
                </div>
            </div>
        `;
    }
}

// 保存支付状态，确保在钱包连接过程中不丢失
function savePaymentState(paymentData) {
    console.log('🔍 savePaymentState 被调用，原始数据:', paymentData);

    currentPaymentData = paymentData;
    selectedCurrency = paymentData.pay_currency;
    paymentAmount = paymentData.pay_amount;
    paymentAddress = paymentData.pay_address;

    // 🔍 验证关键数据
    console.log('🔍 支付状态验证:', {
        pay_currency: paymentData.pay_currency,
        pay_amount: paymentData.pay_amount,
        pay_amount_type: typeof paymentData.pay_amount,
        pay_amount_valid: !!(paymentData.pay_amount && paymentData.pay_amount !== '0'),
        pay_address: paymentData.pay_address
    });

    // Set global paymentData for QR code generation
    window.paymentData = paymentData;

    // 🔧 修复：正确设置当前支付状态，确保币种识别正确
    window.currentPaymentState = {
        currency: selectedCurrency,
        amount: paymentAmount,
        address: paymentAddress,
        paymentId: paymentData.payment_id
    };

    console.log('✅ Payment state saved:', {
        currency: selectedCurrency,
        amount: paymentAmount,
        address: paymentAddress,
        hasQRCode: !!paymentData.qr_code
    });

    // 检查并处理WalletConnect占位符
    handleWalletConnectSessionAfterPaymentLoad();
}

// 验证支付状态是否完整
function validatePaymentState() {
    console.log('🔍 validatePaymentState 检查中...');

    const isValid = currentPaymentData &&
                   selectedCurrency &&
                   paymentAmount &&
                   paymentAddress;

    console.log('🔍 支付状态详细检查:', {
        currentPaymentData: !!currentPaymentData,
        currentPaymentData_details: currentPaymentData,
        selectedCurrency: !!selectedCurrency,
        selectedCurrency_value: selectedCurrency,
        paymentAmount: !!paymentAmount,
        paymentAmount_value: paymentAmount,
        paymentAmount_type: typeof paymentAmount,
        paymentAddress: !!paymentAddress,
        paymentAddress_value: paymentAddress,
        isValid: isValid
    });

    if (!isValid) {
        console.error('❌ Payment state validation failed!');

        // 尝试从currentPaymentData重新获取数据
        if (currentPaymentData) {
            console.log('🔧 尝试从currentPaymentData重新设置状态...');
            selectedCurrency = currentPaymentData.pay_currency;
            paymentAmount = currentPaymentData.pay_amount;
            paymentAddress = currentPaymentData.pay_address;

            console.log('🔧 重新设置后的状态:', {
                selectedCurrency,
                paymentAmount,
                paymentAddress
            });

            // 重新验证
            const revalidated = selectedCurrency && paymentAmount && paymentAddress;
            console.log('🔧 重新验证结果:', revalidated);
            return revalidated;
        }
    }

    return isValid;
}

async function connectWalletForPayment() {
    console.log('🔄 开始钱包连接流程...');

    // 🔧 修复：如果没有支付数据，先创建支付
    if (!currentPaymentData) {
        console.log('🔄 没有支付数据，先创建支付...');

        // 检查是否已选择币种
        if (!selectedCrypto) {
            showNotification('Error', 'Please select a cryptocurrency first', 'error');
            return;
        }

        // 先创建支付
        try {
            await processPayment();
            // 等待支付创建完成
            await new Promise(resolve => setTimeout(resolve, 2000));

            if (!currentPaymentData) {
                console.error('❌ 支付创建失败，无法继续连接钱包');
                showNotification('Error', 'Failed to create payment. Please try again.', 'error');
                return;
            }
        } catch (error) {
            console.error('❌ 创建支付时出错:', error);
            showNotification('Error', 'Failed to create payment. Please try again.', 'error');
            return;
        }
    }

    // Update UI to show connecting state
    const connectBtn = document.getElementById('wallet-connect-btn');
    const statusDiv = document.getElementById('wallet-connection-status');

    if (connectBtn) {
        connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting...';
        connectBtn.disabled = true;
    }

    // 优先尝试连接浏览器钱包（MetaMask等），然后再使用WalletConnect
    if (window.ethereum && currentPaymentData) {
        console.log('🔄 检测到浏览器钱包，尝试连接MetaMask/浏览器钱包...');
        try {
            // 请求连接MetaMask
            const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
            if (accounts && accounts.length > 0) {
                console.log('✅ 浏览器钱包连接成功:', accounts[0]);

                // 触发钱包连接成功回调
                onWalletConnected({
                    account: accounts[0],
                    chainId: parseInt(await window.ethereum.request({ method: 'eth_chainId' }), 16),
                    provider: 'metamask',
                    ethereum: window.ethereum
                });
                return;
            }
        } catch (error) {
            console.error('❌ 浏览器钱包连接失败:', error);
            // 如果浏览器钱包连接失败，继续尝试WalletConnect
        }
    }

    // 如果没有浏览器钱包或连接失败，使用WalletConnect
    if (window.signClient && currentPaymentData) {
        console.log('🔄 使用WalletConnect连接钱包...');
        connectViaWalletConnect();
    } else if (window.walletManager) {
        console.log('🔄 使用WalletManager连接钱包...');
        // Attempt to connect wallet
        window.walletManager.connect()
            .then(() => {
                console.log('Wallet connection initiated');
            })
            .catch(error => {
                console.error('Wallet connection failed:', error);
                showNotification('Error', 'Failed to connect wallet: ' + error.message, 'error');

                // Reset button state
                if (connectBtn) {
                    connectBtn.innerHTML = '<i class="fas fa-link me-2"></i>Connect Wallet';
                    connectBtn.disabled = false;
                }
            });
    } else {
        console.error('❌ 没有可用的钱包连接方式');
        showNotification('Error', 'No wallet connection method available. Please install MetaMask or use a mobile wallet with WalletConnect.', 'error');

        // Reset button state
        if (connectBtn) {
            connectBtn.innerHTML = '<i class="fas fa-link me-2"></i>Connect Wallet';
            connectBtn.disabled = false;
        }
    }
}

// 生成WalletConnect二维码
async function generateWalletConnectQRCode(uri) {
    const qrContainerById = document.getElementById('qr-code-container');
    if (!qrContainerById) {
        console.error('QR code container not found');
        return;
    }

    try {
        console.log('🔄 生成WalletConnect二维码...');
        console.log('🔄 URI:', uri);

        // 清空容器
        qrContainerById.innerHTML = '';

        // 调用后端API重新生成包含WalletConnect URI的二维码
        const response = await fetch('/api/v1/payments/nowpayments/regenerate-qr/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                payment_id: window.paymentData?.payment_id,
                walletconnect_uri: uri
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('🔄 后端二维码API响应:', data);

            if (data.success && data.data.qr_code) {
                // 显示后端生成的二维码
                const img = document.createElement('img');
                img.src = `data:image/png;base64,${data.data.qr_code}`;
                img.alt = 'WalletConnect QR Code';
                img.style.width = '220px';
                img.style.height = '220px';
                img.style.borderRadius = '8px';
                img.style.border = '1px solid #dee2e6';
                img.style.display = 'block';
                img.style.margin = '0 auto';

                qrContainer.appendChild(img);
                console.log('✅ WalletConnect二维码生成成功');
                return;
            } else {
                console.error('❌ 后端API返回无效数据:', data);
            }
        } else {
            console.error('❌ 后端API请求失败:', response.status, response.statusText);
        }

        // 如果后端API失败，显示URI文本
        throw new Error('Backend QR generation failed');

    } catch (error) {
        console.error('❌ 生成WalletConnect二维码失败:', error);

        // 显示URI文本作为fallback
        qrContainer.innerHTML = `
            <div class="text-center p-3">
                <div class="alert alert-info">
                    <h6><i class="fas fa-qrcode me-2"></i>WalletConnect URI</h6>
                    <div class="small text-break bg-light p-2 rounded mt-2" style="word-break: break-all;">${uri}</div>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="copyToClipboard('${uri}')">
                        <i class="fas fa-copy me-1"></i>Copy URI
                    </button>
                </div>
                <p class="small text-muted mt-2">Copy this URI to your wallet app or scan with another device</p>
            </div>
        `;
    }
}

// 检查是否为移动设备
function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// 尝试唤起钱包应用
function tryOpenWalletApp(uri) {
    try {
        console.log('🔄 尝试唤起钱包应用...');

        // 为不同钱包创建深度链接
        const walletLinks = [
            `metamask://wc?uri=${encodeURIComponent(uri)}`,
            `trust://wc?uri=${encodeURIComponent(uri)}`,
            `rainbow://wc?uri=${encodeURIComponent(uri)}`,
            uri // 原始URI作为fallback
        ];

        // 尝试打开第一个钱包应用
        setTimeout(() => {
            window.location.href = walletLinks[0];
        }, 100);

        console.log('✅ 钱包应用唤起请求已发送');

    } catch (error) {
        console.error('❌ 唤起钱包应用失败:', error);
    }
}

// 通过WalletConnect连接钱包
async function connectViaWalletConnect() {
    try {
        console.log('🔄 通过WalletConnect连接钱包...');

        const statusDiv = document.getElementById('wallet-connection-status');
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="alert alert-info mb-3">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Connecting via WalletConnect... Please approve in your wallet app.
                </div>
            `;
        }

        // 🔧 修复：每次都清理旧会话，避免状态冲突
        if (window.signClient && window.signClient.session && window.signClient.session.length > 0) {
            console.log('🧹 发现现有WalletConnect会话，清理以避免状态冲突...');
            await cleanupInvalidSessions();
        }

        // 🔧 修复：不要重复调用connect，使用现有的createRealWalletConnectSession
        console.log('🔄 创建新的WalletConnect会话...');
        const uri = await createRealWalletConnectSession();

        if (!uri) {
            throw new Error('Failed to create WalletConnect session');
        }

        console.log('🔄 WalletConnect URI已创建，等待用户连接...');
        console.log('🔄 URI:', uri);

        // 生成二维码并显示给用户
        await generateWalletConnectQRCode(uri);

        // 尝试唤起钱包应用（如果在移动设备上）
        if (isMobileDevice()) {
            tryOpenWalletApp(uri);
        } else {
            // 在桌面浏览器上，显示连接说明
            const statusDiv = document.getElementById('wallet-connection-status');
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-qrcode me-2"></i>
                        Scan the QR code with your mobile wallet app to connect.
                    </div>
                `;
            }
        }

        // 🔧 修复：approval处理已在createRealWalletConnectSession中完成
        console.log('🔄 WalletConnect连接流程已启动，等待用户扫码...');

    } catch (error) {
        console.error('❌ WalletConnect连接失败:', error);
        showNotification('Error', 'Failed to connect via WalletConnect: ' + error.message, 'error');

        // Reset button state
        const connectBtn = document.getElementById('wallet-connect-btn');
        if (connectBtn) {
            connectBtn.innerHTML = '<i class="fas fa-link me-2"></i>Connect Wallet';
            connectBtn.disabled = false;
        }
    }
}

function onWalletConnected(walletInfo) {
    console.log('🎉 onWalletConnected 函数被调用!');
    console.log('🔄 钱包信息:', walletInfo);

    walletConnected = true;
    connectedWalletAddress = walletInfo.account;

    // 记录钱包连接方式
    window.connectedWalletProvider = walletInfo.provider || 'unknown';
    window.connectedWalletSession = walletInfo.session || null;

    console.log('✅ 钱包连接成功:', walletInfo);
    console.log('🔄 钱包连接方式:', window.connectedWalletProvider);
    console.log('🔄 用户地址:', connectedWalletAddress);

    // 🔍 DEBUG: 检查关键变量
    console.log('🔍 DEBUG: currentPaymentData:', currentPaymentData);
    console.log('🔍 DEBUG: window.paymentData:', window.paymentData);
    console.log('🔍 DEBUG: walletConnected:', walletConnected);
    console.log('🔍 DEBUG: isPaymentWindowOpen:', isPaymentWindowOpen);

    // Update UI
    const connectBtn = document.getElementById('wallet-connect-btn');
    const payBtn = document.getElementById('wallet-pay-btn');
    const statusDiv = document.getElementById('wallet-connection-status');
    const addressDiv = document.getElementById('wallet-address-display');
    const addressSpan = document.getElementById('connected-wallet-address');
    const networkInfo = document.getElementById('wallet-network-info');

    if (connectBtn) {
        connectBtn.style.display = 'none';
    }

    // 显示连接成功状态，但不显示Pay按钮，因为我们将自动支付
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-success mb-3">
                <i class="fas fa-check-circle me-2"></i>
                Wallet connected successfully! Initiating payment...
            </div>
        `;
    }

    if (addressDiv && addressSpan) {
        addressDiv.style.display = 'block';
        addressSpan.textContent = formatWalletAddress(walletInfo.account);
    }

    if (networkInfo) {
        networkInfo.textContent = `Network: ${getNetworkName(walletInfo.chainId)}`;
    }

    // 🔧 修复：只有在支付窗口开启且有支付数据时才检查网络兼容性
    if (isPaymentWindowOpen && currentPaymentData) {
        console.log('✅ 支付窗口已开启且有支付数据，检查网络兼容性...');
        console.log('🔍 DEBUG: 用户链ID:', walletInfo.chainId);
        console.log('🔍 DEBUG: 支付数据:', currentPaymentData);
        console.log('🔍 DEBUG: selectedCurrency:', selectedCurrency);
        console.log('🔍 DEBUG: paymentAmount:', paymentAmount);
        console.log('🔍 DEBUG: paymentAddress:', paymentAddress);

        const isNetworkCompatible = checkNetworkCompatibility(walletInfo.chainId);
        console.log('🔍 DEBUG: 网络兼容性结果:', isNetworkCompatible);

        // 如果网络兼容，自动开始支付流程
        if (isNetworkCompatible) {
            console.log('✅ 网络兼容，开始自动支付...');

            // 延迟1秒让用户看到连接成功消息，然后自动开始支付
            setTimeout(() => {
                initiateAutomaticPayment();
            }, 1000);
        } else {
            // 如果网络不兼容，自动切换到正确网络
            console.log('Network incompatible, attempting automatic network switch...');

            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Switching to correct network for payment...
                    </div>
                `;
            }

            // 自动切换网络
            attemptNetworkSwitchAndPayment();
        }
    } else if (!isPaymentWindowOpen) {
        // 🔧 修复：支付窗口未开启时，钱包连接成功是正常情况
        console.log('✅ 钱包连接成功，等待用户选择支付方式');

        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="alert alert-success mb-3">
                    <i class="fas fa-check-circle me-2"></i>
                    Wallet connected successfully! Please select a cryptocurrency to continue.
                </div>
            `;
        }
    } else {
        // 🔧 修复：支付窗口开启但没有支付数据的情况
        console.error('支付窗口已开启但没有支付数据');
        showNotification('Error', 'Payment data not available. Please try again.', 'error');
    }
}

function initiateAutomaticPayment() {
    console.log('Starting automatic payment process...');

    if (!walletConnected) {
        console.error('Cannot start automatic payment: wallet not connected');
        showNotification('Error', 'Wallet not connected. Please try connecting your wallet again.', 'error');
        return;
    }

    if (!validatePaymentState()) {
        console.error('Cannot start automatic payment: payment state invalid');
        showNotification('Error', 'Payment data is incomplete. Please refresh the page and try again.', 'error');
        return;
    }

    // 更新UI显示支付进行中
    const statusDiv = document.getElementById('wallet-connection-status');
    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-spinner fa-spin me-2"></i>
                Processing payment automatically...
            </div>
        `;
    }

    // 显示支付进度
    const progressDiv = document.getElementById('wallet-payment-progress');
    if (progressDiv) {
        progressDiv.style.display = 'block';
    }

    // 执行支付
    executeWalletPayment(currentPaymentData)
        .then(txHash => {
            console.log('Automatic payment transaction sent:', txHash);

            // 更新状态显示交易哈希
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-success mb-3">
                        <i class="fas fa-check-circle me-2"></i>
                        Payment submitted successfully!
                    </div>
                `;
            }

            // 更新进度显示
            const progressText = document.getElementById('payment-progress-text');
            if (progressText && txHash && typeof txHash === 'string' && txHash.length > 10) {
                progressText.textContent = `Transaction sent: ${txHash.slice(0, 10)}... Waiting for confirmation...`;
            } else if (progressText) {
                progressText.textContent = 'Transaction submitted. Waiting for confirmation...';
            }

            // 监控交易状态
            monitorTransaction(txHash);
        })
        .catch(error => {
            console.error('Automatic payment failed:', error);

            // 显示错误信息
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-danger mb-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Payment failed: ${error.message}
                    </div>
                `;
            }

            // 隐藏进度显示
            if (progressDiv) {
                progressDiv.style.display = 'none';
            }

            showNotification('Error', 'Payment failed: ' + error.message, 'error');
        });
}

async function attemptNetworkSwitchAndPayment() {
    console.log('Starting automatic network switch and payment...');

    if (!walletConnected || !currentPaymentData) {
        console.error('Cannot switch network: wallet not connected or payment data missing');
        showNotification('Error', 'Unable to switch network. Please try connecting your wallet again.', 'error');
        return;
    }

    if (!validatePaymentState()) {
        console.error('Cannot switch network: payment state invalid');
        showNotification('Error', 'Payment data is incomplete. Please refresh the page and try again.', 'error');
        return;
    }

    const currency = currentPaymentData.pay_currency;
    const requiredNetwork = getRequiredNetworkForCurrency(currency);

    if (!requiredNetwork) {
        console.error('No network configuration found for currency:', currency);
        showNotification('Error', `Network configuration not found for ${currency}`, 'error');
        return;
    }

    const statusDiv = document.getElementById('wallet-connection-status');

    try {
        // 检查是否为非EVM网络
        if (requiredNetwork.isNonEVM || requiredNetwork.id === 'solana' || requiredNetwork.id === 'tron') {
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-warning mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${currency} requires ${requiredNetwork.name}. This payment will be processed through NowPayments - no wallet network switch needed.
                    </div>
                `;
            }

            console.log(`${currency} is on non-EVM network ${requiredNetwork.name}, proceeding with NowPayments processing`);

            // 对于非EVM网络，直接显示支付信息，不需要切换网络
            if (statusDiv) {
                statusDiv.innerHTML = `
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        ${currency} payment ready. Send ${currency} to the address shown below.
                    </div>
                `;
            }

            showNotification('Info', `${currency} payment ready. No network switch required.`, 'info');
            return;
        }

        // 对于EVM兼容网络，尝试自动切换
        console.log(`Attempting to switch to ${requiredNetwork.name} (ID: ${requiredNetwork.id})`);

        // 🔧 修复：区分WalletConnect和MetaMask的网络切换方式
        const isWalletConnect = window.connectedWalletSession || currentSession;

        if (isWalletConnect) {
            console.log('🔄 使用WalletConnect网络切换...');
            await switchNetworkViaWalletConnect(requiredNetwork);
        } else if (window.walletManager && window.walletManager.switchNetwork) {
            console.log('🔄 使用MetaMask网络切换...');
            await window.walletManager.switchNetwork(requiredNetwork.id);
        } else {
            throw new Error('Wallet manager not available or does not support network switching');
        }

        // 切换成功，更新状态并开始支付
        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="alert alert-success mb-3">
                    <i class="fas fa-check-circle me-2"></i>
                    Switched to ${requiredNetwork.name}! Starting payment...
                </div>
            `;
        }

        console.log(`Successfully switched to ${requiredNetwork.name}, starting payment...`);

        // 延迟1秒让用户看到切换成功消息，然后开始支付
        setTimeout(() => {
            initiateAutomaticPayment();
        }, 1000);

    } catch (error) {
        console.error('Network switch failed:', error);

        if (statusDiv) {
            statusDiv.innerHTML = `
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Failed to switch network. Please switch to ${requiredNetwork.name} manually.
                </div>
            `;
        }

        showNotification('Error', `Failed to switch to ${requiredNetwork.name}. Please switch manually in your wallet.`, 'error');
    }
}

// 🔧 新增：WalletConnect专用的网络切换函数
async function switchNetworkViaWalletConnect(requiredNetwork) {
    const session = window.connectedWalletSession || currentSession;

    if (!session || !window.signClient) {
        throw new Error('WalletConnect session not available');
    }

    try {
        const hexChainId = '0x' + requiredNetwork.id.toString(16);

        console.log(`🔄 WalletConnect: 请求切换到网络 ${requiredNetwork.name} (${hexChainId})`);

        // 🔧 使用WalletConnect的网络切换请求
        // 注意：chainId应该是当前会话的chainId，不是目标chainId
        const currentChainId = `eip155:${session.namespaces?.eip155?.chains?.[0]?.split(':')[1] || '1'}`;
        console.log(`🔍 DEBUG: 当前会话chainId: ${currentChainId}`);

        await window.signClient.request({
            topic: session.topic,
            chainId: currentChainId,
            request: {
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: hexChainId }]
            }
        });

        console.log(`✅ WalletConnect: 成功切换到 ${requiredNetwork.name}`);

    } catch (error) {
        console.error('🔄 WalletConnect网络切换失败:', error);

        // 如果网络不存在，尝试添加网络
        if (error.code === 4902 || error.message?.includes('Unrecognized chain')) {
            console.log('🔄 网络不存在，尝试添加网络...');
            await addNetworkViaWalletConnect(requiredNetwork);
        } else {
            throw error;
        }
    }
}

// 🔧 新增：WalletConnect专用的网络添加函数
async function addNetworkViaWalletConnect(requiredNetwork) {
    const session = window.connectedWalletSession || currentSession;

    if (!session || !window.signClient) {
        throw new Error('WalletConnect session not available');
    }

    // 🔧 修复：确保网络配置完全符合MetaMask标准
    const networkConfig = {
        chainId: '0x' + requiredNetwork.id.toString(16),
        chainName: requiredNetwork.name,
        nativeCurrency: {
            name: requiredNetwork.nativeCurrency.name,
            symbol: requiredNetwork.nativeCurrency.symbol,
            decimals: requiredNetwork.nativeCurrency.decimals
        },
        rpcUrls: requiredNetwork.rpcUrls.filter(url => url && url.startsWith('http')),
        blockExplorerUrls: requiredNetwork.blockExplorerUrls.filter(url => url && url.startsWith('http'))
    };

    try {
        console.log(`🔄 WalletConnect: 添加网络 ${requiredNetwork.name}`, networkConfig);

        // 注意：chainId应该是当前会话的chainId，不是目标chainId
        const currentChainId = `eip155:${session.namespaces?.eip155?.chains?.[0]?.split(':')[1] || '1'}`;

        await window.signClient.request({
            topic: session.topic,
            chainId: currentChainId,
            request: {
                method: 'wallet_addEthereumChain',
                params: [networkConfig]
            }
        });

        console.log(`✅ WalletConnect: 成功添加网络 ${requiredNetwork.name}`);

        // 添加成功后切换到该网络
        await switchNetworkViaWalletConnect(requiredNetwork);

    } catch (error) {
        console.error('🔄 WalletConnect添加网络失败:', error);
        throw error;
    }
}

function onWalletDisconnected() {
    walletConnected = false;
    connectedWalletAddress = null;

    // Reset UI
    const connectBtn = document.getElementById('wallet-connect-btn');
    const payBtn = document.getElementById('wallet-pay-btn');
    const statusDiv = document.getElementById('wallet-connection-status');
    const addressDiv = document.getElementById('wallet-address-display');

    if (connectBtn) {
        connectBtn.style.display = 'inline-block';
        connectBtn.innerHTML = '<i class="fas fa-link me-2"></i>Connect Wallet';
        connectBtn.disabled = false;
    }

    if (payBtn) {
        payBtn.style.display = 'none';
    }

    if (statusDiv) {
        statusDiv.innerHTML = `
            <div class="alert alert-info mb-3">
                <i class="fas fa-info-circle me-2"></i>
                Connect your Web3 wallet to pay automatically
            </div>
        `;
    }

    if (addressDiv) {
        addressDiv.style.display = 'none';
    }
}

function checkNetworkCompatibility(chainId) {
    if (!currentPaymentData) return false;

    const currency = currentPaymentData.pay_currency;
    const requiredNetwork = getRequiredNetworkForCurrency(currency);

    const warningDiv = document.getElementById('network-switch-warning');

    if (requiredNetwork && chainId !== requiredNetwork.id) {
        // Show network switch warning
        if (warningDiv) {
            warningDiv.style.display = 'block';
            const warningText = warningDiv.querySelector('small');
            if (warningText) {
                warningText.innerHTML = `Please switch to <strong>${requiredNetwork.name}</strong> network for ${currency} payments.`;
            }
        }

        console.log(`Network mismatch: Current ${chainId}, Required ${requiredNetwork.id}`);
        return false;
    } else {
        // Hide warning
        if (warningDiv) {
            warningDiv.style.display = 'none';
        }

        console.log(`Network compatible: ${chainId}`);
        return true;
    }
}

function switchToRequiredNetwork() {
    if (!currentPaymentData) return;

    const currency = currentPaymentData.pay_currency;
    const requiredNetwork = getRequiredNetworkForCurrency(currency);

    if (requiredNetwork && window.walletManager) {
        window.walletManager.switchNetwork(requiredNetwork.id)
            .then(() => {
                showNotification('Success', `Switched to ${requiredNetwork.name} network`, 'success');
            })
            .catch(error => {
                console.error('Network switch failed:', error);
                showNotification('Error', 'Failed to switch network: ' + error.message, 'error');
            });
    }
}

function payWithWallet() {
    if (!walletConnected || !currentPaymentData) {
        showNotification('Error', 'Wallet not connected or payment data not available', 'error');
        return;
    }

    // Show payment progress
    const progressDiv = document.getElementById('wallet-payment-progress');
    const payBtn = document.getElementById('wallet-pay-btn');

    if (progressDiv) {
        progressDiv.style.display = 'block';
    }

    if (payBtn) {
        payBtn.disabled = true;
        payBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    }

    // Execute payment through wallet manager
    executeWalletPayment(currentPaymentData)
        .then(txHash => {
            console.log('Payment transaction sent:', txHash);

            // Update progress
            const progressText = document.getElementById('payment-progress-text');
            if (progressText && txHash && typeof txHash === 'string' && txHash.length > 10) {
                progressText.textContent = `Transaction sent: ${txHash.slice(0, 10)}... Waiting for confirmation...`;
            } else if (progressText) {
                progressText.textContent = 'Transaction submitted. Waiting for confirmation...';
            }

            // Monitor transaction
            monitorTransaction(txHash);
        })
        .catch(error => {
            console.error('Payment failed:', error);
            showNotification('Error', 'Payment failed: ' + error.message, 'error');

            // Reset UI
            if (progressDiv) {
                progressDiv.style.display = 'none';
            }

            if (payBtn) {
                payBtn.disabled = false;
                payBtn.innerHTML = '<i class="fas fa-bolt me-2"></i>Pay Now';
            }
        });
}

async function executeWalletPayment(paymentData) {
    const { pay_address, pay_amount, pay_currency } = paymentData;

    console.log('🔍 执行钱包支付 - 原始数据:', { pay_address, pay_amount, pay_currency });
    console.log('🔍 支付数据类型检查:', {
        pay_amount_type: typeof pay_amount,
        pay_amount_value: pay_amount,
        pay_currency_type: typeof pay_currency,
        pay_currency_value: pay_currency
    });

    // 验证支付金额
    if (!pay_amount || pay_amount === '0' || pay_amount === 0) {
        console.error('❌ 支付金额无效:', pay_amount);
        throw new Error(`Invalid payment amount: ${pay_amount}. Please refresh and try again.`);
    }

    // Get network configuration for the currency
    const networkConfig = getRequiredNetworkForCurrency(pay_currency);
    if (!networkConfig) {
        throw new Error(`No network configuration found for currency: ${pay_currency}`);
    }

    console.log('🔍 网络配置:', networkConfig);

    // 检查是否为非EVM网络
    if (networkConfig.isNonEVM || networkConfig.id === 'solana' || networkConfig.id === 'tron') {
        throw new Error(`${pay_currency} is on ${networkConfig.name} network. Please send ${pay_currency} directly to the payment address shown. Web3 wallet integration is not available for non-EVM networks.`);
    }

    // Get token information
    const tokenAddress = getTokenAddressForCurrency(pay_currency, networkConfig);
    const isNative = isNativeToken(pay_currency, networkConfig);
    const decimals = getTokenDecimals(pay_currency, networkConfig);

    console.log('🔍 代币详情:', {
        currency: pay_currency,
        tokenAddress,
        isNative,
        decimals,
        originalAmount: pay_amount,
        networkConfig: networkConfig
    });

    // 重要：NowPayments返回的pay_amount已经是正确的支付金额
    // 对于NowPayments支付，我们直接使用这个金额，不需要任何转换
    // 钱包会自动处理精度转换（从人类可读格式到Wei）
    const paymentAmount = parseFloat(pay_amount);
    console.log('🔍 支付金额处理:', {
        original: pay_amount,
        parsed: paymentAmount,
        isValid: !isNaN(paymentAmount) && paymentAmount > 0
    });

    if (isNaN(paymentAmount) || paymentAmount <= 0) {
        throw new Error(`Invalid payment amount after parsing: ${paymentAmount}`);
    }

    // 检查钱包连接类型并发送交易
    if (connectedWalletAddress && window.connectedWalletProvider === 'walletconnect') {
        // 使用WalletConnect发送交易
        console.log('🔄 使用WalletConnect发送交易...');
        return await sendTransactionViaWalletConnect(pay_address, paymentAmount, isNative, tokenAddress, decimals, networkConfig);
    } else if (window.walletManager) {
        // 使用传统的WalletManager发送交易
        console.log('🔄 使用WalletManager发送交易...');

        // Validate wallet connection
        if (!window.walletManager.isConnected) {
            throw new Error('Wallet not connected');
        }

        // Send transaction with proper parameters
        try {
            const txHash = await window.walletManager.sendTransaction(
                pay_address,
                paymentAmount,
                isNative ? null : tokenAddress,
                decimals
            );

            console.log('Transaction sent successfully:', txHash);
            return txHash;
        } catch (error) {
            console.error('Transaction failed:', error);
            throw new Error(`Transaction failed: ${error.message}`);
        }
    } else {
        throw new Error('No wallet connection available');
    }
}

// 通过WalletConnect发送交易
async function sendTransactionViaWalletConnect(toAddress, amount, isNative, tokenAddress, decimals, networkConfig) {
    try {
        console.log('🔄 准备WalletConnect交易...');
        console.log('🔄 参数:', { toAddress, amount, isNative, tokenAddress, decimals, networkConfig });

        // 🔧 增强调试：检查WalletConnect会话状态
        console.log('🔍 DEBUG: 检查WalletConnect会话状态...');
        console.log('🔍 DEBUG: window.signClient:', !!window.signClient);
        console.log('🔍 DEBUG: window.connectedWalletSession:', !!window.connectedWalletSession);
        console.log('🔍 DEBUG: currentSession:', !!currentSession);
        console.log('🔍 DEBUG: signClient:', !!signClient);

        // 🔧 修复会话检查 - 使用统一的会话管理
        const activeSession = window.connectedWalletSession || currentSession;

        if (!window.signClient || !activeSession) {
            console.error('❌ WalletConnect会话不可用!');
            console.error('❌ signClient存在:', !!window.signClient);
            console.error('❌ connectedWalletSession存在:', !!window.connectedWalletSession);
            console.error('❌ currentSession存在:', !!currentSession);
            console.error('❌ activeSession存在:', !!activeSession);
            throw new Error('WalletConnect session not available');
        }

        const session = activeSession;
        const account = session.namespaces.eip155.accounts[0];
        const [namespace, chainId, fromAddress] = account.split(':');

        console.log('🔄 会话信息:', { account, chainId, fromAddress });

        // 检查网络是否匹配
        const requiredChainId = networkConfig.id;
        if (parseInt(chainId) !== requiredChainId) {
            throw new Error(`Wrong network. Please switch to ${networkConfig.name} (Chain ID: ${requiredChainId})`);
        }

        // 🔧 获取当前支付的币种信息
        const currentCurrency = window.currentPaymentState?.currency || 'UNKNOWN';
        console.log('🔍 当前支付币种:', currentCurrency);

        let transactionParams;

        if (isNative) {
            // 原生代币转账（ETH, BNB, MATIC等）
            console.log('🔄 准备原生代币转账...');
            console.log('🔍 原生代币信息:', { amount, decimals: 18 });

            // 使用修复后的parseUnits函数转换金额
            const amountInWei = parseUnits(amount, 18);
            console.log('🔍 转换后的Wei金额:', amountInWei);

            transactionParams = {
                from: fromAddress,
                to: toAddress,
                value: '0x' + BigInt(amountInWei).toString(16),
                gas: '0x5208', // 21000 gas for simple transfer
                gasPrice: '0x9502f9000' // 40 Gwei
            };
        } else {
            // ERC-20代币转账
            console.log('🔄 准备ERC-20代币转账...');
            console.log('🔍 代币信息:', { currency: currentCurrency, tokenAddress, decimals, amount });

            if (!tokenAddress) {
                throw new Error('Token address is required for ERC-20 transfer');
            }

            // 使用修复后的parseUnits函数转换金额
            const amountInTokenUnits = parseUnits(amount, decimals);
            console.log('🔍 金额转换:', { originalAmount: amount, decimals, amountInTokenUnits });

            // 🔧 修复ERC-20 transfer函数的ABI编码
            const transferMethodId = '0xa9059cbb'; // transfer(address,uint256)

            // 🔧 修复地址填充 - 确保地址格式正确
            const cleanToAddress = toAddress.toLowerCase().replace('0x', '');
            const paddedToAddress = cleanToAddress.padStart(64, '0');

            // 🔧 修复金额编码 - 确保BigInt转换正确
            const paddedAmount = BigInt(amountInTokenUnits).toString(16).padStart(64, '0');
            const data = transferMethodId + paddedToAddress + paddedAmount;

            console.log('🔍 修复后的交易数据构建:', {
                transferMethodId,
                cleanToAddress,
                paddedToAddress,
                amountInTokenUnits,
                paddedAmount,
                data,
                dataLength: data.length
            });

            // 🔧 大幅优化Gas费设置 - 降低到合理水平
            const gasLimit = getOptimalGasLimit(currentCurrency);
            const gasPrice = await getOptimalGasPrice(networkConfig.id);

            transactionParams = {
                from: fromAddress,
                to: tokenAddress,
                value: '0x0',
                data: data,
                gas: gasLimit,
                gasPrice: gasPrice
            };

            console.log('🔍 优化后的Gas设置:', { gasLimit, gasPrice, estimatedCost: `${parseInt(gasLimit, 16) * parseInt(gasPrice, 16) / 1e18} ETH` });
        }

        console.log('🔄 最终交易参数:', transactionParams);

        // 🔧 构建增强的交易请求，帮助钱包识别代币
        const requestParams = [transactionParams];

        // 🔧 如果是ERC-20代币，尝试添加代币识别信息
        if (!isNative && tokenAddress) {
            const tokenMetadata = getTokenMetadata(currentCurrency);
            console.log('🔍 代币元数据查询:', {
                currency: currentCurrency,
                metadata: tokenMetadata,
                hasMetadata: !!tokenMetadata
            });

            // 🔧 如果没有找到元数据，尝试从动态配置获取
            if (!tokenMetadata && window.dynamicCurrencyConfigs) {
                const dynamicConfig = window.dynamicCurrencyConfigs[currentCurrency.toUpperCase()];
                console.log('🔍 尝试从动态配置获取代币信息:', {
                    currency: currentCurrency.toUpperCase(),
                    dynamicConfig: dynamicConfig,
                    allConfigs: window.dynamicCurrencyConfigs
                });
            }

            // 🔧 添加代币合约验证信息到交易数据中
            console.log('🔍 代币合约地址验证:', {
                currency: currentCurrency,
                tokenAddress: tokenAddress,
                expectedAddress: getExpectedTokenAddress(currentCurrency),
                addressMatch: tokenAddress.toLowerCase() === getExpectedTokenAddress(currentCurrency).toLowerCase()
            });
        }

        // 🔧 发送优化后的交易请求
        console.log('🔄 发送WalletConnect交易请求...');
        // 注意：chainId应该是当前会话的chainId
        const currentChainId = `eip155:${chainId}`;

        const result = await window.signClient.request({
            topic: session.topic,
            chainId: currentChainId,
            request: {
                method: 'eth_sendTransaction',
                params: requestParams
            }
        });

        console.log('✅ 交易已发送:', result);
        return result;

    } catch (error) {
        console.error('❌ WalletConnect交易失败:', error);
        throw error;
    }
}

// 🔧 获取代币的最优Gas限制 - 大幅降低Gas限制
function getOptimalGasLimit(currency) {
    const gasLimits = {
        'DAI': '0xAFC8',       // 45000 - 降低DAI Gas限制
        'USDC': '0x9C40',      // 40000 - 降低USDC Gas限制
        'USDTERC20': '0xAFC8', // 45000 - 降低USDT Gas限制
        'USDT': '0xAFC8',      // 45000
        'default': '0x9C40'    // 40000 - 大幅降低默认ERC-20转账Gas限制
    };

    return gasLimits[currency] || gasLimits['default'];
}

// 🔧 获取最优Gas价格 - 大幅降低Gas价格到实际合理水平
async function getOptimalGasPrice(chainId) {
    try {
        // 对于不同网络使用更低的Gas价格策略
        switch (chainId) {
            case 1: // 以太坊主网
                // 大幅降低Gas价格到实际合理水平
                return '0x12A05F200'; // 5 Gwei (从20 Gwei降低到5 Gwei)
            case 56: // BSC
                return '0x77359400'; // 2 Gwei (从5 Gwei降低到2 Gwei)
            case 137: // Polygon
                return '0x4A817C800'; // 20 Gwei (从40 Gwei降低到20 Gwei)
            default:
                return '0x12A05F200'; // 5 Gwei
        }
    } catch (error) {
        console.warn('⚠️ 获取Gas价格失败，使用默认值:', error);
        return '0x12A05F200'; // 5 Gwei
    }
}

// 🔧 获取代币元数据
function getTokenMetadata(currency) {
    const tokenMetadata = {
        'DAI': {
            symbol: 'DAI',
            name: 'Dai Stablecoin',
            decimals: 18,
            address: '******************************************'
        },
        'USDC': {
            symbol: 'USDC',
            name: 'USD Coin',
            decimals: 6,
            address: '******************************************'
        },
        'USDTERC20': {
            symbol: 'USDT',
            name: 'Tether USD',
            decimals: 6,
            address: '******************************************'
        },
        'USDT': {
            symbol: 'USDT',
            name: 'Tether USD',
            decimals: 6,
            address: '******************************************'
        }
    };

    return tokenMetadata[currency] || null;
}

// 🔧 获取预期的代币合约地址
function getExpectedTokenAddress(currency) {
    const tokenMetadata = getTokenMetadata(currency);
    return tokenMetadata ? tokenMetadata.address : '';
}

function monitorTransaction(txHash) {
    // This would typically monitor the transaction status
    // For now, just show success after a delay
    setTimeout(() => {
        const progressDiv = document.getElementById('wallet-payment-progress');
        if (progressDiv) {
            progressDiv.innerHTML = `
                <hr>
                <div class="alert alert-success">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle me-3 text-success"></i>
                        <div>
                            <strong>Payment Successful!</strong><br>
                            <small>Transaction: ${txHash}</small>
                        </div>
                    </div>
                </div>
            `;
        }

        showNotification('Success', 'Payment completed successfully!', 'success');

        // Refresh payment status
        setTimeout(() => {
            if (currentPaymentData) {
                checkPaymentStatus(currentPaymentData.payment_id);
            }
        }, 3000);
    }, 5000);
}

// Utility functions
function formatWalletAddress(address) {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

function getNetworkName(chainId) {
    const networks = {
        1: 'Ethereum',
        56: 'BSC',
        137: 'Polygon',
        728126428: 'Tron'
    };
    return networks[chainId] || `Chain ${chainId}`;
}

// 动态获取NowPayments支持的币种并匹配技术配置
async function loadDynamicCurrencyConfigs() {
    try {
        console.log('🔄 Loading dynamic currency configs from NowPayments...');

        // 从后端API获取NowPayments支持的币种
        const response = await fetch('/api/v1/payments/nowpayments/currencies/');
        if (!response.ok) {
            throw new Error(`Failed to fetch currencies: ${response.status}`);
        }

        const data = await response.json();
        const supportedCurrencies = data.data?.currencies || [];

        console.log(`✅ Loaded ${supportedCurrencies.length} supported currencies from NowPayments:`, supportedCurrencies);

        // 为每个支持的币种生成正确的技术配置
        const dynamicConfigs = generateDynamicCurrencyConfigs(supportedCurrencies);
        console.log(`🔧 Generated ${Object.keys(dynamicConfigs).length} dynamic currency configs:`, dynamicConfigs);

        // 返回支持的币种列表和配置，供其他函数使用
        return {
            currencies: supportedCurrencies,
            configs: dynamicConfigs
        };

    } catch (error) {
        console.error('❌ Failed to load dynamic currency configs:', error);
        // 返回默认的币种列表作为fallback
        return {
            currencies: ['DAI', 'USDC', 'USDTERC20', 'USDTBSC', 'USDCMATIC'],
            configs: {}
        };
    }
}

// 基于NowPayments支持的币种生成正确的技术配置
function generateDynamicCurrencyConfigs(supportedCurrencies) {
    console.log('🔧 Generating dynamic currency configs for:', supportedCurrencies);

    // 官方合约地址和网络配置映射
    const OFFICIAL_TOKEN_CONFIGS = {
        // 以太坊主网代币
        'DAI': {
            network: 'ethereum',
            chainId: 1,
            tokenAddress: '******************************************',
            decimals: 18,
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/']
        },
        'USDC': {
            network: 'ethereum',
            chainId: 1,
            tokenAddress: '******************************************',
            decimals: 6,
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/']
        },
        'USDTERC20': {
            network: 'ethereum',
            chainId: 1,
            tokenAddress: '******************************************',
            decimals: 6,
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/']
        },

        // BSC网络代币
        'USDTBSC': {
            network: 'bsc',
            chainId: 56,
            tokenAddress: '******************************************',
            decimals: 18,
            rpcUrls: ['https://bsc-dataseed.binance.org/', 'https://bsc-dataseed1.defibit.io/'],
            blockExplorerUrls: ['https://bscscan.com/']
        },
        'FDUSDBSC': {
            id: 56,
            name: 'BNB Smart Chain',
            chainName: 'BSC',
            nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
            rpcUrls: ['https://bsc-dataseed.binance.org/', 'https://bsc-dataseed1.binance.org/', 'https://bsc-dataseed2.binance.org/'],
            blockExplorerUrls: ['https://bscscan.com/'],
            tokenAddress: '******************************************', // 官方FDUSD BSC合约地址
            decimals: 18,
            isNative: false
        },



    };

    // 为NowPayments支持的每个币种生成配置
    const dynamicConfigs = {};

    supportedCurrencies.forEach(currencyObj => {
        // 处理对象格式的币种数据
        const currencyCode = currencyObj.code || currencyObj;
        const currencyUpper = currencyCode.toUpperCase();
        const officialConfig = OFFICIAL_TOKEN_CONFIGS[currencyUpper];

        if (officialConfig) {
            // 使用官方配置 - 统一使用新格式
            dynamicConfigs[currencyUpper] = {
                id: officialConfig.id || officialConfig.chainId,
                name: officialConfig.name || getNetworkDisplayName(officialConfig.id || officialConfig.chainId),
                chainName: officialConfig.chainName || officialConfig.network,
                nativeCurrency: officialConfig.nativeCurrency || getNativeCurrencyForNetwork(officialConfig.network || officialConfig.chainName),
                rpcUrls: officialConfig.rpcUrls,
                blockExplorerUrls: officialConfig.blockExplorerUrls,
                tokenAddress: officialConfig.tokenAddress,
                decimals: officialConfig.decimals,
                isNative: officialConfig.isNative || false,
                // 🔧 修复：添加币种UI配置
                icon_class: currencyObj.icon_class || getCurrencyIcon(currencyUpper),
                display_name: currencyObj.name || currencyUpper,
                is_stablecoin: currencyObj.is_stablecoin || false
            };

            console.log(`✅ Generated config for ${currencyUpper}:`, dynamicConfigs[currencyUpper]);
        } else {
            // 尝试智能匹配
            const smartConfig = smartMatchCurrencyConfig(currencyUpper);
            if (smartConfig) {
                // 🔧 修复：为智能匹配的配置也添加UI信息
                smartConfig.icon_class = currencyObj.icon_class || getCurrencyIcon(currencyUpper);
                smartConfig.display_name = currencyObj.name || currencyUpper;
                smartConfig.is_stablecoin = currencyObj.is_stablecoin || false;

                dynamicConfigs[currencyUpper] = smartConfig;
                console.log(`🤖 Smart matched config for ${currencyUpper}:`, smartConfig);
            } else {
                console.warn(`⚠️ No config found for currency: ${currencyUpper}`);
            }
        }
    });

    return dynamicConfigs;
}

// 🔧 修复：获取币种图标的备用函数
function getCurrencyIcon(currencyCode) {
    const currency = currencyCode.toLowerCase();

    // 根据币种类型返回合适的图标
    if (['usdt', 'usdc', 'fdusd', 'dai', 'busd', 'tusd'].some(stable => currency.includes(stable))) {
        return 'fas fa-dollar-sign text-success';  // 稳定币用绿色美元符号
    } else if (currency === 'btc') {
        return 'fab fa-bitcoin text-warning';
    } else if (currency === 'eth') {
        return 'fab fa-ethereum text-primary';
    } else if (currency === 'ltc') {
        return 'fab fa-litecoin text-info';
    } else if (currency === 'doge') {
        return 'fas fa-dog text-warning';
    } else if (currency === 'sol') {
        return 'fas fa-sun text-warning';
    } else if (['bnb', 'bnbbsc'].includes(currency)) {
        return 'fas fa-coins text-warning';
    } else if (currency === 'ada') {
        return 'fas fa-heart text-primary';
    } else if (currency === 'dot') {
        return 'fas fa-circle text-danger';
    } else if (['matic', 'usdcmatic'].includes(currency)) {
        return 'fas fa-coins text-info';
    } else if (currency === 'avax') {
        return 'fas fa-mountain text-danger';
    } else if (currency === 'link') {
        return 'fas fa-link text-primary';
    } else if (currency === 'uni') {
        return 'fas fa-unicorn text-danger';
    } else if (currency === 'xlm') {
        return 'fas fa-star text-info';
    } else if (currency === 'xrp') {
        return 'fas fa-water text-primary';
    } else if (currency === 'trx') {
        return 'fas fa-bolt text-danger';
    } else if (currency === 'bch') {
        return 'fab fa-bitcoin text-success';
    } else if (currency === 'etc') {
        return 'fab fa-ethereum text-success';
    } else if (currency === 'xmr') {
        return 'fas fa-eye-slash text-dark';
    } else if (currency === 'zec') {
        return 'fas fa-shield-alt text-warning';
    } else if (currency === 'dash') {
        return 'fas fa-tachometer-alt text-primary';
    } else if (currency === 'atom') {
        return 'fas fa-atom text-info';
    } else if (currency === 'algo') {
        return 'fas fa-calculator text-success';
    } else if (currency === 'vet') {
        return 'fas fa-check-circle text-primary';
    } else if (currency === 'icp') {
        return 'fas fa-infinity text-primary';
    } else if (currency === 'fil') {
        return 'fas fa-folder text-info';
    } else if (currency === 'theta') {
        return 'fas fa-play-circle text-warning';
    } else if (currency === 'xtz') {
        return 'fas fa-cube text-primary';
    } else if (currency === 'eos') {
        return 'fas fa-globe text-dark';
    } else {
        return 'fas fa-coins text-secondary';  // 默认图标
    }
}

// 获取网络显示名称
function getNetworkDisplayName(chainId) {
    const networkNames = {
        1: 'Ethereum Mainnet',
        56: 'BNB Smart Chain',
        137: 'Polygon Mainnet',
        42161: 'Arbitrum One',
        10: 'Optimism',
        43114: 'Avalanche C-Chain'
    };
    return networkNames[chainId] || `Chain ${chainId}`;
}

// 获取网络的原生货币信息
function getNativeCurrencyForNetwork(network) {
    const nativeCurrencies = {
        'ethereum': { name: 'ETH', symbol: 'ETH', decimals: 18 },
        'bsc': { name: 'BNB', symbol: 'BNB', decimals: 18 },
        'polygon': { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
        'arbitrum': { name: 'ETH', symbol: 'ETH', decimals: 18 },
        'optimism': { name: 'ETH', symbol: 'ETH', decimals: 18 },
        'avalanche': { name: 'AVAX', symbol: 'AVAX', decimals: 18 }
    };
    return nativeCurrencies[network] || { name: 'ETH', symbol: 'ETH', decimals: 18 };
}

// 智能匹配币种配置（用于未知币种）
function smartMatchCurrencyConfig(currency) {
    console.log(`🤖 Smart matching config for: ${currency}`);

    // 基于币种名称的智能匹配规则
    const smartRules = [
        // BSC网络币种
        { pattern: /BSC$/i, network: 'bsc', chainId: 56, decimals: 18 },
        { pattern: /BEP20$/i, network: 'bsc', chainId: 56, decimals: 18 },
        { pattern: /^BNB/i, network: 'bsc', chainId: 56, decimals: 18 },

        // Polygon网络币种
        { pattern: /MATIC$/i, network: 'polygon', chainId: 137, decimals: 6 },
        { pattern: /POLYGON$/i, network: 'polygon', chainId: 137, decimals: 6 },

        // Arbitrum网络币种
        { pattern: /ARB$/i, network: 'arbitrum', chainId: 42161, decimals: 6 },
        { pattern: /ARBITRUM$/i, network: 'arbitrum', chainId: 42161, decimals: 6 },

        // Optimism网络币种
        { pattern: /OP$/i, network: 'optimism', chainId: 10, decimals: 18 },
        { pattern: /OPTIMISM$/i, network: 'optimism', chainId: 10, decimals: 18 },

        // Avalanche网络币种
        { pattern: /AVAX$/i, network: 'avalanche', chainId: 43114, decimals: 18 },
        { pattern: /AVALANCHE$/i, network: 'avalanche', chainId: 43114, decimals: 18 },

        // 以太坊网络币种
        { pattern: /ERC20$/i, network: 'ethereum', chainId: 1, decimals: 6 },
        { pattern: /ETH$/i, network: 'ethereum', chainId: 1, decimals: 18 },

        // 常见稳定币模式
        { pattern: /^USDT/i, network: 'ethereum', chainId: 1, decimals: 6 },
        { pattern: /^USDC/i, network: 'ethereum', chainId: 1, decimals: 6 },
        { pattern: /^DAI/i, network: 'ethereum', chainId: 1, decimals: 18 },

        // 交易所代币（默认以太坊）
        { pattern: /^(OKB|BNT|UNI|SUSHI|COMP|AAVE|MKR|SNX|YFI|1INCH)$/i, network: 'ethereum', chainId: 1, decimals: 18 },
    ];

    for (const rule of smartRules) {
        if (rule.pattern.test(currency)) {
            console.log(`🎯 Matched rule for ${currency}:`, rule);

            // 为智能匹配的币种生成一个占位符合约地址
            // 这样可以避免被当作原生币处理，同时提醒用户需要配置正确的合约地址
            const placeholderAddress = `0x${'0'.repeat(39)}1`; // 0x000...001 作为占位符

            return {
                id: rule.chainId,
                name: getNetworkDisplayName(rule.chainId),
                chainName: rule.network,
                nativeCurrency: getNativeCurrencyForNetwork(rule.network),
                rpcUrls: getRpcUrlsForNetwork(rule.network),
                blockExplorerUrls: getBlockExplorerUrlsForNetwork(rule.network),
                tokenAddress: placeholderAddress, // 使用占位符地址
                decimals: rule.decimals,
                isNative: false,
                isPlaceholder: true, // 标记为占位符配置
                warning: `⚠️ ${currency} 使用智能匹配配置，请在生产环境中配置正确的合约地址`
            };
        }
    }

    console.log(`❌ No smart match found for: ${currency}`);
    return null;
}

// 获取网络的RPC URLs
function getRpcUrlsForNetwork(network) {
    const rpcUrls = {
        'ethereum': ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
        'bsc': ['https://bsc-dataseed.binance.org/', 'https://bsc-dataseed1.defibit.io/'],
        'polygon': ['https://polygon-rpc.com/', 'https://rpc-mainnet.maticvigil.com/'],
        'arbitrum': ['https://arb1.arbitrum.io/rpc', 'https://arbitrum-mainnet.infura.io/v3/'],
        'optimism': ['https://mainnet.optimism.io/', 'https://optimism-mainnet.infura.io/v3/'],
        'avalanche': ['https://api.avax.network/ext/bc/C/rpc', 'https://avalanche-mainnet.infura.io/v3/']
    };
    return rpcUrls[network] || ['https://mainnet.infura.io/v3/'];
}

// 获取网络的区块浏览器URLs
function getBlockExplorerUrlsForNetwork(network) {
    const explorerUrls = {
        'ethereum': ['https://etherscan.io/'],
        'bsc': ['https://bscscan.com/'],
        'polygon': ['https://polygonscan.com/'],
        'arbitrum': ['https://arbiscan.io/'],
        'optimism': ['https://optimistic.etherscan.io/'],
        'avalanche': ['https://snowtrace.io/']
    };
    return explorerUrls[network] || ['https://etherscan.io/'];
}

// 全局变量存储动态配置
let globalDynamicConfigs = {};

function getRequiredNetworkForCurrency(currency) {
    // 动态网络映射配置 - 基于NowPayments支持的币种
    // 这个映射会根据用户在NowPayments后台配置的币种动态工作
    // 使用正确的官方合约地址和网络信息

    const currencyUpper = currency.toUpperCase();

    console.log(`🔍 Getting network config for currency: ${currencyUpper}`);

    // 首先尝试使用动态配置
    if (globalDynamicConfigs[currencyUpper]) {
        console.log(`✅ Using dynamic config for ${currencyUpper}:`, globalDynamicConfigs[currencyUpper]);
        return globalDynamicConfigs[currencyUpper];
    }

    // 基于币种代码的智能网络检测
    const networkMap = {
        // 以太坊主网代币
        'DAI': {
            id: 1,
            name: 'Ethereum Mainnet',
            chainName: 'Ethereum',
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/'],
            tokenAddress: '******************************************',
            decimals: 18,
            isNative: false
        },
        'USDC': {
            id: 1,
            name: 'Ethereum Mainnet',
            chainName: 'Ethereum',
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/'],
            tokenAddress: '******************************************', // 正确的USDC地址
            decimals: 6,
            isNative: false
        },
        'USDTERC20': {
            id: 1,
            name: 'Ethereum Mainnet',
            chainName: 'Ethereum',
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/'],
            tokenAddress: '******************************************',
            decimals: 6,
            isNative: false
        },
        'ETH': {
            id: 1,
            name: 'Ethereum Mainnet',
            chainName: 'Ethereum',
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
            rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
            blockExplorerUrls: ['https://etherscan.io/'],
            tokenAddress: null,
            decimals: 18,
            isNative: true
        },

        // Polygon网络代币
        'USDCMATIC': {
            id: 137,
            name: 'Polygon Mainnet',
            chainName: 'Polygon',
            nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
            rpcUrls: ['https://polygon-rpc.com/', 'https://rpc-mainnet.matic.network/', 'https://polygon-mainnet.infura.io/v3/'],
            blockExplorerUrls: ['https://polygonscan.com/'],
            tokenAddress: '******************************************',
            decimals: 6,
            isNative: false
        },
        'MATIC': {
            id: 137,
            name: 'Polygon Mainnet',
            chainName: 'Polygon',
            nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
            rpcUrls: ['https://polygon-rpc.com/', 'https://rpc-mainnet.matic.network'],
            blockExplorerUrls: ['https://polygonscan.com/'],
            tokenAddress: null,
            decimals: 18,
            isNative: true
        },

        // BSC网络代币
        'USDTBSC': {
            id: 56,
            name: 'BNB Smart Chain',
            chainName: 'BSC',
            nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
            rpcUrls: ['https://bsc-dataseed.binance.org/', 'https://bsc-dataseed1.binance.org/', 'https://bsc-dataseed2.binance.org/'],
            blockExplorerUrls: ['https://bscscan.com/'],
            tokenAddress: '******************************************',
            decimals: 18,
            isNative: false
        },
        'BNB': {
            id: 56,
            name: 'BNB Smart Chain',
            chainName: 'BSC',
            nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
            rpcUrls: ['https://bsc-dataseed.binance.org/', 'https://bsc-dataseed1.defibit.io/'],
            blockExplorerUrls: ['https://bscscan.com/'],
            tokenAddress: null,
            decimals: 18,
            isNative: true
        },

        // Arbitrum网络代币
        'USDTARB': {
            id: 42161,
            name: 'Arbitrum One',
            chainName: 'Arbitrum',
            nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
            rpcUrls: ['https://arb1.arbitrum.io/rpc', 'https://arbitrum-mainnet.infura.io/v3/'],
            blockExplorerUrls: ['https://arbiscan.io/'],
            tokenAddress: '******************************************', // 正确的Arbitrum USDT地址
            decimals: 6,
            isNative: false
        }
    };

    // 直接匹配
    if (networkMap[currencyUpper]) {
        return networkMap[currencyUpper];
    }

    // 智能匹配 - 处理NowPayments动态币种
    // 检查是否包含网络标识符
    if (currencyUpper.includes('TRC20') || currencyUpper.includes('USDTTRC20')) {
        return {
            id: 'tron',
            name: 'Tron Network',
            chainName: 'Tron',
            nativeCurrency: { name: 'TRX', symbol: 'TRX', decimals: 6 },
            rpcUrls: ['https://api.trongrid.io'],
            blockExplorerUrls: ['https://tronscan.org/'],
            tokenAddress: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
            decimals: 6,
            isNative: false,
            isNonEVM: true
        };
    }

    if (currencyUpper.includes('SOL') || currencyUpper.includes('USDTSOL')) {
        return {
            id: 'solana',
            name: 'Solana Network',
            chainName: 'Solana',
            nativeCurrency: { name: 'SOL', symbol: 'SOL', decimals: 9 },
            rpcUrls: ['https://api.mainnet-beta.solana.com'],
            blockExplorerUrls: ['https://explorer.solana.com/'],
            tokenAddress: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
            decimals: 6,
            isNative: false,
            isNonEVM: true
        };
    }

    // 默认返回以太坊主网（适用于大多数ERC20代币）
    console.warn(`No specific network configuration found for ${currency}, defaulting to Ethereum`);
    return {
        id: 1,
        name: 'Ethereum Mainnet',
        chainName: 'Ethereum',
        nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
        rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
        blockExplorerUrls: ['https://etherscan.io/'],
        tokenAddress: null, // 需要动态查询
        decimals: 18, // 默认精度
        isNative: false
    };
}

function getTokenAddressForCurrency(currency, networkConfig) {
    // 直接从网络配置中获取代币地址
    if (networkConfig && networkConfig.tokenAddress) {
        return networkConfig.tokenAddress;
    }
    return null;
}

function isNativeToken(currency, networkConfig) {
    // 从网络配置中判断是否为原生代币
    if (networkConfig) {
        return networkConfig.isNative === true;
    }

    // 备用判断逻辑
    const nativeTokens = ['ETH', 'BNB', 'MATIC', 'TRX', 'SOL'];
    return nativeTokens.includes(currency.toUpperCase());
}

function getTokenDecimals(currency, networkConfig) {
    // 从网络配置中获取代币精度
    if (networkConfig && networkConfig.decimals) {
        return networkConfig.decimals;
    }

    // 备用精度配置
    const decimalMap = {
        'DAI': 18,
        'USDC': 6,
        'USDT': 6,
        'USDCMATIC': 6,
        'USDTBSC': 18,
        'USDTSOL': 6,
        'USDTTRC20': 6
    };

    return decimalMap[currency.toUpperCase()] || 18;
}



// 将金额转换为Wei单位（用于以太坊交易）
function parseUnits(amount, decimals) {
    console.log(`🔢 parseUnits: ${amount} with ${decimals} decimals`);

    // 确保输入是有效数字
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
        console.error('❌ Invalid amount for parseUnits:', amount);
        return '0';
    }

    // 使用BigInt确保精度
    const multiplier = BigInt(10 ** decimals);
    const amountBigInt = BigInt(Math.floor(numAmount * (10 ** decimals)));

    console.log(`🔢 parseUnits result: ${amountBigInt.toString()}`);
    return amountBigInt.toString();
}

// 将Wei单位转换为可读金额
function formatUnits(amount, decimals) {
    console.log(`🔢 formatUnits: ${amount} with ${decimals} decimals`);

    const divisor = Math.pow(10, decimals);
    const result = (parseFloat(amount) / divisor).toString();

    console.log(`🔢 formatUnits result: ${result}`);
    return result;
}

// Initialize Web3 integration when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait for all modules to load
    setTimeout(() => {
        console.log('Initializing Web3 integration...');

        // Check if all required modules are loaded
        if (typeof window.Web3Config === 'undefined') {
            console.warn('Web3Config not loaded');
            return;
        }

        if (typeof window.walletManager === 'undefined') {
            console.warn('WalletManager not loaded');
            return;
        }

        if (typeof window.paymentIntegration === 'undefined') {
            console.warn('PaymentIntegration not loaded');
            return;
        }

        // Set up wallet event listeners
        if (window.walletManager) {
            window.walletManager.onConnect(onWalletConnected);
            window.walletManager.onDisconnect(onWalletDisconnected);
            window.walletManager.onChainChanged((chainId) => {
                if (walletConnected) {
                    checkNetworkCompatibility(chainId);
                }
            });
        }

        console.log('Web3 integration initialized successfully');

        // 初始化动态币种配置
        initializeDynamicConfigs();

        // 初始化WalletConnect支付系统
        initializePaymentSystem();
    }, 2000);
});

// 初始化动态币种配置
async function initializeDynamicConfigs() {
    try {
        console.log('🔄 Initializing dynamic currency configs...');

        const result = await loadDynamicCurrencyConfigs();
        if (result && result.configs) {
            globalDynamicConfigs = result.configs;
            // 🔧 修复：设置到window对象供其他函数使用
            window.dynamicCurrencyConfigs = result.configs;
            console.log('✅ Dynamic configs initialized:', globalDynamicConfigs);
        }
    } catch (error) {
        console.error('❌ Failed to initialize dynamic configs:', error);
    }
}

// 初始化WalletConnect v2并监听连接
async function initializePaymentSystem() {
    try {
        console.log('🔄 Initializing WalletConnect v2 payment system...');

        // 初始化WalletConnect v2
        const wcInitialized = await initializeWalletConnect();
        if (wcInitialized) {
            console.log('✅ WalletConnect v2 initialized successfully');

            // 检查是否需要创建真正的WalletConnect会话
            await handleWalletConnectSession();
        } else {
            console.warn('⚠️ WalletConnect v2 initialization failed');
        }

    } catch (error) {
        console.error('❌ Failed to initialize payment system:', error);
    }
}

// 处理WalletConnect会话创建（页面初始化时调用）
async function handleWalletConnectSession() {
    console.log('🔄 Handling WalletConnect session (初始化时)...');

    // 检查后端是否返回了占位符
    if (window.paymentData && window.paymentData.walletconnect_uri) {
        const uri = window.paymentData.walletconnect_uri;

        if (uri.startsWith('FRONTEND_CREATE_WC_SESSION:')) {
            console.log('🔄 检测到占位符，创建真正的WalletConnect v2会话...');

            // 解析占位符信息
            const parts = uri.split(':');
            const currency = parts[1];
            const address = parts[2];
            const amount = parts[3];
            const chainId = parts[4];

            console.log(`🔄 支付信息: ${amount} ${currency} 到 ${address} (链ID: ${chainId})`);

            // 创建真正的WalletConnect会话
            const realUri = await createRealWalletConnectSession();
            if (realUri) {
                console.log('✅ 真正的WalletConnect v2 URI已创建:', realUri);

                // 更新支付数据中的URI
                window.paymentData.walletconnect_uri = realUri;

                // 🔧 修复：直接在前端生成WalletConnect二维码
                console.log('🔄 直接生成WalletConnect二维码');

                // 直接在前端生成WalletConnect二维码
                await generateWalletConnectQRCode(realUri);
            } else {
                console.error('❌ 创建WalletConnect会话失败');
            }
        } else {
            console.log('✅ 使用现有的WalletConnect URI:', uri);
        }
    } else {
        console.log('⚠️ 未找到WalletConnect URI数据');
    }
}

// 处理WalletConnect会话创建（支付数据加载后调用）
async function handleWalletConnectSessionAfterPaymentLoad() {
    console.log('🔄 Handling WalletConnect session (支付数据加载后)...');

    // 确保SignClient已初始化
    if (!signClient) {
        console.log('🔄 SignClient未初始化，正在初始化...');
        const initialized = await initializeWalletConnect();
        if (!initialized) {
            console.error('❌ 无法初始化WalletConnect');
            return;
        }
    }

    // 检查后端是否返回了占位符
    if (window.paymentData && window.paymentData.walletconnect_uri) {
        const uri = window.paymentData.walletconnect_uri;

        if (uri.startsWith('FRONTEND_CREATE_WC_SESSION:')) {
            console.log('🔄 检测到占位符，创建真正的WalletConnect v2会话...');

            // 解析占位符信息
            const parts = uri.split(':');
            const currency = parts[1];
            const address = parts[2];
            const amount = parts[3];
            const chainId = parts[4];

            console.log(`🔄 支付信息: ${amount} ${currency} 到 ${address} (链ID: ${chainId})`);

            // 创建真正的WalletConnect会话
            const realUri = await createRealWalletConnectSession();
            if (realUri) {
                console.log('✅ 真正的WalletConnect v2 URI已创建:', realUri);

                // 更新支付数据中的URI
                window.paymentData.walletconnect_uri = realUri;

                // 🔧 修复：直接在前端生成WalletConnect二维码
                console.log('🔄 直接生成WalletConnect二维码');

                // 直接在前端生成WalletConnect二维码
                await generateWalletConnectQRCode(realUri);
            } else {
                console.error('❌ 创建WalletConnect会话失败');
            }
        } else {
            console.log('✅ 使用现有的WalletConnect URI:', uri);
        }
    } else {
        console.log('⚠️ 未找到WalletConnect URI数据');
    }
}

// 获取CSRF Token
function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        return csrfToken.value;
    }

    // 从cookie中获取CSRF token
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
            return value;
        }
    }

    console.warn('⚠️ 未找到CSRF token');
    return '';
}

// 使用真正的URI重新生成二维码
async function regenerateQRCodeWithRealURI(realUri) {
    console.log('🔄 使用真正的URI重新生成二维码...');

    try {
        // 检查是否有支付数据
        if (!window.paymentData || !window.paymentData.payment_id) {
            console.error('❌ 未找到支付数据');
            return false;
        }

        const paymentId = window.paymentData.payment_id;
        console.log('🔄 调用后端API重新生成二维码...');
        console.log('🔄 Payment ID:', paymentId);
        console.log('🔄 WalletConnect URI:', realUri.substring(0, 50) + '...');

        // 调用后端API重新生成二维码
        const response = await fetch('/api/v1/payments/nowpayments/regenerate-qr/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                payment_id: paymentId,
                walletconnect_uri: realUri
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'API返回失败');
        }

        // 更新页面上的二维码
        const qrImg = document.querySelector('img[alt="Payment QR Code"]');
        if (qrImg && data.data.qr_code) {
            const newQrSrc = `data:image/png;base64,${data.data.qr_code}`;
            qrImg.src = newQrSrc;
            console.log('✅ 二维码图片已更新');
        } else {
            console.error('❌ 未找到二维码图片元素或后端未返回二维码数据');
        }

        console.log('✅ 二维码重新生成成功');
        return true;

    } catch (error) {
        console.error('❌ 重新生成二维码失败:', error);

        // 如果API调用失败，尝试直接更新URI（虽然二维码内容不会改变）
        console.log('🔄 API调用失败，但WalletConnect URI已更新到前端数据中');
        return false;
    }
}

// 🔧 新增：直接在前端生成WalletConnect二维码
async function generateWalletConnectQRCode(walletConnectURI) {
    try {
        console.log('🔄 在前端生成WalletConnect二维码...');
        console.log('🔍 DEBUG: 传入参数类型:', typeof walletConnectURI);
        console.log('🔍 DEBUG: 传入参数长度:', walletConnectURI ? walletConnectURI.length : 'null');
        console.log('🔍 DEBUG: 传入参数内容:', walletConnectURI);
        console.log('🔄 WalletConnect URI:', walletConnectURI ? walletConnectURI.substring(0, 50) + '...' : 'null');

        // 🔧 保存URI到全局变量
        window.currentWalletConnectURI = walletConnectURI;

        // 使用QRCode.js库生成二维码
        const qrContainerForWC = document.querySelector('#qr-code-container');
        if (!qrContainerForWC) {
            console.error('❌ 找不到二维码容器');
            return;
        }

        // 清空容器
        qrContainerForWC.innerHTML = '';

        // 创建二维码
        const qrCodeDiv = document.createElement('div');
        qrCodeDiv.style.display = 'flex';
        qrCodeDiv.style.justifyContent = 'center';
        qrCodeDiv.style.alignItems = 'center';
        qrContainerForWC.appendChild(qrCodeDiv);

        // 🔧 修复：使用正确的本地二维码库
        console.log('🔄 尝试使用本地二维码库生成二维码...');

        // 优先使用qrcode-generator库（本地库）
        if (typeof qrcode !== 'undefined') {
            try {
                console.log('✅ 使用qrcode-generator库生成二维码');

                // 🔧 修复：创建二维码对象，使用更高版本支持长URI
                // WalletConnect v2 URI约200字符，需要版本10或更高
                const qr = qrcode(0, 'M'); // 版本0=自动选择，容错级别M
                qr.addData(walletConnectURI);
                qr.make();

                // 创建二维码图片
                const canvas = document.createElement('canvas');
                const size = 220;
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // 绘制二维码
                const moduleCount = qr.getModuleCount();
                const cellSize = size / moduleCount;

                // 白色背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, size, size);

                // 黑色模块
                ctx.fillStyle = '#000000';
                for (let row = 0; row < moduleCount; row++) {
                    for (let col = 0; col < moduleCount; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                // 添加样式
                canvas.style.borderRadius = '8px';
                canvas.style.border = '1px solid #dee2e6';
                canvas.style.display = 'block';
                canvas.style.margin = '0 auto';

                qrCodeDiv.appendChild(canvas);
                console.log('✅ 使用qrcode-generator库生成二维码成功');

            } catch (error) {
                console.error('❌ qrcode-generator库生成失败:', error);
                // 使用备用方案
                generateQRCodeFallback(qrCodeDiv, walletConnectURI);
            }
        } else if (typeof QRCode !== 'undefined' && QRCode.toDataURL) {
            // 备用方案1：使用QRCode库（如果可用）
            console.log('🔄 使用QRCode库作为备用方案');
            QRCode.toDataURL(walletConnectURI, {
                width: 220,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#ffffff'
                },
                errorCorrectionLevel: 'M'
            }, function (error, url) {
                if (error) {
                    console.error('❌ QRCode.toDataURL失败:', error);
                    generateQRCodeFallback(qrCodeDiv, walletConnectURI);
                } else {
                    console.log('✅ 使用QRCode库生成二维码成功');
                    const img = document.createElement('img');
                    img.src = url;
                    img.alt = 'WalletConnect QR Code';
                    img.style.width = '220px';
                    img.style.height = '220px';
                    img.style.borderRadius = '8px';
                    img.style.border = '1px solid #dee2e6';
                    img.style.display = 'block';
                    img.style.margin = '0 auto';
                    qrCodeDiv.appendChild(img);
                }
            });
        } else {
            console.log('⚠️ 本地二维码库都不可用，使用在线API备用方案');
            // 备用方案2：使用在线API生成二维码
            generateQRCodeFallback(qrCodeDiv, walletConnectURI);
        }

    } catch (error) {
        console.error('❌ 生成WalletConnect二维码时发生错误:', error);
    }
}

// 🔧 改进的备用方案：使用多个在线API生成二维码
function generateQRCodeFallback(container, walletConnectURI) {
    console.log('🔄 使用备用方案生成二维码...');

    const img = document.createElement('img');
    const encodedURI = encodeURIComponent(walletConnectURI);

    // 尝试多个二维码服务
    const qrServices = [
        `https://api.qrserver.com/v1/create-qr-code/?size=220x220&ecc=M&data=${encodedURI}`,
        `https://chart.googleapis.com/chart?chs=220x220&cht=qr&chl=${encodedURI}`,
        `https://qr-code-generator.com/api/qr-code?size=220&data=${encodedURI}`
    ];

    let currentServiceIndex = 0;

    function tryNextService() {
        if (currentServiceIndex >= qrServices.length) {
            console.error('❌ 所有二维码服务都失败了');
            // 显示错误信息
            container.innerHTML = `
                <div style="width: 220px; height: 220px; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6; border-radius: 8px; background-color: #f8f9fa;">
                    <div style="text-align: center; color: #6c757d;">
                        <div style="font-size: 16px; margin-bottom: 10px;">⚠️</div>
                        <div style="font-size: 14px;">QR Code generation failed</div>
                        <div style="font-size: 12px; margin-top: 5px;">Please try refreshing the page</div>
                    </div>
                </div>
            `;
            return;
        }

        img.src = qrServices[currentServiceIndex];
        console.log(`🔄 尝试二维码服务 ${currentServiceIndex + 1}/${qrServices.length}: ${qrServices[currentServiceIndex].substring(0, 50)}...`);
        currentServiceIndex++;
    }

    img.alt = 'WalletConnect QR Code';
    img.style.width = '220px';
    img.style.height = '220px';
    img.style.borderRadius = '8px';
    img.style.border = '1px solid #dee2e6';
    img.style.display = 'block';
    img.style.margin = '0 auto';

    img.onload = function() {
        console.log('✅ 使用在线API生成二维码成功');
    };

    img.onerror = function() {
        console.error(`❌ 二维码服务 ${currentServiceIndex} 失败，尝试下一个...`);
        tryNextService();
    };

    // 开始尝试第一个服务
    tryNextService();
    container.appendChild(img);
}

</script>
{% endblock %}
