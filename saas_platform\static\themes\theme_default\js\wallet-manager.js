// Wallet Connection and Management Module
// Handles wallet connection, network switching, and transaction management

class WalletManager {
    constructor() {
        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.web3Modal = null;
        this.wagmiConfig = null;
        this.provider = null;
        this.signer = null;
        
        // Event listeners
        this.onConnectCallbacks = [];
        this.onDisconnectCallbacks = [];
        this.onChainChangedCallbacks = [];
        this.onAccountChangedCallbacks = [];
        
        this.init();
    }
    
    async init() {
        // Wait for Web3Config to be available
        if (typeof window.Web3Config === 'undefined') {
            setTimeout(() => this.init(), 500);
            return;
        }

        // Check if MetaMask is available
        if (typeof window.ethereum !== 'undefined') {
            this.provider = window.ethereum;
            console.log('MetaMask provider detected');

            // Listen for connection events
            this.setupEventListeners();

            // Check if already connected
            await this.checkConnection();
        } else {
            console.error('MetaMask not detected. Please install MetaMask.');
        }
    }
    
    setupEventListeners() {
        if (!this.provider) return;

        // Listen for MetaMask events
        this.provider.on('accountsChanged', (accounts) => {
            console.log('Accounts changed:', accounts);
            if (accounts.length === 0) {
                this.handleDisconnectSuccess();
            } else {
                this.currentAccount = accounts[0];
                this.notifyAccountChanged(accounts[0]);
            }
        });

        this.provider.on('chainChanged', (chainId) => {
            console.log('Chain changed:', chainId);
            this.currentChainId = parseInt(chainId, 16);
            this.notifyChainChanged(this.currentChainId);
        });

        this.provider.on('disconnect', () => {
            console.log('MetaMask disconnected');
            this.handleDisconnectSuccess();
        });
    }
    
    async checkConnection() {
        try {
            if (!this.provider) return;

            // Check if MetaMask is already connected
            const accounts = await this.provider.request({ method: 'eth_accounts' });
            if (accounts && accounts.length > 0) {
                this.isConnected = true;
                this.currentAccount = accounts[0];

                // Get current chain ID
                const chainId = await this.provider.request({ method: 'eth_chainId' });
                this.currentChainId = parseInt(chainId, 16);

                this.notifyConnected();
            }
        } catch (error) {
            console.error('Error checking connection:', error);
        }
    }
    
    async connect() {
        // Check if MetaMask is available
        if (typeof window.ethereum === 'undefined') {
            throw new Error('MetaMask not detected. Please install MetaMask.');
        }

        this.provider = window.ethereum;

        try {
            console.log('Connecting to MetaMask...');
            const accounts = await this.provider.request({
                method: 'eth_requestAccounts'
            });

            if (accounts && accounts.length > 0) {
                this.isConnected = true;
                this.currentAccount = accounts[0];

                // Get current chain ID
                const chainId = await this.provider.request({ method: 'eth_chainId' });
                this.currentChainId = parseInt(chainId, 16);

                this.notifyConnected();
                return { account: this.currentAccount, chainId: this.currentChainId };
            }
        } catch (error) {
            console.error('Failed to connect to MetaMask:', error);
            throw error;
        }
    }
    
    async disconnect() {
        try {
            // MetaMask doesn't have a programmatic disconnect method
            // We just clear our local state
            this.handleDisconnectSuccess();
            console.log('Wallet disconnected locally');
        } catch (error) {
            console.error('Failed to disconnect:', error);
            throw error;
        }
    }
    
    async switchNetwork(chainId) {
        if (!this.isConnected || !this.provider) {
            throw new Error('Wallet not connected');
        }

        try {
            // 将chainId转换为十六进制格式
            const hexChainId = '0x' + chainId.toString(16);

            console.log(`Switching to network: ${chainId} (${hexChainId})`);

            // 使用MetaMask直接API切换网络
            await this.provider.request({
                method: 'wallet_switchEthereumChain',
                params: [{ chainId: hexChainId }],
            });

            this.currentChainId = chainId;
            this.notifyChainChanged(chainId);

            console.log(`Successfully switched to network: ${chainId}`);
        } catch (error) {
            console.error('Failed to switch network:', error);

            // 如果网络不存在，尝试添加网络
            if (error.code === 4902) {
                await this.addNetwork(chainId);
            } else {
                throw error;
            }
        }
    }

    async addNetwork(chainId) {
        const networkConfigs = {
            1: {
                chainId: '0x1',
                chainName: 'Ethereum Mainnet',
                nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
                rpcUrls: ['https://mainnet.infura.io/v3/', 'https://eth-mainnet.alchemyapi.io/v2/'],
                blockExplorerUrls: ['https://etherscan.io/']
            },
            56: {
                chainId: '0x38',
                chainName: 'BNB Smart Chain',
                nativeCurrency: { name: 'BNB', symbol: 'BNB', decimals: 18 },
                rpcUrls: ['https://bsc-dataseed.binance.org/'],
                blockExplorerUrls: ['https://bscscan.com/']
            },
            137: {
                chainId: '0x89',
                chainName: 'Polygon',
                nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
                rpcUrls: ['https://polygon-rpc.com/'],
                blockExplorerUrls: ['https://polygonscan.com/']
            }
        };

        const config = networkConfigs[chainId];
        if (!config) {
            throw new Error(`Network ${chainId} not supported`);
        }

        try {
            await this.provider.request({
                method: 'wallet_addEthereumChain',
                params: [config],
            });

            // 添加成功后切换到该网络
            await this.switchNetwork(chainId);
        } catch (error) {
            console.error('Failed to add network:', error);
            throw error;
        }
    }
    
    async getBalance(tokenAddress = null) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }
        
        try {
            if (tokenAddress && tokenAddress !== 'native') {
                // Get ERC20 token balance
                return await window.wagmi?.readContract?.({
                    address: tokenAddress,
                    abi: [
                        {
                            name: 'balanceOf',
                            type: 'function',
                            stateMutability: 'view',
                            inputs: [{ name: 'account', type: 'address' }],
                            outputs: [{ name: 'balance', type: 'uint256' }],
                        },
                    ],
                    functionName: 'balanceOf',
                    args: [this.currentAccount],
                });
            } else {
                // Get native token balance
                return await window.wagmi?.getBalance?.({
                    address: this.currentAccount,
                });
            }
        } catch (error) {
            console.error('Failed to get balance:', error);
            throw error;
        }
    }
    
    async sendTransaction(to, amount, tokenAddress = null, decimals = 18) {
        if (!this.isConnected) {
            throw new Error('Wallet not connected');
        }

        console.log(`Sending transaction: ${amount} to ${to}`);
        console.log(`Token address: ${tokenAddress}, Decimals: ${decimals}`);

        try {
            if (tokenAddress && tokenAddress !== 'native') {
                // Send ERC20 token
                return await this.sendTokenTransaction(to, amount, tokenAddress, decimals);
            } else {
                // Send native token
                return await this.sendNativeTransaction(to, amount);
            }
        } catch (error) {
            console.error('Failed to send transaction:', error);
            throw error;
        }
    }
    
    async sendNativeTransaction(to, amount) {
        if (!this.provider) {
            throw new Error('No wallet provider available');
        }

        try {
            // 将人类可读的金额转换为Wei单位
            const weiAmount = this.parseEther(amount);
            console.log(`Converting ${amount} ETH to Wei: ${weiAmount.toString()}`);

            // 使用MetaMask直接发送交易
            const txParams = {
                to: to,
                value: '0x' + weiAmount.toString(16), // BigInt转换为十六进制
                from: this.currentAccount,
                gas: '0x' + (21000).toString(16), // 设置标准ETH转账gas limit (21000)
            };

            const txHash = await this.provider.request({
                method: 'eth_sendTransaction',
                params: [txParams],
            });

            if (!txHash) {
                throw new Error('Transaction failed: No transaction hash returned');
            }

            return txHash;
        } catch (error) {
            console.error('Native transaction failed:', error);
            throw error;
        }
    }
    
    async sendTokenTransaction(to, amount, tokenAddress, decimals = 18) {
        if (!this.provider) {
            throw new Error('No wallet provider available');
        }

        try {
            // 将人类可读的金额转换为代币的最小单位
            const tokenAmount = this.parseTokenAmount(amount, decimals);
            console.log(`Converting ${amount} tokens (${decimals} decimals) to smallest unit: ${tokenAmount.toString()}`);

            // 构建ERC20 transfer调用数据
            const transferData = this.encodeTransferData(to, tokenAmount);

            console.log(`Token transfer details:`, {
                tokenAddress: tokenAddress,
                to: to,
                amount: amount,
                tokenAmount: tokenAmount.toString(),
                decimals: decimals,
                transferDataLength: transferData.length
            });

            const txParams = {
                to: tokenAddress,
                data: transferData,
                from: this.currentAccount,
                value: '0x0', // ERC20转账不发送ETH，value必须为0
                gas: '0x' + (65000).toString(16), // 设置合理的gas limit (65000)
            };

            const txHash = await this.provider.request({
                method: 'eth_sendTransaction',
                params: [txParams],
            });

            if (!txHash) {
                throw new Error('Token transaction failed: No transaction hash returned');
            }

            return txHash;
        } catch (error) {
            console.error('Token transaction failed:', error);
            throw error;
        }
    }

    // 将人类可读的ETH金额转换为Wei (使用BigInt处理大数)
    parseEther(amount) {
        try {
            // 使用BigInt来处理大整数，避免精度问题
            const amountStr = parseFloat(amount).toFixed(18); // 确保精度
            const [integerPart, decimalPart = ''] = amountStr.split('.');
            const paddedDecimal = decimalPart.padEnd(18, '0').slice(0, 18);
            const weiString = integerPart + paddedDecimal;
            const weiBigInt = BigInt(weiString);

            console.log(`parseEther: ${amount} ETH -> ${weiBigInt.toString()} Wei`);
            return weiBigInt;
        } catch (error) {
            console.error('Error parsing ETH amount:', error);
            throw new Error(`Invalid ETH amount: ${amount}`);
        }
    }

    // 将人类可读的代币金额转换为最小单位 (使用BigInt处理大数)
    parseTokenAmount(amount, decimals) {
        try {
            // 使用BigInt来处理大整数，避免精度问题
            const amountStr = parseFloat(amount).toFixed(decimals); // 确保精度
            const [integerPart, decimalPart = ''] = amountStr.split('.');
            const paddedDecimal = decimalPart.padEnd(decimals, '0').slice(0, decimals);
            const tokenString = integerPart + paddedDecimal;
            const tokenBigInt = BigInt(tokenString);

            console.log(`parseTokenAmount: ${amount} tokens (${decimals} decimals) -> ${tokenBigInt.toString()}`);
            return tokenBigInt;
        } catch (error) {
            console.error('Error parsing token amount:', error);
            throw new Error(`Invalid token amount: ${amount}`);
        }
    }

    // 编码ERC20 transfer函数调用数据
    encodeTransferData(to, amount) {
        try {
            // ERC20 transfer函数签名: transfer(address,uint256)
            const functionSignature = '0xa9059cbb'; // transfer(address,uint256)的前4字节
            const addressParam = to.slice(2).padStart(64, '0'); // 移除0x并填充到64字符

            // 处理BigInt类型的amount
            let amountHex;
            if (typeof amount === 'bigint') {
                amountHex = amount.toString(16);
            } else {
                amountHex = BigInt(amount).toString(16);
            }
            const amountParam = amountHex.padStart(64, '0'); // 转换为十六进制并填充

            const encodedData = functionSignature + addressParam + amountParam;
            console.log(`encodeTransferData: to=${to}, amount=${amount.toString()}, encoded=${encodedData}`);
            return encodedData;
        } catch (error) {
            console.error('Error encoding transfer data:', error);
            throw new Error(`Failed to encode transfer data: ${error.message}`);
        }
    }
    
    // Event handling
    handleConnectSuccess(data) {
        this.isConnected = true;
        this.currentAccount = data?.address;
        this.currentChainId = data?.chainId;
        this.notifyConnected();
    }
    
    handleDisconnectSuccess() {
        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;
        this.notifyDisconnected();
    }
    
    handleConnectError(error) {
        console.error('Connection error:', error);
        // You can add user notification here
    }
    
    // Event notification methods
    notifyConnected() {
        this.onConnectCallbacks.forEach(callback => {
            try {
                callback({
                    account: this.currentAccount,
                    chainId: this.currentChainId
                });
            } catch (error) {
                console.error('Error in connect callback:', error);
            }
        });
    }
    
    notifyDisconnected() {
        this.onDisconnectCallbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('Error in disconnect callback:', error);
            }
        });
    }
    
    notifyChainChanged(chainId) {
        this.onChainChangedCallbacks.forEach(callback => {
            try {
                callback(chainId);
            } catch (error) {
                console.error('Error in chain changed callback:', error);
            }
        });
    }
    
    notifyAccountChanged(account) {
        this.onAccountChangedCallbacks.forEach(callback => {
            try {
                callback(account);
            } catch (error) {
                console.error('Error in account changed callback:', error);
            }
        });
    }
    
    // Event listener registration
    onConnect(callback) {
        this.onConnectCallbacks.push(callback);
    }
    
    onDisconnect(callback) {
        this.onDisconnectCallbacks.push(callback);
    }
    
    onChainChanged(callback) {
        this.onChainChangedCallbacks.push(callback);
    }
    
    onAccountChanged(callback) {
        this.onAccountChangedCallbacks.push(callback);
    }
    
    // Utility methods
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            account: this.currentAccount,
            chainId: this.currentChainId
        };
    }
    
    formatAddress(address) {
        if (!address) return '';
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    }
    
    formatBalance(balance, decimals = 18) {
        if (!balance) return '0';
        return (Number(balance) / Math.pow(10, decimals)).toFixed(4);
    }
}

// Create global wallet manager instance
window.walletManager = new WalletManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WalletManager;
}
