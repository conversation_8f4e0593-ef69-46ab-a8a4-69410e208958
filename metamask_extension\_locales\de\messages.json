{"QRHardwareInvalidTransactionTitle": {"message": "<PERSON><PERSON>"}, "QRHardwareMismatchedSignId": {"message": "Inkongruente Transaktionsdaten. Bitte überprüfen Sie die Transaktionsdetails."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "<PERSON>ine weiteren Konten. Wenn <PERSON> auf ein Konto zugreifen möchten, das unten nicht aufgelistet ist, verbinden Sie bitte erneut Ihre Hardware-Wallet und wählen Sie diese."}, "QRHardwareScanInstructions": {"message": "Platzieren Sie den QR-Code vor Ihrer Kamera. Der Bildschirm ist verschwommen, das wirkt sich aber nicht auf das Scannen aus."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "Nachdem Si<PERSON> sich mit Ihrer Wallet angemeldet haben, klicken Sie auf 'Signatur erhalten', um die Signatur zu bekommen."}, "QRHardwareSignRequestGetSignature": {"message": "Signatur abrufen"}, "QRHardwareSignRequestSubtitle": {"message": "Scannen Sie den QR-Code mit Ihrer Wallet."}, "QRHardwareSignRequestTitle": {"message": "Signatur abrufen"}, "QRHardwareUnknownQRCodeTitle": {"message": "<PERSON><PERSON>"}, "QRHardwareUnknownWalletQRCode": {"message": "Ungültiger QR-Code. Bitte scannen Sie den QR-Code der Hardware-Wallet."}, "QRHardwareWalletImporterTitle": {"message": "QR-Code scannen"}, "QRHardwareWalletSteps1Description": {"message": "Sie können aus der folgenden Liste offizieller QR-Code-unterstützender Partner wählen."}, "QRHardwareWalletSteps1Title": {"message": "Verbinden Sie Ihre QR-basierte Hardware-Wallet."}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "$1 Konten verbergen", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "$1 Konto verbergen"}, "SrpListShowAccounts": {"message": "$1-Konten anzeigen", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "1 Konto anzeigen"}, "about": {"message": "<PERSON><PERSON>"}, "accept": {"message": "Akzeptieren"}, "acceptTermsOfUse": {"message": "Ich habe die $1 gelesen und stimme ihnen zu.", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Zugriff auf Ihre Kamera …"}, "account": {"message": "Ko<PERSON>"}, "accountActivity": {"message": "Kontoaktivität"}, "accountActivityText": {"message": "Wählen Sie die Konten aus, über die Sie benachrichtigt werden möchten:"}, "accountDetails": {"message": "Kontodetails"}, "accountIdenticon": {"message": "Konto-Identicon"}, "accountIsntConnectedToastText": {"message": "$1 ist nicht mit $2 verbunden"}, "accountName": {"message": "<PERSON><PERSON><PERSON>"}, "accountNameDuplicate": {"message": "Dieser Kontoname existiert bereits.", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Dieser Kontoname ist reserviert.", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Kontooptionen"}, "accountPermissionToast": {"message": "Kontogenehmigungen aktualisiert"}, "accountSelectionRequired": {"message": "Sie müssen ein Konto auswählen!"}, "accountTypeNotSupported": {"message": "Kontotyp nicht unterstützt"}, "accounts": {"message": "Konten"}, "accountsConnected": {"message": "Konten wurden verbunden"}, "accountsPermissionsTitle": {"message": "Ihre Konten einsehen und Transaktionen vorschlagen"}, "accountsSmallCase": {"message": "Konten"}, "active": {"message": "Aktiv"}, "activity": {"message": "Aktivität"}, "activityLog": {"message": "Aktivitätsprotokoll"}, "add": {"message": "Hinzufügen"}, "addACustomNetwork": {"message": "Benutzerdefiniertes Netzwerk hinzufügen"}, "addANetwork": {"message": "Ein neues Netzwerk hinzufügen"}, "addANickname": {"message": "Spitznamen hinzufügen"}, "addAUrl": {"message": "URL hinzufügen"}, "addAccount": {"message": "<PERSON><PERSON> hi<PERSON>uf<PERSON>"}, "addAccountFromNetwork": {"message": "$1-<PERSON><PERSON> hinzufügen", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "<PERSON><PERSON> zu MetaMask hinzufügen"}, "addAcquiredTokens": {"message": "Fügen Sie die Tokens hinzu, die Sie mittels MetaMask erlangt haben."}, "addAlias": {"message": "<PERSON><PERSON>"}, "addBitcoinAccountLabel": {"message": "Bitcoin-Konto"}, "addBlockExplorer": {"message": "Einen Block-Explorer hinzufügen"}, "addBlockExplorerUrl": {"message": "Eine Block-Explorer-URL hinzufügen"}, "addContact": {"message": "Kontakt hinzufügen"}, "addCustomNetwork": {"message": "Benutzerdefiniertes Netzwerk hinzufügen"}, "addEthereumChainWarningModalHeader": {"message": "<PERSON>ügen Sie diesen RPC-<PERSON><PERSON><PERSON> nur hinzu, wenn <PERSON> sich sicher sind, dass Sie ihm vertrauen können. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Betrügerische Anbieter könnten in Hinischt auf den Zustand der Blockchain lügen und Ihre Netzwerkaktivitäten aufzeichnen."}, "addEthereumChainWarningModalListHeader": {"message": "<PERSON><PERSON> ist wichtig, dass Si<PERSON> sich auf Ihren Anbieter verlassen können, da er die Fähigkeit dazu hat:"}, "addEthereumChainWarningModalListPointOne": {"message": "– <PERSON><PERSON>e Konten und IP-Adressen einzusehen und diese miteinander in Verbindung zu setzen."}, "addEthereumChainWarningModalListPointThree": {"message": "– Kontoguthaben und andere On-Chain-Zustände anzuzeigen."}, "addEthereumChainWarningModalListPointTwo": {"message": "– Ihre Transaktionen zu veröffentlichen."}, "addEthereumChainWarningModalTitle": {"message": "Sie fügen einen neuen RPC-Anbieter für das Ethereum-Hauptnetz hinzu."}, "addEthereumWatchOnlyAccount": {"message": "Ein Ethereum-<PERSON><PERSON> (Beta)"}, "addFriendsAndAddresses": {"message": "Freunde und Adressen hinzufügen, welchen Sie vertrauen"}, "addHardwareWalletLabel": {"message": "Hardware-Wallet"}, "addIPFSGateway": {"message": "Fügen Sie Ihr bevorzugtes IPFS-Gateway hinzu."}, "addImportAccount": {"message": "Konto oder Hardware-Wallet hinzufügen"}, "addMemo": {"message": "<PERSON><PERSON>"}, "addNetwork": {"message": "Netzwerk hinzufügen"}, "addNetworkConfirmationTitle": {"message": "$1 hinzufügen", "description": "$1 represents network name"}, "addNewAccount": {"message": "Ein neues Konto hinzufügen"}, "addNewEthereumAccountLabel": {"message": "Ethereum-Konto"}, "addNewSolanaAccountLabel": {"message": "Solana-Konto"}, "addNft": {"message": "NFT hinzufügen"}, "addNfts": {"message": "NFTs hinzufügen"}, "addNonEvmAccount": {"message": "$1-<PERSON><PERSON> hinzufügen", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Um das $1-Netzwerk zu aktivieren, müssen Sie ein $2-Konto erstellen.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "RPC-URL hinzufügen"}, "addSnapAccountToggle": {"message": "„Konto-Snap (Beta) hinzufügen“ aktivieren"}, "addSnapAccountsDescription": {"message": "<PERSON>n Si<PERSON> diese Funktion aktivieren, haben Sie die Möglichkeit, die neuen Beta-Konto-Snaps direkt aus Ihrer Kontoliste hinzuzufügen. Denken Sie bei der Installation eines Konto-Snaps bitte daran, dass es sich dabei um einen Dienst von Drittanbietern handelt."}, "addSuggestedNFTs": {"message": "Vorgeschlagene NFTs hinzufügen"}, "addSuggestedTokens": {"message": "Vorgeschlagene Tokens hinzufügen"}, "addToken": {"message": "Token hinzufügen"}, "addTokenByContractAddress": {"message": "Sie können kein Token finden? Sie können ein beliebiges Token manuell hinzufügen, indem Sie seine Adresse eingeben. Token-Contract-Adressen finden Sie auf $1.", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "URL hinzufügen"}, "addingAccount": {"message": "<PERSON><PERSON> hi<PERSON>uf<PERSON>"}, "addingCustomNetwork": {"message": "Netzwerk wird hinzugefügt"}, "additionalNetworks": {"message": "Zusätzliche Netzwerke"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "addressCopied": {"message": "<PERSON><PERSON>e wurde kopiert!"}, "addressMismatch": {"message": "Nichtübereinstimmung der Website-Adresse"}, "addressMismatchOriginal": {"message": "Aktuelle URL: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Punycode-Version: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "advancedBaseGasFeeToolTip": {"message": "Wenn Ihre Transaktion in den Block aufgenommen wird, wird die Differenz zwischen Ihrer maximalen Grundgebühr und der tatsächlichen Grundgebühr erstattet. Der Gesamtbetrag wird berechnet als maximale Grundgebühr (in GWEI) * Gas-Limit."}, "advancedDetailsDataDesc": {"message": "Daten"}, "advancedDetailsHexDesc": {"message": "Hexadezimal"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Dies ist die Transaktionsnummer eines Kontos. Die Nonce für die erste Transaktion ist 0 und sie erhöht sich in fortlaufender Reihenfolge."}, "advancedGasFeeDefaultOptIn": {"message": "Speichern Sie diese Werte als Standard für das $1-Netzwerk.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Erweiterte Gas-Gebühr"}, "advancedGasPriceTitle": {"message": "Gas-Preis"}, "advancedPriorityFeeToolTip": {"message": "Prioritätsgebühr (alias „<PERSON><PERSON> Tip“) geht direkt an Miner und veranlasst sie, Ihre Transaktion zu priorisieren."}, "airDropPatternDescription": {"message": "Der On-Chain-Verlauf des Tokens zeigt frü<PERSON> von verdächtigen Airdrop-Aktivitäten."}, "airDropPatternTitle": {"message": "Airdrop-Muster"}, "airgapVault": {"message": "AirGap-Tresor"}, "alert": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "alertAccountTypeUpgradeMessage": {"message": "Sie aktualisieren Ihr Konto auf ein Smart-Konto. Sie behalten die gleiche Kontoadresse und profitieren gleichzeitig von schnelleren Transaktionen und niedrigeren Netzwerkgebühren. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Kontotyp"}, "alertActionBuyWithNativeCurrency": {"message": "$1 kaufen"}, "alertActionUpdateGas": {"message": "Gas-Limit aktualisieren"}, "alertActionUpdateGasFee": {"message": "Gebühr aktualisieren"}, "alertActionUpdateGasFeeLevel": {"message": "Gas-Optionen aktualisieren"}, "alertDisableTooltip": {"message": "Dies kann in „Einstellungen > Benachrichtigungen“ geändert werden."}, "alertMessageAddressMismatchWarning": {"message": "Angreifer imitieren manchmal Websites, indem sie kleine Änderungen an der Adresse der Website vornehmen. Vergewissern Si<PERSON> sich, dass Sie mit der beabsichtigten Website interagieren, bevor <PERSON> fort<PERSON>hren."}, "alertMessageChangeInSimulationResults": {"message": "Die voraussichtlichen Änderungen für diese Transaktion wurden aktualisiert. Überprüfen Sie diese sorgfältig, bevor <PERSON> fort<PERSON>hren."}, "alertMessageFirstTimeInteraction": {"message": "Sie interagieren zum ersten Mal mit dieser Adresse. Vergewissern Si<PERSON> sich, dass sie korrekt ist, bevor <PERSON> fort<PERSON>hren."}, "alertMessageGasEstimateFailed": {"message": "Wir sind nicht in der Lage, eine genaue Gebühr anzugeben, und diese Schätzung könnte zu hoch sein. Wir schlagen vor, dass Sie ein individuelles Gas-Limit eingeben, aber es besteht das Risiko, dass die Transaktion trotzdem fehlschlägt."}, "alertMessageGasFeeLow": {"message": "<PERSON>n Si<PERSON> eine niedrige Gebühr wählen, müssen Si<PERSON> mit langsameren Transaktionen und längeren Wartezeiten rechnen. Für schnellere Transaktionen wählen Sie die Gebührenoptionen Markt oder Aggressiv."}, "alertMessageGasTooLow": {"message": "Um mit dieser Transaktion fortzufahren, müssen Sie das Gas-Limit auf 21.000 oder mehr erhöhen."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Sie haben nicht genug $1 auf Ihrem Konto, um die Netzwerkgebühren zu bezahlen."}, "alertMessageNetworkBusy": {"message": "Die Gas-Preise sind hoch und die Schätzungen sind weniger genau."}, "alertMessageNoGasPrice": {"message": "Wir können mit dieser Transaktion nicht fortfahren, bis Sie die Gebühr manuell aktualisieren."}, "alertMessageSignInDomainMismatch": {"message": "Die Website, die die Anfrage stellt, ist nicht die Website, bei der Si<PERSON> sich anmelden. Dies könnte ein Versuch sein, Ihre Anmeldedaten zu stehlen."}, "alertMessageSignInWrongAccount": {"message": "Diese Seite fordert Si<PERSON> auf, sich mit dem falschen Konto anzumelden."}, "alertModalAcknowledge": {"message": "Ich habe das Risiko erkannt und möchte trotzdem fortfahren"}, "alertModalDetails": {"message": "Details zum Warnhinweis"}, "alertModalReviewAllAlerts": {"message": "Alle Benachrichtigungen überprüfen"}, "alertReasonChangeInSimulationResults": {"message": "Ergebnisse haben sich geändert"}, "alertReasonFirstTimeInteraction": {"message": "1. Interaktion"}, "alertReasonGasEstimateFailed": {"message": "Ungenaue Gebühr"}, "alertReasonGasFeeLow": {"message": "Langsame Geschwindigkeit"}, "alertReasonGasTooLow": {"message": "Niedriges Gas-Limit"}, "alertReasonInsufficientBalance": {"message": "Unzureichende Gelder"}, "alertReasonNetworkBusy": {"message": "Netzwerk ist ausgelastet"}, "alertReasonNoGasPrice": {"message": "Gebührenschätzung nicht verfügbar"}, "alertReasonPendingTransactions": {"message": "Ausstehende Transaktion"}, "alertReasonSignIn": {"message": "Verdächtige Anmeldeanfrage"}, "alertReasonWrongAccount": {"message": "Falsches Konto"}, "alertSelectedAccountWarning": {"message": "Diese Anfrage bezieht sich auf ein anderes Konto als das ausgewählte in Ihrer Wallet. Um ein anderes Konto zu verwenden, verbinden Sie es mit der Website."}, "alerts": {"message": "Benachrichtigungen"}, "all": {"message": "Alle"}, "allNetworks": {"message": "Alle Netzwerke"}, "allPermissions": {"message": "Alle Genehmigungen"}, "allTimeHigh": {"message": "Allzeithoch"}, "allTimeLow": {"message": "Allzeittief"}, "allowNotifications": {"message": "Benachrichtigungen erlauben"}, "allowWithdrawAndSpend": {"message": "$1 erlauben, bis zu dem folgenden Betrag abzuheben und auszugeben:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Betrag"}, "amountReceived": {"message": "Empfangener Betrag"}, "amountSent": {"message": "Gesendeter Betrag"}, "andForListItems": {"message": "$1 und $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 und $2 ", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "Die weltweit vertrauenswürdigste Krypto-Wallet", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "<PERSON><PERSON><PERSON>"}, "approve": {"message": "Ausgabenlimit genehmigen"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "$1 Ausgabenobergrenze erhöhen", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "$1 Ausgabenobergrenze genehmigen", "description": "The token symbol that is being approved"}, "approved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approvedOn": {"message": "Genehmigt am $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Genehmigt am $1 für $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Sind Sie sicher?"}, "asset": {"message": "<PERSON><PERSON>"}, "assetChartNoHistoricalPrices": {"message": "Wir konnten keine historischen Daten abrufen"}, "assetMultipleNFTsBalance": {"message": "$1 NFTs"}, "assetOptions": {"message": "Asset-Optionen"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Assets"}, "assetsDescription": {"message": "Automatische Erkennung von <PERSON>s in Ihrer Wallet, Anzeige von NFTs und stapelweise Aktualisierung des Kontostands"}, "attemptToCancelSwapForFree": {"message": "<PERSON><PERSON><PERSON>, den Swap kostenlos zu stornieren"}, "attributes": {"message": "Attribute"}, "attributions": {"message": "Zuschreibungen"}, "auroraRpcDeprecationMessage": {"message": "Die Infura RPC URL unterstützt Aurora nicht mehr."}, "authorizedPermissions": {"message": "Sie haben die folgenden Genehmigungen autorisiert."}, "autoDetectTokens": {"message": "Tokens automatisch erkennen"}, "autoDetectTokensDescription": {"message": "Wir verwenden APIs von Drittanbietern, um neue Token zu erkennen und anzuzeigen, die in Ihrer Wallet gesendet werden. Deaktivieren Sie dies, wenn Sie keine Daten von diesen Diensten beziehen möchten. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Auto-Lock-Timer (Minuten)"}, "autoLockTimeLimitDescription": {"message": "Legen Sie die Leerlaufzeit in Minuten fest, nach der MetaMask gesperrt werden soll."}, "average": {"message": "Durchschnitt"}, "back": {"message": "Zurück"}, "backupAndSync": {"message": "Backup und Synchronisierung"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "Grundfunktionalität"}, "backupAndSyncEnable": {"message": "Backup und Synchronisierung einschalten"}, "backupAndSyncEnableConfirmation": {"message": "<PERSON>im Einschalten von <PERSON> und Synchronisierung schalten Sie auch $1 ein. Möchten Sie fortfahren?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "<PERSON><PERSON>up und Synchronisierung können wir verschlüsselte Daten für Ihre benutzerdefinierten Einstellungen und Funktionen speichern. Dad<PERSON><PERSON> bleibt Ihr MetaMask-Erlebnis auf allen Geräten gleich und die Einstellungen und Funktionen werden bei eventueller Neuinstallation von MetaMask wiederhergestellt. Ihre geheime Wiederherstellungsphrase wird dabei nicht gesichert. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Sie können Ihre Einstellungen jederzeit in $1 aktualisieren", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Einstellungen > Backup und Synchronisierung."}, "backupAndSyncFeatureAccounts": {"message": "Konten"}, "backupAndSyncManageWhatYouSync": {"message": "<PERSON><PERSON><PERSON><PERSON>, was <PERSON><PERSON> synchronisieren"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "<PERSON><PERSON><PERSON> e<PERSON>, was <PERSON><PERSON> zwischen Ihren Geräten synchronisieren."}, "backupAndSyncPrivacyLink": {"message": "<PERSON><PERSON><PERSON><PERSON>, wie wir Ihre Privatsphäre schützen"}, "backupAndSyncSlideDescription": {"message": "Sichern Sie Ihre Konten und Synchronisierungseinstellungen."}, "backupAndSyncSlideTitle": {"message": "Einführung in Backup und Synchronisierung"}, "backupApprovalInfo": {"message": "Dieser geheime Code ist zum Wiedererlangen Ihrer Wallet erforderlich, falls Sie Ihr Gerät verlieren, Ihr Passwort vergessen, MetaMask neu installieren müssen oder auf einem anderen Gerät auf Ihre Wallet zugreifen möchten."}, "backupApprovalNotice": {"message": "Sichern Sie Ihre geheime Wiederherstellungsphrase, um Ihre Wallet und Ihr Geld geschützt zu halten."}, "backupKeyringSnapReminder": {"message": "Vergewisser<PERSON>h, dass Sie eigenständig Zugang zu von diesem Snap erstellten Konten haben, bevor sie ihn entfernen"}, "backupNow": {"message": "Jetzt sichern"}, "balance": {"message": "Guthaben:"}, "balanceOutdated": {"message": "Kontostand könnte überholt sein"}, "baseFee": {"message": "Grundgebühr"}, "basic": {"message": "Grundlegend"}, "basicConfigurationBannerTitle": {"message": "Grundfunktionalität ist ausgeschaltet"}, "basicConfigurationDescription": {"message": "MetaMask bietet grundlegende Funktionen wie Token-Details und Gas-Einstellungen über Internetdienste. Wenn Sie Internetdienste nutzen, wird Ihre IP-Adresse weitergegeben, in diesem Fall an MetaMask. Das ist genau so, wie wenn Sie eine beliebige Website besuchen. MetaMask verwendet diese Daten vorübergehend und verkauft Ihre Daten niemals. Sie können ein VPN verwenden oder diese Dienste abschalten, aber das kann Ihr MetaMask-Erlebnis beeinträchtigen. Um mehr zu erfahren, lesen Sie unsere $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "Grundfunktionalität"}, "basicConfigurationModalCheckbox": {"message": "Ich verstehe und möchte fortfahren"}, "basicConfigurationModalDisclaimerOff": {"message": "Das bedeutet, dass Sie Ihre Zeit auf MetaMask nicht vollständig optimieren können. Grundlegende Funktionen (wie Token-Details, optimale Gas-Einstellungen und andere) stehen Ihnen nicht zur Verfügung."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Durch Ausschalten dieser Funktion werden auch alle Funktionen innerhalb von $1 und $2 deaktiviert.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "Sicherheit und Datenschutz, Backup und Synchronisierung"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "Benachrichtigungen"}, "basicConfigurationModalDisclaimerOn": {"message": "Um Ihre Zeit auf MetaMask zu optimieren, müssen Sie diese Funktion einschalten. Grundlegende Funktionen (wie Token-Details, optimale Gas-Einstellungen und andere) sind für das Web3-Erlebnis wichtig."}, "basicConfigurationModalHeadingOff": {"message": "Grundfunktionalität ausschalten"}, "basicConfigurationModalHeadingOn": {"message": "Grundfunktionalität einschalten"}, "bestPrice": {"message": "Best<PERSON> <PERSON>"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Dies ist eine BETA-Version. Bitte melden Sie Fehler $1."}, "betaMetamaskVersion": {"message": "MetaMask-Version"}, "betaTerms": {"message": "Beta-Nutzungsbedingungen"}, "billionAbbreviation": {"message": "B", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "Ko<PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "<PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "Block-Explorer"}, "blockExplorerUrlDefinition": {"message": "Die URL, die als Block-Explorer für dieses Netzwerk verwendet wird."}, "blockExplorerView": {"message": "Konto bei $1 anzeigen", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "<PERSON><PERSON> so weiter<PERSON>, könnten alle von <PERSON>hnen auf Blur aufgeführten Assets gefährdet sein."}, "blockaidAlertDescriptionMalicious": {"message": "Sie interagieren mit einer schädlichen Website. Wenn Si<PERSON> fortfahren, werden Sie Ihre Assets verlieren."}, "blockaidAlertDescriptionOpenSea": {"message": "<PERSON><PERSON>, kö<PERSON><PERSON> von Ihnen auf OpenSea aufgeführten Assets gefährdet sein."}, "blockaidAlertDescriptionOthers": {"message": "<PERSON>n Sie diese Anfrage bestätigen, könnten Sie Ihre Assets verlieren. Wir empfehlen Ihnen, diese Anfrage abzubrechen."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Sie senden Ihre Assets an einen Betrüger. Wenn <PERSON> fortfahren, werden Sie diese Assets verlieren."}, "blockaidAlertDescriptionWithdraw": {"message": "<PERSON>n Si<PERSON> diese Anfrage bestätigen, erlauben <PERSON> e<PERSON>m Betrüger, sich Ihre Assets abzahlen zu lassen und auszugeben. Sie erhalten diese nicht zurück."}, "blockaidDescriptionApproveFarming": {"message": "<PERSON>n <PERSON> diese Anfrage genehmigen, könnte eine dritte Partei, die für Betrügereien bekannt ist, Ihre gesamten Assets an sich reißen."}, "blockaidDescriptionBlurFarming": {"message": "<PERSON><PERSON> diese An<PERSON>ge genehmigen, kann jemand Ihre bei Blur aufgelisteten Assets stehlen."}, "blockaidDescriptionErrored": {"message": "Aufgrund eines Fehlers konnten wir nicht auf Sicherheitsalarme prüfen. Fahren Si<PERSON> nur fort, wenn Sie jeder involvierten Adresse vertrauen."}, "blockaidDescriptionMaliciousDomain": {"message": "Sie interagieren mit einer schädlichen Domain. Wenn Sie diese Anfrage bestätigen, verlieren Sie eventuell Ihre Assets."}, "blockaidDescriptionMightLoseAssets": {"message": "<PERSON><PERSON> diese Anfrage genehmigen, verlieren Sie eventuell Ihre <PERSON>."}, "blockaidDescriptionSeaportFarming": {"message": "<PERSON><PERSON> diese An<PERSON>ge genehmigen, kann jemand Ihre bei Blur aufgelisteten Assets stehlen."}, "blockaidDescriptionTransferFarming": {"message": "<PERSON>n <PERSON> diese Anfrage genehmigen, wird eine dritte Partei, die für Betrügereien bekannt ist, Ihre gesamten Assets an sich reißen."}, "blockaidMessage": {"message": "Wahrung der Privatsphäre – keine Daten werden an Dritte weitergegeben. Verfügbar auf Arbitrum, Avalanche, BNB chain, Ethereum Mainnet, Linea, Optimism, Polygon, Base und Sepolia."}, "blockaidTitleDeceptive": {"message": "Dies ist eine betrügerische Anfrage."}, "blockaidTitleMayNotBeSafe": {"message": "Seien Sie vorsichtig"}, "blockaidTitleSuspicious": {"message": "Dies ist eine verdächtige Anfrage."}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Ausgeliehen"}, "boughtFor": {"message": "Gekauft für"}, "bridge": {"message": "Bridge"}, "bridgeAllowSwappingOf": {"message": "Genehmigen Sie für das Bridging genauen Zugriff auf $1 $2 fûr $3", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "$1 für Bridge genehmigen", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Sie genehmigen den Zugriff auf den angegebenen Betrag, $1 $2. Der Contract gewährt keinen Zugriff auf weitere Gelder."}, "bridgeApprovalWarningForHardware": {"message": "Sie müssen für das Bridging den Zugriff auf $1 $2 gewähren und dann das Bridging auf $2 genehmigen. Dies bedarf zweier separater Bestätigungen."}, "bridgeBlockExplorerLinkCopied": {"message": "Block-Explorer-<PERSON> k<PERSON>!"}, "bridgeCalculatingAmount": {"message": "Be<PERSON><PERSON>nen ..."}, "bridgeConfirmTwoTransactions": {"message": "Sie werden 2 Transaktionen auf Ihrer Hardware-Wallet bestätigen müssen:"}, "bridgeCreateSolanaAccount": {"message": "Solana-<PERSON><PERSON>"}, "bridgeCreateSolanaAccountDescription": {"message": "Um zum Solana-Netzwerk zu swappen, benötigen Sie ein Konto und eine Empfangsadresse."}, "bridgeCreateSolanaAccountTitle": {"message": "Zunächst benötigen Sie ein Solana-Konto."}, "bridgeDetailsTitle": {"message": "Brigde-Details", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Betrag auswählen"}, "bridgeEnterAmountAndSelectAccount": {"message": "Betrag eingeben und Zielkonto auswählen"}, "bridgeExplorerLinkViewOn": {"message": "Auf $1 ansehen"}, "bridgeFetchNewQuotes": {"message": "Eine neue suchen?"}, "bridgeFrom": {"message": "<PERSON> von"}, "bridgeFromTo": {"message": "Bridge $1 $2 auf $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Die auf dem vorherigen Bildschirm angegebenen Netzwerkgebühren umfassen beide Transaktionen und werden aufgeteilt."}, "bridgeNetCost": {"message": "Nettokosten"}, "bridgeQuoteExpired": {"message": "Ihr Angebot ist abgelaufen."}, "bridgeSelectDestinationAccount": {"message": "Zielkonto auswählen"}, "bridgeSelectNetwork": {"message": "Netzwerk wählen"}, "bridgeSelectTokenAmountAndAccount": {"message": "Token, Betrag und Zielkonto auswählen"}, "bridgeSelectTokenAndAmount": {"message": "Token und Betrag auswählen"}, "bridgeSolanaAccountCreated": {"message": "Solana-<PERSON><PERSON> erste<PERSON>t"}, "bridgeStatusComplete": {"message": "Vollständig", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Fehlgeschlagen", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "In Bearbeitung", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 am $2 empfangen", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Empfang von $1 am $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "$1 für $2 geswappt", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Swapping von $1 für $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Bedingungen"}, "bridgeTimingMinutes": {"message": "$1 Min.", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Bridge nach"}, "bridgeToChain": {"message": "Bridge nach $1"}, "bridgeTokenCannotVerifyDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON> dieses Token manuell hinzugefügt haben, stel<PERSON>, dass Si<PERSON> sich der Risiken für Ihre Gelder bewusst sind, bevor <PERSON> bridgen."}, "bridgeTokenCannotVerifyTitle": {"message": "Wir können diesen Token nicht verifizieren."}, "bridgeTransactionProgress": {"message": "Transaktion $1 von 2"}, "bridgeTxDetailsBridging": {"message": "Bridging"}, "bridgeTxDetailsDelayedDescription": {"message": "Wenden Sie sich an"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "MetaMask-Support"}, "bridgeTxDetailsDelayedTitle": {"message": "Ist es länger als 3 Stunden her?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Status"}, "bridgeTxDetailsTimestamp": {"message": "Zeitstempel"}, "bridgeTxDetailsTimestampValue": {"message": "$1 zu $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 auf", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Gesamte Gasgebühr"}, "bridgeTxDetailsYouReceived": {"message": "Sie empfingen"}, "bridgeTxDetailsYouSent": {"message": "<PERSON><PERSON> sand<PERSON>"}, "bridgeValidationInsufficientGasMessage": {"message": "Sie haben nicht genug $1, um die Gasgebühr für diese Bridge zu entrichten. Geben Sie einen kleineren Betrag ein oder kaufen Sie weitere $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Mehr $1 für Gas benötigt"}, "bridging": {"message": "Bridging"}, "browserNotSupported": {"message": "<PERSON><PERSON> Browser wird nicht unterstützt …"}, "buildContactList": {"message": "Erstellen Sie Ihre Kontaktliste."}, "builtAroundTheWorld": {"message": "MetaMask ist weltweit konzipiert und aufgebaut."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Ausgelastet"}, "buyAndSell": {"message": "Kaufen/Verkaufen"}, "buyMoreAsset": {"message": "Mehr $1 kaufen", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Jetzt kaufen"}, "bytes": {"message": "Bytes"}, "canToggleInSettings": {"message": "Sie können diese Benachrichtigung unter Einstellungen -> Warnungen wieder aktivieren."}, "cancel": {"message": "Stornieren"}, "cancelPopoverTitle": {"message": "Transaktion stornieren"}, "cancelSpeedUpLabel": {"message": "Diese Gas-Gebühr kostet $1 das Original.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Für eine Transaktion im Wert von $1 muss die Gasgebühr um mindestens 10 % erhöht werden, damit sie vom Netz erkannt wird.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "chainId": {"message": "Chain-ID"}, "chainIdDefinition": {"message": "Die Chain-ID, die verwendet wird, um Transaktionen für dieses Netzwerk zu unterzeichnen."}, "chainIdExistsErrorMsg": {"message": "Diese Chain-ID wird derzeit vom $1-Netzwerk verwendet."}, "chainListReturnedDifferentTickerSymbol": {"message": "Dieses <PERSON>-Symbol stimmt nicht mit dem eingegebenen Netzwerknamen oder der Chain-ID überein. Viele beliebte Token verwenden ähnliche Symbole, die Betrüger nutzen können, um Sie dazu zu bringen, ihnen im Gegenzug einen wertvolleren Token zu senden. Überprüfen Sie alles, bevor <PERSON> fort<PERSON>hren."}, "chooseYourNetwork": {"message": "Wählen Sie Ihr Netzwerk"}, "chooseYourNetworkDescription": {"message": "Wenn Sie unsere Standardeinstellungen und -konfigurationen verwenden, nutzen wir Infura als Standard-RPC-Anbieter (Remote Procedure Call), um einen möglichst zuverlässigen und privaten Zugriff auf Ethereum-Daten zu gewährleisten. In Ausnahmefällen können wir auch andere RPC-Anbieter einsetzen, um unseren Nutzern das bestmögliche Erlebnis zu bieten. Sie können Ihren eigenen RPC wählen, doch bedenken Sie dabei, dass jeder RPC Ihre IP-Adresse und Ihr Ethereum-Wallet zur Durchführung von Transaktionen erhalten wird. Weitere Informationen darüber, wie Infura Daten für EVM-Konten verarbeitet, finden Sie in unserer $1 und für Solana-Konten in der $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "klicken <PERSON> hier"}, "chromeRequiredForHardwareWallets": {"message": "<PERSON>e müssen MetaMask unter Google Chrome nutzen, um sich mit Ihrer Hardware-Wallet zu verbinden."}, "circulatingSupply": {"message": "Zirkulierende Versorgung"}, "clear": {"message": "Löschen"}, "clearActivity": {"message": "Aktivitäten und Nonce-Daten löschen"}, "clearActivityButton": {"message": "Aktivitäten-Tab-Daten löschen"}, "clearActivityDescription": {"message": "Dies setzt die Nonce des Kontos zurück und löscht Daten aus dem Aktivitäten-Tab in Ihrer Wallet. Nur das aktuelle Konto und Netzwerk sind betroffen. Ihr Guthaben und eingehenden Transaktionen ändern sich nicht."}, "click": {"message": "<PERSON><PERSON><PERSON>"}, "clickToConnectLedgerViaWebHID": {"message": "<PERSON><PERSON><PERSON> hier, um Ihren Ledger über WebHID zu verbinden.", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "Schließen"}, "closeExtension": {"message": "Erweiterung schließen"}, "closeWindowAnytime": {"message": "<PERSON><PERSON> können dieses Fenster jederzeit schließen."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Name der Sammlung"}, "comboNoOptions": {"message": "Keine Option gefunden", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "Der Großteil des Token-Angebots wird von den wichtigsten Token-Inhabern gehalten, was das Risiko einer zentralisierten Preismanipulation birgt"}, "concentratedSupplyDistributionTitle": {"message": "Konzentrierte Angebotsverteilung"}, "configureSnapPopupDescription": {"message": "Sie verlassen jetzt MetaMask, um diesen Snap zu konfigurieren."}, "configureSnapPopupInstallDescription": {"message": "Sie verlassen jetzt MetaMask, um diesen Snap zu installieren."}, "configureSnapPopupInstallTitle": {"message": "Snap installieren"}, "configureSnapPopupLink": {"message": "Zum Fortfahren auf diesen Link klicken:"}, "configureSnapPopupTitle": {"message": "Snap konfigurieren"}, "confirm": {"message": "Bestätigen"}, "confirmAccountTypeSmartContract": {"message": "Smart-<PERSON><PERSON>"}, "confirmAccountTypeStandard": {"message": "Standardkonto"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Ich habe die Benachrichtigungen zur Kenntnis genommen und möchte trotzdem fortfahren"}, "confirmAlertModalAcknowledgeSingle": {"message": "Ich habe die Benachrichtigung zur Kenntnis genommen und möchte trotzdem fortfahren"}, "confirmFieldPaymaster": {"message": "<PERSON><PERSON><PERSON><PERSON> beza<PERSON> von"}, "confirmFieldTooltipPaymaster": {"message": "Die Gebühr für diese Transaktion wird durch den Paymaster Smart Contract bezahlt."}, "confirmGasFeeTokenBalance": {"message": "Guth:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Unzureichende Gelder"}, "confirmGasFeeTokenMetaMaskFee": {"message": "Einschließlich $1 Gebühr"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "MetaMask ergänzt den Saldo, um diese Transaktion abzuschließen."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Begleichen Sie die Netzwerkgebühr mit dem Guthaben in Ihrer Wallet."}, "confirmGasFeeTokenModalPayETH": {"message": "Mit ETH bezahlen"}, "confirmGasFeeTokenModalPayToken": {"message": "<PERSON>t anderen Token<PERSON> bezahlen"}, "confirmGasFeeTokenModalTitle": {"message": "Token auswählen"}, "confirmGasFeeTokenToast": {"message": "Sie bezahlen diese Netzwerkgebühr mit $1"}, "confirmGasFeeTokenTooltip": {"message": "Diese Gebühr wird für die Bearbeitung Ihrer Transaktion an das Netzwerk gezahlt. Sie umfasst eine MetaMask-Gebühr von $1 für Nicht-ETH-Tokens bzw. vorfinanzierte ETH."}, "confirmInfoAccountNow": {"message": "Jetzt"}, "confirmInfoSwitchingTo": {"message": "<PERSON><PERSON><PERSON><PERSON> zu"}, "confirmNestedTransactionTitle": {"message": "Transaktion $1"}, "confirmPassword": {"message": "Passwort bestätigen"}, "confirmRecoveryPhrase": {"message": "Geheime Wiederherstellungsphrase bestätigen"}, "confirmSimulationApprove": {"message": "<PERSON><PERSON> gene<PERSON>igen"}, "confirmTitleAccountTypeSwitch": {"message": "Konto-Update"}, "confirmTitleApproveTransactionNFT": {"message": "Auszahlungsanfrage"}, "confirmTitleDeployContract": {"message": "<PERSON><PERSON> Kontrakt nutzen"}, "confirmTitleDescApproveTransaction": {"message": "Diese Website möchte die Genehmigung, Ihre NFTs abzuheben."}, "confirmTitleDescDelegationRevoke": {"message": "Sie wechseln zurück zu einem Standardkonto (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "<PERSON><PERSON> wechseln zu einem Smart-Konto"}, "confirmTitleDescDeployContract": {"message": "Diese Website möchte, dass Sie einen Kontrakt nutzen."}, "confirmTitleDescERC20ApproveTransaction": {"message": "Diese Website möchte die Genehmigung, Ihre Tokens abzuheben."}, "confirmTitleDescPermitSignature": {"message": "Diese Website möchte die Genehmigung, Ihre Tokens auszugeben."}, "confirmTitleDescSIWESignature": {"message": "Eine Website möchte, dass Si<PERSON> sich an<PERSON>den, um zu beweisen, dass Sie dieses Konto besitzen."}, "confirmTitleDescSign": {"message": "Überprüfen Sie vor der Bestätigung die Details der Anfrage."}, "confirmTitlePermitTokens": {"message": "Antrag auf Ausgabenobergrenze"}, "confirmTitleRevokeApproveTransaction": {"message": "Genehmigung entfernen"}, "confirmTitleSIWESignature": {"message": "Anmeldeanfrage"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Genehmigung entfernen"}, "confirmTitleSignature": {"message": "Signaturanfrage"}, "confirmTitleTransaction": {"message": "Transaktionsanfrage"}, "confirmationAlertDetails": {"message": "Um Ihre Assets zu schützen, empfehlen wir die Ablehnung der Anfrage."}, "confirmationAlertModalTitleDescription": {"message": "<PERSON><PERSON>e As<PERSON> könnten gefährdet sein"}, "confirmed": {"message": "Bestätigt"}, "confusableUnicode": {"message": "'$1' ist ähnlich wie '$2'."}, "confusableZeroWidthUnicode": {"message": "Breitenloses Zeichen gefunden."}, "confusingEnsDomain": {"message": "Wir haben ein missverständliches Zeichen im ENS-Namen entdeckt. Prüfen Sie den ENS-Namen, um möglichen Betrug zu vermeiden."}, "connect": {"message": "Verbinden"}, "connectAccount": {"message": "Konto verbinden"}, "connectAccountOrCreate": {"message": "Konto verbinden oder neues erstellen"}, "connectAccounts": {"message": "Konten verbinden"}, "connectAnAccountHeader": {"message": "Konten verbinden"}, "connectManually": {"message": "Manuelle Verbindung zur aktuellen Seite"}, "connectMoreAccounts": {"message": "Weitere Konten verbinden"}, "connectSnap": {"message": "Mit $1 verbinden", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "Mit MetaMask verbinden"}, "connectedAccounts": {"message": "Verbundene Konten"}, "connectedAccountsDescriptionPlural": {"message": "Sie haben $1 Konten mit dieser Seite verbunden.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Sie haben $1 Konten mit dieser Seite verbunden."}, "connectedAccountsEmptyDescription": {"message": "MetaMask ist nicht mit dieser Seite verbunden. Um sich mit einer Web3-Seite zu verbinden, finden und klicken Sie auf die Schaltfläche „Verbinden“."}, "connectedAccountsListTooltip": {"message": "$1 kann den Kontostand, die Adresse und die Aktivitäten einsehen und Transaktionen vorschlagen, um für verbundene Konten zu genehmigen.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Verbundene Konten aktualisiert"}, "connectedSites": {"message": "Verbundene Seiten"}, "connectedSitesAndSnaps": {"message": "Verbundene Websites und Snaps"}, "connectedSitesDescription": {"message": "$1 ist mit diesen Seiten verbunden. Sie können Ihre Konto-Adresse einsehen.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 ist mit keiner Seite verbunden.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMask ist mit dieser Seite verbunden, aber es sind noch keine Konten verbunden"}, "connectedSnaps": {"message": "Verbundene Snaps"}, "connectedWithAccount": {"message": "$1 Konten verbunden", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Verbunden mit $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 Netzwerke verbunden", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Verbunden mit $1", "description": "$1 represents network name"}, "connecting": {"message": "Verbinden"}, "connectingTo": {"message": "Verbindung mit $1 wird hergestellt"}, "connectingToDeprecatedNetwork": {"message": "‚$1‘ wird eingestellt und funktioniert möglicherweise nicht mehr. Versuchen Sie ein anderes Netzwerk."}, "connectingToGoerli": {"message": "Verbindungsaufbau zum Goerli-Testnetzwerk"}, "connectingToLineaGoerli": {"message": "Verbindungsaufbau zum Linea-Testnetzwerk"}, "connectingToLineaMainnet": {"message": "Verbindung zum Linea Mainnet wird hergestellt"}, "connectingToLineaSepolia": {"message": "Herstellung der Verbindung zum Linea-Sepolia-Testnetzwerk"}, "connectingToMainnet": {"message": "Verbindung zum Ethereum Mainnet wird hergestellt"}, "connectingToSepolia": {"message": "Verbindung zum Sepolia-Testnetzwerk wird hergestellt"}, "connectionDescription": {"message": "Diese Website mit MetaMask verbinden"}, "connectionFailed": {"message": "Verbindung fehlgeschlagen"}, "connectionFailedDescription": {"message": "Abrufen von $1 fehlgeschlagen. Netzwerkverbindung überprüfen und erneut versuchen.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Um eine Verbindung zu einer Website herzustellen, klicken Sie auf die Schaltfläche „Verbinden“. MetaMask kann sich nur mit web3-Websites verbinden."}, "connectionRequest": {"message": "Verbindungsanfrage"}, "contactUs": {"message": "Kontaktaufnahme"}, "contacts": {"message": "Kontaktaufnahme"}, "contentFromSnap": {"message": "Inhalt von $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON>"}, "contract": {"message": "Contract"}, "contractAddress": {"message": "Contract-<PERSON><PERSON><PERSON>"}, "contractAddressError": {"message": "Sie senden Tokens an die Contract-Adresse des Tokens. Dies kann zum Verlust dieser Tokens führen."}, "contractDeployment": {"message": "Contract-Einsatz"}, "contractInteraction": {"message": "Contract-Interaktion"}, "convertTokenToNFTDescription": {"message": "Wir haben festgestellt, dass dieses Asset eine NFT ist. MetaMask hat jetzt volle native Unterstützung für NFTs. Möchten Sie es aus Ihrer Tokenliste entfernen und als NFT hinzufügen?"}, "convertTokenToNFTExistDescription": {"message": "Wir haben festgestellt, dass dieses Asset als NFT hinzugefügt wurde. Möchten Sie es aus Ihrer Token-Liste entfernen?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "<PERSON><PERSON><PERSON>."}, "copyAddress": {"message": "Adresse in die Zwischenablage kopieren"}, "copyAddressShort": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "copyPrivateKey": {"message": "Privaten Schlüssel kopieren"}, "copyToClipboard": {"message": "In die Zwischenablage kopieren"}, "copyTransactionId": {"message": "Transaktions-ID kopieren"}, "create": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "createNewAccountHeader": {"message": "Neues Konto erstellen"}, "createPassword": {"message": "Passwort erstellen"}, "createSnapAccountDescription": {"message": "$1 möchte ein neues Konto zu MetaMask hinzufügen."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON> er<PERSON>"}, "createSolanaAccount": {"message": "Solana-<PERSON><PERSON>"}, "creatorAddress": {"message": "Adresse des Erstellers"}, "crossChainSwapsLink": {"message": "Netzwerkübergreifender Austausch mit MetaMask Portfolio"}, "crossChainSwapsLinkNative": {"message": "Netzwerkübergreifender Austausch mit Bridge"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "Währung"}, "currencyRateCheckToggle": {"message": "Guthaben und Token-Preisprüfer anzeigen"}, "currencyRateCheckToggleDescription": {"message": "Wie verwenden $1- und $2-APIs, um Ihr Guthaben und den Tokenpreis anzuzeigen. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Währungssymbol"}, "currencySymbolDefinition": {"message": "Das Ticker-Symbol, das für die Währung dieses Netzwerks angezeigt wird."}, "currentAccountNotConnected": {"message": "Ihr aktuelles Konto ist nicht verbunden."}, "currentExtension": {"message": "Aktuelle Erweiterungsseite"}, "currentLanguage": {"message": "Aktuelle Sprache"}, "currentNetwork": {"message": "Aktuelles Netzwerk", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "Die aktuelle RPC-URL für dieses Netzwerk ist veraltet."}, "currentTitle": {"message": "Aktuell:"}, "currentlyUnavailable": {"message": "Nicht verfügbar in diesem Netzwerk"}, "curveHighGasEstimate": {"message": "Aggressives Gas-Schätzungsdiagramm"}, "curveLowGasEstimate": {"message": "Diagramm für eine niedrige Gasschätzung"}, "curveMediumGasEstimate": {"message": "Markt-Gas-Schätzungsdiagramm"}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customGasSettingToolTipMessage": {"message": "$1 verwenden, um den Gas-Preis anzupassen. Das kann verwirrend sein, wenn Sie damit nicht vertraut sind. Interaktion auf eigene Gefahr.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customSpendLimit": {"message": "Benutzerdefiniertes Ausgabenlimit"}, "customToken": {"message": "Benutzerdefiniertes Token"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "Die Token-Erkennung ist in diesem Netzwerk noch nicht verfügbar. Bitte importieren Sie das Token manuell und stellen Si<PERSON> sicher, dass Si<PERSON> ihm vertrauen. Erfahren Sie mehr über $1."}, "customTokenWarningInTokenDetectionNetwork": {"message": "<PERSON><PERSON> kann einen To<PERSON> erstellen, auch gefälschte Versionen von bestehenden Token. Erfahren Sie mehr über $1."}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "<PERSON><PERSON><PERSON>, dass Si<PERSON> einem Token vertrauen, bevor <PERSON> es importieren. Erfahren Sie, wie Sie 1$ vermeiden und die Token-Erkennung $2 aktivieren können."}, "customerSupport": {"message": "Kundensupport"}, "customizeYourNotifications": {"message": "Passen Sie Ihre Benachrichtigungen an"}, "customizeYourNotificationsText": {"message": "Schalten Sie die Arten von Benachrichtigungen ein, die Si<PERSON> empfangen möchten:"}, "dappSuggested": {"message": "Seite vorgeschlagen"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 hat diesen Preis vorgeschlagen.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Seite vorgeschlagen"}, "dappSuggestedHighShortLabel": {"message": "Seite (hoch)"}, "dappSuggestedShortLabel": {"message": "Seite"}, "dappSuggestedTooltip": {"message": "$1 hat diesen Preis vorgeschlagen.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "<PERSON><PERSON><PERSON>"}, "data": {"message": "Daten"}, "dataCollectionForMarketing": {"message": "Datenerhebung für das Marketing"}, "dataCollectionForMarketingDescription": {"message": "Wir verwenden MetaMetrics, um zu erfahren, wie Sie mit unserer Marketingkommunikation umgehen. Wir können relevante Neuigkeiten (wie Produktmerkmale und andere Materialien) teilen."}, "dataCollectionWarningPopoverButton": {"message": "Okay"}, "dataCollectionWarningPopoverDescription": {"message": "Sie haben die Datenerhebung für unsere Marketingzwecke deaktiviert. Dies gilt nur für dieses Gerät. Wenn Sie MetaMask auf anderen Geräten verwenden, stellen <PERSON>, dass Sie sich auch dort abmelden."}, "dataUnavailable": {"message": "Daten nicht verfügbar"}, "dateCreated": {"message": "<PERSON><PERSON> er<PERSON>t"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Kaufoptionen per Debit- oder Kreditkarte"}, "decimal": {"message": "Token-Dezimale"}, "decimalsMustZerotoTen": {"message": "Dezimalzahlen müssen mindestens 0 und dürfen höchstens 36 betragen."}, "decrypt": {"message": "Entschlüsseln"}, "decryptCopy": {"message": "Verschlüsselte Nachricht kopieren"}, "decryptInlineError": {"message": "Diese Nachricht kann aufgrund eines Fehlers nicht entschlüsselt werden: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 möchte diese Nachricht lesen, um Ihre Aktion abzuschließen.", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "Nachricht entschlüsseln"}, "decryptRequest": {"message": "Anfrage entschlüsseln"}, "defaultRpcUrl": {"message": "Standard-RPC-URL"}, "defaultSettingsSubTitle": {"message": "MetaMask verwendet Standardeinstellungen, um ein optimales Gleichgewicht zwischen Sicherheit und Benutzerfreundlichkeit herzustellen. Ändern Sie diese Einstellungen, um Ihre Privatsphäre weiter zu verbessern."}, "defaultSettingsTitle": {"message": "Standard-Datenschutzeinstellungen"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Schauen Sie später nochmal vorbei."}, "defiTabErrorTitle": {"message": "Diese Se<PERSON> konnte nicht geladen werden."}, "delete": {"message": "Löschen"}, "deleteContact": {"message": "Kontakt löschen"}, "deleteMetaMetricsData": {"message": "MetaMetrics-Daten löschen"}, "deleteMetaMetricsDataDescription": {"message": "Dadurch werden historische, mit Ihrer Nutzung auf diesem Gerät verbundene MetaMetrics-Daten gelöscht. Ihre Wallet und Ihre Konten bleiben nach dem Löschen dieser Daten unverändert. Dieser Vorgang kann bis zu 30 Tage dauern. Lesen Sie unsere $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Diese Anfrage kann aufgrund eines Serverproblems des Analysesystems derzeit nicht bearbeitet werden. Bitte versuchen Sie es später erneut."}, "deleteMetaMetricsDataErrorTitle": {"message": "Wir können diese Daten im Moment nicht löschen"}, "deleteMetaMetricsDataModalDesc": {"message": "Wir sind dabei, Ihre gesamten MetaMetrics-Daten zu löschen. Sind Sie sicher?"}, "deleteMetaMetricsDataModalTitle": {"message": "MetaMetrics-<PERSON>n l<PERSON>?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Sie haben diese Aktion am $1 initiiert. Dieser Vorgang kann bis zu 30 Tage dauern. $2 anzeigen", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "<PERSON>n Sie dieses Netzwerk löschen, müssen Sie es erneut hinzufügen, um Ihre Assets in diesem Netzwerk anzuzeigen."}, "deleteNetworkTitle": {"message": "$1-Netzwerk löschen?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Zahlen Sie Krypto von einem anderen Konto über eine Wallet-Adresse oder einen QR-Code ein."}, "deprecatedGoerliNtwrkMsg": {"message": "Aufg<PERSON><PERSON> von Aktualisierungen des Ethereum-Systems wird das Goerli-Testnetzwerk bald eingestellt."}, "deprecatedNetwork": {"message": "Dieses Netzwerk ist veraltet"}, "deprecatedNetworkButtonMsg": {"message": "Verstanden"}, "deprecatedNetworkDescription": {"message": "Das Netzwerk, zu dem Si<PERSON> eine Verbindung herstellen möchten, wird von <PERSON> nicht mehr unterstützt. $1"}, "description": {"message": "Beschreibung"}, "descriptionFromSnap": {"message": "Beschreibung von $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "<PERSON><PERSON> berecht<PERSON>ten Konten gefunden"}, "destinationAccountPickerNoMatching": {"message": "<PERSON>ine übereinstimmenden Konten gefunden"}, "destinationAccountPickerReceiveAt": {"message": "Empfangen am"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Empfangsadresse oder ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Empfangsadresse"}, "destinationTransactionIdLabel": {"message": "Ziel-Tx-ID", "description": "Label for the destination transaction ID field."}, "details": {"message": "Details"}, "developerOptions": {"message": "Entwickler-Optionen"}, "disabledGasOptionToolTipMessage": {"message": "“$1” ist deaktiviert, weil es nicht das Minimum einer zehnprozentigen Erhöhung gegenüber der ursprünglichen Gasgebühr erfüllt.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Verbindung trennen"}, "disconnectAllAccounts": {"message": "Alle Konten trennen"}, "disconnectAllAccountsConfirmationDescription": {"message": "<PERSON>d <PERSON> sicher, dass Sie die Verbindung trennen möchten? Die Seitenfunktionalität könnte verloren gehen."}, "disconnectAllAccountsText": {"message": "Konten"}, "disconnectAllDescriptionText": {"message": "Falls Sie die Verbindung zu dieser Seite trennen, müssen Sie Ihre Konten und Netzwerke bei einer weiteren Nutzung dieser Website erneut verbinden."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "<PERSON><PERSON><PERSON> wird die Verbindung zu dieser Seite getrennt"}, "disconnectPrompt": {"message": "$1 trennen"}, "disconnectThisAccount": {"message": "<PERSON><PERSON> trennen"}, "disconnectedAllAccountsToast": {"message": "Alle Konten wurden von $1 getrennt", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 wurde von $2 getrennt", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Entdecken"}, "discoverSnaps": {"message": "Snaps entdecken", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Verwerfen"}, "dismissReminderDescriptionField": {"message": "Aktivieren Sie diese Option, um die Erinnerungsmeldung zur Sicherung der geheimen Wiederherstellungsphrase zu deaktivieren. Wir empfehlen Ihnen dringend, ein Backup Ihrer geheimen Wiederherstellungsphrase zu erstellen, um den Verlust von Geldern zu vermeiden."}, "dismissReminderField": {"message": "Backup-Erinnerung zur geheimen Wiederherstellungsphrase verwerfen"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Schalten Sie dies ein, um den Vorschlag „Zum Smart-Konto wechseln“ für kein Konto mehr zu sehen. Smart-Konten ermöglichen schnellere Transaktionen, niedrigere Netzwerkgebühren und mehr Flexibilität bei der Zahlung."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "Vorschlag „Zum Smart-Konto wechseln“ ausblenden"}, "displayNftMedia": {"message": "NFT-Medien anzeigen"}, "displayNftMediaDescription": {"message": "Durch die Anzeige von NFT-Medien und -Daten wird Ihre IP-Adresse an OpenSea oder andere Dritte weitergegeben. Dies kann es Angreifern ermöglichen, Ihre IP-Adresse mit Ihrer Ethereum-Adresse in Verbindung zu bringen. Die automatische NFT-Erkennung basiert auf dieser Einstellung und ist nicht verfügbar, wenn diese ausgeschaltet ist."}, "doNotShare": {"message": "<PERSON><PERSON><PERSON> dies mit niemanden"}, "domain": {"message": "Domain"}, "done": {"message": "<PERSON><PERSON><PERSON>"}, "dontShowThisAgain": {"message": "Nicht mehr anzeigen"}, "downArrow": {"message": "Abwärtspfeil"}, "downloadGoogleChrome": {"message": "Google Chrome herunterladen"}, "downloadNow": {"message": "<PERSON><PERSON><PERSON> herunterladen"}, "downloadStateLogs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dragAndDropBanner": {"message": "Sie können Netzwerke ziehen, um sie neu anzuordnen."}, "dropped": {"message": "Abgebrochen"}, "duplicateContactTooltip": {"message": "Dieser Kontaktname kollidiert mit einem bestehenden Konto bzw. Kontakt"}, "duplicateContactWarning": {"message": "<PERSON>e haben doppelte Kontakte"}, "durationSuffixDay": {"message": "T", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "STD", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "M", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "S", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "W", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "J", "description": "Shortened form of 'year'"}, "earn": {"message": "<PERSON><PERSON><PERSON>"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "editANickname": {"message": "Spitznamen bearbeiten"}, "editAccounts": {"message": "Konten bearbeiten"}, "editAddressNickname": {"message": "Adressenspitznamen bearbeiten"}, "editCancellationGasFeeModalTitle": {"message": "Stornogasgebühr bearbeiten"}, "editContact": {"message": "Kontakt bearbeiten"}, "editGasFeeModalTitle": {"message": "Gas-Gebühr bearbeiten"}, "editGasLimitOutOfBounds": {"message": "Gas-Limit muss mindestens $1 betragen"}, "editGasLimitOutOfBoundsV2": {"message": "Gas-Limit muss größer als $1 und kleiner als $2 sein", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "Gas-Limit ist die maximale Gas-Einheit, die Sie zu verwenden bereit sind. Gas-Einheiten sind ein Multiplikator der „Max. Prioritätsgebühr“ und der „Max. Gebühr“."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Die maximale Grundgebühr darf nicht niedriger sein als die Prioritätsgebühr."}, "editGasMaxBaseFeeHigh": {"message": "Die maximale Grundgebühr ist höher als erforderlich."}, "editGasMaxBaseFeeLow": {"message": "Die maximale Grundgebühr ist für aktuelle Netzwerkkonditionen niedrig."}, "editGasMaxFeeHigh": {"message": "Die maximale Grundgebühr ist höher als erforderlich."}, "editGasMaxFeeLow": {"message": "Die maximale Gebühr ist zu niedrig für Netzwerkkonditionen."}, "editGasMaxFeePriorityImbalance": {"message": "Die maximale Gebühr darf nicht niedriger sein als die maximale Prioritätsgebühr."}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Die maximale Prioritätsgebühr muss größer als 0 GWEI sein."}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Die Prioritätsgebühr muss größer als 0 sein."}, "editGasMaxPriorityFeeHigh": {"message": "Die maximale Prioritätsgebühr ist höher als nötig. Sie können mehr zahlen als nötig."}, "editGasMaxPriorityFeeHighV2": {"message": "Die maximale Prioritätsgebühr ist höher als nötig. Sie können mehr zahlen als nötig."}, "editGasMaxPriorityFeeLow": {"message": "Die maximale Prioritätsgebühr ist niedrig für aktuelle Netzwerkkonditionen."}, "editGasMaxPriorityFeeLowV2": {"message": "Die Prioritätsgebühr ist niedrig für aktuelle Netzwerkkonditionen."}, "editGasPriceTooLow": {"message": "Der Gas-Preis muss größer als 0 sein."}, "editGasPriceTooltip": {"message": "Dieses Netzwerk erfordert ein „Gas-Preis“-Feld beim Absenden einer Transaktion. Der Gas-Preis ist der Betrag, den Sie pro Gas-Einheit bezahlen."}, "editGasSubTextFeeLabel": {"message": "Maximale Gebühr:"}, "editGasTitle": {"message": "Priorität bearbeiten"}, "editGasTooLow": {"message": "Unbekannte Bearbeitungszeit"}, "editInPortfolio": {"message": "<PERSON><PERSON>"}, "editNetworkLink": {"message": "Das ursprüngliche Netzwerk bearbeiten"}, "editNetworksTitle": {"message": "Netzwerke bearbeiten"}, "editNonceField": {"message": "<PERSON><PERSON> bear<PERSON>ten"}, "editNonceMessage": {"message": "Dies ist eine erweiterte Funktion, verwenden Sie diese vorsichtig."}, "editPermission": {"message": "Genehmigung bearbeiten"}, "editPermissions": {"message": "Genehmigungen bearbeiten"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Beschleunigung der Gasgebühr bearbeiten"}, "editSpendingCap": {"message": "Ausgabenobergrenze bearbeiten"}, "editSpendingCapAccountBalance": {"message": "Kontostand: $1 $2"}, "editSpendingCapDesc": {"message": "<PERSON><PERSON><PERSON> Si<PERSON> den von Ihnen gewünschten Betrag ein, der in Ihrem Namen ausgegeben werden soll."}, "editSpendingCapError": {"message": "Die Ausgabenobergrenze darf nicht mehr als $1 Dezimalstellen überschreiten. Entfernen Sie die Dezimalstellen, um fortzufahren."}, "editSpendingCapSpecialCharError": {"message": "<PERSON><PERSON> Z<PERSON>"}, "enableAutoDetect": {"message": " Automatische Erkennung aktivieren"}, "enableFromSettings": {"message": " In den Einstellungen aktivieren."}, "enableSnap": {"message": "Aktivieren"}, "enableToken": {"message": "$1 aktivieren", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Aktiviert"}, "enabledNetworks": {"message": "Aktivierte Netzwerke"}, "encryptionPublicKeyNotice": {"message": "$1 wünscht Ihren öffentlichen Verschlüsselungsschlüssel. Durch Ihre Zustimmung kann diese Seite verschlüsselte Nachrichten an Si<PERSON> verfassen.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Öffentlichen Verschlüsselungsschlüssel anfordern"}, "endpointReturnedDifferentChainId": {"message": "Die von Ihnen eingegebene RPC-URL hat eine andere Chain-ID ($1) zurückgegeben.", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Erweiterte Token-Erkennung ist derzeit über $1 verfügbar. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "MetaMask zeigt Ihnen ENS-Domains direkt in der Adressleiste Ihres Browsers an. So funktioniert es:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "<PERSON><PERSON> Si<PERSON>, dass durch die Nutzung dieser Funktion Ihre IP-Adresse für IPFS-Dienste anbietende Dritte sichtbar wird."}, "ensDomainsSettingDescriptionPart1": {"message": "MetaMask findet mit<PERSON><PERSON> von Ethereums ENS-Contract den mit dem ENS-Namen verbundenen Code."}, "ensDomainsSettingDescriptionPart2": {"message": "Wenn der Code mit IPFS verknüpft ist, wird der dazugehörige Content (in der Regel eine Website) wiedergegeben."}, "ensDomainsSettingTitle": {"message": "ENS-Domains in der Adresszeile anzeigen"}, "ensUnknownError": {"message": "ENS-Lookup fehlgeschlagen."}, "enterANameToIdentifyTheUrl": {"message": "Geben Sie zur Identifizierung der URL einen Namen ein"}, "enterChainId": {"message": "Chain-ID eingeben"}, "enterMaxSpendLimit": {"message": "Max. Ausgabenlimit eingeben"}, "enterNetworkName": {"message": "Netzwerkname eingeben"}, "enterOptionalPassword": {"message": "Optionales Passwort eingeben"}, "enterPasswordContinue": {"message": "Zum Fortfahren Passwort eingeben"}, "enterRpcUrl": {"message": "RPC-URL eingeben"}, "enterSymbol": {"message": "Symbol eingeben"}, "enterTokenNameOrAddress": {"message": "Tokennamen eingeben oder Adresse einfügen"}, "enterYourPassword": {"message": "Passwort eingeben"}, "errorCode": {"message": "Code: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Fehler beim Abrufen der Liste sicherer Ketten, bitte mit Vorsicht fortfahren."}, "errorMessage": {"message": "Nachricht: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Code: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Support kontaktieren", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON>, was vorgefallen ist", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Ihre Daten können nicht angezeigt werden. <PERSON><PERSON>, <PERSON>hr<PERSON> Wall<PERSON> und Ihr Geld sind sicher.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "Fehlermeldung", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON>, was vorgefallen ist", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Die Übermittlung von Details, z. B. wie wir den Fehler reproduzieren können, wird uns bei der Behebung des Problems helfen.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Vielen Dank! Wir werden uns bald damit befassen.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMask hat einen Fehler festgestellt.", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "<PERSON><PERSON><PERSON> versuchen", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Stapel:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Fehler bei der Verbindung zum benutzerdefinierten Netzwerk."}, "errorWithSnap": {"message": "Fehler bei $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Geschätzte Gebühr"}, "estimatedFeeTooltip": {"message": "Bet<PERSON>, der für die Bearbeitung der Transaktion im Netzwerk gezahlt wurde."}, "ethGasPriceFetchWarning": {"message": "Der Gas-Preis, der sich aus der Gas-Hauptschätzungsdienst ergibt, ist derzeit nicht verfügbar."}, "ethereumProviderAccess": {"message": "Ethereum-Anbieter Zugriff auf $1 gewähren", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Öffentliche Ethereum-Adresse"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Account auf Etherscan anschauen"}, "etherscanViewOn": {"message": "<PERSON><PERSON> anzeigen"}, "existingChainId": {"message": "Die von Ihnen eingegebenen Informationen sind mit einer bestehenden Chain-ID verknüpft."}, "expandView": {"message": "Ansicht erweitern"}, "experimental": {"message": "Experimentell"}, "exploreweb3": {"message": "Web3 erkunden"}, "exportYourData": {"message": "Ihre Daten exportieren"}, "exportYourDataButton": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "exportYourDataDescription": {"message": "Sie können Daten wie Ihre Kontakte und Einstellungen exportieren."}, "extendWalletWithSnaps": {"message": "Erkunden Sie die von der Community erstellten Snaps, um Ihr web3-Erlebnis individuell zu gestalten.", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "<PERSON><PERSON><PERSON>"}, "externalExtension": {"message": "Externe Erweiterung"}, "externalNameSourcesSetting": {"message": "Vorgeschlagene Spitznamen"}, "externalNameSourcesSettingDescription": {"message": "Wir rufen die vorgeschlagenen Spitznamen für Adressen, mit denen Sie interagieren, aus Drittanbieterquellen wie Etherscan, Infura und Lens Protocol ab. Diese Anbieter sind in der Lage, die betreffenden Adressen sowie Ihre IP-Adresse einzu<PERSON>hen. Ihre Kontoadresse wird jedoch nicht an Drittparteien weitergegeben."}, "failed": {"message": "Fehlgeschlagen"}, "failedToFetchChainId": {"message": "Chain-ID konnte nicht abgerufen werden. Ist Ihre RPC-URL korrekt?"}, "failover": {"message": "Failover"}, "failoverRpcUrl": {"message": "Failover-RPC-URL"}, "failureMessage": {"message": "Etwas ist schiefgelaufen und wir konnten die Aktion nicht abschließen."}, "fast": {"message": "<PERSON><PERSON><PERSON>"}, "feeDetails": {"message": "Details zur Gebühr"}, "fileImportFail": {"message": "Dateiimport fehlgeschlagen? Bitte hier klicken!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "Sie sollten diese Erweiterung deinstallieren.", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask ist für Entwickler gedacht, um mit neuen instabilen APIs zu experimentieren. Wenn Sie kein Entwickler oder Beta-Tester sind, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "Wir übernehmen keine Garantie für die Sicherheit oder Stabilität dieser Erweiterung. Die neuen von Flask angebotenen APIs sind nicht gegen Phishing-Angriffe gehärtet. <PERSON> bedeutet, dass jede Webseite oder jede<PERSON>, der Flask benö<PERSON>gt, ein böswilliger Versuch sein könnte, <PERSON><PERSON><PERSON> Assets zu stehlen.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Alle Flask-APIs sind experimentell. Sie können ohne Vorankündigung geändert oder entfernt werden oder sie können auf unbestimmte Zeit in Flask bleiben, ohne jemals in die stabile MetaMask migriert zu werden. Die Verwendung erfolgt auf eigene Gefahr.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "<PERSON><PERSON><PERSON>, dass Sie Ihre normale MetaMask-Erweiterung deaktivieren, wenn Sie Flask verwenden.", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "Ich akzeptiere die Risiken", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Token-<PERSON><PERSON> muss eine ganze <PERSON> sein"}, "followUsOnTwitter": {"message": "Folgen Sie uns auf Twitter."}, "forbiddenIpfsGateway": {"message": "Verbotene IPFS-Gateway: Bitte geben Sie ein CID-Gateway an."}, "forgetDevice": {"message": "Dieses Gerät entfernen"}, "forgotPassword": {"message": "Passwort vergessen?"}, "form": {"message": "Formular"}, "from": {"message": "<PERSON>"}, "fromAddress": {"message": "Von: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "<PERSON>-Liste: $1"}, "function": {"message": "Funktion: $1"}, "fundingMethod": {"message": "Methode der Mittelbereitstellung"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Vorgeschlagene Gas-Gebühr bearbeiten"}, "gasDisplayDappWarning": {"message": "Diese Gas-Gebühr wurde von $1 vorgeschlagen. Dies kann ein Problem mit Ihrer Transaktion verursachen. Bei Fragen wenden Sie sich bitte an $1.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Gas-Gebühr"}, "gasLimit": {"message": "Gas-Limit"}, "gasLimitRecommended": {"message": "Empfohlenes Gas-Limit ist $1. Wenn das Gas-Limit weniger beträgt, kann es fehlschlagen."}, "gasLimitTooLow": {"message": "Gas-Limit muss mindestens 21000 betragen."}, "gasLimitV2": {"message": "Gas-Limit"}, "gasOption": {"message": "Gas-Option"}, "gasPriceExcessive": {"message": "Ihre Gas-Gebühr ist unnötig hoch. Denken Sie darüber nach, den Betrag zu senken."}, "gasPriceFetchFailed": {"message": "Die Gas-Preis-Schätzung ist aufgrund eines Netzwerkfehlers fehlgeschlagen."}, "gasTimingHoursShort": {"message": "$1 Stunde", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "Langsam"}, "gasTimingMinutesShort": {"message": "Min. $1", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 Sek.", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Verwendetes Gas"}, "general": {"message": "Allgemein"}, "generalCameraError": {"message": "Wir konnten nicht auf Ihre Kamera zugreifen. Bitte versuchen Sie es erneut."}, "generalCameraErrorTitle": {"message": "Etwas ist schiefgelaufen ...."}, "generalDescription": {"message": "Synchronisieren Sie Einstellungen geräteübergreifend, wählen Sie Netzwerkeinstellungen aus und verfolgen Sie Token-Daten"}, "genericExplorerView": {"message": "Konto auf $1 ansehen"}, "goToSite": {"message": "Zur Seite gehen"}, "goerli": {"message": "Goerli-Testnetzwerk"}, "gotIt": {"message": "Verstanden!"}, "grantExactAccess": {"message": "Exakten Zugriff genehmigen"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Hardware"}, "hardwareWalletConnected": {"message": "Hardware-<PERSON>et verknüpft"}, "hardwareWalletLegacyDescription": {"message": "(veraltet)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "<PERSON><PERSON><PERSON>, dass Ihr $1 angeschlossen ist und die Ethereum-App ausgewählt ist."}, "hardwareWalletSubmissionWarningStep2": {"message": "Aktivieren Sie „Smart-Contract-Daten“ oder „Blindes Signieren“ auf Ihrem $1-Gerät."}, "hardwareWalletSubmissionWarningTitle": {"message": "Vor dem Klicken auf Absenden:"}, "hardwareWalletSupportLinkConversion": {"message": "hier klicken"}, "hardwareWallets": {"message": "Eine Hardware-Wallet verknüpfen"}, "hardwareWalletsInfo": {"message": "Hardware-Wallet-Integrationen nutzen API-Aufrufe zur Kommunikation mit externen Servern, die Ihre IP-Adresse und die Adressen der Smart Contracts, mit denen Sie interagieren, se<PERSON> k<PERSON>."}, "hardwareWalletsMsg": {"message": "<PERSON><PERSON>hlen Sie eine Hardware-Wallet aus, die Si<PERSON> mit MetaMask verwenden möchten."}, "here": {"message": "hier", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "<PERSON>x<PERSON><PERSON>"}, "hiddenAccounts": {"message": "Versteckte Konten"}, "hide": {"message": "Verbergen"}, "hideAccount": {"message": "Konto verbergen"}, "hideAdvancedDetails": {"message": "Erweiterte Details verbergen"}, "hideSentitiveInfo": {"message": "Sensible Informationen verbergen"}, "hideTokenPrompt": {"message": "Token verbergen?"}, "hideTokenSymbol": {"message": "$1 verbergen", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Token ohne Guthaben verbergen"}, "high": {"message": "Aggressiv"}, "highGasSettingToolTipMessage": {"message": "Hohe Wahrscheinlichkeit, auch in volatilen Märkten. Verwenden Sie $1, um Schwankungen im Netzwerk-Traffic, die z. B. durch den Ausfall beliebter NFTs entstehen, abzudecken.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "hoch"}, "highestCurrentBid": {"message": "Höchstes aktuelles Gebot"}, "highestFloorPrice": {"message": "Höchster Mindestpreis"}, "history": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "holdToRevealContent1": {"message": "Ihre geheime Wiederherstellungsphrase bietet $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "vollen Zugriff auf Ihre Wallet und Ihr Guthaben.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "<PERSON><PERSON><PERSON> dies mit niemandem. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "Der MetaMask-Support wird Sie nicht danach fragen,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "Betrüger aber schon.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Ihr privater Schl<PERSON><PERSON> bietet $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "vollständigen Zugriff auf Ihre Wallet und Ihr Guthaben.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "Halten zum Offenlegen des gesperrten Kreises"}, "holdToRevealPrivateKey": {"message": "<PERSON><PERSON>, um privaten Schlüssel offenzulegen."}, "holdToRevealPrivateKeyTitle": {"message": "Bewaren Sie Ihren privaten Schlüssel sicher auf."}, "holdToRevealSRP": {"message": "<PERSON><PERSON>, um GWP anzuzeigen."}, "holdToRevealSRPTitle": {"message": "Bewahren Sie Ihre GWP sicher auf."}, "holdToRevealUnlockedLabel": {"message": "zur-Anzeige-Halten-Kreis entsperrt"}, "honeypotDescription": {"message": "Dieser To<PERSON> könnte ein Honeypot-<PERSON><PERSON><PERSON> bergen. <PERSON><PERSON> empfie<PERSON>t sich, vor der Interaktion eine Sorgfaltsprüfung vorzunehmen, um einen möglichen finanziellen Verlust zu vermeiden."}, "honeypotTitle": {"message": "Honeypot"}, "howNetworkFeesWorkExplanation": {"message": "Geschätzte Gebühr für die Abwicklung der Transaktion. Die maximale Gebühr ist $1."}, "howQuotesWork": {"message": "Funktionsweise von Angeboten"}, "howQuotesWorkExplanation": {"message": "Dieses <PERSON> hat die höchste Rendite von den von uns gesuchten Angeboten. Dieser Wert basiert auf dem Swap-Satz, der Bridging-Gebühren und eine MetaMask-Gebühr von $1 % einschließt, abzüglich der Gasgebühren. Die Gasgebühren hängen von der Auslastung des Netzwerks sowie der Komplexität der Transaktion ab."}, "id": {"message": "ID"}, "ignoreAll": {"message": "Alle ignorieren"}, "ignoreTokenWarning": {"message": "<PERSON>n Sie Tokens verbergen, werden Si<PERSON> nicht in Ihrer Wallet angezeigt. Sie können sie jedoch weiterhin hinzufügen, indem Sie nach ihnen suchen."}, "imToken": {"message": "imToken"}, "import": {"message": "Importieren", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Fehler beim Importieren des Kontos."}, "importAccountErrorIsSRP": {"message": "Sie haben eine geheime Wiederherstellungsphrase (oder mnemonische Phrase) eingegeben. Um hier ein Konto zu importieren, müssen Sie einen privaten Schlüssel eingeben, wobei es sich um einen hexadezimalen String mit 64 Zeichen handelt."}, "importAccountErrorNotAValidPrivateKey": {"message": "Dies ist ein ungültiger privater Schlüssel. Sie haben eine hexadezimale Zeichenfolge eingegeben, sie muss aber 64 Zeichen lang sein."}, "importAccountErrorNotHexadecimal": {"message": "Dies ist ein ungültiger privater Schlüsseln. Si<PERSON> müssen eine hexadezimale Zeichenfolge mit 64 Zeichen eingeben."}, "importAccountJsonLoading1": {"message": "<PERSON><PERSON><PERSON> damit, dass dieser JSON-Import ein paar Minuten dauert und MetaMask nicht reagiert."}, "importAccountJsonLoading2": {"message": "Wir entschuldigen uns hierfür und werden dies in Zukunft beschleunigen."}, "importAccountMsg": {"message": "Importierte Konten werden nicht mit der geheimen Wiederherstellungsphrase von MetaMask verknüpft. Erfahren Sie mehr über importierte Konten."}, "importNFT": {"message": "NFT importieren"}, "importNFTAddressToolTip": {"message": "Auf OpenSea gibt es zum Beispiel auf der NFT-Seite unter Details einen blauen Hyperlink mit der Bezeichnung „Contract-Adresse“. Wenn <PERSON><PERSON> darauf klicken, werden Sie zur Adresse des Contracts auf Etherscan weitergeleitet. Oben links auf der Seite sollte ein Symbol mit der Aufschrift „Contract“ zu sehen sein und rechts daneben eine lange Reihe von Buchstaben und Zahlen. Dies ist die Adresse des Contracts, mit dem Ihr NFT erstellt wurde. Klicken Sie auf das „Kopieren“-Symbol rechts neben der Adresse und Sie haben sie in Ihrer Zwischenablage."}, "importNFTPage": {"message": "NFT-Seite importieren"}, "importNFTTokenIdToolTip": {"message": "Die ID eines NFTs ist eine eindeutige Kennung, da keine zwei NFTs gleich sind. Auch diese Nummer finden Sie in OpenSea unter „Details“. Notieren Sie diese oder kopieren Sie sie in Ihre Zwischenablage."}, "importNWordSRP": {"message": "Ich habe eine $1-Wort-Wiederherstellungsphrase", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Private<PERSON><PERSON><PERSON>"}, "importSRPDescription": {"message": "Importieren Sie eine bereits vorhandene Wallet mit Ihrer geheimen 12- oder 24-Wort-<PERSON>iederherstellungsphrase."}, "importSRPNumberOfWordsError": {"message": "Geheime Wiederherstellungsphrasen bestehen aus 12 oder 24 Wörtern"}, "importSRPWordError": {"message": "Das Wort $1 ist falsch oder falsch geschrieben.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Wort $1 und $2 sind falsch oder falsch geschrieben.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Geheime Wiederherstellungsphrase importieren"}, "importSecretRecoveryPhraseUnknownError": {"message": "Ein unbekannter Fehler ist aufgetreten."}, "importSelectedTokens": {"message": "Ausgewählte Tokens importieren?"}, "importSelectedTokensDescription": {"message": "Es werden nur die von Ihnen ausgewählten Tokens in Ihrer Wallet angezeigt. Sie können verborgene Tokens später jederzeit importieren, indem Sie nach ihnen suchen."}, "importTokenQuestion": {"message": "Token importieren?"}, "importTokenWarning": {"message": "<PERSON><PERSON> kann ein Token mit beliebigem Namen erstellen, einsch<PERSON>ß<PERSON> gefälschter Versionen bestehender Tokens. Hinzufügen und Handeln auf eigene Gefahr!"}, "importTokensCamelCase": {"message": "Tokens importieren"}, "importTokensError": {"message": "Wir konnten die Tokens nicht importieren. Bitte versuchen Sie es später erneut."}, "importWallet": {"message": "Wallet importieren"}, "importWalletOrAccountHeader": {"message": "Wallet oder Konto importieren"}, "importWalletSuccess": {"message": "Geheime Wiederherstellungsphrase $1 importiert", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "$1 importieren", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "in Ihren Einstellungen"}, "included": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "includesXTransactions": {"message": "Beinhaltet $1 Transaktionen"}, "infuraBlockedNotification": {"message": "MetaMask kann sich nicht mit dem Blockchain Host verbinden. Überprüfen Sie mögliche Gründe $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Ihre erste Transaktion wurde vom Netzwerk bestätigt. Klicken Sie auf „OK“, um zurückzukehren."}, "insightsFromSnap": {"message": "Einblicke von $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Installieren"}, "installOrigin": {"message": "Origin installieren"}, "installRequest": {"message": "Zu MetaMask hinzufügen"}, "installedOn": {"message": "Installiert auf $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus."}, "insufficientFunds": {"message": "Nicht genügend Gelder."}, "insufficientFundsForGas": {"message": "Unzureichende Gelder für Gas"}, "insufficientLockedLiquidityDescription": {"message": "Aufgrund des Mangels an ausreichend gesicherter oder verbrannter Liquidität ist das Token anfällig für plötzliche Liquiditätsabflüsse, was zu Marktinstabilität führen kann."}, "insufficientLockedLiquidityTitle": {"message": "Unzureichende gesperrte Liquidität"}, "insufficientTokens": {"message": "Nicht genügend Tokens."}, "interactWithSmartContract": {"message": "Smart Contract"}, "interactingWith": {"message": "Interaktion mit"}, "interactingWithTransactionDescription": {"message": "Dies ist der Kontrakt, mit dem Sie interagieren. Schützen Si<PERSON> sich vor Betrügern, indem Sie die Details überprüfen."}, "interaction": {"message": "Interaktion"}, "invalidAddress": {"message": "Ungültige Adresse"}, "invalidAddressRecipient": {"message": "Empfängeradresse ist unzulässig"}, "invalidAssetType": {"message": "Dieses Asset ist ein NFT und muss auf der Seite „NFTs importieren“ unter dem Tab NFTs erneut hinzugefügt werden."}, "invalidChainIdTooBig": {"message": "Ungültige Chain-ID. Die Chain-ID ist zu groß."}, "invalidCustomNetworkAlertContent1": {"message": "Die Chain-ID für benutzerdefiniertes Netzwerk '$1' muss neu eingegeben werden.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Um Si<PERSON> vor betrügerischen oder böswilligen Netzanbietern zu schützen, sind nun Chain-IDs für alle benutzerdefinierten Netzwerke erforderlich."}, "invalidCustomNetworkAlertContent3": {"message": "Gehen Sie zu Einstellungen > Netzwerk und geben Sie die Chain-ID ein. Sie finden die Chain-IDs der beliebtesten Netzwerke auf $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Ungültiges benutzerdefiniertes Netzwerk"}, "invalidHexData": {"message": "Ungültige Hex-Daten"}, "invalidHexNumber": {"message": "Ungültige Hexadezimalzahl."}, "invalidHexNumberLeadingZeros": {"message": "Ungültige Hexadezimalnummer. Entfernen Sie alle führenden Nulle."}, "invalidIpfsGateway": {"message": "Ungültiges IPFS-Gateway: Der Wert muss eine gültige URL sein."}, "invalidNumber": {"message": "Ungültige Zahl. Geben Sie eine dezimale oder mit '0x' vorangestellte hexadezimale Zahl ein."}, "invalidNumberLeadingZeros": {"message": "Ungültige Hexadezimalnummer. Entfernen Sie alle führenden Nulle."}, "invalidRPC": {"message": "Ungültige RPC URI"}, "invalidSeedPhrase": {"message": "Ungültige geheime Wiederherstellungsphrase"}, "invalidSeedPhraseCaseSensitive": {"message": "Ungültige Eingabe! Die geheime Wiederherstellungsphrase berücksichtigt Groß- und Kleinschreibung."}, "ipfsGateway": {"message": "IPFS-Gateway"}, "ipfsGatewayDescription": {"message": "MetaMask verwendet Dienste von Dr<PERSON>tern, um Bilder Ihrer auf IPFS gespeicherten NFTs wiederzugeben, Informationen zu ENS-Adressen anzuzeigen, die Sie in die Adressleiste Ihres Browsers eingegeben haben, und Symbole für verschiedene Tokens abzurufen. Ihre IP-Adresse kann diesen Diensten offengelegt werden, wenn Si<PERSON> diese nutzen."}, "ipfsToggleModalDescriptionOne": {"message": "Wir verwenden Die<PERSON>e von Dr<PERSON>, um Bilder Ihrer auf IPFS gespeicherten NFTs wiederzugeben, <PERSON>en zu ENS-Adressen anzuzeigen, die Sie in die Adressleiste Ihres Browsers eingeben, und Symbole für verschiedene Tokens abzurufen. Ihre IP-Adresse kann diesen Diensten offengelegt werden, wenn Sie diese nutzen."}, "ipfsToggleModalDescriptionTwo": {"message": "Durch die Auswahl von „Bestätigen“ wird die IPFS-Auflösung eingeschaltet. Sie lässt sich jederzeit in $1 wieder ausschalten.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Einstellungen > Sicherheit und Datenschutz"}, "isSigningOrSubmitting": {"message": "Eine frühere Transaktion wird noch signiert oder eingereicht"}, "jazzAndBlockies": {"message": "Jazzicons und Blockies sind zwei verschiedene Arten von einzigartigen Symbolen, mit denen Si<PERSON> ein Konto auf einen Blick erkennen können."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "JSON Datei", "description": "format for importing an account"}, "keyringAccountName": {"message": "<PERSON><PERSON><PERSON>"}, "keyringAccountPublicAddress": {"message": "Öffentliche Adresse"}, "keyringSnapRemovalResult1": {"message": "$1 $2entfernt", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "nicht ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "„$1“ e<PERSON>ben, um zu bestätigen, dass Sie diesen Snap entfernen möchten:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "<PERSON><PERSON><PERSON> Contract-Ad<PERSON>e."}, "knownTokenWarning": {"message": "<PERSON><PERSON> dieser Aktion werden Tokens bearbeitet, die bereits in Ihrer Wallet aufgelistet sind und die dazu verwendet werden können, <PERSON><PERSON> zu betrügen. Genehmigen Sie diese Aktion nur, wenn <PERSON> sicher sind, dass Sie den Wert dieser Tokens ändern möchten. Erfahren Sie mehr über $1."}, "l1Fee": {"message": "L1-<PERSON><PERSON><PERSON><PERSON>"}, "l1FeeTooltip": {"message": "L1-Gas-Gebühr"}, "l2Fee": {"message": "L2-<PERSON><PERSON><PERSON><PERSON>"}, "l2FeeTooltip": {"message": "L2-Gas-Gebühr"}, "lastConnected": {"message": "Zuletzt verbunden"}, "lastSold": {"message": "Zuletzt verkauft"}, "lavaDomeCopyWarning": {"message": "<PERSON><PERSON> Ihrer Sicherheit ist die Auswahl dieses Textes im Moment nicht möglich."}, "layer1Fees": {"message": "Layer 1 Gebühren"}, "layer2Fees": {"message": "Layer-2-<PERSON><PERSON><PERSON><PERSON>"}, "learnHow": {"message": "<PERSON><PERSON><PERSON><PERSON>, wie es funktioniert"}, "learnMore": {"message": "<PERSON><PERSON> er<PERSON>"}, "learnMoreAboutGas": {"message": "Wollen Sie $1 über Gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "Erfahren Sie mehr über bewährte Datenschutzpraktiken."}, "learnMoreAboutSolanaAccounts": {"message": "Erfahren Sie mehr über Solana-Konten"}, "learnMoreKeystone": {"message": "<PERSON><PERSON> er<PERSON>"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON> er<PERSON>"}, "learnMoreUpperCaseWithDot": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "learnScamRisk": {"message": "Betrügereien und Sicherheitsrisiken."}, "leaveMetaMask": {"message": "MetaMask verlassen?"}, "leaveMetaMaskDesc": {"message": "Sie sind im Begriff, eine Seite außer<PERSON>b von MetaMask zu besuchen. Überprüfen Sie die URL, bevor <PERSON> fort<PERSON>hren."}, "ledgerAccountRestriction": {"message": "<PERSON>e müssen Ihr letztes Konto verwenden, ehe Sie ein neues hinzufügen können."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Schließen Sie jede andere Software, die mit Ihrem Gerät verbunden ist und klicken Sie dann hier zum Aktualisieren."}, "ledgerConnectionInstructionHeader": {"message": "Vor dem Klicken bestätigen:"}, "ledgerConnectionInstructionStepFour": {"message": "Aktivieren Sie „Smart Contract Data“ oder „blind signing“ auf Ihrem Ledger-Gerät."}, "ledgerConnectionInstructionStepThree": {"message": "<PERSON><PERSON><PERSON>, dass Ihr Ledger angeschlossen ist und die Ethereum-App ausgewählt ist."}, "ledgerDeviceOpenFailureMessage": {"message": "Das Ledger-Gerät konnte nicht geöffnet werden. Ihr Ledger könnte mit anderer Software verbunden sein. Bitte schließen Sie Ledger Live oder andere Anwendungen, die mit Ihrem Ledger Gerät verbunden sind, und versuchen Sie es erneut."}, "ledgerErrorConnectionIssue": {"message": "Verbinden Sie Ihr Ledger nochmals, öffnen Sie die ETH-App und versuchen Sie es erneut."}, "ledgerErrorDevicedLocked": {"message": "Ihr Ledger ist gesperrt. Entsperren Sie es und versuchen Sie es erneut."}, "ledgerErrorEthAppNotOpen": {"message": "Um das Problem zu lösen, öffnen Sie die ETH-Anwendung auf Ihrem Gerät und versuchen Sie es erneut."}, "ledgerErrorTransactionDataNotPadded": {"message": "Die Eingabedaten der Ethereum-Transaktion sind nicht ausreichend aufgefüllt."}, "ledgerLiveApp": {"message": "Ledger Live App"}, "ledgerLocked": {"message": "<PERSON>ine Verbindung zum Ledger-Gerät. Bitte stellen Sie sicher, dass Ihr Gerät entsperrt ist und die Ethereum-App geöffnet ist."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Um ein neues Gerät anzuschließen, trennen Si<PERSON> das vorherige."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "<PERSON>s kann jeweils nur ein Ledger verbunden werden"}, "ledgerTimeout": {"message": "Ledger Live braucht zu lange für eine Reaktion oder um eine Verbindung herzustellen. <PERSON><PERSON><PERSON> sic<PERSON>, dass die Ledger Live-App geöffnet und Ihr Gerät entsperrt ist."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Das Gerät wurde nicht verbunden. <PERSON>n Sie Ihren Ledger verbinden möchten, klicken Sie bitte erneut auf „Weiter“ und genehmigen Sie die HID-Verbindung", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "Richtungspfeil"}, "lightTheme": {"message": "<PERSON><PERSON>t"}, "likeToImportToken": {"message": "Möchten Sie diesen Token importieren?"}, "likeToImportTokens": {"message": "Möchten Sie diese Tokens hinzufügen?"}, "lineaGoerli": {"message": "Linea-Testnetzwerk"}, "lineaMainnet": {"message": "Linea Mainnet"}, "lineaSepolia": {"message": "Linea-Sepolia-Testnetzwerk"}, "link": {"message": "Link"}, "linkCentralizedExchanges": {"message": "Verlinken Sie Ihre Coinbase- oder Binance-Konten, um Kryptos kostenfrei an MetaMask zu übweisen."}, "links": {"message": "Links"}, "loadMore": {"message": "<PERSON><PERSON> <PERSON>"}, "loading": {"message": "Wird geladen ..."}, "loadingScreenSnapMessage": {"message": "Bitte schließen Sie die Transaktion im Snap ab."}, "loadingTokenList": {"message": "Token-Liste wird geladen"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "<PERSON><PERSON><PERSON>"}, "lockMetaMask": {"message": "MetaMask sperren"}, "lockTimeInvalid": {"message": "Sperrzeit muss eine Zahl zwischen 0 und 10080 sein"}, "logo": {"message": "$1-Logo", "description": "$1 is the name of the ticker"}, "low": {"message": "<PERSON><PERSON><PERSON>"}, "lowEstimatedReturnTooltipMessage": {"message": "Sie zahlen mehr als $1 % Ihres Anfangsbetrags an Gebühren. Prüfen Sie Ihren Empfangsbetrag und die Netzwerkgebühren."}, "lowEstimatedReturnTooltipTitle": {"message": "Hohe Kosten"}, "lowGasSettingToolTipMessage": {"message": "Verwenden Sie $1, um auf einen günstigeren Preis zu warten. Zeitschätzungen sind viel ungenauer, da die Preise nicht vorhersehbar sind.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "ni<PERSON><PERSON>"}, "mainnet": {"message": "Ethereum Mainnet"}, "mainnetToken": {"message": "Diese Adresse stimmt mit einer bekannten Ethereum Mainnet-Token-Adresse überein. Überprüfen Sie erneut die Contract-Adresse und das Netzwerk für das Token, das Sie hinzufügen möchten."}, "makeAnotherSwap": {"message": "Neuen Swap erstellen"}, "makeSureNoOneWatching": {"message": "<PERSON><PERSON><PERSON>, dass niemand zu<PERSON>.", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Standard-Datenschutzeinstellungen verwalten"}, "manageInstitutionalWallets": {"message": "Institutionelle Wallets verwalten"}, "manageInstitutionalWalletsDescription": {"message": "Sc<PERSON><PERSON> Si<PERSON> dies ein, um institutionelle Wallets zu aktivieren."}, "manageNetworksMenuHeading": {"message": "Netzwerke verwalten"}, "managePermissions": {"message": "Genehmigungen verwalten"}, "marketCap": {"message": "Marktkapitalisierung"}, "marketDetails": {"message": "Marktdetails"}, "max": {"message": "<PERSON>."}, "maxBaseFee": {"message": "<PERSON><PERSON>"}, "maxFee": {"message": "Maximale Gebühr"}, "maxFeeTooltip": {"message": "Eine maximale Gebühr, die für die Bezahlung der Transaktion vorgesehen ist."}, "maxPriorityFee": {"message": "Maximale Prioritätsgebühr"}, "medium": {"message": "Mark<PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Verwenden Sie $1 für schnelle Verarbeitung zum aktuellen Marktpreis.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": " Memo"}, "message": {"message": "Nachricht"}, "metaMaskConnectStatusParagraphOne": {"message": "Sie haben nun mehr Kontrolle über Ihre Kontoverbindungen in MetaMask."}, "metaMaskConnectStatusParagraphThree": {"message": "<PERSON><PERSON><PERSON> hier, um Ihre verbundenen Konten zu verwalten."}, "metaMaskConnectStatusParagraphTwo": {"message": "Die Schaltfläche Verbindungsstatus zeigt an, ob die Webseite, die <PERSON> besuchen, mit Ihrem aktuell ausgewählten Konto verbunden ist."}, "metaMetricsIdNotAvailableError": {"message": "Da Sie sich noch nie für MetaMetrics angemeldet haben, gibt es hier keine Daten zu löschen."}, "metadataModalSourceTooltip": {"message": "$1 wird auf npm gehostet und $2 ist die einzige Kennung dieses Snaps.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "Wallet-Benachrichtigungen sind momentan nicht aktiv."}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swaps wird gewartet. Bitte versuchen Sie es später erneut."}, "metamaskVersion": {"message": "MetaMask-Version"}, "methodData": {"message": "<PERSON>e"}, "methodDataTransactionDesc": {"message": "<PERSON><PERSON>, die auf der Grundlage der dekodierten Eingabedaten ausgeführt wird."}, "methodNotSupported": {"message": "Bei diesem Konto nicht unterstützt."}, "metrics": {"message": "Metriken"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "verifizieren Sie die Netzwerkdetails", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Wir empfehlen, dass Sie vor dem Fortfahren $1.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Laut unseren Aufzeichnungen stimmt dieser Netzwerkname nicht mit dieser Chain-ID überein."}, "mismatchedNetworkSymbol": {"message": "Das angegebene Währungssymbol entspricht nicht dem Symbol, das wir für diese Chain-ID erwarten."}, "mismatchedRpcChainId": {"message": "Die vom benutzerdefinierten Netzwerk zurückgesendete Chain-ID stimmt nicht mit der angegebenen Chain-ID überein."}, "mismatchedRpcUrl": {"message": "Laut unseren Aufzeichnungen stimmt der angegebene RPC-URL-Wert nicht mit einem bekannten Anbieter für diese Chain-ID überein."}, "missingSetting": {"message": "Sie können eine Einstellung nicht finden?"}, "missingSettingRequest": {"message": "<PERSON><PERSON> an<PERSON><PERSON>"}, "more": {"message": "mehr"}, "moreAccounts": {"message": "Über $1 mehr Konten", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "Über $1 mehr Netzwerke", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "<PERSON><PERSON>"}, "multichainAddEthereumChainConfirmationDescription": {"message": "<PERSON><PERSON> fügen dieses Netzwerk zu MetaMask hinzu und geben dieser Website die Genehmigung, es zu nutzen."}, "multichainQuoteCardBridgingLabel": {"message": "Bridging"}, "multichainQuoteCardQuoteLabel": {"message": "<PERSON><PERSON><PERSON>"}, "multichainQuoteCardTimeLabel": {"message": "Zeit"}, "multipleSnapConnectionWarning": {"message": "$1 möchte $2 Snaps verwenden", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "Sie müssen mindestens 1 Token auswählen."}, "name": {"message": "Name"}, "nameAddressLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "Der Name wird bereits verwendet"}, "nameInstructionsNew": {"message": "Falls Sie diese Adresse kennen, können Sie ihr einen Spitznamen zu<PERSON>, um sie künftig wiederzuerkennen.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "Diese Adresse hat einen vorgegebenen Spitznamen, den Si<PERSON> jedoch bearbeiten oder weitere Empfehlungen einholen können.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Sie haben bereits einen Spitznamen für diese Adresse hinzugefügt. Sie können weitere vorgeschlagene Spitznamen bearbeiten oder einsehen.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Spitzname", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Vielleicht: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Unbekannte Adresse", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Erkannte Adresse", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Gespeicherte Adresse", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Vorgeschlagen von $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Ethereum Name Service (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "Wählen Sie einen Spitznamen ...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 bittet um Ihre Zustimmung zu:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Netzwerkdetails bearbeiten"}, "nativeTokenScamWarningDescription": {"message": "Das native Token-Symbol stimmt nicht mit dem erwarteten Symbol des nativen Tokens für das Netzwerk mit der zugehörigen Chain-ID überein. Sie haben $1 eingegeben, während das erwartete Token-Symbol $2 ist. Überprüfen Sie bitte, ob <PERSON>e mit der richtigen Chain verbunden sind.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "etwas anderes", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Unerwartetes natives Token-Symbol", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Brauchen Sie Hilfe? Kontaktieren Sie $1.", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "<PERSON><PERSON><PERSON> Si<PERSON> Ihr Fe<PERSON>back"}, "needHelpLinkText": {"message": "MetaMask-Support"}, "needHelpSubmitTicket": {"message": "Ticket absenden"}, "needImportFile": {"message": "<PERSON>e müssen eine zu importierende Datei auswählen.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Negative ETH Beträge können nicht versendet werden."}, "negativeOrZeroAmountToken": {"message": "Negative oder Nullbeträge von Assets können nicht gesendet werden."}, "network": {"message": "Netzwerk"}, "networkChanged": {"message": "Netzwerk geändert"}, "networkChangedMessage": {"message": "Sie führen jetzt eine Transaktion bei $1 durch.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Netzwerkdetails"}, "networkFee": {"message": "Netzwerkgebühr"}, "networkIsBusy": {"message": "Das Netzwerk ist ausgelastet. Die Gas-Preise sind hoch und die Schätzungen sind weniger genau."}, "networkMenu": {"message": "Netzwerkmenü"}, "networkMenuHeading": {"message": "Netzwerk wählen"}, "networkName": {"message": "Netzwerkname"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "<PERSON><PERSON>"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "Der diesem Netzwerk zugeordnete Name."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OP Mainnet"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Testnet"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Netzwerkoptionen"}, "networkPermissionToast": {"message": "Netzwerkgenehmigungen aktualisiert"}, "networkProvider": {"message": "Netzwerkanbieter"}, "networkStatus": {"message": "Netzwerkstatus"}, "networkStatusBaseFeeTooltip": {"message": "Die Grundgebühr wird vom Netzwerk festgelegt und ändert sich alle 13 bis 14 Sekunden. Unsere $1 und $2 Optionen berücksichtigen plötzliche Erhöhungen.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Bandbreite der Prioritätsgebühren (alias „Miner Tip“). Dieser Betrag geht an die Miner und ist ein Anreiz für sie, Ihre Transaktion zu priorisieren."}, "networkStatusStabilityFeeTooltip": {"message": "Die Gas-Gebühren betragen $1 bezogen auf die letzten 72 Stunden.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "Wir können keine Verbindung zu $1 aufbauen.", "description": "$1 represents the network name"}, "networkURL": {"message": "Netzwerk-URL"}, "networkURLDefinition": {"message": "Die URL, die für den Zugriff auf dieses Netzwerk verwendet wird."}, "networkUrlErrorWarning": {"message": "Angreifer imitieren manchmal Websites, indem sie kleine Änderungen an der Adresse der Website vornehmen. Vergewissern Si<PERSON> sich, dass Sie mit der beabsichtigten Website interagieren, bevor <PERSON> fort<PERSON>hren. Punycode-Version: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Netzwerke"}, "networksSmallCase": {"message": "Netzwerke"}, "nevermind": {"message": "<PERSON><PERSON> gut"}, "new": {"message": "Neu!"}, "newAccount": {"message": "<PERSON><PERSON><PERSON>"}, "newAccountNumberName": {"message": "Konto $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Neuer Kontakt"}, "newContract": {"message": "Neuer Contract"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Einstellungen > Sicherheit und Datenschutz"}, "newNFTDetectedInImportNFTsMsg": {"message": "Um Opensea zu verwenden, um Ihre NFTs zu sehen, aktivieren Sie ‚NFT-Medien anzeigen‘ in $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "<PERSON><PERSON> zu, dass MetaMask NFTs automatisch erkennt und anzeigt."}, "newNFTsAutodetected": {"message": "Automatische NFT-Erkennung"}, "newNetworkAdded": {"message": "„$1“ wurde erfolgreich hinzugefügt!"}, "newNetworkEdited": {"message": "„$1“ wurde erfolgreich bearbeitet!"}, "newNftAddedMessage": {"message": "NFT wurde erfolgreich hinzugefügt!"}, "newPassword": {"message": "Neues Passwort (min. 8 Zeichen)"}, "newPrivacyPolicyActionButton": {"message": "<PERSON><PERSON> er<PERSON>"}, "newPrivacyPolicyTitle": {"message": "Wir haben unsere Datenschutzrichtlinie aktualisiert"}, "newRpcUrl": {"message": "Neue RPC-URL"}, "newTokensImportedMessage": {"message": "Sie haben $1 erfolgreich importiert.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token import<PERSON>t"}, "next": {"message": "<PERSON><PERSON>"}, "nftAddFailedMessage": {"message": "NFT kann nicht hinzuge<PERSON><PERSON><PERSON> werden, da die Eigentumsangaben nicht übereinstimmen. <PERSON><PERSON><PERSON>, dass Sie die richtigen Informationen eingegeben haben."}, "nftAddressError": {"message": "Dieses To<PERSON> ist ein NFT. Bei $1 hinzufügen.", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT wurde bereits hinzugefügt."}, "nftAutoDetectionEnabled": {"message": "Automatische NFT-Erkennung aktiviert"}, "nftDisclaimer": {"message": "Haftungsausschluss: MetaMask bezieht die Mediendatei aus der Quellen-URL. Diese URL wird manchmal vom Markt, auf dem das NFT erstellt wurde, ge<PERSON><PERSON>t."}, "nftOptions": {"message": "NFT-Optionen"}, "nftTokenIdPlaceholder": {"message": "Token-ID e<PERSON>ben"}, "nftWarningContent": {"message": "Sie gewähren den Zugriff auf $1, auch auf solche, die Si<PERSON> in Zukunft besitzen könnten. Die Gegenseite kann diese NFTs jederzeit aus Ihrer Wallet übertragen, ohne Si<PERSON> zu fragen, bis Sie diese Genehmigung widerrufen. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "alle Ihre $1-NFTs", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "<PERSON>ien Sie vorsichtig."}, "nfts": {"message": "NFTs"}, "nftsPreviouslyOwned": {"message": "<PERSON><PERSON><PERSON>"}, "nickname": {"message": "Spitzname"}, "noAccountsFound": {"message": "<PERSON>ine Konten für die angegebene Suchanfrage gefunden"}, "noActivity": {"message": "Noch keine Aktivität"}, "noConnectedAccountTitle": {"message": "MetaMask ist nicht mit dieser Website verbunden"}, "noConnectionDescription": {"message": "Um eine Verbindung zu einer Website herzustellen, suchen und wählen Sie die Schaltfläche „Verbinden“. <PERSON>ten Sie, dass MetaMask nur Verbindungen zu Websites auf Web3 herstellen kann"}, "noConversionRateAvailable": {"message": "<PERSON><PERSON>nungskurs verfügbar"}, "noDeFiPositions": {"message": "Noch keine Positionen"}, "noDomainResolution": {"message": "<PERSON><PERSON> Auflösung für die Domain angegeben."}, "noHardwareWalletOrSnapsSupport": {"message": "Snaps und die meisten Hardware-Wallets funktionieren nicht mit Ihrer aktuellen Browserversion."}, "noNFTs": {"message": "Noch keine NFTs"}, "noNetworksFound": {"message": "<PERSON>ür die vorliegende Suchanfrage wurde kein Netzwerk gefunden."}, "noOptionsAvailableMessage": {"message": "Diese Handelsroute ist derzeit nicht verfügbar. Versuchen Sie, den Betrag, das Netzwerk oder den Token zu ändern, und wir werden die bestmögliche Option suchen."}, "noSnaps": {"message": "<PERSON>ine Snaps installiert"}, "noThanks": {"message": "Nein, danke!"}, "noTransactions": {"message": "<PERSON>e haben keine Transaktionen"}, "noWebcamFound": {"message": "Die Webcam Ihres Computers wurde nicht gefunden. Bitte versuchen Si<PERSON> es erneut."}, "noWebcamFoundTitle": {"message": "Webcam nicht gefunden"}, "nonContractAddressAlertDesc": {"message": "Sie senden Anrufdaten an eine Adresse, die kein Kontrakt ist. Dies könnte zu einem Geldverlust führen. <PERSON><PERSON><PERSON>, dass Sie die richtige Adresse und das richtige Netzwerk verwenden, bevor <PERSON> fortfahren."}, "nonContractAddressAlertTitle": {"message": "Mögli<PERSON>hler"}, "nonce": {"message": "Unbekannt"}, "none": {"message": "<PERSON><PERSON>"}, "notBusy": {"message": "Nicht ausgelastet"}, "notCurrentAccount": {"message": "Ist dies das richtige Konto? Es unterscheidet sich von dem aktuell ausgewählten Konto in Ihrer Wallet."}, "notEnoughBalance": {"message": "Unzureichendes G<PERSON>aben"}, "notEnoughGas": {"message": "Nicht genügend Gas"}, "notNow": {"message": "<PERSON>cht jetzt"}, "notificationDetail": {"message": "Details"}, "notificationDetailBaseFee": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (GWEI)"}, "notificationDetailGasLimit": {"message": "Gas-Limit (Einheiten)"}, "notificationDetailGasUsed": {"message": "Verbrauchtes Gas (Einheiten)"}, "notificationDetailMaxFee": {"message": "Maximale Gebühr pro Gas"}, "notificationDetailNetwork": {"message": "Netzwerk"}, "notificationDetailNetworkFee": {"message": "Netzwerkgebühr"}, "notificationDetailPriorityFee": {"message": "Prioritätsgebühr (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "<PERSON><PERSON> dem Block-Explorer überprüfen"}, "notificationItemCollection": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemConfirmed": {"message": "Bestätigt"}, "notificationItemError": {"message": "Gebühren können derzeit nicht abgerufen werden"}, "notificationItemFrom": {"message": "<PERSON>"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Auszahlung bereit"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "<PERSON>e können sich nun <PERSON><PERSON><PERSON> Unstaked $1 auszahlen lassen"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Ihre Anfrage zum Unstake von $1 wurde gesendet"}, "notificationItemNFTReceivedFrom": {"message": "NFT empfangen von"}, "notificationItemNFTSentTo": {"message": "NFT gesendet an"}, "notificationItemNetwork": {"message": "Netzwerk"}, "notificationItemRate": {"message": "<PERSON><PERSON> (inklusive Gebühr)"}, "notificationItemReceived": {"message": "Empfangen"}, "notificationItemReceivedFrom": {"message": "<PERSON><PERSON><PERSON><PERSON> von"}, "notificationItemSent": {"message": "Gesendet"}, "notificationItemSentTo": {"message": "Gesendet an"}, "notificationItemStakeCompleted": {"message": "Stake abgeschlossen"}, "notificationItemStaked": {"message": "Staked"}, "notificationItemStakingProvider": {"message": "Staking-Anbieter"}, "notificationItemStatus": {"message": "Status"}, "notificationItemSwapped": {"message": "Geswappt"}, "notificationItemSwappedFor": {"message": "für"}, "notificationItemTo": {"message": "An"}, "notificationItemTransactionId": {"message": "Transaktions-ID"}, "notificationItemUnStakeCompleted": {"message": "UnStaking abgeschlossen"}, "notificationItemUnStaked": {"message": "Unstaked"}, "notificationItemUnStakingRequested": {"message": "Unstaking angefordert"}, "notificationTransactionFailedMessage": {"message": "Transaktion $1 ist fehlgeschlagen! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Fehlgeschlagene Transaktion", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "Transaktion $1 wurde bestätigt!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Bestätigte Transaktion", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Auf $1 ansehen", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Benachrichtigungen"}, "notificationsFeatureToggle": {"message": "Wallet-Benachrichtigungen aktivieren", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "Dies ermöglicht Wallet-Benachrichtigungen wie das Senden/Empfangen von Geldern oder NFTs und Funktionsankündigungen.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Alle als gelesen markieren"}, "notificationsPageEmptyTitle": {"message": "Hier gibt es nichts zu sehen"}, "notificationsPageErrorContent": {"message": "<PERSON>te versuchen Si<PERSON>, diese Seite erneut zu besuchen."}, "notificationsPageErrorTitle": {"message": "Ein Fehler ist aufgetreten"}, "notificationsPageNoNotificationsContent": {"message": "Sie haben noch keine Benachrichtigungen empfangen."}, "notificationsSettingsBoxError": {"message": "Etwas ist schiefgelaufen! Bitte versuchen Sie es erneut."}, "notificationsSettingsPageAllowNotifications": {"message": "Bleiben Sie mit Benachrichtigungen stets darüber auf dem Laufenden, was in Ihrer Wallet passiert. Zur Nutzung von Benachrichtigungen verwenden wir ein Profil, um bestimmte Einstellungen auf Ihren Geräten zu synchronisieren. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "<PERSON><PERSON><PERSON><PERSON>, wie wir Ihre Privatsphäre bei der Nutzung dieser Funktion schützen."}, "numberOfNewTokensDetectedPlural": {"message": "$1 neue Tokens in diesem Konto gefunden.", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 neues Token in diesem Konto gefunden."}, "numberOfTokens": {"message": "<PERSON><PERSON><PERSON>"}, "ofTextNofM": {"message": "von"}, "off": {"message": "Aus"}, "offlineForMaintenance": {"message": "Offline für Wartung"}, "ok": {"message": "Ok"}, "on": {"message": "An"}, "onboardedMetametricsAccept": {"message": "Ich stimme zu"}, "onboardedMetametricsDisagree": {"message": "<PERSON><PERSON>, danke"}, "onboardedMetametricsKey1": {"message": "Neueste Entwicklungen"}, "onboardedMetametricsKey2": {"message": "Produktmerkmale"}, "onboardedMetametricsKey3": {"message": "Andere relevante Werbematerialien"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "Zus<PERSON><PERSON>lich zu $1 möchten wir Daten verwenden, um zu verstehen, wie Sie mit Marketingkommunikation umgehen.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "<PERSON>s hilft uns, das, was wir mit <PERSON>hnen teilen, zu personalisieren, wie z. B.:"}, "onboardedMetametricsParagraph3": {"message": "<PERSON><PERSON>, dass wir die von Ihnen bereitgestellten Daten niemals verkaufen und Sie sich jederzeit abmelden können."}, "onboardedMetametricsTitle": {"message": "<PERSON><PERSON><PERSON> un<PERSON>, <PERSON><PERSON> Erlebnis zu verbessern"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "Das IPFS-Gateway ermöglicht es, auf von Dritten gehostete Daten zuzugreifen und diese einzusehen. Sie können ein benutzerdefiniertes IPFS-Gateway hinzufügen oder weiterhin das Standard-Gateway verwenden."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Bitte geben Si<PERSON> eine gültige URL ein."}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Benutzerdefiniertes IPFS-Gateway hinzufügen"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "URL für IPFS-Gateway ist gültig"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Wenn Sie unsere Standardeinstellungen und -konfigurationen verwenden, nutzen wir Infura als Standard-RPC-Anbieter (Remote Procedure Call), um einen möglichst zuverlässigen und privaten Zugriff auf Ethereum-Daten zu gewährleisten. In Ausnahmefällen können wir auch andere RPC-Anbieter einsetzen, um unseren Nutzern das bestmögliche Erlebnis zu bieten. Sie können Ihren eigenen RPC wählen, doch bedenken Sie dabei, dass jeder RPC Ihre IP-Adresse und Ihr Ethereum-Wallet zur Durchführung von Transaktionen erhalten wird. Weitere Informationen darüber, wie Infura Daten für EVM-Konten verarbeitet, finden Sie in unserer $1; und für Solana-Konten in der $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "klicken <PERSON> hier"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Wählen Sie Ihr Netzwerk."}, "onboardingCreateWallet": {"message": "<PERSON>e neue Wallet erstellen"}, "onboardingImportWallet": {"message": "Existierende Wallet importieren"}, "onboardingMetametricsAgree": {"message": "Ich stimme zu"}, "onboardingMetametricsDescription": {"message": "Wir würden gerne grundlegende Nutzungs- und Diagnosedaten sammeln, um MetaMask zu verbessern. <PERSON>e sollten wissen, dass wir die Daten, die Sie uns hier zur Verfügung stellen, niemals verkaufen."}, "onboardingMetametricsInfuraTerms": {"message": "Wir werden Sie informieren, wenn wir beschließen, diese Daten für andere Zwecke zu verwenden. Für weitere Informationen können Sie unsere $1 einsehen. Vergessen Si<PERSON> nicht, dass Sie jederzeit zu Einstellungen gehen und sich abmelden können.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Datenschutzrichtlinie"}, "onboardingMetametricsNeverCollect": {"message": "$1 Klicks und Aufrufe der App werden gespeichert, andere Details (wie Ihre öffentliche Adresse) jedoch nicht.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Privat:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 wir verwenden Ihre IP-Adresse vorübergehend, um einen allgemeinen Standort zu ermitteln (z. B. Ihr Land oder Ihre Region), aber er wird niemals gespeichert.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Allgemein:"}, "onboardingMetametricsNeverSellData": {"message": "$1 Sie können jederzeit über die Einstellungen entscheiden, ob Sie Ihre Nutzungsdaten freigeben oder löschen möchten.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Optional:"}, "onboardingMetametricsTitle": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> zu verbessern."}, "onboardingMetametricsUseDataCheckbox": {"message": "Wir verwenden diese Daten, um zu erfahren, wie Sie mit unserer Marketingkommunikation umgehen. Wir können relevante Neuigkeiten (wie Produktmerkmale) teilen."}, "onboardingPinExtensionDescription": {"message": "Heften Sie MetaMask in Ihrem Browser ab, damit Sie auf die Transaktionsbestätigungen zugreifen und sie leicht einsehen können."}, "onboardingPinExtensionDescription2": {"message": "Sie können MetaMask öffnen, indem Sie auf die Erweiterung klicken, und mit nur einem Klick auf Ihre Wallet zugreifen."}, "onboardingPinExtensionDescription3": {"message": "Klicken Sie auf das Symbol der Browser-Erweiterung, um sofort darauf zuzugreifen.", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Ihre MetaMask Installation ist abgeschlossen!"}, "onekey": {"message": "OneKey"}, "only": {"message": "nur"}, "onlyConnectTrust": {"message": "Verbinden Sie sich nur mit Websites, denen Si<PERSON> vertrauen. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Öffnen Sie MetaMask im Vollbildmodus, um Ihren Ledger über WebHID zu verbinden.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Im Block-<PERSON>"}, "optional": {"message": "Optional"}, "options": {"message": "Optionen"}, "origin": {"message": "Ursprung"}, "originChanged": {"message": "Website geändert"}, "originChangedMessage": {"message": "Sie überprüfen gerade eine Anfrage von $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "System"}, "other": {"message": "andere"}, "otherSnaps": {"message": "sonstige Snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "andere"}, "outdatedBrowserNotification": {"message": "<PERSON>hr Browser ist veraltet. Wenn Sie Ihren Browser nicht aktualisieren, können Sie keine Sicherheits-Patches und neue Funktionen von MetaMask erhalten."}, "overrideContentSecurityPolicyHeader": {"message": "Content-Security-Policy-<PERSON><PERSON> au<PERSON><PERSON><PERSON>"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Diese Option dient als Behelfslösung für ein bekanntes Problem in Firefox, bei dem der Content-Security-Policy-Header einer dApp verhindern kann, dass die Erweiterung ordnungsgemäß geladen wird. Die Deaktivierung dieser Option wird nicht empfohlen, es sei denn, sie ist für die Kompatibilität bestimmter Webseiten erforderlich."}, "padlock": {"message": "Padlock"}, "participateInMetaMetrics": {"message": "Bei MetaMetrics teilnehmen"}, "participateInMetaMetricsDescription": {"message": "Nehmen Sie an MetaMetrics teil, um uns bei der Verbesserung von MetaMask zu helfen."}, "password": {"message": "Passwort"}, "passwordNotLongEnough": {"message": "Passwort nicht lang genug"}, "passwordStrength": {"message": "Passwortstärke: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Ein starkes Passwort kann die Sicherheit Ihrer Wallet erhöhen, falls Ihr Gerät gestohlen oder kompromittiert wird."}, "passwordTermsWarning": {"message": "<PERSON><PERSON> verstehe, dass MetaMask dieses Passwort für mich nicht wiederherstellen kann. $1"}, "passwordsDontMatch": {"message": "Passwörter stimmen nicht überein."}, "pastePrivateKey": {"message": "Geben Sie hier die Zeichenfolge Ihres privaten Schlüssels ein:", "description": "For importing an account from a private key"}, "pending": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Durch eine Aktualisierung des Netzwerks werden $1 ausstehende Transaktionen von dieser Website storniert.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Durch einen Netzwerkwechsel werden $1 ausstehende Transaktionen von dieser Website storniert.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Diese Transaktion wird nicht ausgeführt, bevor eine vorherige Transaktion abgeschlossen ist. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "<PERSON><PERSON><PERSON><PERSON>, wie Sie eine Transaktion stornieren oder beschleunigen.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Genehmigungsdetails"}, "permissionFor": {"message": "Genehmigung für"}, "permissionFrom": {"message": "Genehm<PERSON><PERSON> von"}, "permissionRequested": {"message": "Jetzt angefragt"}, "permissionRequestedForAccounts": {"message": "Jetzt für $1 angefordert", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "In diesem Update widerrufen"}, "permissionRevokedForAccounts": {"message": "In diesem Update für $1 widerrufen", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Mit $1 verbinden.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Greifen Sie auf das Internet zu.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Erlauben Sie $1, auf das Internet zuzugreifen. Dies kann sowohl zum Senden als auch zum Empfangen von Daten mit Servern von Drittanbietern verwendet werden.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Verbinden Sie sich mit dem $1-Snap.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Webseite oder Snap erlauben, mit $1 zu interagieren.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Konto-Assets in MetaMask anzeigen.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Erlauben Sie $1 Asset-Informationen an den MetaMask-Kunden zu übermitteln. Die Assets können sowohl Onchain als auch Offchain sein.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Regelmäßige Transaktionen planen und ausführen.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Erlauben Sie $1, Aktionen auszuführen, die periodisch zu festen Zeiten, Daten oder Intervallen ablaufen. Dies kann verwendet werden, um zeitkritische Interaktionen oder Benachrichtigungen auszulösen.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Dialogfenster in MetaMask anzeigen.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Erlauben Sie $1, MetaMask-Popups mit benutzerdefiniertem Text, Eingabefeld und Schaltflächen zur Genehmigung oder Ablehnung einer Aktion anzugeigen. Kann verwendet werden, um z. B<PERSON>n, Bestätigungen und Opt-in-Flows für einen Snap zu erstellen.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "<PERSON><PERSON><PERSON> Adresse, Kontostand, Aktivitäten und Vorschläge für zu genehmigende Transaktionen.", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Auf den Ethereum-Anbieter zugreifen.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Erlauben Sie $1, direkt mit MetaMask zu kommunizieren, damit es Daten aus der Blockchain lesen und Nachrichten und Transaktionen vorschlagen kann.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Leiten Sie beliebige Schlüssel ab, die nur für $1 gelten.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Erlauben Sie $1, beliebige Schlüssel abzuleiten, die nur für $1 gelten, ohne sie offenzulegen. Diese Schlüssel sind von Ihrem MetaMask-Konto bzw. Ihren MetaMask-Konten getrennt und haben nichts mit Ihren privaten Schlüsseln oder Ihrer geheimen Wiederherstellungsphrase zu tun. Andere Snaps können nicht auf diese Informationen zugreifen.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "Ihre bevorzugte Sprache anzeigen.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Lassen Sie $1 über Ihre MetaMask-Einstellungen auf Ihre bevorzugte Sprache zugreifen. Dies kann verwendet werden, um den Inhalt von $1 in Ihrer Sprache zu lokalisieren und anzuzeigen.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "<PERSON><PERSON> Sie Informationen wie Ihre bevorzugte Sprache und Fiat-Währung.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Geben Sie $1 in Ihren MetaMask-Einstellungen Zugriff auf Informationen wie die von Ihnen bevorzugte Sprache und Fiat-Währung. Auf diese Weise kann $1 die auf Ihre Präferenzen zugeschnittenen Inhalte anzeigen. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Anzeige eines benutzerdefinierten Bildschirms", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Lassen Sie $1 einen benutzerdefinierten Startbildschirm in MetaMask anzeigen. Dieser kann für Benutzeroberflächen, Konfiguration und Dashboards verwendet werden.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Anfragen zur Hinzufügung und Steuerung von Ethereum-Konten erlauben", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Lassen Sie $1 Anfragen zum Hinzufügen oder Entfernen von Konten entgegennehmen sowie im Namen dieser Konten unterschreiben und Transaktionen durchführen.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Verwenden Sie Lebenszyklus-Hooks.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Erlauben Sie $1, <PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> zu verwenden, um Code zu bestimmten Zeiten während seines Lebenszyklus auszuführen.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Ethereum-Konten hinzufügen und kontrollieren", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Erlauben Sie $1, Ethereum-Konten hinzuzufügen oder zu entfernen und dann mit diesen Konten Transaktionen durchzuführen und zu unterschreiben.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "$1-<PERSON><PERSON><PERSON> verwalten.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Erlauben Sie $1, Konten und Assets in dem gewünschten Netzwerk zu verwalten. Diese Konten werden unter Verwendung Ihrer geheimen Wiederherstellungsphrase abgeleitet und gesichert (ohne sie preiszugeben). Mi<PERSON> der Fähigkeit, Schl<PERSON><PERSON> abzuleiten, kann $1 eine Vielzahl von Blockchain-Protokollen über Ethereum (EVMs) hinaus unterstützen.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "$1-<PERSON><PERSON><PERSON> verwalten.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Speichern und verwalten Sie die Daten auf Ihrem Gerät.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Erlauben Sie $1, Daten sicher und verschlüsselt zu speichern, zu aktualisieren und abzurufen. Andere Snaps können nicht auf diese Informationen zugreifen.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Bereitstellung von Domain- und Adress-Lookups.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Erlauben Sie dem Snap, Adress- und Domain-Lookups in verschiedenen Teilen der MetaMask-Benutzeroberfläche abzurufen und anzuzeigen.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Benachrichtigungen anzeigen.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Erlauben Sie $1, Benachrichtigungen in MetaMask anzuzeigen. Ein kurzer Benachrichtigungstext kann durch einen Snap für handlungsrelevante oder zeitkritische Informationen ausgelöst werden.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "<PERSON><PERSON>n Sie Protokolldaten für eine oder mehrere Chains bereit.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Erlauben Sie $1, MetaMask Protokolldaten wie Gasschätzungen oder Token-Informationen bereitzustellen.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Erlauben Sie $1, direkt mit $2 zu kommunizieren.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Erlauben Sie $1, <PERSON><PERSON><PERSON><PERSON> an $2 zu senden und eine Antwort von $2 zu erhalten.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 und $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Signatureinblicke modal anzeigen.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Erlauben Sie $1, ein Modal mit Einblicken in jede Signaturanfrage vor der Genehmigung anzuzeigen. Dies kann für Anti-Phishing- und Sicherheitslösungen verwendet werden.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "<PERSON><PERSON> sich die Herkunft der Websites ein, die eine Signaturanfrage initiieren.", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Erlauben Sie $1, die Herkunft (URI) von Websites zu sehen, die Signaturanfragen initiieren. Dies kann für Anti-Phishing- und Sicherheitslösungen verwendet werden.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Transaktions-Einsichten abrufen und anzeigen.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Erlauben Sie $1, Transaktionen zu dekodieren und Einblicke innerhalb der MetaMask UI zu zeigen. Dies kann für Anti-Phishing- und Sicherheitslösungen verwendet werden.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Ursprung der Webseite anzeigen, die Transaktionen vorschlägt", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Erlauben Sie $1, die Herkunft (URI) von Websites einzu<PERSON>hen, die Transaktionen vorschlagen. Dies kann für Anti-Phishing- und Sicherheitslösungen verwendet werden.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Unbekannte Genehmigung: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Öffentlichen Schlüssel für $1 ($2) anzeigen.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Erlauben Sie $2, Ihre öffentlichen Schlüssel (und Adressen) für $1 einzusehen. Damit wird keine Kontrolle über Konten oder Assets gewährt.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Öffentlichen Schlüssel für $1 anzeigen.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Wechseln Sie zum folgenden Netzwerk und nutzen Sie dieses", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Support für WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Erlauben Sie $1, auf Low-Level-Ausführungsumgebungen über WebAssembly zuzugreifen.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "Genehmigungen"}, "permissionsPageEmptyContent": {"message": "Nichts zu sehen hier"}, "permissionsPageEmptySubContent": {"message": "Hier können Sie die Genehmigungen sehen, die Sie installierten Snaps oder verbundenen Websites gegeben haben."}, "permitSimulationChange_approve": {"message": "Ausgabenobergrenze"}, "permitSimulationChange_bidding": {"message": "<PERSON><PERSON> bieten"}, "permitSimulationChange_listing": {"message": "Sie listen auf"}, "permitSimulationChange_nft_listing": {"message": "Notierungspreis"}, "permitSimulationChange_receive": {"message": "<PERSON><PERSON> empfangen"}, "permitSimulationChange_revoke2": {"message": "Widerrufen"}, "permitSimulationChange_transfer": {"message": "<PERSON><PERSON> senden"}, "permitSimulationDetailInfo": {"message": "Sie erteilen dem Spender die Genehmigung, diese Menge an Tokens von Ihrem Konto auszugeben."}, "permittedChainToastUpdate": {"message": "$1 hat Zugang zu $2."}, "personalAddressDetected": {"message": "Personalisierte Adresse identifiziert. Bitte füge die Token-Contract-Adresse ein."}, "pinToTop": {"message": "Pin nach oben"}, "pleaseConfirm": {"message": "Bitte bestätigen"}, "plusMore": {"message": "+ $1 mehr", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 mehr", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Einige dieser Netzwerke werden von Dritten betrieben. Die Verbindungen können weniger zuverlässig sein oder Dritten ermöglichen, Aktivitäten zu verfolgen.", "description": "Learn more link"}, "popularNetworks": {"message": "Beliebte Netzwerke"}, "portfolio": {"message": "Portfolio"}, "preparingSwap": {"message": "Swap wird vorbereitet ..."}, "prev": {"message": "Zurück"}, "price": {"message": "Pre<PERSON>"}, "priceUnavailable": {"message": "<PERSON>is nicht verfügbar"}, "primaryType": {"message": "Primärer <PERSON>"}, "priorityFee": {"message": "Prioritätsgebühr"}, "priorityFeeProperCase": {"message": "Prioritätsgebühr"}, "privacy": {"message": "Datenschutz"}, "privacyMsg": {"message": "Datenschutzerklärung"}, "privateKey": {"message": "Private<PERSON><PERSON><PERSON>", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Privater <PERSON><PERSON><PERSON><PERSON> für $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "Der private Schlüssel ist verborgen", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Eingabe des privaten Schlüssels anzeigen/ausblenden", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Dieser private Schlüssel wird angezeigt", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Warnung: <PERSON><PERSON><PERSON> diesen Schlüssel niemals weiter. <PERSON><PERSON>, der Ihre privaten Schlüssel hat, kann alle Assets auf Ihrem Konto stehlen."}, "privateNetwork": {"message": "Privates Netzwerk"}, "proceedWithTransaction": {"message": "Ich möchte dennoch fortfahren."}, "productAnnouncements": {"message": "Produktankündigungen"}, "proposedApprovalLimit": {"message": "Vorgeschlagenes Genehmigungslimit"}, "provide": {"message": "Bereitstellen"}, "publicAddress": {"message": "Öffentliche Adresse"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Sie haben $1 $2 erhalten"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Sie haben einige Tokens erhalten"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Gelder erhalten"}, "pushPlatformNotificationsFundsSentDescription": {"message": "Sie haben erfolgreich $1 $2 gesendet"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Sie haben erfolgreich einige Tokens gesendet"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Gelder gesendet"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Sie haben neue NFTs erhalten"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT erhalten"}, "pushPlatformNotificationsNftSentDescription": {"message": "Sie haben erfolgreich einen NFT gesendet"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT gesendet"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Ihr Lido-Stake war erfolgreich"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Stake abgeschlossen"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Ihr Lido-Stake kann jetzt ausgezahlt werden"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Stake bereit zur Auszahlung"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Ihre Lido-Auszahlung war erfolgreich"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Auszahlung abgeschlossen"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Ihre Lido-Auszahlungsanfrage wurde übermittelt"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "Auszahlung angefordert"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Ihr RocketPool-Stake war erfolgreich"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Stake abgeschlossen"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Ihr RocketPool-Unstake war erfolgreich"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Unstake abgeschlossen"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Ihr MetaMask-Swap war erfolgreich"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Swap abgeschlossen"}, "queued": {"message": "In Warteschlange"}, "quoteRate": {"message": "Angebotskurs"}, "quotedReceiveAmount": {"message": "$1 Betrag empfangen"}, "quotedTotalCost": {"message": "$1 Gesamtkosten"}, "rank": {"message": "<PERSON>ng"}, "rateIncludesMMFee": {"message": "Rate beinhaltet $1 % Gebühr"}, "reAddAccounts": {"message": "alle anderen Konten erneut hinzuzufügen"}, "reAdded": {"message": "erneut hinz<PERSON>f<PERSON>gt"}, "readdToken": {"message": "<PERSON><PERSON> können dieses Token in Zukunft wieder hinzufügen, indem Sie im Menü der Kontooptionen auf „Token importieren“ gehen."}, "receive": {"message": "Empfangen"}, "receiveCrypto": {"message": "Krypto empfangen"}, "recipientAddressPlaceholderNew": {"message": "Öffentliche Adresse (0x) oder Domainname eingeben"}, "recommendedGasLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "recoveryPhraseReminderBackupStart": {"message": "<PERSON><PERSON> <PERSON>"}, "recoveryPhraseReminderConfirm": {"message": "Verstanden"}, "recoveryPhraseReminderHasBackedUp": {"message": "Bewahren Sie Ihre geheime Wiederherstellungsphrase immer an einem sicheren und geheimen Ort auf."}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Möchten Sie erneut ein Backup Ihrer geheimen Wiederherstellungsphrase erstellen?"}, "recoveryPhraseReminderItemOne": {"message": "Te<PERSON>n Sie niemals Ihre geheime Wiederherstellungsphrase mit jemandem."}, "recoveryPhraseReminderItemTwo": {"message": "Das MetaMask-Team wird Sie niemals nach Ihrer geheimen Wiederherstellungsphrase fragen."}, "recoveryPhraseReminderSubText": {"message": "Ihre geheime Wiederherstellungsphrase kontrolliert alle Ihre Konten."}, "recoveryPhraseReminderTitle": {"message": "Schützen Sie Ihre Gelder."}, "redeposit": {"message": "<PERSON><PERSON><PERSON> e<PERSON>en"}, "refreshList": {"message": "Liste aktualisieren"}, "reject": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON>e <PERSON>en"}, "rejectRequestsDescription": {"message": "Sie sind im Begriff, $1 Anfragen geschlossen abzulehnen."}, "rejectRequestsN": {"message": "$1 Anfragen <PERSON>"}, "rejectTxsDescription": {"message": "Sie sind im Begriff, $1 Transaktionen geschlossen abzulehnen."}, "rejectTxsN": {"message": "$1 Transaktionen ablehnen"}, "rejected": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "remove": {"message": "Entfernen"}, "removeAccount": {"message": "<PERSON><PERSON> entfernen"}, "removeAccountDescription": {"message": "Dieses Konto wird aus Ihrer Wallet entfernt. Bitte stellen Si<PERSON>, dass Sie die ursprüngliche geheime Wiederherstellungsphrase oder den privaten Schlüssel für dieses importierte Konto haben, bevor <PERSON> fortfahren. Sie können Konten über das Dropdown-Menü „Konto“ erneut importieren oder erstellen."}, "removeKeyringSnap": {"message": "Das Entfernen dieses Snaps entfernt folgende Konten aus MetaMask:"}, "removeKeyringSnapToolTip": {"message": "Der Snap kontrolliert die Konten und durch seine Entfernung werden diese Konten ebenfalls aus MetaMask entfernt, verbleiben jedoch in der Blockchain."}, "removeNFT": {"message": "NFT entfernen"}, "removeNftErrorMessage": {"message": "Wir konnten dieses NFT nicht entfernen."}, "removeNftMessage": {"message": "NFT wurde erfolgreich entfernt!"}, "removeSnap": {"message": "Snap entfernen"}, "removeSnapAccountDescription": {"message": "<PERSON><PERSON>, wird dieses Konto nicht mehr in MetaMask verfügbar sein."}, "removeSnapAccountTitle": {"message": "<PERSON><PERSON> entfernen"}, "removeSnapConfirmation": {"message": "Sind Si<PERSON> sicher, dass Sie $1 entfernen möchten?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "Diese Aktion wird diesen Snap und seine Daten löschen sowie alle von Ihnen erteilten Genehmigungen entziehen."}, "replace": {"message": "<PERSON><PERSON><PERSON>"}, "reportIssue": {"message": "Ein Problem melden"}, "requestFrom": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "requestFromInfo": {"message": "Dies ist die Seite, auf der Si<PERSON> um Ihre Unterschrift gebeten werden."}, "requestFromInfoSnap": {"message": "Dies ist der Snap, der Sie zur Unterschrift auffordert."}, "requestFromTransactionDescription": {"message": "Dies ist die Website, die Sie um Ihre Bestätigung bittet."}, "requestingFor": {"message": "Anfordern für"}, "requestingForAccount": {"message": "Anfordern für $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Anforderung von $1", "description": "Name of Network"}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "Z<PERSON>ücksetzen"}, "resetWallet": {"message": "Wallet zurücksetzen"}, "resetWalletSubHeader": {"message": "MetaMask speichert keine Kopie Ihres Passworts. <PERSON>n Sie Probleme haben, <PERSON><PERSON> Konto zu entsperren, müssen Sie Ihre Wallet zurücksetzen. <PERSON><PERSON> können dies tun, indem Sie die geheime Wiederherstellungsphrase angeben, die Sie bei der Einrichtung Ihrer Wallet verwendet haben."}, "resetWalletUsingSRP": {"message": "Diese Aktion löscht Ihre aktuelle Wallet und die geheime Wiederherstellungsphrase von diesem Gerät zusammen mit der Liste der Konten, die Sie erstellt haben. Nach dem Zurücksetzen mit einer geheimen Wiederherstellungsphrase sehen Sie eine Liste von <PERSON>, die auf der geheimen Wiederherstellungsphrase basiert, die Sie zum Zurücksetzen verwenden. Diese neue Liste enthält automatisch Konten, die ein Guthaben aufweisen. Sie können auch $1, die zuvor erstellt wurden. Benutzerdefinierte Konten, die Sie importiert haben, müssen $2 sein und alle benutzerdefinierten Tokens, die Sie einem Konto hinzugefügt haben, müssen ebenfalls $3 sein."}, "resetWalletWarning": {"message": "Vergewisser<PERSON> sich, dass Sie die richtige geheime Wiederherstellungsphrase verwenden, bevor <PERSON> fort<PERSON>hren. Sie können dies nicht mehr rückgängig machen."}, "restartMetamask": {"message": "MetaMask neu starten"}, "restore": {"message": "Wiederherstellen"}, "restoreUserData": {"message": "Benutzerdaten wiederherstellen"}, "resultPageError": {"message": "<PERSON><PERSON>"}, "resultPageErrorDefaultMessage": {"message": "Der Vorgang ist fehlgeschlagen."}, "resultPageSuccess": {"message": "Erfolg"}, "resultPageSuccessDefaultMessage": {"message": "Der Vorgang wurde erfolgreich abgeschlossen."}, "retryTransaction": {"message": "Transaktion wiederholen"}, "reusedTokenNameWarning": {"message": "Ein Token hier verwendet ein Symbol von einem anderen <PERSON>, das <PERSON> beobachten. Dies kann verwirrend oder trügerisch sein."}, "revealSecretRecoveryPhrase": {"message": "Geheime Wiederherstellungsphrase offenlegen"}, "revealSeedWords": {"message": "Geheime Wiederherstellungsphrase offenlegen"}, "revealSeedWordsDescription1": {"message": "Die $1 bietet $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMask ist ein $1. Das bedeutet, dass Sie der Besitzer Ihrer GWP sind.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "vollen Zugriff auf Ihre Wallet und Ihr Guthaben.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "Wallet ohne Vewahrung"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Geheime Wiederherstellungsphrase (GWP)"}, "revealSeedWordsText": {"message": "Text"}, "revealSeedWordsWarning": {"message": "<PERSON><PERSON><PERSON>, dass niemand auf Ihren Bildschirm schaut. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "Der MetaMask Support wird Sie nie danach fragen.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "Sensible Inhalte offenlegen"}, "review": {"message": "Überprüfen"}, "reviewAlert": {"message": "Benachrichtigung überprüfen"}, "reviewAlerts": {"message": "Benachrichtigungen überprüfen"}, "reviewPendingTransactions": {"message": "Ausstehende Transaktionen überprüfen"}, "reviewPermissions": {"message": "Genehmigungen prüfen"}, "revokePermission": {"message": "Genehmigung widerrufen"}, "revokePermissionTitle": {"message": "$1 Genehmigung entfernen", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "<PERSON>e entz<PERSON>hen einer Person die Genehmigung, <PERSON><PERSON><PERSON> von Ihrem Konto auszugeben."}, "reward": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rpcNameOptional": {"message": "RPC-Name (Optional)"}, "rpcUrl": {"message": "RPC-URL"}, "safeTransferFrom": {"message": "Sichere Übertragung von"}, "save": {"message": "Speichern"}, "scanInstructions": {"message": "Platzieren Sie den QR-Code vor Ihrer Kamera."}, "scanQrCode": {"message": "QR-Code scannen"}, "scrollDown": {"message": "Herunterscrollen"}, "search": {"message": "<PERSON><PERSON>"}, "searchAccounts": {"message": "Konten durchsuchen"}, "searchNfts": {"message": "NFTs suchen"}, "searchTokens": {"message": "Tokens suchen"}, "searchTokensByNameOrAddress": {"message": "Tokens nach Name oder Adresse suchen"}, "secretRecoveryPhrase": {"message": "Geheime Wiederherstellungsphrase"}, "secretRecoveryPhrasePlusNumber": {"message": "Geheime Wiederherstellungsphrase $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "<PERSON><PERSON><PERSON>"}, "security": {"message": "Sicherheit"}, "securityAlert": {"message": "Sicherheitsbenachrichtigung von $1 und $2"}, "securityAlerts": {"message": "Sicherheitsbenachrichtigungen"}, "securityAlertsDescription": {"message": "Diese Funktion warnt Sie vor bösartigen Aktivitäten, indem sie aktiv Transaktionen und Signaturanfragen überprüft. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Sicherheit und Datenschutz"}, "securityDescription": {"message": "Verringern Sie das Risiko, sich mit unsicheren Netzwerken zu verbinden und sichern Sie Ihre Konten"}, "securityMessageLinkForNetworks": {"message": "Netzwerk-Betrügereien und Sicherheitsrisiken"}, "securityProviderPoweredBy": {"message": "Unterstützt durch $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "Alle Genehmigungen ansehen", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "Details anzeigen"}, "seedPhraseIntroTitle": {"message": "<PERSON><PERSON>n Sie Ihre Wallet"}, "seedPhraseReq": {"message": "Geheime Wiederherstellungsphrasen bestehen aus 12, 15, 18, 21 oder 24 Wörtern."}, "select": {"message": "Auswählen"}, "selectAccountToConnect": {"message": "Ein zu verbindendes Konto auswählen"}, "selectAccounts": {"message": "<PERSON><PERSON><PERSON>en Sie das Konto/die Konten aus, um sie auf dieser Seite zu verwenden."}, "selectAccountsForSnap": {"message": "<PERSON><PERSON><PERSON>en Sie das Konto/die Konten, das/die mit diesem Snap verwendet werden soll(en)."}, "selectAll": {"message": "\nAlle auswählen"}, "selectAnAccount": {"message": "Ein Konto auswählen"}, "selectAnAccountAlreadyConnected": {"message": "<PERSON><PERSON> wurde bereits mit MetaMask verbunden."}, "selectEnableDisplayMediaPrivacyPreference": {"message": "NFT-Medien anzeigen einschalten"}, "selectHdPath": {"message": "HD-Pfad auswählen"}, "selectNFTPrivacyPreference": {"message": "Automatische NFT-Erkennung aktivieren"}, "selectPathHelp": {"message": "<PERSON><PERSON> <PERSON> nicht die <PERSON>nten sehen, die <PERSON> erwart<PERSON>, versuchen Sie, den HD-Pfad oder das aktuell ausgewählte Netzwerk zu ändern."}, "selectRpcUrl": {"message": "RPC-URL auswählen"}, "selectSecretRecoveryPhrase": {"message": "Geheime Wiederherstellungsphrase auswählen"}, "selectType": {"message": "Typ auswählen"}, "selectedAccountMismatch": {"message": "Anderes Konto ausgewählt"}, "selectingAllWillAllow": {"message": "<PERSON>n <PERSON> alle ausw<PERSON>hlen, erlauben <PERSON> dieser Seite, alle Ihre aktuellen Konten einzu<PERSON>hen. <PERSON><PERSON><PERSON>, dass Si<PERSON> dieser Seite vertrauen."}, "send": {"message": "Senden"}, "sendBugReport": {"message": "Übermitteln Sie uns einen Fehlerbericht."}, "sendNoContactsConversionText": {"message": "klicken <PERSON> hier"}, "sendNoContactsDescription": {"message": "Kontakte ermöglich Ihnen, Transaktionen sicher und mehrfach an ein anderes Konto zu senden. Um einen Kontakt zu erstellen, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "Sie haben noch keine Kontakte"}, "sendSelectReceiveAsset": {"message": "<PERSON><PERSON><PERSON><PERSON> Sie das zu empfangende Asset"}, "sendSelectSendAsset": {"message": "<PERSON><PERSON><PERSON><PERSON> Sie das zu sendende Asset"}, "sendSpecifiedTokens": {"message": "$1 senden", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "<PERSON>n <PERSON> auf diese Schaltfläche klicken, wird Ihre Swap-Transaktion sofort eingeleitet. <PERSON><PERSON> fort<PERSON>hren, überprüfen Sie bitte Ihre Transaktionsdetails."}, "sendTokenAsToken": {"message": "$1 als $2 senden", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Senden von $1"}, "sendingDisabled": {"message": "Das Senden von ERC-1155 NFT-Assets wird noch nicht unterstützt."}, "sendingNativeAsset": {"message": "$1 senden", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Warnhinweis: Sie sind im Begriff, an einen Token-Contract zu senden, und dies könnte zu einem Verlust Ihrer Gelder führen. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Sepolia-Testnetzwerk"}, "setApprovalForAll": {"message": "Erlaubnis für alle erteilen"}, "setApprovalForAllRedesignedTitle": {"message": "Auszahlungsanfrage"}, "setApprovalForAllTitle": {"message": "$1 ohne Ausgabenlimit genehmigen", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Konto-Snap hinzufügen"}, "settings": {"message": "Einstellungen"}, "settingsSearchMatchingNotFound": {"message": "<PERSON><PERSON> passenden Ergebnisse gefunden."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Signatur- und Transaktionsanfragen"}, "show": {"message": "Zeigen"}, "showAccount": {"message": "Konto anzeigen"}, "showAdvancedDetails": {"message": "Erweiterte Details anzeigen"}, "showExtensionInFullSizeView": {"message": "Erweiterung in Vollbildansicht anzeigen"}, "showExtensionInFullSizeViewDescription": {"message": "Schalten Sie dies ein, um die Vollbildansicht als Standard einzustellen, sobald Sie auf das Erweiterungssymbol klicken."}, "showFiatConversionInTestnets": {"message": "Umrechnung auf Testnets anzeigen"}, "showFiatConversionInTestnetsDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> dies aus, um die Fiat-Umrechnung in Testnetzwerken anzuzeigen."}, "showHexData": {"message": "Hexdaten anzeigen"}, "showHexDataDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> dies aus, um das Hex-Datenfeld auf dem Sendebildschirm anzuzeigen."}, "showLess": {"message": "<PERSON><PERSON> anzeigen"}, "showMore": {"message": "<PERSON><PERSON> anzeigen"}, "showNativeTokenAsMainBalance": {"message": "Natives <PERSON><PERSON> als <PERSON>aldo anzeigen"}, "showNft": {"message": "NFT anzeigen"}, "showPermissions": {"message": "Genehmigungen anzeigen"}, "showPrivateKey": {"message": "Privaten Schlüssel anzeigen"}, "showSRP": {"message": "Geheime Wiederherstellungsphrase anzeigen"}, "showTestnetNetworks": {"message": "Test-Netzwerke anzeigen"}, "showTestnetNetworksDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> dies, um Testnetzwerke in der Netzwerkliste anzuzeigen."}, "sign": {"message": "Unterzeichnen"}, "signatureRequest": {"message": "Signaturanfrage"}, "signature_decoding_bid_nft_tooltip": {"message": "Der NFT wird in Ihrer Wallet angezeigt, sobald das Gebot akzeptiert wird."}, "signature_decoding_list_nft_tooltip": {"message": "Erwarten Sie Änderungen erst, wenn jemand Ihre NFTs kauft."}, "signed": {"message": "Unterzeichnet"}, "signing": {"message": "Signieren"}, "signingInWith": {"message": "Anmelden mit"}, "signingWith": {"message": "Unterzeichnung mit"}, "simulationApproveHeading": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "<PERSON>e erteilen einer anderen Person die Genehmigung, N<PERSON>s von Ihrem Konto abzuheben."}, "simulationDetailsERC20ApproveDesc": {"message": "Sie erteilen einer anderen Person die Genehmigung, diesen Bet<PERSON> von Ihrem Konto auszugeben."}, "simulationDetailsFiatNotAvailable": {"message": "Nicht verfügbar"}, "simulationDetailsIncomingHeading": {"message": "<PERSON>e erhalten"}, "simulationDetailsNoChanges": {"message": "<PERSON><PERSON>"}, "simulationDetailsOutgoingHeading": {"message": "<PERSON><PERSON> senden"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "<PERSON><PERSON> entz<PERSON>hen einer anderen Person die Genehmigung, N<PERSON><PERSON> von Ihrem Konto abzuheben."}, "simulationDetailsSetApprovalForAllDesc": {"message": "<PERSON>e erteilen einer anderen Person die Genehmigung, N<PERSON>s von Ihrem Konto abzuheben."}, "simulationDetailsTitle": {"message": "Geschätzte Änderungen"}, "simulationDetailsTitleTooltip": {"message": "Die geschätzten Änderungen sind das, was passieren könnte, wenn Sie diese Transaktion durchführen. Dies ist nur eine Prognostizierung, keine Garantie."}, "simulationDetailsTotalFiat": {"message": "Gesamt = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Diese Transaktion wird wahrscheinlich scheitern"}, "simulationDetailsUnavailable": {"message": "Nicht verfügbar"}, "simulationErrorMessageV2": {"message": "Wir konnten das Gas nicht schätzen. Es könnte einen Fehler im Contract geben und diese Transaktion könnte fehlschlagen."}, "simulationsSettingDescription": {"message": "Schalten Sie diese Funktion ein, um die Saldoänderungen von Transaktionen und Unterschriften abzuschätzen, bevor <PERSON>e sie bestätigen. Dies ist keine Garantie für das endgültige Ergebnis. $1"}, "simulationsSettingSubHeader": {"message": "Geschätzte Saldoänderungen"}, "singleNetwork": {"message": "1 Netzwerk"}, "siweIssued": {"message": "Ausgestellt"}, "siweNetwork": {"message": "Netzwerk"}, "siweRequestId": {"message": "Anfrage-ID"}, "siweResources": {"message": "Ressourcen"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "Kontosicherheit überspringen?"}, "skipAccountSecurityDetails": {"message": "Mir ist klar, dass ich meine Konten und alle dazugehörigen Assets verlieren kann, solange ich keine Sicherungskopie meiner geheimen Wiederherstellungsphrase erstelle."}, "slideBridgeDescription": {"message": "Bewegen Sie sich über 9 Chains, und zwar in Ihrer Wallet"}, "slideBridgeTitle": {"message": "Bereit zum Bridgen?"}, "slideCashOutDescription": {"message": "Verkaufen Sie Ihre Krypto gegen Bargeld"}, "slideCashOutTitle": {"message": "Bargeldauszahlung mit MetaMask"}, "slideDebitCardDescription": {"message": "In ausgewählten Regionen verfügbar"}, "slideDebitCardTitle": {"message": "MetaMask-Debitkarte"}, "slideFundWalletDescription": {"message": "Token hinzufügen oder übertragen, um loszulegen"}, "slideFundWalletTitle": {"message": "Überweisen Sie Guthaben auf Ihre Wallet"}, "slideMultiSrpDescription": {"message": "Mehrere Wallets in MetaMask importieren und verwenden"}, "slideMultiSrpTitle": {"message": "Mehrere geheime Wiederherstellungsphrasen hinzufügen"}, "slideRemoteModeDescription": {"message": "Verwenden Sie Ihr Cold Wallet kabellos"}, "slideRemoteModeTitle": {"message": "Cold Storage, schneller Zugriff"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON>, intelligentere Funktionen"}, "slideSmartAccountUpgradeTitle": {"message": "Beginnen Sie mit der Nutzung von Smart-Konten"}, "slideSolanaDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ein <PERSON>ana-<PERSON><PERSON>, um loszulegen"}, "slideSolanaTitle": {"message": "<PERSON><PERSON> wird nun unterstützt"}, "slideSweepStakeDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> gleich ein NFT für eine Gewinnchance"}, "slideSweepStakeTitle": {"message": "Nehmen Sie am $5000-USDC-Giveaway teil!"}, "smartAccountAccept": {"message": "Smart-<PERSON><PERSON> verwenden"}, "smartAccountBetterTransaction": {"message": "Schnellere Transaktionen, niedrigere Gebühren"}, "smartAccountBetterTransactionDescription": {"message": "Sparen Sie Zeit und Geld, indem Sie Transaktionen gemeinsam abwickeln."}, "smartAccountFeaturesDescription": {"message": "Behalten Sie die gleiche Kontoadresse. Sie können jederzeit zurückwechseln."}, "smartAccountLabel": {"message": "Smart-<PERSON><PERSON>"}, "smartAccountPayToken": {"message": "Bezahlen Sie jederzeit mit jedem Token"}, "smartAccountPayTokenDescription": {"message": "Verwenden Sie die Tokens, die Si<PERSON> bereits haben, um Netzwerkgebühren zu begleichen."}, "smartAccountReject": {"message": "Smart-<PERSON><PERSON> nicht verwenden"}, "smartAccountRequestFor": {"message": "Anfrage für"}, "smartAccountSameAccount": {"message": "Dassel<PERSON> Konto, intelligentere Funktionen."}, "smartAccountSplashTitle": {"message": "Smart-<PERSON><PERSON> verwenden?"}, "smartAccountUpgradeBannerDescription": {"message": "Dieselbe Adresse. Intelligentere Funktionen."}, "smartAccountUpgradeBannerTitle": {"message": "<PERSON>um Smart-<PERSON><PERSON> we<PERSON>n"}, "smartContracts": {"message": "Smart Contracts"}, "smartSwapsErrorNotEnoughFunds": {"message": "Nicht genügend Gelder für einen Smart Swap."}, "smartSwapsErrorUnavailable": {"message": "Smart Swaps sind vorrübergehend nicht verfügbar."}, "smartTransactionCancelled": {"message": "Ihre Transaktion wurde storniert"}, "smartTransactionCancelledDescription": {"message": "Ihre Transaktion konnte nicht abgeschlossen werden und wurde daher storniert, damit Sie keine unnötigen Gas-Gebühren zahlen müssen."}, "smartTransactionError": {"message": "Ihre Transaktion schlug fehl"}, "smartTransactionErrorDescription": {"message": "Plötzliche Marktveränderungen können zu Ausfällen führen. Wenn das Problem weiterhin besteht, wenden Si<PERSON> sich an den MetaMask-Kundensupport."}, "smartTransactionPending": {"message": "Ihre Transaktion wurde übermittelt"}, "smartTransactionSuccess": {"message": "Ihre Transaktion ist abgeschlossen"}, "smartTransactions": {"message": "Smart Transactions"}, "smartTransactionsEnabledDescription": {"message": " und MEV-Schutz. Nun standardmäßig aktiv."}, "smartTransactionsEnabledLink": {"message": "Höhere Erfolgsraten"}, "smartTransactionsEnabledTitle": {"message": "Transaktionen sind jetzt smarter"}, "snapAccountCreated": {"message": "<PERSON><PERSON> erste<PERSON>t"}, "snapAccountCreatedDescription": {"message": "Ihr neues Konto ist jetzt einsatzbereit!"}, "snapAccountCreationFailed": {"message": "Kontoerstellung fehlgeschlagen"}, "snapAccountCreationFailedDescription": {"message": "$1 hat es nicht geschafft, ein Konto für Sie zu erstellen.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Unterzeichnen beenden"}, "snapAccountRedirectSiteDescription": {"message": "Befolgen Sie die Anweisungen von $1"}, "snapAccountRemovalFailed": {"message": "Kontoentfernung fehlgeschlagen"}, "snapAccountRemovalFailedDescription": {"message": "$1 hat es nicht geschafft, dieses Konto für Sie zu löschen.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "<PERSON><PERSON> wurde entfernt"}, "snapAccountRemovedDescription": {"message": "<PERSON><PERSON> wird in MetaMask nicht mehr zur Nutzung zur Verfügung stehen."}, "snapAccounts": {"message": "Konto-Snaps"}, "snapAccountsDescription": {"message": "<PERSON>s Dritter kontrollierte Konten."}, "snapConnectTo": {"message": "Mit $1 verbinden", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Lassen Sie $1 automatisch und ohne Ihre Zustimmung mit $2 verbinden.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 möchte $2 verwenden", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Webseite"}, "snapHomeMenu": {"message": "Snap-Startmenü"}, "snapInstallRequest": {"message": "Durch die Installation von $1 erhält es die folgenden Berechtigungen.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Installation ist abgeschlossen"}, "snapInstallWarningCheck": {"message": "$1 möchte die Genehmigung, Folgendes zu tun:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Seien Sie vorsichtig"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Erlauben Sie $1, Ihre öffentlichen Schlüssel (und Adressen) einzusehen. Damit wird keine Kontrolle über Konten oder Assets gewährt.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Erlauben Sie $1 Snap, Konten und Assets in dem/den gewünschten Netzwerk(en) zu verwalten. Diese Konten werden unter Verwendung Ihrer geheimen Wiederherstellungsphrase abgeleitet und gesichert (ohne sie preiszugeben). Mit der Fähigkeit, Schlüssel abzuleiten, kann $1 eine Vielzahl von Blockchain-Protokollen über Ethereum (EVMs) hinaus unterstützen.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "$1-<PERSON><PERSON><PERSON> verwalten", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "<PERSON>hen Sie Ihren öffentlichen Schlüssel für $1 ein", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 konnte nicht installiert werden.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Installation fehlgeschlagen", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "<PERSON><PERSON>"}, "snapResultSuccess": {"message": "Erfolg"}, "snapResultSuccessDescription": {"message": "$1 ist einsatzbereit"}, "snapUIAssetSelectorTitle": {"message": "<PERSON><PERSON> au<PERSON>en"}, "snapUpdateAlertDescription": {"message": "Holen Sie sich die neueste Version von $1\n", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Update verfügbar"}, "snapUpdateErrorDescription": {"message": "$1 konnte nicht aktualisiert werden.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "Update fehlgeschlagen", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Durch die Aktualisierung von $1 erhält es die folgenden Berechtigungen.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Update abgeschlossen"}, "snapUrlIsBlocked": {"message": "Dieser Snap hat vor, Sie auf eine gesperrte Seite zu bringen. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps wurden verbunden"}, "snapsNoInsight": {"message": "<PERSON><PERSON> möglich"}, "snapsPrivacyWarningFirstMessage": {"message": "Sie erkennen an, dass es sich – sofern nicht anders angegeben – bei jedem von Ihnen installierten Snap um einen Drittanbieter-Service handelt, gem<PERSON>ß der in Consensys $1 genannten Definition. Ihre Nutzung von Drittanbieter-Services unterliegt separaten Bedingungen, die vom Anbieter des jeweiligen Drittanbieter-Service festgelegt werden. Consensys empfiehlt keiner bestimmten Person aus irgendeinem bestimmten Grund die Verwendung eines Snaps. Sie nehmen Zugriff auf, verlassen sich auf und verwenden die Dienste Dritter auf eigenes Risiko. Consensys lehnt jede Verantwortung und Haftung für Verluste ab, die sich aus Ihrer Nutzung von Drittanbieter-Diensten ergeben.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "<PERSON><PERSON> Daten, die Sie mit Drittanbieterdiensten teilen, werden direkt von diesen Drittanbieterdiensten im Einklang mit deren Datenschutzerklärung erfasst. <PERSON>ür weitere Informationen lesen Sie bitte die jeweiligen Datenschutzerklärungen.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys hat keinen Zugriff auf die Informationen, die Sie an den Drittanbieter-Service weitergeben.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Snap-Einstellungen"}, "snapsTermsOfUse": {"message": "Nutzungsbedingungen"}, "snapsToggle": {"message": "Ein Snap wird nur ausgeführt, wenn er aktiviert ist."}, "snapsUIError": {"message": "Kontaktieren Sie die Ersteller von $1 für weitere Unterstützung.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Diese Website fordert ein Solana-Konto."}, "solanaAccountRequired": {"message": "<PERSON>ür die Verbindung mit dieser Website ist ein Solana-Konto erforderlich."}, "solanaImportAccounts": {"message": "Solana-Konten importieren"}, "solanaImportAccountsDescription": {"message": "Importieren Sie eine geheime Wiederherstellungsphrase, um Ihr Solana-Konto von einer anderen Wallet zu migrieren."}, "solanaMoreFeaturesComingSoon": {"message": "Mehr Funktionen in Kürze"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFTs, Unterstützung für Hardware-Wallets und bald noch viel mehr."}, "solanaOnMetaMask": {"message": "<PERSON><PERSON> auf MetaMask"}, "solanaSendReceiveSwapTokens": {"message": "Token senden, empfangen und swappen"}, "solanaSendReceiveSwapTokensDescription": {"message": "Übertragen Sie Token wie SOL, USDC usw. und tätigen Sie damit Transaktionen."}, "someNetworks": {"message": "$1 Netzwerke"}, "somethingDoesntLookRight": {"message": "<PERSON><PERSON>t irgendetwas nicht in Ordnung zu sein? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Etwas ist schiefgelaufen. Versuchen Sie, die Seite neu zu laden."}, "somethingWentWrong": {"message": "Diese Se<PERSON> konnte nicht geladen werden."}, "sortBy": {"message": "Sortieren nach"}, "sortByAlphabetically": {"message": "In alphabetischer Reihenfolge (A–Z)"}, "sortByDecliningBalance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ($1 Hoch-Tief)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "<PERSON><PERSON>"}, "spamModalBlockedDescription": {"message": "Diese Seite wird für 1 Minute gesperrt."}, "spamModalBlockedTitle": {"message": "Sie haben diese Seite vorübergehend gesperrt"}, "spamModalDescription": {"message": "Wenn Si<PERSON> mit mehrfachen Anfragen überschüttet werden, können Sie die Website vorübergehend sperren."}, "spamModalTemporaryBlockButton": {"message": "Diese Seite vorübergehend sperren"}, "spamModalTitle": {"message": "Wir haben mehrere Anfragen verzeichnet"}, "speed": {"message": "Geschwindigkeit"}, "speedUp": {"message": "Beschleunigen"}, "speedUpCancellation": {"message": "Diese Stornierung beschleunigen"}, "speedUpExplanation": {"message": "Wir haben die Gas-Gebühr auf der Grundlage der aktuellen Netzbedingungen aktualisiert und um mindestens 10 % erhöht (erforderlich durch das Netzwerk)."}, "speedUpPopoverTitle": {"message": "Diese Transaktion beschleunigen"}, "speedUpTooltipText": {"message": "Neue Gas-Gebühr"}, "speedUpTransaction": {"message": "Diese Transaktion beschleunigen"}, "spendLimitInsufficient": {"message": "Ausgabenlimit unzureichend"}, "spendLimitInvalid": {"message": "Ausgabenlimit ungültig; muss eine positive Zahl sein"}, "spendLimitPermission": {"message": "Ausgabenlimit-Genehmigung"}, "spendLimitRequestedBy": {"message": "Ausgabenlimit von $1 angefordert", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Ausgabenlimit zu groß"}, "spender": {"message": "<PERSON>pender"}, "spenderTooltipDesc": {"message": "Dies ist die Adresse, an die Sie Ihre NFTs abheben können."}, "spenderTooltipERC20ApproveDesc": {"message": "Dies ist die Adresse, die Ihre Tokens in Ihrem Namen ausgeben kann."}, "spendingCap": {"message": "Ausgabenobergrenze"}, "spendingCaps": {"message": "Ausgabenobergrenzen"}, "srpInputNumberOfWords": {"message": "Ich habe eine $1-Wort-Phrase.", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Geheime Wiederherstellungsphrase $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 Konten", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "Die geheime Wiederherstellungsphrase, mit der Ihr neues Konto erstellt wird"}, "srpListSingleOrZero": {"message": "$1 Konto", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "Das Einfügen schlug fehl, weil sie mehr als 24 Wörter enthielt. Eine geheime Wiederherstellungsphrase darf maximal 24 Wörter enthalten.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Sie können Ihre vollständige geheime Wiederherstellungsphrase in ein beliebiges Feld einfügen.", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "<PERSON><PERSON><PERSON>"}, "srpSecurityQuizImgAlt": {"message": "Ein Auge mit eine<PERSON> Schlüsselloch in der Mitte und drei schwebenden Passwortfeldern"}, "srpSecurityQuizIntroduction": {"message": "Zur Enthüllung Ihrer geheimen Wiederherstellungsphrase, müssen Sie zwei Fragen richtig beantworten."}, "srpSecurityQuizQuestionOneQuestion": {"message": "<PERSON>n Si<PERSON> Ihre geheime Wiederherstellungsphrase verlieren, kann MetaMask ..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "<PERSON>hnen nicht helfen."}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "<PERSON><PERSON><PERSON>ben Sie sie auf, gravieren Sie sie in Metall ein oder bewahren Sie sie an mehreren geheimen Orten auf, damit Sie sie niemals verlieren. Sollten Sie sie verlieren, ist sie für immer weg."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Richtig! <PERSON><PERSON>d kann Ihnen dabei helfen, <PERSON>hre geheime Wiederherstellungsphrase zurückzubekommen."}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "diese für Sie zurückzubekommen."}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "<PERSON>n Sie Ihre geheime Wiederherstellungsphrase verlieren, ist diese für immer verloren. <PERSON><PERSON><PERSON> kann <PERSON>hnen da<PERSON> helfen, sie <PERSON><PERSON><PERSON>, ganz gleich, was behauptet wird."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Falsch! <PERSON><PERSON>d kann Ihnen dabei helfen, <PERSON>hre geheime Wiederherstellungsphrase zurückzubekommen."}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON><PERSON><PERSON>, sel<PERSON><PERSON> ein Support-<PERSON><PERSON><PERSON>, nach Ihrer geheimen Wiederherstellungsphrase fragen ..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "werden Sie betrogen."}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "<PERSON><PERSON>, der <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hre gemeine Wiederherstellungsphrase zu benötigen, lügt <PERSON> an. Wenn <PERSON> diese mit solchen Personen teilen, werden diese Ihre Assets stehlen."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Richtig! Es ist nie eine gute Idee, Ihre geheime Wiederherstellungsphrase mit anderen zu teilen."}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "Sie sollten sie ihnen geben."}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "<PERSON><PERSON>, der <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>hre gemeine Wiederherstellungsphrase zu benötigen, lügt <PERSON> an. Wenn <PERSON> diese mit einer solchen Person teilen, wird sie Ihre Assets stehlen."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Nein! Teilen Sie Ihre geheime Wiederherstellungsphrase mit niemandem, ni<PERSON><PERSON>."}, "srpSecurityQuizTitle": {"message": "Sicherheits-Quiz"}, "srpToggleShow": {"message": "Dieses Wort der geheimen Wiederherstellungsphrase anzeigen/verbergen", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "<PERSON><PERSON> W<PERSON> ist verborgen.", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "<PERSON><PERSON> Wort wird angezeigt.", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Stabil"}, "stableLowercase": {"message": "stabil"}, "stake": {"message": "<PERSON><PERSON><PERSON>"}, "staked": {"message": "Staked"}, "standardAccountLabel": {"message": "Standard-Konto"}, "startEarning": {"message": "<PERSON>t dem <PERSON>n beginnen"}, "stateLogError": {"message": "Fehler beim Abfragen der Statusprotokolle."}, "stateLogFileName": {"message": "MetaMask-Statusprotokolle"}, "stateLogs": {"message": "Statusprotokolle"}, "stateLogsDescription": {"message": "Die Statusprotokolle enthalten Ihre öffentlichen Kontoadressen und gesendeten Transaktionen."}, "status": {"message": "Status"}, "statusNotConnected": {"message": "Nicht verbunden"}, "statusNotConnectedAccount": {"message": "<PERSON>ine verbundenen Konten"}, "step1LatticeWallet": {"message": "Verbinden Sie Ihr Lattice1"}, "step1LatticeWalletMsg": {"message": "Sie können MetaMask mit Ihrem Lattice1-Gerät verbinden, sobald dieses eingerichtet und online ist. Entsperren Sie Ihr Gerät und halten Sie Ihre Geräte-ID bereit.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Ledger-<PERSON><PERSON> herunt<PERSON>n"}, "step1LedgerWalletMsg": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ein<PERSON>ten und Ihr Passwort eingeben, um $1 freizuschalten.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Verbinden Sie Ihre Trezor-Wallet."}, "step1TrezorWalletMsg": {"message": "Schließen Sie Ihre Trezor-Wallet direkt an Ihren Computer an und entsperren Si<PERSON> sie. <PERSON><PERSON><PERSON>, dass Sie die richtige Passphrase verwenden.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Verbinden Sie Ihre Ledger-Wallet."}, "step2LedgerWalletMsg": {"message": "Schließen Sie Ihre Ledger-Wallet direkt an Ihren Computer an, entsperren Si<PERSON> sie und öffnen Sie die Ethereum-App.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Erhalten Sie diese Meldung immer noch?"}, "strong": {"message": "<PERSON>"}, "stxCancelled": {"message": "<PERSON>wap wäre gesche<PERSON>t"}, "stxCancelledDescription": {"message": "Ihre Transaktion wäre fehlgeschlagen und wurde storniert, um Si<PERSON> vor unnötigen Gas-Gebühren zu schützen."}, "stxCancelledSubDescription": {"message": "Versuchen Sie Ihren Swap erneut. Wir werden hier sein, um Si<PERSON> beim nächsten Mal vor ähnlichen Risiken zu schützen."}, "stxFailure": {"message": "Swap fehlgeschlagen"}, "stxFailureDescription": {"message": "Plötzliche Marktveränderungen können zu Ausfällen führen. Wenn das Problem weiterhin besteht, wenden <PERSON>e sich bitte an $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "Schalten Sie Smart Transactions für zuverlässigere und sicherere Transaktionen in unterstützten Netzwerken ein. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Ihr Swap wird privat abgesendet ..."}, "stxPendingPubliclySubmittingSwap": {"message": "Ihr Swap wird öffentlich abgesendet ..."}, "stxSuccess": {"message": "Swap abgeschlossen!"}, "stxSuccessDescription": {"message": "Ihr $1 ist jetzt verfügbar.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "Swap abgeschlossen in <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "<PERSON>s wird versucht, Ihre Transaktion zu stornieren ..."}, "stxUnknown": {"message": "Status unbekannt"}, "stxUnknownDescription": {"message": "Eine Transaktion war erfolgreich, aber wir sind uns nicht sicher, um welche es sich handelt. Dies kann darauf zurückzuführen sein, dass eine andere Transaktion übermittelt wurde, während dieser Swap bearbeitet wurde."}, "stxUserCancelled": {"message": "<PERSON><PERSON><PERSON> stor<PERSON>t"}, "stxUserCancelledDescription": {"message": "Ihre Transaktion wurde storniert und Sie haben keine unnötigen Gas-Gebühren bezahlt."}, "submit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "submitted": {"message": "Abgesendet"}, "suggestedBySnap": {"message": "Vorgeschlagen von $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Empfohlenes Währungssymbol:"}, "suggestedTokenName": {"message": "Vorgeschlagener Name:"}, "supplied": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "support": {"message": "Support"}, "supportCenter": {"message": "Besuchen Sie unser Support Center."}, "supportMultiRpcInformation": {"message": "Wir unterstützen nun mehrere RPCs für ein einzelnes Netzwerk. Ihr aktuellster RPC (ferngesteuerter Prozeduraufruf) wurde standardmäßig ausgewählt, um widersprüchliche Informationen aufzulösen."}, "surveyConversion": {"message": "<PERSON><PERSON><PERSON> an unserer Umfrage teil"}, "surveyTitle": {"message": "Gestalten Sie die Zukunft von MetaMask"}, "swap": {"message": "<PERSON><PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "Slippage anpassen"}, "swapAggregator": {"message": "Aggregator"}, "swapAllowSwappingOf": {"message": "Swapping von $1 zulassen", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "Garantierter Betrag"}, "swapAmountReceivedInfo": {"message": "Dies ist der Mindestbetrag, den Sie empfangen werden. Je nach Slippage können Si<PERSON> auch mehr empfangen."}, "swapAndSend": {"message": "Swappen und Senden"}, "swapAnyway": {"message": "Swap trotzdem ausführen"}, "swapApproval": {"message": "$1 für Swaps genehmigen", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Sie benötigen $1 mehr $2, um diesen Swap abzuschließen", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Sind Sie noch da?"}, "swapAreYouStillThereDescription": {"message": "Wir sind bereit, <PERSON>hnen die neuesten Angebote zu zeigen, wenn Si<PERSON> fortfahren möchten."}, "swapConfirmWithHwWallet": {"message": "Mit Ihrer Hardware-Wallet bestätigen"}, "swapContinueSwapping": {"message": "<PERSON><PERSON>"}, "swapContractDataDisabledErrorDescription": {"message": "Gehen Sie in der Ethereum-App auf Ihrem Ledger auf „Einstellungen“ und lassen Sie Contract-Daten zu. Versuchen Sie dann Ihren Swap erneut."}, "swapContractDataDisabledErrorTitle": {"message": "Contract-Daten sind in Ihrem Ledger nicht aktiviert."}, "swapCustom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "swapDecentralizedExchange": {"message": "Dezentralisierter Austausch"}, "swapDirectContract": {"message": "Direkter Contract"}, "swapEditLimit": {"message": "<PERSON>it bearbeiten"}, "swapEnableDescription": {"message": "Dies ist erforderlich und gibt MetaMask die Genehmigung, Ihren $1 zu swappen.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Das macht $1 für Swapping.", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Einen Betrag eingeben"}, "swapEstimatedNetworkFees": {"message": "Geschätzte Netzwerkgebühren"}, "swapEstimatedNetworkFeesInfo": {"message": "Dies ist eine Schätzung der Netzwerkgebühr, die für den Abschluss Ihres Swaps verwendet wird. Der tatsächliche Betrag kann sich je nach Netzwerkbedingungen ändern."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Transaktionsfehler kommen vor und wir sind hier, um zu helfen. Wenn das Problem weiterhin besteht, können Sie unseren Kundensupport unter $1 erreichen, um weitere Hilfe zu erhalten.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "Swap fehlgeschlagen"}, "swapFetchingQuote": {"message": "<PERSON><PERSON><PERSON> wird e<PERSON>holt"}, "swapFetchingQuoteNofN": {"message": "Angebot $1 von $2 ", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "<PERSON><PERSON><PERSON> einholen..."}, "swapFetchingQuotesErrorDescription": {"message": "Hmmm... etwas ist schiefgelaufen. Versuchen Sie es erneut, oder wenden Si<PERSON> sich an den Kundensupport, wenn der Fehler weiterhin besteht."}, "swapFetchingQuotesErrorTitle": {"message": "Fehler beim Abrufen der Preisangaben"}, "swapFromTo": {"message": "Swap von $1 auf $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Die Gas-Gebühren werden geschätzt und werden aufgrund der Komplexität des Netzwerk-Traffics und der Transaktionskomplexität schwanken."}, "swapGasFeesExplanation": {"message": "MetaMask verdient kein Geld mit Gas-Gebühren. Bei diesen Gebühren handelt es sich um Schätzwerte, die sich je nach Auslastung des Netzwerks und der Komplexität einer Transaktion ändern können. Erfahren Sie mehr über $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "hier", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Erfahren Sie mehr über Gasgebühren"}, "swapGasFeesSplit": {"message": "Die Gas-Gebühren auf dem vorherigen Bildschirm werden auf diese beiden Transaktionen aufgeteilt."}, "swapGasFeesSummary": {"message": "Gasgebühren werden an Krypto-Miner gezahlt, die Transaktionen im $1-Netzwerk verarbeiten. MetaMask profitiert nicht von den Gasgebühren.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "In diesem Angebot sind die Gas-Gebühren enthalten, indem der gesendete bzw. empfangene Tokenbetrag entsprechend angepasst wird. Sie können ETH in einer separaten Transaktion auf Ihrer Aktivitätsliste erhalten."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Erfahren Sie mehr über Gas-Gebühren"}, "swapHighSlippage": {"message": "Hohe Slippage"}, "swapIncludesGasAndMetaMaskFee": {"message": "Enthält Gas und eine MetaMask-Gebühr von $1%", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Enthält eine MetaMask-Gebühr von $1%.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Angebot umfasst $1% MetaMask-Gebühr", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "Enthält eine MetaMask-Gebühr von $1% bis $2.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Mehr über Swaps erfahren"}, "swapLiquiditySourceInfo": {"message": "Wir durchsuchen mehrere Liquiditätsquellen (Börsen, Aggregatoren und professionelle Market Maker), um Wechselkurse und Netzwerkgebühren zu vergleichen."}, "swapLowSlippage": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapMaxSlippage": {"message": "<PERSON><PERSON>"}, "swapMetaMaskFee": {"message": "MetaMask-Gebühr"}, "swapMetaMaskFeeDescription": {"message": "Die Gebühr von $1% ist automatisch in diesem Angebot enthalten. Sie zahlen sie als Gegenleistung für eine Lizenz zur Nutzung der Software von MetaMask zur Aggregation von Liquiditätsanbieterinformationen.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 Angebote.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Neue Angebote in $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "<PERSON><PERSON> verfügbar, die mit $1 übereinstimmen.", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Ihre $1 werden Ihrem Konto gutgeschrieben, sobald diese Transaktion abgeschlossen ist.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "<PERSON><PERSON> sind dabei, $1 $2 (~$3) gegen $4 $5 (~$6) zu swappen.", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Preisdiffer<PERSON><PERSON> von ~$1%", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "Die Auswirkungen auf den Preis konnten aufgrund fehlender Marktpreisdaten nicht ermittelt werden. Bitte bestätigen Si<PERSON> vor dem Tausch, dass Sie mit der Menge der Tokens, die Si<PERSON> erhalten werden, einverstanden sind."}, "swapPriceUnavailableTitle": {"message": "Überprüfen Sie Ihren Kurs, bevor <PERSON> fortfahren."}, "swapProcessing": {"message": "Wird verarbei<PERSON>t"}, "swapQuoteDetails": {"message": "Kursdetails"}, "swapQuoteNofM": {"message": "$1 von $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Ang<PERSON><PERSON><PERSON><PERSON>"}, "swapQuotesExpiredErrorDescription": {"message": "Bitte fordern Sie neue Angebote an, um die aktuellen Kurse zu erhalten."}, "swapQuotesExpiredErrorTitle": {"message": "Angebote-Timeout"}, "swapQuotesNotAvailableDescription": {"message": "Diese Handelsroute ist derzeit nicht verfügbar. Versuchen Sie, den Betrag, das Netzwerk oder den Token zu ändern, und wir werden die bestmögliche Option suchen."}, "swapQuotesNotAvailableErrorDescription": {"message": "Versuchen Sie die Menge oder Slippage Einstellungen anzupassen und versuchen Sie es erneut."}, "swapQuotesNotAvailableErrorTitle": {"message": "<PERSON><PERSON> ve<PERSON>"}, "swapRate": {"message": "<PERSON><PERSON>"}, "swapReceiving": {"message": "Empfangen"}, "swapReceivingInfoTooltip": {"message": "Dies ist eine Schätzung. Der genaue Betrag hängt von der Slippage ab."}, "swapRequestForQuotation": {"message": "Angebotsanfrage"}, "swapSelect": {"message": "Auswählen"}, "swapSelectAQuote": {"message": "<PERSON><PERSON> au<PERSON>wählen"}, "swapSelectAToken": {"message": "Token auswählen"}, "swapSelectQuotePopoverDescription": {"message": "Unten sind alle Kurse aus verschiedenen Liquiditätsquellen zusammengefasst."}, "swapSelectToken": {"message": "Token auswählen"}, "swapShowLatestQuotes": {"message": "Neueste Angebote anzeigen"}, "swapSlippageHighDescription": {"message": "Die eingegebene Slippage ($1%) wird als sehr hoch angesehen und kann zu einem schlechten Kurs führen.", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "Hohe Slippage"}, "swapSlippageLowDescription": {"message": "Ein so niedriger Wert ($1%) kann zu einem fehlgeschlagenen Swap führen.", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapSlippageNegativeDescription": {"message": "Slippage muss größer oder gleich <PERSON> sein"}, "swapSlippageNegativeTitle": {"message": "Zum Fortfahren Slippage erhöhen"}, "swapSlippageOverLimitDescription": {"message": "Slippage-Tolleranz muss 15 % oder weniger betragen. Alles darüber resultiert in einem schlechten Kurs."}, "swapSlippageOverLimitTitle": {"message": "<PERSON><PERSON> hohe Slippage"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "<PERSON>n sich der Kurs zwischen der Aufgabe Ihrer Order und der Bestätigung ändert, nennt man das „Slippage”. Ihr Swap wird automatisch storniert, wenn die Abweichung die von Ihnen eingestellte „Abweichungstoleranz” überschreitet."}, "swapSlippageZeroDescription": {"message": "<PERSON><PERSON> gibt weniger Anbieter mit Null-Slippage, was in einem weniger konkurrenzfähigen Angebot resultieren könne."}, "swapSlippageZeroTitle": {"message": "Suche nach Null-Slippage-Anbietern"}, "swapSource": {"message": "Liquiditätsquelle"}, "swapSuggested": {"message": "Swap vorgeschlagen"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Swaps sind komplexe und zeitkritische Transaktionen. Wir empfehlen diese Gas-Gebühr für ein gutes Gleichgewicht zwischen Kosten und Vertrauen in einen erfolgreichen Swap."}, "swapSwapFrom": {"message": "<PERSON><PERSON><PERSON> von"}, "swapSwapSwitch": {"message": "Token-Reihenfolge wechseln"}, "swapSwapTo": {"message": "<PERSON><PERSON><PERSON> zu"}, "swapToConfirmWithHwWallet": {"message": "zur Bestätigung mit Ihrer Hardware-Wallet"}, "swapTokenAddedManuallyDescription": {"message": "Überprüfen Sie dieses Token auf $1 und stellen Si<PERSON> sicher, dass es sich um das Token handelt, das Si<PERSON> handeln möchten.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token manuell hinzugefügt"}, "swapTokenAvailable": {"message": "Ihr $1 wurde Ihrem Konto hinzugefügt.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "Wir konnten Ihr $1-Guthaben nicht abrufen.", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "In diesem Netzwerk ist kein Token-Tausch verfügbar"}, "swapTokenToToken": {"message": "$1 mit $2 tauschen", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 wurde nur auf 1 Quelle bestätigt. <PERSON><PERSON><PERSON> in Betracht, es vor dem Fortfahren auf $2 zu bestätigen.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Möglicherweise unglaubwürdiges Token"}, "swapTokenVerifiedSources": {"message": "Bestätigt durch $1 Quellen. Auf $2 verifiziert.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 erlaubt bis zu $2 Dezimalstellen", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transaktion vollständig"}, "swapTwoTransactions": {"message": "2 Transaktionen"}, "swapUnknown": {"message": "Unbekannt"}, "swapZeroSlippage": {"message": "0 % Slippage"}, "swapsMaxSlippage": {"message": "Slippage-Toleranz"}, "swapsNotEnoughToken": {"message": "Nicht genügend $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "In Aktivität anzeigen"}, "switch": {"message": "Wechseln"}, "switchEthereumChainConfirmationDescription": {"message": "<PERSON><PERSON><PERSON> wird das ausgewählte Netzwerk innerhalb von MetaMask auf ein zuvor hinzugefügtes Netzwerk umgeschaltet:"}, "switchEthereumChainConfirmationTitle": {"message": "Dieser Seite das Wechseln eines Netzwerks erlauben?"}, "switchInputCurrency": {"message": "Eingangswährung wechseln"}, "switchNetwork": {"message": "Netzwerk wechseln"}, "switchNetworks": {"message": "Netzwerk wechseln"}, "switchToNetwork": {"message": "Zu $1 wechseln", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "<PERSON><PERSON> diesem <PERSON> wechseln"}, "switchedNetworkToastDecline": {"message": "Nicht mehr anzeigen"}, "switchedNetworkToastMessage": {"message": "$1 ist jetzt aktiv bei $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Sie verwenden jetzt $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "Das Wechseln der Netzwerke wird alle ausstehenden Bestätigungen stornieren."}, "symbol": {"message": "Symbol"}, "symbolBetweenZeroTwelve": {"message": "Das Symbol darf maximal 11 Zeichen lang sein."}, "tenPercentIncreased": {"message": "10 % Erhöhung"}, "terms": {"message": "Nutzungsbedingungen"}, "termsOfService": {"message": "Nutzungsbedingungen"}, "termsOfUseAgreeText": {"message": " Ich akzeptiere die Nutzungsbedingungen, die meine Verwendung von <PERSON>a<PERSON>, einsch<PERSON>ß<PERSON> aller seiner Funktionen, betreffen"}, "termsOfUseFooterText": {"message": "<PERSON>te scrollen, um alle Sektionen zu lesen."}, "termsOfUseTitle": {"message": "Unsere Nutzungsbedingungen wurden aktualisiert."}, "testNetworks": {"message": "Test-Netzwerke"}, "testnets": {"message": "Testnets"}, "theme": {"message": "Motiv"}, "themeDescription": {"message": "Wählen Sie Ihr bevorzugtes MetaMask-Motiv aus."}, "thirdPartySoftware": {"message": "Mitteilung bzgl. Drittanbieter-Software", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Zeit"}, "tipsForUsingAWallet": {"message": "Tipps zur Verwendung einer Wallet"}, "tipsForUsingAWalletDescription": {"message": "Das Hinzufügen von Tokens eröffnet weitere Möglichkeiten zur Nutzung von web3."}, "to": {"message": "An"}, "toAddress": {"message": "An: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Wir nutzen die Dienste von 4byte.directory und Sourcify, um Transaktionsdaten zu dekodieren und lesbarer anzuzeigen. Dadurch können Sie das Ergebnis ausstehender und vergangener Transaktionen leichter einsehen, allerdings kann dies zur Weitergabe Ihrer IP-Adresse führen."}, "token": {"message": "Token"}, "tokenAddress": {"message": "Token-<PERSON><PERSON><PERSON>"}, "tokenAlreadyAdded": {"message": "Token wurde bereits hinzugefügt."}, "tokenAutoDetection": {"message": "Automatische Token-Erkennung"}, "tokenContractAddress": {"message": "Token-Contract-<PERSON><PERSON><PERSON>"}, "tokenDecimal": {"message": "Token-Dezimale"}, "tokenDecimalFetchFailed": {"message": "Tokendezimal erforderlich. Finden Sie es auf: $1"}, "tokenDetails": {"message": "Token-Details"}, "tokenFoundTitle": {"message": "1 neues Token gefunden"}, "tokenId": {"message": "Token-ID"}, "tokenList": {"message": "Token-Listen"}, "tokenMarketplace": {"message": "Token-Marktplatz"}, "tokenScamSecurityRisk": {"message": "Token-Betrügereien und Sicherheitsrisiken"}, "tokenStandard": {"message": "Token-Standard"}, "tokenSymbol": {"message": "Tokensymbol"}, "tokens": {"message": "Tokens"}, "tokensFoundTitle": {"message": "$1 neue Tokens gefunden", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Tokens in der Sammlung"}, "tooltipApproveButton": {"message": "<PERSON>ch verstehe"}, "tooltipSatusConnected": {"message": "verbunden"}, "tooltipSatusConnectedUpperCase": {"message": "Verbunden"}, "tooltipSatusNotConnected": {"message": "nicht verbunden"}, "total": {"message": "Gesamt"}, "totalVolume": {"message": "Gesamtvolumen"}, "transaction": {"message": "Transaktion"}, "transactionCancelAttempted": {"message": "Transaktionsstornierung versucht mit Gas-Gebühr von $1 bei $2."}, "transactionCancelSuccess": {"message": "Transaktion bei $2 erfolgreich storniert."}, "transactionConfirmed": {"message": "Transaktion bei $2 bestätigt."}, "transactionCreated": {"message": "Transaktion mit einem Wert von $1 bei $2 erstellt."}, "transactionDataFunction": {"message": "Funktion"}, "transactionDetailGasHeading": {"message": "Voraussichtliche Gas-Gebühr"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Betrag + Gebühren"}, "transactionDropped": {"message": "Transaktion bei $2 eingestellt."}, "transactionError": {"message": "Transaktionsfehler. Fehler in Contract-Code eingefügt."}, "transactionErrorNoContract": {"message": "Das Abrufen einer Funktion bei einer Nicht-Contract-Adresse wird versucht."}, "transactionErrored": {"message": "Bei der Transaktion ist ein Fehler aufgetreten."}, "transactionFlowNetwork": {"message": "Netzwerk"}, "transactionHistoryBaseFee": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Gesamte L1 Gas-Gebühr"}, "transactionHistoryL2GasLimitLabel": {"message": "L2 Gas-Limit"}, "transactionHistoryL2GasPriceLabel": {"message": "L2 Gas-Preis"}, "transactionHistoryMaxFeePerGas": {"message": "Maximale Gebühr pro Gas"}, "transactionHistoryPriorityFee": {"message": "Prioritätsgebühr (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Gesamte Gas-Gebühr"}, "transactionIdLabel": {"message": "Transaktions-ID", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Diese Transaktion beinhaltet: $1."}, "transactionResubmitted": {"message": "Erneutes Absenden der Transaktion mit Erhöhung der geschätzten Gas-Gebühr auf $1 zu $2."}, "transactionSettings": {"message": "Transaktionseinstellungen"}, "transactionSubmitted": {"message": "Transaktion mit einer Gas-Gebühr von $1 bei $2 abgesendet."}, "transactionTotalGasFee": {"message": "Gesamte Gasgebühr", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Transaktion für $2 aktualisiert."}, "transactions": {"message": "Transaktionen"}, "transfer": {"message": "Übertragung"}, "transferCrypto": {"message": "Krypto überweisen"}, "transferFrom": {"message": "Übertragung von"}, "transferRequest": {"message": "Überweisungsanfrage"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Wir haben Probleme mit der Verbindung zu Ihrem Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Überprüfen Sie die Verbindungsanleitung Ihrer Hardware-Wallet und versuchen Sie es erneut.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Sollten Sie die neueste Version von Firefox verwenden, könnten Sie einem Problem begegnen, dass mit der Einstelllung der U2F-Unterstützung seitens Firefox zusammenhängt. Erfahren Sie $1, wie Sie dieses Problem beheben.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "hier", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "Wir hatten Probleme mit der Verbindung zu Ihrem $1, versuchen Sie, $2 zu überprüfen und versuchen es erneut.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "<PERSON><PERSON> von MetaMask ist ein Problem aufgetreten. Dies könnte ein vorübergehendes Problem sein. Versuchen Si<PERSON>, die Erweiterung neu zu starten."}, "tryAgain": {"message": "<PERSON><PERSON><PERSON> versuchen"}, "turnOff": {"message": "Ausschalten"}, "turnOffMetamaskNotificationsError": {"message": "<PERSON>im Deaktivieren der Benachrichtigungen ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut."}, "turnOn": {"message": "Einschalten"}, "turnOnMetamaskNotifications": {"message": "Benachrichtigungen einschalten"}, "turnOnMetamaskNotificationsButton": {"message": "Einschalten"}, "turnOnMetamaskNotificationsError": {"message": "<PERSON><PERSON> Erstellen der Benachrichtigungen ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut."}, "turnOnMetamaskNotificationsMessageFirst": {"message": "Bleiben Sie mit Benachrichtigungen auf dem Laufenden darüber, was in Ihrer Wallet passiert."}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "Benachrichtigungseinstellungen."}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "<PERSON><PERSON><PERSON><PERSON>, wie wir Ihre Privatsphäre bei der Nutzung dieser Funktion schützen."}, "turnOnMetamaskNotificationsMessageSecond": {"message": "Um Wallet-Benachrichtigungen zu nutzen, verwenden wir ein Profil, um bestimmte Einstellungen auf Ihren Geräten zu synchronisieren. $1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "Sie können die Benachrichtigungen jederzeit unter $1 ausschalten"}, "turnOnTokenDetection": {"message": "Erweiterte Token-Erkennung aktivieren"}, "tutorial": {"message": "Tutorial"}, "twelveHrTitle": {"message": "12 Std:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "<PERSON><PERSON> gene<PERSON>"}, "unexpectedBehavior": {"message": "Dieses Verhalten ist unerwartet und sollte als Fehler gemeldet werden, selbst wenn Ihre Konten korrekt wiederhergestellt wurden. Verwenden Sie den unten stehenden Link, um MetaMask einen Fehlerbericht zu senden."}, "units": {"message": "Einheiten"}, "unknown": {"message": "Unbekannt"}, "unknownCollection": {"message": "Unbenannte Sammlung"}, "unknownNetworkForKeyEntropy": {"message": "Unbekanntes Netzwerk", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Fehler: Wir konnten diesen QR-Code nicht identifizieren."}, "unlimited": {"message": "Unbegrenzt"}, "unlock": {"message": "Entsperren"}, "unpin": {"message": "<PERSON><PERSON><PERSON>"}, "unrecognizedChain": {"message": "Dieses benutzerdefinierte Netzwerk wird nicht erkannt.", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "<PERSON><PERSON> von NFT-Tokens (ERC-721) wird derzeit nicht unterstützt.", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "Der Preis dieses Tokens in USD ist äußerst volatil, was auf ein hohes Risiko hindeutet, durch Interaktion mit dem Token einen erheblichen Wert zu verlieren."}, "unstableTokenPriceTitle": {"message": "Unbeständiger Token-Preis"}, "upArrow": {"message": "Aufwärtspfeil"}, "update": {"message": "Update"}, "updateEthereumChainConfirmationDescription": {"message": "Diese Website fordert Sie zur Aktualisierung Ihrer Standard-Netzwerk-URL auf. Sie können die Standardeinstellungen und Netzwerkinformationen jederzeit ändern."}, "updateNetworkConfirmationTitle": {"message": "$1 aktualisieren", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Aktualisieren Sie Ihre Informationen oder"}, "updateRequest": {"message": "Aktualisierungsanfrage"}, "updatedRpcForNetworks": {"message": "Netzwerk-RPCs aktualisiert"}, "uploadDropFile": {"message": "Legen Sie Ihre Datei hier ab"}, "uploadFile": {"message": "<PERSON><PERSON> ho<PERSON>n"}, "urlErrorMsg": {"message": "URIs benötigen die korrekten HTTP/HTTPS Präfixe."}, "use4ByteResolution": {"message": "Smart Contracts dekodieren"}, "useMultiAccountBalanceChecker": {"message": "Kontoguthaben-An<PERSON><PERSON> sammeln"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Erhalten Sie Aktualisierungen von Kontoständen schneller, indem Sie Anfragen zum Kontostand bündeln. Auf diese Weise können wir Ihre Kontostände auf einmal abrufen, wodurch Sie schnellere Aktualisierungen erhalten und eine bessere Erfahrung machen. Wenn diese Funktion deaktiviert ist, ist es für Dritte weniger wahrscheinlich, dass sie Ihre Konten miteinander in Verbindung bringen können."}, "useNftDetection": {"message": "NFTs automatisch erkennen"}, "useNftDetectionDescriptionText": {"message": "<PERSON>sen Sie MetaMask NFTs, die <PERSON><PERSON> be<PERSON>tz<PERSON>, anhand von Drittanbieterdiensten hinzufügen. Durch die automatische Erkennung von NFTs werden Ihre IP- und Kontoadresse für diese Dienste offengelegt. Durch die Aktivierung dieser Funktion können eventuell Ihre IP- und Ethereum-Adresse miteinander in Verbindung gebracht und gefälschte, von Betrügern per Airdropping abgesetzte NFTs anzeigt werden. <PERSON><PERSON> ist möglich, Tokens manuell hinzufügen, um dieses Risiko zu vermeiden."}, "usePhishingDetection": {"message": "Phishing-<PERSON>rkennung verwenden"}, "usePhishingDetectionDescription": {"message": "<PERSON><PERSON>gt eine Warnung für Phishing-Domains, die Ethereum-Nutzer ansprechen."}, "useSafeChainsListValidation": {"message": "Überprüfung der Netzwerkangaben"}, "useSafeChainsListValidationDescription": {"message": "MetaMask verwendet einen Drittanbieter-Service namens $1, um genaue und standardisierte Netzwerkangaben anzuzeigen. Dies verringert die Wahrscheinlichkeit, dass Sie mit einem bösartigen oder falschen Netzwerk in Verbindungen treten. Wenn Sie diese Funktion benutzen, wird Ihre IP-Adresse chainid.network gegenüber offengelegt."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Die automatische Anzeige der an Ihr Konto gesendeten Tokens erfordert die Kommunikation mit Servern von Drittanbietern, um die Bilder der Tokens abzurufen. Diese Server haben Zugriff auf Ihre IP-Adresse."}, "usedByClients": {"message": "<PERSON><PERSON><PERSON><PERSON> von einer Reihe verschiedenen Kunden"}, "userName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "userOpContractDeployError": {"message": "Contract-Bereitstellung von einem Smart-Contract-Konto wird nicht unterstützt"}, "version": {"message": "Version"}, "view": {"message": "Anzeigen"}, "viewActivity": {"message": "Aktivität anzeigen"}, "viewAllQuotes": {"message": "alle Angebote anzeigen"}, "viewContact": {"message": "Kontakt anzeigen"}, "viewDetails": {"message": "Details anzeigen"}, "viewMore": {"message": "<PERSON><PERSON> anzeigen"}, "viewOnBlockExplorer": {"message": "Im Block-Explorer anzeigen"}, "viewOnCustomBlockExplorer": {"message": "Zeige $1 bei $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "$1 auf Etherscan anzeigen", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Im Explorer anzeigen"}, "viewOnOpensea": {"message": "Auf <PERSON> an<PERSON>"}, "viewSolanaAccount": {"message": "Solana-Konto anzeigen"}, "viewTransaction": {"message": "Transaktion einsehen"}, "viewinExplorer": {"message": "$1 im Explorer anzeigen", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Seite besuchen"}, "visitSupportDataConsentModalAccept": {"message": "Bestätigen"}, "visitSupportDataConsentModalDescription": {"message": "Möchten Sie Ihre MetaMask-Kennung und Ihre App-Version mit unserem Support Center teilen? Dies kann uns bei der Lösung Ihres Problems helfen, ist aber optional."}, "visitSupportDataConsentModalReject": {"message": "<PERSON>cht teilen"}, "visitSupportDataConsentModalTitle": {"message": "Gerätede<PERSON> mit dem Support teilen"}, "visitWebSite": {"message": "Besuchen Sie unsere Webseite."}, "wallet": {"message": "Wallet"}, "walletConnectionGuide": {"message": "unsere Hardware-Wallet-Verbindungsanleitung"}, "wantToAddThisNetwork": {"message": "Möchten Sie dieses Netzwerk hinzufügen?"}, "wantsToAddThisAsset": {"message": "<PERSON><PERSON><PERSON> kann das folgende Asset zu Ihrer Wallet hinzugefügt werden."}, "warning": {"message": "<PERSON><PERSON><PERSON>"}, "warningFromSnap": {"message": "Warnung von $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Durch das Einschalten dieser Option können Sie Ethereum-Konten über eine öffentliche Adresse oder einen ENS-Namen ansehen. <PERSON><PERSON><PERSON> zu dieser Beta-Funktion füllen Sie bitte diese $1 aus.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Ethereum-<PERSON><PERSON><PERSON> (Beta)"}, "watchOutMessage": {"message": "Vorsicht vor $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "<PERSON><PERSON><PERSON>"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Wir haben festgestellt, dass die aktuelle Webseite versucht hat, die entfernte window.web3 API zu verwenden. Sollte die Seite defekt sein, klicken Sie bitte auf $1 für weitere Informationen.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "Webseiten", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Willkommen zurück"}, "welcomeToMetaMask": {"message": "<PERSON> geht's"}, "whatsThis": {"message": "Was ist das?"}, "willApproveAmountForBridging": {"message": "Damit wird $1 für Bridging genehmigt."}, "willApproveAmountForBridgingHardware": {"message": "Sie werden zwei Transaktionen auf Ihrer Hardware-Wallet bestätigen müssen."}, "withdrawing": {"message": "Auszahlung"}, "wrongNetworkName": {"message": "Laut unseren Aufzeichnungen stimmt dieser Netzwerkname nicht mit dieser Chain-ID überein."}, "yes": {"message": "<PERSON>a"}, "you": {"message": "<PERSON><PERSON>"}, "youDeclinedTheTransaction": {"message": "Sie haben die Transaktion abgelehnt."}, "youNeedToAllowCameraAccess": {"message": "<PERSON>e müssen Zugriff auf die Kamera erlauben, um diese Funktion nutzen zu können."}, "youReceived": {"message": "<PERSON>e er<PERSON>ten", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "<PERSON><PERSON> sand<PERSON>", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "yourActivity": {"message": "Ihre Aktivität"}, "yourBalance": {"message": "Ihr Kontostand"}, "yourNFTmayBeAtRisk": {"message": "Ihr NFT könnte gefährdet sein"}, "yourNetworks": {"message": "Ihre Netzwerke"}, "yourPrivateSeedPhrase": {"message": "Ihre geheime Wiederherstellungsphrase"}, "yourTransactionConfirmed": {"message": "Transaktion bereits bestätigt"}, "yourTransactionJustConfirmed": {"message": "Wir waren nicht in der Lage, Ihre Transaktion zu stornieren, bevor sie in der Blockchain bestätigt wurde."}, "yourWalletIsReady": {"message": "Ihre Wallet ist bereit"}}