{"QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "QRHardwareWalletImporterTitle": {"message": "Skanuj kod QR"}, "about": {"message": "Informacje"}, "accessingYourCamera": {"message": "Uruchamian<PERSON> ka<PERSON>..."}, "account": {"message": "Ko<PERSON>"}, "accountDetails": {"message": "Szczegóły konta"}, "accountName": {"message": "Nazwa konta"}, "accountOptions": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "accountSelectionRequired": {"message": "Należy wybrać konto!"}, "activityLog": {"message": "dziennik aktywności"}, "addAcquiredTokens": {"message": "Dodaj tokeny pozyskane przy pomocy MetaMask"}, "addAlias": {"message": "<PERSON><PERSON><PERSON> alias"}, "addNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "addSuggestedTokens": {"message": "<PERSON><PERSON><PERSON>."}, "addToken": {"message": "<PERSON><PERSON><PERSON> token"}, "advanced": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "amount": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "appDescription": {"message": "Wtyczka przeglądarki do Ethereum", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "Zatwierdź"}, "approved": {"message": "Zatwierdzone"}, "asset": {"message": "Składnik aktywów"}, "attributions": {"message": "Atrybuty"}, "autoLockTimeLimit": {"message": "Czas automatycznego wylogowania (w minutach)"}, "autoLockTimeLimitDescription": {"message": "Ustaw czas bezczynności w minutach, po którym MetaMask wyloguje Cię automatycznie"}, "average": {"message": "Średnia"}, "back": {"message": "Wstecz"}, "backupApprovalInfo": {"message": "Ten tajny kod jest wymagany do odzyskania portfela w przypadku zgubienia urządzenia, zapomnienia hasła, ponownego zainstalowania MetaMask lub potrzeby uzyskania dostępu do portfela na innym urządzeniu."}, "backupApprovalNotice": {"message": "Utwórz kopię zapasową tajnego kodu odzyskiwania, aby z<PERSON><PERSON><PERSON><PERSON> bezpieczeństwo swojego portfela i środków."}, "backupNow": {"message": "Utwórz kopię zapasową"}, "balance": {"message": "Ilość środków"}, "balanceOutdated": {"message": "<PERSON><PERSON> m<PERSON><PERSON>"}, "basic": {"message": "Podstawy"}, "blockExplorerUrl": {"message": "Przeglądaj blok"}, "blockExplorerView": {"message": "Wyświetl konto w $1", "description": "$1 replaced by URL for custom block explorer"}, "browserNotSupported": {"message": "Twoja przeglądarka nie jest obsługiwana..."}, "bytes": {"message": "Baj<PERSON>"}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "cancelled": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "chainId": {"message": "Identyfikat<PERSON>"}, "chromeRequiredForHardwareWallets": {"message": "Żeby połączyć się z portfelem sprzętowym, nale<PERSON>y uruchomić MetaMask z przeglądarką Google Chrome."}, "close": {"message": "Zamknij"}, "confirm": {"message": "Potwierdź"}, "confirmPassword": {"message": "Potwierd<PERSON> hasło"}, "confirmed": {"message": "Potwierdzone"}, "connect": {"message": "Połącz"}, "connectingTo": {"message": "Łączenie z $1"}, "connectingToGoerli": {"message": "Łączenie z siecią testową Goerli"}, "connectingToLineaGoerli": {"message": "Łączenie z siecią testową Linea Goerli"}, "connectingToMainnet": {"message": "Łączenie z główną siecią Ethereum"}, "contractDeployment": {"message": "Uruchomienie kontraktu"}, "contractInteraction": {"message": "Interakcja z kontraktem"}, "copiedExclamation": {"message": "Skopiowane!"}, "copyAddress": {"message": "Skopiuj adres do schowka"}, "copyToClipboard": {"message": "Skopiuj do schowka"}, "copyTransactionId": {"message": "Ko<PERSON><PERSON>j identyfikator transakcji"}, "create": {"message": "Utwórz"}, "createPassword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "currencyConversion": {"message": "Przeliczanie walut"}, "currentLanguage": {"message": "Obecny język"}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customToken": {"message": "Wła<PERSON><PERSON> token"}, "decimal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> liczb po przecinku"}, "decimalsMustZerotoTen": {"message": "Liczb po przecinku musi być co najmniej 0 i nie więcej niż 36."}, "delete": {"message": "Usuń"}, "details": {"message": "Szczegóły"}, "done": {"message": "<PERSON><PERSON><PERSON>"}, "downloadGoogleChrome": {"message": "Ściągnij Google Chrome"}, "downloadStateLogs": {"message": "Załaduj logi stanów"}, "dropped": {"message": "Odrzucone"}, "edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "editContact": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "enterPasswordContinue": {"message": "<PERSON><PERSON><PERSON> hasło żeby kontynuować"}, "ethereumPublicAddress": {"message": "Adres publiczny Ethereum"}, "etherscanView": {"message": "Zobacz konto na Etherscan"}, "expandView": {"message": "Rozwiń widok"}, "failed": {"message": "<PERSON><PERSON> udało się"}, "fast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fileImportFail": {"message": "Importowanie pliku nie działa? Kliknij tutaj!", "description": "Helps user import their account from a JSON file"}, "forgetDevice": {"message": "<PERSON><PERSON>ń to urządzenie."}, "from": {"message": "Z"}, "gasLimit": {"message": "Limit gazu"}, "gasLimitTooLow": {"message": "Limit gazu musi wyn<PERSON>ić co najmniej 21000"}, "gasUsed": {"message": "Użyty gaz"}, "general": {"message": "Ogólne"}, "goerli": {"message": "<PERSON><PERSON><PERSON> testowa Goerli"}, "hardware": {"message": "sprz<PERSON><PERSON>"}, "hardwareWalletConnected": {"message": "Podłączono sprzętowy portfel"}, "hardwareWallets": {"message": "Podłącz sprzętowy portfel"}, "hardwareWalletsMsg": {"message": "Wybierz portfel sprzętowy, którego chcesz użyć z MetaMaskiem"}, "here": {"message": "tutaj", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "<PERSON>"}, "hide": {"message": "<PERSON><PERSON><PERSON>"}, "hideTokenPrompt": {"message": "Schować token?"}, "history": {"message": "Historia"}, "import": {"message": "Import<PERSON>j", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": " Importowane konta nie będą powiązane z Twoją pierwotną frazą seed MetaMask. <PERSON><PERSON><PERSON> się więcej o importowaniu kont "}, "imported": {"message": "Zaimportowane", "description": "status showing that an account has been fully loaded into the keyring"}, "initialTransactionConfirmed": {"message": "Twoja transakcja została potwierdzona w sieci. Kliknij OK żeby wrócić."}, "insufficientBalance": {"message": "Niewystarcz<PERSON><PERSON><PERSON> saldo."}, "insufficientFunds": {"message": "Niewystarczające środki."}, "insufficientTokens": {"message": "Niewystarczająca liczba tokenów."}, "invalidAddress": {"message": "Nieprawidłowy adres"}, "invalidAddressRecipient": {"message": "Nieprawidłowy adres odbiorcy"}, "invalidRPC": {"message": "Nieprawidłowe RPC URI"}, "invalidSeedPhrase": {"message": "Nieprawidłowa fraza seed"}, "jsonFile": {"message": "Plik JSON", "description": "format for importing an account"}, "knownAddressRecipient": {"message": "<PERSON><PERSON><PERSON> ad<PERSON> k<PERSON>."}, "learnMore": {"message": "Dowiedz się więcej"}, "learnMoreUpperCase": {"message": "Dowiedz się więcej"}, "ledgerAccountRestriction": {"message": "Musisz użyć swojego poprzedniego konta zanim dodasz kolejne."}, "likeToImportTokens": {"message": "<PERSON><PERSON> ch<PERSON>z dodać te tokeny?"}, "lineaGoerli": {"message": "Sieć testowa Linea Goerli"}, "links": {"message": "Łącza"}, "loadMore": {"message": "Załaduj więcej"}, "loading": {"message": "Ładowanie..."}, "localhost": {"message": "Serwer lokalny 8545"}, "lock": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mainnet": {"message": "Główna sieć Ethereum"}, "max": {"message": "Ma<PERSON>."}, "message": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metamaskVersion": {"message": "<PERSON><PERSON><PERSON>"}, "mustSelectOne": {"message": "Należy wybrać co najmniej 1 token."}, "needImportFile": {"message": "Musisz wybrać plik do zaimportowania.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Nie można wysłać ujemnych ilości ETH."}, "networkName": {"message": "<PERSON><PERSON><PERSON>"}, "networks": {"message": "<PERSON><PERSON><PERSON>"}, "nevermind": {"message": "<PERSON><PERSON>"}, "newAccount": {"message": "Nowe konto"}, "newAccountNumberName": {"message": "Konto $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Nowy kontakt"}, "newContract": {"message": "Nowy kontrakt"}, "newPassword": {"message": "Nowe hasło (min. 8 znaków)"}, "next": {"message": "<PERSON><PERSON>"}, "noConversionRateAvailable": {"message": "<PERSON><PERSON> kursu waluty"}, "noWebcamFound": {"message": "Twoja kamera nie została znaleziona. Spróbuj ponownie."}, "noWebcamFoundTitle": {"message": "Nie znaleziono kamery"}, "notEnoughGas": {"message": "Za mało gazu"}, "ofTextNofM": {"message": "z"}, "off": {"message": "W<PERSON>ł."}, "on": {"message": "Włą<PERSON><PERSON>"}, "origin": {"message": "Pochodzenie"}, "participateInMetaMetrics": {"message": "Weź udział w MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "<PERSON><PERSON> udział w MetaMetrics, aby pomóc nam ulepszyć MetaMask"}, "password": {"message": "<PERSON><PERSON><PERSON>"}, "passwordNotLongEnough": {"message": "<PERSON>ło jest za krótkie"}, "passwordsDontMatch": {"message": "Hasła są niezgodne"}, "pastePrivateKey": {"message": "Tutaj wklej swój prywatny klucz:", "description": "For importing an account from a private key"}, "pending": {"message": "oczekiwanie"}, "personalAddressDetected": {"message": "Wykryto osobisty adres. Wprowadź adres kontraktu tokenów."}, "prev": {"message": "Poprzednie"}, "privacyMsg": {"message": "Polityka p<PERSON>watności"}, "privateKey": {"message": "Klucz prywatny", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Uwaga: <PERSON>e ujawniaj nikomu tego klucza. Ktokolwiek posiadający Twoje prywatne klucze może użyć środków znajdujących się na Twoim koncie."}, "privateNetwork": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "readdToken": {"message": "Możesz później ponownie dodać ten token poprzez \"Dodaj token\" w opcjach menu swojego konta."}, "reject": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON><PERSON> wszystkie"}, "rejectTxsDescription": {"message": "Zamierzasz odrzucić wiele ($1) transakcji jednocześnie."}, "rejectTxsN": {"message": "<PERSON><PERSON><PERSON><PERSON> transakcje ($1)"}, "rejected": {"message": "Odrzucone"}, "remove": {"message": "usuń"}, "removeAccount": {"message": "Us<PERSON>ń konto"}, "removeAccountDescription": {"message": "To konto będzie usunięte z Twojego portfela. <PERSON><PERSON><PERSON> prz<PERSON>lej, up<PERSON><PERSON><PERSON> si<PERSON>, że masz frazę seed i klucz prywatny do tego importowanego konta. Możesz później importować lub utworzyć nowe konta z rozwijanego menu kont. "}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "restore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "<PERSON><PERSON><PERSON> słowa seed"}, "revealSeedWordsWarning": {"message": "Te słowa mogą być użyte żeby ukraść Twoje konta.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "rpcUrl": {"message": "Nowy adres URL RPC"}, "save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "scanInstructions": {"message": "Umieść kod QR na wprost kamery"}, "scanQrCode": {"message": "Skanuj kod QR"}, "search": {"message": "Szukaj"}, "securityAndPrivacy": {"message": "Bezpieczeństwo i prywatność"}, "seedPhraseReq": {"message": "Frazy seed mają 12 słów"}, "selectAnAccount": {"message": "<PERSON><PERSON><PERSON>rz konto"}, "selectHdPath": {"message": "<PERSON><PERSON><PERSON>rz ścieżkę HD"}, "selectPathHelp": {"message": "<PERSON><PERSON><PERSON> nie widzisz poniżej swoich kont Ledger, spróbuj przełączyć się na \"Legacy (MEW / MyCrypto)\""}, "selectType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "send": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"message": "Ustawienia"}, "showFiatConversionInTestnets": {"message": "Pokaż przeliczanie w sieciach testowych"}, "showFiatConversionInTestnetsDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> tę opcję, aby w<PERSON><PERSON><PERSON><PERSON><PERSON>ć przeliczanie waluty fiat w sieciach testowych"}, "showHexData": {"message": "<PERSON><PERSON><PERSON> dane hex"}, "showHexDataDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> to żeby pokazać pole danych hex na ekranie wysyłania"}, "sign": {"message": "Podpisz"}, "signatureRequest": {"message": "Prośba o podpis"}, "signed": {"message": "Podpisane"}, "somethingWentWrong": {"message": "Ups! Coś poszło nie tak."}, "speedUp": {"message": "Prz<PERSON>ś<PERSON><PERSON>"}, "speedUpCancellation": {"message": "Przyśpiesz anulowanie"}, "speedUpTransaction": {"message": "Przyśpiesz transakcję"}, "stateLogError": {"message": "Błąd podczas pobierania logów stanów."}, "stateLogs": {"message": "<PERSON><PERSON> s<PERSON>"}, "stateLogsDescription": {"message": "Logi stanów zawierają Twoje publiczne adresy kont i wykonanych transakcji."}, "submitted": {"message": "<PERSON><PERSON>ła<PERSON>"}, "supportCenter": {"message": "Odwiedź nasze Centrum Pomocy"}, "switchNetworks": {"message": "<PERSON><PERSON><PERSON> sieci"}, "symbolBetweenZeroTwelve": {"message": "Symbol musi mieć maksymalnie 11 znaków."}, "terms": {"message": "<PERSON><PERSON><PERSON>"}, "to": {"message": "Do"}, "tokenAlreadyAdded": {"message": "Token jest już dodany."}, "tokenContractAddress": {"message": "<PERSON><PERSON> k<PERSON>"}, "tokenSymbol": {"message": "Symbol tokena"}, "total": {"message": "<PERSON><PERSON>"}, "transaction": {"message": "transak<PERSON>ja"}, "transactionCancelAttempted": {"message": "Podjęto próbę anulowania transakcji z opłatą za gaz w wysokości $1 – $2"}, "transactionCancelSuccess": {"message": "Transakcja została anulowana pomyślnie – $2"}, "transactionConfirmed": {"message": "Transakcja została potwierdzona o $2."}, "transactionCreated": {"message": "Transakcja o wartości $1 została utworzona o $2."}, "transactionDropped": {"message": "Transakcja odrzucona – $2."}, "transactionError": {"message": "Błąd transakcji. Wyjątek w kodzie kontraktu."}, "transactionErrorNoContract": {"message": "Próba wywołania funkcji na adresie, który nie jest adresem kontraktu."}, "transactionErrored": {"message": "Wystą<PERSON>ł błąd podczas wykonywania transakcji."}, "transactionResubmitted": {"message": "Transakcja wysłana ponownie z opłatą za gaz podwyższoną do $1 – $2"}, "transactionSubmitted": {"message": "Transakcja wykonana z opłatą za gaz w wysokości $1 – $2."}, "transactionUpdated": {"message": "Transakcja zaktualizowana – $2."}, "transfer": {"message": "Przelew"}, "transferFrom": {"message": "Przelew z"}, "tryAgain": {"message": "Spróbuj ponownie"}, "unapproved": {"message": "Niezatwierdzone"}, "units": {"message": "jednostki"}, "unknown": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unknownQrCode": {"message": "Błąd: nie mogliśmy odczytać tego kodu QR"}, "unlock": {"message": "Odblokuj"}, "urlErrorMsg": {"message": "URI wymaga prawidłowego prefiksu HTTP/HTTPS."}, "usedByClients": {"message": "Używany przez różnych klientów"}, "userName": {"message": "Nazwa użytkownika"}, "viewContact": {"message": "Wyświetl kontakt"}, "visitWebSite": {"message": "Odwiedź naszą stronę"}, "welcomeBack": {"message": "Witaj z powrotem!"}, "youNeedToAllowCameraAccess": {"message": "Żeby użyć tej opcji należy podłączyć kamerę"}, "yourPrivateSeedPhrase": {"message": "<PERSON><PERSON> prywatna fraza seed"}}