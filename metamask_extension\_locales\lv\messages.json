{"QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "QRHardwareWalletImporterTitle": {"message": "Ieskenēt QR kodu"}, "about": {"message": "Par"}, "accessingYourCamera": {"message": "Piekļūst kamerai..."}, "account": {"message": "<PERSON><PERSON>"}, "accountDetails": {"message": "Konta informācija"}, "accountName": {"message": "Konta no<PERSON>ukums"}, "accountOptions": {"message": "Konta opcijas"}, "accountSelectionRequired": {"message": "Jums j<PERSON>a kont<PERSON>!"}, "activityLog": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addAcquiredTokens": {"message": "Pievienojiet marķierus, ko <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addAlias": {"message": "<PERSON><PERSON><PERSON>"}, "addNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "addSuggestedTokens": {"message": "<PERSON><PERSON><PERSON> marķierus"}, "addToken": {"message": "<PERSON><PERSON><PERSON>"}, "advanced": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "amount": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "appDescription": {"message": "Ethereum maks pārlūkā", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "approved": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "asset": {"message": "<PERSON><PERSON>ī<PERSON><PERSON>"}, "attributions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "autoLockTimeLimit": {"message": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON> (minūtes)"}, "autoLockTimeLimitDescription": {"message": "Iestatiet bezdarbības laiku minūt<PERSON>, pēc kura MetaMask veiks automātisku izrak<PERSON>nu"}, "average": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "back": {"message": "Atpakaļ"}, "backupApprovalInfo": {"message": "<PERSON><PERSON> slepenais kods ir <PERSON>, lai at<PERSON><PERSON><PERSON> jū<PERSON> maku, ja paz<PERSON><PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON> paroli, jāpārinstalē MetaMask vai vēlaties piekļūt makam no citas ierīces."}, "backupApprovalNotice": {"message": "<PERSON><PERSON><PERSON><PERSON>et savu slepeno atgū<PERSON> kodu, lai maks un līdzekļi atras<PERSON> droš<PERSON>."}, "backupNow": {"message": "<PERSON><PERSON><PERSON><PERSON> tag<PERSON>"}, "balance": {"message": "Bilance"}, "balanceOutdated": {"message": "Bilance var nebūt aktuāla"}, "basic": {"message": "Pamata"}, "blockExplorerUrl": {"message": "Bloķēt Explorer"}, "blockExplorerView": {"message": "Skatīt kontu $1", "description": "$1 replaced by URL for custom block explorer"}, "browserNotSupported": {"message": "<PERSON><PERSON><PERSON> p<PERSON>kprogramma netiek atbalstīta..."}, "bytes": {"message": "<PERSON><PERSON>"}, "cancel": {"message": "Atcelt"}, "cancelled": {"message": "Atcelts"}, "chainId": {"message": "Ķēdes ID"}, "chromeRequiredForHardwareWallets": {"message": "MetaMask ir jāpalaiž pārlūkprogrammā Google Chrome, lai varētu pievienot aparatūras maku."}, "close": {"message": "Aizvērt"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "confirmPassword": {"message": "Apstipriniet paroli"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "connect": {"message": "<PERSON><PERSON><PERSON>ša<PERSON>"}, "connectingTo": {"message": "Pieslēdzas $1"}, "connectingToGoerli": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> testa tīklam"}, "connectingToLineaGoerli": {"message": "Pieslēdzas Linea Goerli testa tīklam"}, "connectingToMainnet": {"message": "Savienojas ar galveno Ethereum tīklu"}, "contractDeployment": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contractInteraction": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "copiedExclamation": {"message": "Nokopēts!"}, "copyAddress": {"message": "Iekopēt adresi starpliktuvē"}, "copyToClipboard": {"message": "Kopēt starpliktuvē"}, "copyTransactionId": {"message": "<PERSON><PERSON><PERSON><PERSON> darī<PERSON>"}, "create": {"message": "Izveidot"}, "createPassword": {"message": "Izveidot paroli"}, "currencyConversion": {"message": "Valūtas konvertācija"}, "currentLanguage": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> valodā"}, "custom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "customToken": {"message": "Pielāgots marķieris"}, "decimal": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pre<PERSON>"}, "decimalsMustZerotoTen": {"message": "Daļskaitļiem jābūt diapazonā no 0 līdz 36."}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "details": {"message": "Informācija"}, "done": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "downloadGoogleChrome": {"message": "Lejupielādēt ar Google Chrome"}, "downloadStateLogs": {"message": "Lejupielād<PERSON><PERSON> stāvokļa žurn<PERSON>"}, "dropped": {"message": "Atmests"}, "edit": {"message": "<PERSON><PERSON>"}, "editContact": {"message": "Rediģēt līgumu"}, "enterPasswordContinue": {"message": "<PERSON>eva<PERSON><PERSON> paroli, lai tur<PERSON>"}, "ethereumPublicAddress": {"message": "Ethereum publiskā adrese"}, "etherscanView": {"message": "<PERSON><PERSON><PERSON>t kontu ar Etherscan"}, "expandView": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "failed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fast": {"message": "<PERSON><PERSON><PERSON>"}, "fileImportFail": {"message": "Vai faila importēšanas iespēja nedarbojas? Klikšķiniet šeit!", "description": "Helps user import their account from a JSON file"}, "forgetDevice": {"message": "Aizmirst š<PERSON>"}, "from": {"message": "No"}, "gasLimit": {"message": "Gas robežvērtība"}, "gasLimitTooLow": {"message": "Minimālajai Gas robežvērtībai jābūt vismaz 21000"}, "gasUsed": {"message": "Izlietotie Gas"}, "general": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "goerli": {"message": "<PERSON><PERSON><PERSON> testa tīkls"}, "hardware": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hardwareWalletConnected": {"message": "Aparatūras maks pieslēgts"}, "hardwareWallets": {"message": "Pieslēdziet aparatūras maku"}, "hardwareWalletsMsg": {"message": "Atlasiet aparatūras maku lietošanai ar MetaMask"}, "here": {"message": "š<PERSON>", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Hex dati"}, "hide": {"message": "<PERSON><PERSON>ē<PERSON>"}, "hideTokenPrompt": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>?"}, "history": {"message": "Vēsture"}, "import": {"message": "Importēt", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": "Importētie konti netiks piesaistīti sākotnēji izveidotā MetaMask konta atkopšanas frāzei. Uzziniet vairāk par importētajiem kontiem"}, "imported": {"message": "Importēts", "description": "status showing that an account has been fully loaded into the keyring"}, "initialTransactionConfirmed": {"message": "<PERSON><PERSON><PERSON> s<PERSON>ējais darījums apstiprināts tīklā. Spiediet OK, lai atgrieztos."}, "insufficientBalance": {"message": "Nepietiekama bilance."}, "insufficientFunds": {"message": "Nepietiek līdzekļu."}, "insufficientTokens": {"message": "Nepietiek marķieru."}, "invalidAddress": {"message": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>e"}, "invalidAddressRecipient": {"message": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>"}, "invalidRPC": {"message": "Nederīgs RPC URL"}, "invalidSeedPhrase": {"message": "Nederīga <PERSON>kopšanas frāze"}, "jsonFile": {"message": "JSON datne", "description": "format for importing an account"}, "knownAddressRecipient": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> līguma adrese."}, "learnMore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ledgerAccountRestriction": {"message": "Jums jāiz<PERSON>to pē<PERSON><PERSON><PERSON><PERSON> konts pirms varat pievienot jaunu."}, "likeToImportTokens": {"message": "Vai vēlaties pievienot šos marķierus?"}, "lineaGoerli": {"message": "Linea Goerli testa tīkls"}, "links": {"message": "<PERSON><PERSON>"}, "loadMore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "loading": {"message": "Notiek iel<PERSON>de..."}, "localhost": {"message": "Resursdators 8545"}, "lock": {"message": "Izrakstīties"}, "mainnet": {"message": "Galvenais Ethereum tīkls"}, "max": {"message": "Ma<PERSON>."}, "memo": {"message": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "message": {"message": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>"}, "metamaskVersion": {"message": "MetaMask versija"}, "mustSelectOne": {"message": "Jāatlasa vismaz 1 marķieris."}, "needImportFile": {"message": "Jums <PERSON> fails, ko import<PERSON>t", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Nav iespējams nosūtīt negatīvu ETH summu."}, "networkName": {"message": "<PERSON><PERSON><PERSON>"}, "networks": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "nevermind": {"message": "Nav svar<PERSON>gi"}, "newAccount": {"message": "<PERSON><PERSON><PERSON> konts"}, "newAccountNumberName": {"message": "Konts $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "newContract": {"message": "<PERSON><PERSON><PERSON> lī<PERSON>s"}, "newPassword": {"message": "Jauna parole (vism. 8 raks<PERSON><PERSON><PERSON>)"}, "next": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "noConversionRateAvailable": {"message": "Konversijas kurss nav pieejams"}, "noWebcamFound": {"message": "<PERSON><PERSON><PERSON> datora tīmekļa kamera netika atrasta. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz."}, "noWebcamFoundTitle": {"message": "Tīmekļa kamera nav atrasta"}, "notEnoughGas": {"message": "Nepietiek Gas"}, "ofTextNofM": {"message": "no"}, "off": {"message": "Izsl."}, "ok": {"message": "<PERSON><PERSON>"}, "on": {"message": "Iesl."}, "origin": {"message": "Avots"}, "participateInMetaMetrics": {"message": "Piedalieties MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Piedalieties MetaMetrics un palīdziet mums to uzlabot"}, "password": {"message": "Parole"}, "passwordNotLongEnough": {"message": "Parole nav pietiekami gara"}, "passwordsDontMatch": {"message": "<PERSON><PERSON><PERSON>"}, "pastePrivateKey": {"message": "Ielīmējiet privātās at<PERSON>lēgas rindu šeit:", "description": "For importing an account from a private key"}, "pending": {"message": "gaida"}, "personalAddressDetected": {"message": "Konstatēta personīgā adrese. Ievadiet marķiera līguma adresi."}, "prev": {"message": "<PERSON><PERSON><PERSON>."}, "privacyMsg": {"message": "Privātuma politika"}, "privateKey": {"message": "Privātā at<PERSON>lēga", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Brīdinājums! Nekad neizpaudiet šo atslēgu. <PERSON><PERSON><PERSON><PERSON>, kuram ir <PERSON><PERSON><PERSON> privātās atslēgas, var nozagt jebkuru jūsu kontā esošo aktīvu."}, "privateNetwork": {"message": "<PERSON>riv<PERSON><PERSON><PERSON> t<PERSON>"}, "readdToken": {"message": "<PERSON><PERSON><PERSON> varat šo marķieri iestatīt atpakaļ nākotnē, konta opciju izvēlnē atverot \"Pievienot marķieri\"."}, "reject": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON><PERSON> visus"}, "rejectTxsDescription": {"message": "<PERSON><PERSON><PERSON> g<PERSON>ties noraidīt $1 darījumus."}, "rejectTxsN": {"message": "Noraidīt $1 darījumus"}, "rejected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removeAccount": {"message": "Konta noņ<PERSON>ša<PERSON>"}, "removeAccountDescription": {"message": "Šis darījums tiks dzēsts no jūsu maka. <PERSON>rms turpiniet, l<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka jums ir šī importētā konta oriģinālā sēklas frāze vai privātā atslēga. Jūs varat importēt vai izveidot kontus vēlreiz, izmantojot konta nolaižamo izvēlni."}, "required": {"message": "Nepieciešams"}, "reset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "restore": {"message": "At<PERSON>uno<PERSON>"}, "revealSeedWords": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>"}, "revealSeedWordsWarning": {"message": "<PERSON><PERSON> vā<PERSON> var i<PERSON>t visu jūsu kontu no<PERSON>.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "rpcUrl": {"message": "Jauns RPC URL"}, "save": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "scanInstructions": {"message": "Novietojiet QR kodu kameras priekšā"}, "scanQrCode": {"message": "Ieskenēt QR kodu"}, "search": {"message": "Meklēt"}, "securityAndPrivacy": {"message": "Drošība un konfidencialitāte"}, "seedPhraseReq": {"message": "Atkopšanas frāzes ir 12 vārdus garas"}, "selectAnAccount": {"message": "Atlasiet kontu"}, "selectHdPath": {"message": "Atlasīt HD ceļu"}, "selectPathHelp": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nered<PERSON>t savus esoš<PERSON> ž<PERSON> kont<PERSON>, mēģiniet pārslēgt ceļus uz \"Legacy (MEW / MyCrypto)\""}, "selectType": {"message": "Atlasiet veidu"}, "send": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"message": "Iestatījumi"}, "showFiatConversionInTestnets": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> konvers<PERSON>ju testa tīklos"}, "showFiatConversionInTestnetsDescription": {"message": "<PERSON><PERSON>, lai testa tīklos uzrādītu fiat konversiju"}, "showHexData": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hex datus"}, "showHexDataDescription": {"message": "<PERSON><PERSON>, lai atvērtu hex datus sūtī<PERSON> ekr<PERSON>"}, "sign": {"message": "Parakstīt"}, "signatureRequest": {"message": "Paraksta pieprasījums"}, "signed": {"message": "Parakstīts"}, "somethingWentWrong": {"message": "Ak vai! Ra<PERSON><PERSON><PERSON> probl<PERSON>."}, "speedUp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "speedUpTransaction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> šo dar<PERSON>mu"}, "stateLogError": {"message": "Iegūstot <PERSON>a <PERSON>, r<PERSON><PERSON><PERSON>."}, "stateLogs": {"message": "<PERSON><PERSON>"}, "stateLogsDescription": {"message": "Statusa žurnāli ietver publisko kontu adreses un nosūtītos darī<PERSON>mus."}, "submitted": {"message": "Iesniegts"}, "supportCenter": {"message": "Apmeklējiet mūsu Atbalsta centru"}, "switchNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "symbol": {"message": "Simbols"}, "symbolBetweenZeroTwelve": {"message": "<PERSON><PERSON><PERSON><PERSON> nedr<PERSON><PERSON><PERSON> b<PERSON><PERSON> v<PERSON> par 11 rakstzīmēm."}, "terms": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "to": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "token": {"message": "Marķieris"}, "tokenAlreadyAdded": {"message": "Žetons jau pie<PERSON>."}, "tokenContractAddress": {"message": "Marķiera līguma adrese"}, "tokenSymbol": {"message": "Žetona simbols"}, "total": {"message": "Kopā"}, "transaction": {"message": "dar<PERSON><PERSON><PERSON>"}, "transactionCancelAttempted": {"message": "Darījums tika mēģināts atcelt ar maksājumu par Gas $1 pie $2"}, "transactionCancelSuccess": {"message": "Darījums veiksmīgi atcelts $2"}, "transactionConfirmed": {"message": "Darījums apstiprināts $2."}, "transactionCreated": {"message": "Darījums $1 vērtībā izveidots $2."}, "transactionDropped": {"message": "Darījums atmests $2."}, "transactionError": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON> kodā uzrādās izņēmums."}, "transactionErrorNoContract": {"message": "Mēģināt i<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kas nav norā<PERSON><PERSON>ta līgum<PERSON>."}, "transactionErrored": {"message": "Darījumā radusies kļūda."}, "transactionResubmitted": {"message": "Darījums atkārtoti iesniegts ar maksu par <PERSON>, p<PERSON>elin<PERSON><PERSON> līdz $1 pie $2"}, "transactionSubmitted": {"message": "Darījums iesniegts ar maksu par Gas $1 pie $2."}, "transactionUpdated": {"message": "Darījums atjaunināts $2."}, "transfer": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "transferFrom": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> no"}, "tryAgain": {"message": "Mēģināt vēlreiz"}, "unapproved": {"message": "Nav aps<PERSON><PERSON><PERSON>"}, "units": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unknown": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "unknownQrCode": {"message": "Kļūda: šo QR kodu neizdevās identificēt"}, "unlock": {"message": "Atbloķēt"}, "urlErrorMsg": {"message": "URI jāsākas ar atbilstošo HTTP/HTTPS priedēkli."}, "usedByClients": {"message": "<PERSON><PERSON><PERSON>"}, "userName": {"message": "Lietotājvārds"}, "viewContact": {"message": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "visitWebSite": {"message": "Apmeklējiet mūsu tīmek<PERSON>a vietni"}, "welcomeBack": {"message": "Ar <PERSON>!"}, "youNeedToAllowCameraAccess": {"message": "<PERSON> lietotu <PERSON>, j<PERSON><PERSON><PERSON> ka<PERSON>."}, "yourPrivateSeedPhrase": {"message": "<PERSON>ū<PERSON> privātā <PERSON> frāze"}}