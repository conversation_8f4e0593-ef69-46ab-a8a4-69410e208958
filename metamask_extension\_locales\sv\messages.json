{"QRHardwareSignRequestCancel": {"message": "Avvisa"}, "QRHardwareWalletImporterTitle": {"message": "Skanna QR-koden"}, "about": {"message": "Om"}, "accessingYourCamera": {"message": "Beg<PERSON>r å<PERSON> till din kamera..."}, "account": {"message": "Ko<PERSON>"}, "accountDetails": {"message": "Ko<PERSON><PERSON><PERSON><PERSON>"}, "accountName": {"message": "Kontonamn"}, "accountOptions": {"message": "Kontoalternativ"}, "accountSelectionRequired": {"message": "Du måste välja ett konto!"}, "activityLog": {"message": "aktivitetslogg"}, "addAcquiredTokens": {"message": "<PERSON><PERSON><PERSON> till de tokens du har skaffat med MetaMask"}, "addAlias": {"message": "<PERSON><PERSON><PERSON> till alias"}, "addNetwork": {"message": "Lägg till nätverk"}, "addSuggestedTokens": {"message": "Lägg till föreslagna tokens"}, "addToken": {"message": "Lägg till token"}, "advanced": {"message": "<PERSON><PERSON><PERSON>"}, "amount": {"message": "Belopp"}, "appDescription": {"message": "En Ethereum-plånbok i din webbläsare", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "Godkä<PERSON>"}, "approved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "asset": {"message": "Till<PERSON><PERSON><PERSON>"}, "attributions": {"message": "Tillskrivningar"}, "autoLockTimeLimit": {"message": "Timer för automatisk utloggning (minuter)"}, "autoLockTimeLimitDescription": {"message": "<PERSON><PERSON><PERSON> in efter hur lång tids inaktivitet som MetaMask automatiskt loggar ut"}, "average": {"message": "Genomsnitt"}, "back": {"message": "Bakåt"}, "backupApprovalInfo": {"message": "Den hemliga koden krävs för att komma åt din plånbok ifall du tappar bort din enhet, gl<PERSON><PERSON> ditt lösen<PERSON>, behöver installera om MetaMask eller om du vill komma åt din plånbok på en annan enhet."}, "backupApprovalNotice": {"message": "Säkerhetskopiera din hemliga återställningskod för att se till att din plånbok och dina tillgångar är säkra."}, "backupNow": {"message": "Säkerhetskopiera nu"}, "balance": {"message": "<PERSON><PERSON>"}, "balanceOutdated": {"message": "Saldot kanske inte är uppdaterat"}, "basic": {"message": "<PERSON><PERSON><PERSON>"}, "blockExplorerUrl": {"message": "Blockera Utforskaren"}, "blockExplorerView": {"message": "Visa konto på $1", "description": "$1 replaced by URL for custom block explorer"}, "browserNotSupported": {"message": "<PERSON>l<PERSON>sare stöds inte..."}, "cancel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "cancelled": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "chromeRequiredForHardwareWallets": {"message": "Du måste använda MetaMask på Google Chrome för att ansluta till din hårdvaruplånbok."}, "close": {"message": "Stäng"}, "confirm": {"message": "Bekräfta"}, "confirmPassword": {"message": "Bekräfta lösenord"}, "confirmed": {"message": "Bekräftat"}, "connect": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "connectingTo": {"message": "Ansluter till $1"}, "connectingToGoerli": {"message": "Ansluter till Goerli Test Network"}, "connectingToLineaGoerli": {"message": "Ansluter till Linea Goerli Test Network"}, "connectingToMainnet": {"message": "Koppla till Ethereums huvudnätverk"}, "contractDeployment": {"message": "Kontraktplacering"}, "contractInteraction": {"message": "Kontraktinteraktion"}, "copiedExclamation": {"message": "Kopierades!"}, "copyAddress": {"message": "<PERSON><PERSON><PERSON> adress till urk<PERSON>p"}, "copyToClipboard": {"message": "Kopiera till Urklipp"}, "copyTransactionId": {"message": "Kopiera transaktions-ID"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "createPassword": {"message": "Skapa lö<PERSON>ord"}, "currencyConversion": {"message": "Valutaomvandling"}, "currentLanguage": {"message": "Aktuellt språk"}, "custom": {"message": "<PERSON><PERSON><PERSON>"}, "customToken": {"message": "Anpassad token"}, "decimal": {"message": "Decimalprecision"}, "decimalsMustZerotoTen": {"message": "Decimalerna måste vara minst 0 och inte över 36."}, "delete": {"message": "<PERSON><PERSON><PERSON>"}, "details": {"message": "Info"}, "done": {"message": "<PERSON><PERSON><PERSON>"}, "downloadGoogleChrome": {"message": "Ladda ner Google Chrome"}, "downloadStateLogs": {"message": "<PERSON>dda ner <PERSON>"}, "dropped": {"message": "<PERSON><PERSON><PERSON>"}, "edit": {"message": "Rediger<PERSON>"}, "editContact": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "enterPasswordContinue": {"message": "<PERSON><PERSON> lösenord för att fortsätta"}, "ethereumPublicAddress": {"message": "Ethereum offentlig adress"}, "etherscanView": {"message": "Visa konto på Etherscan"}, "expandView": {"message": "Expandera vy"}, "failed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fast": {"message": "<PERSON><PERSON>bb"}, "fileImportFail": {"message": "Fungerar inte filimporten? <PERSON>licka här!", "description": "Helps user import their account from a JSON file"}, "forgetDevice": {"message": "Glö<PERSON> den här enheten"}, "from": {"message": "<PERSON><PERSON><PERSON>"}, "gasLimit": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "gasLimitTooLow": {"message": "Gasgräns måste vara minst 21 000"}, "gasUsed": {"message": "Gas använd"}, "general": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "goerli": {"message": "Goerli testnätverk"}, "hardware": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "hardwareWalletConnected": {"message": "Hårdvaruplånbok ansluten"}, "hardwareWallets": {"message": "Anslut en hårdvaruplånbok"}, "hardwareWalletsMsg": {"message": "Välj en hårdvaruplånbok som du vill använda med MetaMask"}, "here": {"message": "<PERSON>är", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Hex-data"}, "hide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "hideTokenPrompt": {"message": "Göm token?"}, "history": {"message": "Historik"}, "import": {"message": "Importera", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": "Importerade konton kommer inte att kopplas till din ursprungligen skapade seedphrase för ditt MetaMask-konto. Läs mer om importerade konton"}, "imported": {"message": "Importerade", "description": "status showing that an account has been fully loaded into the keyring"}, "initialTransactionConfirmed": {"message": "Din initiala transaktion har bekräftats av nätverket. Klicka på OK för att gå tillbaka."}, "insufficientBalance": {"message": "Otillräcklig balans."}, "insufficientFunds": {"message": "Otillräckliga medel."}, "insufficientTokens": {"message": "Otillräckliga tokens."}, "invalidAddress": {"message": "<PERSON><PERSON><PERSON><PERSON> adress"}, "invalidAddressRecipient": {"message": "<PERSON><PERSON><PERSON><PERSON> adress är og<PERSON>ig"}, "invalidRPC": {"message": "Ogiltig RPC-URL"}, "invalidSeedPhrase": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "jsonFile": {"message": "JSON-fil", "description": "format for importing an account"}, "knownAddressRecipient": {"message": "Känd kontraktadress."}, "learnMore": {"message": "<PERSON><PERSON><PERSON>r"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON><PERSON>r"}, "ledgerAccountRestriction": {"message": "Du måste använda ditt senaste konto innan du kan lägga till ett nytt."}, "likeToImportTokens": {"message": "Vill du lägga till dessa tokens?"}, "lineaGoerli": {"message": "Linea Goerli testnätverk"}, "links": {"message": "Länkar"}, "loadMore": {"message": "Ladda mer"}, "loading": {"message": "<PERSON><PERSON><PERSON> in..."}, "lock": {"message": "Logga ut"}, "mainnet": {"message": "Ethereums huvudnätverk"}, "max": {"message": "Maximalt"}, "message": {"message": "Meddelande"}, "metamaskVersion": {"message": "MetaMask-version"}, "mustSelectOne": {"message": "Minst 1 token måste väljas."}, "needImportFile": {"message": "Du måste välja en fil att importera.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Kan inte skicka negativa summor av ETH."}, "networkName": {"message": "Nätverksnamn"}, "networks": {"message": "Nätverk"}, "nevermind": {"message": "<PERSON><PERSON><PERSON> i det"}, "newAccount": {"message": "Nytt konto"}, "newAccountNumberName": {"message": "Konto $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Ny kontakt"}, "newContract": {"message": "Nytt kontrakt"}, "newPassword": {"message": "<PERSON>ytt lösenord (minst 8 tecken)"}, "next": {"message": "<PERSON><PERSON><PERSON>"}, "noConversionRateAvailable": {"message": "Ingen omräkningskurs tillgänglig"}, "noWebcamFound": {"message": "Din dators webbkamera hittades inte. Vänligen försök igen."}, "noWebcamFoundTitle": {"message": "Webbkamera hittades ej"}, "notEnoughGas": {"message": "Inte tillräckligt med gas"}, "ofTextNofM": {"message": "av"}, "off": {"message": "Av"}, "ok": {"message": "OK"}, "on": {"message": "På"}, "origin": {"message": "Ursprung"}, "participateInMetaMetrics": {"message": "Delta i MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Delta i MetaMetrics för att hjälpa oss göra MetaMask bättre"}, "password": {"message": "L<PERSON>senord"}, "passwordNotLongEnough": {"message": "Lösenordet är inte tillräckligt långt"}, "passwordsDontMatch": {"message": "Lösenorden matchar inte"}, "pastePrivateKey": {"message": "Klistra in din privata nyckel här:", "description": "For importing an account from a private key"}, "pending": {"message": "väntar"}, "personalAddressDetected": {"message": "Personlig adress upptäckt. Ange kontraktadress för token."}, "prev": {"message": "Föregående"}, "privacyMsg": {"message": "Integritetspolicy"}, "privateKey": {"message": "Privat nyckel", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Varning: Avsl<PERSON><PERSON> aldrig denna n<PERSON>. Med dina privata nycklar kan vem som helst stjäla tillgångarna på ditt konto."}, "privateNetwork": {"message": "Privat nätverk"}, "readdToken": {"message": "Du kan lägga till denna token i framtiden genom att välja \"<PERSON><PERSON><PERSON> till token\" i kontots alternativmeny."}, "reject": {"message": "Avvisa"}, "rejectAll": {"message": "Neka alla"}, "rejectTxsDescription": {"message": "Du håller på att massneka överföringar på $1."}, "rejectTxsN": {"message": "Neka överföringar på $1"}, "rejected": {"message": "<PERSON><PERSON><PERSON>"}, "remove": {"message": "<PERSON> bort"}, "removeAccount": {"message": "Ta bort konto"}, "removeAccountDescription": {"message": "Detta konto kommer att avlägsnas från din plånbok. Säkerställ att du har din ursprungliga seed phrase eller den privata nyckeln till detta importerade konto innan du fortsätter. Du kan importera eller skapa konton igen via kontots rullgardinsmeny."}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "restore": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "Visa seed-ord"}, "revealSeedWordsWarning": {"message": "De här orden kan användas för att stjäla alla dina konton.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "rpcUrl": {"message": "Ny RPC-URL"}, "save": {"message": "Spara"}, "scanInstructions": {"message": "Placera QR-koden framför din kamera"}, "scanQrCode": {"message": "Skanna QR-koden"}, "search": {"message": "<PERSON>ö<PERSON>"}, "securityAndPrivacy": {"message": "Säkerhet och integritet"}, "seedPhraseReq": {"message": "Nyckelfraser är 12 ord långa."}, "selectAnAccount": {"message": "V<PERSON><PERSON><PERSON> ett konto"}, "selectHdPath": {"message": "Välj HD-sökväg"}, "selectPathHelp": {"message": "Om du inte ser dina befintliga Ledger-konton nedan, prova att byta sökväg till \"Legacy (MEW / MyCrypto)\""}, "selectType": {"message": "V<PERSON><PERSON>j typ"}, "send": {"message": "<PERSON><PERSON><PERSON>"}, "settings": {"message": "Inställningar"}, "showFiatConversionInTestnets": {"message": "Visa omvandling på testnätverk"}, "showFiatConversionInTestnetsDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> denna för att visa fiatomvandlingar på Testnätverk"}, "showHexData": {"message": "Visa hex-information"}, "showHexDataDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>tta för att visa hex-datafältet på sändarskärmen"}, "sign": {"message": "Signera"}, "signatureRequest": {"message": "Signaturbegäran"}, "signed": {"message": "Signerat"}, "somethingWentWrong": {"message": "Hoppsan! <PERSON><PERSON>got gick fel."}, "speedUp": {"message": "<PERSON><PERSON><PERSON> upp"}, "speedUpCancellation": {"message": "Snabba upp denna av<PERSON>"}, "speedUpTransaction": {"message": "Snabba upp den här transaktionen"}, "stateLogError": {"message": "Fel vid inhämtande av statusloggar."}, "stateLogs": {"message": "Statushistorik"}, "stateLogsDescription": {"message": "Statusloggar innehåller dina publika kontoadresser och skickade transaktioner."}, "submitted": {"message": "Inskickat"}, "supportCenter": {"message": "Besök vårt supportcenter"}, "switchNetworks": {"message": "Växla nätverk"}, "symbolBetweenZeroTwelve": {"message": "Symbolen måste vara 11 tecken eller färre."}, "terms": {"message": "Användarvillkor"}, "to": {"message": "<PERSON>"}, "tokenAlreadyAdded": {"message": "Token har redan lagts till."}, "tokenContractAddress": {"message": "Kontraktsadress för token"}, "tokenSymbol": {"message": "Token-symbol"}, "transaction": {"message": "överföring"}, "transactionCancelAttempted": {"message": "Överföringsavbrott har försökt göras med gasavgift på $1 vid $2"}, "transactionCancelSuccess": {"message": "Överföringen avbröts vid $2"}, "transactionConfirmed": {"message": "Transaktionen bekräftad på $2."}, "transactionCreated": {"message": "Transaktion skapad med ett värde på $1 på $2."}, "transactionDropped": {"message": "Transaktionen avbruten vid $2."}, "transactionError": {"message": "Överföringsfel. Undantag i kontraktskoden."}, "transactionErrorNoContract": {"message": "Försöker åberopa en funktion på en icke-kontraktsadress."}, "transactionErrored": {"message": "Transaktionen påträffade ett fel."}, "transactionResubmitted": {"message": "Transaktion skickad igen med gas-avgift ökad till $1 på $2"}, "transactionSubmitted": {"message": "Överföring angedd med gasavgift på $1 vid $2."}, "transactionUpdated": {"message": "Transaktionen uppdaterades $2."}, "transfer": {"message": "Överföring"}, "transferFrom": {"message": "Överför från"}, "tryAgain": {"message": "Försök igen"}, "unapproved": {"message": "Inte godkänd"}, "units": {"message": "enheter"}, "unknown": {"message": "<PERSON><PERSON><PERSON>"}, "unknownQrCode": {"message": "Fel: Vi kunde inte identifiera denna QR-kod"}, "unlock": {"message": "<PERSON><PERSON><PERSON> upp"}, "urlErrorMsg": {"message": "URI:er kräver lämpligt HTTP/HTTPS-prefix."}, "usedByClients": {"message": "Används av många olika klienter"}, "userName": {"message": "Användarnamn"}, "viewContact": {"message": "Visa kontakt"}, "visitWebSite": {"message": "Besök vår hemsida"}, "welcomeBack": {"message": "Välkommen tillbaka!"}, "youNeedToAllowCameraAccess": {"message": "Du måste tillåta å<PERSON>komst till kameran för att använda den här funktionen."}, "yourPrivateSeedPhrase": {"message": "Din privata seedphrase"}}