{"QRHardwareInvalidTransactionTitle": {"message": "Error"}, "QRHardwareMismatchedSignId": {"message": "Datos de transacción incongruentes. Compruebe los detalles."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "No hay más cuentas. Para acceder a otra cuenta que no figura en la lista, vuelva a conectar su monedero físico y selecciónela."}, "QRHardwareScanInstructions": {"message": "Coloque el código QR delante de la cámara. La pantalla está borrosa, pero no afectará la lectura."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "Después de firmar con su monedero, haga clic en \"Obtener firma\" para recibir la firma"}, "QRHardwareSignRequestGetSignature": {"message": "Obtener firma"}, "QRHardwareSignRequestSubtitle": {"message": "Escanee código QR con su monedero"}, "QRHardwareSignRequestTitle": {"message": "Solicitar firma"}, "QRHardwareUnknownQRCodeTitle": {"message": "Error"}, "QRHardwareUnknownWalletQRCode": {"message": "Código QR no válido. Escanee el QR sincronizado del monedero físico."}, "QRHardwareWalletImporterTitle": {"message": "Escanear código QR"}, "QRHardwareWalletSteps1Description": {"message": "A continuación puede elegir entre una lista de socios oficiales que admiten códigos QR."}, "QRHardwareWalletSteps1Title": {"message": "Conecte su monedero físico QR"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Ocultar cuentas de $1", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Ocultar 1 cuenta"}, "SrpListShowAccounts": {"message": "Mostrar $1 cuentas", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Mostrar 1 cuenta"}, "about": {"message": "Acerca de"}, "accept": {"message": "Aceptar"}, "acceptTermsOfUse": {"message": "Leí y estoy de acuerdo con $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Accediendo a la cámara…"}, "account": {"message": "C<PERSON><PERSON>"}, "accountActivity": {"message": "Actividad de la cuenta"}, "accountActivityText": {"message": "Seleccione las cuentas sobre las que desea recibir notificaciones:"}, "accountDetails": {"message": "Detalles de la cuenta"}, "accountIdenticon": {"message": "Identicon de cuenta"}, "accountIsntConnectedToastText": {"message": "$1 no está conectado a $2"}, "accountName": {"message": "Nombre de la cuenta"}, "accountNameDuplicate": {"message": "Este nombre de cuenta ya existe", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Este nombre de cuenta está reservado", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Opciones de la cuenta"}, "accountPermissionToast": {"message": "Se actualizaron los permisos de la cuenta"}, "accountSelectionRequired": {"message": "Debe seleccionar una cuenta."}, "accountTypeNotSupported": {"message": "Tipo de cuenta no admitido"}, "accounts": {"message": "Cuentas"}, "accountsConnected": {"message": "Cuentas conectadas"}, "accountsPermissionsTitle": {"message": "Ver sus cuentas y sugerir transacciones"}, "accountsSmallCase": {"message": "cuentas"}, "active": {"message": "Activo"}, "activity": {"message": "Actividad"}, "activityLog": {"message": "registro de actividad"}, "add": {"message": "Agregar"}, "addACustomNetwork": {"message": "Agregar una red personalizada"}, "addANetwork": {"message": "Agregar una red"}, "addANickname": {"message": "Añadir un apodo"}, "addAUrl": {"message": "Agregar una URL"}, "addAccount": {"message": "<PERSON><PERSON><PERSON> cue<PERSON>"}, "addAccountFromNetwork": {"message": "Agregar cuenta de $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "Añadir cuenta a MetaMask"}, "addAcquiredTokens": {"message": "Agregar los tokens que adquirió con MetaMask"}, "addAlias": {"message": "<PERSON><PERSON><PERSON><PERSON> alias"}, "addBitcoinAccountLabel": {"message": "Cuenta de Bitcoin"}, "addBlockExplorer": {"message": "Agregar un explorador de bloque"}, "addBlockExplorerUrl": {"message": "Agregar una URL del explorador de bloques"}, "addContact": {"message": "Agregar contacto"}, "addCustomNetwork": {"message": "Agregar red personalizada"}, "addEthereumChainWarningModalHeader": {"message": "Agregue este proveedor de RPC solo si está seguro de que puede confiar en él. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Los proveedores malintencionados pueden mentir sobre el estado de la cadena de bloques y registrar la actividad de su red."}, "addEthereumChainWarningModalListHeader": {"message": "Es importante que su proveedor sea confiable, ya que tiene el poder para:"}, "addEthereumChainWarningModalListPointOne": {"message": "Ver sus cuentas y direcciones IP, y asociarlas"}, "addEthereumChainWarningModalListPointThree": {"message": "Mostrar saldos de cuentas y otros estados en la cadena"}, "addEthereumChainWarningModalListPointTwo": {"message": "Transmitir sus transacciones"}, "addEthereumChainWarningModalTitle": {"message": "Va a agregar un nuevo proveedor de RPC para la red principal de Ethereum"}, "addEthereumWatchOnlyAccount": {"message": "Observar una cuenta Ethereum (Beta)"}, "addFriendsAndAddresses": {"message": "Agregue amigos y direcciones de confianza"}, "addHardwareWalletLabel": {"message": "Monedero físico"}, "addIPFSGateway": {"message": "Agregue su puerta de enlace de IPFS preferida"}, "addImportAccount": {"message": "Añadir una cuenta o un monedero físico"}, "addMemo": {"message": "<PERSON><PERSON><PERSON> memo"}, "addNetwork": {"message": "Agregar red"}, "addNetworkConfirmationTitle": {"message": "Agregar $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Añadir una cuenta nueva de Ethereum"}, "addNewEthereumAccountLabel": {"message": "Cuenta de Ethereum"}, "addNewSolanaAccountLabel": {"message": "Cuenta de Solana"}, "addNft": {"message": "<PERSON><PERSON><PERSON>"}, "addNfts": {"message": "<PERSON><PERSON><PERSON>"}, "addNonEvmAccount": {"message": "Añadir cuenta de $1", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Para habilitar la red de $1, debe crear una cuenta de $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Agregar URL RPC"}, "addSnapAccountToggle": {"message": "Activar \"Añadir una cuenta Snap (Beta)\""}, "addSnapAccountsDescription": {"message": "Activar esta función le dará la opción de agregar los nuevos Snaps de la cuenta Beta directamente desde su lista de cuentas. Si instala una cuenta Snap, recuerde que es un servicio de terceros."}, "addSuggestedNFTs": {"message": "Añadir NFT sugeridos"}, "addSuggestedTokens": {"message": "Agregar tokens sugeridos"}, "addToken": {"message": "Agregar token"}, "addTokenByContractAddress": {"message": "¿No encuentra un token? Puede agregar cualquier token si copia su dirección. Puede encontrar la dirección de contrato del token en $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Agregar URL"}, "addingAccount": {"message": "Añadiendo cuenta"}, "addingCustomNetwork": {"message": "Agregando red"}, "additionalNetworks": {"message": "Redes adicionales"}, "address": {"message": "Dirección"}, "addressCopied": {"message": "¡Dirección copiada!"}, "addressMismatch": {"message": "La dirección del sitio no coincide"}, "addressMismatchOriginal": {"message": "URL actual: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Versión de Punycode: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "<PERSON><PERSON><PERSON>"}, "advancedBaseGasFeeToolTip": {"message": "Cuando su transacción se incluya en el bloque, se reembolsará cualquier diferencia entre su tarifa base máxima y la tarifa base real. El importe total se calcula como tarifa base máxima (en GWEI) * límite de gas."}, "advancedDetailsDataDesc": {"message": "Datos"}, "advancedDetailsHexDesc": {"message": "Hex"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Este es el número de transacción de una cuenta. El nonce para la primera transacción es 0 y aumenta en orden secuencial."}, "advancedGasFeeDefaultOptIn": {"message": "Guarde estos valores como mis valores por defecto para la red de $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Tarifa de gas avanzada"}, "advancedGasPriceTitle": {"message": "Precio del gas"}, "advancedPriorityFeeToolTip": {"message": "La tarifa de prioridad (también llamada “propina del minero”) va directamente a los mineros para incentivarlos a priorizar su transacción."}, "airDropPatternDescription": {"message": "El historial en cadena del token revela instancias anteriores de actividades sospechosas de airdrop."}, "airDropPatternTitle": {"message": "Patrón de airdrop"}, "airgapVault": {"message": "Bóveda AirGap"}, "alert": {"message": "<PERSON><PERSON><PERSON>"}, "alertAccountTypeUpgradeMessage": {"message": "Está actualizando su cuenta a una cuenta inteligente. Mantendrá la misma dirección de cuenta y accederá a transacciones más rápidas y tarifas de red más bajas. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Tipo de cuenta"}, "alertActionBuyWithNativeCurrency": {"message": "Comprar $1"}, "alertActionUpdateGas": {"message": "Actualizar el límite de gas"}, "alertActionUpdateGasFee": {"message": "Actualizar tarifa"}, "alertActionUpdateGasFeeLevel": {"message": "Actualizar opciones de gas"}, "alertDisableTooltip": {"message": "Esto se puede modificar en \"Configuración > Alertas\""}, "alertMessageAddressMismatchWarning": {"message": "A veces, los atacantes imitan sitios web haciendo pequeños cambios en la dirección del sitio. Asegúrese de estar interactuando con el sitio deseado antes de continuar."}, "alertMessageChangeInSimulationResults": {"message": "Se actualizaron los cambios estimados para esta transacción. Revíselos detenidamente antes de proceder."}, "alertMessageFirstTimeInteraction": {"message": "Está interactuando con esta dirección por primera vez. Asegúrese de que sea correcta antes de continuar."}, "alertMessageGasEstimateFailed": {"message": "No podemos proporcionar una tarifa exacta y esta estimación podría ser alta. Le sugerimos que ingrese un límite de gas personalizado, pero existe el riesgo de que la transacción aún falle."}, "alertMessageGasFeeLow": {"message": "Al elegir una tarifa baja, tenga en cuenta que las transacciones serán más lentas y los tiempos de espera más largos. Para transacciones más rápidas, elija las opciones de tarifa de mercado o agresiva."}, "alertMessageGasTooLow": {"message": "Para continuar con esta transacción, deberá aumentar el límite de gas a 21000 o más."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "No tienes suficiente $1 en tu cuenta para pagar las tarifas de red."}, "alertMessageNetworkBusy": {"message": "Los precios del gas son altos y las estimaciones son menos precisas."}, "alertMessageNoGasPrice": {"message": "No podemos seguir adelante con esta transacción hasta que actualice manualmente la tarifa."}, "alertMessageSignInDomainMismatch": {"message": "El sitio que realiza la solicitud no es el sitio en el que está iniciando sesión. Esto podría ser un intento de robar sus credenciales de inicio de sesión."}, "alertMessageSignInWrongAccount": {"message": "Este sitio le pide que inicie sesión con la cuenta incorrecta."}, "alertModalAcknowledge": {"message": "Soy consciente del riesgo y aun así deseo continuar"}, "alertModalDetails": {"message": "Detalles de la alerta"}, "alertModalReviewAllAlerts": {"message": "Revisar todas las alertas"}, "alertReasonChangeInSimulationResults": {"message": "Los resultados cambiaron"}, "alertReasonFirstTimeInteraction": {"message": "1.ª interacción"}, "alertReasonGasEstimateFailed": {"message": "<PERSON><PERSON><PERSON> inexacta"}, "alertReasonGasFeeLow": {"message": "Velocidad baja"}, "alertReasonGasTooLow": {"message": "Límite de gas bajo"}, "alertReasonInsufficientBalance": {"message": "Fondos insuficientes"}, "alertReasonNetworkBusy": {"message": "La red está ocupada"}, "alertReasonNoGasPrice": {"message": "Estimación de tarifa no disponible"}, "alertReasonPendingTransactions": {"message": "Transacción pendiente"}, "alertReasonSignIn": {"message": "Solicitud de inicio de sesión sospechosa"}, "alertReasonWrongAccount": {"message": "<PERSON><PERSON><PERSON>a"}, "alertSelectedAccountWarning": {"message": "Esta solicitud es para una cuenta distinta a la seleccionada en su monedero. Para utilizar otra cuenta, conéctela al sitio."}, "alerts": {"message": "<PERSON><PERSON><PERSON>"}, "all": {"message": "Todo"}, "allNetworks": {"message": "Todas las redes"}, "allPermissions": {"message": "Todos los permisos"}, "allTimeHigh": {"message": "Punto más alto"}, "allTimeLow": {"message": "Punto más bajo"}, "allowNotifications": {"message": "Permitir notificaciones"}, "allowWithdrawAndSpend": {"message": "Permitir que se retire $1 y gastar hasta el siguiente importe:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Importe"}, "amountReceived": {"message": "<PERSON><PERSON> recibido"}, "amountSent": {"message": "Monto enviado"}, "andForListItems": {"message": "$1, y $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 y $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "El criptomonedero más confiable del mundo", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Solicitar"}, "approve": {"message": "Aprobar límite de gastos"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "Aumentar el límite de gasto de $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Aprobar límite de gasto $1", "description": "The token symbol that is being approved"}, "approved": {"message": "Aprobado"}, "approvedOn": {"message": "Aprobado el $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Aprobado el $1 por $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "¿E<PERSON>á seguro?"}, "asset": {"message": "Activo"}, "assetChartNoHistoricalPrices": {"message": "No pudimos obtener ningún dato histórico"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "Opciones de activos"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Activos"}, "assetsDescription": {"message": "Detectar automáticamente tokens en su monedero, mostrar NFT y recibir actualizaciones de saldo de cuenta por lotes"}, "attemptToCancelSwapForFree": {"message": "Intente cancelar el intercambio de forma gratuita"}, "attributes": {"message": "Atributos"}, "attributions": {"message": "Atribuciones"}, "auroraRpcDeprecationMessage": {"message": "La URL de RPC de Infura ya no es compatible con Aurora."}, "authorizedPermissions": {"message": "Ha autorizado los siguientes permisos"}, "autoDetectTokens": {"message": "Detectar tokens automáticamente"}, "autoDetectTokensDescription": {"message": "Usamos API de terceros para detectar y mostrar nuevos tokens enviados a su monedero. Desactive esta opción si no desea que la aplicación extraiga datos de esos servicios. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Temporizador con bloqueo automático (minutos)"}, "autoLockTimeLimitDescription": {"message": "Establezca el tiempo de inactividad en minutos antes de que se bloquee MetaMask."}, "average": {"message": "Promedio"}, "back": {"message": "Volver"}, "backupAndSync": {"message": "Funcionalidad básica de"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "copia de respaldo y sincronización"}, "backupAndSyncEnable": {"message": "Activar la copia de respaldo y la sincronización"}, "backupAndSyncEnableConfirmation": {"message": "Al activar la copia de respaldo y la sincronización, también se activa en $1. ¿Desea continuar?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "La copia de respaldo y la sincronización nos permiten almacenar datos cifrados para tu configuración y funciones personalizadas. Esto garantiza la misma experiencia con MetaMask en todos los dispositivos y restaura la configuración y las funciones si alguna vez necesitas reinstalar MetaMask. Esto no crea un copia de repaldo de tu frase secreta de recuperación. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Puede actualizar sus preferencias en cualquier momento en $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Configuración > Copia de respaldo y sincronización."}, "backupAndSyncFeatureAccounts": {"message": "Cuentas"}, "backupAndSyncManageWhatYouSync": {"message": "Administre lo que sincroniza"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Active lo que se sincroniza entre sus dispositivos."}, "backupAndSyncPrivacyLink": {"message": "Más información sobre cómo protegemos su privacidad"}, "backupAndSyncSlideDescription": {"message": "Realice una copia de respaldo de sus cuentas y sincronice la configuración."}, "backupAndSyncSlideTitle": {"message": "Presentamos la copia de respaldo y la sincronización"}, "backupApprovalInfo": {"message": "Este código secreto es necesario para que recupere la cartera en caso de que pierda el dispositivo, olvide su contraseña, tenga que volver a instalar MetaMask o quiera acceder a su monedero en otro dispositivo."}, "backupApprovalNotice": {"message": "Cree una copia de seguridad de su frase secreta de recuperación para mantener protegidos sus fondos y su monedero."}, "backupKeyringSnapReminder": {"message": "Asegúrese de poder acceder a cualquier cuenta creada por este Snap por si mismo antes de eliminarlo"}, "backupNow": {"message": "<PERSON><PERSON><PERSON><PERSON> ahora"}, "balance": {"message": "<PERSON><PERSON>"}, "balanceOutdated": {"message": "Es posible que el saldo esté desactualizado"}, "baseFee": {"message": "Tarifa base"}, "basic": {"message": "Básico"}, "basicConfigurationBannerTitle": {"message": "La funcionalidad básica está desactivada"}, "basicConfigurationDescription": {"message": "MetaMask ofrece funciones básicas como los detalles del token y la configuración de gas mediante servicios de Internet. Al utilizar los servicios de Internet, su dirección IP es compartida, en este caso con MetaMask. Es lo mismo que cuando visita cualquier página web. MetaMask utiliza estos datos temporalmente y nunca los vende. Puede utilizar una VPN o desactivar estos servicios, pero esto podría afectar su experiencia con MetaMask. Para saber más, consulte nuestra $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "Funcionalidad básica"}, "basicConfigurationModalCheckbox": {"message": "Entiendo y deseo continuar"}, "basicConfigurationModalDisclaimerOff": {"message": "Esto significa que no optimizará completamente su tiempo en MetaMask. Las funciones básicas (como los detalles de los tokens, la configuración óptima de gas y otras) no estarán disponibles para usted."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "Al desactivar esta opción también se deshabilitan todas las funciones dentro de $1 y $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "seguridad y privacidad, copia de respaldo y sincronización"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "notificaciones"}, "basicConfigurationModalDisclaimerOn": {"message": "Para optimizar su tiempo en MetaMask, tendrá que activar esta función. Las funciones básicas (como los detalles de los tokens, la configuración óptima de gas y otras) son importantes para la experiencia Web3."}, "basicConfigurationModalHeadingOff": {"message": "Desactivar la funcionalidad básica"}, "basicConfigurationModalHeadingOn": {"message": "Activar la funcionalidad básica"}, "bestPrice": {"message": "<PERSON><PERSON><PERSON> precio"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Esta es una versión beta. Por favor, comunique los errores $1"}, "betaMetamaskVersion": {"message": "Versión Beta de MetaMask"}, "betaTerms": {"message": "Términos de uso de beta"}, "billionAbbreviation": {"message": "mm", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "C<PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "Activo", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "Intercambiar", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "Dirección URL del explorador de bloques"}, "blockExplorerUrlDefinition": {"message": "La dirección URL que se utiliza como explorador de bloques de esta red."}, "blockExplorerView": {"message": "Ver cuenta en $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "Si continúa, todos los activos que ha registrado en Blur podrían estar en peligro."}, "blockaidAlertDescriptionMalicious": {"message": "Está interactuando con un sitio malicioso. Si continúa, perderá sus activos."}, "blockaidAlertDescriptionOpenSea": {"message": "Si continúa, todos los activos que ha incluido en OpenSea podrían estar en peligro."}, "blockaidAlertDescriptionOthers": {"message": "Si confirma esta solicitud, podría perder sus activos. Le recomendamos que cancele esta solicitud."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Está enviando sus activos a un estafador. Si continúa, perderá esos activos."}, "blockaidAlertDescriptionWithdraw": {"message": "Si confirma esta solicitud, permitirá que un estafador retire y gaste sus activos. No los podrá recuperar."}, "blockaidDescriptionApproveFarming": {"message": "Si aprueba esta solicitud, un tercero conocido por realizar estafas podría tomar todos sus activos."}, "blockaidDescriptionBlurFarming": {"message": "Si aprueba esta solicitud, algu<PERSON> puede robar sus activos enlistados en Blur."}, "blockaidDescriptionErrored": {"message": "Debido a un error, no pudimos comprobar si hay alertas de seguridad. Continúe solo si confía en todas las direcciones involucradas."}, "blockaidDescriptionMaliciousDomain": {"message": "Está interactuando con un dominio malicioso. Si aprueba esta solicitud, podría perder sus activos."}, "blockaidDescriptionMightLoseAssets": {"message": "Si aprueba esta solicitud, podría perder sus activos."}, "blockaidDescriptionSeaportFarming": {"message": "Si aprueba esta solicitud, algu<PERSON> puede robar sus activos enlistados en OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "Si aprueba esta solicitud, un tercero conocido por estafas tomará todos sus activos."}, "blockaidMessage": {"message": "Preservación de la privacidad: no se comparten datos con terceros. Disponible en Arbitrum, Avalanche, BNB Chain, la red principal de Ethereum, Linea, Optimism, Polygon, Base y Sepolia."}, "blockaidTitleDeceptive": {"message": "Esta es una solicitud engañosa"}, "blockaidTitleMayNotBeSafe": {"message": "Sea cuidadoso"}, "blockaidTitleSuspicious": {"message": "Esta es una solicitud sospechosa"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Prestado"}, "boughtFor": {"message": "Comprado para"}, "bridge": {"message": "Puente"}, "bridgeAllowSwappingOf": {"message": "Permitir acceso exacto a $1 $2 en $3 para puentear", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Aprobar $1 para puentear", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Usted está permitiendo el acceso al monto especificado, $1 $2. El contrato no tendrá acceso a fondos adicionales."}, "bridgeApprovalWarningForHardware": {"message": "Deberá permitir el acceso a $1 $2 para puentear y luego aprobar el puente a $2. Esto requerirá dos confirmaciones por separado."}, "bridgeBlockExplorerLinkCopied": {"message": "¡Se copió el enlace del explorador de bloques!"}, "bridgeCalculatingAmount": {"message": "Calculando..."}, "bridgeConfirmTwoTransactions": {"message": "Deberá confirmar 2 transacciones en su monedero físico:"}, "bridgeCreateSolanaAccount": {"message": "<PERSON><PERSON>r cuenta de Solana"}, "bridgeCreateSolanaAccountDescription": {"message": "Para cambiar a la red Solana, necesita una cuenta y una dirección receptora."}, "bridgeCreateSolanaAccountTitle": {"message": "Primero necesitará una cuenta de Solana."}, "bridgeDetailsTitle": {"message": "Detalles del puenteo", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Seleccione el monto"}, "bridgeEnterAmountAndSelectAccount": {"message": "Ingrese el monto y seleccione la cuenta de destino"}, "bridgeExplorerLinkViewOn": {"message": "Ver en $1"}, "bridgeFetchNewQuotes": {"message": "¿Obtener una nueva?"}, "bridgeFrom": {"message": "Puentear desde"}, "bridgeFromTo": {"message": "Puentear $1 $2 a $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Cualquier tarifa de red indicada en la pantalla anterior incluye ambas transacciones y se dividirá."}, "bridgeNetCost": {"message": "Costo neto"}, "bridgeQuoteExpired": {"message": "Su cotización caducó."}, "bridgeSelectDestinationAccount": {"message": "Seleccione la cuenta de destino"}, "bridgeSelectNetwork": {"message": "Seleccionar red"}, "bridgeSelectTokenAmountAndAccount": {"message": "Seleccione token, monto y cuenta de destino"}, "bridgeSelectTokenAndAmount": {"message": "Seleccione token y monto"}, "bridgeSolanaAccountCreated": {"message": "Se creó la cuenta de Solana"}, "bridgeStatusComplete": {"message": "Completada", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Fallida", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "En curso", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 recibido en $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Recibiendo $1 en $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "Se intercambió $1 por $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Intercambiando $1 por $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Térm<PERSON>s"}, "bridgeTimingMinutes": {"message": "$1 min", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Puentear hacia"}, "bridgeToChain": {"message": "Puentear hacia $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Si agregó este token manualmente, asegúrese de que conoce los riesgos para sus fondos antes de puentear."}, "bridgeTokenCannotVerifyTitle": {"message": "No podemos verificar este token."}, "bridgeTransactionProgress": {"message": "Transacción $1 de 2"}, "bridgeTxDetailsBridging": {"message": "Puentear"}, "bridgeTxDetailsDelayedDescription": {"message": "Comuníquese con"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Soporte de MetaMask"}, "bridgeTxDetailsDelayedTitle": {"message": "¿Han pasado más de 3 horas?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Estado"}, "bridgeTxDetailsTimestamp": {"message": "Marca de tiempo"}, "bridgeTxDetailsTimestampValue": {"message": "$1 a $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 en", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Tarifa total de gas"}, "bridgeTxDetailsYouReceived": {"message": "Recibió"}, "bridgeTxDetailsYouSent": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeValidationInsufficientGasMessage": {"message": "No dispone de suficiente $1 para pagar la tarifa de gas de este puente. Introduzca un monto menor o compre más $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Se necesita más $1 para gas"}, "bridging": {"message": "Puentear"}, "browserNotSupported": {"message": "Su explorador no es compatible..."}, "buildContactList": {"message": "Cree su lista de contactos"}, "builtAroundTheWorld": {"message": "MetaMask está diseñado y construido en todo el mundo."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Ocupado"}, "buyAndSell": {"message": "Comprar / vender"}, "buyMoreAsset": {"message": "Comprar más $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "<PERSON><PERSON><PERSON> ahora"}, "bytes": {"message": "Bytes"}, "canToggleInSettings": {"message": "Puede volver a activar esta notificación desde Configuración > Alertas."}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "cancelPopoverTitle": {"message": "Cancelar transacción"}, "cancelSpeedUpLabel": {"message": "Esta tarifa de gas va a $1 el original.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Para $1 una transacción, la tarifa de gas debe aumentar al menos un 10 % para que sea reconocida por la red.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Cancelado"}, "chainId": {"message": "Identificador de cadena"}, "chainIdDefinition": {"message": "El identificador de cadena que se utiliza para firmar transacciones en esta red."}, "chainIdExistsErrorMsg": {"message": "En este momento, la red $1 está utilizando este identificador de cadena."}, "chainListReturnedDifferentTickerSymbol": {"message": "Este símbolo de token no coincide con el nombre de la red o el ID de cadena ingresados. Muchos tokens populares usan símbolos similares, que los estafadores pueden usar para engañarlo y enviarles a cambio un token más valioso. Verifique todo antes de continuar."}, "chooseYourNetwork": {"message": "<PERSON><PERSON>"}, "chooseYourNetworkDescription": {"message": "Cuando usa nuestra configuración y ajustes por defecto, usamos Infura como nuestro proveedor de llamada a procedimiento remoto (RPC) por defecto para ofrecer el acceso más confiable y privado posible a los datos de Ethereum. En casos limitados, es posible que usemos otros proveedores de RPC para ofrecer la mejor experiencia a nuestros usuarios. Puede elegir su propio proveedor de RPC, pero recuerde que cualquier proveedor de RPC recibirá su dirección IP y su monedero de Ethereum para realizar transacciones. Para obtener más información sobre cómo Infura maneja los datos de las cuentas EVM, lea nuestra $1, y para las cuentas de Solana, $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "haga clic aquí"}, "chromeRequiredForHardwareWallets": {"message": "Debe usar MetaMask en Google Chrome para poder conectarse a su monedero físico."}, "circulatingSupply": {"message": "Suministro circulante"}, "clear": {"message": "Bo<PERSON>r"}, "clearActivity": {"message": "Borrar datos de actividad y nonce"}, "clearActivityButton": {"message": "Borrar datos de la pestaña de actividad"}, "clearActivityDescription": {"message": "Esto restablece el nonce de la cuenta y borra los datos de la pestaña de actividad en su monedero. Solo la cuenta actual y la red se verán afectadas. Sus saldos y transacciones entrantes no cambiarán."}, "click": {"message": "Clic"}, "clickToConnectLedgerViaWebHID": {"message": "Haga clic aquí para conectar su Ledger a través de WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "closeExtension": {"message": "Cerrar extensión"}, "closeWindowAnytime": {"message": "<PERSON>uede cerrar esta ventana en cualquier momento."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Nombre de la colección"}, "comboNoOptions": {"message": "No se encontraron opciones", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "La mayor parte del suministro del token está en manos de los principales poseedores del token, lo que plantea un riesgo de manipulación centralizada de precios"}, "concentratedSupplyDistributionTitle": {"message": "Distribución de suministro concentrado"}, "configureSnapPopupDescription": {"message": "Ahora está saliendo de MetaMask para configurar este snap."}, "configureSnapPopupInstallDescription": {"message": "Ahora está saliendo de MetaMask para instalar este snap."}, "configureSnapPopupInstallTitle": {"message": "Instalar snap"}, "configureSnapPopupLink": {"message": "Haga clic para continuar:"}, "configureSnapPopupTitle": {"message": "Configurar snap"}, "confirm": {"message": "Confirmar"}, "confirmAccountTypeSmartContract": {"message": "Cuenta inteligente"}, "confirmAccountTypeStandard": {"message": "Cuenta estándar"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Soy consciente de las alertas y aun así deseo continuar"}, "confirmAlertModalAcknowledgeSingle": {"message": "Soy consciente de la alerta y aun así deseo continuar"}, "confirmFieldPaymaster": {"message": "<PERSON><PERSON><PERSON> pagada por"}, "confirmFieldTooltipPaymaster": {"message": "La tarifa de esta transacción la pagará el contrato inteligente del pagador."}, "confirmGasFeeTokenBalance": {"message": "Saldo:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Fondos insuficientes"}, "confirmGasFeeTokenMetaMaskFee": {"message": "Incluye tarifa de $1"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "MetaMask está complementando el saldo para completar esta transacción."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Paga la tarifa de red con el saldo de tu billetera."}, "confirmGasFeeTokenModalPayETH": {"message": "Pagar con ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Pagar con otros tokens"}, "confirmGasFeeTokenModalTitle": {"message": "Seleccione un token"}, "confirmGasFeeTokenToast": {"message": "Está pagando esta tarifa de red con $1"}, "confirmGasFeeTokenTooltip": {"message": "Esto se paga a la red para procesar tu transacción. Incluye una comisión de $1 de MetaMask para tokens que no sean ETH o ETH prefinanciados."}, "confirmInfoAccountNow": {"message": "<PERSON><PERSON>"}, "confirmInfoSwitchingTo": {"message": "Cambiando a"}, "confirmNestedTransactionTitle": {"message": "Transacción $1"}, "confirmPassword": {"message": "Confirmar con<PERSON>"}, "confirmRecoveryPhrase": {"message": "Confirmar frase secreta de recuperación"}, "confirmSimulationApprove": {"message": "Aprueba"}, "confirmTitleAccountTypeSwitch": {"message": "Actualización de cuenta"}, "confirmTitleApproveTransactionNFT": {"message": "Solicitud de retiro"}, "confirmTitleDeployContract": {"message": "Implementar un contrato"}, "confirmTitleDescApproveTransaction": {"message": "Este sitio solicita permiso para retirar sus NFT"}, "confirmTitleDescDelegationRevoke": {"message": "Está volviendo a una cuenta estándar (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Está cambiando a una cuenta inteligente"}, "confirmTitleDescDeployContract": {"message": "Este sitio quiere que implemente un contrato"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Este sitio solicita permiso para retirar sus tokens"}, "confirmTitleDescPermitSignature": {"message": "Este sitio solicita permiso para gastar sus tokens."}, "confirmTitleDescSIWESignature": {"message": "Un sitio quiere que inicie sesión para demostrar que es el propietario de esta cuenta."}, "confirmTitleDescSign": {"message": "Revise los detalles de la solicitud antes de confirmar."}, "confirmTitlePermitTokens": {"message": "Solicitud de límite de gasto"}, "confirmTitleRevokeApproveTransaction": {"message": "Eliminar permiso"}, "confirmTitleSIWESignature": {"message": "Solicitud de inicio de sesión"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Eliminar permiso"}, "confirmTitleSignature": {"message": "Solicitud de firma"}, "confirmTitleTransaction": {"message": "Solicitud de transacción"}, "confirmationAlertDetails": {"message": "Para proteger sus activos, le sugerimos que rechace la solicitud."}, "confirmationAlertModalTitleDescription": {"message": "Sus activos podrían estar en riesgo"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confusableUnicode": {"message": "“$1” es similar a “$2”."}, "confusableZeroWidthUnicode": {"message": "Se encontró un carácter de ancho cero."}, "confusingEnsDomain": {"message": "Se detectó un carácter que puede confundirse con otro similar en el nombre de ENS. Verifique el nombre de ENS para evitar una posible estafa."}, "connect": {"message": "Conectar"}, "connectAccount": {"message": "Conectar cuenta"}, "connectAccountOrCreate": {"message": "Conectar cuenta o crear nueva"}, "connectAccounts": {"message": "Conectar cuentas"}, "connectAnAccountHeader": {"message": "Conectar una cuenta"}, "connectManually": {"message": "Conectarse manualmente al sitio actual"}, "connectMoreAccounts": {"message": "Conectar más cuentas"}, "connectSnap": {"message": "Conectar $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "Conectarse con MetaMask"}, "connectedAccounts": {"message": "Cuentas conectadas"}, "connectedAccountsDescriptionPlural": {"message": "Tiene $1 cuentas conectadas a este sitio.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Tiene 1 cuenta conectada a este sitio."}, "connectedAccountsEmptyDescription": {"message": "MetaMask no está conectado a este sitio. Para conectarse a un sitio de Web3, busque el botón de conexión en su sitio."}, "connectedAccountsListTooltip": {"message": "$1 puede ver el saldo de la cuenta, la dirección, la actividad y sugerir transacciones para aprobar para las cuentas conectadas.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Se actualizaron las cuentas conectadas"}, "connectedSites": {"message": "Sitios conectados"}, "connectedSitesAndSnaps": {"message": "Sitios conectados y Snaps"}, "connectedSitesDescription": {"message": "$1 está conectado a estos sitios. Pueden ver la dirección de su cuenta.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 no está conectado a ningún sitio.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMask está conectado a este sitio, pero aún no hay cuentas conectadas"}, "connectedSnaps": {"message": "Snaps conectados"}, "connectedWithAccount": {"message": "$1 cuentas conectadas", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Conectado con $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 redes conectadas", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Conectado con $1", "description": "$1 represents network name"}, "connecting": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "connectingTo": {"message": "Estableciendo conexión a $1"}, "connectingToDeprecatedNetwork": {"message": "'$1' se está eliminando gradualmente y es posible que no funcione. Pruebe con otra red."}, "connectingToGoerli": {"message": "Estableciendo conexión a la red de prueba Goerli"}, "connectingToLineaGoerli": {"message": "Conectando a la red de prueba Linea Goerli"}, "connectingToLineaMainnet": {"message": "Estableciendo conexión a la red principal de Linea"}, "connectingToLineaSepolia": {"message": "Estableciendo conexión a la red de prueba Linea Sepolia"}, "connectingToMainnet": {"message": "Estableciendo conexión a la red principal de Ethereum"}, "connectingToSepolia": {"message": "Conectando a la red de prueba Sepolia"}, "connectionDescription": {"message": "Conecta este sitio web con MetaMask"}, "connectionFailed": {"message": "Conexión fallida"}, "connectionFailedDescription": {"message": "Error al obtener $1, verifique su red y vuelva a intentarlo.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Para conectarse a un sitio, seleccione el botón de conexión. MetaMask solo puede conectarse a sitios Web3."}, "connectionRequest": {"message": "Solicitud de conexión"}, "contactUs": {"message": "Cont<PERSON><PERSON><PERSON>s"}, "contacts": {"message": "Contactos"}, "contentFromSnap": {"message": "Contenido de $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contract": {"message": "Contrato"}, "contractAddress": {"message": "Dirección de contrato"}, "contractAddressError": {"message": "Está enviando tokens a la dirección de contrato del token. Esto puede provocar la pérdida de los tokens."}, "contractDeployment": {"message": "Implementación de contrato"}, "contractInteraction": {"message": "Interacción con el contrato"}, "convertTokenToNFTDescription": {"message": "Hemos detectado que este activo es un NFT. MetaMask ahora tiene soporte nativo completo para NFT. ¿Quiere eliminarlo de su lista de tokens y añadirlo como un NFT?"}, "convertTokenToNFTExistDescription": {"message": "Hemos detectado que este recurso se ha agregado como NFT. ¿Quiere eliminarlo de su lista de tokens?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Copiado."}, "copyAddress": {"message": "Copiar dirección al Portapapeles"}, "copyAddressShort": {"message": "<PERSON><PERSON><PERSON>"}, "copyPrivateKey": {"message": "Copiar clave privada"}, "copyToClipboard": {"message": "Copiar al Portapapeles"}, "copyTransactionId": {"message": "Copiar ID de transacción"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "createNewAccountHeader": {"message": "<PERSON><PERSON>r una nueva cuenta"}, "createPassword": {"message": "<PERSON><PERSON><PERSON> con<PERSON>"}, "createSnapAccountDescription": {"message": "$1 quiere agregar una nueva cuenta a MetaMask."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON> cuenta"}, "createSolanaAccount": {"message": "<PERSON><PERSON>r cuenta de Solana"}, "creatorAddress": {"message": "Dirección del creador"}, "crossChainSwapsLink": {"message": "Intercambie entre redes con MetaMask Portfolio"}, "crossChainSwapsLinkNative": {"message": "Intercambie entre redes con Bridge"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "Moneda"}, "currencyRateCheckToggle": {"message": "Mostrar saldo y verificador de precios de tokens"}, "currencyRateCheckToggleDescription": {"message": "Utilizamos API de $1 y $2 para mostrar su saldo y el precio del token. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Símbolo de moneda"}, "currencySymbolDefinition": {"message": "El símbolo bursátil que se muestra para la moneda de esta red."}, "currentAccountNotConnected": {"message": "La cuenta actual no está conectada"}, "currentExtension": {"message": "Página de extensión actual"}, "currentLanguage": {"message": "Idioma actual"}, "currentNetwork": {"message": "Red actual", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "La URL de rpc actual para esta red ha quedado obsoleta."}, "currentTitle": {"message": "Actual:"}, "currentlyUnavailable": {"message": "No disponible en esta red"}, "curveHighGasEstimate": {"message": "Gráfico de estimación de gas alto"}, "curveLowGasEstimate": {"message": "Gráfico de estimación de gas bajo"}, "curveMediumGasEstimate": {"message": "Gráfico de estimación de gas de mercado"}, "custom": {"message": "<PERSON><PERSON><PERSON>"}, "customGasSettingToolTipMessage": {"message": "Use $1 para personalizar el precio de gas. Esto puede ser confuso si no está familiarizado. Interactúe bajo su propio riesgo.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Personalizado"}, "customSpendLimit": {"message": "Límite de gastos personalizado"}, "customToken": {"message": "Token personalizado"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "La detección de token aún no está disponible en esta red. Importe el token de forma manual y asegúrese de que confía en él. Más información sobre $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Cualquiera puede crear un token, incluida la creación de versiones falsas de tokens existentes. Aprenda más sobre $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Asegúrese de confiar en un token antes de agregarlo. Aprenda cómo evitar $1. También puede habilitar la detección de tokens $2."}, "customerSupport": {"message": "atención al cliente"}, "customizeYourNotifications": {"message": "Personalice sus notificaciones"}, "customizeYourNotificationsText": {"message": "Active los tipos de notificaciones que desea recibir:"}, "dappSuggested": {"message": "Sitio sugerido"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 ha sugerido este precio.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Sitio sugerido"}, "dappSuggestedHighShortLabel": {"message": "<PERSON><PERSON> (alto)"}, "dappSuggestedShortLabel": {"message": "Sitio"}, "dappSuggestedTooltip": {"message": "$1 ha recomendado este precio.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "Oscuro"}, "data": {"message": "Datos"}, "dataCollectionForMarketing": {"message": "Recopilación de datos para marketing"}, "dataCollectionForMarketingDescription": {"message": "Usaremos MetaMetrics para saber cómo interactúa con nuestras comunicaciones de marketing. Es posible que compartamos noticias relevantes (como características del producto y otros materiales)."}, "dataCollectionWarningPopoverButton": {"message": "Bien"}, "dataCollectionWarningPopoverDescription": {"message": "Desactivó la recopilación de datos para nuestros fines de marketing. Esto solo se aplica a este dispositivo. Si utiliza MetaMask en otros dispositivos, asegúrese de desactivarlo allí también."}, "dataUnavailable": {"message": "datos no disponibles"}, "dateCreated": {"message": "Fecha de creación"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Opciones de compra con tarjeta de débito o crédito"}, "decimal": {"message": "Decimales del token"}, "decimalsMustZerotoTen": {"message": "Los decimales deben ser al menos 0 y no más de 36."}, "decrypt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "decryptCopy": {"message": "<PERSON><PERSON><PERSON> mensaje cifrado"}, "decryptInlineError": {"message": "Este mensaje no se puede descifrar debido al error: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 quisiera leer este mensaje para completar la acción", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "decryptRequest": {"message": "<PERSON><PERSON><PERSON><PERSON> solicitud"}, "defaultRpcUrl": {"message": "URL RPC por defecto"}, "defaultSettingsSubTitle": {"message": "MetaMask utiliza la configuración por defecto para equilibrar mejor la seguridad y la facilidad de uso. Cambie esta configuración para aumentar aún más su privacidad."}, "defaultSettingsTitle": {"message": "Configuración de privacidad por defecto"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Vuelve a visitarnos más tarde."}, "defiTabErrorTitle": {"message": "No pudimos cargar esta página."}, "delete": {"message": "Eliminar"}, "deleteContact": {"message": "Eliminar contacto"}, "deleteMetaMetricsData": {"message": "Eliminar datos de MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "Esto eliminará los datos históricos de MetaMetrics asociados con su uso en este dispositivo. Su monedero y cuentas permanecerán exactamente como están ahora después de que se eliminen estos datos. Este proceso puede tardar hasta 30 días. Consulte nuestra $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Esta solicitud no se puede ejecutar en este momento por un problema con el servidor del sistema de análisis. Vuelva a intentarlo más tarde"}, "deleteMetaMetricsDataErrorTitle": {"message": "No podemos eliminar estos datos en este momento"}, "deleteMetaMetricsDataModalDesc": {"message": "Estamos a punto de eliminar todos sus datos de MetaMetrics. ¿Está seguro?"}, "deleteMetaMetricsDataModalTitle": {"message": "¿Eliminar datos de MetaMetrics?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Usted inició esta acción el $1. Este proceso puede tardar hasta 30 días. Consulte la $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "Si elimina esta red, deber<PERSON> volver a agregarla para ver sus activos en esta red"}, "deleteNetworkTitle": {"message": "¿Eliminar la red de $1?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Deposite criptomonedas desde otra cuenta con una dirección de monedero o un código QR."}, "deprecatedGoerliNtwrkMsg": {"message": "Debido a las actualizaciones del sistema Ethereum, la red de prueba Goerli se eliminará pronto."}, "deprecatedNetwork": {"message": "Esta red está en desuso"}, "deprecatedNetworkButtonMsg": {"message": "Entendido"}, "deprecatedNetworkDescription": {"message": "La red a la que está intentando conectarse ya no es compatible con Metamask. $1"}, "description": {"message": "Descripción"}, "descriptionFromSnap": {"message": "Descripción de $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "No se encontraron cuentas elegibles"}, "destinationAccountPickerNoMatching": {"message": "No se encontraron cuentas coincidentes"}, "destinationAccountPickerReceiveAt": {"message": "Recibir en"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Dirección receptora o ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Dirección receptora"}, "destinationTransactionIdLabel": {"message": "ID de transacción de destino", "description": "Label for the destination transaction ID field."}, "details": {"message": "Detalles"}, "developerOptions": {"message": "Opciones de desarrollador"}, "disabledGasOptionToolTipMessage": {"message": "“$1” está desactivado porque no cumple el mínimo de un aumento del 10 % respecto a la tarifa de gas original.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Desconectar"}, "disconnectAllAccounts": {"message": "Desconectar todas las cuentas"}, "disconnectAllAccountsConfirmationDescription": {"message": "¿Está seguro de que se quiere desconectar? Podría perder la funcionalidad del sitio."}, "disconnectAllAccountsText": {"message": "cuentas"}, "disconnectAllDescriptionText": {"message": "Si se desconecta de este sitio, tendrá que volver a conectar sus cuentas y redes para volver a utilizarlo."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "Esto lo desconectará de este sitio"}, "disconnectPrompt": {"message": "Desconectar $1"}, "disconnectThisAccount": {"message": "Desconectar esta cuenta"}, "disconnectedAllAccountsToast": {"message": "Todas las cuentas desconectadas de $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 desconectada de $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Descubrir"}, "discoverSnaps": {"message": "Descubrir Snaps", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "dismissReminderDescriptionField": {"message": "Active esta opción para ignorar el recordatorio de respaldo de la frase de recuperación. Le recomendamos que respalde la frase secreta de recuperación para evitar la pérdida de fondos."}, "dismissReminderField": {"message": "Ignorar el recordatorio de respaldo de la frase de recuperación"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Active esta opción para que ya no aparezca la sugerencia \"Cambiar a cuenta inteligente\" en ninguna cuenta. Las cuentas inteligentes permiten transacciones más rápidas, tarifas de red más bajas y mayor flexibilidad de pago."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "Descartar la sugerencia \"Cambiar a cuenta inteligente\""}, "displayNftMedia": {"message": "Mostrar medios NFT"}, "displayNftMediaDescription": {"message": "Mostrar los medios y datos NFT expone su dirección IP a OpenSea u otros terceros. Esto puede permitir que los atacantes asocien su dirección IP con su dirección Ethereum. La detección automática de NFT se basa en esta configuración y no estará disponible cuando esté desactivada."}, "doNotShare": {"message": "No comparta esto con nadie"}, "domain": {"message": "<PERSON>inio"}, "done": {"message": "<PERSON><PERSON>"}, "dontShowThisAgain": {"message": "No volver a mostrar"}, "downArrow": {"message": "flecha hacia abajo"}, "downloadGoogleChrome": {"message": "Descargar Google Chrome"}, "downloadNow": {"message": "<PERSON><PERSON><PERSON> ahora"}, "downloadStateLogs": {"message": "Descargar registros de estado"}, "dragAndDropBanner": {"message": "Puede arrastrar redes para reordenarlas. "}, "dropped": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "duplicateContactTooltip": {"message": "Este nombre de contacto coincide con una cuenta o contacto existente"}, "duplicateContactWarning": {"message": "Tiene contactos duplicados"}, "durationSuffixDay": {"message": "D", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "H", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "M", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "S", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "S", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "A", "description": "Shortened form of 'year'"}, "earn": {"message": "G<PERSON><PERSON>"}, "edit": {"message": "<PERSON><PERSON>"}, "editANickname": {"message": "<PERSON><PERSON> alias"}, "editAccounts": {"message": "<PERSON><PERSON> cuentas"}, "editAddressNickname": {"message": "Editar apodo de dirección"}, "editCancellationGasFeeModalTitle": {"message": "Editar tarifa de cancelación de gas"}, "editContact": {"message": "<PERSON><PERSON>"}, "editGasFeeModalTitle": {"message": "Editar tarifa de gas"}, "editGasLimitOutOfBounds": {"message": "El límite de gas debe ser al menos $1"}, "editGasLimitOutOfBoundsV2": {"message": "El límite de gas debe ser superior a $1 e inferior a $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "El límite de gas es el máximo de unidades de gas que está dispuesto a utilizar. Las unidades de gas son un multiplicador de la \"Tarifa de prioridad máxima\" y de la \"Tarifa máxima\"."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "La tarifa base máxima no puede ser inferior a la tarifa de prioridad"}, "editGasMaxBaseFeeHigh": {"message": "La tarifa base máxima es más alta de lo necesario"}, "editGasMaxBaseFeeLow": {"message": "La tarifa base máxima es baja para las condiciones actuales de la red"}, "editGasMaxFeeHigh": {"message": "La tarifa base máxima es más alta de lo necesario"}, "editGasMaxFeeLow": {"message": "Tarifa máxima demasiado baja para las condiciones de red"}, "editGasMaxFeePriorityImbalance": {"message": "La tarifa base máxima no puede ser inferior a la tarifa de prioridad máxima"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "La tarifa de prioridad máxima debe ser superior a 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "La tarifa de prioridad debe ser superior a 0."}, "editGasMaxPriorityFeeHigh": {"message": "La tarifa de prioridad máxima es más alta de lo necesario. Es posible que pague más de lo necesario."}, "editGasMaxPriorityFeeHighV2": {"message": "La tarifa de prioridad es más alta de lo necesario. Es posible que pague más de lo necesario"}, "editGasMaxPriorityFeeLow": {"message": "La tarifa de prioridad máxima es baja para las condiciones actuales de la red"}, "editGasMaxPriorityFeeLowV2": {"message": "La tarifa de prioridad es baja para las condiciones actuales de la red"}, "editGasPriceTooLow": {"message": "El precio del gas debe ser superior a 0"}, "editGasPriceTooltip": {"message": "Esta red requiere un campo \"Precio del gas\" cuando se envía una transacción. El precio del gas es la cantidad que se pagará por unidad de gas."}, "editGasSubTextFeeLabel": {"message": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON>:"}, "editGasTitle": {"message": "Editar prioridad"}, "editGasTooLow": {"message": "Se desconoce el tiempo de procesamiento"}, "editInPortfolio": {"message": "Editar en Portfolio"}, "editNetworkLink": {"message": "editar la red original"}, "editNetworksTitle": {"message": "<PERSON>ar redes"}, "editNonceField": {"message": "<PERSON><PERSON> nonce"}, "editNonceMessage": {"message": "Esta es una función avanzada, úsela con precaución."}, "editPermission": {"message": "<PERSON><PERSON> permiso"}, "editPermissions": {"message": "<PERSON><PERSON> per<PERSON>"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Editar la tarifa de aceleración de gas"}, "editSpendingCap": {"message": "Editar límite de gasto"}, "editSpendingCapAccountBalance": {"message": "Saldo de la cuenta: $1 $2"}, "editSpendingCapDesc": {"message": "Ingrese la cantidad que considere conveniente que se gaste en su nombre."}, "editSpendingCapError": {"message": "El límite de gasto no puede superar $1 dígitos decimales. Elimine los dígitos decimales para continuar."}, "editSpendingCapSpecialCharError": {"message": "Ingrese solo números"}, "enableAutoDetect": {"message": " Activar autodetección"}, "enableFromSettings": {"message": " Actívela en Configuración."}, "enableSnap": {"message": "Activar"}, "enableToken": {"message": "activar $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Activado"}, "enabledNetworks": {"message": "Redes habilitadas"}, "encryptionPublicKeyNotice": {"message": "$1 quisiera su clave pública de cifrado. Al aceptar, este sitio podrá redactar mensajes cifrados para usted.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Solicitar clave pública de cifrado"}, "endpointReturnedDifferentChainId": {"message": "La URL de RPC que ingresó devolvió un ID de cadena diferente ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Actualmente, la detección mejorada de token se encuentra disponible en $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "MetaMask le permite ver los dominios de ENS directamente en la barra de direcciones de su navegador. Así es como funciona:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Tenga en cuenta que el uso de esta función expone su dirección IP a servicios de terceros de IPFS."}, "ensDomainsSettingDescriptionPart1": {"message": "MetaMask consulta el contrato ENS de Ethereum para encontrar el código conectado al nombre de ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "Si el código se vincula a IPFS, puede ver el contenido asociado a él (generalmente un sitio web)."}, "ensDomainsSettingTitle": {"message": "Mostrar dominios ENS en la barra de direcciones"}, "ensUnknownError": {"message": "Error al buscar ENS."}, "enterANameToIdentifyTheUrl": {"message": "Ingrese un nombre para identificar la URL"}, "enterChainId": {"message": "Ingrese el ID de cadena"}, "enterMaxSpendLimit": {"message": "Escribir límite máximo de gastos"}, "enterNetworkName": {"message": "Ingrese el nombre de la red"}, "enterOptionalPassword": {"message": "Ingrese la contraseña opcional"}, "enterPasswordContinue": {"message": "Escribir contraseña para continuar"}, "enterRpcUrl": {"message": "Ingrese el URL de RPC"}, "enterSymbol": {"message": "Ingrese el símbolo"}, "enterTokenNameOrAddress": {"message": "Ingrese el nombre del token o pegue la dirección"}, "enterYourPassword": {"message": "Ingrese su contraseña"}, "errorCode": {"message": "Código: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Error al obtener la lista de cadenas seguras, por favor continúe con precaución."}, "errorMessage": {"message": "Mensaje: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Código: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Comuníquese con soporte", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Describa lo sucedido", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "No se puede mostrar su información. No se preocupe, su monedero y sus fondos están a salvo.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "<PERSON><PERSON><PERSON>", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Describa lo sucedido", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Compartir detalles como la forma de reproducir el error nos ayudará a solucionar el problema.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "¡Gracias! Lo revisaremos en breve.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMask encontró un error", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "Vuelva a intentarlo", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Pila:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Error al conectarse a la red personalizada."}, "errorWithSnap": {"message": "Error con $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "<PERSON><PERSON><PERSON> estima<PERSON>"}, "estimatedFeeTooltip": {"message": "Monto pagado para procesar la transacción en la red."}, "ethGasPriceFetchWarning": {"message": "Se muestra el precio del gas de respaldo, ya que el servicio para calcular el precio del gas principal no se encuentra disponible en este momento."}, "ethereumProviderAccess": {"message": "Otorgar acceso al proveedor de Ethereum a $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Dirección pública de Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Ver cuenta en Etherscan"}, "etherscanViewOn": {"message": "Ver en Etherscan"}, "existingChainId": {"message": "La información que ha ingresado está asociada con un ID de cadena existente."}, "expandView": {"message": "Expandir vista"}, "experimental": {"message": "Experimental"}, "exploreweb3": {"message": "Explorar la Web3"}, "exportYourData": {"message": "Exporte sus datos"}, "exportYourDataButton": {"message": "<PERSON><PERSON><PERSON>"}, "exportYourDataDescription": {"message": "Puede exportar datos como sus contactos y preferencias."}, "extendWalletWithSnaps": {"message": "Explore los Snaps creados por la comunidad para personalizar su experiencia web3", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "Cuenta externa"}, "externalExtension": {"message": "Extensión externa"}, "externalNameSourcesSetting": {"message": "Apodos propuestos"}, "externalNameSourcesSettingDescription": {"message": "Obtendremos apodos propuestos para las direcciones con las que interactúa de fuentes de terceros como Etherscan, Infura y Lens Protocol. Estas fuentes podrán ver esas direcciones y su dirección IP. La dirección de su cuenta no estará expuesta a terceros."}, "failed": {"message": "Con errores"}, "failedToFetchChainId": {"message": "No se pudo capturar el id. de cadena. ¿La dirección URL de RPC es correcta?"}, "failover": {"message": "Conmutación por error"}, "failoverRpcUrl": {"message": "URL de RPC de conmutación por error"}, "failureMessage": {"message": "Se produjo un error y no pudimos completar la acción"}, "fast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "feeDetails": {"message": "Detalles de la tarifa"}, "fileImportFail": {"message": "¿No funciona la importación del archivo? Haga clic aquí.", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "le recomendamos que desinstale esta extensión", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask es para que los desarrolladores experimenten con nuevas API inestables. A menos que usted sea desarrollador o probador beta, $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "No garantizamos la seguridad o estabilidad de esta extensión. Las nuevas API ofrecidas por Flask no están protegidas contra los ataques de phishing, lo que significa que cualquier sitio o snap que requiera Flask podría ser un intento malicioso de robar sus activos.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Todas las API de Flask son experimentales. Se pueden cambiar o eliminadar sin previo aviso o pueden permanecer en Flask indefinidamente sin ser migradas a MetaMask estable. Úselas bajo su propia responsabilidad.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Asegúrese de deshabilitar su extensión MetaMask recurrente cuando use Flask.", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "Acepto los riesgos", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "La cantidad de tokens debe ser un número entero"}, "followUsOnTwitter": {"message": "Síganos en Twitter"}, "forbiddenIpfsGateway": {"message": "Puerta de enlace de IPFS prohibida: especifique una puerta de enlace de CID"}, "forgetDevice": {"message": "Olvidar este dispositivo"}, "forgotPassword": {"message": "¿Olvidó su contraseña?"}, "form": {"message": "formulario"}, "from": {"message": "De"}, "fromAddress": {"message": "De: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "De las listas de tóken: $1"}, "function": {"message": "Función: $1"}, "fundingMethod": {"message": "Método de financiación"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Editar tarifa de gas sugerida"}, "gasDisplayDappWarning": {"message": "Esta tarifa de gas ha sido sugerida por $1. Anularla puede causar un problema con su transacción. Comuníquese con $1 si tiene preguntas.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Tarifa de gas"}, "gasLimit": {"message": "Límite de gas"}, "gasLimitRecommended": {"message": "El límite de gas recomendado es de $1. Si el límite de gas está por debajo de ese nivel, puede fallar."}, "gasLimitTooLow": {"message": "El límite de gas debe ser al menos 21 000"}, "gasLimitV2": {"message": "Límite de gas"}, "gasOption": {"message": "Opción de gas"}, "gasPriceExcessive": {"message": "Su tarifa de gas es demasiado alta. Considere reducir el importe."}, "gasPriceFetchFailed": {"message": "Se produjo un error al calcular el precio del gas debido a una falla en la red."}, "gasTimingHoursShort": {"message": "$1 horas", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "<PERSON><PERSON>"}, "gasTimingMinutesShort": {"message": "$1 min", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 s", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gas usado"}, "general": {"message": "General"}, "generalCameraError": {"message": "No hemos podido acceder a su cámara. Por favor, inténtelo de nuevo."}, "generalCameraErrorTitle": {"message": "Algo salió mal..."}, "generalDescription": {"message": "Sincronizar configuraciones entre dispositivos, seleccionar preferencias de red y dar seguimiento a los datos de tokens"}, "genericExplorerView": {"message": "Ver cuenta en $1"}, "goToSite": {"message": "<PERSON>r al sitio"}, "goerli": {"message": "Red de prueba Goerli"}, "gotIt": {"message": "Entendido"}, "grantExactAccess": {"message": "<PERSON><PERSON><PERSON> acceso exacto"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Hardware"}, "hardwareWalletConnected": {"message": "Monedero físico conectado"}, "hardwareWalletLegacyDescription": {"message": "(antiguo)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Asegúrese de que su $1 esté conectado y seleccione la aplicación de Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "Habilite los “datos de contrato inteligente” o la “firma ciega” en su dispositivo de $1."}, "hardwareWalletSubmissionWarningTitle": {"message": "Antes de hacer clic en Enviar:"}, "hardwareWalletSupportLinkConversion": {"message": "Haga clic aquí"}, "hardwareWallets": {"message": "Conectar un monedero físico"}, "hardwareWalletsInfo": {"message": "Las integraciones de monedero físico utilizan llamadas API a servidores externos, que pueden ver su dirección IP y las direcciones de contrato inteligente con las que interactúa."}, "hardwareWalletsMsg": {"message": "Seleccione un monedero físico que desee usar con MetaMask."}, "here": {"message": "aquí", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Datos hexadecimales"}, "hiddenAccounts": {"message": "Cuentas ocultas"}, "hide": {"message": "Ocultar"}, "hideAccount": {"message": "Ocultar cuenta"}, "hideAdvancedDetails": {"message": "O<PERSON>lta<PERSON> de<PERSON><PERSON> a<PERSON>"}, "hideSentitiveInfo": {"message": "Ocultar información confidencial"}, "hideTokenPrompt": {"message": "¿Ocultar token?"}, "hideTokenSymbol": {"message": "Ocultar $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Ocultar tokens sin saldo"}, "high": {"message": "Agresivo"}, "highGasSettingToolTipMessage": {"message": "Alta probabilidad, incluso en mercados volátiles. Use $1 para cubrir aumentos repentinos en el tráfico de la red debido a cosas como drops de NFT populares.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "alto"}, "highestCurrentBid": {"message": "Oferta actual más alta"}, "highestFloorPrice": {"message": "<PERSON><PERSON> mínimo más alto"}, "history": {"message": "Historial"}, "holdToRevealContent1": {"message": "Su frase secreta de recuperación proporciona $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "acceso completo a su monedero y fondos.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "No comparta esto con nadie. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "El soporte de MetaMask no solicitará esto,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "pero los defraudadores sí.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Su clave privada proporciona $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "acceso completo a su monedero y a sus fondos.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "mantenga presionado para revelar el círculo bloqueado"}, "holdToRevealPrivateKey": {"message": "Mantenga presionado para revelar su clave privada"}, "holdToRevealPrivateKeyTitle": {"message": "Mantenga segura su clave privada"}, "holdToRevealSRP": {"message": "Mantenga presionado para revelar su SRP"}, "holdToRevealSRPTitle": {"message": "Mantenga segura su SRP"}, "holdToRevealUnlockedLabel": {"message": "mantenga presionado para revelar el círculo desbloqueado"}, "honeypotDescription": {"message": "Este token podría representar un riesgo de honeypot. Se recomienda realizar la debida diligencia antes de interactuar para evitar posibles pérdidas financieras."}, "honeypotTitle": {"message": "Honeypot"}, "howNetworkFeesWorkExplanation": {"message": "Tarifa estimada necesaria para procesar la transacción. La tarifa máxima es $1."}, "howQuotesWork": {"message": "Cómo funcionan las cotizaciones"}, "howQuotesWorkExplanation": {"message": "Esta cotización tiene la mejor rentabilidad de las cotizaciones que buscamos. Se basa en la tarifa de intercambio, que incluye las tarifas de puente y una tarifa de MetaMask del $1 %, menos las tarifas de gas. Las tarifas de gas dependen de lo ocupada que esté la red y de lo compleja que sea la transacción."}, "id": {"message": "ID"}, "ignoreAll": {"message": "<PERSON><PERSON><PERSON> todo"}, "ignoreTokenWarning": {"message": "Si oculta tokens, estos no se mostrarán en su monedero. Sin embargo, aún puede agregarlos bus<PERSON>."}, "imToken": {"message": "imToken"}, "import": {"message": "Importar", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Error al importar la cuenta."}, "importAccountErrorIsSRP": {"message": "Ha ingresado una frase secreta de recuperación (o mnemotécnica). Para importar una cuenta aquí, debe ingresar una clave privada, que es una cadena hexadecimal de 64 caracteres."}, "importAccountErrorNotAValidPrivateKey": {"message": "Esta no es una clave privada válida. Ha ingresado una cadena hexadecimal, pero debería tener 64 caracteres."}, "importAccountErrorNotHexadecimal": {"message": "Esta no es una clave privada válida. Debe ingresar una cadena hexadecimal de 64 caracteres."}, "importAccountJsonLoading1": {"message": "Considere que esta importación de JSON tomará unos minutos y congelerá su MetaMask."}, "importAccountJsonLoading2": {"message": "Nos disculpamos, y lo haremos más rápido en un futuro."}, "importAccountMsg": {"message": "Las cuentas importadas no se asociarán con su frase secreta de recuperación de MetaMask. Obtenga más información sobre las cuentas importadas"}, "importNFT": {"message": "Agregar NFT"}, "importNFTAddressToolTip": {"message": "En OpenSea, por ejemplo, en la página de NFT en Detalles, hay un un vínculo azul etiquetado como 'Dirección del contrato'. Si haces clic en esto, te llevará a la dirección del contrato en Etherscan; en la parte superior izquierda de esa página, debe haber un icono etiquetado como 'Contrato' y, a la derecha, una larga cadena de letras y números. Esta es la dirección del contrato que creó tu NFT. Haz clic en el icono de 'copiar' que aparece a la derecha de la dirección, y la tendrás en el portapapeles."}, "importNFTPage": {"message": "Importar página de NFT"}, "importNFTTokenIdToolTip": {"message": "La ID de un NFT es un identificador único, ya que no hay dos NFT iguales. Nuevamente, en OpenSea, este número se encuentra en 'Detalles'. Tome nota de ello o cópielo en su portapapeles."}, "importNWordSRP": {"message": "Tengo una frase de recuperación de $1 palabras", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Clave privada"}, "importSRPDescription": {"message": "Importe una billetera existente con su frase secreta de recuperación de 12 o 24 palabras."}, "importSRPNumberOfWordsError": {"message": "Las frases secretas de recuperación contienen 12 o 24 palabras"}, "importSRPWordError": {"message": "La palabra $1 es incorrecta o está mal escrita.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Las palabras $1 y $2 son incorrectas o están mal escritas.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Importar frase secreta de recuperación"}, "importSecretRecoveryPhraseUnknownError": {"message": "Se produjo un error desconocido."}, "importSelectedTokens": {"message": "¿Agregar los tokens seleccionados?"}, "importSelectedTokensDescription": {"message": "Únicamente los tokens seleccionados aparecerán en su monedero. Siempre podrá agregar tokens ocultos más tarde realizando una búsqueda de los mismos."}, "importTokenQuestion": {"message": "¿Desea importar el token?"}, "importTokenWarning": {"message": "Toda persona puede crear un token con cualquier nombre, incluso versiones falsas de tokens existentes. ¡Agréguelo y realice transacciones bajo su propio riesgo!"}, "importTokensCamelCase": {"message": "Importar tokens"}, "importTokensError": {"message": "No pudimos importar los tokens. Por favor, inténtelo de nuevo más tarde."}, "importWallet": {"message": "Importar monedero"}, "importWalletOrAccountHeader": {"message": "Importar un monedero o una cuenta"}, "importWalletSuccess": {"message": "Se importó la frase secreta de recuperación $1", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Importar $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "en su Configuración"}, "included": {"message": "incluido"}, "includesXTransactions": {"message": "Incluye $1 transacciones"}, "infuraBlockedNotification": {"message": "MetaMask no se pudo conectar al host de la cadena de bloques. Revise las razones posibles $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "La red confirmó la transacción inicial. Haga clic en Aceptar para volver."}, "insightsFromSnap": {"message": "Perspectivas de $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Instalar"}, "installOrigin": {"message": "Instalar origen"}, "installRequest": {"message": "<PERSON><PERSON><PERSON> a MetaMask"}, "installedOn": {"message": "Instalado en $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "<PERSON><PERSON> insuficiente."}, "insufficientFunds": {"message": "Fondos insuficientes."}, "insufficientFundsForGas": {"message": "Fondos insuficientes para el gas"}, "insufficientLockedLiquidityDescription": {"message": "La falta de liquidez adecuadamente bloqueada o quemada hace que el token sea vulnerable a retiros repentinos de liquidez, lo que podría causar inestabilidad en el mercado."}, "insufficientLockedLiquidityTitle": {"message": "Liquidez bloqueada insuficiente"}, "insufficientTokens": {"message": "Tokens insuficientes."}, "interactWithSmartContract": {"message": "Contrato inteligente"}, "interactingWith": {"message": "Interactuando con"}, "interactingWithTransactionDescription": {"message": "Este es el contrato con el que está interactuando. Protéjase de los estafadores verificando los detalles."}, "interaction": {"message": "Interacción"}, "invalidAddress": {"message": "Dirección no válida"}, "invalidAddressRecipient": {"message": "La dirección del destinatario no es válida"}, "invalidAssetType": {"message": "Este activo es un NFT y debe volver a añadirse en la página de Importar NFTs que se encuentra en la pestaña de NFTs"}, "invalidChainIdTooBig": {"message": "Identificador de cadena no válido. El identificador de cadena es demasiado grande."}, "invalidCustomNetworkAlertContent1": {"message": "Es necesario volver a especificar el id. de la cadena para la red virtual “$1”.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Para protegerlo de proveedores de red malintencionados o defectuosos, ahora se requieren id. de cadena para todas las redes personalizadas."}, "invalidCustomNetworkAlertContent3": {"message": "Vaya a Configuración > Red y especifique el id. de cadena. Puede encontrar los id. de cadena de las redes más populares en $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Red personalizada no válida"}, "invalidHexData": {"message": "Datos hexadecimales no válidos"}, "invalidHexNumber": {"message": "Número hexadecimal no válido."}, "invalidHexNumberLeadingZeros": {"message": "Número hexadecimal no válido. Quite todos los ceros iniciales."}, "invalidIpfsGateway": {"message": "Puerta de enlace de IPFS no válida: el valor debe ser una dirección URL válida"}, "invalidNumber": {"message": "Número no válido. Escriba un número decimal o un número hexadecimal con el prefijo “0x”."}, "invalidNumberLeadingZeros": {"message": "Número no válido. Quite todos los ceros iniciales."}, "invalidRPC": {"message": "Dirección URL de RPC no válida"}, "invalidSeedPhrase": {"message": "Frase secreta de recuperación no válida"}, "invalidSeedPhraseCaseSensitive": {"message": "¡Entrada inválida! La frase secreta de recuperación distingue entre mayúsculas y minúsculas."}, "ipfsGateway": {"message": "Puerta de enlace de IPFS"}, "ipfsGatewayDescription": {"message": "MetaMask utiliza servicios de terceros para mostrar imágenes de sus NFT almacenados en IPFS, mostrar información relacionada con las direcciones ENS ingresadas en la barra de direcciones de su navegador y obtener íconos para diferentes tokens. Su dirección IP puede estar expuesta a estos servicios cuando los está utilizando."}, "ipfsToggleModalDescriptionOne": {"message": "Utilizamos servicios de terceros para mostrar imágenes de sus NFT almacenados en IPFS, mostrar información relacionada con las direcciones ENS ingresadas en la barra de direcciones de su navegador y obtener íconos para diferentes tokens. Su dirección IP puede estar expuesta a estos servicios cuando los está utilizando."}, "ipfsToggleModalDescriptionTwo": {"message": "Al seleccionar Confirmar, se activa la resolución de IPFS. Puede desactivarlo en $1 en cualquier momento.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Configuración > Seguridad y privacidad"}, "isSigningOrSubmitting": {"message": "Aún se está firmando o enviando una transacción anterior"}, "jazzAndBlockies": {"message": "Jazzicons y Blockies son dos estilos distintos de íconos únicos que pueden ayudarlo a identificar rápidamente una cuenta."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "Archivo JSON", "description": "format for importing an account"}, "keyringAccountName": {"message": "Nombre de la cuenta"}, "keyringAccountPublicAddress": {"message": "Dirección pública"}, "keyringSnapRemovalResult1": {"message": "$1 eliminado $2", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "no ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Ingrese $1 para confirmar que desea eliminar este snap:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Dirección de contrato conocida."}, "knownTokenWarning": {"message": "Esta acción editará tokens que ya estén enumerados en su monedero y que se pueden usar para engañarlo. Apruebe solo si está seguro de que quiere cambiar lo que representan estos tokens. Más información sobre $1"}, "l1Fee": {"message": "Tarifa L1"}, "l1FeeTooltip": {"message": "Tarifa de gas L1"}, "l2Fee": {"message": "Tarifa L2"}, "l2FeeTooltip": {"message": "Tarifa de gas L2"}, "lastConnected": {"message": "Última conexión"}, "lastSold": {"message": "Última venta"}, "lavaDomeCopyWarning": {"message": "Por su seguridad, seleccionar este texto no está disponible en este momento."}, "layer1Fees": {"message": "Tarifas de la capa 1"}, "layer2Fees": {"message": "Tarifas de la capa 2"}, "learnHow": {"message": "Más información"}, "learnMore": {"message": "Más información"}, "learnMoreAboutGas": {"message": "¿Quiere $1 sobre gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "Obtenga más información sobre las mejores prácticas de privacidad."}, "learnMoreAboutSolanaAccounts": {"message": "Obtenga más información sobre las cuentas de Solana"}, "learnMoreKeystone": {"message": "Más información"}, "learnMoreUpperCase": {"message": "Más información"}, "learnMoreUpperCaseWithDot": {"message": "Más información."}, "learnScamRisk": {"message": "estafas y riesgos en seguridad."}, "leaveMetaMask": {"message": "¿Dejar <PERSON>?"}, "leaveMetaMaskDesc": {"message": "Está a punto de visitar un sitio fuera de MetaMask. Vuelva a verificar la URL antes de continuar."}, "ledgerAccountRestriction": {"message": "Debe usar su última cuenta antes de poder agregar una nueva."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Cierre cualquier otro software conectado a su dispositivo y haga clic aquí para actualizar."}, "ledgerConnectionInstructionHeader": {"message": "Antes de hacer clic en Confirmar:"}, "ledgerConnectionInstructionStepFour": {"message": "Habilite \"datos de contrato inteligente\" o \"firma ciega\" en su dispositivo Ledger."}, "ledgerConnectionInstructionStepThree": {"message": "Asegúrese de que su Ledger esté conectado y seleccione la aplicación Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "El dispositivo Ledger no pudo abrirse. Su Ledger podría estar conectado a otro software. Cierre Ledger Live u otras aplicaciones conectadas a su dispositivo Ledger, e intente conectarse de nuevo."}, "ledgerErrorConnectionIssue": {"message": "Vuelva a conectar su Ledger, abra la aplicación ETH e inténtelo nuevamente."}, "ledgerErrorDevicedLocked": {"message": "Su Ledger está bloqueado. Desbloquéelo y vuelve a intentarlo."}, "ledgerErrorEthAppNotOpen": {"message": "Para resolver el problema, abra la aplicación ETH en su dispositivo y vuelva a intentarlo."}, "ledgerErrorTransactionDataNotPadded": {"message": "Los datos de entrada de la transacción de Ethereum no están lo suficientemente completos."}, "ledgerLiveApp": {"message": "Aplicación de Ledger Live"}, "ledgerLocked": {"message": "No se pudo establecer la conexión con el dispositivo Ledger. Asegúrese de que el dispositivo está desbloqueado y que la aplicación de Ethereum está abierta."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Para conectar un nuevo dispositivo, desconecte el anterior."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Solo puede conectar un Ledger a la vez"}, "ledgerTimeout": {"message": "Ledger Live tardó mucho en responder o se excedió el tiempo de espera de la conexión. Asegúrese de que la aplicación de Ledger Live está abierta y que su dispositivo está desbloqueado."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "El dispositivo Ledger no se ha conectado. Si desea conectar su Ledger, haga clic de nuevo en 'Continuar' y apruebe la conexión HID", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "flecha de nivel"}, "lightTheme": {"message": "<PERSON><PERSON><PERSON>"}, "likeToImportToken": {"message": "¿Le gustaría importar este token?"}, "likeToImportTokens": {"message": "¿Le gustaría agregar estos tokens?"}, "lineaGoerli": {"message": "Red de prueba Linea Goerli"}, "lineaMainnet": {"message": "Red principal de Linea"}, "lineaSepolia": {"message": "Red de prueba Linea Sepolia"}, "link": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "linkCentralizedExchanges": {"message": "Vincule sus cuentas de Coinbase o Binance para transferir criptomonedas a MetaMask de forma gratuita."}, "links": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loadMore": {"message": "<PERSON>gar más"}, "loading": {"message": "Cargando…"}, "loadingScreenSnapMessage": {"message": "Por favor, complete la transacción en el Snap."}, "loadingTokenList": {"message": "Cargando lista de tokens"}, "localhost": {"message": "Host local 8545"}, "lock": {"message": "Bloquear"}, "lockMetaMask": {"message": "<PERSON><PERSON><PERSON>"}, "lockTimeInvalid": {"message": "El tiempo de bloqueo debe ser un número entre 0 y 10080"}, "logo": {"message": "Logo de $1", "description": "$1 is the name of the ticker"}, "low": {"message": "<PERSON><PERSON>"}, "lowEstimatedReturnTooltipMessage": {"message": "Pagará más del $1 % de su monto inicial en tarifas. Compruebe el monto que recibe y las tarifas de red."}, "lowEstimatedReturnTooltipTitle": {"message": "Costo elevado"}, "lowGasSettingToolTipMessage": {"message": "Utilice $1 para esperar un precio más bajo. Las estimaciones de tiempo son mucho menos precisas ya que los precios son algo imprevisibles.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "bajo"}, "mainnet": {"message": "Red principal de Ethereum"}, "mainnetToken": {"message": "Esta dirección coincide con una dirección conocida de token en la red principal de Ethereum. Vuelve a comprobar la dirección del contrato y la red correspondiente al token que intentas añadir."}, "makeAnotherSwap": {"message": "Crear un nuevo intercambio"}, "makeSureNoOneWatching": {"message": "Asegúrese de que nadie esté mirando", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Gestionar la configuración de privacidad por defecto"}, "manageInstitutionalWallets": {"message": "Administrar monederos institucionales"}, "manageInstitutionalWalletsDescription": {"message": "Active esta opción para habilitar los monederos institucionales."}, "manageNetworksMenuHeading": {"message": "Administrar redes"}, "managePermissions": {"message": "Administrar permis<PERSON>"}, "marketCap": {"message": "Capitalización bursátil"}, "marketDetails": {"message": "Detalles del mercado"}, "max": {"message": "Máx."}, "maxBaseFee": {"message": "Tarifa base máxima"}, "maxFee": {"message": "<PERSON><PERSON><PERSON>"}, "maxFeeTooltip": {"message": "Una tarifa máxima proporcionada para pagar la transacción."}, "maxPriorityFee": {"message": "Tarifa de prioridad máxima"}, "medium": {"message": "<PERSON><PERSON><PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Utilice $1 para un procesamiento rápido al precio actual del mercado.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "memorándum"}, "message": {"message": "Men<PERSON><PERSON>"}, "metaMaskConnectStatusParagraphOne": {"message": "Ahora tiene más control sobre las conexiones de su cuenta en MetaMask."}, "metaMaskConnectStatusParagraphThree": {"message": "Haga clic en él para administrar las cuentas conectadas."}, "metaMaskConnectStatusParagraphTwo": {"message": "El botón de estado de la conexión muestra si el sitio web que visita está conectado a la cuenta seleccionada actualmente."}, "metaMetricsIdNotAvailableError": {"message": "Como nunca ha utilizado MetaMetrics, no hay datos que eliminar aquí."}, "metadataModalSourceTooltip": {"message": "$1 está alojado en npm y $2 es el identificador único de este Snap.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "Las notificaciones del monedero no están activas actualmente."}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swaps está en mantenimiento. Vuelva a comprobarlo más tarde."}, "metamaskVersion": {"message": "Versión de MetaMask"}, "methodData": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "methodDataTransactionDesc": {"message": "Función ejecutada en base a datos de entrada decodificados."}, "methodNotSupported": {"message": "No compatible con esta cuenta."}, "metrics": {"message": "Indicadores"}, "millionAbbreviation": {"message": "m", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "verifique los detalles de la red", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Recomendamos que usted $1 antes de proceder.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Según nuestros registros, es posible que el nombre de la red no coincida correctamente con este ID de cadena."}, "mismatchedNetworkSymbol": {"message": "El símbolo de moneda enviado no coincide con lo que esperamos para este ID de cadena."}, "mismatchedRpcChainId": {"message": "El ID de cadena devuelto por la red personalizada no coincide con el ID de cadena enviado."}, "mismatchedRpcUrl": {"message": "Según nuestros registros, el valor de la URL de RPC enviado no coincide con un proveedor conocido para este ID de cadena."}, "missingSetting": {"message": "¿No puede encontrar un ajuste?"}, "missingSettingRequest": {"message": "Solicítelo aquí"}, "more": {"message": "más"}, "moreAccounts": {"message": "Más de $1 cuentas adicionales", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "Más de $1 redes adicionales", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "Más cotizaciones"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Está agregando esta red a MetaMask y otorgando permiso a este sitio para usarla."}, "multichainQuoteCardBridgingLabel": {"message": "Puenteo"}, "multichainQuoteCardQuoteLabel": {"message": "Cotización"}, "multichainQuoteCardTimeLabel": {"message": "<PERSON><PERSON>"}, "multipleSnapConnectionWarning": {"message": "$1 quiere conectarse a $2", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "Debe seleccionar al menos 1 token."}, "name": {"message": "Nombre"}, "nameAddressLabel": {"message": "Dirección", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "El nombre ya está en uso"}, "nameInstructionsNew": {"message": "Si conoce esta dirección, as<PERSON><PERSON>le un apodo para reconocerla en el futuro.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "Esta dirección tiene un apodo predeterminado, pero puede editarlo o explorar otras sugerencias.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Ya agregó un apodo para esta dirección anteriormente. Puede editar o ver otros apodos sugeridos.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "A<PERSON>do", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Quizás: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Dirección desconocida", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "Dirección reconocida", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "Dirección guardada", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Propuesto por $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Servicio de nombres de Ethereum (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "Elija un apodo...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 solicita su aprobación para:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Editar de<PERSON> de <PERSON> red"}, "nativeTokenScamWarningDescription": {"message": "El símbolo del token nativo no coincide con el símbolo esperado del token nativo para la red con el ID de cadena asociado. Ingresó $1 mientras que el símbolo del token esperado es $2. Verifique que esté conectado a la cadena correcta.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "algo más", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Símbolo de token nativo inesperado", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "¿Necesita ayuda? Comuníquese con $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Comparta su opinión"}, "needHelpLinkText": {"message": "Soporte de MetaMask"}, "needHelpSubmitTicket": {"message": "Enviar un ticket"}, "needImportFile": {"message": "Debe seleccionar un archivo para la importación.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "No se pueden enviar cantidades negativas de ETH."}, "negativeOrZeroAmountToken": {"message": "No se pueden enviar montos negativos o nulos de activos."}, "network": {"message": "Red"}, "networkChanged": {"message": "La red cambió"}, "networkChangedMessage": {"message": "Ahora está realizando transacciones en $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Detalles de la red"}, "networkFee": {"message": "Ta<PERSON>fa de red"}, "networkIsBusy": {"message": "La red está ocupada. Los precios del gas son altos y las estimaciones son menos precisas."}, "networkMenu": {"message": "Menú de red"}, "networkMenuHeading": {"message": "Seleccionar una red"}, "networkName": {"message": "Nombre de la red"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "El nombre asociado a esta red."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "Red principal OP"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Red de prueba"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Opciones de red"}, "networkPermissionToast": {"message": "Se actualizaron los permisos de la red"}, "networkProvider": {"message": "Proveed<PERSON> de red"}, "networkStatus": {"message": "Estado de la red"}, "networkStatusBaseFeeTooltip": {"message": "La tarifa base la fija la red y cambia cada 13-14 segundos. Nuestras opciones $1 y $2 dan cuenta de los aumentos repentinos.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Rango de tarifas de prioridad (también llamada “propina del minero”): esto va directamente a los mineros para incentivarlos a priorizar su transacción."}, "networkStatusStabilityFeeTooltip": {"message": "Las tarifas de gas son de $1 en relación con las últimas 72 horas.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "No podemos conectar a $1", "description": "$1 represents the network name"}, "networkURL": {"message": "Dirección URL de la red"}, "networkURLDefinition": {"message": "La dirección URL que se utilizó para acceder a esta red."}, "networkUrlErrorWarning": {"message": "A veces, los atacantes imitan sitios web haciendo pequeños cambios en la dirección del sitio. Asegúrese de estar interactuando con el sitio deseado antes de continuar. Versión de Punycode: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Redes"}, "networksSmallCase": {"message": "redes"}, "nevermind": {"message": "No es importante"}, "new": {"message": "¡Nuevo!"}, "newAccount": {"message": "Cuenta nueva"}, "newAccountNumberName": {"message": "Cuenta $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Contacto nuevo"}, "newContract": {"message": "Contrato nuevo"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Configuración > Seguridad y privacidad"}, "newNFTDetectedInImportNFTsMsg": {"message": "Para usar Opensea, para ver sus NFT, active 'Mostrar medios NFT' en $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Deje que MetaMask detecte y muestre automáticamente los NFT en su monedero."}, "newNFTsAutodetected": {"message": "Autodetección de NFT"}, "newNetworkAdded": {"message": "¡\"$1\" se añadió con éxito!"}, "newNetworkEdited": {"message": "¡\"$1\" se editó con éxito!"}, "newNftAddedMessage": {"message": "¡NFT se agregó correctamente!"}, "newPassword": {"message": "Contraseña nueva (mín. de 8 caracteres)"}, "newPrivacyPolicyActionButton": {"message": "<PERSON><PERSON>"}, "newPrivacyPolicyTitle": {"message": "Hemos actualizado nuestra política de privacidad"}, "newRpcUrl": {"message": "Nueva URL de RPC"}, "newTokensImportedMessage": {"message": "Ha importado $1 exitosamente.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token importado"}, "next": {"message": "Siguient<PERSON>"}, "nftAddFailedMessage": {"message": "No se puede agregar el NFT porque los detalles de propiedad no coinciden. Asegúrese de haber ingresado la información correcta."}, "nftAddressError": {"message": "Este token es un NFT. Añadir a $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT ya ha sido aña<PERSON>do."}, "nftAutoDetectionEnabled": {"message": "Autodetección de NFT habilitada"}, "nftDisclaimer": {"message": "Descargo de responsabilidad: MetaMask extrae el archivo multimedia de la URL de origen. A veces, el mercado en el que se acuñó el NFT cambia esta URL."}, "nftOptions": {"message": "Opciones de NFT"}, "nftTokenIdPlaceholder": {"message": "Ingrese el id del token"}, "nftWarningContent": {"message": "Está otorgando acceso a $1, incluidos los que pueda poseer en el futuro. La contraparte puede transferir estos NFT desde su billetera en cualquier momento sin preguntarle hasta que revoque esta aprobación. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "todos sus NFT de $1", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Proceda con precaución."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "Poseído anterior<PERSON>e"}, "nickname": {"message": "A<PERSON>do"}, "noAccountsFound": {"message": "No se encuentran cuentas para la consulta de búsqueda determinada"}, "noActivity": {"message": "Aún no hay actividad"}, "noConnectedAccountTitle": {"message": "MetaMask no está conectado a este sitio"}, "noConnectionDescription": {"message": "Para conectarse a un sitio, busque y seleccione el botón \"conectar\". Recuerde que MetaMask solo puede conectarse a sitios en Web3"}, "noConversionRateAvailable": {"message": "No hay tasa de conversión disponible"}, "noDeFiPositions": {"message": "Aún no hay posiciones"}, "noDomainResolution": {"message": "No se proporcionó resolución para el dominio."}, "noHardwareWalletOrSnapsSupport": {"message": "Snaps, y la mayoría de los monederos físicos, no funcionarán con la versión actual de su navegador."}, "noNFTs": {"message": "No hay ningún NFT aún"}, "noNetworksFound": {"message": "No se encontraron redes para la consulta de búsqueda dada"}, "noOptionsAvailableMessage": {"message": "Esta ruta comercial no está disponible en este momento. Intente cambiar el monto, la red o el token y encontraremos la mejor opción."}, "noSnaps": {"message": "No tiene ningún snap instalado."}, "noThanks": {"message": "No, gracias"}, "noTransactions": {"message": "No tiene transacciones"}, "noWebcamFound": {"message": "No se encontró la cámara web del equipo. Vuelva a intentarlo."}, "noWebcamFoundTitle": {"message": "No se encontró cámara web"}, "nonContractAddressAlertDesc": {"message": "Estás enviando datos de una llamada a una dirección que no es un contrato. Podrías perder fondos. Asegúrate de usar la dirección y la red correctas antes de continuar."}, "nonContractAddressAlertTitle": {"message": "Posible error"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "Ninguna"}, "notBusy": {"message": "No ocupado"}, "notCurrentAccount": {"message": "¿Esta es la cuenta correcta? Es distinta de la cuenta seleccionada actualmente en su monedero"}, "notEnoughBalance": {"message": "<PERSON><PERSON> insuficiente"}, "notEnoughGas": {"message": "No hay gas suficiente"}, "notNow": {"message": "<PERSON>ora no"}, "notificationDetail": {"message": "Detalles"}, "notificationDetailBaseFee": {"message": "Tarifa base (GWEI)"}, "notificationDetailGasLimit": {"message": "Límite de gas (unidades)"}, "notificationDetailGasUsed": {"message": "Gas usado (unidades)"}, "notificationDetailMaxFee": {"message": "Tarifa máxima por gas"}, "notificationDetailNetwork": {"message": "Red"}, "notificationDetailNetworkFee": {"message": "Ta<PERSON>fa de red"}, "notificationDetailPriorityFee": {"message": "Tarifa de prioridad (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "Verifique en el BlockExplorer"}, "notificationItemCollection": {"message": "Colección"}, "notificationItemConfirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemError": {"message": "No se pueden obtener las tarifas en este momento"}, "notificationItemFrom": {"message": "De"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "<PERSON><PERSON><PERSON> listo"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Ahora puede retirar sus $1 sin staking"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "Se envió su solicitud para hacer unstaking de $1"}, "notificationItemNFTReceivedFrom": {"message": "NFT recibido de"}, "notificationItemNFTSentTo": {"message": "NFT enviado a"}, "notificationItemNetwork": {"message": "Red"}, "notificationItemRate": {"message": "Tasa (tarifa incluida)"}, "notificationItemReceived": {"message": "Recibido"}, "notificationItemReceivedFrom": {"message": "Recibido de"}, "notificationItemSent": {"message": "Enviado"}, "notificationItemSentTo": {"message": "Enviado a"}, "notificationItemStakeCompleted": {"message": "Staking finalizado"}, "notificationItemStaked": {"message": "Con staking"}, "notificationItemStakingProvider": {"message": "Proveed<PERSON> de staking"}, "notificationItemStatus": {"message": "Estado"}, "notificationItemSwapped": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemSwappedFor": {"message": "por"}, "notificationItemTo": {"message": "Para"}, "notificationItemTransactionId": {"message": "ID de transacción"}, "notificationItemUnStakeCompleted": {"message": "Unstaking finalizado"}, "notificationItemUnStaked": {"message": "Sin staking"}, "notificationItemUnStakingRequested": {"message": "Unstaking solicitado"}, "notificationTransactionFailedMessage": {"message": "¡La transacción $1 fallida! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Transacción fallida", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "¡Transacción $1 confirmada!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Transacción confirmada", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Ver en $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Notificaciones"}, "notificationsFeatureToggle": {"message": "Habilitar notificaciones del monedero", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "Esto permite activar notificaciones del monedero como enviar/recibir fondos o NTF y anuncios de funciones.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Marcar todo como leído"}, "notificationsPageEmptyTitle": {"message": "No hay nada que ver aquí"}, "notificationsPageErrorContent": {"message": "Intente volver a visitar esta página."}, "notificationsPageErrorTitle": {"message": "<PERSON><PERSON> un error"}, "notificationsPageNoNotificationsContent": {"message": "Aún no ha recibido ninguna notificación."}, "notificationsSettingsBoxError": {"message": "Se produjo un error. Vuelva a intentarlo."}, "notificationsSettingsPageAllowNotifications": {"message": "Manténgase informado sobre lo que sucede en su monedero con notificaciones. Para usar notificaciones, utilizamos un perfil para sincronizar algunas configuraciones en sus dispositivos. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Obtenga más información sobre cómo protegemos su privacidad mientras utiliza esta función."}, "numberOfNewTokensDetectedPlural": {"message": "$1 nuevos tokens encontrados en esta cuenta", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 nuevo token encontrado en esta cuenta"}, "numberOfTokens": {"message": "Número de tokens"}, "ofTextNofM": {"message": "de"}, "off": {"message": "Desactivado"}, "offlineForMaintenance": {"message": "Sin conexión por mantenimiento"}, "ok": {"message": "Aceptar"}, "on": {"message": "Activado"}, "onboardedMetametricsAccept": {"message": "Acepto"}, "onboardedMetametricsDisagree": {"message": "No, gracias"}, "onboardedMetametricsKey1": {"message": "Últimas novedades"}, "onboardedMetametricsKey2": {"message": "Características de productos"}, "onboardedMetametricsKey3": {"message": "Otros materiales promocionales relevantes"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "Además de $1, nos gustaría utilizar datos para comprender cómo interactúa con las comunicaciones de marketing.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "Esto nos ayuda a personalizar lo que compartimos con usted, como:"}, "onboardedMetametricsParagraph3": {"message": "Recuerde, nunca vendemos los datos que usted proporciona y puede optar por no participar en cualquier momento."}, "onboardedMetametricsTitle": {"message": "Ayúdenos a mejorar su experiencia"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "La puerta de enlace de IPFS permite acceder y visualizar datos alojados por terceros. Puede agregar una puerta de enlace de IPFS personalizada o continuar usando la predeterminada."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Ingrese una URL válida"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Agregar puerta de enlace de IPFS personalizada"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "La URL de la puerta de enlace de IPFS es válida"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Cuando usa nuestra configuración y ajustes por defecto, usamos Infura como nuestro proveedor de llamada a procedimiento remoto (RPC) por defecto para ofrecer el acceso más confiable y privado posible a los datos de Ethereum. En casos limitados, es posible que usemos otros proveedores de RPC para ofrecer la mejor experiencia a nuestros usuarios. Puede elegir su propio proveedor de RPC, pero recuerde que cualquier proveedor de RPC recibirá su dirección IP y su monedero de Ethereum para realizar transacciones. Para obtener más información sobre cómo Infura maneja los datos de las cuentas EVM, lea nuestra $1; para las cuentas de Solana, $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "haga clic aquí"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "<PERSON><PERSON>"}, "onboardingCreateWallet": {"message": "<PERSON><PERSON>r un monedero nuevo"}, "onboardingImportWallet": {"message": "Importar un monedero existente"}, "onboardingMetametricsAgree": {"message": "Acepto"}, "onboardingMetametricsDescription": {"message": "Nos gustaría recopilar datos básicos de uso y diagnóstico para mejorar MetaMask. Tenga presente que nunca venderemos los datos que nos proporcione aquí."}, "onboardingMetametricsInfuraTerms": {"message": "Le informaremos si decidimos usar estos datos para otros fines. Puede consultar $1 para obtener más información. Recuerde que puede acceder a la configuración y excluirse en cualquier momento.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Política de privacidad"}, "onboardingMetametricsNeverCollect": {"message": "Se almacenan los clics y las visualizaciones de $1 en la aplicación, pero no otros detalles (como su dirección pública).", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Privadas:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 usamos su dirección IP temporalmente para detectar una ubicación general (como su país o región), pero no la guardamos nunca.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Generales:"}, "onboardingMetametricsNeverSellData": {"message": "$1 usted decide si desea compartir o eliminar sus datos de uso a través de la configuración en cualquier momento.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Opcionales:"}, "onboardingMetametricsTitle": {"message": "Ayúdenos a mejorar MetaMask"}, "onboardingMetametricsUseDataCheckbox": {"message": "Utilizaremos estos datos para saber cómo interactúa con nuestras comunicaciones de marketing. Es posible que compartamos noticias relevantes (como características del producto)."}, "onboardingPinExtensionDescription": {"message": "<PERSON><PERSON> en su navegador para que sea accesible y las confirmaciones de las transacciones se vean fácilmente."}, "onboardingPinExtensionDescription2": {"message": "Para abrir MetaMask haga clic en la extensión y acceda a su monedero con 1 clic."}, "onboardingPinExtensionDescription3": {"message": "Haga clic en el icono de la extensión del navegador para tener acceso instantáneo", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "¡Su instalación de MetaMask ha finalizado!"}, "onekey": {"message": "OneKey"}, "only": {"message": "solo"}, "onlyConnectTrust": {"message": "Conéctese solo con sitios de confianza. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Pase al modo de pantalla completa para conectar su Ledger.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Abrir en el explorador de bloques"}, "optional": {"message": "Opcional"}, "options": {"message": "Opciones"}, "origin": {"message": "Origen"}, "originChanged": {"message": "El sitio cambió"}, "originChangedMessage": {"message": "Ahora está revisando una solicitud de $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Sistema"}, "other": {"message": "otro"}, "otherSnaps": {"message": "otros snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "otros"}, "outdatedBrowserNotification": {"message": "Su navegador está desactualizado. Si no actualiza su navegador, no podrá obtener los parches de seguridad y las nuevas funciones de MetaMask."}, "overrideContentSecurityPolicyHeader": {"message": "Sobreescribir el encabezado Content-Security-Policy"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Esta opción es una solución alternativa para un problema conocido en Firefox, donde el encabezado Content-Security-Policy de una dapp puede impedir que la extensión se cargue correctamente. No se recomienda desactivar esta opción a menos que sea necesario para la compatibilidad específica de la página web."}, "padlock": {"message": "Candado"}, "participateInMetaMetrics": {"message": "Participar en MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Participe en MetaMetrics para ayudarnos a mejorar MetaMask"}, "password": {"message": "Contraseña"}, "passwordNotLongEnough": {"message": "La contraseña no es suficientemente larga"}, "passwordStrength": {"message": "Solidez de la contraseña: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Una contraseña segura puede mejorar la protección de su monedero en caso de que le roben el dispositivo o esté comprometido."}, "passwordTermsWarning": {"message": "Entiendo que MetaMask no me puede recuperar esta contraseña. $1"}, "passwordsDontMatch": {"message": "Las contraseñas no coinciden"}, "pastePrivateKey": {"message": "Pegue aquí la cadena de clave privada:", "description": "For importing an account from a private key"}, "pending": {"message": "Pendiente"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "La actualización de la red cancelará $1 transacciones pendientes de este sitio.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Al cambiar de red se cancelarán $1 transacciones pendientes de este sitio.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Esta transacción no se realizará hasta que haya finalizado una transacción anterior. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Aprenda a cancelar o acelerar una transacción.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Detalles del permiso"}, "permissionFor": {"message": "Permiso para"}, "permissionFrom": {"message": "Permiso de"}, "permissionRequested": {"message": "Solicitado ahora"}, "permissionRequestedForAccounts": {"message": "Solicitado ahora por $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Revocado en esta actualización"}, "permissionRevokedForAccounts": {"message": "Revocado en esta actualización por $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Conectarse a $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Acceso a internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Permita que $1 tenga acceso a Internet. Esto se puede usar tanto para enviar como para recibir datos con servidores de terceros.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Conéctese al snap de $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Permita que el sitio web o el snap interactúen con $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Mostrar los activos de la cuenta en MetaMask.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Permitir que $1 proporcione información de activos al cliente de MetaMask. Los activos pueden estar en la cadena o fuera de la cadena.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Programar y ejecutar acciones periódicas.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Permita que $1 realice acciones que se ejecutan periódicamente en horas, fechas o intervalos fijos. Esto se puede usar para activar interacciones o notificaciones sensibles al tiempo.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Mostrar ventanas de diálogo en MetaMask.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Permita que $1 muestre ventanas emergentes de MetaMask con texto personalizado, campo de entrada y botones para aprobar o rechazar una acción.\nSe puede usar para crear, p. ej. alertas, confirmaciones y flujos de suscripción para un snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "Ver las direcciones de las cuentas permitidas (requerido)", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Acceda al proveedor de Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Permita que $1 se comunique directamente con MetaMask para que lea datos de la cadena de bloques y sugiera mensajes y transacciones.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Obtenga claves arbitrarias únicas para $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Permita que $1 obtenga claves arbitrarias únicas para $1, sin exponerlas. Estas claves son independientes de su(s) cuenta(s) de MetaMask y no están relacionadas con sus claves privadas ni su frase secreta de recuperación. <PERSON><PERSON><PERSON> snaps no pueden acceder a esta información.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "Vea su idioma preferido.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Permita que $1 acceda a su idioma preferido desde la configuración de MetaMask. Esto se puede usar para localizar y mostrar el contenido de $1 usando su idioma.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "Vea información como su idioma preferido y moneda fiduciaria.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Permita que $1 acceda a información como su idioma preferido y moneda fiduciaria en su configuración de MetaMask. Esto ayuda a que $1 muestre contenido adaptado a sus preferencias. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Mostrar una pantalla personalizada", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Permita que $1 muestre una pantalla de inicio personalizada en MetaMask. Esto se puede utilizar para interfaces de usuario, configuración y paneles.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Permitir solicitudes para agregar y controlar cuentas Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Permita que $1 reciba solicitudes para agregar o eliminar cuentas, además de firmar y realizar transacciones en nombre de estas cuentas.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Utilice ganchos de ciclo de vida.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Permita que $1 use ganchos de ciclo de vida para ejecutar código en momentos específicos durante su ciclo de vida.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Agregar y controlar cuentas de Ethereum", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Permita que $1 agregue o elimine cuentas de Ethereum, luego realice transacciones y firme con estas cuentas.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "Administrar cuentas de $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Permita a $1 para administrar cuentas y activos en la red solicitada. Estas cuentas se derivan y se respaldan utilizando su frase de recuperación secreta (sin revelarla). Con el poder de derivar claves, $1 puede admitir una variedad de protocolos de cadenas de bloques más allá de Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "Administrar cuentas de $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Almacene y administre sus datos en su dispositivo.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Permita que $1 almacene, actualice y recupere datos de forma segura con cifrado. <PERSON><PERSON><PERSON> snaps no pueden acceder a esta información.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Proporcionar búsquedas de dominios y direcciones.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Permita que el snap obtenga y muestre búsquedas de direcciones y dominios en diferentes partes de la interfaz de usuario de MetaMask.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Mostrar notificaciones.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Permita que $1 muestre notificaciones dentro de MetaMask. Se puede activar un breve texto de notificación con un snap para obtener información procesable o sensible al tiempo.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Proporciona datos de protocolo para una o más cadenas de bloques.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Asigna $1 para que MetaMask pueda obtener datos del protocolo, como estimaciones de gas o información sobre tokens.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Permitir que $1 se comunique directamente con $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Permita que $1 envíe mensajes a $2 y reciba una respuesta de $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 y $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Mostrar modo de información de firma.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Permita que $1 muestre un modo con información sobre cualquier solicitud de firma antes de su aprobación. Esto se puede utilizar para soluciones de seguridad y antiphishing.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "Vea los orígenes de los sitios web que inician una solicitud de firma", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Permita que $1 vea el origen (URI) de los sitios web que inician solicitudes de firma. Esto se puede utilizar para soluciones antiphishing y de seguridad.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Obtenga y muestre información de transacciones.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Permita que $1 decodifique transacciones y muestre información dentro de la interfaz de usuario de MetaMask. Esto se puede utilizar para soluciones antiphishing y de seguridad.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Ver los orígenes de los sitios web que sugieren transacciones", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Permita que $1 vea el origen (URI) de los sitios web que sugieren transacciones. Esto se puede utilizar para soluciones antiphishing y de seguridad.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Permiso desconocido: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Ver su clave pública para $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Permita que $2 vea sus claves (y direcciones) públicas para $1. Esto no otorga ningún control de cuentas o activos.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Vea su clave pública para $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Utilice sus redes habilitadas", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Soporte para WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Permita que $1 acceda a entornos de ejecución de bajo nivel a través de WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "<PERSON><PERSON><PERSON>"}, "permissionsPageEmptyContent": {"message": "Nada que ver aquí"}, "permissionsPageEmptySubContent": {"message": "Aquí es donde puedes ver los permisos que has otorgado a los Snaps instalados o a los sitios conectados."}, "permitSimulationChange_approve": {"message": "Límite de gasto"}, "permitSimulationChange_bidding": {"message": "Usted hace una oferta"}, "permitSimulationChange_listing": {"message": "Usted publica una oferta"}, "permitSimulationChange_nft_listing": {"message": "Precio de venta"}, "permitSimulationChange_receive": {"message": "Usted recibe"}, "permitSimulationChange_revoke2": {"message": "Revocar"}, "permitSimulationChange_transfer": {"message": "Usted envía"}, "permitSimulationDetailInfo": {"message": "Le está dando permiso al gastador para gastar esta cantidad de tokens de su cuenta."}, "permittedChainToastUpdate": {"message": "$1 tiene acceso a $2."}, "personalAddressDetected": {"message": "Se detectó una dirección personal. Ingrese la dirección de contrato del token."}, "pinToTop": {"message": "<PERSON><PERSON><PERSON> arriba"}, "pleaseConfirm": {"message": "Confirmar"}, "plusMore": {"message": "+ $1 más", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 más", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Algunas de estas redes dependen de terceros. Las conexiones pueden ser menos fiables o permitir que terceros rastreen la actividad.", "description": "Learn more link"}, "popularNetworks": {"message": "Redes populares"}, "portfolio": {"message": "Portafolio"}, "preparingSwap": {"message": "Preparando intercambio..."}, "prev": {"message": "Ant."}, "price": {"message": "Precio"}, "priceUnavailable": {"message": "precio no disponible"}, "primaryType": {"message": "Tipo principal"}, "priorityFee": {"message": "Tarifa de prioridad"}, "priorityFeeProperCase": {"message": "Tarifa de prioridad"}, "privacy": {"message": "Privacidad"}, "privacyMsg": {"message": "Política de privacidad"}, "privateKey": {"message": "Clave privada", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Clave privada para $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "La clave privada está oculta", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Mostrar/ocultar la entrada de clave privada", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Esta clave privada se está mostrando", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Advertencia: No revele esta clave. Cualquier persona que tenga sus claves privadas podría robar los activos de su cuenta."}, "privateNetwork": {"message": "Red privada"}, "proceedWithTransaction": {"message": "<PERSON><PERSON><PERSON> continuar de todos modos"}, "productAnnouncements": {"message": "Anuncios de productos"}, "proposedApprovalLimit": {"message": "Límite de aprobación propuesto"}, "provide": {"message": "Proporcionar"}, "publicAddress": {"message": "Dirección pública"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Recibió $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Recibió algunos tokens"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Fondos recibidos"}, "pushPlatformNotificationsFundsSentDescription": {"message": "Envió correctamente $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Envió correctamente algunos tokens"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Fondos enviados"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Recibió nuevos NFT"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT recibido"}, "pushPlatformNotificationsNftSentDescription": {"message": "Envió correctamente un NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT enviado"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Su staking en Lido se realizó correctamente"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Staking finalizado"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Su staking en Lido ya está listo para retirarse"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Staking listo para retirarse"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Su retiro en Lido se realizó correctamente"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Retiro finalizado"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Se envió su solicitud de retiro en Lido"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "<PERSON><PERSON><PERSON> solicitado"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Su staking en RocketPool se realizó correctamente"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Staking finalizado"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "Su unstaking en RocketPool se realizó correctamente"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Unstaking finalizado"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Su intercambio en MetaMask se realizó correctamente"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Intercambio finalizado"}, "queued": {"message": "En cola"}, "quoteRate": {"message": "Tarifa de cotización"}, "quotedReceiveAmount": {"message": "Monto recibido de $1"}, "quotedTotalCost": {"message": "Costo total de $1"}, "rank": {"message": "Ra<PERSON>"}, "rateIncludesMMFee": {"message": "La tarifa incluye una cuota del $1 %"}, "reAddAccounts": {"message": "volver a agregar cualquier otra cuenta"}, "reAdded": {"message": "agregada nuevamente"}, "readdToken": {"message": "Puede volver a agregar este token en el futuro desde “Agregar token” en el menú de opciones de las cuentas."}, "receive": {"message": "Recibir"}, "receiveCrypto": {"message": "Recibir criptomonedas"}, "recipientAddressPlaceholderNew": {"message": "Ingrese la dirección pública (0x) o el nombre del dominio"}, "recommendedGasLabel": {"message": "Recomendado"}, "recoveryPhraseReminderBackupStart": {"message": "Iniciar aqu<PERSON>"}, "recoveryPhraseReminderConfirm": {"message": "Entendido"}, "recoveryPhraseReminderHasBackedUp": {"message": "Guarde siempre su frase secreta de recuperación en un lugar seguro y secreto."}, "recoveryPhraseReminderHasNotBackedUp": {"message": "¿Necesita volver a crear una copia de seguridad de su frase secreta de recuperación?"}, "recoveryPhraseReminderItemOne": {"message": "No comparta nunca su frase secreta de recuperación con nadie."}, "recoveryPhraseReminderItemTwo": {"message": "El equipo de MetaMask nunca le pedirá su frase secreta de recuperación."}, "recoveryPhraseReminderSubText": {"message": "Mediante su frase secreta de recuperación, se controlan todas sus cuentas."}, "recoveryPhraseReminderTitle": {"message": "Proteja sus fondos."}, "redeposit": {"message": "Volver a depositar"}, "refreshList": {"message": "Actualizar lista"}, "reject": {"message": "<PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON> todo"}, "rejectRequestsDescription": {"message": "Está a punto de rechazar $1 solicitudes en lote."}, "rejectRequestsN": {"message": "Rechazar $1 solicitudes"}, "rejectTxsDescription": {"message": "Está a punto de rechazar $1 transacciones en lote."}, "rejectTxsN": {"message": "Rechazar $1 transacciones"}, "rejected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "remove": {"message": "<PERSON><PERSON><PERSON>"}, "removeAccount": {"message": "<PERSON><PERSON><PERSON> cuenta"}, "removeAccountDescription": {"message": "Esta cuenta se eliminará de su monedero. Antes de continuar, asegúrese de tener la frase secreta de recuperación original o la clave privada de esta cuenta importada. Puede importar o crear cuentas nuevamente en la lista desplegable de la cuenta. "}, "removeKeyringSnap": {"message": "Al eliminar este Snap, se eliminan estas cuentas de MetaMask:"}, "removeKeyringSnapToolTip": {"message": "El snap controla las cuentas y, al eliminarlo, las cuentas también se eliminarán de MetaMask, pero permanecer<PERSON> en la cadena de bloques."}, "removeNFT": {"message": "Eliminar NFT"}, "removeNftErrorMessage": {"message": "No pudimos eliminar este NFT."}, "removeNftMessage": {"message": "¡El NFT se eliminó con éxito!"}, "removeSnap": {"message": "Eliminar snap"}, "removeSnapAccountDescription": {"message": "<PERSON> continúa, esta cuenta ya no estará disponible en MetaMask."}, "removeSnapAccountTitle": {"message": "Eliminar cuenta"}, "removeSnapConfirmation": {"message": "¿Está seguro de que desea eliminar $1?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "Esta acción eliminará el snap, sus datos y revocará los permisos otorgados."}, "replace": {"message": "reemplazar"}, "reportIssue": {"message": "Reportar un problema"}, "requestFrom": {"message": "Solicitud de"}, "requestFromInfo": {"message": "Este es el sitio que solicita su firma."}, "requestFromInfoSnap": {"message": "Este es el Snap que solicita su firma."}, "requestFromTransactionDescription": {"message": "Este es el sitio que le pide su confirmación."}, "requestingFor": {"message": "Solicitando para"}, "requestingForAccount": {"message": "Solicitando para $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Solicitando para $1", "description": "Name of Network"}, "required": {"message": "Requerido"}, "reset": {"message": "Restablecer"}, "resetWallet": {"message": "Restable<PERSON> monedero"}, "resetWalletSubHeader": {"message": "MetaMask no guarda una copia de su contraseña. Si tiene problemas para desbloquear su cuenta, deberá restablecer su monedero. Puede hacerlo proporcionando la frase secreta de recuperación que utilizó cuando configuró su monedero."}, "resetWalletUsingSRP": {"message": "Esta acción eliminará su monedero actual y la frase secreta de recuperación de este dispositivo, junto con la lista de cuentas que ha seleccionado. Después de restablecer con una frase secreta de recuperación, verá una lista de cuentas basada en la frase secreta de recuperación que usa para restablecer. Esta nueva lista incluirá automáticamente las cuentas que tengan saldo. También podrá $1 creado anteriormente. Las cuentas personalizadas que haya importado deberán costar $2, y cualquier token personalizado que haya agregado a una cuenta también deberá costar $3."}, "resetWalletWarning": {"message": "Asegúrese de usar la frase secreta de recuperación correcta antes de continuar. No podrá deshacer esto."}, "restartMetamask": {"message": "Reiniciar Meta<PERSON>"}, "restore": {"message": "Restaurar"}, "restoreUserData": {"message": "Restaure sus datos de usuario"}, "resultPageError": {"message": "Error"}, "resultPageErrorDefaultMessage": {"message": "La operación falló."}, "resultPageSuccess": {"message": "Éxito"}, "resultPageSuccessDefaultMessage": {"message": "La operación se completó con éxito."}, "retryTransaction": {"message": "Reintentar transacción"}, "reusedTokenNameWarning": {"message": "Un token reutiliza un símbolo de otro token que se le muestra. Esto puede ser confuso o engañoso."}, "revealSecretRecoveryPhrase": {"message": "Revelar frase secreta de recuperación"}, "revealSeedWords": {"message": "Revelar frase secreta de recuperación"}, "revealSeedWordsDescription1": {"message": "La $1 proporciona la $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMask es un $1. Eso significa que usted es el propietario de su SRP.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "acceso completo a su monedero y fondos.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "monedero no custodiado"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Frase secreta de recuperación (SRP)"}, "revealSeedWordsText": {"message": "Texto"}, "revealSeedWordsWarning": {"message": "Asegúrese de que nadie esté mirando su pantalla. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "El soporte técnico de MetaMask nunca se lo solicitará.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "<PERSON><PERSON><PERSON> contenido <PERSON>"}, "review": {"message": "Rev<PERSON><PERSON>"}, "reviewAlert": {"message": "Alerta de revisión"}, "reviewAlerts": {"message": "<PERSON><PERSON><PERSON>"}, "reviewPendingTransactions": {"message": "Revisar transacciones pendientes"}, "reviewPermissions": {"message": "<PERSON><PERSON><PERSON>"}, "revokePermission": {"message": "<PERSON><PERSON><PERSON> permiso"}, "revokePermissionTitle": {"message": "Eliminar permiso de $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "Está eliminando el permiso de una persona para gastar tokens de su cuenta."}, "reward": {"message": "Recompensa"}, "rpcNameOptional": {"message": "Nombre de RPC (opcional)"}, "rpcUrl": {"message": "URL de RPC"}, "safeTransferFrom": {"message": "Transferencia segura desde"}, "save": {"message": "Guardar"}, "scanInstructions": {"message": "Ponga el código QR frente a la cámara"}, "scanQrCode": {"message": "Escanear código QR"}, "scrollDown": {"message": "Desp<PERSON><PERSON><PERSON> hacia abajo"}, "search": {"message": "Buscar"}, "searchAccounts": {"message": "Buscar cuentas"}, "searchNfts": {"message": "Buscar NFT"}, "searchTokens": {"message": "Buscar tokens"}, "searchTokensByNameOrAddress": {"message": "Buscar tokens por nombre o dirección"}, "secretRecoveryPhrase": {"message": "Frase secreta de recuperación"}, "secretRecoveryPhrasePlusNumber": {"message": "Frase secreta de recuperación $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "Prote<PERSON>"}, "security": {"message": "Seguridad"}, "securityAlert": {"message": "Alerta de seguridad de $1 y $2"}, "securityAlerts": {"message": "Alertas de seguridad"}, "securityAlertsDescription": {"message": "Esta función le alerta sobre actividades maliciosas al revisar activamente las solicitudes de transacciones y firmas. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Seguridad y privacidad"}, "securityDescription": {"message": "Reduzca sus posibilidades de unirse a redes inseguras y proteja sus cuentas"}, "securityMessageLinkForNetworks": {"message": "estafas en la red y riesgos de seguridad"}, "securityProviderPoweredBy": {"message": "Impulsado por $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "Ver todos los permisos", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "seedPhraseIntroTitle": {"message": "Proteger su cartera"}, "seedPhraseReq": {"message": "Las frases secretas de recuperación contienen 12, 15, 18, 21 o 24 palabras"}, "select": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selectAccountToConnect": {"message": "Seleccione una cuenta para conectarse"}, "selectAccounts": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> cuenta<PERSON>"}, "selectAccountsForSnap": {"message": "Seleccione la(s) cuenta(s) para usar con este snap"}, "selectAll": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo"}, "selectAnAccount": {"message": "Seleccionar una cuenta"}, "selectAnAccountAlreadyConnected": {"message": "Esta cuenta ya se conectó a MetaMask."}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Activar Mostrar medios NFT"}, "selectHdPath": {"message": "Seleccione la ruta de acceso al disco duro"}, "selectNFTPrivacyPreference": {"message": "Habilite la autodetección de NFT"}, "selectPathHelp": {"message": "Si no ve las cuentas previstas, intente cambiar la ruta HD o la red seleccionada actualmente."}, "selectRpcUrl": {"message": "Seleccionar URL de RPC"}, "selectSecretRecoveryPhrase": {"message": "Seleccionar frase secreta de recuperación"}, "selectType": {"message": "Seleccionar tipo"}, "selectedAccountMismatch": {"message": "Se seleccionó una cuenta distinta"}, "selectingAllWillAllow": {"message": "Seleccionar todo permitirá que este sitio vea todas las cuentas actuales. Asegúrese de que este sitio sea de confianza."}, "send": {"message": "Enviar"}, "sendBugReport": {"message": "Envíenos un informe de error."}, "sendNoContactsConversionText": {"message": "haga clic aquí"}, "sendNoContactsDescription": {"message": "Los contactos le permiten enviar transacciones de forma segura a otra cuenta múltiples veces. Para crear un contacto, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "<PERSON>ún no tiene ningún contacto"}, "sendSelectReceiveAsset": {"message": "Seleccione el activo a recibir"}, "sendSelectSendAsset": {"message": "Seleccione el activo a enviar"}, "sendSpecifiedTokens": {"message": "Enviar $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Al hacer clic en este botón se iniciará inmediatamente su transacción de canje. Revise los detalles de la transacción antes de continuar."}, "sendTokenAsToken": {"message": "Enviar $1 como $2", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Enviando $1"}, "sendingDisabled": {"message": "Todavía no se admite el envío de activos NFT ERC-1155."}, "sendingNativeAsset": {"message": "Enviando $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Advertencia: está a punto de enviar un contrato de token que podría dar lugar a una pérdida de fondos. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Red de prueba Sepolia"}, "setApprovalForAll": {"message": "Establecer aprobación para todos"}, "setApprovalForAllRedesignedTitle": {"message": "Solicitud de retiro"}, "setApprovalForAllTitle": {"message": "Aprobar $1 sin límite preestablecido", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "<PERSON><PERSON><PERSON> una cuenta Snap"}, "settings": {"message": "Configuración"}, "settingsSearchMatchingNotFound": {"message": "No se encontraron resultados coincidentes."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Solicitudes de firmas y transacciones"}, "show": {"message": "Mostrar"}, "showAccount": {"message": "Mostrar cuenta"}, "showAdvancedDetails": {"message": "<PERSON><PERSON> de<PERSON><PERSON> a<PERSON>"}, "showExtensionInFullSizeView": {"message": "Mostrar extensión en vista de tamaño completo"}, "showExtensionInFullSizeViewDescription": {"message": "Active esto para que la vista en tamaño completo sea por defecto cuando haga clic en el icono de extensión."}, "showFiatConversionInTestnets": {"message": "Mostrar conversión en redes de prueba"}, "showFiatConversionInTestnetsDescription": {"message": "Seleccione esta opción para mostrar la conversión de moneda fiduciaria en las redes de prueba"}, "showHexData": {"message": "Mostrar datos hexadecimales"}, "showHexDataDescription": {"message": "Seleccione esta opción para mostrar el campo de datos hexadecimales en la pantalla de envío"}, "showLess": {"message": "<PERSON><PERSON> menos"}, "showMore": {"message": "Mostrar más"}, "showNativeTokenAsMainBalance": {"message": "Mostrar el token nativo como saldo principal"}, "showNft": {"message": "Mostrar NFT"}, "showPermissions": {"message": "<PERSON>rar permisos"}, "showPrivateKey": {"message": "Mostrar clave privada"}, "showSRP": {"message": "Mostrar frase secreta de recuperación"}, "showTestnetNetworks": {"message": "Mostrar redes de prueba"}, "showTestnetNetworksDescription": {"message": "Seleccione esta opción para mostrar las redes de prueba en la lista de redes"}, "sign": {"message": "<PERSON><PERSON><PERSON>"}, "signatureRequest": {"message": "Solicitud de firma"}, "signature_decoding_bid_nft_tooltip": {"message": "Una vez aceptada la oferta, el NFT se reflejará en su monedero."}, "signature_decoding_list_nft_tooltip": {"message": "<PERSON><PERSON><PERSON> cambios solo si alguien compra sus NFT."}, "signed": {"message": "<PERSON><PERSON><PERSON>"}, "signing": {"message": "<PERSON><PERSON><PERSON>"}, "signingInWith": {"message": "Iniciar se<PERSON><PERSON> con"}, "signingWith": {"message": "Firmando con"}, "simulationApproveHeading": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "Le está dando permiso a otra persona para que retire NFT de su cuenta."}, "simulationDetailsERC20ApproveDesc": {"message": "Le está dando permiso a otra persona para que gaste este monto de su cuenta."}, "simulationDetailsFiatNotAvailable": {"message": "No disponible"}, "simulationDetailsIncomingHeading": {"message": "Usted recibe"}, "simulationDetailsNoChanges": {"message": "Sin cambios"}, "simulationDetailsOutgoingHeading": {"message": "Envía"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Está eliminando el permiso de otra persona para retirar NFT de su cuenta."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Está dando permiso a otra persona para que retire NFT de su cuenta."}, "simulationDetailsTitle": {"message": "Cambios estimados"}, "simulationDetailsTitleTooltip": {"message": "Los cambios estimados son los que podrían producirse si sigue adelante con esta transacción. Esto es solo una predicción, no una garantía."}, "simulationDetailsTotalFiat": {"message": "Total = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Es probable que esta transacción falle"}, "simulationDetailsUnavailable": {"message": "No disponible"}, "simulationErrorMessageV2": {"message": "No pudimos estimar el gas. Podría haber un error en el contrato y esta transacción podría fallar."}, "simulationsSettingDescription": {"message": "Active esta opción para calcular los cambios de saldo de las transacciones y firmas antes de confirmarlas. Esto no garantiza su resultado final. $1"}, "simulationsSettingSubHeader": {"message": "Estimar cambios de saldo"}, "singleNetwork": {"message": "1 red"}, "siweIssued": {"message": "Emitido"}, "siweNetwork": {"message": "Red"}, "siweRequestId": {"message": "Solicitar ID"}, "siweResources": {"message": "Recursos"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "¿Omitir la seguridad de la cuenta?"}, "skipAccountSecurityDetails": {"message": "Entiendo que hasta que no haga una copia de seguridad de mi frase secreta de recuperación, puedo perder mis cuentas y todos los activos asociados."}, "slideBridgeDescription": {"message": "Opere en 9 cadenas, todas dentro de su monedero"}, "slideBridgeTitle": {"message": "¿Listo para puentear?"}, "slideCashOutDescription": {"message": "Venda sus criptomonedas por efectivo"}, "slideCashOutTitle": {"message": "Retire su dinero con MetaMask"}, "slideDebitCardDescription": {"message": "Disponible en regiones seleccionadas"}, "slideDebitCardTitle": {"message": "Tarjeta de débito MetaMask"}, "slideFundWalletDescription": {"message": "Añada o transfiera tokens para comenzar"}, "slideFundWalletTitle": {"message": "Añada fondos a su monedero"}, "slideMultiSrpDescription": {"message": "Importar y usar varios monederos en MetaMask"}, "slideMultiSrpTitle": {"message": "Agregar varias frases secretas de recuperación"}, "slideRemoteModeDescription": {"message": "Use su monedero frío de forma inalámbrica"}, "slideRemoteModeTitle": {"message": "Almacenamiento en frío, acceso rápido"}, "slideSmartAccountUpgradeDescription": {"message": "La misma dirección, funciones más inteligentes"}, "slideSmartAccountUpgradeTitle": {"message": "Empiece a usar cuentas inteligentes"}, "slideSolanaDescription": {"message": "Cree una cuenta de Solana para comenzar"}, "slideSolanaTitle": {"message": "Solana ahora es compatible"}, "slideSweepStakeDescription": {"message": "Acuñe un NFT ahora para tener la oportunidad de ganar"}, "slideSweepStakeTitle": {"message": "¡Participe en el sorteo de $5000 USDC!"}, "smartAccountAccept": {"message": "Use una cuenta inteligente"}, "smartAccountBetterTransaction": {"message": "Transacciones más rápidas, tarifas más bajas"}, "smartAccountBetterTransactionDescription": {"message": "Ahorre tiempo y dinero procesando transacciones de forma conjunta."}, "smartAccountFeaturesDescription": {"message": "Mantenga la misma dirección de cuenta y podrá volver a cambiarla en cualquier momento."}, "smartAccountLabel": {"message": "Cuenta inteligente"}, "smartAccountPayToken": {"message": "Pague con cualquier token, en cualquier momento"}, "smartAccountPayTokenDescription": {"message": "Use los tokens que ya tiene para cubrir las tarifas de red."}, "smartAccountReject": {"message": "No use una cuenta inteligente"}, "smartAccountRequestFor": {"message": "Solicitud de"}, "smartAccountSameAccount": {"message": "La misma cuenta, funciones más inteligentes."}, "smartAccountSplashTitle": {"message": "¿Usar una cuenta inteligente?"}, "smartAccountUpgradeBannerDescription": {"message": "La misma dirección. Funciones más inteligentes."}, "smartAccountUpgradeBannerTitle": {"message": "Cambiar a cuenta inteligente"}, "smartContracts": {"message": "Contratos inteligentes"}, "smartSwapsErrorNotEnoughFunds": {"message": "No hay suficientes fondos para un intercambio inteligente."}, "smartSwapsErrorUnavailable": {"message": "Los intercambios inteligentes no están disponibles temporalmente."}, "smartTransactionCancelled": {"message": "Su transacción se canceló"}, "smartTransactionCancelledDescription": {"message": "Su transacción no se pudo completa, así que se canceló para ahorrarle el pago de comisiones de gas innecesarias."}, "smartTransactionError": {"message": "Su transacción falló"}, "smartTransactionErrorDescription": {"message": "Los cambios repentinos en el mercado pueden causar fallos. Si el problema continúa, póngase en contacto con el soporte al cliente de MetaMask."}, "smartTransactionPending": {"message": "Se envió su transacción"}, "smartTransactionSuccess": {"message": "Su transacción está completa"}, "smartTransactions": {"message": "Transacciones inteligentes"}, "smartTransactionsEnabledDescription": {"message": " y protección MEV. Ahora activadas por defecto."}, "smartTransactionsEnabledLink": {"message": "Mayores tasas de éxito"}, "smartTransactionsEnabledTitle": {"message": "Las transacciones ahora son más inteligentes"}, "snapAccountCreated": {"message": "<PERSON><PERSON>ta creada"}, "snapAccountCreatedDescription": {"message": "¡Su nueva cuenta está lista para usar!"}, "snapAccountCreationFailed": {"message": "Error al crear la cuenta"}, "snapAccountCreationFailedDescription": {"message": "$1 no logró crear una cuenta para usted.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Terminar de firmar"}, "snapAccountRedirectSiteDescription": {"message": "Siga las instrucciones desde $1"}, "snapAccountRemovalFailed": {"message": "Error al eliminar la cuenta"}, "snapAccountRemovalFailedDescription": {"message": "$1 no logró eliminar esta cuenta para usted.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Cuenta eliminada"}, "snapAccountRemovedDescription": {"message": "Esta cuenta ya no estará disponible para su uso en MetaMask."}, "snapAccounts": {"message": "Cuenta de Snaps"}, "snapAccountsDescription": {"message": "Cuentas controladas por Snaps de terceros."}, "snapConnectTo": {"message": "Conectarse a $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Permita que $1 se conecte automáticamente a $2 sin su aprobación.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 quiere conectarse a $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Sitio web"}, "snapHomeMenu": {"message": "Menú de inicio de Snap"}, "snapInstallRequest": {"message": "Instalar $1 le otorga los siguientes permisos.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Instalación completa"}, "snapInstallWarningCheck": {"message": "$1 quiere permiso para hacer lo siguiente:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Proceda con precaución"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Permita que $1 vea sus claves (y direcciones) públicas. Esto no otorga ningún control de cuentas o activos.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Permita que Snap $1 administre cuentas y activos en las red(es) solicitada(s). Estas cuentas se derivan y se respaldan utilizando su frase de recuperación secreta (sin revelarla). Con el poder de derivar claves, $1 puede admitir una variedad de protocolos blockchain más allá de Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Administrar cuentas de $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "Ver su clave pública para $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 no se pudo instalar.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Instalación fallida", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "Error"}, "snapResultSuccess": {"message": "Éxito"}, "snapResultSuccessDescription": {"message": "$1 está listo para usar"}, "snapUIAssetSelectorTitle": {"message": "Seleccione un activo"}, "snapUpdateAlertDescription": {"message": "Obtenga la última versión de $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Actualización disponible"}, "snapUpdateErrorDescription": {"message": "$1 no se pudo actualizar.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "Actualización fallida", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "Actualizar $1 le otorga los siguientes permisos.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Actualización completa"}, "snapUrlIsBlocked": {"message": "Este Snap quiere llevarlo a un sitio bloqueado. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps conectados"}, "snapsNoInsight": {"message": "No hay información que mostrar"}, "snapsPrivacyWarningFirstMessage": {"message": "Usted reconoce que el snap que está a punto de instalar es un Servicio de terceros, menos que se identifique de otro modo, según se define en Consensys $1. El uso que haga de los Servicios de terceros se rige por términos y condiciones independientes establecidos por el proveedor de Servicios de terceros. Consensys no recomienda el uso de ningún snap a ninguna persona en particular por ningún motivo en particular. Usted accede, confía o utiliza el Servicio de terceros bajo su propio riesgo. Consensys se exime de toda responsabilidad por cualquier pérdida a causa del uso de los Servicios de terceros.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Cualquier información que comparta con Servicios de terceros será recopilada directamente por dichos Servicios de terceros de acuerdo con sus políticas de privacidad. Consulte sus políticas de privacidad para obtener más información.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys no tiene acceso a la información que usted comparte con servicios de terceros.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Configuración de Snap"}, "snapsTermsOfUse": {"message": "T<PERSON><PERSON><PERSON>s de uso"}, "snapsToggle": {"message": "Un snap solo se ejecutará si está habilitado"}, "snapsUIError": {"message": "Póngase en contacto con los creadores de $1 para obtener más ayuda.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Este sitio solicita una cuenta de Solana."}, "solanaAccountRequired": {"message": "Se requiere una cuenta de Solana para conectarse a este sitio."}, "solanaImportAccounts": {"message": "Importar cuentas de Solana"}, "solanaImportAccountsDescription": {"message": "Importe una frase secreta de recuperación para migrar su cuenta de Solana desde otro monedero."}, "solanaMoreFeaturesComingSoon": {"message": "Próximamente habrá más funciones"}, "solanaMoreFeaturesComingSoonDescription": {"message": "Próximamente, compatibilidad con NFT, monederos físicos y más."}, "solanaOnMetaMask": {"message": "Solana en MetaMask"}, "solanaSendReceiveSwapTokens": {"message": "Envíe, reciba e intercambie tokens"}, "solanaSendReceiveSwapTokensDescription": {"message": "Transfiera y realice transacciones con tokens como SOL, USDC y más."}, "someNetworks": {"message": "$1 redes"}, "somethingDoesntLookRight": {"message": "Algo no se ve bien, ¿cierto? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Algo salió mal. Intente volver a cargar la página."}, "somethingWentWrong": {"message": "No pudimos cargar esta página."}, "sortBy": {"message": "Ordenar por"}, "sortByAlphabetically": {"message": "Alfabéticamente (A-Z)"}, "sortByDecliningBalance": {"message": "<PERSON><PERSON>nte ($1 alto-bajo)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Fuente"}, "spamModalBlockedDescription": {"message": "Este sitio se bloqueará por 1 minuto."}, "spamModalBlockedTitle": {"message": "Ha bloqueado este sitio temporalmente"}, "spamModalDescription": {"message": "Si recibe spam con varias solicitudes, puede bloquear el sitio temporalmente."}, "spamModalTemporaryBlockButton": {"message": "Bloquear temporalmente este sitio"}, "spamModalTitle": {"message": "Hemos notado varias solicitudes"}, "speed": {"message": "Velocidad"}, "speedUp": {"message": "<PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "Acelerar esta cancelación"}, "speedUpExplanation": {"message": "Hemos actualizado la tarifa de gas en función de las condiciones actuales de la red y la hemos aumentado al menos un 10 % (exigido por la red)."}, "speedUpPopoverTitle": {"message": "Acelerar la transacción"}, "speedUpTooltipText": {"message": "Nueva tarifa de gas"}, "speedUpTransaction": {"message": "Acelerar esta transacción"}, "spendLimitInsufficient": {"message": "Límite de gastos insuficiente"}, "spendLimitInvalid": {"message": "El límite de gastos no es válido, debe ser un número positivo"}, "spendLimitPermission": {"message": "Permiso de límite de gastos"}, "spendLimitRequestedBy": {"message": "Límite de gastos solicitado por $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "El límite de gastos es demasiado alto"}, "spender": {"message": "Gas<PERSON>or"}, "spenderTooltipDesc": {"message": "Esta es la dirección que podrá retirar sus NFT."}, "spenderTooltipERC20ApproveDesc": {"message": "Esta es la dirección que podrá gastar sus tokens en su nombre."}, "spendingCap": {"message": "Límite de gasto"}, "spendingCaps": {"message": "Límites de gasto"}, "srpInputNumberOfWords": {"message": "Tengo una frase de $1 palabras", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Frase secreta de recuperación $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 cuentas", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "La frase secreta de recuperación a partir de la cual se generará su nueva cuenta"}, "srpListSingleOrZero": {"message": "$1 cuenta", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "<PERSON><PERSON>ar falló porque contenía más de 24 palabras. Una frase secreta de recuperación puede tener un máximo de 24 palabras.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "<PERSON><PERSON><PERSON> pegar toda su frase secreta de recuperación en cualquier campo", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "Comenzar"}, "srpSecurityQuizImgAlt": {"message": "Un ojo con un ojo de cerradura en el centro y tres campos de contraseña flotantes"}, "srpSecurityQuizIntroduction": {"message": "Para revelar su frase secreta de recuperación, debe responder correctamente dos preguntas"}, "srpSecurityQuizQuestionOneQuestion": {"message": "Si extravía su frase secreta de recuperación, MetaMask..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "No puede ayudarlo"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "Es<PERSON>r<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON> en metal o guárdelo en múltiples lugares secretos para que nunca lo pierda. Si lo pierde, lo ha perdido para siempre."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "¡Cierto! Nadie puede ayudarlo a recuperar su Frase secreta de recuperación"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "<PERSON><PERSON><PERSON> recuperarla para usted"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "Si pierde su frase secreta de recuperación, ésta se perderá para siempre. Nadie puede ayudarle a recuperarla, sin importar lo que digan."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "¡Incorrecto! Nadie puede ayudarlo a recuperar su frase secreta de recuperación"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON>, incluso un agente de soporte, le pide su frase secreta de recuperación..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "Lo están estafando"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Cualquiera que afirme necesitar su frase secreta de recuperación le está mintiendo. Si la comparte, le robarán sus activos."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "¡Correcto! Compartir su frase secreta de recuperación nunca es una buena idea"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "Debería dá<PERSON>la"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Cualquiera que afirme necesitar su frase secreta de recuperación le está mintiendo. Si la comparte, le robarán sus activos."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "¡No! Nunca comparta su frase secreta de recuperación con nadie, nunca"}, "srpSecurityQuizTitle": {"message": "Cuestionario de seguridad"}, "srpToggleShow": {"message": "Mostrar/Ocultar esta palabra de la frase secreta de recuperación", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "Esta palabra está escondida", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Esta palabra se está mostrando", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Estable"}, "stableLowercase": {"message": "estable"}, "stake": {"message": "Staking"}, "staked": {"message": "Con staking"}, "standardAccountLabel": {"message": "Cuenta estándar"}, "startEarning": {"message": "Comienza a ganar"}, "stateLogError": {"message": "Error al recuperar los registros de estado."}, "stateLogFileName": {"message": "Registros de estado de MetaMask"}, "stateLogs": {"message": "Registros de estado"}, "stateLogsDescription": {"message": "Los registros de estado contienen sus direcciones de cuentas públicas y las transacciones enviadas."}, "status": {"message": "Estado"}, "statusNotConnected": {"message": "No conectado"}, "statusNotConnectedAccount": {"message": "No hay cuentas conectadas"}, "step1LatticeWallet": {"message": "Conecte su Lattice1"}, "step1LatticeWalletMsg": {"message": "Puede conectar MetaMask a su dispositivo Lattice1 una vez que esté configurado y en línea. Desbloquee su dispositivo y tenga a mano el ID correspondiente.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Descargar la aplicación de Ledger"}, "step1LedgerWalletMsg": {"message": "Descargue y configure la aplicación, e ingrese su contraseña para desbloquear $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Conecte su Trezor"}, "step1TrezorWalletMsg": {"message": "Conecte su Trezor directamente al equipo y desbloquéelo. Asegúrese de utilizar la frase de contraseña correcta.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Conecte su Ledger"}, "step2LedgerWalletMsg": {"message": "Conecte su Ledger directamente a su equipo, desbloquéelo y abra la aplicación Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "¿Sigue recibiendo este mensaje?"}, "strong": {"message": "<PERSON><PERSON>e"}, "stxCancelled": {"message": "El intercambio habría fallado"}, "stxCancelledDescription": {"message": "Su transacción pudo haber fallado y fue cancelada para protegerlo de pagar comisiones de gas innecesarias."}, "stxCancelledSubDescription": {"message": "Intente su swap nuevamente. Estaremos aquí para protegerlo contra riesgos similares la próxima vez."}, "stxFailure": {"message": "Error al intercambiar"}, "stxFailureDescription": {"message": "Los cambios repentinos del mercado pueden causar fallas. Si el problema persiste, comuníquese con $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "Active las Transacciones inteligentes para realizar transacciones más confiables y seguras en las redes compatibles. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Enviando su intercambio de forma privada..."}, "stxPendingPubliclySubmittingSwap": {"message": "Enviando su intercambio de forma pública..."}, "stxSuccess": {"message": "¡Intercambio finalizado!"}, "stxSuccessDescription": {"message": "Su $1 ya está disponible.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "El intercambio finalizará en <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Intentando cancelar su transacción..."}, "stxUnknown": {"message": "Estado desconocido"}, "stxUnknownDescription": {"message": "Una transacción se ha realizado correctamente, pero no estamos seguros de qué se trata. Esto puede deberse a que envió otra transacción mientras se procesaba este intercambio."}, "stxUserCancelled": {"message": "Intercambio cancelado"}, "stxUserCancelledDescription": {"message": "Su transacción ha sido cancelada y no pagó ninguna comisión de gas innecesaria."}, "submit": {"message": "Enviar"}, "submitted": {"message": "Enviado"}, "suggestedBySnap": {"message": "Sugerido por $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Símbolo de moneda sugerido:"}, "suggestedTokenName": {"message": "Nombre sugerido:"}, "supplied": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "support": {"message": "Soporte técnico"}, "supportCenter": {"message": "Visite nuestro Centro de soporte técnico"}, "supportMultiRpcInformation": {"message": "Ahora admitimos varias RPC para una sola red. Su RPC más reciente se ha seleccionado como predeterminada para resolver información conflictiva."}, "surveyConversion": {"message": "Responda a nuestra encuesta"}, "surveyTitle": {"message": "Dé forma al futuro de MetaMask"}, "swap": {"message": "Intercambiar"}, "swapAdjustSlippage": {"message": "Aju<PERSON>"}, "swapAggregator": {"message": "Agregador"}, "swapAllowSwappingOf": {"message": "Permitir intercambio de $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON>"}, "swapAmountReceivedInfo": {"message": "Se refiere al monto mínimo que recibirá. Puede recibir más en función del deslizamiento."}, "swapAndSend": {"message": "Can<PERSON>ar y enviar"}, "swapAnyway": {"message": "Intercambiar de todos modos"}, "swapApproval": {"message": "Aprobar $1 para intercambios", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Necesita $1 más $2 para completar este intercambio", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "¿Sigue ahí?"}, "swapAreYouStillThereDescription": {"message": "Estamos listos para mostrarle las últimas cotizaciones cuando desee continuar"}, "swapConfirmWithHwWallet": {"message": "Confirmar con su monedero físico"}, "swapContinueSwapping": {"message": "<PERSON><PERSON><PERSON><PERSON> inter<PERSON>"}, "swapContractDataDisabledErrorDescription": {"message": "En la aplicación de Ethereum en su Ledger, diríjase a \"Configuración\" y habilite los datos de contrato. A continuación, vuelva a intentar su intercambio."}, "swapContractDataDisabledErrorTitle": {"message": "Los datos de contrato no se habilitaron en su Ledger"}, "swapCustom": {"message": "personalizado"}, "swapDecentralizedExchange": {"message": "Cambio descentralizado"}, "swapDirectContract": {"message": "Contrato directo"}, "swapEditLimit": {"message": "<PERSON>ar l<PERSON>"}, "swapEnableDescription": {"message": "Esta acción es obligatoria y le da permiso a MetaMask para intercambiar su $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Esto será $1 por intercambiar", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Introduzca un importe"}, "swapEstimatedNetworkFees": {"message": "Tarifas de red estimadas"}, "swapEstimatedNetworkFeesInfo": {"message": "Un estimado de la tarifa de red que se usará para completar el intercambio. El monto real puede cambiar según las condiciones de la red."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "<PERSON><PERSON><PERSON> ocurrir fallas en las transacciones, por lo que estamos aquí para ayudarlo. Si el problema continúa, comuníquese con nuestro soporte al cliente al $1 para recibir ayuda adicional.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "Error al intercambiar"}, "swapFetchingQuote": {"message": "Obteniendo cotización"}, "swapFetchingQuoteNofN": {"message": "Obtener cotización $1 de $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Obteniendo cotizaciones..."}, "swapFetchingQuotesErrorDescription": {"message": "Se produjo un error. Vuelva a intentarlo o, si el error persiste, póngase en contacto con el soporte al cliente."}, "swapFetchingQuotesErrorTitle": {"message": "Error al capturar cotizaciones"}, "swapFromTo": {"message": "El intercambio de $1 por $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Las tarifas de gas son estimadas y fluctuarán en función del tráfico de la red y la complejidad de las transacciones."}, "swapGasFeesExplanation": {"message": "MetaMask no obtiene dinero de las tarifas de gas. Estas tarifas son estimaciones y pueden cambiar según el nivel de tráfico de la red y la complejidad de la transacción. Más información $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "aquí", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Obtenga más información sobre las tarifas de gas"}, "swapGasFeesSplit": {"message": "Las tarifas de gas en la pantalla anterior se dividen entre estas dos transacciones."}, "swapGasFeesSummary": {"message": "Las tarifas de gas se pagan a los mineros de criptomonedas que procesan transacciones en la red $1. MetaMask no se beneficia de las tarifas de gas.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Esta cotización incorpora las tarifas de gas al ajustar la cantidad de tokens enviada o recibida. Puede recibir ETH en una transacción separada en su lista de actividades."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Más información sobre las tarifas de gas"}, "swapHighSlippage": {"message": "Deslizamiento alto"}, "swapIncludesGasAndMetaMaskFee": {"message": "Incluye gas y una tasa de MetaMask del $1%", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Incluye una tasa de MetaMask del $1%.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "La cotización refleja la tarifa de MetaMask del $1 %", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "Incluye una tarifa MetaMask de $1% - $2", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Más información sobre los intercambios"}, "swapLiquiditySourceInfo": {"message": "Buscamos varias fuentes de liquidez (sitios de cambio, agregadores y creadores de mercado profesionales) para comparar las mejores tasas de cambio y las tarifas de red."}, "swapLowSlippage": {"message": "Deslizamiento bajo"}, "swapMaxSlippage": {"message": "<PERSON><PERSON><PERSON>"}, "swapMetaMaskFee": {"message": "Ta<PERSON><PERSON> de MetaMask"}, "swapMetaMaskFeeDescription": {"message": "La tarifa de $1% se incluye automáticamente en esta cotización. Lo paga a cambio de una licencia para usar el software de agregación de información del proveedor de liquidez de MetaMask.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 cotizaciones.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Cotizaciones nuevas en $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "No hay tokens disponibles que coincidan con $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Su $1 se agregará a la cuenta una vez que se procese esta transacción.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Está a punto de intercambiar $1 $2 (~$3) por $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Diferencia de precio de ~$1 %", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "No se pudo determinar el impacto sobre el precio debido a la falta de datos de los precios del mercado. Antes de realizar el intercambio, confirme que está de acuerdo con la cantidad de tokens que está a punto de recibir."}, "swapPriceUnavailableTitle": {"message": "Antes de continuar, verifique su tasa"}, "swapProcessing": {"message": "Procesamiento"}, "swapQuoteDetails": {"message": "Detalles de cotización"}, "swapQuoteNofM": {"message": "$1 de $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Fuente de la cotización"}, "swapQuotesExpiredErrorDescription": {"message": "Solicite cotizaciones nuevas para tener los costos más recientes."}, "swapQuotesExpiredErrorTitle": {"message": "Tiempo de espera de cotizaciones"}, "swapQuotesNotAvailableDescription": {"message": "Esta ruta de operaciones no está disponible en este momento. Intente cambiar el importe, la red o el token y encontraremos la mejor opción."}, "swapQuotesNotAvailableErrorDescription": {"message": "Intente ajustar la configuración de monto o deslizamiento y vuelva a intentarlo."}, "swapQuotesNotAvailableErrorTitle": {"message": "No hay cotizaciones disponibles"}, "swapRate": {"message": "<PERSON><PERSON><PERSON>"}, "swapReceiving": {"message": "Recibiendo"}, "swapReceivingInfoTooltip": {"message": "Este es un valor estimado. El monto exacto depende del deslizamiento."}, "swapRequestForQuotation": {"message": "Solicitud de cotización"}, "swapSelect": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "swapSelectAQuote": {"message": "Seleccionar una cotización"}, "swapSelectAToken": {"message": "Se<PERSON><PERSON><PERSON><PERSON> token"}, "swapSelectQuotePopoverDescription": {"message": "A continuación, se muestran todas las cotizaciones recopiladas de diversas fuentes de liquidez."}, "swapSelectToken": {"message": "Se<PERSON><PERSON><PERSON><PERSON> token"}, "swapShowLatestQuotes": {"message": "Mostrar cotizaciones más recientes"}, "swapSlippageHighDescription": {"message": "El deslizamiento ingresado ($1%) se considera muy alto y puede resultar en una mala tasa", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "Deslizamiento alto"}, "swapSlippageLowDescription": {"message": "Un valor tan bajo ($1%) puede resultar en un intercambio fallido", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Deslizamiento bajo"}, "swapSlippageNegativeDescription": {"message": "El deslizamiento debe ser mayor o igual que cero"}, "swapSlippageNegativeTitle": {"message": "Aumentar el deslizamiento para continuar"}, "swapSlippageOverLimitDescription": {"message": "La tolerancia al deslizamiento debe ser del 15 % o menos. Cualquier cosa más alta resultará en una mala tasa."}, "swapSlippageOverLimitTitle": {"message": "Reducir el deslizamiento para continuar"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Si el precio cambia entre el momento en que hace el pedido y cuando se confirma, se denomina \"deslizamiento\". El canje se cancelará automáticamente si el deslizamiento supera lo establecido en la configuración de la \"tolerancia de deslizamiento\"."}, "swapSlippageZeroDescription": {"message": "Hay menos proveedores de cotizaciones de deslizamiento cero, lo que resultará en una cotización menos competitiva."}, "swapSlippageZeroTitle": {"message": "Abastecimiento de proveedores de deslizamiento cero"}, "swapSource": {"message": "Fuente de liquidez"}, "swapSuggested": {"message": "Intercambio sugerido"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Los intercambios son transacciones complejas y urgentes. Recomendamos esta tarifa de gas para lograr un buen equilibrio entre el costo y la garantía de un intercambio exitoso."}, "swapSwapFrom": {"message": "Intercambiar de"}, "swapSwapSwitch": {"message": "Cambiar orden de los tokens"}, "swapSwapTo": {"message": "Intercambiar a"}, "swapToConfirmWithHwWallet": {"message": "para confirmar con su monedero físico"}, "swapTokenAddedManuallyDescription": {"message": "Verifique este token en $1 y asegúrese de que sea el token que desea operar.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token aña<PERSON>do <PERSON>e"}, "swapTokenAvailable": {"message": "Su $1 se agregó a la cuenta.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "No se pudo recuperar su saldo de $1", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "El token no está disponible para intercambiar en esta región"}, "swapTokenToToken": {"message": "Intercambiar $1 por $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 solo se verifica en 1 fuente. Considere verificarlo en $2 antes de continuar.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Token potencialmente falso"}, "swapTokenVerifiedSources": {"message": "Confirmado por fuentes de $1. Verificar en $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 permite hasta $2 decimales", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transacción completa"}, "swapTwoTransactions": {"message": "2 transacciones"}, "swapUnknown": {"message": "Desconocido"}, "swapZeroSlippage": {"message": "0 % de deslizamiento"}, "swapsMaxSlippage": {"message": "Tolerancia de deslizamiento"}, "swapsNotEnoughToken": {"message": "No hay suficiente $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Ver en actividad"}, "switch": {"message": "Cambiar"}, "switchEthereumChainConfirmationDescription": {"message": "Esto cambiará la red seleccionada en MetaMask por una red agregada con anterioridad:"}, "switchEthereumChainConfirmationTitle": {"message": "¿Le permite a este sitio cambiar la red?"}, "switchInputCurrency": {"message": "Cambiar moneda de entrada"}, "switchNetwork": {"message": "Cambiar red"}, "switchNetworks": {"message": "Cambiar redes"}, "switchToNetwork": {"message": "Cambiar a $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Cambiar a esta cuenta"}, "switchedNetworkToastDecline": {"message": "No volver a mostrar"}, "switchedNetworkToastMessage": {"message": "$1 ahora está activo en $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Ahora está usando $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "Cambiar de red cancelará todas las confirmaciones pendientes"}, "symbol": {"message": "Símbolo"}, "symbolBetweenZeroTwelve": {"message": "El símbolo debe tener 11 caracteres o menos."}, "tenPercentIncreased": {"message": "10 % de aumento"}, "terms": {"message": "T<PERSON><PERSON><PERSON>s de uso"}, "termsOfService": {"message": "Términos de servicio"}, "termsOfUseAgreeText": {"message": " Acepto los Términos de uso, que se aplican al uso que hago de MetaMask y de todas sus funcionalidades"}, "termsOfUseFooterText": {"message": "Por favor, desplácese para leer todas las secciones"}, "termsOfUseTitle": {"message": "Nuestros Términos de uso han sido actualizados"}, "testNetworks": {"message": "Redes de prueba"}, "testnets": {"message": "Red de pruebas"}, "theme": {"message": "<PERSON><PERSON>"}, "themeDescription": {"message": "Elija su tema MetaMask preferido."}, "thirdPartySoftware": {"message": "Aviso de software de terceros", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Tiempo"}, "tipsForUsingAWallet": {"message": "Consejos para utilizar un monedero"}, "tipsForUsingAWalletDescription": {"message": "Al añadir tokens se desbloquean más formas de utilizar Web3."}, "to": {"message": "Para"}, "toAddress": {"message": "Para: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Utilizamos los servicios de 4byte.directory y Sourcify para decodificar y mostrar datos de transacciones de forma más legible. Esto le ayuda a comprender el resultado de las transacciones pendientes y pasadas, pero puede dar lugar a que se comparta su dirección IP."}, "token": {"message": "Token"}, "tokenAddress": {"message": "Dirección del token"}, "tokenAlreadyAdded": {"message": "Ya se agregó el token."}, "tokenAutoDetection": {"message": "Detección automática de tokens"}, "tokenContractAddress": {"message": "Dirección de contrato de token"}, "tokenDecimal": {"message": "Decimales del token"}, "tokenDecimalFetchFailed": {"message": "Se requiere decimal del token. Encuéntrelo en: $1"}, "tokenDetails": {"message": "Detalles del token"}, "tokenFoundTitle": {"message": "1 nuevo token encontrado"}, "tokenId": {"message": "ID de token"}, "tokenList": {"message": "Lista de tókenes"}, "tokenMarketplace": {"message": "Mercado de tokens"}, "tokenScamSecurityRisk": {"message": "estafas de tokens y riesgos de seguridad"}, "tokenStandard": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tokenSymbol": {"message": "Símbolo del token"}, "tokens": {"message": "Tokens"}, "tokensFoundTitle": {"message": "$1 nuevos tokens encontrados", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Tokens en la colección"}, "tooltipApproveButton": {"message": "Co<PERSON><PERSON><PERSON>"}, "tooltipSatusConnected": {"message": "conectado"}, "tooltipSatusConnectedUpperCase": {"message": "Conectado"}, "tooltipSatusNotConnected": {"message": "no conectado"}, "total": {"message": "Total"}, "totalVolume": {"message": "Volúmen total"}, "transaction": {"message": "transacción"}, "transactionCancelAttempted": {"message": "Se intentó cancelar la transacción con una tarifa de gas de $1 en $2"}, "transactionCancelSuccess": {"message": "La transacción de canceló correctamente en $2"}, "transactionConfirmed": {"message": "La transacción de confirmó en $2."}, "transactionCreated": {"message": "La transacción se creó con un valor de $1 en $2."}, "transactionDataFunction": {"message": "Función"}, "transactionDetailGasHeading": {"message": "Tarifa estimada de gas"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Monto + cargos"}, "transactionDropped": {"message": "La transacción se abandonó en $2."}, "transactionError": {"message": "Error de transacción. Excepción generada en el código de contrato."}, "transactionErrorNoContract": {"message": "Intentando llamar a una función en una dirección sin contrato."}, "transactionErrored": {"message": "La transacción encontró un error."}, "transactionFlowNetwork": {"message": "Red"}, "transactionHistoryBaseFee": {"message": "Tarifa base (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Tarifa total de gas L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Límite de gas L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Precio de gas L2"}, "transactionHistoryMaxFeePerGas": {"message": "Tarifa máxima por gas"}, "transactionHistoryPriorityFee": {"message": "Tarifa de prioridad (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Tarifa total de gas"}, "transactionIdLabel": {"message": "ID de transacción", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Esta transacción incluye: $1."}, "transactionResubmitted": {"message": "Transacción reenviada con la tarifa de gas aumentada a $1 en $2"}, "transactionSettings": {"message": "Ajustes de la transacción"}, "transactionSubmitted": {"message": "Transacción enviada con una tarifa de gas de $1 en $2."}, "transactionTotalGasFee": {"message": "Tarifa de gas total", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "La transacción se actualizó en $2."}, "transactions": {"message": "Transacciones"}, "transfer": {"message": "Transferir"}, "transferCrypto": {"message": "Transferir criptomonedas"}, "transferFrom": {"message": "Transferir desde"}, "transferRequest": {"message": "Solicitud de transferencia"}, "trillionAbbreviation": {"message": "b", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Tenemos problemas para conectarnos con su Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Revise nuestra guía de conexión de monederos físicos y vuelva a intentarlo.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Si tiene la última versión de Firefox, es posible que experimente un problema relacionado con la eliminación de la compatibilidad con U2F de Firefox. Aprenda a solucionar este problema $1.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "aquí", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "Tuvimos problemas al conectar su $1. Pruebe revisar $2 e inténtelo de nuevo.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "MetaMask tuvo problemas para iniciar. Este error podría ser intermitente, así que intente reiniciar la extensión."}, "tryAgain": {"message": "Vuelva a intentarlo"}, "turnOff": {"message": "Desactivar"}, "turnOffMetamaskNotificationsError": {"message": "Hubo un error al desactivar las notificaciones. Vuelva a intentarlo más tarde."}, "turnOn": {"message": "Activar"}, "turnOnMetamaskNotifications": {"message": "Activar las notificaciones"}, "turnOnMetamaskNotificationsButton": {"message": "Activar"}, "turnOnMetamaskNotificationsError": {"message": "Hubo un error al crear las notificaciones. Vuelva a intentarlo más tarde."}, "turnOnMetamaskNotificationsMessageFirst": {"message": "Manténgase informado sobre lo que sucede en su monedero con notificaciones."}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "configuración de notificaciones."}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "Obtenga más información sobre cómo protegemos su privacidad mientras utiliza esta función."}, "turnOnMetamaskNotificationsMessageSecond": {"message": "Para usar las notificaciones del monedero, utilizamos un perfil para sincronizar algunas configuraciones en sus dispositivos. $1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "Puede desactivar las notificaciones en cualquier momento en $1"}, "turnOnTokenDetection": {"message": "Activar la detección mejorada de tokens"}, "tutorial": {"message": "Tutorial"}, "twelveHrTitle": {"message": "12 horas:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "No aprobado"}, "unexpectedBehavior": {"message": "Este comportamiento es inesperado y debe comunicarse como un error, incluso si sus cuentas se restauran correctamente. Use el enlace a continuación para enviar un informe de errores a MetaMask."}, "units": {"message": "unidades"}, "unknown": {"message": "Desconocido"}, "unknownCollection": {"message": "Colección sin nombre"}, "unknownNetworkForKeyEntropy": {"message": "Red desconocida", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Error: No se pudo identificar ese código QR"}, "unlimited": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"message": "Desb<PERSON>que<PERSON>"}, "unpin": {"message": "Desanclar"}, "unrecognizedChain": {"message": "No se reconoce esta red personalizada", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "Actualmente no se admite el envío de tokens NFT (ERC-721)", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "El precio de este token en USD es muy volátil, lo que indica un alto riesgo de perder valor significativo al interactuar con él."}, "unstableTokenPriceTitle": {"message": "Precio inestable del token"}, "upArrow": {"message": "flecha ascendente"}, "update": {"message": "Actualizar"}, "updateEthereumChainConfirmationDescription": {"message": "Este sitio solicita actualizar la URL de su red predeterminada. Puede editar los valores predeterminados y la información de la red en cualquier momento."}, "updateNetworkConfirmationTitle": {"message": "Actualizar $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Actualice su información o"}, "updateRequest": {"message": "Solicitud de actualización"}, "updatedRpcForNetworks": {"message": "RPC de red actualizadas"}, "uploadDropFile": {"message": "Ingrese su archivo aquí"}, "uploadFile": {"message": "Cargar archivo"}, "urlErrorMsg": {"message": "Las direcciones URL requieren el prefijo HTTP/HTTPS adecuado."}, "use4ByteResolution": {"message": "Decodificar contratos inteligentes"}, "useMultiAccountBalanceChecker": {"message": "Solicitudes de saldo de cuenta por lotes"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Obtenga actualizaciones de saldo más rápidas mediante el procesamiento por lotes de solicitudes de saldo de cuenta. Esto nos permite obtener los saldos de su cuenta juntos, para que obtenga actualizaciones más rápidas para una experiencia mejorada. Cuando esta función está desactivada, es menos probable que terceros logran asociar a sus cuentas entre sí."}, "useNftDetection": {"message": "Detección automática de NFT"}, "useNftDetectionDescriptionText": {"message": "Deje que MetaMask agregue los NFT de su propiedad mediante servicios de terceros. La detección automática de los NFT expone su IP y dirección de cuenta a estos servicios. Habilitar esta función podría asociar su dirección IP con su dirección de Ethereum y mostrar NFT falsos enviados mediante airdrop por estafadores. Puede agregar tókenes manualmente para evitar este riesgo."}, "usePhishingDetection": {"message": "Usar detección de phishing"}, "usePhishingDetectionDescription": {"message": "Mostrar una advertencia respecto de los dominios de phishing dirigidos a los usuarios de Ethereum"}, "useSafeChainsListValidation": {"message": "Verificación de detalles de la red"}, "useSafeChainsListValidationDescription": {"message": "MetaMask utiliza un servicio de terceros llamado $1 para mostrar detalles de red precisos y estandarizados. Esto reduce las posibilidades de conectarse a una red maliciosa o incorrecta. Al utilizar esta función, su dirección IP queda expuesta a chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "La visualización automática de tokens enviados a su cuenta implica la comunicación con servidores de terceros para obtener imágenes de tokens. Esos servicios tendrán acceso a su dirección IP."}, "usedByClients": {"message": "Usado por una variedad de clientes distintos"}, "userName": {"message": "Nombre de usuario"}, "userOpContractDeployError": {"message": "La implementación de contratos desde una cuenta de contrato inteligente no es compatible"}, "version": {"message": "Versión"}, "view": {"message": "<PERSON>er"}, "viewActivity": {"message": "Ver actividad"}, "viewAllQuotes": {"message": "ver todas las cotizaciones"}, "viewContact": {"message": "Ver contacto"}, "viewDetails": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "viewMore": {"message": "<PERSON>er más"}, "viewOnBlockExplorer": {"message": "Ver en el explorador de bloques"}, "viewOnCustomBlockExplorer": {"message": "Ver $1 en $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Ver $1 en Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Ver en el explorador"}, "viewOnOpensea": {"message": "Ver en Opensea"}, "viewSolanaAccount": {"message": "Ver cuenta de Solana"}, "viewTransaction": {"message": "Ver transacción"}, "viewinExplorer": {"message": "Ver $1 en el explorador", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Visitar sitio"}, "visitSupportDataConsentModalAccept": {"message": "Confirmar"}, "visitSupportDataConsentModalDescription": {"message": "¿Desea compartir su identificador de MetaMask y la versión de la aplicación con nuestro Centro de soporte técnico? Esto puede ayudarnos a resolver mejor su problema, pero es opcional."}, "visitSupportDataConsentModalReject": {"message": "No compartir"}, "visitSupportDataConsentModalTitle": {"message": "Compartir detalles del dispositivo con soporte"}, "visitWebSite": {"message": "Visite nuestro sitio web"}, "wallet": {"message": "<PERSON><PERSON><PERSON>"}, "walletConnectionGuide": {"message": "nuestra guía de conexión del monedero físico"}, "wantToAddThisNetwork": {"message": "¿Desea añadir esta red?"}, "wantsToAddThisAsset": {"message": "$1 quiere agregar este activo a su monedero."}, "warning": {"message": "Advertencia"}, "warningFromSnap": {"message": "Advertencia de $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Al activar esta opción, podrá ver las cuentas de Ethereum a través de una dirección pública o un nombre de ENS. Para recibir comentarios sobre esta función Beta, complete este $1.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Observar cuentas Ethereum (Beta)"}, "watchOutMessage": {"message": "Cuidado con $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "<PERSON><PERSON><PERSON>"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Parece que el sitio web actual intentó utilizar la API de window.web3 que se eliminó. Si el sitio no funciona, haga clic en $1 para obtener más información.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "sitios web", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Bienvenido de nuevo"}, "welcomeToMetaMask": {"message": "Comencemos"}, "whatsThis": {"message": "¿Qué es esto?"}, "willApproveAmountForBridging": {"message": "Esto aprobará $1 para puentear."}, "willApproveAmountForBridgingHardware": {"message": "Deberá confirmar dos transacciones en su monedero físico."}, "withdrawing": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "wrongNetworkName": {"message": "Según nuestros registros, es posible que el nombre de la red no coincida correctamente con este ID de cadena."}, "yes": {"message": "Sí"}, "you": {"message": "Usted"}, "youDeclinedTheTransaction": {"message": "Usted rechazó la transacción."}, "youNeedToAllowCameraAccess": {"message": "Necesita permitir el acceso a la cámara para usar esta función."}, "youReceived": {"message": "Usted recibió", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "<PERSON><PERSON> envió", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Sus cuentas"}, "yourActivity": {"message": "Su actividad"}, "yourBalance": {"message": "Su saldo"}, "yourNFTmayBeAtRisk": {"message": "Sus NFT podrían estar en riesgo"}, "yourNetworks": {"message": "Sus redes"}, "yourPrivateSeedPhrase": {"message": "Su frase secreta de recuperación"}, "yourTransactionConfirmed": {"message": "Transacción ya confirmada"}, "yourTransactionJustConfirmed": {"message": "No pudimos cancelar su transacción antes de que se confirmara en la cadena de bloques."}, "yourWalletIsReady": {"message": "Su monedero está listo"}}