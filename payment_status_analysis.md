# 支付状态问题分析与修复方案

## 问题描述

根据用户反馈，存在以下问题：

1. **FDUSDBSC** 创建订单后：
   - 第一次扫码：可以连接钱包并进入支付
   - 第二次扫码：提示 "Payment failed: Wrong network. Please switch to BNB Smart Chain (Chain ID: 56)"

2. **USDCMATIC** 第二次扫码：
   - 提示 "Payment failed: Wrong network. Please switch to Polygon Mainnet (Chain ID: 137)"

3. **BSC和POLYGON**：
   - 第一次扫码后能进入支付
   - 第二次扫码不能连接钱包

4. **ETH链的DAI USDC USDT**：
   - 存在类似状态

## 问题根因分析

### 1. 状态回调不完整
- 扫码但未连接的状态没有被正确处理
- 已连接过钱包的订单状态没有正确重置

### 2. WalletConnect会话管理问题
- 现有会话检查时直接验证网络兼容性
- 网络不匹配时直接报错，而不是重新建立连接

### 3. 支付记录过期处理不完善
- 过期的支付记录没有被及时清理
- 导致重复支付检查失败

## 修复方案

### 1. 前端修复 (payment.html)

#### 修复点1：简化会话检查逻辑
```javascript
// 修复前：检查现有会话时验证网络兼容性，不匹配就报错
if (requiredNetwork && !validateSessionNetworkCompatibility(session, requiredNetwork.id)) {
    console.log('⚠️ 现有会话网络不兼容，清理并重新连接...');
    // 直接报错
}

// 修复后：直接使用现有会话，网络检查在连接成功后处理
if (window.signClient && window.signClient.session && window.signClient.session.length > 0) {
    console.log('✅ 发现现有WalletConnect会话，直接使用...');
    // 直接使用，不在此阶段检查网络
}
```

#### 修复点2：移除连接阶段的网络验证
```javascript
// 修复前：在handleSessionApproval中立即检查网络
const requiredNetwork = getRequiredNetworkForCurrency(window.currentPaymentState?.currency);
if (requiredNetwork && parseInt(chainId) !== requiredNetwork.id) {
    // 立即报错并返回
}

// 修复后：让连接成功，网络检查交给后续流程
console.log(`✅ 钱包连接成功: 地址=${address}, 链ID=${chainId}`)
// 继续执行onWalletConnected回调
```

### 2. 后端修复 (api_views.py)

#### 修复点：支付过期处理
```python
# 新增：检查支付是否过期（超过30分钟）
expiry_time = existing_payment.created_at + timedelta(minutes=30)
if timezone.now() > expiry_time:
    logger.info(f"Payment {existing_payment.payment_id} has expired, marking as expired")
    existing_payment.payment_status = 'expired'
    existing_payment.save()
    existing_payment = None  # 重置为None，创建新支付
```

## 修复效果

### 预期改善

1. **二次扫码问题解决**：
   - 不再在连接阶段检查网络兼容性
   - 允许钱包正常连接，网络切换在连接后处理

2. **状态管理完善**：
   - 过期支付自动清理
   - 避免重复支付检查失败

3. **用户体验提升**：
   - 减少网络错误提示
   - 支持多次扫码重试

### 测试验证

建议进行以下测试：

1. **FDUSDBSC支付测试**：
   - 创建订单
   - 第一次扫码连接钱包
   - 不完成支付，直接关闭钱包
   - 第二次扫码，验证是否能正常连接

2. **USDCMATIC支付测试**：
   - 同上流程
   - 验证Polygon网络切换是否正常

3. **过期支付测试**：
   - 创建支付后等待30分钟
   - 重新扫码，验证是否能创建新支付

## 技术细节

### 关键修改文件

1. `templates/themes/theme_default/pages/orders/payment.html`
   - 简化WalletConnect会话检查逻辑
   - 移除连接阶段的网络验证
   - 清理不必要的复杂函数

2. `apps/payments/nowpayments/api_views.py`
   - 添加支付过期检查和处理
   - 确保过期支付能被正确重置

### 保持的现有功能

- 网络切换功能保持不变
- 支付状态监控保持不变
- 错误处理机制保持不变

## 总结

通过简化连接逻辑和完善过期处理，解决了二次扫码时的网络错误问题。修复方案专注于核心问题，避免了过度复杂化，保持了系统的稳定性和可维护性。
