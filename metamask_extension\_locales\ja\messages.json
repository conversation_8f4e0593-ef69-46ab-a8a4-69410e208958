{"QRHardwareInvalidTransactionTitle": {"message": "エラー"}, "QRHardwareMismatchedSignId": {"message": "トランザクションデータが一致していません。トランザクションの詳細を確認してください。"}, "QRHardwarePubkeyAccountOutOfRange": {"message": "他にアカウントはありません。以下のリストにない別のアカウントにアクセスする場合は、ハードウェアウォレットを接続しなおして選択してください。"}, "QRHardwareScanInstructions": {"message": "カメラをQRコードに向けてください。画面がぼやけていますが、読み取りには影響しません。"}, "QRHardwareSignRequestCancel": {"message": "拒否"}, "QRHardwareSignRequestDescription": {"message": "ウォレットで署名したら、「署名を取得」をクリックして署名を受け取ります"}, "QRHardwareSignRequestGetSignature": {"message": "署名を取得"}, "QRHardwareSignRequestSubtitle": {"message": "ウォレットでQRコードをスキャンします"}, "QRHardwareSignRequestTitle": {"message": "署名をリクエスト"}, "QRHardwareUnknownQRCodeTitle": {"message": "エラー"}, "QRHardwareUnknownWalletQRCode": {"message": "QRコードが無効です。ハードウェアウォレットの同期QRコードをスキャンしてください。"}, "QRHardwareWalletImporterTitle": {"message": "QRコードのスキャン"}, "QRHardwareWalletSteps1Description": {"message": "公式QRコードをサポートする以下のパートナーのリストから選択できます。"}, "QRHardwareWalletSteps1Title": {"message": "QRハードウェアウォレットを接続"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "$1件のアカウントを非表示", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "1件のアカウントを非表示"}, "SrpListShowAccounts": {"message": "$1件のアカウントを表示", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "1件のアカウントを表示"}, "about": {"message": "基本情報"}, "accept": {"message": "同意する"}, "acceptTermsOfUse": {"message": "$1を読んで同意しました", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "カメラにアクセス中..."}, "account": {"message": "アカウント"}, "accountActivity": {"message": "アカウントアクティビティ"}, "accountActivityText": {"message": "通知を受けたいアカウントを選択してください:"}, "accountDetails": {"message": "アカウントの詳細"}, "accountIdenticon": {"message": "アカウントのアイデンティコン"}, "accountIsntConnectedToastText": {"message": "$1は$2に接続されていません"}, "accountName": {"message": "アカウント名"}, "accountNameDuplicate": {"message": "このアカウント名は既に存在します", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "このアカウント名は予約されています", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "アカウントのオプション"}, "accountPermissionToast": {"message": "アカウントのアクセス許可が更新されました"}, "accountSelectionRequired": {"message": "アカウントを選択する必要があります！"}, "accountTypeNotSupported": {"message": "アカウントタイプがサポートされていません"}, "accounts": {"message": "アカウント"}, "accountsConnected": {"message": "アカウントが接続されました"}, "accountsPermissionsTitle": {"message": "アカウントを確認しトランザクションを提案する"}, "accountsSmallCase": {"message": "アカウント"}, "active": {"message": "アクティブ"}, "activity": {"message": "アクティビティ"}, "activityLog": {"message": "アクティビティのログ"}, "add": {"message": "追加"}, "addACustomNetwork": {"message": "カスタムネットワークを追加"}, "addANetwork": {"message": "ネットワークを追加"}, "addANickname": {"message": "ニックネームを追加"}, "addAUrl": {"message": "URLを追加"}, "addAccount": {"message": "アカウントを追加"}, "addAccountFromNetwork": {"message": "$1アカウントを追加", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "MetaMaskにアカウントを追加する"}, "addAcquiredTokens": {"message": "MetaMaskを使用して取得したトークンを追加します"}, "addAlias": {"message": "別名を追加"}, "addBitcoinAccountLabel": {"message": "ビットコインアカウント"}, "addBlockExplorer": {"message": "ブロックエクスプローラーを追加"}, "addBlockExplorerUrl": {"message": "ブロックエクスプローラーURLを追加"}, "addContact": {"message": "連絡先を追加"}, "addCustomNetwork": {"message": "カスタムネットワークを追加"}, "addEthereumChainWarningModalHeader": {"message": "このRPCプロバイダーは、確実に信頼できる場合のみ追加してください。$1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "悪質なプロバイダーは、ブロックチェーンのステートを偽り、ユーザーのネットワークアクティビティを記録する可能性があります。"}, "addEthereumChainWarningModalListHeader": {"message": "プロバイダーは次の権限を有するため、信頼性が重要です:"}, "addEthereumChainWarningModalListPointOne": {"message": "アカウントと IP アドレスの把握、およびそれらの関連付け"}, "addEthereumChainWarningModalListPointThree": {"message": "アカウントの残高およびその他オンチェーンステートの表示"}, "addEthereumChainWarningModalListPointTwo": {"message": "トランザクションのブロードキャスト"}, "addEthereumChainWarningModalTitle": {"message": "イーサリアムメインネット用の新しいRPCプロバイダーを追加しようとしています"}, "addEthereumWatchOnlyAccount": {"message": "イーサリアムアカウントの監視 (ベータ)"}, "addFriendsAndAddresses": {"message": "信頼できる友達とアドレスを追加する"}, "addHardwareWalletLabel": {"message": "ハードウェアウォレット"}, "addIPFSGateway": {"message": "優先IPFSゲートウェイを追加"}, "addImportAccount": {"message": "アカウントまたはハードウェアウォレットを追加"}, "addMemo": {"message": "メモを追加"}, "addNetwork": {"message": "ネットワークを追加"}, "addNetworkConfirmationTitle": {"message": "$1の追加", "description": "$1 represents network name"}, "addNewAccount": {"message": "新しいイーサリアムアカウントを追加"}, "addNewEthereumAccountLabel": {"message": "イーサリアムアカウント"}, "addNewSolanaAccountLabel": {"message": "Solanaアカウント"}, "addNft": {"message": "NFTを追加"}, "addNfts": {"message": "NFTを追加"}, "addNonEvmAccount": {"message": "$1アカウントを追加", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "$1ネットワークを有効にするには、$2アカウントを作成する必要があります。", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "RPC URLを追加"}, "addSnapAccountToggle": {"message": "「アカウントSnapの追加 (ベータ版)」を有効にする"}, "addSnapAccountsDescription": {"message": "この機能をオンにすると、アカウントリストから直接新しいベータ版のアカウントSnapを追加できるようになります。アカウントSnapをインストールする際は、サードパーティサービスである点にご注意ください。"}, "addSuggestedNFTs": {"message": "推奨されたNFTを追加"}, "addSuggestedTokens": {"message": "推奨されたトークンを追加"}, "addToken": {"message": "トークンを追加"}, "addTokenByContractAddress": {"message": "トークンが見つからない場合、アドレスを貼り付けて手動でトークンを追加できます。トークンコントラクトアドレスは$1にあります", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "URLを追加"}, "addingAccount": {"message": "アカウントの追加"}, "addingCustomNetwork": {"message": "ネットワークを追加中"}, "additionalNetworks": {"message": "他のネットワーク"}, "address": {"message": "アドレス"}, "addressCopied": {"message": "アドレスがコピーされました！"}, "addressMismatch": {"message": "サイトアドレスの不一致"}, "addressMismatchOriginal": {"message": "現在のURL: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Punycodeバージョン: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "高度な設定"}, "advancedBaseGasFeeToolTip": {"message": "トランザクションがブロックに含まれた場合、最大基本料金と実際の基本料金の差が返金されます。合計金額は、最大基本料金 (Gwei単位) * ガスリミットで計算されます。"}, "advancedDetailsDataDesc": {"message": "データ"}, "advancedDetailsHexDesc": {"message": "16進法"}, "advancedDetailsNonceDesc": {"message": "ナンス"}, "advancedDetailsNonceTooltip": {"message": "これはアカウントのトランザクション番号です。最初のトランザクションのナンスは0で、順番に上がっていきます。"}, "advancedGasFeeDefaultOptIn": {"message": "これらの値を$1ネットワークのデフォルトとして保存する", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "ガス代の高度な設定"}, "advancedGasPriceTitle": {"message": "ガス価格"}, "advancedPriorityFeeToolTip": {"message": "優先手数料 (別名「マイナーチップ」) はマイナーに直接支払われ、トランザクションを優先するインセンティブとなります。"}, "airDropPatternDescription": {"message": "トークンのオンチェーン履歴で、不審なエアドロップアクティビティの以前のインスタンスを確認できます。"}, "airDropPatternTitle": {"message": "エアドロップパターン"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "アラート"}, "alertAccountTypeUpgradeMessage": {"message": "アカウントをスマートアカウントにアップデートしようとしています。アカウントアドレスはそのままで、より高速なトランザクションと低いネットワーク手数料が利用できるようになります。$1。"}, "alertAccountTypeUpgradeTitle": {"message": "アカウントタイプ"}, "alertActionBuyWithNativeCurrency": {"message": "$1を購入"}, "alertActionUpdateGas": {"message": "ガスリミットを更新"}, "alertActionUpdateGasFee": {"message": "手数料を更新"}, "alertActionUpdateGasFeeLevel": {"message": "ガスオプションを更新"}, "alertDisableTooltip": {"message": "これは「設定」>「アラート」で変更できます"}, "alertMessageAddressMismatchWarning": {"message": "攻撃者は、サイトのアドレスに若干の変更を加えてサイトを模倣することがあります。続行する前に、意図したサイトとやり取りしていることを確認してください。"}, "alertMessageChangeInSimulationResults": {"message": "このトランザクションの推定増減額が更新されました。先に進む前によく確認してください。"}, "alertMessageFirstTimeInteraction": {"message": "このアドレスとやり取りするのは初めてです。先に進む前に、これが正しいことを確認してください。"}, "alertMessageGasEstimateFailed": {"message": "正確な手数料を提供できず、この見積もりは高い可能性があります。カスタムガスリミットの入力をお勧めしますが、それでもトランザクションが失敗するリスクがあります。"}, "alertMessageGasFeeLow": {"message": "低い手数料を選択すると、トランザクションに時間がかかり、待機時間が長くなります。より素早くトランザクションを行うには、市場に合った、または積極的な手数料のオプションを選択してください。"}, "alertMessageGasTooLow": {"message": "このトランザクションを続行するには、ガスリミットを21000以上に上げる必要があります。"}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "アカウントにネットワーク手数料を支払うのに十分な$1がありません。"}, "alertMessageNetworkBusy": {"message": "ガス価格が高く、見積もりはあまり正確ではありません。"}, "alertMessageNoGasPrice": {"message": "手数料を手動で更新するまでこのトランザクションを進めることができません。"}, "alertMessageSignInDomainMismatch": {"message": "要求元のサイトはサインインしようとしているサイトではありません。ログイン情報を盗もうとしている可能性があります。"}, "alertMessageSignInWrongAccount": {"message": "このサイトは正しくないアカウントでのサインインを求めています。"}, "alertModalAcknowledge": {"message": "リスクを承知したうえで続行します"}, "alertModalDetails": {"message": "アラートの詳細"}, "alertModalReviewAllAlerts": {"message": "すべてのアラートを確認する"}, "alertReasonChangeInSimulationResults": {"message": "結果が変更されました"}, "alertReasonFirstTimeInteraction": {"message": "初回のやり取り"}, "alertReasonGasEstimateFailed": {"message": "不正確な手数料"}, "alertReasonGasFeeLow": {"message": "低速"}, "alertReasonGasTooLow": {"message": "低ガスリミット"}, "alertReasonInsufficientBalance": {"message": "資金不足"}, "alertReasonNetworkBusy": {"message": "ネットワークが混雑中"}, "alertReasonNoGasPrice": {"message": "手数料の見積もりが利用できません"}, "alertReasonPendingTransactions": {"message": "保留中のトランザクション"}, "alertReasonSignIn": {"message": "不審なサインイン要求"}, "alertReasonWrongAccount": {"message": "正しくないアカウント"}, "alertSelectedAccountWarning": {"message": "このリクエストは、ウォレットで選択されているアカウントとは別のアカウントに対するものです。別のアカウントを使用するには、そのアカウントをサイトに接続してください。"}, "alerts": {"message": "アラート"}, "all": {"message": "すべて"}, "allNetworks": {"message": "すべてのネットワーク"}, "allPermissions": {"message": "すべてのアクセス許可"}, "allTimeHigh": {"message": "最高記録"}, "allTimeLow": {"message": "最低記録"}, "allowNotifications": {"message": "通知を許可する"}, "allowWithdrawAndSpend": {"message": "$1に以下の額までの引き出しと使用を許可します。", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "金額"}, "amountReceived": {"message": "受取額"}, "amountSent": {"message": "送金額"}, "andForListItems": {"message": "$1、および$2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1および$2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "世界有数の信頼性を誇る暗号資産ウォレット", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMaskベータ版", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "適用"}, "approve": {"message": "使用限度額の承認"}, "approveButtonText": {"message": "承認"}, "approveIncreaseAllowance": {"message": "$1の使用上限を上げる", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "$1の使用上限を承認する", "description": "The token symbol that is being approved"}, "approved": {"message": "承認済み"}, "approvedOn": {"message": "$1に承認", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "$1に$2に対して承認済み", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "よろしいですか？"}, "asset": {"message": "アセット"}, "assetChartNoHistoricalPrices": {"message": "過去のデータを取得できませんでした"}, "assetMultipleNFTsBalance": {"message": "$1個のNFT"}, "assetOptions": {"message": "アセットのオプション"}, "assetSingleNFTBalance": {"message": "$1個のNFT"}, "assets": {"message": "アセット"}, "assetsDescription": {"message": "ウォレットのトークンを自動検出し、NFTを表示して、アカウント残高の最新情報を一括で取得します"}, "attemptToCancelSwapForFree": {"message": "無料でスワップのキャンセルを試行"}, "attributes": {"message": "属性"}, "attributions": {"message": "属性"}, "auroraRpcDeprecationMessage": {"message": "Infura RPC URLでAuroraがサポートされなくなりました。"}, "authorizedPermissions": {"message": "以下の権限を承認しました"}, "autoDetectTokens": {"message": "トークンを自動検出"}, "autoDetectTokensDescription": {"message": "サードパーティAPIを使用して、ウォレットに送られた新しいトークンを検出・表示します。アプリがこれらのサービスからデータを自動的に取得しないようにするには、オフにしてください。$1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "オートロックタイマー (分)"}, "autoLockTimeLimitDescription": {"message": "MetaMaskがロックされるまでのアイドル時間を分単位で設定します。"}, "average": {"message": "平均"}, "back": {"message": "戻る"}, "backupAndSync": {"message": "バックアップと同期"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "基本機能"}, "backupAndSyncEnable": {"message": "バックアップと同期をオンにする"}, "backupAndSyncEnableConfirmation": {"message": "バックアップと同期をオンにすると、$1もオンになります。続行しますか？", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "バックアップと同期を使用すると、カスタム設定およびカスタム機能のデータが暗号化されてMetaMask側に保管されます。これにより、他のデバイスでもMetaMaskのエクスペリエンスが統一され、MetaMaskの再インストールが必要になった場合に設定と機能が復元されます。ただし、シークレットリカバリーフレーズはバックアップされません。$1。", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "設定はいつでも$1で更新できます", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "[設定] > [バックアップと同期]。"}, "backupAndSyncFeatureAccounts": {"message": "アカウント"}, "backupAndSyncManageWhatYouSync": {"message": "同期する項目の管理"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "デバイス間で同期する項目をオンにします。"}, "backupAndSyncPrivacyLink": {"message": "当社のプライバシー保護への取り組みについて見る"}, "backupAndSyncSlideDescription": {"message": "アカウントをバックアップして設定を同期します。"}, "backupAndSyncSlideTitle": {"message": "バックアップと同期のご紹介"}, "backupApprovalInfo": {"message": "このシークレット コードは、デバイスをなくしたとき、パスワードを忘れたとき、MetaMaskの再インストールが必要なとき、または別のデバイスでウォレットにアクセスするときに必要です。"}, "backupApprovalNotice": {"message": "シークレットリカバリーフレーズをバックアップして、ウォレットと資金の安全を確保してください。"}, "backupKeyringSnapReminder": {"message": "削除する前に、このSnapが作成したすべてのアカウントに自分でアクセスできることを確認してください"}, "backupNow": {"message": "今すぐバックアップ"}, "balance": {"message": "残高"}, "balanceOutdated": {"message": "残高が期限切れの可能性があります"}, "baseFee": {"message": "基本手数料"}, "basic": {"message": "基本"}, "basicConfigurationBannerTitle": {"message": "基本機能はオフになっています"}, "basicConfigurationDescription": {"message": "MetaMaskは、インターネットサービスを通じてトークンの詳細やガス設定などの基本的な機能を提供します。インターネットサービスを使用すると、この場合はMetaMaskに、ユーザーのIPアドレスが共有されます。これは他のどのWebサイトにアクセスした場合も同様で、MetaMaskはこのデータを一時的に使用し、ユーザーのデータを販売することは一切ありません。VPNを使用したり、これらのサービスをオフにしたりすることもできますが、MetaMaskでのエクスペリエンスに影響を与える可能性があります。詳細は$1をお読みください。", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "基本機能"}, "basicConfigurationModalCheckbox": {"message": "理解したうえで続行します"}, "basicConfigurationModalDisclaimerOff": {"message": "これは、MetaMaskでの時間が完全に最適化されないことを意味します。基本機能 (トークンの詳細、最適なガス設定など) は利用できません。"}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "これをオフにすると、$1、$2のすべての機能も無効になります。", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "セキュリティとプライバシー、バックアップと同期"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "通知"}, "basicConfigurationModalDisclaimerOn": {"message": "MetaMaskでの時間を最適化するには、この機能をオンにする必要があります。基本機能 (トークンの詳細、最適なガス設定など) は、Web3エクスペリエンスに重要です。"}, "basicConfigurationModalHeadingOff": {"message": "基本機能をオフにする"}, "basicConfigurationModalHeadingOn": {"message": "基本機能をオンにする"}, "bestPrice": {"message": "最適な価格"}, "beta": {"message": "ベータ版"}, "betaHeaderText": {"message": "これはベータ版です。バグは報告してください $1"}, "betaMetamaskVersion": {"message": "MetaMaskベータ版"}, "betaTerms": {"message": "ベータ版利用規約"}, "billionAbbreviation": {"message": "B", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "アカウント", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "アセット", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "スワップ", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "ブロックエクスプローラーのURL"}, "blockExplorerUrlDefinition": {"message": "このネットワークのブロックエクスプローラーとして使用されるURL。"}, "blockExplorerView": {"message": "$1でアカウントを表示", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "続行すると、Blurに出品しているすべての資産がリスクにさらされる可能性があります。"}, "blockaidAlertDescriptionMalicious": {"message": "悪意のあるサイトとやり取りしています。続行すると、資産を失うことになります。"}, "blockaidAlertDescriptionOpenSea": {"message": "続行すると、OpenSeaに出品しているすべての資産がリスクにさらされる可能性があります。"}, "blockaidAlertDescriptionOthers": {"message": "この要求を確定すると、資産が失われる可能性があります。この要求はキャンセルすることをお勧めします。"}, "blockaidAlertDescriptionTokenTransfer": {"message": "資産を詐欺師に送ろうとしています。続行すると、これらの資産が失われます。"}, "blockaidAlertDescriptionWithdraw": {"message": "この要求を確定すると、詐欺師に資産の引き出しと使用を許可することになります。失った資産を取り戻すことはできません。"}, "blockaidDescriptionApproveFarming": {"message": "このリクエストを承認すると、詐欺が判明しているサードパーティに資産をすべて奪われる可能性があります。"}, "blockaidDescriptionBlurFarming": {"message": "このリクエストを承認すると、Blurに登録されている資産を誰かに盗まれる可能性があります。"}, "blockaidDescriptionErrored": {"message": "エラーが発生したため、セキュリティアラートをチェックできませんでした。関連するすべてのアドレスが信頼できる場合のみ続行してください。"}, "blockaidDescriptionMaliciousDomain": {"message": "悪質なドメインとやり取りしています。このリクエストを承認すると、資産を失う可能性があります。"}, "blockaidDescriptionMightLoseAssets": {"message": "このリクエストを承認すると、資産を失う可能性があります。"}, "blockaidDescriptionSeaportFarming": {"message": "このリクエストを承認すると、OpenSeaに登録されている資産を誰かに盗まれる可能性があります。"}, "blockaidDescriptionTransferFarming": {"message": "このリクエストを承認すると、詐欺が判明しているサードパーティに資産をすべて奪われます。"}, "blockaidMessage": {"message": "プライバシーを保護 - サードパーティとデータが一切共有されません。Arbitrum、Avalanche、BNB Chain、イーサリアムメインネット、Linea、Optimism、Polygon、Base、Sepoliaで利用可能。"}, "blockaidTitleDeceptive": {"message": "これは虚偽のリクエストです"}, "blockaidTitleMayNotBeSafe": {"message": "ご注意ください"}, "blockaidTitleSuspicious": {"message": "これは不審なリクエストです"}, "blockies": {"message": "<PERSON><PERSON>"}, "borrowed": {"message": "借入済み"}, "boughtFor": {"message": "購入価格"}, "bridge": {"message": "ブリッジ"}, "bridgeAllowSwappingOf": {"message": "ブリッジ用に$3の$1 $2への正確なアクセスを許可する", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "$1のブリッジを承認", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "指定された金額、$1 $2へのアクセスを許可しようとしています。このコントラクトはそれ以上の資金にはアクセスしません。"}, "bridgeApprovalWarningForHardware": {"message": "ブリッジ用に$1 $2へのアクセスを許可し、$2へのブリッジを承認する必要があります。これには2回の別々の承認が必要です。"}, "bridgeBlockExplorerLinkCopied": {"message": "ブロックエクスプローラーのリンクをコピーしました！"}, "bridgeCalculatingAmount": {"message": "計算中..."}, "bridgeConfirmTwoTransactions": {"message": "ハードウェアウォレットで2つのトランザクションを確定する必要があります:"}, "bridgeCreateSolanaAccount": {"message": "Solanaアカウントを作成"}, "bridgeCreateSolanaAccountDescription": {"message": "Solanaネットワークにスワップするには、アカウントと受取アドレスが必要です。"}, "bridgeCreateSolanaAccountTitle": {"message": "はじめにSolanaアカウントが必要です。"}, "bridgeDetailsTitle": {"message": "ブリッジの詳細", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "金額を選択"}, "bridgeEnterAmountAndSelectAccount": {"message": "金額を入力して、送金先アカウントを選択します"}, "bridgeExplorerLinkViewOn": {"message": "$1で表示"}, "bridgeFetchNewQuotes": {"message": "新しいものを取得しますか？"}, "bridgeFrom": {"message": "ブリッジ元:"}, "bridgeFromTo": {"message": "$1 $2を$3にブリッジ", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "前の画面のクォートのネットワーク手数料には両方のトランザクションが含まれていて、分割されます。"}, "bridgeNetCost": {"message": "純コスト"}, "bridgeQuoteExpired": {"message": "クォートがタイムアウトしました。"}, "bridgeSelectDestinationAccount": {"message": "送金先アカウントを選択します"}, "bridgeSelectNetwork": {"message": "ネットワークを選択"}, "bridgeSelectTokenAmountAndAccount": {"message": "トークン、金額、送金先アカウントを選択します"}, "bridgeSelectTokenAndAmount": {"message": "トークンと金額を選択"}, "bridgeSolanaAccountCreated": {"message": "Solanaアカウントが作成されました"}, "bridgeStatusComplete": {"message": "完了", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "失敗", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "進行中", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$2で$1を受け取りました", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "$2で$1を受け取っています", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "$1を$2にスワップしました", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "$1を$2にスワップしています", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "利用規約"}, "bridgeTimingMinutes": {"message": "$1分", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "ブリッジ先:"}, "bridgeToChain": {"message": "$1にブリッジ"}, "bridgeTokenCannotVerifyDescription": {"message": "このトークンを手動で追加した場合は、ブリッジを行う前に、資金へのリスクを認識していることを確認してください。"}, "bridgeTokenCannotVerifyTitle": {"message": "このトークンは検証できません。"}, "bridgeTransactionProgress": {"message": "トランザクション $1/2"}, "bridgeTxDetailsBridging": {"message": "ブリッジ"}, "bridgeTxDetailsDelayedDescription": {"message": "お問い合わせ先:"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "MetaMaskサポート"}, "bridgeTxDetailsDelayedTitle": {"message": "3時間を経過していますか？"}, "bridgeTxDetailsNonce": {"message": "ナンス"}, "bridgeTxDetailsStatus": {"message": "ステータス"}, "bridgeTxDetailsTimestamp": {"message": "タイムスタンプ"}, "bridgeTxDetailsTimestampValue": {"message": "$1 $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "次のネットワークの$1 $2:", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "ガス代合計"}, "bridgeTxDetailsYouReceived": {"message": "受取"}, "bridgeTxDetailsYouSent": {"message": "送金"}, "bridgeValidationInsufficientGasMessage": {"message": "このブリッジのガス代を支払うのに十分な$1がありません。金額を下げるか、$1を買い足してください。"}, "bridgeValidationInsufficientGasTitle": {"message": "ガス代により多くの$1が必要です"}, "bridging": {"message": "ブリッジ"}, "browserNotSupported": {"message": "ご使用のブラウザはサポートされていません..."}, "buildContactList": {"message": "連絡先リストを作成する"}, "builtAroundTheWorld": {"message": "MetaMaskは、世界中でデザイン・開発されています。"}, "bulletpoint": {"message": "·"}, "busy": {"message": "ビジー状態"}, "buyAndSell": {"message": "購入・売却"}, "buyMoreAsset": {"message": "$1を追加購入", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "今すぐ購入"}, "bytes": {"message": "バイト"}, "canToggleInSettings": {"message": "この通知は「設定」>「アラート」で再度有効にできます。"}, "cancel": {"message": "キャンセル"}, "cancelPopoverTitle": {"message": "トランザクションをキャンセル"}, "cancelSpeedUpLabel": {"message": "このガス代は、元の額を$1ます。", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "トランザクションを$1するには、ネットワークに認識されるようにガス代を10%以上増額する必要があります。", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "キャンセル済み"}, "chainId": {"message": "チェーンID"}, "chainIdDefinition": {"message": "このネットワークのトランザクションの署名に使用されるチェーンID。"}, "chainIdExistsErrorMsg": {"message": "このチェーンIDは現在$1ネットワークで使用されています。"}, "chainListReturnedDifferentTickerSymbol": {"message": "このトークンシンボルは、入力されたネットワーク名またはチェーンIDと一致しません。人気のトークンの多くはシンボルが似ているため、詐欺師がそれを利用してより価値の高いトークンを送り返すように仕向ける可能性があります。続行する前にすべてを確認してください。"}, "chooseYourNetwork": {"message": "ネットワークを選択してください"}, "chooseYourNetworkDescription": {"message": "デフォルトの設定と構成を使用する場合、当社はInfuraをデフォルトの遠隔手続き呼び出し (RPC) プロバイダーとして使用し、イーサリアムデータへの可能な限り最も信頼性が高くプライベートなアクセスを提供します。まれに、ユーザーに最も優れたエクスペリエンスを提供するために、他のRPCプロバイダーが使用される場合もあります。ユーザーが自らRPCを選択することもできますが、どのRPCもトランザクションを実行するために、ユーザーのIPアドレスとイーサリアムウォレットを取得する点にご注意ください。InfuraによるEVMアカウントのデータの取り扱いに関する詳細は、$1をご覧ください。Solanaアカウントの場合は、$2。", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "こちらをクリックしてください"}, "chromeRequiredForHardwareWallets": {"message": "ハードウェアウォレットに接続するには、MetaMaskをGoogle Chromeで使用する必要があります。"}, "circulatingSupply": {"message": "循環供給量"}, "clear": {"message": "消去"}, "clearActivity": {"message": "アクティビティとナンスデータを消去"}, "clearActivityButton": {"message": "アクティビティタブのデータを消去"}, "clearActivityDescription": {"message": "これによりアカウントのナンスがリセットされ、ウォレットのアクティビティタブからデータが消去されます。現在のアカウントとネットワークだけが影響を受けます。残高と受信トランザクションへの変更はありません。"}, "click": {"message": "クリックして"}, "clickToConnectLedgerViaWebHID": {"message": "ここをクリックして、WebHIDでLedgerを接続します", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "閉じる"}, "closeExtension": {"message": "拡張機能を閉じる"}, "closeWindowAnytime": {"message": "このウィンドウはいつでも閉じることができます。"}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "コレクション名"}, "comboNoOptions": {"message": "オプションが見つかりません", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "供給されているトークンの大部分が上位トークン保有者によって保有されているため、中央集権型の価格操作の危険があります。"}, "concentratedSupplyDistributionTitle": {"message": "供給量の集中"}, "configureSnapPopupDescription": {"message": "MetaMaskから移動してこのsnapを構成します。"}, "configureSnapPopupInstallDescription": {"message": "MetaMaskから移動してこのsnapをインストールします。"}, "configureSnapPopupInstallTitle": {"message": "snapをインストール"}, "configureSnapPopupLink": {"message": "続けるにはこのリンクをクリックしてください:"}, "configureSnapPopupTitle": {"message": "snapを構成"}, "confirm": {"message": "確認"}, "confirmAccountTypeSmartContract": {"message": "スマートアカウント"}, "confirmAccountTypeStandard": {"message": "スタンダードアカウント"}, "confirmAlertModalAcknowledgeMultiple": {"message": "アラートを確認したうえで続行します"}, "confirmAlertModalAcknowledgeSingle": {"message": "アラートを確認したうえで続行します"}, "confirmFieldPaymaster": {"message": "手数料の支払元"}, "confirmFieldTooltipPaymaster": {"message": "このトランザクションの手数料は、ペイマスターのスマートコントラクトにより支払われます。"}, "confirmGasFeeTokenBalance": {"message": "残高:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "資金不足"}, "confirmGasFeeTokenMetaMaskFee": {"message": "$1の手数料を含む"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "このトランザクションを完了させるため、MetaMaskが残高を補完しています。"}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "ウォレットの残高を使ってネットワーク手数料を支払います。"}, "confirmGasFeeTokenModalPayETH": {"message": "ETHで支払う"}, "confirmGasFeeTokenModalPayToken": {"message": "他のトークンで支払う"}, "confirmGasFeeTokenModalTitle": {"message": "トークンを選択"}, "confirmGasFeeTokenToast": {"message": "このネットワーク手数料は$1で支払います"}, "confirmGasFeeTokenTooltip": {"message": "これはトランザクションを処理するために、ネットワークに支払われます。ETH以外のトークンまたは事前に入金済みのETHに対する$1のMetaMask手数料も含まれています。"}, "confirmInfoAccountNow": {"message": "現在"}, "confirmInfoSwitchingTo": {"message": "切り替え先"}, "confirmNestedTransactionTitle": {"message": "トランザクション$1"}, "confirmPassword": {"message": "パスワードの確認"}, "confirmRecoveryPhrase": {"message": "シークレットリカバリーフレーズの確認"}, "confirmSimulationApprove": {"message": "承認"}, "confirmTitleAccountTypeSwitch": {"message": "アカウントのアップデート"}, "confirmTitleApproveTransactionNFT": {"message": "出金のリクエスト"}, "confirmTitleDeployContract": {"message": "コントラクトを展開"}, "confirmTitleDescApproveTransaction": {"message": "このサイトがNFTの引き出し許可を求めています。"}, "confirmTitleDescDelegationRevoke": {"message": "スタンダードアカウント (EOA) に戻そうとしています。"}, "confirmTitleDescDelegationUpgrade": {"message": "スマートアカウントに切り替えようとしています"}, "confirmTitleDescDeployContract": {"message": "このサイトがコントラクトの展開を求めています"}, "confirmTitleDescERC20ApproveTransaction": {"message": "このサイトがトークンの引き出し許可を求めています。"}, "confirmTitleDescPermitSignature": {"message": "このサイトがトークンの使用許可を求めています。"}, "confirmTitleDescSIWESignature": {"message": "サイトがこのアカウントを所有することを証明するためにサインインを求めています。"}, "confirmTitleDescSign": {"message": "確定する前に、要求の詳細を確認してください。"}, "confirmTitlePermitTokens": {"message": "使用上限リクエスト"}, "confirmTitleRevokeApproveTransaction": {"message": "アクセス許可を取り消す"}, "confirmTitleSIWESignature": {"message": "サインインリクエスト"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "アクセス許可を取り消す"}, "confirmTitleSignature": {"message": "署名要求"}, "confirmTitleTransaction": {"message": "トランザクションの要求"}, "confirmationAlertDetails": {"message": "資産を守るため、リクエストを拒否することをお勧めします。"}, "confirmationAlertModalTitleDescription": {"message": "資産が危険にさらされている可能性があります"}, "confirmed": {"message": "確認されました"}, "confusableUnicode": {"message": "「$1」は「$2」と類似しています。"}, "confusableZeroWidthUnicode": {"message": "ゼロ幅文字が見つかりました。"}, "confusingEnsDomain": {"message": "ENS名に混乱しやすい文字が発見されました。詐欺を防ぐためにENS名を確認して下さい。"}, "connect": {"message": "接続"}, "connectAccount": {"message": "アカウントの接続"}, "connectAccountOrCreate": {"message": "アカウントを接続するか、または新規に作成します"}, "connectAccounts": {"message": "アカウントを接続"}, "connectAnAccountHeader": {"message": "アカウントの接続"}, "connectManually": {"message": "現在のサイトに手動で接続"}, "connectMoreAccounts": {"message": "他のアカウントを接続"}, "connectSnap": {"message": "$1を接続", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "MetaMaskを使用して接続"}, "connectedAccounts": {"message": "接続されたアカウント"}, "connectedAccountsDescriptionPlural": {"message": "このサイトに接続されているアカウントを$1個持っています。", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "このサイトに接続されているアカウントを1個持っています。"}, "connectedAccountsEmptyDescription": {"message": "MetaMaskはこのサイトに接続されていません。web3サイトに接続するには、そのサイトの接続ボタンをクリックしてください。"}, "connectedAccountsListTooltip": {"message": "$1はアカウントの残高、アドレス、アクティビティを確認し、接続されたアカウントで承認するトランザクションを提案できます。", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "接続されたアカウントが更新されました"}, "connectedSites": {"message": "接続済みのサイト"}, "connectedSitesAndSnaps": {"message": "接続されているサイトとSnap"}, "connectedSitesDescription": {"message": "$1はこれらのサイトに接続されています。これらのサイトは、アカウントアドレスを把握できます。", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1はどのサイトとも接続されていません。", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMaskはこのサイトに接続されていますが、まだアカウントは接続されていません"}, "connectedSnaps": {"message": "接続されているSnap"}, "connectedWithAccount": {"message": "$1個のアカウントが接続されました", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "$1と接続されました", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1個のネットワークを接続済み", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "$1と接続済み", "description": "$1 represents network name"}, "connecting": {"message": "接続中..."}, "connectingTo": {"message": "$1に接続中"}, "connectingToDeprecatedNetwork": {"message": "「$1」は段階的に廃止されており、機能しない可能性があります。別のネットワークをお試しください。"}, "connectingToGoerli": {"message": "Goerliテストネットワークに接続中"}, "connectingToLineaGoerli": {"message": "Linea Goerliテストネットワークに接続中"}, "connectingToLineaMainnet": {"message": "Lineaメインネットに接続中"}, "connectingToLineaSepolia": {"message": "Linea Sepoliaテストネットワークに接続中"}, "connectingToMainnet": {"message": "イーサリアムメインネットに接続中"}, "connectingToSepolia": {"message": "Sepoliaテストネットワークに接続中"}, "connectionDescription": {"message": "このWebサイトをMetaMaskに接続します"}, "connectionFailed": {"message": "接続できませんでした"}, "connectionFailedDescription": {"message": "$1を取得できませんでした。ネットワークを確認してもう一度お試しください。", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "サイトに接続するには、「接続」ボタンを選択します。MetaMaskはWeb3のサイトにしか接続できません。"}, "connectionRequest": {"message": "接続リクエスト"}, "contactUs": {"message": "お問い合わせ"}, "contacts": {"message": "連絡先"}, "contentFromSnap": {"message": "$1のコンテンツ", "description": "$1 represents the name of the snap"}, "continue": {"message": "続行"}, "contract": {"message": "コントラクト"}, "contractAddress": {"message": "コントラクトアドレス"}, "contractAddressError": {"message": "トークンのコントラクトアドレスにトークンを送信します。これにより、これらのトークンが失われる可能性があります。"}, "contractDeployment": {"message": "コントラクトの展開"}, "contractInteraction": {"message": "コントラクトインタラクション"}, "convertTokenToNFTDescription": {"message": "このアセットはNFTであることが検出されました。MetaMaskでは現在、NFTが完全にネイティブでサポートされています。トークンリストから削除して、NFTとして追加しますか？"}, "convertTokenToNFTExistDescription": {"message": "このアセットはNFTとして追加されていることが検出されました。トークンリストから削除しますか？"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "コピーしました。"}, "copyAddress": {"message": "アドレスをクリップボードにコピー"}, "copyAddressShort": {"message": "アドレスをコピー"}, "copyPrivateKey": {"message": "秘密鍵をコピー"}, "copyToClipboard": {"message": "クリップボードにコピー"}, "copyTransactionId": {"message": "トランザクションIDをコピー"}, "create": {"message": "作成"}, "createNewAccountHeader": {"message": "新しいアカウントの作成"}, "createPassword": {"message": "パスワードを作成"}, "createSnapAccountDescription": {"message": "$1がMetaMaskへの新しいアカウントの追加を要求しています。"}, "createSnapAccountTitle": {"message": "アカウントの作成"}, "createSolanaAccount": {"message": "Solanaアカウントを作成"}, "creatorAddress": {"message": "クリエイターのアドレス"}, "crossChainSwapsLink": {"message": "MetaMask Portfolioでネットワーク間でスワップ"}, "crossChainSwapsLinkNative": {"message": " ブリッジによるネットワーク間のスワップ"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "通貨"}, "currencyRateCheckToggle": {"message": "残高とトークン価格チェッカーを表示"}, "currencyRateCheckToggleDescription": {"message": "MetaMaskは、$1と$2のAPIを使用して残高とトークンの価格を表示します。$3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "通貨記号"}, "currencySymbolDefinition": {"message": "このネットワークの通貨に対して表示されるティッカーシンボル。"}, "currentAccountNotConnected": {"message": "現在のアカウントは接続されていません"}, "currentExtension": {"message": "現在の拡張機能ページ"}, "currentLanguage": {"message": "現在の言語"}, "currentNetwork": {"message": "現在のネットワーク", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "このネットワークの現在のRPC URLは非推奨となりました。"}, "currentTitle": {"message": "現在:"}, "currentlyUnavailable": {"message": "このネットワークでは利用できません"}, "curveHighGasEstimate": {"message": "積極的なガス代見積もりグラフ"}, "curveLowGasEstimate": {"message": "低いガス代見積もりグラフ"}, "curveMediumGasEstimate": {"message": "市場のガス代見積もりグラフ"}, "custom": {"message": "高度な設定"}, "customGasSettingToolTipMessage": {"message": "ガス価格をカスタマイズするには$1を使用します。慣れていない場合はわかりにくい可能性があります。自己責任で操作してください。", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "カスタム"}, "customSpendLimit": {"message": "カスタム使用限度額"}, "customToken": {"message": "カスタムトークン"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "このネットワークではまだトークンの検出を利用できません。トークンを手動でインポートし、信頼できることを確認してください。$1の詳細をご覧ください"}, "customTokenWarningInTokenDetectionNetwork": {"message": "手動でトークンをインポートする前に、信頼できることを確認してください。$1の詳細をご覧ください。"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "インポートする前にトークンが信頼できることを確認してください。$1を避ける方法の詳細をご覧ください。また、$2トークンの検出を有効にすることもできます。"}, "customerSupport": {"message": "カスタマーサポート"}, "customizeYourNotifications": {"message": "通知のカスタマイズ"}, "customizeYourNotificationsText": {"message": "受け取る通知の種類をオンにします"}, "dappSuggested": {"message": "サイト提案"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1はこの価格を提案しています。", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "提案されたサイト"}, "dappSuggestedHighShortLabel": {"message": "サイト (高)"}, "dappSuggestedShortLabel": {"message": "サイト"}, "dappSuggestedTooltip": {"message": "$1はこの価格を推奨しています。", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "ダーク"}, "data": {"message": "データ"}, "dataCollectionForMarketing": {"message": "マーケティング目的のデータ収集"}, "dataCollectionForMarketingDescription": {"message": "当社はMetaMetricsを使用して、ユーザーによる当社のマーケティングコミュニケーションとのインタラクションを把握します。また、関連ニュースをお伝えする場合もあります (製品の機能、その他資料など)。"}, "dataCollectionWarningPopoverButton": {"message": "OK"}, "dataCollectionWarningPopoverDescription": {"message": "マーケティング目的のデータ収集をオフにしました。これはこのデバイスにのみ適用されます。MetaMaskを他のデバイスで使用する場合は、そのデバイスでもオプトアウトしてください。"}, "dataUnavailable": {"message": "データが利用できません"}, "dateCreated": {"message": "作成日"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "デビットカードまたはクレジットカードの購入オプション"}, "decimal": {"message": "トークンの小数桁数"}, "decimalsMustZerotoTen": {"message": "小数桁数は0以上、36以下の範囲で使用する必要があります。"}, "decrypt": {"message": "解読"}, "decryptCopy": {"message": "暗号化されたメッセージをコピー"}, "decryptInlineError": {"message": "このメッセージは次のエラーにより解読できません。$1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1は、このメッセージを読んでアクションを完了させることを望んでいます", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "メッセージを解読"}, "decryptRequest": {"message": "リクエストを解読"}, "defaultRpcUrl": {"message": "デフォルトのRPC URL"}, "defaultSettingsSubTitle": {"message": "MetaMaskは、デフォルト設定を使用して安全と使いやすさのバランスを最適化しています。プライバシーをさらに強化したい場合は、これらの設定を変更してください。"}, "defaultSettingsTitle": {"message": "デフォルトのプライバシー設定"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "後程またアクセスしてみてください。"}, "defiTabErrorTitle": {"message": "このページを読み込めませんでした。"}, "delete": {"message": "削除"}, "deleteContact": {"message": "連絡先を削除"}, "deleteMetaMetricsData": {"message": "MetaMetricsデータを削除"}, "deleteMetaMetricsDataDescription": {"message": "これにより、このデバイスでの使用に関連した過去のMetaMetricsデータが削除されます。このデータが削除された後も、ウォレットとアカウントに変化はありません。このプロセスには最長30日間かかる場合があります。$1をご覧ください。", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "アナリティクスシステムサーバーの問題により、現在このリクエストを完了させることができません。後ほど再度お試しください"}, "deleteMetaMetricsDataErrorTitle": {"message": "現在このデータを削除できません"}, "deleteMetaMetricsDataModalDesc": {"message": "すべてのMetaMetricsデータを削除しようとしています。よろしいですか？"}, "deleteMetaMetricsDataModalTitle": {"message": "MetaMetricsデータを削除しますか？"}, "deleteMetaMetricsDataRequestedDescription": {"message": "このアクションは$1に開始しました。このプロセスには最長30日間かかる場合があります。$2をご覧ください", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "このネットワークを削除した場合、このネットワーク内の資産を見るには、再度ネットワークの追加が必要になります。"}, "deleteNetworkTitle": {"message": "$1ネットワークを削除しますか？", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "ウォレットアドレスまたはQRコードを使った別のアカウントからの仮想通貨のデポジット"}, "deprecatedGoerliNtwrkMsg": {"message": "イーサリアムシステムのアップデートに伴い、Goerliテストネットワークはまもなく段階的に廃止される予定です。"}, "deprecatedNetwork": {"message": "このネットワークはサポートされなくなりました"}, "deprecatedNetworkButtonMsg": {"message": "了解"}, "deprecatedNetworkDescription": {"message": "接続しているネットワークは現在MetaMaskによりサポートされていません。$1"}, "description": {"message": "説明"}, "descriptionFromSnap": {"message": "$1からの説明", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "対象のアカウントが見つかりませんでした"}, "destinationAccountPickerNoMatching": {"message": "一致するアカウントが見つかりませんでした"}, "destinationAccountPickerReceiveAt": {"message": "受取先:"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "受取アドレスまたはENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "受取アドレス"}, "destinationTransactionIdLabel": {"message": "送金先Tx ID", "description": "Label for the destination transaction ID field."}, "details": {"message": "詳細"}, "developerOptions": {"message": "開発者用オプション"}, "disabledGasOptionToolTipMessage": {"message": "元のガス代の10%以上という増額の条件を満たしていないため、「$1」は無効になっています。", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "接続解除"}, "disconnectAllAccounts": {"message": "すべてのアカウントを接続解除"}, "disconnectAllAccountsConfirmationDescription": {"message": "本当に接続解除しますか？サイトの機能を失う可能性があります。"}, "disconnectAllAccountsText": {"message": "アカウント"}, "disconnectAllDescriptionText": {"message": "このサイトとの接続を解除すると、このサイトをもう一度使用するには、アカウントとネットワークを接続しなおす必要があります。"}, "disconnectAllSnapsText": {"message": "Snap"}, "disconnectMessage": {"message": "これにより、このサイトへの接続が解除されます"}, "disconnectPrompt": {"message": "$1を接続解除"}, "disconnectThisAccount": {"message": "このアカウントを接続解除"}, "disconnectedAllAccountsToast": {"message": "すべてのアカウントの$1への接続が解除されました", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1の$2への接続が解除されました", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "ディスカバリ"}, "discoverSnaps": {"message": "Snapのご紹介", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "閉じる"}, "dismissReminderDescriptionField": {"message": "これをオンにすると、シークレットリカバリーフレーズのバックアップのリマインダーメッセージが解除されます。資金の損失を防ぐために、シークレットリカバリーフレーズのバックアップを取ることを強くお勧めします。"}, "dismissReminderField": {"message": "シークレットリカバリーフレーズのバックアップリマインダーを解除"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "すべてのアカウントで「スマートアカウントへの切り替え」の提案が表示されないようにするには、これをオンにします。スマートアカウントでは、より高速なトランザクション、低いネットワーク手数料、柔軟な支払方法が利用できるようになります。"}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "「スマートアカウントへの切り替え」の提案を閉じる"}, "displayNftMedia": {"message": "NFTメディアの表示"}, "displayNftMediaDescription": {"message": "NFTのメディアとデータを表示した場合、IPアドレスがOpenSeaをはじめとするサードパーティに公開されます。その結果、攻撃者がユーザーのIPアドレスとイーサリアムアドレスを関連付けられるようになる可能性があります。NFTの自動検出はこの設定に依存しており、この設定を無効にすると利用できなくなります。"}, "doNotShare": {"message": "これは誰にも教えないでください"}, "domain": {"message": "ドメイン"}, "done": {"message": "完了"}, "dontShowThisAgain": {"message": "今後表示しない"}, "downArrow": {"message": "下矢印"}, "downloadGoogleChrome": {"message": "Google Chromeをダウンロード"}, "downloadNow": {"message": "今すぐダウンロード"}, "downloadStateLogs": {"message": "ステートログをダウンロード"}, "dragAndDropBanner": {"message": "ネットワークをドラッグして並び替えることができます。"}, "dropped": {"message": "削除されました"}, "duplicateContactTooltip": {"message": "この連絡先名は既存のアカウントまたは連絡先と矛盾しています"}, "duplicateContactWarning": {"message": "重複する連絡先があります"}, "durationSuffixDay": {"message": "日", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "時間", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "ミリ秒", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "分", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "か月", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "秒", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "週間", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "年", "description": "Shortened form of 'year'"}, "earn": {"message": "収益化"}, "edit": {"message": "編集"}, "editANickname": {"message": "ニックネームを編集"}, "editAccounts": {"message": "アカウントを編集"}, "editAddressNickname": {"message": "アドレスのニックネームを編集"}, "editCancellationGasFeeModalTitle": {"message": "キャンセルのガス代を編集"}, "editContact": {"message": "連絡先を編集"}, "editGasFeeModalTitle": {"message": "ガス代を編集"}, "editGasLimitOutOfBounds": {"message": "ガスリミットは$1以上にする必要があります"}, "editGasLimitOutOfBoundsV2": {"message": "ガスリミットは$1より大きく、$2未満でなければなりません", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "ガスリミットは、使用しても構わないガスの最大単位数です。ガスの単位数は、「最大優先手数料」および「最大手数料」の乗数になります。"}, "editGasMaxBaseFeeGWEIImbalance": {"message": "最大基本料金を優先手数料よりも低くすることはできません"}, "editGasMaxBaseFeeHigh": {"message": "最大基本料金が必要以上です"}, "editGasMaxBaseFeeLow": {"message": "現在のネットワーク状況に対して最大基本料金が低いです"}, "editGasMaxFeeHigh": {"message": "最大手数料が必要以上です"}, "editGasMaxFeeLow": {"message": "ネットワークの状況に対して最大手数料が低すぎます"}, "editGasMaxFeePriorityImbalance": {"message": "最大手数料を優先手数料よりも低くすることはできません"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "最大優先手数料は0gweiより高くなければなりません"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "優先手数料は0より高くなければなりません。"}, "editGasMaxPriorityFeeHigh": {"message": "最大優先手数料が必要以上です。必要以上の額が支払われる可能性があります。"}, "editGasMaxPriorityFeeHighV2": {"message": "優先手数料が必要以上です。必要以上の額が支払われる可能性があります。"}, "editGasMaxPriorityFeeLow": {"message": "現在のネットワーク状況に対して最大優先手数料が低いです"}, "editGasMaxPriorityFeeLowV2": {"message": "現在のネットワーク状況に対して優先手数料が低いです"}, "editGasPriceTooLow": {"message": "ガス価格は0より高くなければなりません"}, "editGasPriceTooltip": {"message": "このネットワークは、トランザクションの送信時に「ガス価格」フィールドが必要です。ガス価格は、ガス1単位あたりに支払う金額です。"}, "editGasSubTextFeeLabel": {"message": "最大手数料:"}, "editGasTitle": {"message": "優先度を編集"}, "editGasTooLow": {"message": "不明な処理時間"}, "editInPortfolio": {"message": "Portfolioで編集"}, "editNetworkLink": {"message": "元のネットワークを編集"}, "editNetworksTitle": {"message": "ネットワークを編集"}, "editNonceField": {"message": "ナンスを編集"}, "editNonceMessage": {"message": "これは高度な機能であり、慎重に使用してください。"}, "editPermission": {"message": "アクセス許可の編集"}, "editPermissions": {"message": "アクセス許可の編集"}, "editSpeedUpEditGasFeeModalTitle": {"message": "高速化用のガス代を編集"}, "editSpendingCap": {"message": "使用上限の編集"}, "editSpendingCapAccountBalance": {"message": "アカウント残高: $1 $2"}, "editSpendingCapDesc": {"message": "代理で使用されても構わない金額を入力してください。"}, "editSpendingCapError": {"message": "使用限度は小数点以下$1桁を超えることができません。続けるには、小数点以下の桁を削除してください。"}, "editSpendingCapSpecialCharError": {"message": "数字のみで入力してください"}, "enableAutoDetect": {"message": " 自動検出を有効にする"}, "enableFromSettings": {"message": " 設定で有効にします。"}, "enableSnap": {"message": "有効にする"}, "enableToken": {"message": "$1を有効にする", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "有効"}, "enabledNetworks": {"message": "ネットワークが有効になりました"}, "encryptionPublicKeyNotice": {"message": "$1は公開暗号鍵を必要とします。同意することによって、このサイトは暗号化されたメッセージを作成できます。", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "公開暗号鍵をリクエスト"}, "endpointReturnedDifferentChainId": {"message": "入力したRPC URLが別のチェーンID ($1) を返しました。", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "改善されたトークン検出は現在$1で利用可能です。$2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "MetaMaskは、ENSドメインをブラウザのアドレスバーに直接表示します。使い方は次の通りです:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "この機能を使用すると、IPアドレスがIPFSのサードパーティサービスに公開されます。"}, "ensDomainsSettingDescriptionPart1": {"message": "MetaMaskはイーサリアムのENSコントラクトを確認し、ENS名に接続されたコードを取得します。"}, "ensDomainsSettingDescriptionPart2": {"message": "コードがIPFSにリンクしている場合、関連付けられたコンテンツ (通常Webサイト) を見ることができます。"}, "ensDomainsSettingTitle": {"message": "アドレスバーにENSドメインを表示する"}, "ensUnknownError": {"message": "ENSの検索に失敗しました。"}, "enterANameToIdentifyTheUrl": {"message": "URLを識別するための名前を入力してください"}, "enterChainId": {"message": "チェーンIDを入力してください"}, "enterMaxSpendLimit": {"message": "使用限度額の最大値を入力してください"}, "enterNetworkName": {"message": "ネットワーク名を入力してください"}, "enterOptionalPassword": {"message": "オプションのパスワードを入力してください"}, "enterPasswordContinue": {"message": "続行するには、パスワードを入力してください"}, "enterRpcUrl": {"message": "RPC URLを入力してください"}, "enterSymbol": {"message": "シンボルを入力してください"}, "enterTokenNameOrAddress": {"message": "トークン名を入力するか、アドレスを貼り付けてください"}, "enterYourPassword": {"message": "パスワードを入力してください"}, "errorCode": {"message": "コード: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "安全なチェーンリストの取得中にエラーが発生しました。慎重に続けてください。"}, "errorMessage": {"message": "メッセージ: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "コード: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "サポートへのお問い合わせ", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "発生した問題についてご説明ください", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "情報は表示できませんが、ご心配なく。ウォレットと資金は安全です。", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "エラーメッセージ", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "発生した問題についてご説明ください", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "バグを再現する方法などの詳細をお知らせいただくと、問題の解決に役立ちます。", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "ありがとうございます。すぐに確認いたします。", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMaskにエラーが発生しました", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "再試行", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "スタック:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "カスタムネットワークへの接続中にエラーが発生しました。"}, "errorWithSnap": {"message": "$1でエラーが発生しました", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "予想手数料"}, "estimatedFeeTooltip": {"message": "ネットワーク上のトランザクションの処理に支払われる金額"}, "ethGasPriceFetchWarning": {"message": "現在メインのガスの見積もりサービスが利用できないため、バックアップのガス価格が提供されています。"}, "ethereumProviderAccess": {"message": "イーサリアムプロバイダーに$1へのアクセス権を付与する", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "イーサリアムのパブリックアドレス"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Etherscanでアカウントを表示"}, "etherscanViewOn": {"message": "Etherscanで表示"}, "existingChainId": {"message": "入力された情報は、既存のチェーンIDと関連付けられています。"}, "expandView": {"message": "ビューを展開"}, "experimental": {"message": "試験運用"}, "exploreweb3": {"message": "Web3を閲覧"}, "exportYourData": {"message": "データのエクスポート"}, "exportYourDataButton": {"message": "ダウンロード"}, "exportYourDataDescription": {"message": "連絡先やユーザー設定などのデータをエクスポートできます。"}, "extendWalletWithSnaps": {"message": "Web3のエクスペリエンスをカスタマイズする、コミュニティが開発したSnapをご覧ください", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "外部アカウント"}, "externalExtension": {"message": "外部拡張機能"}, "externalNameSourcesSetting": {"message": "ニックネームの提案"}, "externalNameSourcesSettingDescription": {"message": "当社は、Etherscan、Infura、Lensプロトコルなどのサードパーティソースから、やり取りするアドレスに使用するニックネームの提案を取得します。これらのソースは対象となるアドレスとユーザーのIPアドレスを把握できます。ユーザーのアカウントアドレスはサードパーティに公開されません。"}, "failed": {"message": "失敗しました"}, "failedToFetchChainId": {"message": "チェーンIDを取り込むことができませんでした。お使いのRPC URLは正しいですか？"}, "failover": {"message": "フェイルオーバー"}, "failoverRpcUrl": {"message": "フェイルオーバーRPC URL "}, "failureMessage": {"message": "問題が発生しました。アクションを完了させることができません"}, "fast": {"message": "高速"}, "feeDetails": {"message": "手数料の詳細"}, "fileImportFail": {"message": "ファイルのインポートが機能していない場合、ここをクリックしてください！", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "この拡張機能はアンインストールしてください", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flaskは、開発者が新しい不安定なAPIをテストするためのものです。開発者やベータテスター以外の方は、$1。", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "この拡張機能の安全性や安定性は保証されていません。Flaskで提供される新しいAPIはフィッシング攻撃への対策ができていないため、Flaskを必要とするサイトまたはsnapは、資産の窃取を目的とした悪質なものである可能性があります。", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Flask APIはすべて試験運用です。これらは通知なしに変更または削除される可能性があり、安定したMetaMaskに移行することなく永久にFlaskに残る可能性もあります。自己責任でご使用ください。", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Flaskの使用中は、通常のMetaMask拡張機能を無効にしてください。", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "リスクを受け入れる", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "トークンの金額は整数で入力する必要があります"}, "followUsOnTwitter": {"message": "Twitterでフォロー"}, "forbiddenIpfsGateway": {"message": "IPFSゲートウェイの使用は禁止されています。CIDゲートウェイを指定してください"}, "forgetDevice": {"message": "このデバイスの登録を解除"}, "forgotPassword": {"message": "パスワードを忘れた場合"}, "form": {"message": "フォーム"}, "from": {"message": "移動元"}, "fromAddress": {"message": "移動元: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "トークンリストから: $1"}, "function": {"message": "機能: $1"}, "fundingMethod": {"message": "入金方法"}, "gas": {"message": "ガス"}, "gasDisplayAcknowledgeDappButtonText": {"message": "ガス代の提案を編集"}, "gasDisplayDappWarning": {"message": "このガス代は$1により提案されています。これを上書きすると、トランザクションに問題が発生する可能性があります。ご質問がございましたら、$1までお問い合わせください。", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "ガス代"}, "gasLimit": {"message": "ガスリミット"}, "gasLimitRecommended": {"message": "推奨されるガスリミットは$1です。ガスリミットがこれ未満の場合、失敗する可能性があります。"}, "gasLimitTooLow": {"message": "ガスリミットは21000以上にする必要があります"}, "gasLimitV2": {"message": "ガスリミット"}, "gasOption": {"message": "ガスのオプション"}, "gasPriceExcessive": {"message": "ガス代が不要に高く設定されています。金額を下げることを検討してください。"}, "gasPriceFetchFailed": {"message": "ネットワークエラーのため、ガス価格の見積もりに失敗しました。"}, "gasTimingHoursShort": {"message": "$1時間", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "低速"}, "gasTimingMinutesShort": {"message": "$1分", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1秒", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "ガス使用量"}, "general": {"message": "一般"}, "generalCameraError": {"message": "カメラにアクセスできませんでした。もう一度お試しください"}, "generalCameraErrorTitle": {"message": "問題が発生しました...."}, "generalDescription": {"message": "デバイス間で設定を同期して、ネットワーク設定を選択し、トークンデータを追跡します"}, "genericExplorerView": {"message": "$1でアカウントを表示"}, "goToSite": {"message": "サイトに移動"}, "goerli": {"message": "Goerliテストネットワーク"}, "gotIt": {"message": "了解"}, "grantExactAccess": {"message": "正確なアクセスを許可する"}, "gwei": {"message": "gwei"}, "hardware": {"message": "ハードウェア"}, "hardwareWalletConnected": {"message": "ハードウェアウォレットが接続されました"}, "hardwareWalletLegacyDescription": {"message": "(レガシー)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "$1が接続されていて、イーサリアムアプリを選択していることを確認してください。"}, "hardwareWalletSubmissionWarningStep2": {"message": "$1デバイスで「スマートコントラクトデータ」または「ブラインド署名」を有効にします。"}, "hardwareWalletSubmissionWarningTitle": {"message": "送信をクリックする前:に"}, "hardwareWalletSupportLinkConversion": {"message": "ここをクリック"}, "hardwareWallets": {"message": "ハードウェアウォレットを接続"}, "hardwareWalletsInfo": {"message": "ハードウェアウォレットの統合には、外部サーバーへのAPI呼び出しを使用します。外部サーバーはこれにより、あなたがやり取りしたIPアドレスとスマートコントラクトアドレスを把握できます。"}, "hardwareWalletsMsg": {"message": "MetaMaskに接続するハードウェアウォレットを選択してください。"}, "here": {"message": "こちら", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "16進データ"}, "hiddenAccounts": {"message": "非表示のアカウント"}, "hide": {"message": "非表示"}, "hideAccount": {"message": "アカウントを非表示"}, "hideAdvancedDetails": {"message": "高度な詳細を非表示"}, "hideSentitiveInfo": {"message": "機密情報を非表示"}, "hideTokenPrompt": {"message": "トークンを非表示にしますか？"}, "hideTokenSymbol": {"message": "$1を非表示", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "残高のないトークンを非表示"}, "high": {"message": "積極的"}, "highGasSettingToolTipMessage": {"message": "変動の激しい市場でも確率が高くなります。人気のNFTドロップなどによるネットワークトラフィックの急増に備えるには、$1を使用してください。", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "高"}, "highestCurrentBid": {"message": "現在の最高入札額"}, "highestFloorPrice": {"message": "フロア価格の最高額"}, "history": {"message": "履歴"}, "holdToRevealContent1": {"message": "シークレットリカバリーフレーズは$1を提供します。", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "ウォレットと資金への完全アクセス", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "これは誰にも教えないでください。$1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "MetaMaskサポートがこの情報を尋ねることはなく、", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "もし尋ねられた場合はフィッシング詐欺の可能性があります。", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "秘密鍵は$1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "ウォレットと資金への完全アクセスを提供します。", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "長押ししてロックされた円を表示します"}, "holdToRevealPrivateKey": {"message": "長押しして秘密鍵を表示します"}, "holdToRevealPrivateKeyTitle": {"message": "秘密鍵は安全に保管してください"}, "holdToRevealSRP": {"message": "長押ししてSRPを表示します"}, "holdToRevealSRPTitle": {"message": "SRPは安全に保管してください"}, "holdToRevealUnlockedLabel": {"message": "長押ししてロックが解除された円を表示します"}, "honeypotDescription": {"message": "このトークンはハニーポットの危険性があります。金銭的な損失を防ぐために、やり取りする前にデューディリジェンスを行うことをお勧めします。"}, "honeypotTitle": {"message": "ハニーポット"}, "howNetworkFeesWorkExplanation": {"message": "トランザクションの処理に必要な手数料の見積もりです。最大手数料は$1です。"}, "howQuotesWork": {"message": "クォートの仕組み"}, "howQuotesWorkExplanation": {"message": "このクォートには、当社が検索した中で最も有利なクォートが含まれています。これは、ブリッジ手数料と$1%のMetaMaskの手数料からガス代を引いた、スワップレートに基づいています。ガス代は、ネットワークの混雑状況とトランザクションの複雑さによって変わります。"}, "id": {"message": "ID"}, "ignoreAll": {"message": "すべて無視"}, "ignoreTokenWarning": {"message": "トークンを非表示にするとウォレットに表示されなくなりますが、検索して追加することはできます。"}, "imToken": {"message": "imToken"}, "import": {"message": "インポート", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "アカウントのインポート中にエラーが発生しました。"}, "importAccountErrorIsSRP": {"message": "シークレットリカバリーフレーズ (またはニーモニックフレーズ) が入力されました。ここにアカウントをインポートするには、秘密鍵を入力する必要があります。秘密鍵は64文字の16進数の文字列です。"}, "importAccountErrorNotAValidPrivateKey": {"message": "これは有効な秘密鍵ではありません。16進数の文字列を入力しましたが、64文字でなければなりません。"}, "importAccountErrorNotHexadecimal": {"message": "これは有効な秘密鍵ではありません。16進数の64文字の文字列を入力する必要があります。"}, "importAccountJsonLoading1": {"message": "このJSONのインポートには数分かかり、MetaMaskがフリーズします。"}, "importAccountJsonLoading2": {"message": "申し訳ございません。今後高速化できるよう取り組みます。"}, "importAccountMsg": {"message": "インポートされたアカウントは、MetaMaskアカウントのシークレットリカバリーフレーズと関連付けられません。インポートされたアカウントの詳細を表示"}, "importNFT": {"message": "NFTをインポート"}, "importNFTAddressToolTip": {"message": "OpenSeaの場合、NFTページの詳細の下に、「コントラクトアドレス」という青いハイパーリンクがあります。これをクリックすると、Etherscanのコントラクトのアドレスに移動します。そのページの左上に「コントラクト」というアイコンがあり、その右側には文字と数字で構成された長い文字列があります。これがNFTを作成したコントラクトのアドレスです。アドレスの右側にある「コピー」アイコンをクリックすると、クリップボードにコピーされます。"}, "importNFTPage": {"message": "NFTページをインポート"}, "importNFTTokenIdToolTip": {"message": "NFTのIDは一意の識別子で、同じNFTは2つとして存在しません。前述の通り、OpenSeaではこの番号は「詳細」に表示されます。このIDを書き留めるか、クリップボードにコピーしてください。"}, "importNWordSRP": {"message": "$1単語のリカバリーフレーズがあります", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "秘密鍵"}, "importSRPDescription": {"message": "12単語または24単語のシークレットリカバリーフレーズを使用して、既存のウォレットをインポートします。"}, "importSRPNumberOfWordsError": {"message": "シークレットリカバリーフレーズは、12単語または24単語で構成されています"}, "importSRPWordError": {"message": "単語「$1」が正しくないか、スペルが違います。", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "単語「$1」と「$2」が正しくないか、スペルが違います。", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "シークレットリカバリーフレーズをインポート"}, "importSecretRecoveryPhraseUnknownError": {"message": "不明なエラーが発生しました。"}, "importSelectedTokens": {"message": "選択したトークンをインポートしますか？"}, "importSelectedTokensDescription": {"message": "選択したトークンだけがウォレットに表示されます。非表示のトークンは後でいつでも検索してインポートできます。"}, "importTokenQuestion": {"message": "トークンをインポートしますか？"}, "importTokenWarning": {"message": "誰でも既存のトークンの偽バージョンを含めて、任意の名前でトークンを作成することができます。追加および取引は自己責任となります！"}, "importTokensCamelCase": {"message": "トークンをインポート"}, "importTokensError": {"message": "トークンをインポートできませんでした。後ほど再度お試しください。"}, "importWallet": {"message": "ウォレットをインポート"}, "importWalletOrAccountHeader": {"message": "ウォレットまたはアカウントのインポート"}, "importWalletSuccess": {"message": "シークレットリカバリーフレーズ$1がインポートされました", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "$1をインポート", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "インポート済み", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "設定で"}, "included": {"message": "含む"}, "includesXTransactions": {"message": "$1件のトランザクションを含む"}, "infuraBlockedNotification": {"message": "MetaMaskがブロックチェーンのホストに接続できません。考えられる理由$1を確認してください。", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "最初のトランザクションはネットワークによって承認されました。戻るには「OK」をクリックします。"}, "insightsFromSnap": {"message": "$1からのインサイト", "description": "$1 represents the name of the snap"}, "install": {"message": "インストール"}, "installOrigin": {"message": "インストール元"}, "installRequest": {"message": "MetaMaskに追加"}, "installedOn": {"message": "$1にインストール", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "残高が不十分です。"}, "insufficientFunds": {"message": "資金が不十分です。"}, "insufficientFundsForGas": {"message": "ガス代が足りません"}, "insufficientLockedLiquidityDescription": {"message": "流動性が十分にロックまたはバーンされていないと、トークンの流動性が突然低下しやすくなり、市場が不安定になる可能性があります。"}, "insufficientLockedLiquidityTitle": {"message": "流動性のロックが不十分です"}, "insufficientTokens": {"message": "トークンが不十分です。"}, "interactWithSmartContract": {"message": "スマートコントラクト"}, "interactingWith": {"message": "相手:"}, "interactingWithTransactionDescription": {"message": "このコントラクトとやり取りしています。詳細を確認して詐欺師から身を守りましょう。"}, "interaction": {"message": "やり取り"}, "invalidAddress": {"message": "無効なアドレス"}, "invalidAddressRecipient": {"message": "送金先アドレスが無効です"}, "invalidAssetType": {"message": "このアセットはNFTであるため、「NFT」タブの「NFTのインポート」ページで追加しなおす必要があります"}, "invalidChainIdTooBig": {"message": "無効なチェーンID。チェーンIDが大きすぎます。"}, "invalidCustomNetworkAlertContent1": {"message": "カスタムネットワーク $1のチェーンIDの再入力が必要です。", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "悪意または欠陥のあるネットワークプロバイダーからユーザーを保護するため、すべてのカスタムネットワークに対してチェーンIDが必要になりました。"}, "invalidCustomNetworkAlertContent3": {"message": "「設定」>「ネットワーク」に進んで、チェーンIDを入力します。最もよく使用されるネットワークのチェーンIDは$1にあります。", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "カスタムネットワークが無効です"}, "invalidHexData": {"message": "16進データが無効です"}, "invalidHexNumber": {"message": "無効な16進数です。"}, "invalidHexNumberLeadingZeros": {"message": "無効な16進数です。頭のゼロを削除してください。"}, "invalidIpfsGateway": {"message": "無効なIPFSゲートウェイです: 値が有効なURLになる必要があります"}, "invalidNumber": {"message": "無効な数値です。10進数または「0x」で始まる16進数を入力してください。"}, "invalidNumberLeadingZeros": {"message": "無効な数値です。頭のゼロを削除してください。"}, "invalidRPC": {"message": "無効なRPC URL"}, "invalidSeedPhrase": {"message": "無効なシークレットリカバリーフレーズ"}, "invalidSeedPhraseCaseSensitive": {"message": "入力値が無効です！シークレットリカバリーフレーズは大文字・小文字が区別されます。"}, "ipfsGateway": {"message": "IPFSゲートウェイ"}, "ipfsGatewayDescription": {"message": "MetaMaskは、サードパーティサービスを使用して、IPFSに保管されているNFTの画像の表示、ブラウザのアドレスバーに入力されたENSアドレスに関する情報の表示、様々なトークンのアイコンの取得を行います。これらのサービスの使用時には、IPアドレスが当該サービスに公開される可能性があります。"}, "ipfsToggleModalDescriptionOne": {"message": "MetaMaskは、サードパーティサービスを使用して、IPFSに保管されているNFTの画像の表示、ブラウザのアドレスバーに入力されたENSアドレスに関する情報の表示、様々なトークンのアイコンの取得を行います。これらのサービスの使用時には、IPアドレスが当該サービスに公開される可能性があります。"}, "ipfsToggleModalDescriptionTwo": {"message": "「確認」を選択すると、IPFS解決がオンになります。これは$1でいつでもオフにできます。", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "「設定」>「セキュリティとプライバシー」"}, "isSigningOrSubmitting": {"message": "以前のトランザクションがまだ署名中または送信中です"}, "jazzAndBlockies": {"message": "JazziconとBlockieは、アカウントを一目で見分けるためのユニークなアイコンであり、2つの異なるスタイルが特徴です。"}, "jazzicons": {"message": "Jazzicon"}, "jsonFile": {"message": "JSONファイル", "description": "format for importing an account"}, "keyringAccountName": {"message": "アカウント名"}, "keyringAccountPublicAddress": {"message": "パブリックアドレス"}, "keyringSnapRemovalResult1": {"message": "$1の削除を完了$2", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "できませんでした", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "「$1」と入力して、このSnapを削除することを確定してください:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "既知のコントラクトアドレスです。"}, "knownTokenWarning": {"message": "このアクションは、ウォレットに既に一覧表示されているトークンを編集します。これは、フィッシングに使用される可能性があります。これらのトークンの表す内容を変更する意図が確実な場合にのみ承認します。$1に関する詳細をご覧ください"}, "l1Fee": {"message": "L1手数料"}, "l1FeeTooltip": {"message": "L1ガス代"}, "l2Fee": {"message": "L2手数料"}, "l2FeeTooltip": {"message": "L2ガス代"}, "lastConnected": {"message": "前回の接続"}, "lastSold": {"message": "前回の売却"}, "lavaDomeCopyWarning": {"message": "安全上の理由により、現在このテキストは選択できません。"}, "layer1Fees": {"message": "レイヤー1手数料"}, "layer2Fees": {"message": "レイヤー2手数料"}, "learnHow": {"message": "方法"}, "learnMore": {"message": "詳細"}, "learnMoreAboutGas": {"message": "ガスに関する$1をご希望ですか？", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "プライバシーのベストプラクティスに関する詳細をご覧ください。"}, "learnMoreAboutSolanaAccounts": {"message": "Solanaアカウントの詳細"}, "learnMoreKeystone": {"message": "詳細"}, "learnMoreUpperCase": {"message": "詳細"}, "learnMoreUpperCaseWithDot": {"message": "詳細。"}, "learnScamRisk": {"message": "詐欺やセキュリティのリスク。"}, "leaveMetaMask": {"message": "MetaMaskから離れますか？"}, "leaveMetaMaskDesc": {"message": "MetaMask以外のサイトにアクセスしようとしています。続行する前にURLをもう一度確認してください。"}, "ledgerAccountRestriction": {"message": "新しいアカウントを追加するには、その前に最後のアカウントを使用する必要があります。"}, "ledgerConnectionInstructionCloseOtherApps": {"message": "デバイスに接続されている他のソフトウェアを閉じてから、ここをクリックして更新してください。"}, "ledgerConnectionInstructionHeader": {"message": "確認をクリックする前:に:"}, "ledgerConnectionInstructionStepFour": {"message": "Ledgerデバイスで「スマートコントラクトデータ」または「ブラインド署名」を有効にしてください。"}, "ledgerConnectionInstructionStepThree": {"message": "Ledgerが接続されていて、イーサリアムアプリを選択していることを確認してください。"}, "ledgerDeviceOpenFailureMessage": {"message": "Ledgerデバイスを開けませんでした。Ledgerが他のソフトウェアに接続されている可能性があります。Ledger LiveまたはLedgerデバイスに接続されている他のアプリケーションを閉じて、もう一度接続してみてください。"}, "ledgerErrorConnectionIssue": {"message": "Ledgerを接続し直し、ETHアプリを開いてもう一度お試しください。"}, "ledgerErrorDevicedLocked": {"message": "Ledgerがロックされています。ロックを解除してからもう一度お試しください。"}, "ledgerErrorEthAppNotOpen": {"message": "この問題を解決するには、デバイスでETHアプリケーションを開いてもう一度お試しください。"}, "ledgerErrorTransactionDataNotPadded": {"message": "イーサリアムトランザクションの入力データが十分にパディングされていません。"}, "ledgerLiveApp": {"message": "Ledger Liveアプリ"}, "ledgerLocked": {"message": "Ledgerデバイスに接続できません。デバイスのロックが解除され、イーサリアムアプリが開かれていることを確認してください。"}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "新しいデバイスに接続するには、前のデバイスの接続を解除してください。"}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "一度に接続できるLedgerは1台のみです"}, "ledgerTimeout": {"message": "Ledger Liveが応答に時間がかかりすぎているか、接続がタイムアウトしました。Ledger Liveのアプリが開かれていて、デバイスのロックが解除されていることを確認してください。"}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Ledgerデバイスが接続されていません。Ledgerに接続する場合は、もう一度「続行」をクリックして、HID接続を承認してください。", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "水平矢印"}, "lightTheme": {"message": "ライト"}, "likeToImportToken": {"message": "このトークンをインポートしますか？"}, "likeToImportTokens": {"message": "これらのトークンを追加しますか？"}, "lineaGoerli": {"message": "Linea Goerliテストネットワーク"}, "lineaMainnet": {"message": "Lineaメインネット"}, "lineaSepolia": {"message": "Linea Sepoliaテストネットワーク"}, "link": {"message": "リンク"}, "linkCentralizedExchanges": {"message": "CoinbaseまたはBinanceアカウントをリンクして、無料でMetaMaskに仮想通貨を送金します。"}, "links": {"message": "リンク"}, "loadMore": {"message": "さらにロード"}, "loading": {"message": "ロードしています..."}, "loadingScreenSnapMessage": {"message": "Snapでトランザクションを完了させてください。"}, "loadingTokenList": {"message": "トークンリストをロードしています"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "ロック"}, "lockMetaMask": {"message": "MetaMaskをロック"}, "lockTimeInvalid": {"message": "ロック時間は0～10080の間の数字で設定する必要があります"}, "logo": {"message": "$1ロゴ", "description": "$1 is the name of the ticker"}, "low": {"message": "低"}, "lowEstimatedReturnTooltipMessage": {"message": "手数料として、開始金額の$1%を超える額を支払うことになります。受取額とネットワーク手数料を確認してください。"}, "lowEstimatedReturnTooltipTitle": {"message": "ハイコスト"}, "lowGasSettingToolTipMessage": {"message": "値下がりを待つには$1を使用してください。価格がやや予測不能なため、予想時間はあまり正確ではありません。", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "低"}, "mainnet": {"message": "イーサリアムメインネット"}, "mainnetToken": {"message": "このアドレスは、既知のイーサリアムメインネットのトークンアドレスと一致しています。追加するトークンのコントラクトアドレスとネットワークを再確認してください。"}, "makeAnotherSwap": {"message": "新しいスワップの作成"}, "makeSureNoOneWatching": {"message": "誰にも見られていないことを確認してください", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "デフォルトのプライバシー設定の管理"}, "manageInstitutionalWallets": {"message": "Institutionalウォレットの管理"}, "manageInstitutionalWalletsDescription": {"message": "Institutionalウォレットを有効にするには、これをオンにします。"}, "manageNetworksMenuHeading": {"message": "ネットワークの管理"}, "managePermissions": {"message": "アクセス許可の管理"}, "marketCap": {"message": "時価総額"}, "marketDetails": {"message": "マーケットの詳細"}, "max": {"message": "最大"}, "maxBaseFee": {"message": "最大基本料金"}, "maxFee": {"message": "最大手数料"}, "maxFeeTooltip": {"message": "トランザクションの支払いに提供される最大手数料"}, "maxPriorityFee": {"message": "最大優先手数料"}, "medium": {"message": "市場"}, "mediumGasSettingToolTipMessage": {"message": "現在の市場価格での迅速な処理には、$1を使用してください。", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "メモ"}, "message": {"message": "メッセージ"}, "metaMaskConnectStatusParagraphOne": {"message": "アカウントの接続をMetaMaskでさらに制御できるようになりました。"}, "metaMaskConnectStatusParagraphThree": {"message": "接続されているアカウントを管理するには、これをクリックします。"}, "metaMaskConnectStatusParagraphTwo": {"message": "訪問しているWebサイトが現在選択しているアカウントに接続されている場合、接続ステータスボタンが表示されます。"}, "metaMetricsIdNotAvailableError": {"message": "MetaMetricsにオプトインしていないため、ここで削除するデータはありません。"}, "metadataModalSourceTooltip": {"message": "$1はnpmでホストされていて、$2はこのSnapの一意のIDです。", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "ウォレットの通知は現在無効になっています"}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swapsはメンテナンス中です。後でもう一度確認してください。"}, "metamaskVersion": {"message": "MetaMaskのバージョン"}, "methodData": {"message": "方法"}, "methodDataTransactionDesc": {"message": "解読された入力データに基づき実行された機能"}, "methodNotSupported": {"message": "このアカウントではサポートされていません。"}, "metrics": {"message": "メトリクス"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "ネットワークの詳細の確認", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "先に進む前に$1をお勧めします。", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "弊社の記録によると、ネットワーク名がこのチェーンIDと正しく一致していない可能性があります。"}, "mismatchedNetworkSymbol": {"message": "送信された通貨記号がこのチェーンIDに関して予想されるものと一致していません。"}, "mismatchedRpcChainId": {"message": "カスタムネットワークにより返されたチェーンIDが、送信されたチェーンIDと一致しません。"}, "mismatchedRpcUrl": {"message": "弊社の記録によると、送信されたRPC URLの値がこのチェーンIDの既知のプロバイダーと一致しません。"}, "missingSetting": {"message": "設定が見つかりませんか？"}, "missingSettingRequest": {"message": "ここからリクエスト"}, "more": {"message": "他"}, "moreAccounts": {"message": "+ $1個のアカウント", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1個のネットワーク", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "他のクォート"}, "multichainAddEthereumChainConfirmationDescription": {"message": "このネットワークをMetaMaskに追加し、サイトがそれを使用することを許可しようとしています。"}, "multichainQuoteCardBridgingLabel": {"message": "ブリッジ"}, "multichainQuoteCardQuoteLabel": {"message": "クォート"}, "multichainQuoteCardTimeLabel": {"message": "時間"}, "multipleSnapConnectionWarning": {"message": "$1が$2 Snapの使用を求めています", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "トークンを1つ以上選択する必要があります。"}, "name": {"message": "名前"}, "nameAddressLabel": {"message": "アドレス", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "名前がすでに使用されています"}, "nameInstructionsNew": {"message": "このアドレスを知っている場合は、今後認識できるようニックネームを付けてください。", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "このアドレスにはデフォルトのニックネームがありますが、編集したり、他の提案を閲覧したりできます。", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "以前このアドレスのニックネームを追加しています。編集するか、他のニックネームの提案を参照できます。", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "ニックネーム", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "たとえば: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "不明なアドレス", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "認識されたアドレス", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "保存したアドレス", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "提案元: $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "イーサリアムネームサービス (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lensプロトコル"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "ニックネームを選択...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1が次の承認を求めています:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "ネットワークの詳細を編集"}, "nativeTokenScamWarningDescription": {"message": "ネイティブトークンシンボルが、関連付けられているチェーンIDのネットワークで予想されるネイティブトークンのシンボルと一致していません。予想されるトークンシンボルは$2ですが、$1と入力されました。正しいチェーンに接続されていることを確認してください。", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "別のシンボル", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "予期せぬネイティブトークンシンボル", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "アシスタンスが必要な場合は、$1にお問い合わせください", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "フィードバックを提供"}, "needHelpLinkText": {"message": "MetaMaskサポート"}, "needHelpSubmitTicket": {"message": "チケットを送信"}, "needImportFile": {"message": "インポートするファイルの選択が必要です。", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "負の額のETHを送金することはできません。"}, "negativeOrZeroAmountToken": {"message": "資産をマイナスまたはゼロの金額で送ることはできません。"}, "network": {"message": "ネットワーク:"}, "networkChanged": {"message": "ネットワークが変更されました"}, "networkChangedMessage": {"message": "現在$1で取引しています。", "description": "$1 is the name of the network"}, "networkDetails": {"message": "ネットワークの詳細"}, "networkFee": {"message": "ネットワーク手数料"}, "networkIsBusy": {"message": "ネットワークが混み合っています。ガス代が高く、見積もりはあまり正確ではありません。"}, "networkMenu": {"message": "ネットワークメニュー"}, "networkMenuHeading": {"message": "ネットワークを選択"}, "networkName": {"message": "ネットワーク名"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "ビットコイン"}, "networkNameDefinition": {"message": "このネットワークに関連付けられている名前。"}, "networkNameEthereum": {"message": "イーサリアム"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OPメインネット"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "テストネット"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "ネットワークオプション"}, "networkPermissionToast": {"message": "ネットワークへのアクセス許可が更新されました"}, "networkProvider": {"message": "ネットワークプロバイダー"}, "networkStatus": {"message": "ネットワークステータス"}, "networkStatusBaseFeeTooltip": {"message": "基本料金はネットワークによって設定され、13～14秒ごとに変更されます。弊社の$1と$2のオプションは、突然の上昇を考慮したものです。", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "優先手数料 (別名「マイナーチップ」) の範囲。これはマイナーに直接支払われ、トランザクションを優先するインセンティブとなります。"}, "networkStatusStabilityFeeTooltip": {"message": "ガス代は過去72時間と比較して$1です。", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "$1に接続できません", "description": "$1 represents the network name"}, "networkURL": {"message": "ネットワークURL"}, "networkURLDefinition": {"message": "このネットワークへのアクセスに使用されるURLです。"}, "networkUrlErrorWarning": {"message": "攻撃者は、サイトのアドレスに若干の変更を加えてサイトを模倣することがあります。続行する前に、意図したサイトとやり取りしていることを確認してください。Punycodeバージョン: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "ネットワーク"}, "networksSmallCase": {"message": "ネットワーク"}, "nevermind": {"message": "取り消し"}, "new": {"message": "新登場！"}, "newAccount": {"message": "新しいアカウント"}, "newAccountNumberName": {"message": "アカウント$1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "新しい連絡先"}, "newContract": {"message": "新しいコントラクト"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "「設定」>「セキュリティとプライバシー」"}, "newNFTDetectedInImportNFTsMsg": {"message": "OpenSeaを使用してNFTを表示するには、$1で「NFTメディアの表示」をオンにしてください。", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "MetaMaskによるウォレット内のNFTの自動検出と表示を許可します。"}, "newNFTsAutodetected": {"message": "NFTの自動検出"}, "newNetworkAdded": {"message": "「$1」が追加されました！"}, "newNetworkEdited": {"message": "“$1”が編集されました！"}, "newNftAddedMessage": {"message": "NFTが追加されました！"}, "newPassword": {"message": "新しいパスワード (最低8文字)"}, "newPrivacyPolicyActionButton": {"message": "続きを表示"}, "newPrivacyPolicyTitle": {"message": "プライバシーポリシーが更新されました"}, "newRpcUrl": {"message": "新しいRPC URL"}, "newTokensImportedMessage": {"message": "$1をインポートしました。", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "トークンがインポートされました"}, "next": {"message": "次へ"}, "nftAddFailedMessage": {"message": "所有者情報が一致していないため、NFTを追加できません。入力された情報が正しいことを確認してください。"}, "nftAddressError": {"message": "このトークンはNFTです。$1で追加してください", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFTがすでに追加されています。"}, "nftAutoDetectionEnabled": {"message": "NFTの自動検出が有効になりました"}, "nftDisclaimer": {"message": "開示事項: MetaMaskはソースURLからメディアファイルを取得します。このURLは時々、NFTがミントされたマーケットプレイスにより変更されることがあります。"}, "nftOptions": {"message": "NFTオプション"}, "nftTokenIdPlaceholder": {"message": "トークンIDを入力してください"}, "nftWarningContent": {"message": "今後取得する可能性のあるものも含め、$1へのアクセスを許可しようとしています。相手はこの承認が取り消されるまで、お客様のウォレットからいつでも許可なしにこれらのNFTを送ることができます。$2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "手持ちのすべての$1 NFT", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "慎重に進めてください。"}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "以前保有"}, "nickname": {"message": "ニックネーム"}, "noAccountsFound": {"message": "指定された検索クエリでアカウントが見つかりませんでした"}, "noActivity": {"message": "まだアクティビティがありません"}, "noConnectedAccountTitle": {"message": "MetaMaskはこのサイトに接続されていません"}, "noConnectionDescription": {"message": "サイトに接続するには、「接続」ボタンを見つけて選択します。MetaMaskはWeb3のサイトにしか接続できないのでご注意ください"}, "noConversionRateAvailable": {"message": "利用可能な換算レートがありません"}, "noDeFiPositions": {"message": "まだポジションがありません"}, "noDomainResolution": {"message": "指定されたドメインの名前解決ができません。"}, "noHardwareWalletOrSnapsSupport": {"message": "Snap、およびほとんどのハードウェアウォレットは、現在お使いのブラウザのバージョンで使用できません。"}, "noNFTs": {"message": "NFTはまだありません"}, "noNetworksFound": {"message": "入力された検索クエリでネットワークが見つかりません"}, "noOptionsAvailableMessage": {"message": "この取引ルートは現在使用できません。金額、ネットワーク、またはトークンを変更すれば、最適なオプションをお探しします。"}, "noSnaps": {"message": "snapがインストールされていません"}, "noThanks": {"message": "結構です"}, "noTransactions": {"message": "トランザクションがありません"}, "noWebcamFound": {"message": "お使いのコンピューターのWebカメラが見つかりませんでした。もう一度お試しください。"}, "noWebcamFoundTitle": {"message": "Webカメラが見つかりません"}, "nonContractAddressAlertDesc": {"message": "コントラクトではないアドレスにコールデータを送信しようとしています。これにより、資金が失われる可能性があります。続行する前に、正しいアドレスとネットワークを使用していることを確認してください。"}, "nonContractAddressAlertTitle": {"message": "誤りの可能性があります"}, "nonce": {"message": "ナンス"}, "none": {"message": "なし"}, "notBusy": {"message": "ビジー状態ではありません"}, "notCurrentAccount": {"message": "これは正しいアカウントですか？ウォレットで現在選択されているアカウントと異なっています"}, "notEnoughBalance": {"message": "残高が不十分です"}, "notEnoughGas": {"message": "ガスが不足しています"}, "notNow": {"message": "また後で"}, "notificationDetail": {"message": "詳細"}, "notificationDetailBaseFee": {"message": "基本料金 (gwei)"}, "notificationDetailGasLimit": {"message": "ガスリミット (単位)"}, "notificationDetailGasUsed": {"message": "使用ガス (単位)"}, "notificationDetailMaxFee": {"message": "ガス1単位あたりの最大手数料"}, "notificationDetailNetwork": {"message": "ネットワーク"}, "notificationDetailNetworkFee": {"message": "ネットワーク手数料"}, "notificationDetailPriorityFee": {"message": "優先手数料 (gwei)"}, "notificationItemCheckBlockExplorer": {"message": "ブロックエクスプローラーで確認する"}, "notificationItemCollection": {"message": "コレクション"}, "notificationItemConfirmed": {"message": "確定されました"}, "notificationItemError": {"message": "現在手数料を取得できません"}, "notificationItemFrom": {"message": "移動元"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "出金準備ができました"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "これでステーキングが解除された$1を引き出すことができます"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "$1のステーキングを解除するリクエストが送信されました"}, "notificationItemNFTReceivedFrom": {"message": "NFTを次の相手から受け取りました:"}, "notificationItemNFTSentTo": {"message": "NFTを次の相手に送りました:"}, "notificationItemNetwork": {"message": "ネットワーク"}, "notificationItemRate": {"message": "レート (手数料込み)"}, "notificationItemReceived": {"message": "受け取りました"}, "notificationItemReceivedFrom": {"message": "次の相手から受け取りました:"}, "notificationItemSent": {"message": "送りました"}, "notificationItemSentTo": {"message": "次の相手に送りました:"}, "notificationItemStakeCompleted": {"message": "ステーキングが完了しました"}, "notificationItemStaked": {"message": "ステーキングしました"}, "notificationItemStakingProvider": {"message": "ステーキングプロバイダー"}, "notificationItemStatus": {"message": "ステータス"}, "notificationItemSwapped": {"message": "スワップ完了"}, "notificationItemSwappedFor": {"message": "for"}, "notificationItemTo": {"message": "移動先"}, "notificationItemTransactionId": {"message": "トランザクションID"}, "notificationItemUnStakeCompleted": {"message": "ステーキングの解除が完了しました"}, "notificationItemUnStaked": {"message": "ステーキングが解除されました"}, "notificationItemUnStakingRequested": {"message": "ステーキングの解除がリクエストされました"}, "notificationTransactionFailedMessage": {"message": "トランザクション $1 に失敗しました！$2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "トランザクション失敗", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "トランザクション $1 が承認されました！", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "トランザクションの承認完了", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "$1で表示", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "通知"}, "notificationsFeatureToggle": {"message": "ウォレットの通知を有効にする", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "これにより、資金やNFTのやり取りなどに関するウォレットの通知と、機能に関するお知らせが有効になります。", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "すべて既読にする"}, "notificationsPageEmptyTitle": {"message": "ここに表示する内容はありません"}, "notificationsPageErrorContent": {"message": "このページにもう一度アクセスしてみてください"}, "notificationsPageErrorTitle": {"message": "エラーが発生しました"}, "notificationsPageNoNotificationsContent": {"message": "まだ通知を受け取っていません。"}, "notificationsSettingsBoxError": {"message": "問題が発生しました。もう一度お試しください。"}, "notificationsSettingsPageAllowNotifications": {"message": "通知を使えば、ウォレットで何が起きているか常に把握できます。通知を使用するには、プロファイルを使用してデバイス間で一部の設定を同期します。$1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "この機能を使用する際に当社がどのようにユーザーのプライバシーを保護するのか、ご覧ください。"}, "numberOfNewTokensDetectedPlural": {"message": "$1種類の新しいトークンがこのアカウントで見つかりました", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1つの新しいトークンがこのアカウントで見つかりました"}, "numberOfTokens": {"message": "トークンの数"}, "ofTextNofM": {"message": "中の"}, "off": {"message": "オフ"}, "offlineForMaintenance": {"message": "メンテナンスのためにオフラインです"}, "ok": {"message": "OK"}, "on": {"message": "オン"}, "onboardedMetametricsAccept": {"message": "同意する"}, "onboardedMetametricsDisagree": {"message": "いいえ、結構です"}, "onboardedMetametricsKey1": {"message": "最新情報"}, "onboardedMetametricsKey2": {"message": "製品の機能"}, "onboardedMetametricsKey3": {"message": "その他関連プロモーション資料"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "$1に加え、マーケティングコミュニケーションとのインタラクションについて把握するためにもデータを使用します。", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "これは、次のようなお伝えする情報のカスタマイズに役立ちます:"}, "onboardedMetametricsParagraph3": {"message": "ユーザーから提供されたデータが販売されることは一切なく、いつでもオプトアウトできます。"}, "onboardedMetametricsTitle": {"message": "エクスペリエンスの改善にご協力ください"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "IPFSゲートウェイにより、第三者がホスティングしているデータへのアクセスと表示が可能になります。カスタムIPFSゲートウェイを追加するか、引き続きデフォルトを使用できます。"}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "有効なURLを入力してください"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "カスタムIPFSゲートウェイを追加"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "IPFSゲートウェイのURLが有効です"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "デフォルトの設定と構成を使用する場合、当社はInfuraをデフォルトの遠隔手続き呼び出し (RPC) プロバイダーとして使用し、イーサリアムデータへの可能な限り最も信頼性が高くプライベートなアクセスを提供します。まれに、ユーザーに最も優れたエクスペリエンスを提供するために、他のRPCプロバイダーが使用される場合もあります。ユーザーが自らRPCを選択することもできますが、どのRPCもトランザクションを実行するために、ユーザーのIPアドレスとイーサリアムウォレットを取得する点にご注意ください。InfuraによるEVMアカウントのデータの取り扱いに関する詳細は、$1をご覧ください。Solanaアカウントの場合は、$2。"}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "こちらをクリック"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "ネットワークを選択してください"}, "onboardingCreateWallet": {"message": "新規ウォレットを作成"}, "onboardingImportWallet": {"message": "既存のウォレットをインポート"}, "onboardingMetametricsAgree": {"message": "同意します"}, "onboardingMetametricsDescription": {"message": "MetaMaskの改善を目的に、基本的な使用状況および診断データを収集したいと思います。ここで提供されるデータが販売されることはありません。"}, "onboardingMetametricsInfuraTerms": {"message": "このデータを他の目的に使用する際は、お知らせします。詳細は当社の$1をご覧ください。設定でいつでもオプトアウトできます。", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "プライバシー ポリシー"}, "onboardingMetametricsNeverCollect": {"message": "$1 アプリのクリックやビューは保存されますが、その他の詳細 (ユーザーのパブリックアドレスなど) は保存されません。", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "非公開:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 大まかな位置情報 (国や地域など) の検出にユーザーのIPアドレスが一時的に使用されますが、保存されることはありません。", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "一般:"}, "onboardingMetametricsNeverSellData": {"message": "$1 使用状況データを共有するか削除するかは、設定でいつでも指定できます。", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "任意:"}, "onboardingMetametricsTitle": {"message": "MetaMaskの改善にご協力ください"}, "onboardingMetametricsUseDataCheckbox": {"message": "このデータは、ユーザーによる当社のマーケティングコミュニケーションとのインタラクションを把握するために使用されます。また、関連ニュースをお伝えする場合もあります (製品の機能など)。"}, "onboardingPinExtensionDescription": {"message": "MetaMaskをブラウザにピン留めすることで、アクセスしやすくなり、トランザクションの承認を簡単に表示できるようになります。"}, "onboardingPinExtensionDescription2": {"message": "拡張機能をクリックしてMetaMaskを開き、ワンクリックでウォレットにアクセスできます。"}, "onboardingPinExtensionDescription3": {"message": "ブラウザの拡張機能アイコンをクリックすると、すぐにアクセスできます", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "MetaMaskのインストールが完了しました！"}, "onekey": {"message": "OneKey"}, "only": {"message": "のみ"}, "onlyConnectTrust": {"message": "信頼するサイトにのみ接続してください。$1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "全画面モードにしてLedgerを接続します。", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "ブロックエクスプローラーで開く"}, "optional": {"message": "オプション"}, "options": {"message": "オプション"}, "origin": {"message": "起点"}, "originChanged": {"message": "サイトが変更されました"}, "originChangedMessage": {"message": "現在$1からの要求を確認しています。", "description": "$1 is the name of the origin"}, "osTheme": {"message": "システム"}, "other": {"message": "その他"}, "otherSnaps": {"message": "他のsnap", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "その他"}, "outdatedBrowserNotification": {"message": "古いブラウザを使用しています。ブラウザをアップデートしないと、MetaMaskからセキュリティパッチや新機能を入手できなくなります。"}, "overrideContentSecurityPolicyHeader": {"message": "Content-Security-Policyヘッダーを上書き"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "このオプションは、DAppのContent-Security-Policyヘッダーによって拡張機能が正しく読み込まれない場合があるという、Firefoxの既知の問題に対する回避策です。特定のWebページの互換性に必要な場合を除き、このオプションを無効にすることはお勧めしません。"}, "padlock": {"message": "南京錠"}, "participateInMetaMetrics": {"message": "MetaMetricsに参加"}, "participateInMetaMetricsDescription": {"message": "MetaMetricsに参加して、MetaMaskの改善にご協力ください"}, "password": {"message": "パスワード"}, "passwordNotLongEnough": {"message": "パスワードの長さが足りません"}, "passwordStrength": {"message": "パスワードの強度: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "強力なパスワードは、デバイスが盗まれたり侵入されたりした場合に、ウォレットのセキュリティを高めます。"}, "passwordTermsWarning": {"message": "私はMetaMaskがこのパスワードを復元できないことを理解しています。$1"}, "passwordsDontMatch": {"message": "パスワードが一致しません"}, "pastePrivateKey": {"message": "秘密鍵の文字列をここに貼り付けます:", "description": "For importing an account from a private key"}, "pending": {"message": "保留中"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "ネットワークを更新すると、このサイトからの$1件の保留中のトランザクションがキャンセルされます。", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "ネットワークを切り替えると、このサイトからの$1件の保留中のトランザクションがキャンセルされます。", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "このトランザクションは、前のトランザクションが完了するまで実行されません。$1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "トランザクションをキャンセルまたは高速化する方法をご覧ください。", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "アクセス許可の詳細"}, "permissionFor": {"message": "アクセス許可:"}, "permissionFrom": {"message": "次からのアクセス許可:"}, "permissionRequested": {"message": "現在リクエスト中"}, "permissionRequestedForAccounts": {"message": "$1に対して要求済み", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "この更新で取り消し"}, "permissionRevokedForAccounts": {"message": "この更新で$1に対して取り消し済み", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "$1に接続。", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "インターネットにアクセスします。", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "$1によるインターネットへのアクセスを許可します。これは、サードパーティサーバーとのデータの送受信に使用されます。", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "$1 snapに接続します。", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Webサイトまたはsnapによる$1とのやり取りを許可します。", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "MetaMaskにアカウントの資産を表示します。", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "$1によるMetaMaskクライアントへの資産情報の提供を許可します。資産にはオンチェーンとオフチェーンの両方のものが含まれます。", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "定期的なアクションのスケジュール設定と実行。", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "$1が一定の時刻、日付、または間隔で定期的に実行されるアクションを実行することを許可します。これは、時間依存のやり取りや通知のトリガーに使用されます。", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "MetaMaskにダイアログウィンドウを表示します。", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "$1がカスタムテキスト、入力フィールド、アクションの承認・拒否ボタンを備えたMetaMaskポップアップを表示することを許可します。\nこれは、Snapのアラート、承認、オプトインフローなどの作成に使用されます。", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "アドレス、アカウント残高、アクティビティを表示して、承認するトランザクションを提案", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "イーサリアムプロバイダーにアクセスします。", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "$1がブロックチェーンのデータを読み込みメッセージやトランザクションを提案するために、MetaMaskと直接通信することを許可します。", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "$1に固有の任意のキーを導出します。", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "$1が、$1固有の任意のキーを公開せずに導出することを許可します。これらのキーはMetaMaskアカウントとは切り離されており、秘密鍵やシークレットリカバリーフレーズとは関連性がありません。他のSnapはこの情報にアクセスできません。", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "言語設定の表示", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "$1がMetaMaskの言語設定にアクセスできるようにします。これは、$1のコンテンツをユーザーの言語にローカライズして表示するために使用されます。", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "優先言語や法定通貨などの情報の表示", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "$1によるMetaMask設定の優先言語や法定通貨などの情報へのアクセスを許可します。これにより、$1がユーザーの設定に合わせたコンテンツを表示できるようになります。", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "カスタム画面の表示", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "$1がMetaMaskでカスタムホーム画面を表示することを許可します。これは、ユーザーインターフェース、構成、ダッシュボードに使用されます。", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "イーサリアムアカウントの追加と制御の要求を許可する", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "$1がアカウントの追加または削除のリクエストを受け取ることや、これらのアカウントの代理で署名やトランザクションを行うことを許可します。", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "ライフサイクルフックを使用します。", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "$1がライフサイクルフックを使用して、ライフサイクルの特定のタイミングでコードを実行することを許可します。", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "イーサリアムアカウントを追加して管理します", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "$1がイーサリアムアカウントを追加または削除し、これらのアカウントでトランザクションや署名を行うことを許可します。", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "$1アカウントの管理", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "$1による要求されたネットワークでのアカウントおよび資産の管理を許可します。これらのアカウントはシークレットリカバリーフレーズを (公開せずに) 使用して導出およびバックアップされます。キーを導出できることで、$1はイーサリアム (EVM) だけでなく、様々なブロックチェーンプロトコルをサポートできるようになります。", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "$1アカウントの管理", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "デバイスにデータを保管し管理します。", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "$1が暗号化を使用して安全にデータを保管、更新、取得することを許可します。他のSnapはこの情報にアクセスできません。", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "ドメインとアドレス検索を提供します。", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "SnapがMetaMask UIのさまざまな部分でアドレスとドメイン検索の取得と表示を行うことを許可します。", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "通知を表示します。", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "$1がMetaMask内に通知を表示することを許可します。Snapは、行動を促す情報や緊急性の高い情報に関する短い通知テキストをトリガーできます。", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "1つまたは複数のチェーンにプロトコルデータを提供します。", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "ガス代の見積もりやトークン情報など、$1によるMetaMaskへのプロトコルデータの提供を許可します。", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "$1が$2と直接やり取りすることを許可します。", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "$1による$2へのメッセージの送信と$2からの応答の受信を許可します。", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1および$2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "署名分析情報モーダルの表示。", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "署名要求の承認前に、$1がモーダルを表示して署名要求に関する分析情報を提供することを許可します。これはフィッシング対策やセキュリティソリューションに使用されます。", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "署名要求を開始したWebサイトの出所を表示する", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "$1による署名要求を開始したWebサイトの出所 (URI) の確認を許可します。これは、フィッシング対策やセキュリティソリューションに使用されます。", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "トランザクションインサイトを取得して表示します。", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "$1によるトランザクションのデコードと、MetaMask UI内でのインサイトの表示を許可します。これは、フィッシング対策やセキュリティソリューションに使用されます。", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "トランザクションを提案しているWebサイトの提供元を確認します", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "$1が、トランザクションを提案するWebサイトの出所 (URI) を確認することを許可します。これは、フィッシング対策やセキュリティソリューションに使用されます。", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "不明な許可: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "$1 ($2) の公開鍵を表示します。", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "$2が、$1の公開鍵 (およびアドレス) を表示することを許可します。これは、アカウントや資産のコントロールを許可するものでは一切ありません。", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "$1の公開鍵を表示します。", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "有効なネットワークを使用します", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "WebAssemblyのサポート", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "$1がWebAssemblyを介して低レベルの実行環境にアクセスすることを許可します。", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "許可"}, "permissionsPageEmptyContent": {"message": "ここに表示する内容はありません"}, "permissionsPageEmptySubContent": {"message": "ここには、インストールされたSnapや接続されたサイトに付与したアクセス許可が表示されます。"}, "permitSimulationChange_approve": {"message": "使用上限"}, "permitSimulationChange_bidding": {"message": "入札"}, "permitSimulationChange_listing": {"message": "出品"}, "permitSimulationChange_nft_listing": {"message": "出品価格"}, "permitSimulationChange_receive": {"message": "受取"}, "permitSimulationChange_revoke2": {"message": "取り消す"}, "permitSimulationChange_transfer": {"message": "送金"}, "permitSimulationDetailInfo": {"message": "この数量のトークンをアカウントから転送する権限を使用者に付与しようとしています。"}, "permittedChainToastUpdate": {"message": "$1は$2にアクセスできます。"}, "personalAddressDetected": {"message": "個人アドレスが検出されました。トークンコントラクトアドレスを入力してください。"}, "pinToTop": {"message": "最上部にピン留め"}, "pleaseConfirm": {"message": "確認してください"}, "plusMore": {"message": "他$1件", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "その他$1件", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "これらのネットワークの一部はサードパーティに依存しているため、接続の信頼性が低かったり、サードパーティによるアクティビティの追跡が可能になったりする可能性があります。", "description": "Learn more link"}, "popularNetworks": {"message": "人気のネットワーク"}, "portfolio": {"message": "Portfolio"}, "preparingSwap": {"message": "スワップを準備しています..."}, "prev": {"message": "前へ"}, "price": {"message": "価格"}, "priceUnavailable": {"message": "価格が利用できません"}, "primaryType": {"message": "基本型"}, "priorityFee": {"message": "優先手数料"}, "priorityFeeProperCase": {"message": "優先手数料"}, "privacy": {"message": "プライバシー"}, "privacyMsg": {"message": "プライバシーポリシー"}, "privateKey": {"message": "秘密鍵", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "$1の秘密鍵", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "秘密鍵は非表示になっています", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "秘密鍵の入力の表示・非表示を切り替えます", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "秘密鍵は表示されています", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "警告: この鍵は絶対に公開しないでください。秘密鍵を持つ人は誰でも、アカウントに保持されているアセットを盗むことができます。"}, "privateNetwork": {"message": "プライベートネットワーク"}, "proceedWithTransaction": {"message": "それでも続行"}, "productAnnouncements": {"message": "製品に関するお知らせ"}, "proposedApprovalLimit": {"message": "提案された承認限度額"}, "provide": {"message": "提供"}, "publicAddress": {"message": "パブリックアドレス"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "$1 $2を受け取りました"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "トークンを受け取りました"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "資金の受領"}, "pushPlatformNotificationsFundsSentDescription": {"message": "$1 $2を送りました"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "トークンを送りました"}, "pushPlatformNotificationsFundsSentTitle": {"message": "資金の送付"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "新しいNFTを受け取りました"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFTの受領"}, "pushPlatformNotificationsNftSentDescription": {"message": "NFTを送りました"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFTの送付"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Lidoのステーキングが完了しました"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "ステーキング完了"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Lidoのステークの出金準備ができました"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "ステークの出金準備完了"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Lidoの出金が完了しました"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "出金完了"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Lidoの出金リクエストが送信されました"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "出金のリクエスト"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "RocketPoolのステーキングが完了しました"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "ステーキング完了"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "RocketPoolのステーキングの解除が完了しました"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "ステーキングの解除完了"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "MetaMaskスワップが完了しました"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "スワップ完了"}, "queued": {"message": "キュー待ち"}, "quoteRate": {"message": "クォートレート"}, "quotedReceiveAmount": {"message": "$1の受取額"}, "quotedTotalCost": {"message": "$1の合計コスト"}, "rank": {"message": "ランク"}, "rateIncludesMMFee": {"message": "レートには$1%の手数料が含まれています"}, "reAddAccounts": {"message": "他のアカウントを再度追加"}, "reAdded": {"message": "再度追加されました"}, "readdToken": {"message": "アカウントオプションメニューで「トークンのインポート」を選択することによって、今後このトークンを戻すことができます。"}, "receive": {"message": "受取"}, "receiveCrypto": {"message": "仮想通貨を受け取る"}, "recipientAddressPlaceholderNew": {"message": "パブリックアドレス (0x) またはドメイン名を入力してください"}, "recommendedGasLabel": {"message": "推奨"}, "recoveryPhraseReminderBackupStart": {"message": "ここから開始"}, "recoveryPhraseReminderConfirm": {"message": "了解"}, "recoveryPhraseReminderHasBackedUp": {"message": "シークレットリカバリーフレーズは常に安全かつ秘密の場所に保管してください"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "シークレットリカバリーフレーズのバックアップが必要ですか？"}, "recoveryPhraseReminderItemOne": {"message": "シークレットリカバリーフレーズは誰とも決して共有しないでください"}, "recoveryPhraseReminderItemTwo": {"message": "MetaMaskチームが、ユーザーのシークレットリカバリーフレーズを確認することは絶対にありません"}, "recoveryPhraseReminderSubText": {"message": "シークレットリカバリーフレーズは、ご利用のすべてのアカウントを制御します。"}, "recoveryPhraseReminderTitle": {"message": "資産を守りましょう"}, "redeposit": {"message": "再デポジット"}, "refreshList": {"message": "リストを更新"}, "reject": {"message": "拒否"}, "rejectAll": {"message": "すべて拒否"}, "rejectRequestsDescription": {"message": "$1件のリクエストを一括で拒否しようとしています。"}, "rejectRequestsN": {"message": "$1件のリクエストを拒否"}, "rejectTxsDescription": {"message": "$1件のトランザクションを一括拒否しようとしています。"}, "rejectTxsN": {"message": "$1件のトランザクションを拒否"}, "rejected": {"message": "拒否されました"}, "remove": {"message": "削除"}, "removeAccount": {"message": "アカウントを削除"}, "removeAccountDescription": {"message": "このアカウントはウォレットから削除されます。続行する前に、インポートしたアカウントの元のシークレットリカバリーフレーズまたは秘密鍵を持っていることを確認してください。アカウントはアカウントドロップダウンから再度インポートまたは作成できます。"}, "removeKeyringSnap": {"message": "このSnapを削除すると、これらのアカウントがMetaMaskから削除されます:"}, "removeKeyringSnapToolTip": {"message": "Snapはアカウントをコントロールするため、Snapを削除するとアカウントもMetaMaskから削除されますが、ブロックチェーン上から削除されることはありません。"}, "removeNFT": {"message": "NFTを削除"}, "removeNftErrorMessage": {"message": "このNFTを削除できませんでした。"}, "removeNftMessage": {"message": "NFTが削除されました！"}, "removeSnap": {"message": "snapを削除"}, "removeSnapAccountDescription": {"message": "続行すると、このアカウントはMetaMaskで使用できなくなります。"}, "removeSnapAccountTitle": {"message": "アカウントを削除"}, "removeSnapConfirmation": {"message": "$1を削除してよろしいですか？", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "この操作により、snapとそのデータが削除され、与えられたアクセス許可が取り消されます。"}, "replace": {"message": "置き換え"}, "reportIssue": {"message": "問題を報告する"}, "requestFrom": {"message": "要求元"}, "requestFromInfo": {"message": "これは署名を求めているサイトです。"}, "requestFromInfoSnap": {"message": "これは署名を求めているSnapです。"}, "requestFromTransactionDescription": {"message": "これが承認を要求しているサイトです。"}, "requestingFor": {"message": "次の要求:"}, "requestingForAccount": {"message": "$1の要求", "description": "Name of Account"}, "requestingForNetwork": {"message": "$1の要求", "description": "Name of Network"}, "required": {"message": "必須"}, "reset": {"message": "リセット"}, "resetWallet": {"message": "ウォレットをリセット"}, "resetWalletSubHeader": {"message": "MetaMaskはパスワードのコピーを保管しません。アカウントのロックを解除できない場合は、ウォレットをリセットする必要があります。これは、ウォレットのセットアップ時に使用したシークレットリカバリーフレーズを入力することで行えます。"}, "resetWalletUsingSRP": {"message": "この操作により、このデバイスから現在のウォレットとシークレットリカバリーフレーズ、および作成されたアカウントのリストが削除されます。シークレットリカバリーフレーズでリセットすると、リセットに使用されたシークレットリカバリーフレーズに基づくアカウントのリストが表示されます。この新しいリストには、残高のあるアカウントが自動的に含まれます。また、以前作成された$1することもできます。インポートしたカスタムアカウントは$2である必要があり、アカウントに追加されたカスタムトークンも$3である必要があります。"}, "resetWalletWarning": {"message": "続行する前に、正しいシークレットリカバリーフレーズを使用していることを確認してください。これは元に戻せません。"}, "restartMetamask": {"message": "MetaMask を再起動"}, "restore": {"message": "復元"}, "restoreUserData": {"message": "ユーザーデータの復元"}, "resultPageError": {"message": "エラー"}, "resultPageErrorDefaultMessage": {"message": "操作に失敗しました。"}, "resultPageSuccess": {"message": "成功"}, "resultPageSuccessDefaultMessage": {"message": "操作が正常に完了しました。"}, "retryTransaction": {"message": "トランザクションを再試行"}, "reusedTokenNameWarning": {"message": "ここのトークンは、監視する別のトークンのシンボルを再使用します。これは混乱を招いたり紛らわしい場合があります。"}, "revealSecretRecoveryPhrase": {"message": "シークレットリカバリーフレーズを確認"}, "revealSeedWords": {"message": "シークレットリカバリーフレーズを確認"}, "revealSeedWordsDescription1": {"message": "$1は$2を提供します。", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMaskは$1です。つまり、ユーザーがSRPの所有者となります。", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "ウォレットと資金への完全アクセス"}, "revealSeedWordsNonCustodialWallet": {"message": "ノンカストディアルウォレット"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "シークレットリカバリーフレーズ (SRP)"}, "revealSeedWordsText": {"message": "テキスト"}, "revealSeedWordsWarning": {"message": "誰にも画面を見られていないことを確認してください。$1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "MetaMaskサポートがこの情報を尋ねることはありません。", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "機密のコンテンツを確認"}, "review": {"message": "確認"}, "reviewAlert": {"message": "アラートを確認"}, "reviewAlerts": {"message": "アラートを確認する"}, "reviewPendingTransactions": {"message": "保留中のトランザクションを確認"}, "reviewPermissions": {"message": " アクセス許可を確認する"}, "revokePermission": {"message": "許可を取り消す"}, "revokePermissionTitle": {"message": "$1のアクセス許可を取り消す", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "別のユーザーに付与したアカウントのトークン使用許可を取り消そうとしています。"}, "reward": {"message": "報酬"}, "rpcNameOptional": {"message": "RPC名 (オプション)"}, "rpcUrl": {"message": "RPC URL"}, "safeTransferFrom": {"message": "安全な送金元:"}, "save": {"message": "保存"}, "scanInstructions": {"message": "QRコードにカメラを向けてください"}, "scanQrCode": {"message": "QRコードをスキャン"}, "scrollDown": {"message": "下にスクロール"}, "search": {"message": "検索"}, "searchAccounts": {"message": "アカウントを検索"}, "searchNfts": {"message": "NFTを検索"}, "searchTokens": {"message": "トークンを検索"}, "searchTokensByNameOrAddress": {"message": "トークンを名前またはアドレスで検索する"}, "secretRecoveryPhrase": {"message": "シークレットリカバリーフレーズ"}, "secretRecoveryPhrasePlusNumber": {"message": "シークレットリカバリーフレーズ$1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "安全なウォレット"}, "security": {"message": "セキュリティ"}, "securityAlert": {"message": "$1と$2からのセキュリティアラート"}, "securityAlerts": {"message": "セキュリティアラート"}, "securityAlertsDescription": {"message": "この機能は、トランザクションと署名要求を能動的に確認し、悪質または異常なアクティビティに関するアラートを発します。$1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "セキュリティとプライバシー"}, "securityDescription": {"message": "安全ではないネットワークに参加してしまう可能性を減らし、アカウントを守ります"}, "securityMessageLinkForNetworks": {"message": "ネットワーク詐欺とセキュリティのリスク"}, "securityProviderPoweredBy": {"message": "データソース: $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "すべてのアクセス許可を表示", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "詳細を表示"}, "seedPhraseIntroTitle": {"message": "ウォレットの保護"}, "seedPhraseReq": {"message": "シークレットリカバリーフレーズは、12、15、18、21、24語で構成されます"}, "select": {"message": "選択"}, "selectAccountToConnect": {"message": "接続するアカウントを選択します"}, "selectAccounts": {"message": "このサイトに使用するアカウントを選択してください"}, "selectAccountsForSnap": {"message": "このsnapで使用するアカウントを選択してください"}, "selectAll": {"message": "すべて選択"}, "selectAnAccount": {"message": "アカウントを選択してください"}, "selectAnAccountAlreadyConnected": {"message": "このアカウントはすでにMetaMaskに接続されています"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "NFTメディアの表示をオンにする"}, "selectHdPath": {"message": "HDパスを選択"}, "selectNFTPrivacyPreference": {"message": "NFTの自動検出を有効にする"}, "selectPathHelp": {"message": "アカウントが見当たらない場合は、HDパスまたは現在選択されているネットワークを切り替えてみてください。"}, "selectRpcUrl": {"message": "RPC URLを選択"}, "selectSecretRecoveryPhrase": {"message": "シークレットリカバリーフレーズを選択"}, "selectType": {"message": "種類を選択"}, "selectedAccountMismatch": {"message": "別のアカウントが選択されました"}, "selectingAllWillAllow": {"message": "すべてを選択すると、このサイトに現在のすべてのアカウントが表示されます。このサイトが信頼できることを確認してください。"}, "send": {"message": "送金"}, "sendBugReport": {"message": "バグの報告をお送りください。"}, "sendNoContactsConversionText": {"message": "ここをクリック"}, "sendNoContactsDescription": {"message": "連絡先を使用すると、別のアカウントに安全に何度もトランザクションを送信できます。連絡先を作成するには、$1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "まだ連絡先がありません"}, "sendSelectReceiveAsset": {"message": "受け取るアラートを選択してください"}, "sendSelectSendAsset": {"message": "送る資産を選択してください"}, "sendSpecifiedTokens": {"message": "$1を送金", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "このボタンをクリックすると、直ちにスワップトランザクションが開始します。続ける前に、トランザクションの詳細を確認してください。"}, "sendTokenAsToken": {"message": "$1を$2として送金", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "$1を送信中"}, "sendingDisabled": {"message": "ERC-1155 NFTアセットの送信は、まだサポートされていません。"}, "sendingNativeAsset": {"message": "$1を送信中", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "警告: 資金の喪失に繋がる可能性のあるトークンコントラクトに送信しようとしています。$1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Sepoliaテストネットワーク"}, "setApprovalForAll": {"message": "すべてを承認に設定"}, "setApprovalForAllRedesignedTitle": {"message": "出金のリクエスト"}, "setApprovalForAllTitle": {"message": "使用限度額なしで$1を承認", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "snapアカウントを追加"}, "settings": {"message": "設定"}, "settingsSearchMatchingNotFound": {"message": "一致する結果が見つかりませんでした。"}, "settingsSubHeadingSignaturesAndTransactions": {"message": "署名およびトランザクション要求"}, "show": {"message": "表示"}, "showAccount": {"message": "アカウントを表示"}, "showAdvancedDetails": {"message": "高度な詳細を表示"}, "showExtensionInFullSizeView": {"message": "拡張機能をフルサイズで表示"}, "showExtensionInFullSizeViewDescription": {"message": "この機能をオンにすると、拡張機能アイコンをクリックした時にフルサイズ表示がデフォルトになります。"}, "showFiatConversionInTestnets": {"message": "テストネット上に換算レートを表示"}, "showFiatConversionInTestnetsDescription": {"message": "これを選択すると、テストネット上に法定通貨の換算レートが表示されます"}, "showHexData": {"message": "16進データを表示"}, "showHexDataDescription": {"message": "これを選択すると、送金画面に16進データフィールドが表示されます"}, "showLess": {"message": "表示量を減らす"}, "showMore": {"message": "他を表示"}, "showNativeTokenAsMainBalance": {"message": "ネイティブトークンをメイン残高として表示"}, "showNft": {"message": "NFTを表示"}, "showPermissions": {"message": "表示許可"}, "showPrivateKey": {"message": "秘密鍵を表示"}, "showSRP": {"message": "シークレットリカバリーフレーズを表示"}, "showTestnetNetworks": {"message": "テストネットワークを表示"}, "showTestnetNetworksDescription": {"message": "ネットワークリストにテストネットワークを表示するには、こちらを選択してください"}, "sign": {"message": "署名"}, "signatureRequest": {"message": "署名のリクエスト"}, "signature_decoding_bid_nft_tooltip": {"message": "入札が受け入れられると、NFTがウォレットに反映されます。"}, "signature_decoding_list_nft_tooltip": {"message": "お手持ちのNFTが購入された場合のみ変更されます。"}, "signed": {"message": "署名が完了しました"}, "signing": {"message": "署名"}, "signingInWith": {"message": "サインイン方法:"}, "signingWith": {"message": "署名方法:"}, "simulationApproveHeading": {"message": "引き出し"}, "simulationDetailsApproveDesc": {"message": "別のユーザーに、アカウントからのNFTの引き出しを許可しようとしています。"}, "simulationDetailsERC20ApproveDesc": {"message": "別のユーザーに、アカウントからこの数量を使用する許可を与えようとしています。"}, "simulationDetailsFiatNotAvailable": {"message": "利用できません"}, "simulationDetailsIncomingHeading": {"message": "受取額"}, "simulationDetailsNoChanges": {"message": "変更なし"}, "simulationDetailsOutgoingHeading": {"message": "送金額"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "別のユーザーに付与したアカウントからのNFT引き出し許可を取り消そうとしています。"}, "simulationDetailsSetApprovalForAllDesc": {"message": "別のユーザーに、アカウントからのNFTの引き出しを許可しようとしています。"}, "simulationDetailsTitle": {"message": "予測される増減額"}, "simulationDetailsTitleTooltip": {"message": "予測される変化は、このトランザクションを実行すると起きる可能性がある変化です。これは単に予測に過ぎず、保証されたものではありません。"}, "simulationDetailsTotalFiat": {"message": "合計 = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "このトランザクションはおそらく失敗します"}, "simulationDetailsUnavailable": {"message": "利用不可"}, "simulationErrorMessageV2": {"message": "ガス代を見積もることができませんでした。コントラクトにエラーがある可能性があり、このトランザクションは失敗するかもしれません。"}, "simulationsSettingDescription": {"message": "トランザクションと署名を確定する前に残高の増減を予測するには、この機能をオンにします。これは最終的な結果を保証するものではありません。$1"}, "simulationsSettingSubHeader": {"message": "予測される残高の増減"}, "singleNetwork": {"message": "1つのネットワーク"}, "siweIssued": {"message": "発行済み"}, "siweNetwork": {"message": "ネットワーク"}, "siweRequestId": {"message": "リクエストID"}, "siweResources": {"message": "リソース"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "アカウントのセキュリティをスキップしますか？"}, "skipAccountSecurityDetails": {"message": "私は、シークレットリカバリーフレーズをバックアップするまで、アカウントとそのアセットのすべてを失う可能性があることを理解しています。"}, "slideBridgeDescription": {"message": "9つのチェーン間での移動がすべてウォレット内で"}, "slideBridgeTitle": {"message": "ブリッジの準備はいいですか？"}, "slideCashOutDescription": {"message": "仮想通貨を売ってキャッシュを入手"}, "slideCashOutTitle": {"message": "MetaMaskでキャッシュアウト"}, "slideDebitCardDescription": {"message": "一部地域で利用可能"}, "slideDebitCardTitle": {"message": "MetaMaskデビットカード"}, "slideFundWalletDescription": {"message": "トークンを追加または送金して開始します"}, "slideFundWalletTitle": {"message": "ウォレットに入金"}, "slideMultiSrpDescription": {"message": "MetaMaskで複数のウォレットをインポートして使用します"}, "slideMultiSrpTitle": {"message": "複数のシークレットリカバリーフレーズの追加"}, "slideRemoteModeDescription": {"message": "コールドウォレットを無線で使用します"}, "slideRemoteModeTitle": {"message": "コールドストレージ、高速アクセス"}, "slideSmartAccountUpgradeDescription": {"message": "同じアドレスでよりスマートな機能"}, "slideSmartAccountUpgradeTitle": {"message": "スマートアカウントの使用を開始"}, "slideSolanaDescription": {"message": "Solanaアカウントを作成して開始します"}, "slideSolanaTitle": {"message": "Solanaがサポートされるようになりました"}, "slideSweepStakeDescription": {"message": "今NFTをミントすると、次のものが当たるチャンスです:"}, "slideSweepStakeTitle": {"message": "$5000 USDCプレゼントにご応募ください！"}, "smartAccountAccept": {"message": "スマートアカウントを使用"}, "smartAccountBetterTransaction": {"message": "より高速なトランザクション、低い手数料"}, "smartAccountBetterTransactionDescription": {"message": "トランザクションをまとめて処理して時間とお金を節約します"}, "smartAccountFeaturesDescription": {"message": "アカウントアドレスはそのままで、いつでも元に戻せます。"}, "smartAccountLabel": {"message": "スマートアカウント"}, "smartAccountPayToken": {"message": "いつでもどのトークンでも支払い可能"}, "smartAccountPayTokenDescription": {"message": "すでに持っているトークンを使ってネットワーク手数料を支払えます。"}, "smartAccountReject": {"message": "スマートアカウントを使用しない"}, "smartAccountRequestFor": {"message": "要求の対象アカウント:"}, "smartAccountSameAccount": {"message": "同じアカウントでよりスマートな機能。"}, "smartAccountSplashTitle": {"message": "スマートアカウントを使用しますか？"}, "smartAccountUpgradeBannerDescription": {"message": "同じアドレスでよりスマートな機能。"}, "smartAccountUpgradeBannerTitle": {"message": "スマートアカウントに切り替える"}, "smartContracts": {"message": "スマートコントラクト"}, "smartSwapsErrorNotEnoughFunds": {"message": "スマートスワップに必要な資金が不足しています。"}, "smartSwapsErrorUnavailable": {"message": "スマートスワップは一時的にご利用いただけません。"}, "smartTransactionCancelled": {"message": "トランザクションがキャンセルされました"}, "smartTransactionCancelledDescription": {"message": "トランザクションを完了できなかったため、不要なガス代の支払いを避けるためにキャンセルされました。"}, "smartTransactionError": {"message": "トランザクションに失敗しました"}, "smartTransactionErrorDescription": {"message": "突然の市場の変化により失敗する場合があります。問題が解決されない場合は、MetaMaskカスタマーサポートにお問い合わせください。"}, "smartTransactionPending": {"message": "トランザクションが送信されました"}, "smartTransactionSuccess": {"message": "トランザクションが完了しました"}, "smartTransactions": {"message": "スマートトランザクション"}, "smartTransactionsEnabledDescription": {"message": " そしてMEV保護。デフォルトでオンになりました。"}, "smartTransactionsEnabledLink": {"message": "より高い成功率"}, "smartTransactionsEnabledTitle": {"message": "トランザクションがさらにスマートに"}, "snapAccountCreated": {"message": "アカウントが作成されました"}, "snapAccountCreatedDescription": {"message": "新しいアカウントの使用準備ができました！"}, "snapAccountCreationFailed": {"message": "アカウントの作成に失敗しました"}, "snapAccountCreationFailedDescription": {"message": "$1はアカウントを作成できませんでした。", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "署名を完了させる"}, "snapAccountRedirectSiteDescription": {"message": "$1の指示に従ってください"}, "snapAccountRemovalFailed": {"message": "アカウントの削除に失敗しました"}, "snapAccountRemovalFailedDescription": {"message": "$1がこのアカウントを削除できませんでした。", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "アカウントが削除されました"}, "snapAccountRemovedDescription": {"message": "このアカウントはMetaMaskで使用できなくなります。"}, "snapAccounts": {"message": "アカウントSnap"}, "snapAccountsDescription": {"message": "サードパーティSnapが制御するアカウント"}, "snapConnectTo": {"message": "$1に接続", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "$1によるユーザーの承認なしでの$2への自動接続を許可してください。", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1が$2の使用を求めています", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Webサイト"}, "snapHomeMenu": {"message": "Snapホームメニュー"}, "snapInstallRequest": {"message": "$1をインストールすると、次のアクセス許可が付与されます。", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "インストール完了"}, "snapInstallWarningCheck": {"message": "$1が次の許可を求めています:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "慎重に進めてください"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "$1が公開鍵 (およびアドレス) を表示することを許可します。これは、アカウントや資産のコントロールを許可するものでは一切ありません。", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "$1 Snapによる要求されたネットワークでのアカウントおよび資産の管理を許可します。これらのアカウントはシークレットリカバリーフレーズを (公開せずに) 使用して導出およびバックアップされます。キーを導出できることで、$1はイーサリアム (EVM) だけでなく、様々なブロックチェーンプロトコルをサポートできるようになります。", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "$1アカウントの管理", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "$1の公開鍵の表示", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1をインストールできませんでした。", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "インストールに失敗しました", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "エラー"}, "snapResultSuccess": {"message": "成功"}, "snapResultSuccessDescription": {"message": "$1を使用する準備が整いました"}, "snapUIAssetSelectorTitle": {"message": "資産の選択"}, "snapUpdateAlertDescription": {"message": "$1の最新バージョンを入手", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "アップデートが利用できます"}, "snapUpdateErrorDescription": {"message": "$1を更新できませんでした。", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "更新失敗", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "$1をアップデートすると、次のアクセス許可が付与されます。", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "更新完了"}, "snapUrlIsBlocked": {"message": "このSnapがブロックされたサイトに移動しようとしています。$1。"}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snapが接続されました"}, "snapsNoInsight": {"message": "表示するインサイトはありません"}, "snapsPrivacyWarningFirstMessage": {"message": "ユーザーは、別途特定されていない限り、インストールするSnapがConsensys$1で定義されているサードパーティサービスであることを認めたものとみなされます。サードパーティサービスの使用には、当該サードパーティサービスプロバイダーにより定められた、別の諸条件が適用されます。Consensysは、いかなる理由であっても、特定の人物によるSnapの使用を一切推奨しません。サードパーティサービスへのアクセス、依存、使用は、ユーザーの自己責任で行うものとします。Consensysは、サードパーティサービスの使用によりアカウントで発生する損失について、一切責任および賠償責任を負いません。", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "サードパーティサービスと共有する情報は、当該サードパーティサービスにより、それぞれのプライバシーポリシーに従い直接収集されます。詳細は、各サードパーティサービスのプライバシーポリシーをご覧ください。", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensysは、ユーザーがサードパーティと共有した情報に一切アクセスできません。", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Snapの設定"}, "snapsTermsOfUse": {"message": "利用規約"}, "snapsToggle": {"message": "snapは有効になっている場合にのみ実行されます"}, "snapsUIError": {"message": "今後のサポートは、$1の作成者にお問い合わせください。", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "このサイトがSolanaアカウントを求めています。"}, "solanaAccountRequired": {"message": "このサイトへの接続には、Solanaアカウントが必要です。"}, "solanaImportAccounts": {"message": "Solanaアカウントをインポート"}, "solanaImportAccountsDescription": {"message": "シークレットリカバリーフレーズをインポートして、他のウォレットからSolanaアカウントを移行します。"}, "solanaMoreFeaturesComingSoon": {"message": "他の機能も近日追加予定"}, "solanaMoreFeaturesComingSoonDescription": {"message": "NFT、ハードウェアウォレットのサポート、その他機能も近日追加予定。"}, "solanaOnMetaMask": {"message": "MetaMaskでSolana"}, "solanaSendReceiveSwapTokens": {"message": "トークンの送金、受取、スワップ"}, "solanaSendReceiveSwapTokensDescription": {"message": "SOLやUSDCなどのトークンでの送金・取引。"}, "someNetworks": {"message": "$1個のネットワーク"}, "somethingDoesntLookRight": {"message": "何か不審な点があれば、$1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "エラーが発生しました。ページを再度読み込んでみてください。"}, "somethingWentWrong": {"message": "このページを読み込めませんでした。"}, "sortBy": {"message": "並べ替え基準"}, "sortByAlphabetically": {"message": "アルファベット順 (A～Z)"}, "sortByDecliningBalance": {"message": "残高の多い順 ($1 高～低)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "ソース"}, "spamModalBlockedDescription": {"message": "このサイトは1分間ブロックされます"}, "spamModalBlockedTitle": {"message": "このサイトを一時的にブロックしました"}, "spamModalDescription": {"message": "複数の要求が行われ迷惑な場合は、一時的にサイトをブロックできます。"}, "spamModalTemporaryBlockButton": {"message": "このサイトを一時的にブロック"}, "spamModalTitle": {"message": "複数の要求が検出されました"}, "speed": {"message": "速度"}, "speedUp": {"message": "高速化"}, "speedUpCancellation": {"message": "このキャンセルを高速化"}, "speedUpExplanation": {"message": "現在のネットワーク状況に基づきガス代を更新し、10%以上 (ネットワークによる要件) 増額させました。"}, "speedUpPopoverTitle": {"message": "トランザクションを高速化"}, "speedUpTooltipText": {"message": "新しいガス代"}, "speedUpTransaction": {"message": "このトランザクションを高速化"}, "spendLimitInsufficient": {"message": "使用限度額が十分ではありません"}, "spendLimitInvalid": {"message": "使用限度額が無効です。正の数値を使用する必要があります"}, "spendLimitPermission": {"message": "使用限度額の許可"}, "spendLimitRequestedBy": {"message": "使用限度額が$1によりリクエストされました", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "使用限度額が大きすぎます"}, "spender": {"message": "使用者"}, "spenderTooltipDesc": {"message": "これは、NFTを引き出せるようになるアドレスです。"}, "spenderTooltipERC20ApproveDesc": {"message": "これが、トークンを代理で使用できるようになるアドレスです。"}, "spendingCap": {"message": "使用上限"}, "spendingCaps": {"message": "使用上限"}, "srpInputNumberOfWords": {"message": "$1語のフレーズがあります", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "シークレットリカバリーフレーズ$1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1件のアカウント", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "新しいアカウントの生成元となるシークレットリカバリーフレーズ"}, "srpListSingleOrZero": {"message": "$1件のアカウント", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "24を超える単語が含まれていたため、貼り付けに失敗しました。シークレットリカバリーフレーズは24語までです。", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "シークレットリカバリーフレーズ全体をいずれかのフィールドに張り付けできます。", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "開始"}, "srpSecurityQuizImgAlt": {"message": "目の中央に鍵穴があり、3つのパスワード入力欄がフローティング表示されている画像"}, "srpSecurityQuizIntroduction": {"message": "シークレットリカバリーフレーズを確認するには、2つの質問に正しく答える必要があります。"}, "srpSecurityQuizQuestionOneQuestion": {"message": "シークレットリカバリーフレーズをなくした場合、MetaMaskは..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "どうすることもできません"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "書き留めたり金属に掘ったり、いくつかの秘密の場所に保管したりして、絶対になくさないようにしてください。なくした場合、一生戻ってきません。"}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "正解です！シークレットリカバリーフレーズは誰にも取り戻すことができません"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "それを取り戻すことができます"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "シークレットリカバリーフレーズをなくした場合、一生戻ってきません。誰が何と言おうと、誰にも取り戻すことはできません。"}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "不正解！シークレットリカバリーフレーズは誰にも取り戻せません"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "誰かにシークレットリカバリーフレーズを尋ねられたら、それがサポート担当者であっても..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "あなたは騙されようとしています"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "シークレットリカバリーフレーズが必要だと言われたら、それは嘘です。教えてしまったら資産を盗まれます。"}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "正解です！シークレットリカバリーフレーズは決して誰にも教えてはいけません"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "教えるべきです"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "シークレットリカバリーフレーズが必要だと言われたら、それは嘘です。教えてしまったら資産を盗まれます。"}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "不正解！シークレットリカバリーフレーズは決して誰にも教えないでください"}, "srpSecurityQuizTitle": {"message": "セキュリティの質問"}, "srpToggleShow": {"message": "シークレットリカバリーフレーズのこの単語を表示・非表示", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "この単語は表示されません", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "この単語は表示されます", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "安定"}, "stableLowercase": {"message": "安定"}, "stake": {"message": "ステーク"}, "staked": {"message": "ステーキング済み"}, "standardAccountLabel": {"message": "スタンダードアカウント"}, "startEarning": {"message": "収益化を開始"}, "stateLogError": {"message": "ステートログの取得中にエラーが発生しました。"}, "stateLogFileName": {"message": "MetaMaskステートログ"}, "stateLogs": {"message": "ステートログ"}, "stateLogsDescription": {"message": "ステートログには、パブリックアカウントアドレスと送信済みトランザクションが含まれています。"}, "status": {"message": "ステータス"}, "statusNotConnected": {"message": "未接続"}, "statusNotConnectedAccount": {"message": "アカウントが接続されていません"}, "step1LatticeWallet": {"message": "Lattice1を接続する"}, "step1LatticeWalletMsg": {"message": "セットアップが完了しオンラインになると、MetaMaskをLattice1デバイスに接続できます。デバイスのロックを解除し、デバイスIDを準備してください。", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Ledgerアプリをダウンロード"}, "step1LedgerWalletMsg": {"message": "$1のロックを解除するには、ダウンロードして設定し、パスワードを入力してください。", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Trezorを接続する"}, "step1TrezorWalletMsg": {"message": "Trezorをコンピューターに直接接続し、ロックを解除します。 必ず正しいパスフレーズを使用してください。", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Ledgerを接続する"}, "step2LedgerWalletMsg": {"message": "コンピューターにLedgerを直接接続します。Ledgerのロックを解除し、イーサリアムアプリを開きます。", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "まだこのメッセージが表示されますか？"}, "strong": {"message": "強"}, "stxCancelled": {"message": "スワップが失敗するところでした"}, "stxCancelledDescription": {"message": "トランザクションが失敗しそうになり、不要なガス代の支払いを避けるためにキャンセルされました。"}, "stxCancelledSubDescription": {"message": "もう一度スワップをお試しください。次回は同様のリスクを避けられるようサポートします。"}, "stxFailure": {"message": "スワップに失敗しました"}, "stxFailureDescription": {"message": "突然の市場変動が失敗の原因になります。問題が解決されないようでしたら、$1にお問い合わせください。", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "スマートトランザクションをオンにして、サポートされているネットワーク上でのトランザクションの信頼性と安全性を高めましょう。$1"}, "stxPendingPrivatelySubmittingSwap": {"message": "スワップを非公開で送信中..."}, "stxPendingPubliclySubmittingSwap": {"message": "スワップを公開で送信中..."}, "stxSuccess": {"message": "スワップ完了！"}, "stxSuccessDescription": {"message": "$1が利用可能になりました。", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "スワップ完了まで残り <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "トランザクションのキャンセルを試みています..."}, "stxUnknown": {"message": "ステータス不明"}, "stxUnknownDescription": {"message": "トランザクションは成功しましたが、詳細がわかりません。このスワップの処理中に別のトランザクションが送信されたことが原因である可能性があります。"}, "stxUserCancelled": {"message": "スワップがキャンセルされました"}, "stxUserCancelledDescription": {"message": "不要なガス代を支払うことなくトランザクションがキャンセルされました。"}, "submit": {"message": "送信"}, "submitted": {"message": "送信済み"}, "suggestedBySnap": {"message": "$1による提案", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "推奨通貨シンボル。"}, "suggestedTokenName": {"message": "提案された名前:"}, "supplied": {"message": "預入済み"}, "support": {"message": "サポート"}, "supportCenter": {"message": "サポートセンターをご利用ください"}, "supportMultiRpcInformation": {"message": "1つのネットワークで複数のRPCがサポートされるようになりました。情報の矛盾を解決するため、最も最近のRPCがデフォルトのRPCとして選択されています。"}, "surveyConversion": {"message": "アンケートに回答する"}, "surveyTitle": {"message": "MetaMaskの未来を形作りましょう"}, "swap": {"message": "スワップ"}, "swapAdjustSlippage": {"message": "スリッページの調整"}, "swapAggregator": {"message": "アグリゲーター"}, "swapAllowSwappingOf": {"message": "$1のスワップを許可", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "保証額"}, "swapAmountReceivedInfo": {"message": "これは受け取る最低額です。スリッページによりそれ以上の額を受け取ることもあります。"}, "swapAndSend": {"message": "スワップして送金"}, "swapAnyway": {"message": "スワップを続ける"}, "swapApproval": {"message": "$1のスワップを承認", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "このスワップを完了させるには、さらに$1の$2が必要です。", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "まだご利用中ですか？"}, "swapAreYouStillThereDescription": {"message": "続ける際には、最新のクォートを表示する準備ができています"}, "swapConfirmWithHwWallet": {"message": "ハードウェアウォレットで確定"}, "swapContinueSwapping": {"message": "スワップを続ける"}, "swapContractDataDisabledErrorDescription": {"message": "Ledgerのイーサリアムアプリで「設定」に移動し、コントラクトデータを許可します。次に、スワップを再度試します。"}, "swapContractDataDisabledErrorTitle": {"message": "コントラクトデータがLedgerで無効です"}, "swapCustom": {"message": "カスタム"}, "swapDecentralizedExchange": {"message": "分散型取引所"}, "swapDirectContract": {"message": "ダイレクトコントラクト"}, "swapEditLimit": {"message": "限度額を編集"}, "swapEnableDescription": {"message": "これは必須であり、MetaMaskに$1をスワップする許可を付与します。", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "これはスワップ用に$1", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "金額を入力してください"}, "swapEstimatedNetworkFees": {"message": "推定ネットワーク手数料"}, "swapEstimatedNetworkFeesInfo": {"message": "これは、スワップを完了させるために使用されるネットワーク手数料の見積もりです。実際の額はネットワークの状態によって変化する可能性があります。"}, "swapFailedErrorDescriptionWithSupportLink": {"message": "トランザクション障害が発生した場合は、いつでもお手伝いいたします。この問題が解決しない場合は、$1でカスタマーサポートにお問い合わせください。", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "スワップに失敗しました"}, "swapFetchingQuote": {"message": "クォートを取得中"}, "swapFetchingQuoteNofN": {"message": "$2件中$1件の見積もりを取得中", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "クォートを取得中..."}, "swapFetchingQuotesErrorDescription": {"message": "問題が発生しました。もう一度実行してください。エラーが解消されない場合は、カスタマサポートにお問い合わせください。"}, "swapFetchingQuotesErrorTitle": {"message": "見積もり取得エラー"}, "swapFromTo": {"message": "$1から$2へのスワップ", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "ガス代は、ネットワークトラフィックとトランザクションの複雑さに基づき推定され、変動します。"}, "swapGasFeesExplanation": {"message": "MetaMaskはガス代から収益を得ません。これらの手数料は見積もりであり、ネットワークの混雑状況やトランザクションの複雑さによって変わる可能性があります。詳細は$1をご覧ください。", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "こちら", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "ガス代に関する詳細"}, "swapGasFeesSplit": {"message": "前の画面のガス代は、この2つのトランザクションに分けられています。"}, "swapGasFeesSummary": {"message": "ガス代は、$1ネットワークでトランザクションを処理するクリプトマイナーに支払われます。MetaMaskはガス代から利益を得ません。", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "このクォートは、やり取りするトークンの数量を調整し、ガス代込みで提示されています。アクティビティリストの別のトランザクションでETHを受け取る場合があります。"}, "swapGasIncludedTooltipExplanationLinkText": {"message": "ガス代に関する詳細"}, "swapHighSlippage": {"message": "高スリッページ"}, "swapIncludesGasAndMetaMaskFee": {"message": "ガス代と$1%のMetaMask手数料が含まれています", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "$1%のMetaMask手数料が含まれています。", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "クォートには$1%のMetaMask手数料が含まれています", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "$1%のMetaMask手数料が含まれています – $2", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Swapsの詳細"}, "swapLiquiditySourceInfo": {"message": "弊社は、為替レートとネットワーク手数料を比較するために、複数の流動性供給源 (取引所、アグリゲーター、専門のマーケットメーカー) を検索します。"}, "swapLowSlippage": {"message": "低スリッページ"}, "swapMaxSlippage": {"message": "最大スリッページ"}, "swapMetaMaskFee": {"message": "MetaMask手数料"}, "swapMetaMaskFeeDescription": {"message": "このクォートには、$1%の手数料が自動的に含まれています。この手数料は、MetaMaskの流動性プロバイダーの情報集積ソフトウェアの使用ライセンスの代金として支払うものです。", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1件の見積もり。", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "見積もりの更新まで $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "$1と一致するトークンがありません", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "このトランザクションの処理が完了すると、$1がアカウントに追加されます。", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "$1 $2 (～$3) を $4 $5 (～$6) にスワップしようとしています。", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "最大$1%の価格差", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "市場価格のデータが不足しているため、プライスインパクトを測定できませんでした。スワップする前に、これから受領するトークンの額に問題がないか確認してください。"}, "swapPriceUnavailableTitle": {"message": "続行する前にレートを確認してください"}, "swapProcessing": {"message": "処理中"}, "swapQuoteDetails": {"message": "見積もりの詳細"}, "swapQuoteNofM": {"message": "$1/$2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "見積もり提供元"}, "swapQuotesExpiredErrorDescription": {"message": "最新のレートを取得するには、新しい見積もりをリクエストしてください。"}, "swapQuotesExpiredErrorTitle": {"message": "見積もりのタイムアウト"}, "swapQuotesNotAvailableDescription": {"message": "この取引ルートは現在使用できません。金額、ネットワーク、またはトークンを変更すれば、最適なオプションをお探しします。"}, "swapQuotesNotAvailableErrorDescription": {"message": "額の調整またはスリッページの設定を試してから、もう一度実行してください。"}, "swapQuotesNotAvailableErrorTitle": {"message": "見積もりを取得できません"}, "swapRate": {"message": "レート"}, "swapReceiving": {"message": "受信中"}, "swapReceivingInfoTooltip": {"message": "これは推定値です。正確な額はスリッページによって異なります。"}, "swapRequestForQuotation": {"message": "見積もりのリクエスト"}, "swapSelect": {"message": "選択"}, "swapSelectAQuote": {"message": "見積もりを選択"}, "swapSelectAToken": {"message": "トークンを選択"}, "swapSelectQuotePopoverDescription": {"message": "以下は複数の流動性供給源から収集したすべての見積もりです。"}, "swapSelectToken": {"message": "トークンを選択"}, "swapShowLatestQuotes": {"message": "最新のクォートを表示"}, "swapSlippageHighDescription": {"message": "入力されたスリッページ ($1%) は非常に高いもののため、不利なレートに繋がる可能性があります", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "高スリッページ"}, "swapSlippageLowDescription": {"message": "値がこのように低い ($1%) と、スワップの失敗に繋がります", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "低スリッページ"}, "swapSlippageNegativeDescription": {"message": "スリッページは0以上でなければなりません"}, "swapSlippageNegativeTitle": {"message": "続けるにはスリッページを増やしてください"}, "swapSlippageOverLimitDescription": {"message": "スリッページの許容範囲は15%以下でなければなりません。それを超えると不利なレートになります。"}, "swapSlippageOverLimitTitle": {"message": "続けるにはスリッページを減らしてください"}, "swapSlippagePercent": {"message": "$1%", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "注文から確定までの間に価格が変動することを「スリッページ」といいます。スリッページが「スリッページ許容範囲」の設定を超えた場合、スワップは自動的にキャンセルされます。"}, "swapSlippageZeroDescription": {"message": "スリッページがゼロのプロバイダーは少ないため、不利なクォートになる可能性があります。"}, "swapSlippageZeroTitle": {"message": "スリッページがゼロのプロバイダーを使用中"}, "swapSource": {"message": "流動性の供給源"}, "swapSuggested": {"message": "スワップが提案されました"}, "swapSuggestedGasSettingToolTipMessage": {"message": "スワップは複雑で急を要するトランザクションです。コストとスワップの確実な成功のバランスが取れたこのガス代をお勧めします。"}, "swapSwapFrom": {"message": "スワップ元"}, "swapSwapSwitch": {"message": "トークンの切り替え"}, "swapSwapTo": {"message": "スワップ先"}, "swapToConfirmWithHwWallet": {"message": "ハードウェアウォレットで確定"}, "swapTokenAddedManuallyDescription": {"message": "このトークンを$1で検証して、取引したいトークンであることを確認してください。", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "トークンが手動で追加されました"}, "swapTokenAvailable": {"message": "$1がアカウントに追加されました。", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "$1の残高を取り戻すことができませんでした。", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "この地域ではトークンのスワップが行えません"}, "swapTokenToToken": {"message": "$1を$2にスワップ", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1は1つのソースでしか検証されていません。進める前に$2で検証することをご検討ください。", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "偽物のトークンの可能性"}, "swapTokenVerifiedSources": {"message": "$1個のソースで確定済み。$2で確認。", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1は小数点以下$2桁まで使用できます", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "トランザクションが完了しました"}, "swapTwoTransactions": {"message": "2つのトランザクション"}, "swapUnknown": {"message": "不明"}, "swapZeroSlippage": {"message": "0%スリッページ"}, "swapsMaxSlippage": {"message": "最大スリッページ"}, "swapsNotEnoughToken": {"message": "$1が不足しています", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "アクティビティに表示"}, "switch": {"message": "切り替える"}, "switchEthereumChainConfirmationDescription": {"message": "これによりMetaMask内で選択されたネットワークが、以前に追加されたものに切り替わります。"}, "switchEthereumChainConfirmationTitle": {"message": "このサイトによるネットワークの切り替えを許可しますか？"}, "switchInputCurrency": {"message": "通貨の変更"}, "switchNetwork": {"message": "ネットワークを切り替える"}, "switchNetworks": {"message": "ネットワークを切り替える"}, "switchToNetwork": {"message": "$1に切り替える", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "このアカウントに切り替える"}, "switchedNetworkToastDecline": {"message": "今後表示しない"}, "switchedNetworkToastMessage": {"message": "$1が$2で有効になりました", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "$1に切り替えました", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "ネットワークを切り替えると、保留中の承認がすべてキャンセルされます"}, "symbol": {"message": "シンボル"}, "symbolBetweenZeroTwelve": {"message": "シンボルは11文字以下にする必要があります。"}, "tenPercentIncreased": {"message": "10%の増加"}, "terms": {"message": "利用規約"}, "termsOfService": {"message": "サービス規約"}, "termsOfUseAgreeText": {"message": " MetaMaskおよびそのすべての機能の利用に適用される利用規約に同意します。"}, "termsOfUseFooterText": {"message": "スクロールしてすべてのセクションをお読みください"}, "termsOfUseTitle": {"message": "利用規約が更新されました"}, "testNetworks": {"message": "テストネットワーク"}, "testnets": {"message": "テストネット"}, "theme": {"message": "テーマ"}, "themeDescription": {"message": "ご希望のMetaMaskテーマを選択してください。"}, "thirdPartySoftware": {"message": "サードパーティソフトウェアに関する通知", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "時間"}, "tipsForUsingAWallet": {"message": "ウォレット使用のヒント"}, "tipsForUsingAWalletDescription": {"message": "トークンを追加すると、Web3の使用方法が増えます。"}, "to": {"message": "移動先"}, "toAddress": {"message": "移動先: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "当社は4byte.directoryとSourcifyサービスを使用してトランザクションデータを解読し、より読みやすい形で表示しています。これにより、保留中および過去のトランザクションの結果を理解しやすくなりますが、IPアドレスが共有される可能性があります。"}, "token": {"message": "トークン"}, "tokenAddress": {"message": "トークンアドレス"}, "tokenAlreadyAdded": {"message": "トークンの追加がすでに完了しています。"}, "tokenAutoDetection": {"message": "トークンの自動検出"}, "tokenContractAddress": {"message": "トークンコントラクトアドレス"}, "tokenDecimal": {"message": "トークンの小数桁数"}, "tokenDecimalFetchFailed": {"message": "トークンの小数点以下の桁数が必要です。確認はこちら: $1"}, "tokenDetails": {"message": "トークンの詳細"}, "tokenFoundTitle": {"message": "1 つの新しいトークンが見つかりました"}, "tokenId": {"message": "トークンID"}, "tokenList": {"message": "トークンリスト:"}, "tokenMarketplace": {"message": "トークンマーケットプレイス"}, "tokenScamSecurityRisk": {"message": "トークン関連の詐欺やセキュリティのリスク"}, "tokenStandard": {"message": "トークン規格"}, "tokenSymbol": {"message": "トークンシンボル"}, "tokens": {"message": "トークン"}, "tokensFoundTitle": {"message": "$1種類の新しいトークンが見つかりました", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "コレクションにあるトークン"}, "tooltipApproveButton": {"message": "理解しました"}, "tooltipSatusConnected": {"message": "接続済み"}, "tooltipSatusConnectedUpperCase": {"message": "接続済み"}, "tooltipSatusNotConnected": {"message": "未接続"}, "total": {"message": "合計"}, "totalVolume": {"message": "合計量"}, "transaction": {"message": "トランザクション"}, "transactionCancelAttempted": {"message": "$1のガス代が$2でトランザクションのキャンセルが試みられました"}, "transactionCancelSuccess": {"message": "$2でのトランザクションがキャンセルされました"}, "transactionConfirmed": {"message": "$2でトランザクションが承認されました。"}, "transactionCreated": {"message": "トランザクションは$1の値が$2で作成されました。"}, "transactionDataFunction": {"message": "関数"}, "transactionDetailGasHeading": {"message": "ガス代見積もり"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "金額 + 手数料"}, "transactionDropped": {"message": "トランザクションは$2で削除されました。"}, "transactionError": {"message": "トランザクションエラー。コントラクトコードで例外がスローされました。"}, "transactionErrorNoContract": {"message": "コントラクトではないアドレスに対して関数の呼び出しを試みています。"}, "transactionErrored": {"message": "トランザクションでエラーが発生しました。"}, "transactionFlowNetwork": {"message": "ネットワーク"}, "transactionHistoryBaseFee": {"message": "基本料金 (<PERSON>wei)"}, "transactionHistoryL1GasLabel": {"message": "L1ガス代合計"}, "transactionHistoryL2GasLimitLabel": {"message": "L2ガスリミット"}, "transactionHistoryL2GasPriceLabel": {"message": "L2ガス価格"}, "transactionHistoryMaxFeePerGas": {"message": "ガス1単位あたりの最大手数料"}, "transactionHistoryPriorityFee": {"message": "優先手数料 (gwei)"}, "transactionHistoryTotalGasFee": {"message": "ガス代合計"}, "transactionIdLabel": {"message": "トランザクションID", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "このトランザクションには$1が含まれています。"}, "transactionResubmitted": {"message": "推定のガス代を$2で$1に増加し、トランザクションを再送信しました"}, "transactionSettings": {"message": "トランザクション設定"}, "transactionSubmitted": {"message": "$1の推定ガス代が$2でトランザクションが送信されました。"}, "transactionTotalGasFee": {"message": "ガス代合計", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "トランザクションが$2で更新されました。"}, "transactions": {"message": "トランザクション"}, "transfer": {"message": "送金"}, "transferCrypto": {"message": "仮想通貨の送金"}, "transferFrom": {"message": "送金元"}, "transferRequest": {"message": "送金要求"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Ledgerの接続に問題が発生しました。$1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "ハードウェアウォレットの接続ガイドを確認し、もう一度お試しください。", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Firefoxの最新バージョンを使用している場合、FirefoxのU2Fサポート廃止に関連した問題が発生する可能性があります。$1でこの問題を解決する方法をご覧ください。", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "こちら", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "$1に接続できませんでした。$2を確認してから、もう一度実行してください。", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "MetaMaskがうまく起動しませんでした。このエラーは断続的に発生する可能性があるため、拡張機能を再起動してみてください。"}, "tryAgain": {"message": "再試行"}, "turnOff": {"message": "オフにする"}, "turnOffMetamaskNotificationsError": {"message": "通知を無効化する際にエラーが発生しました。後でもう一度お試しください。"}, "turnOn": {"message": "オンにする"}, "turnOnMetamaskNotifications": {"message": "通知をオンにする"}, "turnOnMetamaskNotificationsButton": {"message": "オンにする"}, "turnOnMetamaskNotificationsError": {"message": "通知の作成中にエラーが発生しました。後でもう一度お試しください。"}, "turnOnMetamaskNotificationsMessageFirst": {"message": "通知を使えば、ウォレットで何が起きているか常に把握できます。"}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "通知設定。"}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "この機能を使用する際に当社がどのようにユーザーのプライバシーを保護するのか、ご覧ください。"}, "turnOnMetamaskNotificationsMessageSecond": {"message": "ウォレット通知を使用するために、プロファイルを使用して一部の設定をデバイス間で同期します。$1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "通知は$1でいつでもオフにできます"}, "turnOnTokenDetection": {"message": "強化されたトークン検出をオンにする"}, "tutorial": {"message": "チュートリアル"}, "twelveHrTitle": {"message": "12時間:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "未承認"}, "unexpectedBehavior": {"message": "これは想定外の動作であるため、アカウントが適切に復元された場合も、バグとして報告する必要があります。下のリンクを使用して、MetaMaskにバグの報告を送信してください。"}, "units": {"message": "単位"}, "unknown": {"message": "不明"}, "unknownCollection": {"message": "無名のコレクション"}, "unknownNetworkForKeyEntropy": {"message": "不明なネットワーク", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "エラー: QRコードを識別できませんでした"}, "unlimited": {"message": "無制限"}, "unlock": {"message": "ロック解除"}, "unpin": {"message": "ピン留めを解除"}, "unrecognizedChain": {"message": "このカスタムネットワークは認識されていません", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "NFT (ERC-721) トークンの送信は現在サポートされていません", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "このトークンのUSDでの価格は非常に不安定で、やり取りすると高額を失う危険性が高いです。"}, "unstableTokenPriceTitle": {"message": "トークン価格が不安定です"}, "upArrow": {"message": "上矢印"}, "update": {"message": "更新"}, "updateEthereumChainConfirmationDescription": {"message": "このサイトがデフォルトのネットワークURLの更新を要求しています。デフォルトとネットワーク情報はいつでも編集できます。"}, "updateNetworkConfirmationTitle": {"message": "$1を更新", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "情報を更新するか"}, "updateRequest": {"message": "更新リクエスト"}, "updatedRpcForNetworks": {"message": "ネットワークRPCが更新されました"}, "uploadDropFile": {"message": "ここにファイルをドロップします"}, "uploadFile": {"message": "ファイルをアップロード"}, "urlErrorMsg": {"message": "URLには適切なHTTP/HTTPSプレフィックスが必要です。"}, "use4ByteResolution": {"message": "スマートコントラクトのデコード"}, "useMultiAccountBalanceChecker": {"message": "アカウント残高の一括リクエスト"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "アカウントの残高リクエストを一斉に行うことで、より素早く残高更新を取得できます。これにより、アカウントの残高を一斉に取得できるので、更新がより迅速になり、エクスペリエンスが向上します。この機能がオフの場合、サードパーティがユーザーのアカウントをお互いに関連付けなくなる可能性があります。"}, "useNftDetection": {"message": "NFTを自動検出"}, "useNftDetectionDescriptionText": {"message": "MetaMaskが、サードパーティサービス を使って、ユーザーが所有するNFTを追加することを許可します。NFTの自動検出機能を利用すると、ユーザーのIPとアカウントアドレスがこれらのサービスに公開されます。この機能を有効にした場合、ユーザーのIPアドレスとイーサリアムアドレスが紐付けられ、詐欺師によりエアドロップされた偽のNFTが表示される可能性があります。このリスクを回避するため、手動でトークンを追加することもできます。"}, "usePhishingDetection": {"message": "フィッシング検出を使用"}, "usePhishingDetectionDescription": {"message": "イーサリアムユーザーを対象としたドメインのフィッシングに対して警告を表示します"}, "useSafeChainsListValidation": {"message": "ネットワーク情報の確認"}, "useSafeChainsListValidationDescription": {"message": "MetaMaskは$1というサードパーティサービスを利用して、正確かつ標準化されたネットワーク情報を表示しています。これにより、悪質なネットワークや正しくないネットワークに接続してしまう可能性が減ります。この機能を使用すると、ユーザーのIPアドレスがchainid.networkに公開されます。"}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "アカウントに送られたトークンを自動的に表示するには、サードパーティサーバーと通信し、トークンの画像を取得する必要があります。これらのサーバーはユーザーのIPアドレスにアクセスできます。"}, "usedByClients": {"message": "さまざまな異なるクライアントによって使用されています"}, "userName": {"message": "ユーザー名"}, "userOpContractDeployError": {"message": "スマートコントラクトアカウントからのコントラクトの展開はサポートされていません"}, "version": {"message": "バージョン"}, "view": {"message": "表示"}, "viewActivity": {"message": "アクティビティを表示"}, "viewAllQuotes": {"message": "すべてのクォートを表示"}, "viewContact": {"message": "連絡先を表示"}, "viewDetails": {"message": "詳細を表示"}, "viewMore": {"message": "詳細を表示"}, "viewOnBlockExplorer": {"message": "ブロックエクスプローラーで表示"}, "viewOnCustomBlockExplorer": {"message": "$1を$2で表示", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "$1をEtherscanで表示", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "エクスプローラーで表示"}, "viewOnOpensea": {"message": "Openseaで表示"}, "viewSolanaAccount": {"message": "Solanaアカウントを表示"}, "viewTransaction": {"message": "トランザクションを表示"}, "viewinExplorer": {"message": "$1をエクスプローラーで表示", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "サイトにアクセス"}, "visitSupportDataConsentModalAccept": {"message": "確定"}, "visitSupportDataConsentModalDescription": {"message": "MetaMaskの識別子とアプリのバージョンをサポートセンターと共有しますか？これにより問題を解決しやすくなりますが、あくまでもオプションです。"}, "visitSupportDataConsentModalReject": {"message": "共有しない"}, "visitSupportDataConsentModalTitle": {"message": "デバイス情報をサポートと共有する"}, "visitWebSite": {"message": "弊社Webサイトにアクセス"}, "wallet": {"message": "ウォレット"}, "walletConnectionGuide": {"message": "弊社のハードウェアウォレット接続ガイド"}, "wantToAddThisNetwork": {"message": "このネットワークを追加しますか？"}, "wantsToAddThisAsset": {"message": "$1がこのアセットのウォレットへの追加を要求しています"}, "warning": {"message": "警告"}, "warningFromSnap": {"message": "$1からの警告", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "このオプションをオンにすると、パブリックアドレスまたはENS名でイーサリアムアカウントを監視できるようになります。ベータ機能に関するフィードバックは、こちらの$1に入力してください。", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "イーサリアムアカウントの監視 (ベータ)"}, "watchOutMessage": {"message": "$1にご注意ください。", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "弱"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "現在のWebサイトが、削除済みのwindow.web3 APIの使用を検知しました。サイトが破損しているようであれば、$1をクリックして詳細を確認してください。", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "Webサイト", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "お帰りなさい"}, "welcomeToMetaMask": {"message": "始めましょう"}, "whatsThis": {"message": "これは何ですか？"}, "willApproveAmountForBridging": {"message": "これによりブリッジ用に$1が承認されます。"}, "willApproveAmountForBridgingHardware": {"message": "ハードウェアウォレットで2つのトランザクションを確定する必要があります。"}, "withdrawing": {"message": "引き出し中"}, "wrongNetworkName": {"message": "弊社の記録によると、ネットワーク名がこのチェーンIDと正しく一致していない可能性があります。"}, "yes": {"message": "はい"}, "you": {"message": "ユーザー"}, "youDeclinedTheTransaction": {"message": "トランザクションを拒否しました。"}, "youNeedToAllowCameraAccess": {"message": "この機能を使用するには、カメラへのアクセスを許可する必要があります。"}, "youReceived": {"message": "受取額:", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "送金額:", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "アカウント"}, "yourActivity": {"message": "アクティビティ"}, "yourBalance": {"message": "残高"}, "yourNFTmayBeAtRisk": {"message": "NFTが危険にさらされている可能性があります"}, "yourNetworks": {"message": "あなたのネットワーク"}, "yourPrivateSeedPhrase": {"message": "シークレットリカバリーフレーズ"}, "yourTransactionConfirmed": {"message": "トランザクションはすでに確定済みです"}, "yourTransactionJustConfirmed": {"message": "ブロックチェーン上で確定しているため、トランザクションをキャンセルできませんでした。"}, "yourWalletIsReady": {"message": "ウォレットの準備ができました"}}