{"QRHardwareSignRequestCancel": {"message": "Odmítnout"}, "account": {"message": "Účet"}, "accountDetails": {"message": "Detaily účtu"}, "accountName": {"message": "Název <PERSON>čtu"}, "addToken": {"message": "<PERSON><PERSON><PERSON><PERSON> token"}, "amount": {"message": "Částka"}, "appDescription": {"message": "Ethereum rozšíření prohlížeče", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "Schv<PERSON><PERSON>"}, "approved": {"message": "Schv<PERSON>len<PERSON>"}, "attributions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "back": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "balance": {"message": "Zůstatek:"}, "cancel": {"message": "Zrušit"}, "confirm": {"message": "Potvrdit"}, "confirmPassword": {"message": "Potvrdit heslo"}, "confirmed": {"message": "Potvrzeno"}, "connectingToMainnet": {"message": "Připojuji se k Ethereum Mainnet"}, "contractDeployment": {"message": "Nasazení k<PERSON>"}, "copiedExclamation": {"message": "Zkopírováno!"}, "copyToClipboard": {"message": "Kopírovat do schránky"}, "create": {"message": "Vytvořit"}, "customToken": {"message": "Vlastní token"}, "decimal": {"message": "Počet desetinných míst přesnosti"}, "decimalsMustZerotoTen": {"message": "Desetinných míst musí být od 0 do 36."}, "details": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "done": {"message": "Hotovo"}, "downloadStateLogs": {"message": "St<PERSON><PERSON>out stavové protokoly"}, "dropped": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "etherscanView": {"message": "Prohlédněte si účet na Etherscan"}, "failed": {"message": "Neúspěšné"}, "fileImportFail": {"message": "Import souboru nefunguje? Klikněte sem!", "description": "Helps user import their account from a JSON file"}, "from": {"message": "Od"}, "gasLimit": {"message": "<PERSON>it paliva"}, "gasLimitTooLow": {"message": "Limit paliva musí být alespoň 21000"}, "here": {"message": "zde", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hide": {"message": "Skr<PERSON><PERSON>"}, "hideTokenPrompt": {"message": "Skrýt token?"}, "importAccountMsg": {"message": "Importované účty nebudou spojeny s vaší původní MetaMaskovou klíčovou frází. Zjistěte více o importovaných účtech "}, "imported": {"message": "I<PERSON>rt<PERSON><PERSON>", "description": "status showing that an account has been fully loaded into the keyring"}, "insufficientFunds": {"message": "Nedostatek finančních prostředků."}, "insufficientTokens": {"message": "Nedostatek tokenů."}, "invalidAddress": {"message": "Neplatná adresa"}, "invalidAddressRecipient": {"message": "Adresa příjemce je neplatná"}, "invalidRPC": {"message": "Neplatné RPC URI"}, "jsonFile": {"message": "JSON soubor", "description": "format for importing an account"}, "learnMore": {"message": "Zjistěte více."}, "learnMoreUpperCase": {"message": "Zjistěte více."}, "likeToImportTokens": {"message": "Chcete přidat tyto tokeny?"}, "links": {"message": "Od<PERSON><PERSON>"}, "loading": {"message": "Načítám..."}, "lock": {"message": "Odhlásit"}, "message": {"message": "Zpráva"}, "mustSelectOne": {"message": "Musíte zvolit aspoň 1 token."}, "needImportFile": {"message": "Musíte zvolit soubor k <PERSON>u.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Nelze odeslat zápornou částku ETH."}, "networks": {"message": "Sítě"}, "newAccount": {"message": "Nový účet"}, "newAccountNumberName": {"message": "Účet $1", "description": "Default name of next account to be created on create account screen"}, "newContract": {"message": "Nový kontrakt"}, "newPassword": {"message": "<PERSON><PERSON> (min 8 znaků)"}, "next": {"message": "Dalš<PERSON>"}, "passwordNotLongEnough": {"message": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON> dost d<PERSON>"}, "passwordsDontMatch": {"message": "<PERSON><PERSON>"}, "pastePrivateKey": {"message": "Vložte zde svůj privátní klíč:", "description": "For importing an account from a private key"}, "personalAddressDetected": {"message": "Detekována osobní adresa. Zadejte adresu kontraktu tokenu."}, "privacyMsg": {"message": "<PERSON><PERSON><PERSON> o<PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privateKey": {"message": "Privátní klíč", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Upozornění: <PERSON><PERSON>ěte tento klíč. Kdokoli může s vaším privátním klíčem odcizit vaše aktiva z účtu."}, "privateNetwork": {"message": "Soukromá síť"}, "readdToken": {"message": "Tento token můžete v budoucnu přidat zpět s „Přidat token“ v nastavení účtu."}, "reject": {"message": "Odmítnout"}, "rejected": {"message": "Odmí<PERSON>nut<PERSON>"}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "Zobrazit slova klíčové fráze"}, "revealSeedWordsWarning": {"message": "Nebnovujte slova klíčové fráze na veřejnosti! Tato slova mohou být použita k odcizení veškerých v<PERSON><PERSON><PERSON>.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "save": {"message": "Uložit"}, "search": {"message": "Hledat"}, "seedPhraseReq": {"message": "klíčové fráze mají 12 slov"}, "selectType": {"message": "<PERSON><PERSON><PERSON><PERSON> typ"}, "send": {"message": "<PERSON><PERSON><PERSON>"}, "settings": {"message": "Nastavení"}, "sign": {"message": "Podepsat"}, "signatureRequest": {"message": "Požadavek podpisu"}, "signed": {"message": "Podepsáno"}, "stateLogError": {"message": "Chyba během získávání stavových protokolů."}, "stateLogs": {"message": "Stavové protokoly"}, "stateLogsDescription": {"message": "Stavové protokoly obsahují vaše veřejné adresy úč<PERSON>ů a odeslané transakce."}, "submitted": {"message": "Odesláno"}, "supportCenter": {"message": "Navštivte naše centrum podpory"}, "symbolBetweenZeroTwelve": {"message": "Symbol musí mít 11 nebo méně znaků."}, "terms": {"message": "Podmínky použití"}, "to": {"message": "Komu"}, "tokenAlreadyAdded": {"message": "Token byl už p<PERSON>."}, "tokenSymbol": {"message": "Symbol tokenu"}, "total": {"message": "<PERSON><PERSON><PERSON>"}, "transactionError": {"message": "Chyba transakce. Vyhozena výjimka v kódu kontraktu."}, "unapproved": {"message": "Neschváleno"}, "unknown": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"message": "Přihlásit"}, "urlErrorMsg": {"message": "URI vyžadují korektní HTTP/HTTPS prefix."}, "usedByClients": {"message": "Používána různými klienty"}, "visitWebSite": {"message": "Navštivte naši stránku"}}