(functors=>options=>{"use strict";const{Map:Map,Object:Object,ReferenceError:ReferenceError,Reflect:Reflect,TypeError:TypeError}=globalThis,{create:create,defineProperties:defineProperties,defineProperty:defineProperty,freeze:freeze,fromEntries:fromEntries,getOwnPropertyDescriptors:getOwnPropertyDescriptors,getOwnPropertyNames:getOwnPropertyNames,keys:keys}=Object,{get:get,set:set}=Reflect,{}=options||{},cell=(name,value=undefined)=>{const observers=[];return freeze({get:freeze((()=>value)),set:freeze((newValue=>{value=newValue;for(const observe of observers)observe(value)})),observe:freeze((observe=>{observers.push(observe),observe(value)})),enumerable:!0})},cells=[{globalThis:cell(),Array:cell(),ArrayBuffer:cell(),Date:cell(),FinalizationRegistry:cell(),Float32Array:cell(),JSON:cell(),Map:cell(),Math:cell(),Number:cell(),Object:cell(),Promise:cell(),Proxy:cell(),Reflect:cell(),FERAL_REG_EXP:cell(),Set:cell(),String:cell(),Symbol:cell(),Uint8Array:cell(),WeakMap:cell(),WeakSet:cell(),FERAL_ERROR:cell(),RangeError:cell(),ReferenceError:cell(),SyntaxError:cell(),TypeError:cell(),AggregateError:cell(),assign:cell(),create:cell(),defineProperties:cell(),entries:cell(),freeze:cell(),getOwnPropertyDescriptor:cell(),getOwnPropertyDescriptors:cell(),getOwnPropertyNames:cell(),getPrototypeOf:cell(),is:cell(),isFrozen:cell(),isSealed:cell(),isExtensible:cell(),keys:cell(),objectPrototype:cell(),seal:cell(),preventExtensions:cell(),setPrototypeOf:cell(),values:cell(),fromEntries:cell(),speciesSymbol:cell(),toStringTagSymbol:cell(),iteratorSymbol:cell(),matchAllSymbol:cell(),unscopablesSymbol:cell(),symbolKeyFor:cell(),symbolFor:cell(),isInteger:cell(),stringifyJson:cell(),defineProperty:cell(),apply:cell(),construct:cell(),reflectGet:cell(),reflectGetOwnPropertyDescriptor:cell(),reflectHas:cell(),reflectIsExtensible:cell(),ownKeys:cell(),reflectPreventExtensions:cell(),reflectSet:cell(),isArray:cell(),arrayPrototype:cell(),arrayBufferPrototype:cell(),mapPrototype:cell(),proxyRevocable:cell(),regexpPrototype:cell(),setPrototype:cell(),stringPrototype:cell(),weakmapPrototype:cell(),weaksetPrototype:cell(),functionPrototype:cell(),promisePrototype:cell(),generatorPrototype:cell(),iteratorPrototype:cell(),typedArrayPrototype:cell(),uncurryThis:cell(),objectHasOwnProperty:cell(),arrayFilter:cell(),arrayForEach:cell(),arrayIncludes:cell(),arrayJoin:cell(),arrayMap:cell(),arrayFlatMap:cell(),arrayPop:cell(),arrayPush:cell(),arraySlice:cell(),arraySome:cell(),arraySort:cell(),iterateArray:cell(),arrayBufferSlice:cell(),arrayBufferGetByteLength:cell(),typedArraySet:cell(),mapSet:cell(),mapGet:cell(),mapHas:cell(),mapDelete:cell(),mapEntries:cell(),iterateMap:cell(),setAdd:cell(),setDelete:cell(),setForEach:cell(),setHas:cell(),iterateSet:cell(),regexpTest:cell(),regexpExec:cell(),matchAllRegExp:cell(),stringEndsWith:cell(),stringIncludes:cell(),stringIndexOf:cell(),stringMatch:cell(),generatorNext:cell(),generatorThrow:cell(),stringReplace:cell(),stringSearch:cell(),stringSlice:cell(),stringSplit:cell(),stringStartsWith:cell(),iterateString:cell(),weakmapDelete:cell(),weakmapGet:cell(),weakmapHas:cell(),weakmapSet:cell(),weaksetAdd:cell(),weaksetHas:cell(),functionToString:cell(),functionBind:cell(),promiseAll:cell(),promiseCatch:cell(),promiseThen:cell(),finalizationRegistryRegister:cell(),finalizationRegistryUnregister:cell(),getConstructorOf:cell(),isObject:cell(),isError:cell(),identity:cell(),FERAL_EVAL:cell(),FERAL_FUNCTION:cell(),noEvalEvaluate:cell(),FERAL_STACK_GETTER:cell(),FERAL_STACK_SETTER:cell(),AsyncGeneratorFunctionInstance:cell()},{},{makeEnvironmentCaptor:cell(),getEnvironmentOption:cell(),getEnvironmentOptionsList:cell(),environmentOptionsListHas:cell()},{},{transferBufferToImmutable:cell(),isBufferImmutable:cell(),sliceBufferToImmutable:cell()},{},{an:cell(),bestEffortStringify:cell(),enJoin:cell()},{},{},{makeLRUCacheMap:cell()},{makeNoteLogArgsArrayKit:cell()},{q:cell(),b:cell(),X:cell(),unredactedDetails:cell(),makeError:cell(),annotateError:cell(),loggedErrorHandler:cell(),makeAssert:cell(),assert:cell(),assertEqual:cell(),sanitizeError:cell()},{isTypedArray:cell(),makeHardener:cell()},{cauterizeProperty:cell()},{NativeErrors:cell(),constantProperties:cell(),universalPropertyNames:cell(),initialGlobalPropertyNames:cell(),sharedGlobalPropertyNames:cell(),uniqueGlobalPropertyNames:cell(),FunctionInstance:cell(),AsyncFunctionInstance:cell(),isAccessorPermit:cell(),permitted:cell()},{makeIntrinsicsCollector:cell(),getGlobalIntrinsics:cell()},{default:cell()},{default:cell()},{default:cell()},{default:cell()},{default:cell()},{minEnablements:cell(),moderateEnablements:cell(),severeEnablements:cell()},{default:cell()},{default:cell()},{makeEvalFunction:cell()},{makeFunctionConstructor:cell()},{setGlobalObjectSymbolUnscopables:cell(),setGlobalObjectConstantProperties:cell(),setGlobalObjectMutableProperties:cell(),setGlobalObjectEvaluators:cell()},{alwaysThrowHandler:cell(),strictScopeTerminatorHandler:cell(),strictScopeTerminator:cell()},{createSloppyGlobalsScopeTerminator:cell()},{makeEvalScopeKit:cell()},{getSourceURL:cell()},{rejectHtmlComments:cell(),evadeHtmlCommentTest:cell(),rejectImportExpressions:cell(),evadeImportExpressionTest:cell(),rejectSomeDirectEvalExpressions:cell(),mandatoryTransforms:cell(),applyTransforms:cell(),transforms:cell()},{isValidIdentifierName:cell(),getScopeConstants:cell()},{makeEvaluate:cell()},{makeSafeEvaluator:cell()},{tameFunctionToString:cell()},{tameDomains:cell()},{tameModuleSource:cell()},{consoleLevelMethods:cell(),consoleOtherMethods:cell(),makeLoggingConsoleKit:cell(),pumpLogToConsole:cell(),makeCausalConsole:cell(),defineCausalConsoleFromLogger:cell(),filterConsole:cell()},{makeRejectionHandlers:cell()},{tameConsole:cell()},{filterFileName:cell(),shortenCallSiteString:cell(),tameV8ErrorConstructor:cell()},{default:cell()},{makeAlias:cell(),load:cell(),loadNow:cell()},{deferExports:cell(),getDeferredExports:cell()},{provideCompartmentEvaluator:cell(),compartmentEvaluate:cell()},{makeVirtualModuleInstance:cell(),makeModuleInstance:cell()},{link:cell(),instantiate:cell()},{InertCompartment:cell(),CompartmentPrototype:cell(),compartmentOptions:cell(),makeCompartmentConstructor:cell()},{getAnonymousIntrinsics:cell()},{tameHarden:cell()},{tameSymbolConstructor:cell()},{tameFauxDataProperty:cell(),tameFauxDataProperties:cell()},{tameRegeneratorRuntime:cell()},{shimArrayBufferTransfer:cell()},{chooseReporter:cell(),reportInGroup:cell()},{repairIntrinsics:cell()},{},{},{},{},{}];defineProperties(cells[3],getOwnPropertyDescriptors(cells[2]));const namespaces=cells.map((cells=>freeze(create(null,{...cells,[Symbol.toStringTag]:{value:"Module",writable:!1,enumerable:!1,configurable:!1}}))));for(let index=0;index<namespaces.length;index+=1)cells[index]["*"]=cell(0,namespaces[index]);function observeImports(map,importName,importIndex){for(const[name,observers]of map.get(importName)){const cell=cells[importIndex][name];if(void 0===cell)throw new ReferenceError(`Cannot import name ${name} (has ${Object.getOwnPropertyNames(cells[importIndex]).join(", ")})`);for(const observer of observers)cell.observe(observer)}}return functors[0]({imports(entries){new Map(entries)},liveVar:{},onceVar:{universalThis:cells[0].globalThis.set,Array:cells[0].Array.set,ArrayBuffer:cells[0].ArrayBuffer.set,Date:cells[0].Date.set,FinalizationRegistry:cells[0].FinalizationRegistry.set,Float32Array:cells[0].Float32Array.set,JSON:cells[0].JSON.set,Map:cells[0].Map.set,Math:cells[0].Math.set,Number:cells[0].Number.set,Object:cells[0].Object.set,Promise:cells[0].Promise.set,Proxy:cells[0].Proxy.set,Reflect:cells[0].Reflect.set,FERAL_REG_EXP:cells[0].FERAL_REG_EXP.set,Set:cells[0].Set.set,String:cells[0].String.set,Symbol:cells[0].Symbol.set,Uint8Array:cells[0].Uint8Array.set,WeakMap:cells[0].WeakMap.set,WeakSet:cells[0].WeakSet.set,FERAL_ERROR:cells[0].FERAL_ERROR.set,RangeError:cells[0].RangeError.set,ReferenceError:cells[0].ReferenceError.set,SyntaxError:cells[0].SyntaxError.set,TypeError:cells[0].TypeError.set,AggregateError:cells[0].AggregateError.set,assign:cells[0].assign.set,create:cells[0].create.set,defineProperties:cells[0].defineProperties.set,entries:cells[0].entries.set,freeze:cells[0].freeze.set,getOwnPropertyDescriptor:cells[0].getOwnPropertyDescriptor.set,getOwnPropertyDescriptors:cells[0].getOwnPropertyDescriptors.set,getOwnPropertyNames:cells[0].getOwnPropertyNames.set,getPrototypeOf:cells[0].getPrototypeOf.set,is:cells[0].is.set,isFrozen:cells[0].isFrozen.set,isSealed:cells[0].isSealed.set,isExtensible:cells[0].isExtensible.set,keys:cells[0].keys.set,objectPrototype:cells[0].objectPrototype.set,seal:cells[0].seal.set,preventExtensions:cells[0].preventExtensions.set,setPrototypeOf:cells[0].setPrototypeOf.set,values:cells[0].values.set,fromEntries:cells[0].fromEntries.set,speciesSymbol:cells[0].speciesSymbol.set,toStringTagSymbol:cells[0].toStringTagSymbol.set,iteratorSymbol:cells[0].iteratorSymbol.set,matchAllSymbol:cells[0].matchAllSymbol.set,unscopablesSymbol:cells[0].unscopablesSymbol.set,symbolKeyFor:cells[0].symbolKeyFor.set,symbolFor:cells[0].symbolFor.set,isInteger:cells[0].isInteger.set,stringifyJson:cells[0].stringifyJson.set,defineProperty:cells[0].defineProperty.set,apply:cells[0].apply.set,construct:cells[0].construct.set,reflectGet:cells[0].reflectGet.set,reflectGetOwnPropertyDescriptor:cells[0].reflectGetOwnPropertyDescriptor.set,reflectHas:cells[0].reflectHas.set,reflectIsExtensible:cells[0].reflectIsExtensible.set,ownKeys:cells[0].ownKeys.set,reflectPreventExtensions:cells[0].reflectPreventExtensions.set,reflectSet:cells[0].reflectSet.set,isArray:cells[0].isArray.set,arrayPrototype:cells[0].arrayPrototype.set,arrayBufferPrototype:cells[0].arrayBufferPrototype.set,mapPrototype:cells[0].mapPrototype.set,proxyRevocable:cells[0].proxyRevocable.set,regexpPrototype:cells[0].regexpPrototype.set,setPrototype:cells[0].setPrototype.set,stringPrototype:cells[0].stringPrototype.set,weakmapPrototype:cells[0].weakmapPrototype.set,weaksetPrototype:cells[0].weaksetPrototype.set,functionPrototype:cells[0].functionPrototype.set,promisePrototype:cells[0].promisePrototype.set,generatorPrototype:cells[0].generatorPrototype.set,iteratorPrototype:cells[0].iteratorPrototype.set,typedArrayPrototype:cells[0].typedArrayPrototype.set,uncurryThis:cells[0].uncurryThis.set,objectHasOwnProperty:cells[0].objectHasOwnProperty.set,arrayFilter:cells[0].arrayFilter.set,arrayForEach:cells[0].arrayForEach.set,arrayIncludes:cells[0].arrayIncludes.set,arrayJoin:cells[0].arrayJoin.set,arrayMap:cells[0].arrayMap.set,arrayFlatMap:cells[0].arrayFlatMap.set,arrayPop:cells[0].arrayPop.set,arrayPush:cells[0].arrayPush.set,arraySlice:cells[0].arraySlice.set,arraySome:cells[0].arraySome.set,arraySort:cells[0].arraySort.set,iterateArray:cells[0].iterateArray.set,arrayBufferSlice:cells[0].arrayBufferSlice.set,arrayBufferGetByteLength:cells[0].arrayBufferGetByteLength.set,typedArraySet:cells[0].typedArraySet.set,mapSet:cells[0].mapSet.set,mapGet:cells[0].mapGet.set,mapHas:cells[0].mapHas.set,mapDelete:cells[0].mapDelete.set,mapEntries:cells[0].mapEntries.set,iterateMap:cells[0].iterateMap.set,setAdd:cells[0].setAdd.set,setDelete:cells[0].setDelete.set,setForEach:cells[0].setForEach.set,setHas:cells[0].setHas.set,iterateSet:cells[0].iterateSet.set,regexpTest:cells[0].regexpTest.set,regexpExec:cells[0].regexpExec.set,matchAllRegExp:cells[0].matchAllRegExp.set,stringEndsWith:cells[0].stringEndsWith.set,stringIncludes:cells[0].stringIncludes.set,stringIndexOf:cells[0].stringIndexOf.set,stringMatch:cells[0].stringMatch.set,generatorNext:cells[0].generatorNext.set,generatorThrow:cells[0].generatorThrow.set,stringReplace:cells[0].stringReplace.set,stringSearch:cells[0].stringSearch.set,stringSlice:cells[0].stringSlice.set,stringSplit:cells[0].stringSplit.set,stringStartsWith:cells[0].stringStartsWith.set,iterateString:cells[0].iterateString.set,weakmapDelete:cells[0].weakmapDelete.set,weakmapGet:cells[0].weakmapGet.set,weakmapHas:cells[0].weakmapHas.set,weakmapSet:cells[0].weakmapSet.set,weaksetAdd:cells[0].weaksetAdd.set,weaksetHas:cells[0].weaksetHas.set,functionToString:cells[0].functionToString.set,functionBind:cells[0].functionBind.set,promiseAll:cells[0].promiseAll.set,promiseCatch:cells[0].promiseCatch.set,promiseThen:cells[0].promiseThen.set,finalizationRegistryRegister:cells[0].finalizationRegistryRegister.set,finalizationRegistryUnregister:cells[0].finalizationRegistryUnregister.set,getConstructorOf:cells[0].getConstructorOf.set,isObject:cells[0].isObject.set,isError:cells[0].isError.set,identity:cells[0].identity.set,FERAL_EVAL:cells[0].FERAL_EVAL.set,FERAL_FUNCTION:cells[0].FERAL_FUNCTION.set,noEvalEvaluate:cells[0].noEvalEvaluate.set,FERAL_STACK_GETTER:cells[0].FERAL_STACK_GETTER.set,FERAL_STACK_SETTER:cells[0].FERAL_STACK_SETTER.set,AsyncGeneratorFunctionInstance:cells[0].AsyncGeneratorFunctionInstance.set},importMeta:{}}),functors[1]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{},importMeta:{}}),functors[2]({imports(entries){new Map(entries)},liveVar:{},onceVar:{makeEnvironmentCaptor:cells[2].makeEnvironmentCaptor.set,getEnvironmentOption:cells[2].getEnvironmentOption.set,getEnvironmentOptionsList:cells[2].getEnvironmentOptionsList.set,environmentOptionsListHas:cells[2].environmentOptionsListHas.set},importMeta:{}}),functors[3]({imports(entries){observeImports(new Map(entries),"./src/env-options.js",2)},liveVar:{},onceVar:{},importMeta:{}}),functors[4]({imports(entries){new Map(entries)},liveVar:{},onceVar:{transferBufferToImmutable:cells[4].transferBufferToImmutable.set,isBufferImmutable:cells[4].isBufferImmutable.set,sliceBufferToImmutable:cells[4].sliceBufferToImmutable.set},importMeta:{}}),functors[5]({imports(entries){observeImports(new Map(entries),"./index.js",4)},liveVar:{},onceVar:{},importMeta:{}}),functors[6]({imports(entries){observeImports(new Map(entries),"../commons.js",0)},liveVar:{},onceVar:{an:cells[6].an.set,bestEffortStringify:cells[6].bestEffortStringify.set,enJoin:cells[6].enJoin.set},importMeta:{}}),functors[7]({imports(entries){new Map(entries)},liveVar:{},onceVar:{},importMeta:{}}),functors[8]({imports(entries){new Map(entries)},liveVar:{},onceVar:{},importMeta:{}}),functors[9]({imports(entries){new Map(entries)},liveVar:{},onceVar:{makeLRUCacheMap:cells[9].makeLRUCacheMap.set},importMeta:{}}),functors[10]({imports(entries){observeImports(new Map(entries),"../make-lru-cachemap.js",9)},liveVar:{},onceVar:{makeNoteLogArgsArrayKit:cells[10].makeNoteLogArgsArrayKit.set},importMeta:{}}),functors[11]({imports(entries){const map=new Map(entries);observeImports(map,"../commons.js",0),observeImports(map,"./stringify-utils.js",6),observeImports(map,"./types.js",7),observeImports(map,"./internal-types.js",8),observeImports(map,"./note-log-args.js",10)},liveVar:{},onceVar:{quote:cells[11].q.set,bare:cells[11].b.set,redactedDetails:cells[11].X.set,unredactedDetails:cells[11].unredactedDetails.set,makeError:cells[11].makeError.set,note:cells[11].annotateError.set,loggedErrorHandler:cells[11].loggedErrorHandler.set,makeAssert:cells[11].makeAssert.set,assert:cells[11].assert.set,assertEqual:cells[11].assertEqual.set,sanitizeError:cells[11].sanitizeError.set},importMeta:{}}),functors[12]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{isTypedArray:cells[12].isTypedArray.set,makeHardener:cells[12].makeHardener.set},importMeta:{}}),functors[13]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{cauterizeProperty:cells[13].cauterizeProperty.set},importMeta:{}}),functors[14]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{NativeErrors:cells[14].NativeErrors.set,constantProperties:cells[14].constantProperties.set,universalPropertyNames:cells[14].universalPropertyNames.set,initialGlobalPropertyNames:cells[14].initialGlobalPropertyNames.set,sharedGlobalPropertyNames:cells[14].sharedGlobalPropertyNames.set,uniqueGlobalPropertyNames:cells[14].uniqueGlobalPropertyNames.set,FunctionInstance:cells[14].FunctionInstance.set,AsyncFunctionInstance:cells[14].AsyncFunctionInstance.set,isAccessorPermit:cells[14].isAccessorPermit.set,permitted:cells[14].permitted.set},importMeta:{}}),functors[15]({imports(entries){const map=new Map(entries);observeImports(map,"./cauterize-property.js",13),observeImports(map,"./commons.js",0),observeImports(map,"./permits.js",14)},liveVar:{},onceVar:{makeIntrinsicsCollector:cells[15].makeIntrinsicsCollector.set,getGlobalIntrinsics:cells[15].getGlobalIntrinsics.set},importMeta:{}}),functors[16]({imports(entries){const map=new Map(entries);observeImports(map,"./permits.js",14),observeImports(map,"./commons.js",0),observeImports(map,"./cauterize-property.js",13)},liveVar:{},onceVar:{default:cells[16].default.set},importMeta:{}}),functors[17]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{default:cells[17].default.set},importMeta:{}}),functors[18]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{default:cells[18].default.set},importMeta:{}}),functors[19]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{default:cells[19].default.set},importMeta:{}}),functors[20]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{default:cells[20].default.set},importMeta:{}}),functors[21]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{minEnablements:cells[21].minEnablements.set,moderateEnablements:cells[21].moderateEnablements.set,severeEnablements:cells[21].severeEnablements.set},importMeta:{}}),functors[22]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./enablements.js",21)},liveVar:{},onceVar:{default:cells[22].default.set},importMeta:{}}),functors[23]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{default:cells[23].default.set},importMeta:{}}),functors[24]({imports(entries){new Map(entries)},liveVar:{},onceVar:{makeEvalFunction:cells[24].makeEvalFunction.set},importMeta:{}}),functors[25]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{makeFunctionConstructor:cells[25].makeFunctionConstructor.set},importMeta:{}}),functors[26]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./make-eval-function.js",24),observeImports(map,"./make-function-constructor.js",25),observeImports(map,"./permits.js",14)},liveVar:{},onceVar:{setGlobalObjectSymbolUnscopables:cells[26].setGlobalObjectSymbolUnscopables.set,setGlobalObjectConstantProperties:cells[26].setGlobalObjectConstantProperties.set,setGlobalObjectMutableProperties:cells[26].setGlobalObjectMutableProperties.set,setGlobalObjectEvaluators:cells[26].setGlobalObjectEvaluators.set},importMeta:{}}),functors[27]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{alwaysThrowHandler:cells[27].alwaysThrowHandler.set,strictScopeTerminatorHandler:cells[27].strictScopeTerminatorHandler.set,strictScopeTerminator:cells[27].strictScopeTerminator.set},importMeta:{}}),functors[28]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./strict-scope-terminator.js",27)},liveVar:{},onceVar:{createSloppyGlobalsScopeTerminator:cells[28].createSloppyGlobalsScopeTerminator.set},importMeta:{}}),functors[29]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{makeEvalScopeKit:cells[29].makeEvalScopeKit.set},importMeta:{}}),functors[30]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{getSourceURL:cells[30].getSourceURL.set},importMeta:{}}),functors[31]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./get-source-url.js",30)},liveVar:{},onceVar:{rejectHtmlComments:cells[31].rejectHtmlComments.set,evadeHtmlCommentTest:cells[31].evadeHtmlCommentTest.set,rejectImportExpressions:cells[31].rejectImportExpressions.set,evadeImportExpressionTest:cells[31].evadeImportExpressionTest.set,rejectSomeDirectEvalExpressions:cells[31].rejectSomeDirectEvalExpressions.set,mandatoryTransforms:cells[31].mandatoryTransforms.set,applyTransforms:cells[31].applyTransforms.set,transforms:cells[31].transforms.set},importMeta:{}}),functors[32]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{isValidIdentifierName:cells[32].isValidIdentifierName.set,getScopeConstants:cells[32].getScopeConstants.set},importMeta:{}}),functors[33]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./scope-constants.js",32)},liveVar:{},onceVar:{makeEvaluate:cells[33].makeEvaluate.set},importMeta:{}}),functors[34]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./strict-scope-terminator.js",27),observeImports(map,"./sloppy-globals-scope-terminator.js",28),observeImports(map,"./eval-scope.js",29),observeImports(map,"./transforms.js",31),observeImports(map,"./make-evaluate.js",33),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{makeSafeEvaluator:cells[34].makeSafeEvaluator.set},importMeta:{}}),functors[35]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameFunctionToString:cells[35].tameFunctionToString.set},importMeta:{}}),functors[36]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameDomains:cells[36].tameDomains.set},importMeta:{}}),functors[37]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameModuleSource:cells[37].tameModuleSource.set},importMeta:{}}),functors[38]({imports(entries){observeImports(new Map(entries),"../commons.js",0)},liveVar:{},onceVar:{consoleLevelMethods:cells[38].consoleLevelMethods.set,consoleOtherMethods:cells[38].consoleOtherMethods.set,makeLoggingConsoleKit:cells[38].makeLoggingConsoleKit.set,pumpLogToConsole:cells[38].pumpLogToConsole.set,makeCausalConsole:cells[38].makeCausalConsole.set,defineCausalConsoleFromLogger:cells[38].defineCausalConsoleFromLogger.set,filterConsole:cells[38].filterConsole.set},importMeta:{}}),functors[39]({imports(entries){observeImports(new Map(entries),"../commons.js",0)},liveVar:{},onceVar:{makeRejectionHandlers:cells[39].makeRejectionHandlers.set},importMeta:{}}),functors[40]({imports(entries){const map=new Map(entries);observeImports(map,"../commons.js",0),observeImports(map,"./assert.js",11),observeImports(map,"./console.js",38),observeImports(map,"./unhandled-rejection.js",39)},liveVar:{},onceVar:{tameConsole:cells[40].tameConsole.set},importMeta:{}}),functors[41]({imports(entries){observeImports(new Map(entries),"../commons.js",0)},liveVar:{},onceVar:{filterFileName:cells[41].filterFileName.set,shortenCallSiteString:cells[41].shortenCallSiteString.set,tameV8ErrorConstructor:cells[41].tameV8ErrorConstructor.set},importMeta:{}}),functors[42]({imports(entries){const map=new Map(entries);observeImports(map,"../commons.js",0),observeImports(map,"../permits.js",14),observeImports(map,"./tame-v8-error-constructor.js",41)},liveVar:{},onceVar:{default:cells[42].default.set},importMeta:{}}),functors[43]({imports(entries){const map=new Map(entries);observeImports(map,"@endo/env-options",3),observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{makeAlias:cells[43].makeAlias.set,load:cells[43].load.set,loadNow:cells[43].loadNow.set},importMeta:{}}),functors[44]({imports(entries){const map=new Map(entries);observeImports(map,"./module-load.js",43),observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{deferExports:cells[44].deferExports.set,getDeferredExports:cells[44].getDeferredExports.set},importMeta:{}}),functors[45]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./transforms.js",31),observeImports(map,"./make-safe-evaluator.js",34)},liveVar:{},onceVar:{provideCompartmentEvaluator:cells[45].provideCompartmentEvaluator.set,compartmentEvaluate:cells[45].compartmentEvaluate.set},importMeta:{}}),functors[46]({imports(entries){const map=new Map(entries);observeImports(map,"./error/assert.js",11),observeImports(map,"./module-proxy.js",44),observeImports(map,"./commons.js",0),observeImports(map,"./compartment-evaluate.js",45)},liveVar:{},onceVar:{makeVirtualModuleInstance:cells[46].makeVirtualModuleInstance.set,makeModuleInstance:cells[46].makeModuleInstance.set},importMeta:{}}),functors[47]({imports(entries){const map=new Map(entries);observeImports(map,"./error/assert.js",11),observeImports(map,"./module-instance.js",46),observeImports(map,"./commons.js",0)},liveVar:{},onceVar:{link:cells[47].link.set,instantiate:cells[47].instantiate.set},importMeta:{}}),functors[48]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./global-object.js",26),observeImports(map,"./error/assert.js",11),observeImports(map,"./permits.js",14),observeImports(map,"./module-load.js",43),observeImports(map,"./module-link.js",47),observeImports(map,"./module-proxy.js",44),observeImports(map,"./compartment-evaluate.js",45),observeImports(map,"./make-safe-evaluator.js",34)},liveVar:{},onceVar:{InertCompartment:cells[48].InertCompartment.set,CompartmentPrototype:cells[48].CompartmentPrototype.set,compartmentOptions:cells[48].compartmentOptions.set,makeCompartmentConstructor:cells[48].makeCompartmentConstructor.set},importMeta:{}}),functors[49]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./compartment.js",48)},liveVar:{},onceVar:{getAnonymousIntrinsics:cells[49].getAnonymousIntrinsics.set},importMeta:{}}),functors[50]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameHarden:cells[50].tameHarden.set},importMeta:{}}),functors[51]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameSymbolConstructor:cells[51].tameSymbolConstructor.set},importMeta:{}}),functors[52]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameFauxDataProperty:cells[52].tameFauxDataProperty.set,tameFauxDataProperties:cells[52].tameFauxDataProperties.set},importMeta:{}}),functors[53]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{tameRegeneratorRuntime:cells[53].tameRegeneratorRuntime.set},importMeta:{}}),functors[54]({imports(entries){observeImports(new Map(entries),"./commons.js",0)},liveVar:{},onceVar:{shimArrayBufferTransfer:cells[54].shimArrayBufferTransfer.set},importMeta:{}}),functors[55]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{chooseReporter:cells[55].chooseReporter.set,reportInGroup:cells[55].reportInGroup.set},importMeta:{}}),functors[56]({imports(entries){const map=new Map(entries);observeImports(map,"@endo/env-options",3),observeImports(map,"@endo/immutable-arraybuffer/shim.js",5),observeImports(map,"./commons.js",0),observeImports(map,"./make-hardener.js",12),observeImports(map,"./intrinsics.js",15),observeImports(map,"./permits-intrinsics.js",16),observeImports(map,"./tame-function-constructors.js",17),observeImports(map,"./tame-date-constructor.js",18),observeImports(map,"./tame-math-object.js",19),observeImports(map,"./tame-regexp-constructor.js",20),observeImports(map,"./enable-property-overrides.js",22),observeImports(map,"./tame-locale-methods.js",23),observeImports(map,"./global-object.js",26),observeImports(map,"./make-safe-evaluator.js",34),observeImports(map,"./permits.js",14),observeImports(map,"./tame-function-tostring.js",35),observeImports(map,"./tame-domains.js",36),observeImports(map,"./tame-module-source.js",37),observeImports(map,"./error/tame-console.js",40),observeImports(map,"./error/tame-error-constructor.js",42),observeImports(map,"./error/assert.js",11),observeImports(map,"./get-anonymous-intrinsics.js",49),observeImports(map,"./compartment.js",48),observeImports(map,"./tame-harden.js",50),observeImports(map,"./tame-symbol-constructor.js",51),observeImports(map,"./tame-faux-data-properties.js",52),observeImports(map,"./tame-regenerator-runtime.js",53),observeImports(map,"./shim-arraybuffer-transfer.js",54),observeImports(map,"./reporting.js",55)},liveVar:{},onceVar:{repairIntrinsics:cells[56].repairIntrinsics.set},importMeta:{}}),functors[57]({imports(entries){const map=new Map(entries);observeImports(map,"./assert-sloppy-mode.js",1),observeImports(map,"./commons.js",0),observeImports(map,"./lockdown.js",56)},liveVar:{},onceVar:{},importMeta:{}}),functors[58]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./compartment.js",48),observeImports(map,"./tame-function-tostring.js",35),observeImports(map,"./intrinsics.js",15),observeImports(map,"./reporting.js",55)},liveVar:{},onceVar:{},importMeta:{}}),functors[59]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{},importMeta:{}}),functors[60]({imports(entries){const map=new Map(entries);observeImports(map,"./commons.js",0),observeImports(map,"./error/console.js",38),observeImports(map,"./error/assert.js",11)},liveVar:{},onceVar:{},importMeta:{}}),functors[61]({imports(entries){const map=new Map(entries);observeImports(map,"./src/lockdown-shim.js",57),observeImports(map,"./src/compartment-shim.js",58),observeImports(map,"./src/assert-shim.js",59),observeImports(map,"./src/console-shim.js",60)},liveVar:{},onceVar:{},importMeta:{}}),cells[cells.length-1]["*"].get()})([({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([]);const universalThis=globalThis;$h͏_once.universalThis(universalThis);const{Array:Array,ArrayBuffer:ArrayBuffer,Date:Date,FinalizationRegistry:FinalizationRegistry,Float32Array:Float32Array,JSON:JSON,Map:Map,Math:Math,Number:Number,Object:Object,Promise:Promise,Proxy:Proxy,Reflect:Reflect,RegExp:FERAL_REG_EXP,Set:Set,String:String,Symbol:Symbol,Uint8Array:Uint8Array,WeakMap:WeakMap,WeakSet:WeakSet}=globalThis;$h͏_once.Array(Array),$h͏_once.ArrayBuffer(ArrayBuffer),$h͏_once.Date(Date),$h͏_once.FinalizationRegistry(FinalizationRegistry),$h͏_once.Float32Array(Float32Array),$h͏_once.JSON(JSON),$h͏_once.Map(Map),$h͏_once.Math(Math),$h͏_once.Number(Number),$h͏_once.Object(Object),$h͏_once.Promise(Promise),$h͏_once.Proxy(Proxy),$h͏_once.Reflect(Reflect),$h͏_once.FERAL_REG_EXP(FERAL_REG_EXP),$h͏_once.Set(Set),$h͏_once.String(String),$h͏_once.Symbol(Symbol),$h͏_once.Uint8Array(Uint8Array),$h͏_once.WeakMap(WeakMap),$h͏_once.WeakSet(WeakSet);const{Error:FERAL_ERROR,RangeError:RangeError,ReferenceError:ReferenceError,SyntaxError:SyntaxError,TypeError:TypeError,AggregateError:AggregateError}=globalThis;$h͏_once.FERAL_ERROR(FERAL_ERROR),$h͏_once.RangeError(RangeError),$h͏_once.ReferenceError(ReferenceError),$h͏_once.SyntaxError(SyntaxError),$h͏_once.TypeError(TypeError),$h͏_once.AggregateError(AggregateError);const{assign:assign,create:create,defineProperties:defineProperties,entries:entries,freeze:freeze,getOwnPropertyDescriptor:getOwnPropertyDescriptor,getOwnPropertyDescriptors:getOwnPropertyDescriptors,getOwnPropertyNames:getOwnPropertyNames,getPrototypeOf:getPrototypeOf,is:is,isFrozen:isFrozen,isSealed:isSealed,isExtensible:isExtensible,keys:keys,prototype:objectPrototype,seal:seal,preventExtensions:preventExtensions,setPrototypeOf:setPrototypeOf,values:values,fromEntries:fromEntries}=Object;$h͏_once.assign(assign),$h͏_once.create(create),$h͏_once.defineProperties(defineProperties),$h͏_once.entries(entries),$h͏_once.freeze(freeze),$h͏_once.getOwnPropertyDescriptor(getOwnPropertyDescriptor),$h͏_once.getOwnPropertyDescriptors(getOwnPropertyDescriptors),$h͏_once.getOwnPropertyNames(getOwnPropertyNames),$h͏_once.getPrototypeOf(getPrototypeOf),$h͏_once.is(is),$h͏_once.isFrozen(isFrozen),$h͏_once.isSealed(isSealed),$h͏_once.isExtensible(isExtensible),$h͏_once.keys(keys),$h͏_once.objectPrototype(objectPrototype),$h͏_once.seal(seal),$h͏_once.preventExtensions(preventExtensions),$h͏_once.setPrototypeOf(setPrototypeOf),$h͏_once.values(values),$h͏_once.fromEntries(fromEntries);const{species:speciesSymbol,toStringTag:toStringTagSymbol,iterator:iteratorSymbol,matchAll:matchAllSymbol,unscopables:unscopablesSymbol,keyFor:symbolKeyFor,for:symbolFor}=Symbol;$h͏_once.speciesSymbol(speciesSymbol),$h͏_once.toStringTagSymbol(toStringTagSymbol),$h͏_once.iteratorSymbol(iteratorSymbol),$h͏_once.matchAllSymbol(matchAllSymbol),$h͏_once.unscopablesSymbol(unscopablesSymbol),$h͏_once.symbolKeyFor(symbolKeyFor),$h͏_once.symbolFor(symbolFor);const{isInteger:isInteger}=Number;$h͏_once.isInteger(isInteger);const{stringify:stringifyJson}=JSON;$h͏_once.stringifyJson(stringifyJson);const{defineProperty:originalDefineProperty}=Object;$h͏_once.defineProperty(((object,prop,descriptor)=>{const result=originalDefineProperty(object,prop,descriptor);if(result!==object)throw TypeError(`Please report that the original defineProperty silently failed to set ${stringifyJson(String(prop))}. (SES_DEFINE_PROPERTY_FAILED_SILENTLY)`);return result}));const{apply:apply,construct:construct,get:reflectGet,getOwnPropertyDescriptor:reflectGetOwnPropertyDescriptor,has:reflectHas,isExtensible:reflectIsExtensible,ownKeys:ownKeys,preventExtensions:reflectPreventExtensions,set:reflectSet}=Reflect;$h͏_once.apply(apply),$h͏_once.construct(construct),$h͏_once.reflectGet(reflectGet),$h͏_once.reflectGetOwnPropertyDescriptor(reflectGetOwnPropertyDescriptor),$h͏_once.reflectHas(reflectHas),$h͏_once.reflectIsExtensible(reflectIsExtensible),$h͏_once.ownKeys(ownKeys),$h͏_once.reflectPreventExtensions(reflectPreventExtensions),$h͏_once.reflectSet(reflectSet);const{isArray:isArray,prototype:arrayPrototype}=Array;$h͏_once.isArray(isArray),$h͏_once.arrayPrototype(arrayPrototype);const{prototype:arrayBufferPrototype}=ArrayBuffer;$h͏_once.arrayBufferPrototype(arrayBufferPrototype);const{prototype:mapPrototype}=Map;$h͏_once.mapPrototype(mapPrototype);const{revocable:proxyRevocable}=Proxy;$h͏_once.proxyRevocable(proxyRevocable);const{prototype:regexpPrototype}=RegExp;$h͏_once.regexpPrototype(regexpPrototype);const{prototype:setPrototype}=Set;$h͏_once.setPrototype(setPrototype);const{prototype:stringPrototype}=String;$h͏_once.stringPrototype(stringPrototype);const{prototype:weakmapPrototype}=WeakMap;$h͏_once.weakmapPrototype(weakmapPrototype);const{prototype:weaksetPrototype}=WeakSet;$h͏_once.weaksetPrototype(weaksetPrototype);const{prototype:functionPrototype}=Function;$h͏_once.functionPrototype(functionPrototype);const{prototype:promisePrototype}=Promise;$h͏_once.promisePrototype(promisePrototype);const{prototype:generatorPrototype}=getPrototypeOf((function*(){}));$h͏_once.generatorPrototype(generatorPrototype);const iteratorPrototype=getPrototypeOf(getPrototypeOf(arrayPrototype.values()));$h͏_once.iteratorPrototype(iteratorPrototype);const typedArrayPrototype=getPrototypeOf(Uint8Array.prototype);$h͏_once.typedArrayPrototype(typedArrayPrototype);const{bind:bind}=functionPrototype,uncurryThis=bind.bind(bind.call);$h͏_once.uncurryThis(uncurryThis);const objectHasOwnProperty=uncurryThis(objectPrototype.hasOwnProperty);$h͏_once.objectHasOwnProperty(objectHasOwnProperty);const arrayFilter=uncurryThis(arrayPrototype.filter);$h͏_once.arrayFilter(arrayFilter);const arrayForEach=uncurryThis(arrayPrototype.forEach);$h͏_once.arrayForEach(arrayForEach);const arrayIncludes=uncurryThis(arrayPrototype.includes);$h͏_once.arrayIncludes(arrayIncludes);const arrayJoin=uncurryThis(arrayPrototype.join);$h͏_once.arrayJoin(arrayJoin);const arrayMap=uncurryThis(arrayPrototype.map);$h͏_once.arrayMap(arrayMap);const arrayFlatMap=uncurryThis(arrayPrototype.flatMap);$h͏_once.arrayFlatMap(arrayFlatMap);const arrayPop=uncurryThis(arrayPrototype.pop);$h͏_once.arrayPop(arrayPop);const arrayPush=uncurryThis(arrayPrototype.push);$h͏_once.arrayPush(arrayPush);const arraySlice=uncurryThis(arrayPrototype.slice);$h͏_once.arraySlice(arraySlice);const arraySome=uncurryThis(arrayPrototype.some);$h͏_once.arraySome(arraySome);const arraySort=uncurryThis(arrayPrototype.sort);$h͏_once.arraySort(arraySort);const iterateArray=uncurryThis(arrayPrototype[iteratorSymbol]);$h͏_once.iterateArray(iterateArray);const arrayBufferSlice=uncurryThis(arrayBufferPrototype.slice);$h͏_once.arrayBufferSlice(arrayBufferSlice);const arrayBufferGetByteLength=uncurryThis(getOwnPropertyDescriptor(arrayBufferPrototype,"byteLength").get);$h͏_once.arrayBufferGetByteLength(arrayBufferGetByteLength);const typedArraySet=uncurryThis(typedArrayPrototype.set);$h͏_once.typedArraySet(typedArraySet);const mapSet=uncurryThis(mapPrototype.set);$h͏_once.mapSet(mapSet);const mapGet=uncurryThis(mapPrototype.get);$h͏_once.mapGet(mapGet);const mapHas=uncurryThis(mapPrototype.has);$h͏_once.mapHas(mapHas);const mapDelete=uncurryThis(mapPrototype.delete);$h͏_once.mapDelete(mapDelete);const mapEntries=uncurryThis(mapPrototype.entries);$h͏_once.mapEntries(mapEntries);const iterateMap=uncurryThis(mapPrototype[iteratorSymbol]);$h͏_once.iterateMap(iterateMap);const setAdd=uncurryThis(setPrototype.add);$h͏_once.setAdd(setAdd);const setDelete=uncurryThis(setPrototype.delete);$h͏_once.setDelete(setDelete);const setForEach=uncurryThis(setPrototype.forEach);$h͏_once.setForEach(setForEach);const setHas=uncurryThis(setPrototype.has);$h͏_once.setHas(setHas);const iterateSet=uncurryThis(setPrototype[iteratorSymbol]);$h͏_once.iterateSet(iterateSet);const regexpTest=uncurryThis(regexpPrototype.test);$h͏_once.regexpTest(regexpTest);const regexpExec=uncurryThis(regexpPrototype.exec);$h͏_once.regexpExec(regexpExec);const matchAllRegExp=uncurryThis(regexpPrototype[matchAllSymbol]);$h͏_once.matchAllRegExp(matchAllRegExp);const stringEndsWith=uncurryThis(stringPrototype.endsWith);$h͏_once.stringEndsWith(stringEndsWith);const stringIncludes=uncurryThis(stringPrototype.includes);$h͏_once.stringIncludes(stringIncludes);const stringIndexOf=uncurryThis(stringPrototype.indexOf);$h͏_once.stringIndexOf(stringIndexOf);const stringMatch=uncurryThis(stringPrototype.match);$h͏_once.stringMatch(stringMatch);const generatorNext=uncurryThis(generatorPrototype.next);$h͏_once.generatorNext(generatorNext);const generatorThrow=uncurryThis(generatorPrototype.throw);$h͏_once.generatorThrow(generatorThrow);const stringReplace=uncurryThis(stringPrototype.replace);$h͏_once.stringReplace(stringReplace);const stringSearch=uncurryThis(stringPrototype.search);$h͏_once.stringSearch(stringSearch);const stringSlice=uncurryThis(stringPrototype.slice);$h͏_once.stringSlice(stringSlice);const stringSplit=uncurryThis(stringPrototype.split);$h͏_once.stringSplit(stringSplit);const stringStartsWith=uncurryThis(stringPrototype.startsWith);$h͏_once.stringStartsWith(stringStartsWith);const iterateString=uncurryThis(stringPrototype[iteratorSymbol]);$h͏_once.iterateString(iterateString);const weakmapDelete=uncurryThis(weakmapPrototype.delete);$h͏_once.weakmapDelete(weakmapDelete);const weakmapGet=uncurryThis(weakmapPrototype.get);$h͏_once.weakmapGet(weakmapGet);const weakmapHas=uncurryThis(weakmapPrototype.has);$h͏_once.weakmapHas(weakmapHas);const weakmapSet=uncurryThis(weakmapPrototype.set);$h͏_once.weakmapSet(weakmapSet);const weaksetAdd=uncurryThis(weaksetPrototype.add);$h͏_once.weaksetAdd(weaksetAdd);const weaksetHas=uncurryThis(weaksetPrototype.has);$h͏_once.weaksetHas(weaksetHas);const functionToString=uncurryThis(functionPrototype.toString);$h͏_once.functionToString(functionToString);const functionBind=uncurryThis(bind);$h͏_once.functionBind(functionBind);const{all:all}=Promise;$h͏_once.promiseAll((promises=>apply(all,Promise,[promises])));const promiseCatch=uncurryThis(promisePrototype.catch);$h͏_once.promiseCatch(promiseCatch);const promiseThen=uncurryThis(promisePrototype.then);$h͏_once.promiseThen(promiseThen);const finalizationRegistryRegister=FinalizationRegistry&&uncurryThis(FinalizationRegistry.prototype.register);$h͏_once.finalizationRegistryRegister(finalizationRegistryRegister);const finalizationRegistryUnregister=FinalizationRegistry&&uncurryThis(FinalizationRegistry.prototype.unregister);$h͏_once.finalizationRegistryUnregister(finalizationRegistryUnregister);$h͏_once.getConstructorOf((fn=>reflectGet(getPrototypeOf(fn),"constructor")));$h͏_once.isObject((value=>Object(value)===value));$h͏_once.isError((value=>value instanceof FERAL_ERROR));$h͏_once.identity((x=>x));const FERAL_EVAL=eval;$h͏_once.FERAL_EVAL(FERAL_EVAL);const FERAL_FUNCTION=Function;$h͏_once.FERAL_FUNCTION(FERAL_FUNCTION);$h͏_once.noEvalEvaluate((()=>{throw TypeError('Cannot eval with evalTaming set to "no-eval" (SES_NO_EVAL)')}));const er1StackDesc=getOwnPropertyDescriptor(Error("er1"),"stack"),er2StackDesc=getOwnPropertyDescriptor(TypeError("er2"),"stack");let feralStackGetter,feralStackSetter;if(er1StackDesc&&er2StackDesc&&er1StackDesc.get){if("function"!=typeof er1StackDesc.get||er1StackDesc.get!==er2StackDesc.get||"function"!=typeof er1StackDesc.set||er1StackDesc.set!==er2StackDesc.set)throw TypeError("Unexpected Error own stack accessor functions (SES_UNEXPECTED_ERROR_OWN_STACK_ACCESSOR)");feralStackGetter=freeze(er1StackDesc.get),feralStackSetter=freeze(er1StackDesc.set)}const FERAL_STACK_GETTER=feralStackGetter;$h͏_once.FERAL_STACK_GETTER(FERAL_STACK_GETTER);const FERAL_STACK_SETTER=feralStackSetter;$h͏_once.FERAL_STACK_SETTER(FERAL_STACK_SETTER);const AsyncGeneratorFunctionInstance=(()=>{try{return new FERAL_FUNCTION("return (async function* AsyncGeneratorFunctionInstance() {})")()}catch(error){if("SyntaxError"===error.name)return;if("EvalError"===error.name)return async function*(){};throw error}})();$h͏_once.AsyncGeneratorFunctionInstance(AsyncGeneratorFunctionInstance)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let TypeError;if($h͏_imports([["./commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]]]]]),function(){return this}())throw TypeError("SES failed to initialize, sloppy mode (SES_NO_SLOPPY)")}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([]);const{freeze:freeze}=Object,{apply:apply}=Reflect,uncurryThis=fn=>(receiver,...args)=>apply(fn,receiver,args),arrayPush=uncurryThis(Array.prototype.push),arrayIncludes=uncurryThis(Array.prototype.includes),stringSplit=uncurryThis(String.prototype.split),q=JSON.stringify,Fail=(literals,...args)=>{let msg=literals[0];for(let i=0;i<args.length;i+=1)msg=`${msg}${args[i]}${literals[i+1]}`;throw Error(msg)},makeEnvironmentCaptor=(aGlobal,dropNames=!1)=>{const capturedEnvironmentOptionNames=[],getEnvironmentOption=(optionName,defaultSetting,optOtherValues=undefined)=>{"string"==typeof optionName||Fail`Environment option name ${q(optionName)} must be a string.`,"string"==typeof defaultSetting||Fail`Environment option default setting ${q(defaultSetting)} must be a string.`;let setting=defaultSetting;const globalProcess=aGlobal.process||void 0,globalEnv="object"==typeof globalProcess&&globalProcess.env||void 0;if("object"==typeof globalEnv&&optionName in globalEnv){dropNames||arrayPush(capturedEnvironmentOptionNames,optionName);const optionValue=globalEnv[optionName];"string"==typeof optionValue||Fail`Environment option named ${q(optionName)}, if present, must have a corresponding string value, got ${q(optionValue)}`,setting=optionValue}return void 0===optOtherValues||setting===defaultSetting||arrayIncludes(optOtherValues,setting)||Fail`Unrecognized ${q(optionName)} value ${q(setting)}. Expected one of ${q([defaultSetting,...optOtherValues])}`,setting};freeze(getEnvironmentOption);const getEnvironmentOptionsList=optionName=>{const option=getEnvironmentOption(optionName,"");return freeze(""===option?[]:stringSplit(option,","))};freeze(getEnvironmentOptionsList);const getCapturedEnvironmentOptionNames=()=>freeze([...capturedEnvironmentOptionNames]);return freeze(getCapturedEnvironmentOptionNames),freeze({getEnvironmentOption:getEnvironmentOption,getEnvironmentOptionsList:getEnvironmentOptionsList,environmentOptionsListHas:(optionName,element)=>arrayIncludes(getEnvironmentOptionsList(optionName),element),getCapturedEnvironmentOptionNames:getCapturedEnvironmentOptionNames})};$h͏_once.makeEnvironmentCaptor(makeEnvironmentCaptor),freeze(makeEnvironmentCaptor);const{getEnvironmentOption:getEnvironmentOption,getEnvironmentOptionsList:getEnvironmentOptionsList,environmentOptionsListHas:environmentOptionsListHas}=makeEnvironmentCaptor(globalThis,!0);$h͏_once.getEnvironmentOption(getEnvironmentOption),$h͏_once.getEnvironmentOptionsList(getEnvironmentOptionsList),$h͏_once.environmentOptionsListHas(environmentOptionsListHas)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([["./src/env-options.js",[]]])}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([]);const{setPrototypeOf:setPrototypeOf,getOwnPropertyDescriptors:getOwnPropertyDescriptors}=Object,{apply:apply}=Reflect,{prototype:arrayBufferPrototype}=ArrayBuffer,{slice:slice,transfer:transfer}=arrayBufferPrototype,arrayBufferSlice=(arrayBuffer,start=undefined,end=undefined)=>apply(slice,arrayBuffer,[start,end]);let arrayBufferTransfer;if(transfer)arrayBufferTransfer=arrayBuffer=>apply(transfer,arrayBuffer,[]);else{if(!globalThis.structuredClone)throw TypeError('Can only emulate immutable ArrayBuffer on a platform with either "structuredClone" or "ArrayBuffer.prototype.transfer"');arrayBufferTransfer=arrayBuffer=>(arrayBufferSlice(arrayBuffer,0,0),globalThis.structuredClone(arrayBuffer,{transfer:[arrayBuffer]}))}class ImmutableArrayBufferInternal{#buffer;constructor(buffer){this.#buffer=arrayBufferTransfer(buffer)}get byteLength(){return this.#buffer.byteLength}get detached(){return this.#buffer,!1}get maxByteLength(){return this.#buffer.byteLength}get resizable(){return this.#buffer,!1}get immutable(){return this.#buffer,!0}slice(start=undefined,end=undefined){return arrayBufferSlice(this.#buffer,start,end)}sliceToImmutable(start=undefined,end=undefined){return sliceBufferToImmutable(this.#buffer,start,end)}resize(_newByteLength=undefined){throw this.#buffer,TypeError("Cannot resize an immutable ArrayBuffer")}transfer(_newLength=undefined){throw this.#buffer,TypeError("Cannot detach an immutable ArrayBuffer")}transferToFixedLength(_newLength=undefined){throw this.#buffer,TypeError("Cannot detach an immutable ArrayBuffer")}transferToImmutable(_newLength=undefined){throw this.#buffer,TypeError("Cannot detach an immutable ArrayBuffer")}}const immutableArrayBufferPrototype=ImmutableArrayBufferInternal.prototype;delete immutableArrayBufferPrototype.constructor;const{slice:{value:sliceOfImmutable},immutable:{get:isImmutableGetter}}=getOwnPropertyDescriptors(immutableArrayBufferPrototype);setPrototypeOf(immutableArrayBufferPrototype,arrayBufferPrototype);const transferBufferToImmutable=(buffer,newLength=undefined)=>{if(void 0!==newLength)if(transfer)buffer=apply(transfer,buffer,[newLength]);else{if(newLength<=(buffer=arrayBufferTransfer(buffer)).byteLength)buffer=arrayBufferSlice(buffer,0,newLength);else{const oldTA=new Uint8Array(buffer),newTA=new Uint8Array(newLength);newTA.set(oldTA),buffer=newTA.buffer}}return new ImmutableArrayBufferInternal(buffer)};$h͏_once.transferBufferToImmutable(transferBufferToImmutable);$h͏_once.isBufferImmutable((buffer=>{try{return apply(isImmutableGetter,buffer,[])}catch(err){if(err instanceof TypeError)return arrayBufferSlice(buffer,0,0),!1;throw err}}));const sliceBufferToImmutable=(buffer,start=undefined,end=undefined)=>transferBufferToImmutable(((buffer,start=undefined,end=undefined)=>{try{return apply(sliceOfImmutable,buffer,[start,end])}catch(err){if(err instanceof TypeError)return arrayBufferSlice(buffer,start,end);throw err}})(buffer,start,end));$h͏_once.sliceBufferToImmutable(sliceBufferToImmutable)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let transferBufferToImmutable,isBufferImmutable,sliceBufferToImmutable;$h͏_imports([["./index.js",[["transferBufferToImmutable",[$h͏_a=>transferBufferToImmutable=$h͏_a]],["isBufferImmutable",[$h͏_a=>isBufferImmutable=$h͏_a]],["sliceBufferToImmutable",[$h͏_a=>sliceBufferToImmutable=$h͏_a]]]]]);const{getOwnPropertyDescriptors:getOwnPropertyDescriptors,defineProperties:defineProperties}=Object,{prototype:arrayBufferPrototype}=ArrayBuffer,arrayBufferMethods={transferToImmutable(newLength=undefined){return transferBufferToImmutable(this,newLength)},sliceToImmutable(start=undefined,end=undefined){return sliceBufferToImmutable(this,start,end)},get immutable(){return isBufferImmutable(this)}};"sliceToImmutable"in arrayBufferPrototype&&console.warn('About to overwrite a prior implementation of "sliceToImmutable"'),defineProperties(arrayBufferPrototype,getOwnPropertyDescriptors(arrayBufferMethods))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Set,String,isArray,arrayJoin,arraySlice,arraySort,arrayMap,keys,fromEntries,freeze,is,isError,setAdd,setHas,stringIncludes,stringStartsWith,stringifyJson,toStringTagSymbol;$h͏_imports([["../commons.js",[["Set",[$h͏_a=>Set=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["isArray",[$h͏_a=>isArray=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["arraySlice",[$h͏_a=>arraySlice=$h͏_a]],["arraySort",[$h͏_a=>arraySort=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["keys",[$h͏_a=>keys=$h͏_a]],["fromEntries",[$h͏_a=>fromEntries=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["is",[$h͏_a=>is=$h͏_a]],["isError",[$h͏_a=>isError=$h͏_a]],["setAdd",[$h͏_a=>setAdd=$h͏_a]],["setHas",[$h͏_a=>setHas=$h͏_a]],["stringIncludes",[$h͏_a=>stringIncludes=$h͏_a]],["stringStartsWith",[$h͏_a=>stringStartsWith=$h͏_a]],["stringifyJson",[$h͏_a=>stringifyJson=$h͏_a]],["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]]]]]);$h͏_once.enJoin(((terms,conjunction)=>{if(0===terms.length)return"(none)";if(1===terms.length)return terms[0];if(2===terms.length){const[first,second]=terms;return`${first} ${conjunction} ${second}`}return`${arrayJoin(arraySlice(terms,0,-1),", ")}, ${conjunction} ${terms[terms.length-1]}`}));const an=str=>(str=`${str}`).length>=1&&stringIncludes("aeiouAEIOU",str[0])?`an ${str}`:`a ${str}`;$h͏_once.an(an),freeze(an);const bestEffortStringify=(payload,spaces=undefined)=>{const seenSet=new Set,replacer=(_,val)=>{switch(typeof val){case"object":{if(null===val)return null;if(setHas(seenSet,val))return"[Seen]";if(setAdd(seenSet,val),isError(val))return`[${val.name}: ${val.message}]`;if(toStringTagSymbol in val)return`[${val[toStringTagSymbol]}]`;if(isArray(val))return val;const names=keys(val);if(names.length<2)return val;let sorted=!0;for(let i=1;i<names.length;i+=1)if(names[i-1]>=names[i]){sorted=!1;break}if(sorted)return val;arraySort(names);const entries=arrayMap(names,(name=>[name,val[name]]));return fromEntries(entries)}case"function":return`[Function ${val.name||"<anon>"}]`;case"string":return stringStartsWith(val,"[")?`[${val}]`:val;case"undefined":case"symbol":return`[${String(val)}]`;case"bigint":return`[${val}n]`;case"number":return is(val,NaN)?"[NaN]":val===1/0?"[Infinity]":val===-1/0?"[-Infinity]":val;default:return val}};try{return stringifyJson(payload,replacer,spaces)}catch(_err){return"[Something that failed to stringify]"}};$h͏_once.bestEffortStringify(bestEffortStringify),freeze(bestEffortStringify)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([])}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([])}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([]);const{isSafeInteger:isSafeInteger}=Number,{freeze:freeze}=Object,{toStringTag:toStringTagSymbol}=Symbol,makeSelfCell=data=>{const selfCell={next:void 0,prev:void 0,data:data};return selfCell.next=selfCell,selfCell.prev=selfCell,selfCell},spliceAfter=(prev,selfCell)=>{if(prev===selfCell)throw TypeError("Cannot splice a cell into itself");if(selfCell.next!==selfCell||selfCell.prev!==selfCell)throw TypeError("Expected self-linked cell");const cell=selfCell,next=prev.next;return cell.prev=prev,cell.next=next,prev.next=cell,next.prev=cell,cell},spliceOut=cell=>{const{prev:prev,next:next}=cell;prev.next=next,next.prev=prev,cell.prev=cell,cell.next=cell},makeLRUCacheMap=keysBudget=>{if(!isSafeInteger(keysBudget)||keysBudget<0)throw TypeError("keysBudget must be a safe non-negative integer number");const keyToCell=new WeakMap;let size=0;const head=makeSelfCell(void 0),touchCell=key=>{const cell=keyToCell.get(key);if(void 0!==cell&&void 0!==cell.data)return spliceOut(cell),spliceAfter(head,cell),cell},has=key=>void 0!==touchCell(key);freeze(has);const get=key=>{const cell=touchCell(key);return cell&&cell.data&&cell.data.get(key)};freeze(get);const set=(key,value)=>{if(keysBudget<1)return lruCacheMap;let cell=touchCell(key);if(void 0===cell&&(cell=makeSelfCell(void 0),spliceAfter(head,cell)),!cell.data)for(size+=1,cell.data=new WeakMap,keyToCell.set(key,cell);size>keysBudget;){const condemned=head.prev;spliceOut(condemned),condemned.data=void 0,size-=1}return cell.data.set(key,value),lruCacheMap};freeze(set);const deleteIt=key=>{const cell=keyToCell.get(key);return void 0!==cell&&(spliceOut(cell),keyToCell.delete(key),void 0!==cell.data&&(cell.data=void 0,size-=1,!0))};freeze(deleteIt);const lruCacheMap=freeze({has:has,get:get,set:set,delete:deleteIt,[toStringTagSymbol]:"LRUCacheMap"});return lruCacheMap};$h͏_once.makeLRUCacheMap(makeLRUCacheMap),freeze(makeLRUCacheMap)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let makeLRUCacheMap;$h͏_imports([["../make-lru-cachemap.js",[["makeLRUCacheMap",[$h͏_a=>makeLRUCacheMap=$h͏_a]]]]]);const{freeze:freeze}=Object,{isSafeInteger:isSafeInteger}=Number,makeNoteLogArgsArrayKit=(errorsBudget=1e3,argsPerErrorBudget=100)=>{if(!isSafeInteger(argsPerErrorBudget)||argsPerErrorBudget<1)throw TypeError("argsPerErrorBudget must be a safe positive integer number");const noteLogArgsArrayMap=makeLRUCacheMap(errorsBudget),addLogArgs=(error,logArgs)=>{const logArgsArray=noteLogArgsArrayMap.get(error);void 0!==logArgsArray?(logArgsArray.length>=argsPerErrorBudget&&logArgsArray.shift(),logArgsArray.push(logArgs)):noteLogArgsArrayMap.set(error,[logArgs])};freeze(addLogArgs);const takeLogArgsArray=error=>{const result=noteLogArgsArrayMap.get(error);return noteLogArgsArrayMap.delete(error),result};return freeze(takeLogArgsArray),freeze({addLogArgs:addLogArgs,takeLogArgsArray:takeLogArgsArray})};$h͏_once.makeNoteLogArgsArrayKit(makeNoteLogArgsArrayKit),freeze(makeNoteLogArgsArrayKit)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let RangeError,TypeError,WeakMap,arrayJoin,arrayMap,arrayPop,arrayPush,assign,freeze,defineProperty,globalThis,is,isError,regexpTest,stringIndexOf,stringReplace,stringSlice,stringStartsWith,weakmapDelete,weakmapGet,weakmapHas,weakmapSet,AggregateError,getOwnPropertyDescriptors,ownKeys,create,objectPrototype,objectHasOwnProperty,an,bestEffortStringify,makeNoteLogArgsArrayKit;$h͏_imports([["../commons.js",[["RangeError",[$h͏_a=>RangeError=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["WeakMap",[$h͏_a=>WeakMap=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["arrayPop",[$h͏_a=>arrayPop=$h͏_a]],["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["assign",[$h͏_a=>assign=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["is",[$h͏_a=>is=$h͏_a]],["isError",[$h͏_a=>isError=$h͏_a]],["regexpTest",[$h͏_a=>regexpTest=$h͏_a]],["stringIndexOf",[$h͏_a=>stringIndexOf=$h͏_a]],["stringReplace",[$h͏_a=>stringReplace=$h͏_a]],["stringSlice",[$h͏_a=>stringSlice=$h͏_a]],["stringStartsWith",[$h͏_a=>stringStartsWith=$h͏_a]],["weakmapDelete",[$h͏_a=>weakmapDelete=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["weakmapHas",[$h͏_a=>weakmapHas=$h͏_a]],["weakmapSet",[$h͏_a=>weakmapSet=$h͏_a]],["AggregateError",[$h͏_a=>AggregateError=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["objectPrototype",[$h͏_a=>objectPrototype=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]]]],["./stringify-utils.js",[["an",[$h͏_a=>an=$h͏_a]],["bestEffortStringify",[$h͏_a=>bestEffortStringify=$h͏_a]]]],["./types.js",[]],["./internal-types.js",[]],["./note-log-args.js",[["makeNoteLogArgsArrayKit",[$h͏_a=>makeNoteLogArgsArrayKit=$h͏_a]]]]]);const declassifiers=new WeakMap,quote=(payload,spaces=undefined)=>{const result=freeze({toString:freeze((()=>bestEffortStringify(payload,spaces)))});return weakmapSet(declassifiers,result,payload),result};$h͏_once.quote(quote),freeze(quote);const canBeBare=freeze(/^[\w:-]( ?[\w:-])*$/),bare=(payload,spaces=undefined)=>{if("string"!=typeof payload||!regexpTest(canBeBare,payload))return quote(payload,spaces);const result=freeze({toString:freeze((()=>payload))});return weakmapSet(declassifiers,result,payload),result};$h͏_once.bare(bare),freeze(bare);const hiddenDetailsMap=new WeakMap,getMessageString=({template:template,args:args})=>{const parts=[template[0]];for(let i=0;i<args.length;i+=1){const arg=args[i];let argStr;argStr=weakmapHas(declassifiers,arg)?`${arg}`:isError(arg)?`(${an(arg.name)})`:`(${an(typeof arg)})`,arrayPush(parts,argStr,template[i+1])}return arrayJoin(parts,"")},DetailsTokenProto=freeze({toString(){const hiddenDetails=weakmapGet(hiddenDetailsMap,this);return void 0===hiddenDetails?"[Not a DetailsToken]":getMessageString(hiddenDetails)}});freeze(DetailsTokenProto.toString);const redactedDetails=(template,...args)=>{const detailsToken=freeze({__proto__:DetailsTokenProto});return weakmapSet(hiddenDetailsMap,detailsToken,{template:template,args:args}),detailsToken};$h͏_once.redactedDetails(redactedDetails),freeze(redactedDetails);const unredactedDetails=(template,...args)=>(args=arrayMap(args,(arg=>weakmapHas(declassifiers,arg)?arg:quote(arg))),redactedDetails(template,...args));$h͏_once.unredactedDetails(unredactedDetails),freeze(unredactedDetails);const getLogArgs=({template:template,args:args})=>{const logArgs=[template[0]];for(let i=0;i<args.length;i+=1){let arg=args[i];weakmapHas(declassifiers,arg)&&(arg=weakmapGet(declassifiers,arg));const priorWithoutSpace=stringReplace(arrayPop(logArgs)||"",/ $/,"");""!==priorWithoutSpace&&arrayPush(logArgs,priorWithoutSpace);const nextWithoutSpace=stringReplace(template[i+1],/^ /,"");arrayPush(logArgs,arg,nextWithoutSpace)}return""===logArgs[logArgs.length-1]&&arrayPop(logArgs),logArgs},hiddenMessageLogArgs=new WeakMap;let errorTagNum=0;const errorTags=new WeakMap,tagError=(err,optErrorName=err.name)=>{let errorTag=weakmapGet(errorTags,err);return void 0!==errorTag||(errorTagNum+=1,errorTag=`${optErrorName}#${errorTagNum}`,weakmapSet(errorTags,err,errorTag)),errorTag},sanitizeError=error=>{const descs=getOwnPropertyDescriptors(error),{name:_nameDesc,message:_messageDesc,errors:_errorsDesc,cause:_causeDesc,stack:_stackDesc,...restDescs}=descs,restNames=ownKeys(restDescs);if(restNames.length>=1){for(const name of restNames)delete error[name];const droppedNote=create(objectPrototype,restDescs);note(error,redactedDetails`originally with properties ${quote(droppedNote)}`)}for(const name of ownKeys(error)){const desc=descs[name];desc&&objectHasOwnProperty(desc,"get")&&defineProperty(error,name,{value:error[name]})}freeze(error)};$h͏_once.sanitizeError(sanitizeError);const makeError=(optDetails=redactedDetails`Assert failed`,errConstructor=globalThis.Error,{errorName:errorName,cause:cause,errors:errors,sanitize:sanitize=!0}={})=>{"string"==typeof optDetails&&(optDetails=redactedDetails([optDetails]));const hiddenDetails=weakmapGet(hiddenDetailsMap,optDetails);if(void 0===hiddenDetails)throw TypeError(`unrecognized details ${quote(optDetails)}`);const messageString=getMessageString(hiddenDetails),opts=cause&&{cause:cause};let error;return void 0!==AggregateError&&errConstructor===AggregateError?error=AggregateError(errors||[],messageString,opts):(error=errConstructor(messageString,opts),void 0!==errors&&defineProperty(error,"errors",{value:errors,writable:!0,enumerable:!1,configurable:!0})),weakmapSet(hiddenMessageLogArgs,error,getLogArgs(hiddenDetails)),void 0!==errorName&&tagError(error,errorName),sanitize&&sanitizeError(error),error};$h͏_once.makeError(makeError),freeze(makeError);const{addLogArgs:addLogArgs,takeLogArgsArray:takeLogArgsArray}=makeNoteLogArgsArrayKit(),hiddenNoteCallbackArrays=new WeakMap,note=(error,detailsNote)=>{"string"==typeof detailsNote&&(detailsNote=redactedDetails([detailsNote]));const hiddenDetails=weakmapGet(hiddenDetailsMap,detailsNote);if(void 0===hiddenDetails)throw TypeError(`unrecognized details ${quote(detailsNote)}`);const logArgs=getLogArgs(hiddenDetails),callbacks=weakmapGet(hiddenNoteCallbackArrays,error);if(void 0!==callbacks)for(const callback of callbacks)callback(error,logArgs);else addLogArgs(error,logArgs)};$h͏_once.note(note),freeze(note);const loggedErrorHandler={getStackString:globalThis.getStackString||(error=>{if(!("stack"in error))return"";const stackString=`${error.stack}`,pos=stringIndexOf(stackString,"\n");return stringStartsWith(stackString," ")||-1===pos?stackString:stringSlice(stackString,pos+1)}),tagError:error=>tagError(error),resetErrorTagNum:()=>{errorTagNum=0},getMessageLogArgs:error=>weakmapGet(hiddenMessageLogArgs,error),takeMessageLogArgs:error=>{const result=weakmapGet(hiddenMessageLogArgs,error);return weakmapDelete(hiddenMessageLogArgs,error),result},takeNoteLogArgsArray:(error,callback)=>{const result=takeLogArgsArray(error);if(void 0!==callback){const callbacks=weakmapGet(hiddenNoteCallbackArrays,error);callbacks?arrayPush(callbacks,callback):weakmapSet(hiddenNoteCallbackArrays,error,[callback])}return result||[]}};$h͏_once.loggedErrorHandler(loggedErrorHandler),freeze(loggedErrorHandler);const makeAssert=(optRaise=undefined,unredacted=!1)=>{const details=unredacted?unredactedDetails:redactedDetails,assertFailedDetails=details`Check failed`,fail=(optDetails=assertFailedDetails,errConstructor=undefined,options=undefined)=>{const reason=makeError(optDetails,errConstructor,options);throw void 0!==optRaise&&optRaise(reason),reason};freeze(fail);const Fail=(template,...args)=>fail(details(template,...args));const equal=(actual,expected,optDetails=undefined,errConstructor=undefined,options=undefined)=>{is(actual,expected)||fail(optDetails||details`Expected ${actual} is same as ${expected}`,errConstructor||RangeError,options)};freeze(equal);const assertTypeof=(specimen,typename,optDetails)=>{if(typeof specimen!==typename){if("string"==typeof typename||Fail`${quote(typename)} must be a string`,void 0===optDetails){const typeWithDeterminer=an(typename);optDetails=details`${specimen} must be ${bare(typeWithDeterminer)}`}fail(optDetails,TypeError)}};freeze(assertTypeof);const assert=assign((function(flag,optDetails=undefined,errConstructor=undefined,options=undefined){flag||fail(optDetails,errConstructor,options)}),{error:makeError,fail:fail,equal:equal,typeof:assertTypeof,string:(specimen,optDetails=undefined)=>assertTypeof(specimen,"string",optDetails),note:note,details:details,Fail:Fail,quote:quote,bare:bare,makeAssert:makeAssert});return freeze(assert)};$h͏_once.makeAssert(makeAssert),freeze(makeAssert);const assert=makeAssert();$h͏_once.assert(assert);const assertEqual=assert.equal;$h͏_once.assertEqual(assertEqual)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Set,String,TypeError,WeakSet,globalThis,apply,arrayForEach,defineProperty,freeze,getOwnPropertyDescriptor,getOwnPropertyDescriptors,getPrototypeOf,isInteger,isObject,objectHasOwnProperty,ownKeys,preventExtensions,setAdd,setForEach,setHas,toStringTagSymbol,typedArrayPrototype,weaksetAdd,weaksetHas,FERAL_STACK_GETTER,FERAL_STACK_SETTER,isError,assert;$h͏_imports([["./commons.js",[["Set",[$h͏_a=>Set=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["WeakSet",[$h͏_a=>WeakSet=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["arrayForEach",[$h͏_a=>arrayForEach=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]],["isInteger",[$h͏_a=>isInteger=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["preventExtensions",[$h͏_a=>preventExtensions=$h͏_a]],["setAdd",[$h͏_a=>setAdd=$h͏_a]],["setForEach",[$h͏_a=>setForEach=$h͏_a]],["setHas",[$h͏_a=>setHas=$h͏_a]],["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]],["typedArrayPrototype",[$h͏_a=>typedArrayPrototype=$h͏_a]],["weaksetAdd",[$h͏_a=>weaksetAdd=$h͏_a]],["weaksetHas",[$h͏_a=>weaksetHas=$h͏_a]],["FERAL_STACK_GETTER",[$h͏_a=>FERAL_STACK_GETTER=$h͏_a]],["FERAL_STACK_SETTER",[$h͏_a=>FERAL_STACK_SETTER=$h͏_a]],["isError",[$h͏_a=>isError=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const typedArrayToStringTag=getOwnPropertyDescriptor(typedArrayPrototype,toStringTagSymbol);assert(typedArrayToStringTag);const getTypedArrayToStringTag=typedArrayToStringTag.get;assert(getTypedArrayToStringTag);const isTypedArray=object=>void 0!==apply(getTypedArrayToStringTag,object,[]);$h͏_once.isTypedArray(isTypedArray);const freezeTypedArray=array=>{preventExtensions(array),arrayForEach(ownKeys(array),(name=>{const desc=getOwnPropertyDescriptor(array,name);assert(desc),(propertyKey=>{const n=+String(propertyKey);return isInteger(n)&&String(n)===propertyKey})(name)||defineProperty(array,name,{...desc,writable:!1,configurable:!1})}))};$h͏_once.makeHardener((()=>{if("function"==typeof globalThis.harden){return globalThis.harden}const hardened=new WeakSet,{harden:harden}={harden(root){const toFreeze=new Set;function enqueue(val){if(!isObject(val))return;const type=typeof val;if("object"!==type&&"function"!==type)throw TypeError(`Unexpected typeof: ${type}`);weaksetHas(hardened,val)||setHas(toFreeze,val)||setAdd(toFreeze,val)}const baseFreezeAndTraverse=obj=>{isTypedArray(obj)?freezeTypedArray(obj):freeze(obj);const descs=getOwnPropertyDescriptors(obj);enqueue(getPrototypeOf(obj)),arrayForEach(ownKeys(descs),(name=>{const desc=descs[name];objectHasOwnProperty(desc,"value")?enqueue(desc.value):(enqueue(desc.get),enqueue(desc.set))}))},freezeAndTraverse=void 0===FERAL_STACK_GETTER&&void 0===FERAL_STACK_SETTER?baseFreezeAndTraverse:obj=>{if(isError(obj)){const stackDesc=getOwnPropertyDescriptor(obj,"stack");stackDesc&&stackDesc.get===FERAL_STACK_GETTER&&stackDesc.configurable&&defineProperty(obj,"stack",{value:apply(FERAL_STACK_GETTER,obj,[])})}return baseFreezeAndTraverse(obj)},markHardened=value=>{weaksetAdd(hardened,value)};return enqueue(root),setForEach(toFreeze,freezeAndTraverse),setForEach(toFreeze,markHardened),root}};return harden}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let objectHasOwnProperty;$h͏_imports([["./commons.js",[["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]]]]]);$h͏_once.cauterizeProperty(((obj,prop,known,subPath,{warn:warn,error:error})=>{known||warn(`Removing ${subPath}`);try{delete obj[prop]}catch(err){if(objectHasOwnProperty(obj,prop)){if("function"==typeof obj&&"prototype"===prop&&(obj.prototype=void 0,void 0===obj.prototype))return void warn(`Tolerating undeletable ${subPath} === undefined`);error(`failed to delete ${subPath}`,err)}else error(`deleting ${subPath} threw`,err);throw err}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let arrayPush,arrayForEach;$h͏_imports([["./commons.js",[["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["arrayForEach",[$h͏_a=>arrayForEach=$h͏_a]]]]]);const constantProperties={Infinity:1/0,NaN:NaN,undefined:void 0};$h͏_once.constantProperties(constantProperties);$h͏_once.universalPropertyNames({isFinite:"isFinite",isNaN:"isNaN",parseFloat:"parseFloat",parseInt:"parseInt",decodeURI:"decodeURI",decodeURIComponent:"decodeURIComponent",encodeURI:"encodeURI",encodeURIComponent:"encodeURIComponent",Array:"Array",ArrayBuffer:"ArrayBuffer",BigInt:"BigInt",BigInt64Array:"BigInt64Array",BigUint64Array:"BigUint64Array",Boolean:"Boolean",DataView:"DataView",EvalError:"EvalError",Float16Array:"Float16Array",Float32Array:"Float32Array",Float64Array:"Float64Array",Int8Array:"Int8Array",Int16Array:"Int16Array",Int32Array:"Int32Array",Map:"Map",Number:"Number",Object:"Object",Promise:"Promise",Proxy:"Proxy",RangeError:"RangeError",ReferenceError:"ReferenceError",Set:"Set",String:"String",SyntaxError:"SyntaxError",TypeError:"TypeError",Uint8Array:"Uint8Array",Uint8ClampedArray:"Uint8ClampedArray",Uint16Array:"Uint16Array",Uint32Array:"Uint32Array",URIError:"URIError",WeakMap:"WeakMap",WeakSet:"WeakSet",Iterator:"Iterator",AsyncIterator:"AsyncIterator",AggregateError:"AggregateError",JSON:"JSON",Reflect:"Reflect",escape:"escape",unescape:"unescape",ModuleSource:"ModuleSource",lockdown:"lockdown",harden:"harden",HandledPromise:"HandledPromise"});$h͏_once.initialGlobalPropertyNames({Date:"%InitialDate%",Error:"%InitialError%",RegExp:"%InitialRegExp%",Math:"%InitialMath%",getStackString:"%InitialGetStackString%"});$h͏_once.sharedGlobalPropertyNames({Date:"%SharedDate%",Error:"%SharedError%",RegExp:"%SharedRegExp%",Symbol:"%SharedSymbol%",Math:"%SharedMath%"});$h͏_once.uniqueGlobalPropertyNames({globalThis:"%UniqueGlobalThis%",eval:"%UniqueEval%",Function:"%UniqueFunction%",Compartment:"%UniqueCompartment%"});const NativeErrors=[EvalError,RangeError,ReferenceError,SyntaxError,TypeError,URIError];$h͏_once.NativeErrors(NativeErrors),"undefined"!=typeof AggregateError&&arrayPush(NativeErrors,AggregateError);const FunctionInstance={"[[Proto]]":"%FunctionPrototype%",length:"number",name:"string"};$h͏_once.FunctionInstance(FunctionInstance);const AsyncFunctionInstance={"[[Proto]]":"%AsyncFunctionPrototype%"};$h͏_once.AsyncFunctionInstance(AsyncFunctionInstance);const fn=FunctionInstance,asyncFn=AsyncFunctionInstance,getter={get:fn,set:"undefined"},accessor={get:fn,set:fn};arrayForEach(["caller","arguments"],(prop=>{}));function NativeError(prototype){return{"[[Proto]]":"%SharedError%",prototype:prototype}}function NativeErrorPrototype(constructor){return{"[[Proto]]":"%ErrorPrototype%",constructor:constructor,message:"string",name:"string",toString:!1,cause:!1}}function TypedArray(prototype){return{"[[Proto]]":"%TypedArray%",BYTES_PER_ELEMENT:"number",prototype:prototype}}function TypedArrayPrototype(constructor){return{"[[Proto]]":"%TypedArrayPrototype%",BYTES_PER_ELEMENT:"number",constructor:constructor}}$h͏_once.isAccessorPermit((permit=>permit===getter||permit===accessor));const CommonMath={E:"number",LN10:"number",LN2:"number",LOG10E:"number",LOG2E:"number",PI:"number",SQRT1_2:"number",SQRT2:"number","@@toStringTag":"string",abs:fn,acos:fn,acosh:fn,asin:fn,asinh:fn,atan:fn,atanh:fn,atan2:fn,cbrt:fn,ceil:fn,clz32:fn,cos:fn,cosh:fn,exp:fn,expm1:fn,floor:fn,fround:fn,hypot:fn,imul:fn,log:fn,log1p:fn,log10:fn,log2:fn,max:fn,min:fn,pow:fn,round:fn,sign:fn,sin:fn,sinh:fn,sqrt:fn,tan:fn,tanh:fn,trunc:fn,f16round:fn,sumPrecise:fn,idiv:!1,idivmod:!1,imod:!1,imuldiv:!1,irem:!1,mod:!1,irandom:!1},permitted={"[[Proto]]":null,"%ThrowTypeError%":fn,Infinity:"number",NaN:"number",undefined:"undefined","%UniqueEval%":fn,isFinite:fn,isNaN:fn,parseFloat:fn,parseInt:fn,decodeURI:fn,decodeURIComponent:fn,encodeURI:fn,encodeURIComponent:fn,Object:{"[[Proto]]":"%FunctionPrototype%",assign:fn,create:fn,defineProperties:fn,defineProperty:fn,entries:fn,freeze:fn,fromEntries:fn,getOwnPropertyDescriptor:fn,getOwnPropertyDescriptors:fn,getOwnPropertyNames:fn,getOwnPropertySymbols:fn,getPrototypeOf:fn,is:fn,isExtensible:fn,isFrozen:fn,isSealed:fn,keys:fn,preventExtensions:fn,prototype:"%ObjectPrototype%",seal:fn,setPrototypeOf:fn,values:fn,hasOwn:fn,groupBy:fn,__getClass:!1},"%ObjectPrototype%":{"[[Proto]]":null,constructor:"Object",hasOwnProperty:fn,isPrototypeOf:fn,propertyIsEnumerable:fn,toLocaleString:fn,toString:fn,valueOf:fn,"--proto--":accessor,__defineGetter__:fn,__defineSetter__:fn,__lookupGetter__:fn,__lookupSetter__:fn},"%UniqueFunction%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%FunctionPrototype%"},"%InertFunction%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%FunctionPrototype%"},"%FunctionPrototype%":{apply:fn,bind:fn,call:fn,constructor:"%InertFunction%",toString:fn,"@@hasInstance":fn,caller:!1,arguments:!1,fileName:!1,lineNumber:!1},Boolean:{"[[Proto]]":"%FunctionPrototype%",prototype:"%BooleanPrototype%"},"%BooleanPrototype%":{constructor:"Boolean",toString:fn,valueOf:fn},"%SharedSymbol%":{"[[Proto]]":"%FunctionPrototype%",asyncIterator:"symbol",for:fn,hasInstance:"symbol",isConcatSpreadable:"symbol",iterator:"symbol",keyFor:fn,match:"symbol",matchAll:"symbol",prototype:"%SymbolPrototype%",replace:"symbol",search:"symbol",species:"symbol",split:"symbol",toPrimitive:"symbol",toStringTag:"symbol",unscopables:"symbol",asyncDispose:"symbol",dispose:"symbol",useSimple:!1,useSetter:!1,operatorSet:!1},"%SymbolPrototype%":{constructor:"%SharedSymbol%",description:getter,toString:fn,valueOf:fn,"@@toPrimitive":fn,"@@toStringTag":"string"},"%InitialError%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%ErrorPrototype%",captureStackTrace:fn,stackTraceLimit:accessor,prepareStackTrace:accessor,isError:fn},"%SharedError%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%ErrorPrototype%",captureStackTrace:fn,stackTraceLimit:accessor,prepareStackTrace:accessor,isError:fn},"%ErrorPrototype%":{constructor:"%SharedError%",message:"string",name:"string",toString:fn,at:!1,stack:accessor,cause:!1},EvalError:NativeError("%EvalErrorPrototype%"),RangeError:NativeError("%RangeErrorPrototype%"),ReferenceError:NativeError("%ReferenceErrorPrototype%"),SyntaxError:NativeError("%SyntaxErrorPrototype%"),TypeError:NativeError("%TypeErrorPrototype%"),URIError:NativeError("%URIErrorPrototype%"),AggregateError:NativeError("%AggregateErrorPrototype%"),"%EvalErrorPrototype%":NativeErrorPrototype("EvalError"),"%RangeErrorPrototype%":NativeErrorPrototype("RangeError"),"%ReferenceErrorPrototype%":NativeErrorPrototype("ReferenceError"),"%SyntaxErrorPrototype%":NativeErrorPrototype("SyntaxError"),"%TypeErrorPrototype%":NativeErrorPrototype("TypeError"),"%URIErrorPrototype%":NativeErrorPrototype("URIError"),"%AggregateErrorPrototype%":NativeErrorPrototype("AggregateError"),Number:{"[[Proto]]":"%FunctionPrototype%",EPSILON:"number",isFinite:fn,isInteger:fn,isNaN:fn,isSafeInteger:fn,MAX_SAFE_INTEGER:"number",MAX_VALUE:"number",MIN_SAFE_INTEGER:"number",MIN_VALUE:"number",NaN:"number",NEGATIVE_INFINITY:"number",parseFloat:fn,parseInt:fn,POSITIVE_INFINITY:"number",prototype:"%NumberPrototype%"},"%NumberPrototype%":{constructor:"Number",toExponential:fn,toFixed:fn,toLocaleString:fn,toPrecision:fn,toString:fn,valueOf:fn},BigInt:{"[[Proto]]":"%FunctionPrototype%",asIntN:fn,asUintN:fn,prototype:"%BigIntPrototype%",bitLength:!1,fromArrayBuffer:!1,tdiv:!1,fdiv:!1,cdiv:!1,ediv:!1,tdivrem:!1,fdivrem:!1,cdivrem:!1,edivrem:!1,sqrt:!1,sqrtrem:!1,floorLog2:!1,ctz:!1},"%BigIntPrototype%":{constructor:"BigInt",toLocaleString:fn,toString:fn,valueOf:fn,"@@toStringTag":"string"},"%InitialMath%":{...CommonMath,random:fn},"%SharedMath%":{...CommonMath,random:fn},"%InitialDate%":{"[[Proto]]":"%FunctionPrototype%",now:fn,parse:fn,prototype:"%DatePrototype%",UTC:fn},"%SharedDate%":{"[[Proto]]":"%FunctionPrototype%",now:fn,parse:fn,prototype:"%DatePrototype%",UTC:fn},"%DatePrototype%":{constructor:"%SharedDate%",getDate:fn,getDay:fn,getFullYear:fn,getHours:fn,getMilliseconds:fn,getMinutes:fn,getMonth:fn,getSeconds:fn,getTime:fn,getTimezoneOffset:fn,getUTCDate:fn,getUTCDay:fn,getUTCFullYear:fn,getUTCHours:fn,getUTCMilliseconds:fn,getUTCMinutes:fn,getUTCMonth:fn,getUTCSeconds:fn,setDate:fn,setFullYear:fn,setHours:fn,setMilliseconds:fn,setMinutes:fn,setMonth:fn,setSeconds:fn,setTime:fn,setUTCDate:fn,setUTCFullYear:fn,setUTCHours:fn,setUTCMilliseconds:fn,setUTCMinutes:fn,setUTCMonth:fn,setUTCSeconds:fn,toDateString:fn,toISOString:fn,toJSON:fn,toLocaleDateString:fn,toLocaleString:fn,toLocaleTimeString:fn,toString:fn,toTimeString:fn,toUTCString:fn,valueOf:fn,"@@toPrimitive":fn,getYear:fn,setYear:fn,toGMTString:fn},String:{"[[Proto]]":"%FunctionPrototype%",fromCharCode:fn,fromCodePoint:fn,prototype:"%StringPrototype%",raw:fn,fromArrayBuffer:!1},"%StringPrototype%":{length:"number",charAt:fn,charCodeAt:fn,codePointAt:fn,concat:fn,constructor:"String",endsWith:fn,includes:fn,indexOf:fn,lastIndexOf:fn,localeCompare:fn,match:fn,matchAll:fn,normalize:fn,padEnd:fn,padStart:fn,repeat:fn,replace:fn,replaceAll:fn,search:fn,slice:fn,split:fn,startsWith:fn,substring:fn,toLocaleLowerCase:fn,toLocaleUpperCase:fn,toLowerCase:fn,toString:fn,toUpperCase:fn,trim:fn,trimEnd:fn,trimStart:fn,valueOf:fn,"@@iterator":fn,at:fn,isWellFormed:fn,toWellFormed:fn,unicodeSets:fn,substr:fn,anchor:fn,big:fn,blink:fn,bold:fn,fixed:fn,fontcolor:fn,fontsize:fn,italics:fn,link:fn,small:fn,strike:fn,sub:fn,sup:fn,trimLeft:fn,trimRight:fn,compare:!1,__quote:!1},"%StringIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,"@@toStringTag":"string"},"%InitialRegExp%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%RegExpPrototype%","@@species":getter,escape:fn,input:!1,$_:!1,lastMatch:!1,"$&":!1,lastParen:!1,"$+":!1,leftContext:!1,"$`":!1,rightContext:!1,"$'":!1,$1:!1,$2:!1,$3:!1,$4:!1,$5:!1,$6:!1,$7:!1,$8:!1,$9:!1},"%SharedRegExp%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%RegExpPrototype%","@@species":getter,escape:fn},"%RegExpPrototype%":{constructor:"%SharedRegExp%",exec:fn,dotAll:getter,flags:getter,global:getter,hasIndices:getter,ignoreCase:getter,"@@match":fn,"@@matchAll":fn,multiline:getter,"@@replace":fn,"@@search":fn,source:getter,"@@split":fn,sticky:getter,test:fn,toString:fn,unicode:getter,unicodeSets:getter,compile:!1},"%RegExpStringIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,"@@toStringTag":"string"},Array:{"[[Proto]]":"%FunctionPrototype%",from:fn,isArray:fn,of:fn,prototype:"%ArrayPrototype%","@@species":getter,at:fn,fromAsync:fn},"%ArrayPrototype%":{length:"number",concat:fn,constructor:"Array",copyWithin:fn,entries:fn,every:fn,fill:fn,filter:fn,find:fn,findIndex:fn,flat:fn,flatMap:fn,forEach:fn,includes:fn,indexOf:fn,join:fn,keys:fn,lastIndexOf:fn,map:fn,pop:fn,push:fn,reduce:fn,reduceRight:fn,reverse:fn,shift:fn,slice:fn,some:fn,sort:fn,splice:fn,toLocaleString:fn,toString:fn,unshift:fn,values:fn,"@@iterator":fn,"@@unscopables":{"[[Proto]]":null,copyWithin:"boolean",entries:"boolean",fill:"boolean",find:"boolean",findIndex:"boolean",flat:"boolean",flatMap:"boolean",includes:"boolean",keys:"boolean",values:"boolean",at:"boolean",findLast:"boolean",findLastIndex:"boolean",toReversed:"boolean",toSorted:"boolean",toSpliced:"boolean",with:"boolean",group:"boolean",groupToMap:"boolean",groupBy:"boolean"},findLast:fn,findLastIndex:fn,toReversed:fn,toSorted:fn,toSpliced:fn,with:fn,group:fn,groupToMap:fn,groupBy:fn,at:fn},"%ArrayIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,"@@toStringTag":"string"},"%TypedArray%":{"[[Proto]]":"%FunctionPrototype%",from:fn,of:fn,prototype:"%TypedArrayPrototype%","@@species":getter},"%TypedArrayPrototype%":{buffer:getter,byteLength:getter,byteOffset:getter,constructor:"%TypedArray%",copyWithin:fn,entries:fn,every:fn,fill:fn,filter:fn,find:fn,findIndex:fn,forEach:fn,includes:fn,indexOf:fn,join:fn,keys:fn,lastIndexOf:fn,length:getter,map:fn,reduce:fn,reduceRight:fn,reverse:fn,set:fn,slice:fn,some:fn,sort:fn,subarray:fn,toLocaleString:fn,toString:fn,values:fn,"@@iterator":fn,"@@toStringTag":getter,at:fn,findLast:fn,findLastIndex:fn,toReversed:fn,toSorted:fn,with:fn},BigInt64Array:TypedArray("%BigInt64ArrayPrototype%"),BigUint64Array:TypedArray("%BigUint64ArrayPrototype%"),Float16Array:TypedArray("%Float16ArrayPrototype%"),Float32Array:TypedArray("%Float32ArrayPrototype%"),Float64Array:TypedArray("%Float64ArrayPrototype%"),Int16Array:TypedArray("%Int16ArrayPrototype%"),Int32Array:TypedArray("%Int32ArrayPrototype%"),Int8Array:TypedArray("%Int8ArrayPrototype%"),Uint16Array:TypedArray("%Uint16ArrayPrototype%"),Uint32Array:TypedArray("%Uint32ArrayPrototype%"),Uint8ClampedArray:TypedArray("%Uint8ClampedArrayPrototype%"),Uint8Array:{...TypedArray("%Uint8ArrayPrototype%"),fromBase64:fn,fromHex:fn},"%BigInt64ArrayPrototype%":TypedArrayPrototype("BigInt64Array"),"%BigUint64ArrayPrototype%":TypedArrayPrototype("BigUint64Array"),"%Float16ArrayPrototype%":TypedArrayPrototype("Float16Array"),"%Float32ArrayPrototype%":TypedArrayPrototype("Float32Array"),"%Float64ArrayPrototype%":TypedArrayPrototype("Float64Array"),"%Int16ArrayPrototype%":TypedArrayPrototype("Int16Array"),"%Int32ArrayPrototype%":TypedArrayPrototype("Int32Array"),"%Int8ArrayPrototype%":TypedArrayPrototype("Int8Array"),"%Uint16ArrayPrototype%":TypedArrayPrototype("Uint16Array"),"%Uint32ArrayPrototype%":TypedArrayPrototype("Uint32Array"),"%Uint8ClampedArrayPrototype%":TypedArrayPrototype("Uint8ClampedArray"),"%Uint8ArrayPrototype%":{...TypedArrayPrototype("Uint8Array"),setFromBase64:fn,setFromHex:fn,toBase64:fn,toHex:fn},Map:{"[[Proto]]":"%FunctionPrototype%","@@species":getter,prototype:"%MapPrototype%",groupBy:fn},"%MapPrototype%":{clear:fn,constructor:"Map",delete:fn,entries:fn,forEach:fn,get:fn,has:fn,keys:fn,set:fn,size:getter,values:fn,"@@iterator":fn,"@@toStringTag":"string"},"%MapIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,"@@toStringTag":"string"},Set:{"[[Proto]]":"%FunctionPrototype%",prototype:"%SetPrototype%","@@species":getter,groupBy:!1},"%SetPrototype%":{add:fn,clear:fn,constructor:"Set",delete:fn,entries:fn,forEach:fn,has:fn,keys:fn,size:getter,values:fn,"@@iterator":fn,"@@toStringTag":"string",intersection:fn,union:fn,difference:fn,symmetricDifference:fn,isSubsetOf:fn,isSupersetOf:fn,isDisjointFrom:fn},"%SetIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,"@@toStringTag":"string"},WeakMap:{"[[Proto]]":"%FunctionPrototype%",prototype:"%WeakMapPrototype%"},"%WeakMapPrototype%":{constructor:"WeakMap",delete:fn,get:fn,has:fn,set:fn,"@@toStringTag":"string"},WeakSet:{"[[Proto]]":"%FunctionPrototype%",prototype:"%WeakSetPrototype%"},"%WeakSetPrototype%":{add:fn,constructor:"WeakSet",delete:fn,has:fn,"@@toStringTag":"string"},ArrayBuffer:{"[[Proto]]":"%FunctionPrototype%",isView:fn,prototype:"%ArrayBufferPrototype%","@@species":getter,fromString:!1,fromBigInt:!1},"%ArrayBufferPrototype%":{byteLength:getter,constructor:"ArrayBuffer",slice:fn,"@@toStringTag":"string",concat:!1,transfer:fn,resize:fn,resizable:getter,maxByteLength:getter,transferToFixedLength:fn,detached:getter,transferToImmutable:fn,sliceToImmutable:fn,immutable:getter},"%ImmutableArrayBufferPrototype%":{"[[Proto]]":"%ArrayBufferPrototype%",byteLength:getter,slice:fn,transfer:fn,resize:fn,resizable:getter,maxByteLength:getter,transferToFixedLength:fn,detached:getter,transferToImmutable:fn,sliceToImmutable:fn,immutable:getter},SharedArrayBuffer:!1,"%SharedArrayBufferPrototype%":!1,DataView:{"[[Proto]]":"%FunctionPrototype%",BYTES_PER_ELEMENT:"number",prototype:"%DataViewPrototype%"},"%DataViewPrototype%":{buffer:getter,byteLength:getter,byteOffset:getter,constructor:"DataView",getBigInt64:fn,getBigUint64:fn,getFloat16:fn,getFloat32:fn,getFloat64:fn,getInt8:fn,getInt16:fn,getInt32:fn,getUint8:fn,getUint16:fn,getUint32:fn,setBigInt64:fn,setBigUint64:fn,setFloat16:fn,setFloat32:fn,setFloat64:fn,setInt8:fn,setInt16:fn,setInt32:fn,setUint8:fn,setUint16:fn,setUint32:fn,"@@toStringTag":"string"},Atomics:!1,JSON:{parse:fn,stringify:fn,"@@toStringTag":"string",rawJSON:fn,isRawJSON:fn},Iterator:{"[[Proto]]":"%FunctionPrototype%",prototype:"%IteratorPrototype%",from:fn,zip:fn,zipKeyed:fn,concat:fn},"%IteratorPrototype%":{"@@iterator":fn,constructor:"Iterator",map:fn,filter:fn,take:fn,drop:fn,flatMap:fn,reduce:fn,toArray:fn,forEach:fn,some:fn,every:fn,find:fn,"@@toStringTag":"string",toAsync:fn,"@@dispose":!1},"%WrapForValidIteratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,return:fn},"%IteratorHelperPrototype%":{"[[Proto]]":"%IteratorPrototype%",next:fn,return:fn,"@@toStringTag":"string"},AsyncIterator:{"[[Proto]]":"%FunctionPrototype%",prototype:"%AsyncIteratorPrototype%",from:fn},"%AsyncIteratorPrototype%":{"@@asyncIterator":fn,constructor:"AsyncIterator",map:fn,filter:fn,take:fn,drop:fn,flatMap:fn,reduce:fn,toArray:fn,forEach:fn,some:fn,every:fn,find:fn,"@@toStringTag":"string","@@asyncDispose":!1},"%WrapForValidAsyncIteratorPrototype%":{"[[Proto]]":"%AsyncIteratorPrototype%",next:fn,return:fn},"%AsyncIteratorHelperPrototype%":{"[[Proto]]":"%AsyncIteratorPrototype%",next:fn,return:fn,"@@toStringTag":"string"},"%InertGeneratorFunction%":{"[[Proto]]":"%InertFunction%",prototype:"%Generator%"},"%Generator%":{"[[Proto]]":"%FunctionPrototype%",constructor:"%InertGeneratorFunction%",prototype:"%GeneratorPrototype%","@@toStringTag":"string"},"%InertAsyncGeneratorFunction%":{"[[Proto]]":"%InertFunction%",prototype:"%AsyncGenerator%"},"%AsyncGenerator%":{"[[Proto]]":"%FunctionPrototype%",constructor:"%InertAsyncGeneratorFunction%",prototype:"%AsyncGeneratorPrototype%",length:"number","@@toStringTag":"string"},"%GeneratorPrototype%":{"[[Proto]]":"%IteratorPrototype%",constructor:"%Generator%",next:fn,return:fn,throw:fn,"@@toStringTag":"string"},"%AsyncGeneratorPrototype%":{"[[Proto]]":"%AsyncIteratorPrototype%",constructor:"%AsyncGenerator%",next:fn,return:fn,throw:fn,"@@toStringTag":"string"},HandledPromise:{"[[Proto]]":"Promise",applyFunction:fn,applyFunctionSendOnly:fn,applyMethod:fn,applyMethodSendOnly:fn,get:fn,getSendOnly:fn,prototype:"%PromisePrototype%",resolve:fn},ModuleSource:{"[[Proto]]":"%AbstractModuleSource%",prototype:"%ModuleSourcePrototype%"},"%ModuleSourcePrototype%":{"[[Proto]]":"%AbstractModuleSourcePrototype%",constructor:"ModuleSource","@@toStringTag":"string",bindings:getter,needsImport:getter,needsImportMeta:getter,imports:getter,exports:getter,reexports:getter},"%AbstractModuleSource%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%AbstractModuleSourcePrototype%"},"%AbstractModuleSourcePrototype%":{constructor:"%AbstractModuleSource%"},Promise:{"[[Proto]]":"%FunctionPrototype%",all:fn,allSettled:fn,any:fn,prototype:"%PromisePrototype%",race:fn,reject:fn,resolve:fn,withResolvers:fn,"@@species":getter,try:fn},"%PromisePrototype%":{catch:fn,constructor:"Promise",finally:fn,then:fn,"@@toStringTag":"string","UniqueSymbol(async_id_symbol)":accessor,"UniqueSymbol(trigger_async_id_symbol)":accessor,"UniqueSymbol(destroyed)":accessor},"%InertAsyncFunction%":{"[[Proto]]":"%InertFunction%",prototype:"%AsyncFunctionPrototype%"},"%AsyncFunctionPrototype%":{"[[Proto]]":"%FunctionPrototype%",constructor:"%InertAsyncFunction%",length:"number","@@toStringTag":"string"},Reflect:{apply:fn,construct:fn,defineProperty:fn,deleteProperty:fn,get:fn,getOwnPropertyDescriptor:fn,getPrototypeOf:fn,has:fn,isExtensible:fn,ownKeys:fn,preventExtensions:fn,set:fn,setPrototypeOf:fn,"@@toStringTag":"string"},Proxy:{"[[Proto]]":"%FunctionPrototype%",revocable:fn},escape:fn,unescape:fn,"%UniqueCompartment%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%CompartmentPrototype%",toString:fn},"%InertCompartment%":{"[[Proto]]":"%FunctionPrototype%",prototype:"%CompartmentPrototype%",toString:fn},"%CompartmentPrototype%":{constructor:"%InertCompartment%",evaluate:fn,globalThis:getter,name:getter,import:asyncFn,load:asyncFn,importNow:fn,module:fn,"@@toStringTag":"string"},lockdown:fn,harden:{...fn,isFake:"boolean"},"%InitialGetStackString%":fn};$h͏_once.permitted(permitted)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let cauterizeProperty,TypeError,WeakSet,arrayFilter,create,defineProperty,entries,freeze,getOwnPropertyDescriptor,getOwnPropertyDescriptors,globalThis,is,isObject,objectHasOwnProperty,values,weaksetHas,constantProperties,sharedGlobalPropertyNames,universalPropertyNames,permitted;$h͏_imports([["./cauterize-property.js",[["cauterizeProperty",[$h͏_a=>cauterizeProperty=$h͏_a]]]],["./commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]],["WeakSet",[$h͏_a=>WeakSet=$h͏_a]],["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["is",[$h͏_a=>is=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["values",[$h͏_a=>values=$h͏_a]],["weaksetHas",[$h͏_a=>weaksetHas=$h͏_a]]]],["./permits.js",[["constantProperties",[$h͏_a=>constantProperties=$h͏_a]],["sharedGlobalPropertyNames",[$h͏_a=>sharedGlobalPropertyNames=$h͏_a]],["universalPropertyNames",[$h͏_a=>universalPropertyNames=$h͏_a]],["permitted",[$h͏_a=>permitted=$h͏_a]]]]]);const isFunction=obj=>"function"==typeof obj;function initProperty(obj,name,desc){if(objectHasOwnProperty(obj,name)){const preDesc=getOwnPropertyDescriptor(obj,name);if(!preDesc||!is(preDesc.value,desc.value)||preDesc.get!==desc.get||preDesc.set!==desc.set||preDesc.writable!==desc.writable||preDesc.enumerable!==desc.enumerable||preDesc.configurable!==desc.configurable)throw TypeError(`Conflicting definitions of ${name}`)}defineProperty(obj,name,desc)}function sampleGlobals(globalObject,newPropertyNames){const newIntrinsics={__proto__:null};for(const[globalName,intrinsicName]of entries(newPropertyNames))objectHasOwnProperty(globalObject,globalName)&&(newIntrinsics[intrinsicName]=globalObject[globalName]);return newIntrinsics}const makeIntrinsicsCollector=reporter=>{const intrinsics=create(null);let pseudoNatives;const addIntrinsics=newIntrinsics=>{!function(obj,descs){for(const[name,desc]of entries(descs))initProperty(obj,name,desc)}(intrinsics,getOwnPropertyDescriptors(newIntrinsics))};freeze(addIntrinsics);const completePrototypes=()=>{for(const[name,intrinsic]of entries(intrinsics)){if(!isObject(intrinsic))continue;if(!objectHasOwnProperty(intrinsic,"prototype"))continue;const permit=permitted[name];if("object"!=typeof permit)throw TypeError(`Expected permit object at permits.${name}`);const namePrototype=permit.prototype;if(!namePrototype){cauterizeProperty(intrinsic,"prototype",!1,`${name}.prototype`,reporter);continue}if("string"!=typeof namePrototype||!objectHasOwnProperty(permitted,namePrototype))throw TypeError(`Unrecognized ${name}.prototype permits entry`);const intrinsicPrototype=intrinsic.prototype;if(objectHasOwnProperty(intrinsics,namePrototype)){if(intrinsics[namePrototype]!==intrinsicPrototype)throw TypeError(`Conflicting bindings of ${namePrototype}`)}else intrinsics[namePrototype]=intrinsicPrototype}};freeze(completePrototypes);const finalIntrinsics=()=>(freeze(intrinsics),pseudoNatives=new WeakSet(arrayFilter(values(intrinsics),isFunction)),intrinsics);freeze(finalIntrinsics);const isPseudoNative=obj=>{if(!pseudoNatives)throw TypeError("isPseudoNative can only be called after finalIntrinsics");return weaksetHas(pseudoNatives,obj)};freeze(isPseudoNative);const intrinsicsCollector={addIntrinsics:addIntrinsics,completePrototypes:completePrototypes,finalIntrinsics:finalIntrinsics,isPseudoNative:isPseudoNative};return freeze(intrinsicsCollector),addIntrinsics(constantProperties),addIntrinsics(sampleGlobals(globalThis,universalPropertyNames)),intrinsicsCollector};$h͏_once.makeIntrinsicsCollector(makeIntrinsicsCollector);$h͏_once.getGlobalIntrinsics(((globalObject,reporter)=>{const{addIntrinsics:addIntrinsics,finalIntrinsics:finalIntrinsics}=makeIntrinsicsCollector(reporter);return addIntrinsics(sampleGlobals(globalObject,sharedGlobalPropertyNames)),finalIntrinsics()}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let permitted,FunctionInstance,isAccessorPermit,Map,String,Symbol,TypeError,arrayFilter,arrayIncludes,arrayMap,entries,getOwnPropertyDescriptor,getPrototypeOf,isObject,mapGet,objectHasOwnProperty,ownKeys,symbolKeyFor,cauterizeProperty;$h͏_imports([["./permits.js",[["permitted",[$h͏_a=>permitted=$h͏_a]],["FunctionInstance",[$h͏_a=>FunctionInstance=$h͏_a]],["isAccessorPermit",[$h͏_a=>isAccessorPermit=$h͏_a]]]],["./commons.js",[["Map",[$h͏_a=>Map=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["Symbol",[$h͏_a=>Symbol=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["arrayIncludes",[$h͏_a=>arrayIncludes=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["symbolKeyFor",[$h͏_a=>symbolKeyFor=$h͏_a]]]],["./cauterize-property.js",[["cauterizeProperty",[$h͏_a=>cauterizeProperty=$h͏_a]]]]]),$h͏_once.default((function(intrinsics,markVirtualizedNativeFunction,reporter){const primitives=["undefined","boolean","number","string","symbol"],wellKnownSymbolNames=new Map(Symbol?arrayMap(arrayFilter(entries(permitted["%SharedSymbol%"]),(([name,permit])=>"symbol"===permit&&"symbol"==typeof Symbol[name])),(([name])=>[Symbol[name],`@@${name}`])):[]);function asStringPropertyName(path,prop){if("string"==typeof prop)return prop;const wellKnownSymbol=mapGet(wellKnownSymbolNames,prop);if("symbol"==typeof prop){if(wellKnownSymbol)return wellKnownSymbol;{const registeredKey=symbolKeyFor(prop);return void 0!==registeredKey?`RegisteredSymbol(${registeredKey})`:`Unique${String(prop)}`}}throw TypeError(`Unexpected property name type ${path} ${prop}`)}function isAllowedPropertyValue(path,value,prop,permit){if("object"==typeof permit)return visitProperties(path,value,permit),!0;if(!1===permit)return!1;if("string"==typeof permit)if("prototype"===prop||"constructor"===prop){if(objectHasOwnProperty(intrinsics,permit)){if(value!==intrinsics[permit])throw TypeError(`Does not match permit for ${path}`);return!0}}else if(arrayIncludes(primitives,permit)){if(typeof value!==permit)throw TypeError(`At ${path} expected ${permit} not ${typeof value}`);return!0}throw TypeError(`Unexpected property ${prop} with permit ${permit} at ${path}`)}function isAllowedProperty(path,obj,prop,permit){const desc=getOwnPropertyDescriptor(obj,prop);if(!desc)throw TypeError(`Property ${prop} not found at ${path}`);if(objectHasOwnProperty(desc,"value")){if(isAccessorPermit(permit))throw TypeError(`Accessor expected at ${path}`);return isAllowedPropertyValue(path,desc.value,prop,permit)}if(!isAccessorPermit(permit))throw TypeError(`Accessor not expected at ${path}`);return isAllowedPropertyValue(`${path}<get>`,desc.get,prop,permit.get)&&isAllowedPropertyValue(`${path}<set>`,desc.set,prop,permit.set)}function getSubPermit(obj,permit,prop){const permitProp="__proto__"===prop?"--proto--":prop;return objectHasOwnProperty(permit,permitProp)?permit[permitProp]:"function"==typeof obj&&objectHasOwnProperty(FunctionInstance,permitProp)?FunctionInstance[permitProp]:void 0}function visitProperties(path,obj,permit){if(null==obj)return;!function(path,obj,protoName){if(!isObject(obj))throw TypeError(`Object expected: ${path}, ${obj}, ${protoName}`);const proto=getPrototypeOf(obj);if(null!==proto||null!==protoName){if(void 0!==protoName&&"string"!=typeof protoName)throw TypeError(`Malformed permit ${path}.__proto__`);if(proto!==intrinsics[protoName||"%ObjectPrototype%"])throw TypeError(`Unexpected [[Prototype]] at ${path}.__proto__ (expected ${protoName||"%ObjectPrototype%"})`)}}(path,obj,permit["[[Proto]]"]),"function"==typeof obj&&markVirtualizedNativeFunction(obj);for(const prop of ownKeys(obj)){const propString=asStringPropertyName(path,prop),subPath=`${path}.${propString}`,subPermit=getSubPermit(obj,permit,propString);subPermit&&isAllowedProperty(subPath,obj,prop,subPermit)||cauterizeProperty(obj,prop,!1===subPermit,subPath,reporter)}}visitProperties("intrinsics",intrinsics,permitted)}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_FUNCTION,SyntaxError,TypeError,defineProperties,getPrototypeOf,setPrototypeOf,freeze,AsyncGeneratorFunctionInstance;$h͏_imports([["./commons.js",[["FERAL_FUNCTION",[$h͏_a=>FERAL_FUNCTION=$h͏_a]],["SyntaxError",[$h͏_a=>SyntaxError=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]],["setPrototypeOf",[$h͏_a=>setPrototypeOf=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["AsyncGeneratorFunctionInstance",[$h͏_a=>AsyncGeneratorFunctionInstance=$h͏_a]]]]]),$h͏_once.default((function(){try{FERAL_FUNCTION.prototype.constructor("return 1")}catch(ignore){return freeze({})}const newIntrinsics={};function repairFunction(name,intrinsicName,declaration){let FunctionInstance;try{FunctionInstance=(0,eval)(declaration)}catch(e){if(e instanceof SyntaxError)return;throw e}const FunctionPrototype=getPrototypeOf(FunctionInstance),InertConstructor=function(){throw TypeError("Function.prototype.constructor is not a valid constructor.")};defineProperties(InertConstructor,{prototype:{value:FunctionPrototype},name:{value:name,writable:!1,enumerable:!1,configurable:!0}}),defineProperties(FunctionPrototype,{constructor:{value:InertConstructor}}),InertConstructor!==FERAL_FUNCTION.prototype.constructor&&setPrototypeOf(InertConstructor,FERAL_FUNCTION.prototype.constructor),newIntrinsics[intrinsicName]=InertConstructor}return repairFunction("Function","%InertFunction%","(function(){})"),repairFunction("GeneratorFunction","%InertGeneratorFunction%","(function*(){})"),repairFunction("AsyncFunction","%InertAsyncFunction%","(async function(){})"),void 0!==AsyncGeneratorFunctionInstance&&repairFunction("AsyncGeneratorFunction","%InertAsyncGeneratorFunction%","(async function*(){})"),newIntrinsics}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Date,TypeError,apply,construct,defineProperties;$h͏_imports([["./commons.js",[["Date",[$h͏_a=>Date=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["construct",[$h͏_a=>construct=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]]]]]),$h͏_once.default((function(){const OriginalDate=Date,DatePrototype=OriginalDate.prototype,tamedMethods={now(){throw TypeError("secure mode Calling %SharedDate%.now() throws")}},makeDateConstructor=({powers:powers="none"}={})=>{let ResultDate;return ResultDate="original"===powers?function(...rest){return void 0===new.target?apply(OriginalDate,void 0,rest):construct(OriginalDate,rest,new.target)}:function(...rest){if(void 0===new.target)throw TypeError("secure mode Calling %SharedDate% constructor as a function throws");if(0===rest.length)throw TypeError("secure mode Calling new %SharedDate%() with no arguments throws");return construct(OriginalDate,rest,new.target)},defineProperties(ResultDate,{length:{value:7},prototype:{value:DatePrototype,writable:!1,enumerable:!1,configurable:!1},parse:{value:OriginalDate.parse,writable:!0,enumerable:!1,configurable:!0},UTC:{value:OriginalDate.UTC,writable:!0,enumerable:!1,configurable:!0}}),ResultDate},InitialDate=makeDateConstructor({powers:"original"}),SharedDate=makeDateConstructor({powers:"none"});return defineProperties(InitialDate,{now:{value:OriginalDate.now,writable:!0,enumerable:!1,configurable:!0}}),defineProperties(SharedDate,{now:{value:tamedMethods.now,writable:!0,enumerable:!1,configurable:!0}}),defineProperties(DatePrototype,{constructor:{value:SharedDate}}),{"%InitialDate%":InitialDate,"%SharedDate%":SharedDate}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Math,TypeError,create,getOwnPropertyDescriptors,objectPrototype;$h͏_imports([["./commons.js",[["Math",[$h͏_a=>Math=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["objectPrototype",[$h͏_a=>objectPrototype=$h͏_a]]]]]),$h͏_once.default((function(){const originalMath=Math,initialMath=originalMath,{random:_,...otherDescriptors}=getOwnPropertyDescriptors(originalMath);return{"%InitialMath%":initialMath,"%SharedMath%":create(objectPrototype,{...otherDescriptors,random:{value:{random(){throw TypeError("secure mode %SharedMath%.random() throws")}}.random,writable:!0,enumerable:!1,configurable:!0}})}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_REG_EXP,TypeError,construct,defineProperties,getOwnPropertyDescriptor,speciesSymbol;$h͏_imports([["./commons.js",[["FERAL_REG_EXP",[$h͏_a=>FERAL_REG_EXP=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["construct",[$h͏_a=>construct=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["speciesSymbol",[$h͏_a=>speciesSymbol=$h͏_a]]]]]),$h͏_once.default((function(regExpTaming="safe"){const RegExpPrototype=FERAL_REG_EXP.prototype,makeRegExpConstructor=(_={})=>{const ResultRegExp=function(...rest){return void 0===new.target?FERAL_REG_EXP(...rest):construct(FERAL_REG_EXP,rest,new.target)};if(defineProperties(ResultRegExp,{length:{value:2},prototype:{value:RegExpPrototype,writable:!1,enumerable:!1,configurable:!1}}),speciesSymbol){const speciesDesc=getOwnPropertyDescriptor(FERAL_REG_EXP,speciesSymbol);if(!speciesDesc)throw TypeError("no RegExp[Symbol.species] descriptor");defineProperties(ResultRegExp,{[speciesSymbol]:speciesDesc})}return ResultRegExp},InitialRegExp=makeRegExpConstructor(),SharedRegExp=makeRegExpConstructor();return"unsafe"!==regExpTaming&&delete RegExpPrototype.compile,defineProperties(RegExpPrototype,{constructor:{value:SharedRegExp}}),{"%InitialRegExp%":InitialRegExp,"%SharedRegExp%":SharedRegExp}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let toStringTagSymbol,iteratorSymbol;$h͏_imports([["./commons.js",[["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]],["iteratorSymbol",[$h͏_a=>iteratorSymbol=$h͏_a]]]]]);const minEnablements={"%ObjectPrototype%":{toString:!0},"%FunctionPrototype%":{toString:!0},"%ErrorPrototype%":{name:!0},"%IteratorPrototype%":{toString:!0,constructor:!0,[toStringTagSymbol]:!0}};$h͏_once.minEnablements(minEnablements);const moderateEnablements={"%ObjectPrototype%":{toString:!0,valueOf:!0},"%ArrayPrototype%":{toString:!0,push:!0,concat:!0,[iteratorSymbol]:!0},"%FunctionPrototype%":{constructor:!0,bind:!0,toString:!0},"%ErrorPrototype%":{constructor:!0,message:!0,name:!0,toString:!0},"%TypeErrorPrototype%":{constructor:!0,message:!0,name:!0},"%SyntaxErrorPrototype%":{message:!0,name:!0},"%RangeErrorPrototype%":{message:!0,name:!0},"%URIErrorPrototype%":{message:!0,name:!0},"%EvalErrorPrototype%":{message:!0,name:!0},"%ReferenceErrorPrototype%":{message:!0,name:!0},"%AggregateErrorPrototype%":{message:!0,name:!0},"%PromisePrototype%":{constructor:!0},"%TypedArrayPrototype%":"*","%Generator%":{constructor:!0,name:!0,toString:!0},"%IteratorPrototype%":{toString:!0,constructor:!0,[toStringTagSymbol]:!0}};$h͏_once.moderateEnablements(moderateEnablements);const severeEnablements={...moderateEnablements,"%ObjectPrototype%":"*","%TypedArrayPrototype%":"*","%MapPrototype%":"*","%SetPrototype%":"*"};$h͏_once.severeEnablements(severeEnablements)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Set,String,TypeError,arrayForEach,defineProperty,getOwnPropertyDescriptor,getOwnPropertyDescriptors,isObject,objectHasOwnProperty,ownKeys,setHas,minEnablements,moderateEnablements,severeEnablements;$h͏_imports([["./commons.js",[["Set",[$h͏_a=>Set=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayForEach",[$h͏_a=>arrayForEach=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["setHas",[$h͏_a=>setHas=$h͏_a]]]],["./enablements.js",[["minEnablements",[$h͏_a=>minEnablements=$h͏_a]],["moderateEnablements",[$h͏_a=>moderateEnablements=$h͏_a]],["severeEnablements",[$h͏_a=>severeEnablements=$h͏_a]]]]]),$h͏_once.default((function(intrinsics,overrideTaming,{warn:warn},overrideDebug=[]){const debugProperties=new Set(overrideDebug);function enable(path,obj,prop,desc){if("value"in desc&&desc.configurable){const{value:value}=desc,isDebug=setHas(debugProperties,prop),{get:getter,set:setter}=getOwnPropertyDescriptor({get[prop](){return value},set[prop](newValue){if(obj===this)throw TypeError(`Cannot assign to read only property '${String(prop)}' of '${path}'`);objectHasOwnProperty(this,prop)?this[prop]=newValue:(isDebug&&warn(TypeError(`Override property ${prop}`)),defineProperty(this,prop,{value:newValue,writable:!0,enumerable:!0,configurable:!0}))}},prop);defineProperty(getter,"originalValue",{value:value,writable:!1,enumerable:!1,configurable:!1}),defineProperty(obj,prop,{get:getter,set:setter,enumerable:desc.enumerable,configurable:desc.configurable})}}function enableProperty(path,obj,prop){const desc=getOwnPropertyDescriptor(obj,prop);desc&&enable(path,obj,prop,desc)}function enableAllProperties(path,obj){const descs=getOwnPropertyDescriptors(obj);descs&&arrayForEach(ownKeys(descs),(prop=>enable(path,obj,prop,descs[prop])))}let plan;switch(overrideTaming){case"min":plan=minEnablements;break;case"moderate":plan=moderateEnablements;break;case"severe":plan=severeEnablements;break;default:throw TypeError(`unrecognized overrideTaming ${overrideTaming}`)}!function enableProperties(path,obj,plan){for(const prop of ownKeys(plan)){const desc=getOwnPropertyDescriptor(obj,prop);if(!desc||desc.get||desc.set)continue;const subPath=`${path}.${String(prop)}`,subPlan=plan[prop];if(!0===subPlan)enableProperty(subPath,obj,prop);else if("*"===subPlan)enableAllProperties(subPath,desc.value);else{if(!isObject(subPlan))throw TypeError(`Unexpected override enablement plan ${subPath}`);enableProperties(subPath,desc.value,subPlan)}}}("root",intrinsics,plan)}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Number,String,TypeError,defineProperty,getOwnPropertyNames,isObject,regexpExec,assert;$h͏_imports([["./commons.js",[["Number",[$h͏_a=>Number=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["getOwnPropertyNames",[$h͏_a=>getOwnPropertyNames=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["regexpExec",[$h͏_a=>regexpExec=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{Fail:Fail,quote:q}=assert,localePattern=/^(\w*[a-z])Locale([A-Z]\w*)$/,tamedMethods={localeCompare(arg){if(null==this)throw TypeError('Cannot localeCompare with null or undefined "this" value');const s=`${this}`,that=`${arg}`;return s<that?-1:s>that?1:(s===that||Fail`expected ${q(s)} and ${q(that)} to compare`,0)},toString(){return`${this}`}},nonLocaleCompare=tamedMethods.localeCompare,numberToString=tamedMethods.toString;$h͏_once.default((function(intrinsics,localeTaming="safe"){if("unsafe"!==localeTaming){defineProperty(String.prototype,"localeCompare",{value:nonLocaleCompare});for(const intrinsicName of getOwnPropertyNames(intrinsics)){const intrinsic=intrinsics[intrinsicName];if(isObject(intrinsic))for(const methodName of getOwnPropertyNames(intrinsic)){const match=regexpExec(localePattern,methodName);if(match){"function"==typeof intrinsic[methodName]||Fail`expected ${q(methodName)} to be a function`;const nonLocaleMethodName=`${match[1]}${match[2]}`,method=intrinsic[nonLocaleMethodName];"function"==typeof method||Fail`function ${q(nonLocaleMethodName)} not found`,defineProperty(intrinsic,methodName,{value:method})}}}defineProperty(Number.prototype,"toLocaleString",{value:numberToString})}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([]);$h͏_once.makeEvalFunction((evaluator=>source=>"string"!=typeof source?source:evaluator(source)))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_FUNCTION,arrayJoin,arrayPop,defineProperties,getPrototypeOf,assert;$h͏_imports([["./commons.js",[["FERAL_FUNCTION",[$h͏_a=>FERAL_FUNCTION=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["arrayPop",[$h͏_a=>arrayPop=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{Fail:Fail}=assert;$h͏_once.makeFunctionConstructor((evaluator=>{const newFunction=function(_body){const bodyText=`${arrayPop(arguments)||""}`,parameters=`${arrayJoin(arguments,",")}`;new FERAL_FUNCTION(parameters,""),new FERAL_FUNCTION(bodyText);return evaluator(`(function anonymous(${parameters}\n) {\n${bodyText}\n})`)};return defineProperties(newFunction,{prototype:{value:FERAL_FUNCTION.prototype,writable:!1,enumerable:!1,configurable:!1}}),getPrototypeOf(FERAL_FUNCTION)===FERAL_FUNCTION.prototype||Fail`Function prototype is the same accross compartments`,getPrototypeOf(newFunction)===FERAL_FUNCTION.prototype||Fail`Function constructor prototype is the same across compartments`,newFunction}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let TypeError,assign,create,defineProperty,entries,freeze,objectHasOwnProperty,unscopablesSymbol,makeEvalFunction,makeFunctionConstructor,constantProperties,universalPropertyNames;$h͏_imports([["./commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]],["assign",[$h͏_a=>assign=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["unscopablesSymbol",[$h͏_a=>unscopablesSymbol=$h͏_a]]]],["./make-eval-function.js",[["makeEvalFunction",[$h͏_a=>makeEvalFunction=$h͏_a]]]],["./make-function-constructor.js",[["makeFunctionConstructor",[$h͏_a=>makeFunctionConstructor=$h͏_a]]]],["./permits.js",[["constantProperties",[$h͏_a=>constantProperties=$h͏_a]],["universalPropertyNames",[$h͏_a=>universalPropertyNames=$h͏_a]]]]]);$h͏_once.setGlobalObjectSymbolUnscopables((globalObject=>{defineProperty(globalObject,unscopablesSymbol,freeze(assign(create(null),{set:freeze((()=>{throw TypeError("Cannot set Symbol.unscopables of a Compartment's globalThis")})),enumerable:!1,configurable:!1})))}));$h͏_once.setGlobalObjectConstantProperties((globalObject=>{for(const[name,constant]of entries(constantProperties))defineProperty(globalObject,name,{value:constant,writable:!1,enumerable:!1,configurable:!1})}));$h͏_once.setGlobalObjectMutableProperties(((globalObject,{intrinsics:intrinsics,newGlobalPropertyNames:newGlobalPropertyNames,makeCompartmentConstructor:makeCompartmentConstructor,markVirtualizedNativeFunction:markVirtualizedNativeFunction,parentCompartment:parentCompartment})=>{for(const[name,intrinsicName]of entries(universalPropertyNames))objectHasOwnProperty(intrinsics,intrinsicName)&&defineProperty(globalObject,name,{value:intrinsics[intrinsicName],writable:!0,enumerable:!1,configurable:!0});for(const[name,intrinsicName]of entries(newGlobalPropertyNames))objectHasOwnProperty(intrinsics,intrinsicName)&&defineProperty(globalObject,name,{value:intrinsics[intrinsicName],writable:!0,enumerable:!1,configurable:!0});const perCompartmentGlobals={globalThis:globalObject};perCompartmentGlobals.Compartment=freeze(makeCompartmentConstructor(makeCompartmentConstructor,intrinsics,markVirtualizedNativeFunction,{parentCompartment:parentCompartment,enforceNew:!0}));for(const[name,value]of entries(perCompartmentGlobals))defineProperty(globalObject,name,{value:value,writable:!0,enumerable:!1,configurable:!0}),"function"==typeof value&&markVirtualizedNativeFunction(value)}));$h͏_once.setGlobalObjectEvaluators(((globalObject,evaluator,markVirtualizedNativeFunction)=>{{const f=freeze(makeEvalFunction(evaluator));markVirtualizedNativeFunction(f),defineProperty(globalObject,"eval",{value:f,writable:!0,enumerable:!1,configurable:!0})}{const f=freeze(makeFunctionConstructor(evaluator));markVirtualizedNativeFunction(f),defineProperty(globalObject,"Function",{value:f,writable:!0,enumerable:!1,configurable:!0})}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Proxy,String,TypeError,ReferenceError,create,freeze,getOwnPropertyDescriptors,assert;$h͏_imports([["./commons.js",[["Proxy",[$h͏_a=>Proxy=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["ReferenceError",[$h͏_a=>ReferenceError=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{Fail:Fail,quote:q}=assert,objTarget=freeze({__proto__:null}),alwaysThrowHandler=new Proxy(objTarget,freeze({get(_shadow,prop){Fail`Please report unexpected scope handler trap: ${q(String(prop))}`}}));$h͏_once.alwaysThrowHandler(alwaysThrowHandler);const strictScopeTerminatorHandler=freeze(create(alwaysThrowHandler,getOwnPropertyDescriptors({get(_shadow,_prop){},set(_shadow,prop,_value){throw ReferenceError(`${String(prop)} is not defined`)},has:(_shadow,prop)=>!0,getPrototypeOf:_shadow=>null,getOwnPropertyDescriptor(_shadow,prop){const quotedProp=q(String(prop));console.warn(`getOwnPropertyDescriptor trap on scopeTerminatorHandler for ${quotedProp}`,TypeError().stack)},ownKeys:_shadow=>[]})));$h͏_once.strictScopeTerminatorHandler(strictScopeTerminatorHandler);const strictScopeTerminator=new Proxy(objTarget,strictScopeTerminatorHandler);$h͏_once.strictScopeTerminator(strictScopeTerminator)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Proxy,create,freeze,getOwnPropertyDescriptors,reflectSet,strictScopeTerminatorHandler,alwaysThrowHandler;$h͏_imports([["./commons.js",[["Proxy",[$h͏_a=>Proxy=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["reflectSet",[$h͏_a=>reflectSet=$h͏_a]]]],["./strict-scope-terminator.js",[["strictScopeTerminatorHandler",[$h͏_a=>strictScopeTerminatorHandler=$h͏_a]],["alwaysThrowHandler",[$h͏_a=>alwaysThrowHandler=$h͏_a]]]]]);const objTarget=freeze({__proto__:null}),createSloppyGlobalsScopeTerminator=globalObject=>{const scopeProxyHandlerProperties={...strictScopeTerminatorHandler,set:(_shadow,prop,value)=>reflectSet(globalObject,prop,value),has:(_shadow,_prop)=>!0},sloppyGlobalsScopeTerminatorHandler=freeze(create(alwaysThrowHandler,getOwnPropertyDescriptors(scopeProxyHandlerProperties)));return new Proxy(objTarget,sloppyGlobalsScopeTerminatorHandler)};$h͏_once.createSloppyGlobalsScopeTerminator(createSloppyGlobalsScopeTerminator),freeze(createSloppyGlobalsScopeTerminator)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_EVAL,create,defineProperties,freeze,assert;$h͏_imports([["./commons.js",[["FERAL_EVAL",[$h͏_a=>FERAL_EVAL=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{Fail:Fail}=assert;$h͏_once.makeEvalScopeKit((()=>{const evalScope=create(null),oneTimeEvalProperties=freeze({eval:{get:()=>(delete evalScope.eval,FERAL_EVAL),enumerable:!1,configurable:!0}}),evalScopeKit={evalScope:evalScope,allowNextEvalToBeUnsafe(){const{revoked:revoked}=evalScopeKit;null!==revoked&&Fail`a handler did not reset allowNextEvalToBeUnsafe ${revoked.err}`,defineProperties(evalScope,oneTimeEvalProperties)},revoked:null};return evalScopeKit}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_REG_EXP,regexpExec,stringSlice;$h͏_imports([["./commons.js",[["FERAL_REG_EXP",[$h͏_a=>FERAL_REG_EXP=$h͏_a]],["regexpExec",[$h͏_a=>regexpExec=$h͏_a]],["stringSlice",[$h͏_a=>stringSlice=$h͏_a]]]]]);const sourceMetaEntriesRegExp=new FERAL_REG_EXP("(?:\\s*//\\s*[@#]\\s*([a-zA-Z][a-zA-Z0-9]*)\\s*=\\s*([^\\s\\*]*)|/\\*\\s*[@#]\\s*([a-zA-Z][a-zA-Z0-9]*)\\s*=\\s*([^\\s\\*]*)\\s*\\*/)\\s*$");$h͏_once.getSourceURL((src=>{let sourceURL="<unknown>";for(;src.length>0;){const match=regexpExec(sourceMetaEntriesRegExp,src);if(null===match)break;src=stringSlice(src,0,src.length-match[0].length),"sourceURL"===match[3]?sourceURL=match[4]:"sourceURL"===match[1]&&(sourceURL=match[2])}return sourceURL}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_REG_EXP,SyntaxError,stringReplace,stringSearch,stringSlice,stringSplit,freeze,getSourceURL;function getLineNumber(src,pattern){const index=stringSearch(src,pattern);if(index<0)return-1;const adjustment="\n"===src[index]?1:0;return stringSplit(stringSlice(src,0,index),"\n").length+adjustment}$h͏_imports([["./commons.js",[["FERAL_REG_EXP",[$h͏_a=>FERAL_REG_EXP=$h͏_a]],["SyntaxError",[$h͏_a=>SyntaxError=$h͏_a]],["stringReplace",[$h͏_a=>stringReplace=$h͏_a]],["stringSearch",[$h͏_a=>stringSearch=$h͏_a]],["stringSlice",[$h͏_a=>stringSlice=$h͏_a]],["stringSplit",[$h͏_a=>stringSplit=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]]]],["./get-source-url.js",[["getSourceURL",[$h͏_a=>getSourceURL=$h͏_a]]]]]);const htmlCommentPattern=new FERAL_REG_EXP("(?:\x3c!--|--\x3e)","g"),rejectHtmlComments=src=>{const lineNumber=getLineNumber(src,htmlCommentPattern);if(lineNumber<0)return src;const name=getSourceURL(src);throw SyntaxError(`Possible HTML comment rejected at ${name}:${lineNumber}. (SES_HTML_COMMENT_REJECTED)`)};$h͏_once.rejectHtmlComments(rejectHtmlComments);const evadeHtmlCommentTest=src=>stringReplace(src,htmlCommentPattern,(match=>"<"===match[0]?"< ! --":"-- >"));$h͏_once.evadeHtmlCommentTest(evadeHtmlCommentTest);const importPattern=new FERAL_REG_EXP("(^|[^.]|\\.\\.\\.)\\bimport(\\s*(?:\\(|/[/*]))","g"),rejectImportExpressions=src=>{const lineNumber=getLineNumber(src,importPattern);if(lineNumber<0)return src;const name=getSourceURL(src);throw SyntaxError(`Possible import expression rejected at ${name}:${lineNumber}. (SES_IMPORT_REJECTED)`)};$h͏_once.rejectImportExpressions(rejectImportExpressions);const evadeImportExpressionTest=src=>stringReplace(src,importPattern,((_,p1,p2)=>`${p1}__import__${p2}`));$h͏_once.evadeImportExpressionTest(evadeImportExpressionTest);const someDirectEvalPattern=new FERAL_REG_EXP("(^|[^.])\\beval(\\s*\\()","g"),rejectSomeDirectEvalExpressions=src=>{const lineNumber=getLineNumber(src,someDirectEvalPattern);if(lineNumber<0)return src;const name=getSourceURL(src);throw SyntaxError(`Possible direct eval expression rejected at ${name}:${lineNumber}. (SES_EVAL_REJECTED)`)};$h͏_once.rejectSomeDirectEvalExpressions(rejectSomeDirectEvalExpressions);const mandatoryTransforms=source=>(source=rejectHtmlComments(source),source=rejectImportExpressions(source));$h͏_once.mandatoryTransforms(mandatoryTransforms);const applyTransforms=(source,transforms)=>{for(let i=0,l=transforms.length;i<l;i+=1){source=(0,transforms[i])(source)}return source};$h͏_once.applyTransforms(applyTransforms);const transforms=freeze({rejectHtmlComments:freeze(rejectHtmlComments),evadeHtmlCommentTest:freeze(evadeHtmlCommentTest),rejectImportExpressions:freeze(rejectImportExpressions),evadeImportExpressionTest:freeze(evadeImportExpressionTest),rejectSomeDirectEvalExpressions:freeze(rejectSomeDirectEvalExpressions),mandatoryTransforms:freeze(mandatoryTransforms),applyTransforms:freeze(applyTransforms)});$h͏_once.transforms(transforms)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let arrayFilter,arrayIncludes,getOwnPropertyDescriptor,getOwnPropertyNames,objectHasOwnProperty,regexpTest;$h͏_imports([["./commons.js",[["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["arrayIncludes",[$h͏_a=>arrayIncludes=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getOwnPropertyNames",[$h͏_a=>getOwnPropertyNames=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]],["regexpTest",[$h͏_a=>regexpTest=$h͏_a]]]]]);const keywords=["await","break","case","catch","class","const","continue","debugger","default","delete","do","else","export","extends","finally","for","function","if","import","in","instanceof","new","return","super","switch","this","throw","try","typeof","var","void","while","with","yield","let","static","enum","implements","package","protected","interface","private","public","await","null","true","false","this","arguments"],identifierPattern=/^[a-zA-Z_$][\w$]*$/,isValidIdentifierName=name=>"eval"!==name&&!arrayIncludes(keywords,name)&&regexpTest(identifierPattern,name);function isImmutableDataProperty(obj,name){const desc=getOwnPropertyDescriptor(obj,name);return desc&&!1===desc.configurable&&!1===desc.writable&&objectHasOwnProperty(desc,"value")}$h͏_once.isValidIdentifierName(isValidIdentifierName);$h͏_once.getScopeConstants(((globalObject,moduleLexicals={})=>{const globalObjectNames=getOwnPropertyNames(globalObject),moduleLexicalNames=getOwnPropertyNames(moduleLexicals),moduleLexicalConstants=arrayFilter(moduleLexicalNames,(name=>isValidIdentifierName(name)&&isImmutableDataProperty(moduleLexicals,name)));return{globalObjectConstants:arrayFilter(globalObjectNames,(name=>!arrayIncludes(moduleLexicalNames,name)&&isValidIdentifierName(name)&&isImmutableDataProperty(globalObject,name))),moduleLexicalConstants:moduleLexicalConstants}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_FUNCTION,arrayJoin,apply,getScopeConstants;function buildOptimizer(constants,name){return 0===constants.length?"":`const {${arrayJoin(constants,",")}} = this.${name};`}$h͏_imports([["./commons.js",[["FERAL_FUNCTION",[$h͏_a=>FERAL_FUNCTION=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]]]],["./scope-constants.js",[["getScopeConstants",[$h͏_a=>getScopeConstants=$h͏_a]]]]]);$h͏_once.makeEvaluate((context=>{const{globalObjectConstants:globalObjectConstants,moduleLexicalConstants:moduleLexicalConstants}=getScopeConstants(context.globalObject,context.moduleLexicals),globalObjectOptimizer=buildOptimizer(globalObjectConstants,"globalObject"),moduleLexicalOptimizer=buildOptimizer(moduleLexicalConstants,"moduleLexicals"),evaluateFactory=FERAL_FUNCTION(`\n    with (this.scopeTerminator) {\n      with (this.globalObject) {\n        with (this.moduleLexicals) {\n          with (this.evalScope) {\n            ${globalObjectOptimizer}\n            ${moduleLexicalOptimizer}\n            return function() {\n              'use strict';\n              return eval(arguments[0]);\n            };\n          }\n        }\n      }\n    }\n  `);return apply(evaluateFactory,context,[])}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let apply,arrayFlatMap,freeze,identity,strictScopeTerminator,createSloppyGlobalsScopeTerminator,makeEvalScopeKit,applyTransforms,mandatoryTransforms,makeEvaluate,assert;$h͏_imports([["./commons.js",[["apply",[$h͏_a=>apply=$h͏_a]],["arrayFlatMap",[$h͏_a=>arrayFlatMap=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["identity",[$h͏_a=>identity=$h͏_a]]]],["./strict-scope-terminator.js",[["strictScopeTerminator",[$h͏_a=>strictScopeTerminator=$h͏_a]]]],["./sloppy-globals-scope-terminator.js",[["createSloppyGlobalsScopeTerminator",[$h͏_a=>createSloppyGlobalsScopeTerminator=$h͏_a]]]],["./eval-scope.js",[["makeEvalScopeKit",[$h͏_a=>makeEvalScopeKit=$h͏_a]]]],["./transforms.js",[["applyTransforms",[$h͏_a=>applyTransforms=$h͏_a]],["mandatoryTransforms",[$h͏_a=>mandatoryTransforms=$h͏_a]]]],["./make-evaluate.js",[["makeEvaluate",[$h͏_a=>makeEvaluate=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{Fail:Fail}=assert;$h͏_once.makeSafeEvaluator((({globalObject:globalObject,moduleLexicals:moduleLexicals={},globalTransforms:globalTransforms=[],sloppyGlobalsMode:sloppyGlobalsMode=!1})=>{const scopeTerminator=sloppyGlobalsMode?createSloppyGlobalsScopeTerminator(globalObject):strictScopeTerminator,evalScopeKit=makeEvalScopeKit(),{evalScope:evalScope}=evalScopeKit,evaluateContext=freeze({evalScope:evalScope,moduleLexicals:moduleLexicals,globalObject:globalObject,scopeTerminator:scopeTerminator});let evaluate;return{safeEvaluate:(source,options)=>{const{localTransforms:localTransforms=[]}=options||{};let err;evaluate||(evaluate=makeEvaluate(evaluateContext)),source=applyTransforms(source,arrayFlatMap([localTransforms,globalTransforms,[mandatoryTransforms]],identity));try{return evalScopeKit.allowNextEvalToBeUnsafe(),apply(evaluate,globalObject,[source])}catch(e){throw err=e,e}finally{const unsafeEvalWasStillExposed="eval"in evalScope;delete evalScope.eval,unsafeEvalWasStillExposed&&(evalScopeKit.revoked={err:err},Fail`handler did not reset allowNextEvalToBeUnsafe ${err}`)}}}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let WeakSet,defineProperty,freeze,functionPrototype,functionToString,stringEndsWith,weaksetAdd,weaksetHas;$h͏_imports([["./commons.js",[["WeakSet",[$h͏_a=>WeakSet=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["functionPrototype",[$h͏_a=>functionPrototype=$h͏_a]],["functionToString",[$h͏_a=>functionToString=$h͏_a]],["stringEndsWith",[$h͏_a=>stringEndsWith=$h͏_a]],["weaksetAdd",[$h͏_a=>weaksetAdd=$h͏_a]],["weaksetHas",[$h͏_a=>weaksetHas=$h͏_a]]]]]);let markVirtualizedNativeFunction;$h͏_once.tameFunctionToString((()=>{if(void 0===markVirtualizedNativeFunction){const virtualizedNativeFunctions=new WeakSet;defineProperty(functionPrototype,"toString",{value:{toString(){const str=functionToString(this);return stringEndsWith(str,") { [native code] }")||!weaksetHas(virtualizedNativeFunctions,this)?str:`function ${this.name}() { [native code] }`}}.toString}),markVirtualizedNativeFunction=freeze((func=>weaksetAdd(virtualizedNativeFunctions,func)))}return markVirtualizedNativeFunction}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let TypeError,globalThis,getOwnPropertyDescriptor,defineProperty;function tameDomains(domainTaming="safe"){if("unsafe"===domainTaming)return;const globalProcess=globalThis.process||void 0;if("object"==typeof globalProcess){const domainDescriptor=getOwnPropertyDescriptor(globalProcess,"domain");if(void 0!==domainDescriptor&&void 0!==domainDescriptor.get)throw TypeError("SES failed to lockdown, Node.js domains have been initialized (SES_NO_DOMAINS)");defineProperty(globalProcess,"domain",{value:null,configurable:!1,writable:!1,enumerable:!1})}}$h͏_imports([["./commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]]]]]),Object.defineProperty(tameDomains,"name",{value:"tameDomains"}),$h͏_once.tameDomains(tameDomains)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let functionPrototype,getPrototypeOf,globalThis,objectPrototype,setPrototypeOf;$h͏_imports([["./commons.js",[["functionPrototype",[$h͏_a=>functionPrototype=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["objectPrototype",[$h͏_a=>objectPrototype=$h͏_a]],["setPrototypeOf",[$h͏_a=>setPrototypeOf=$h͏_a]]]]]);$h͏_once.tameModuleSource((()=>{const newIntrinsics={},ModuleSource=globalThis.ModuleSource;if(void 0!==ModuleSource){function AbstractModuleSource(){}newIntrinsics.ModuleSource=ModuleSource;const ModuleSourceProto=getPrototypeOf(ModuleSource);ModuleSourceProto===functionPrototype?(setPrototypeOf(ModuleSource,AbstractModuleSource),newIntrinsics["%AbstractModuleSource%"]=AbstractModuleSource,newIntrinsics["%AbstractModuleSourcePrototype%"]=AbstractModuleSource.prototype):(newIntrinsics["%AbstractModuleSource%"]=ModuleSourceProto,newIntrinsics["%AbstractModuleSourcePrototype%"]=ModuleSourceProto.prototype);const ModuleSourcePrototype=ModuleSource.prototype;if(void 0!==ModuleSourcePrototype){newIntrinsics["%ModuleSourcePrototype%"]=ModuleSourcePrototype;getPrototypeOf(ModuleSourcePrototype)===objectPrototype&&setPrototypeOf(ModuleSource.prototype,AbstractModuleSource.prototype)}}return newIntrinsics}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let WeakSet,arrayFilter,arrayFlatMap,arrayMap,arrayPop,arrayPush,defineProperty,freeze,fromEntries,isError,stringEndsWith,stringIncludes,stringSplit,weaksetAdd,weaksetHas;$h͏_imports([["../commons.js",[["WeakSet",[$h͏_a=>WeakSet=$h͏_a]],["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["arrayFlatMap",[$h͏_a=>arrayFlatMap=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["arrayPop",[$h͏_a=>arrayPop=$h͏_a]],["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["fromEntries",[$h͏_a=>fromEntries=$h͏_a]],["isError",[$h͏_a=>isError=$h͏_a]],["stringEndsWith",[$h͏_a=>stringEndsWith=$h͏_a]],["stringIncludes",[$h͏_a=>stringIncludes=$h͏_a]],["stringSplit",[$h͏_a=>stringSplit=$h͏_a]],["weaksetAdd",[$h͏_a=>weaksetAdd=$h͏_a]],["weaksetHas",[$h͏_a=>weaksetHas=$h͏_a]]]]]);const defineName=(name,fn)=>defineProperty(fn,"name",{value:name}),consoleLevelMethods=freeze([["debug","debug"],["log","log"],["info","info"],["warn","warn"],["error","error"],["trace","log"],["dirxml","log"],["group","log"],["groupCollapsed","log"]]);$h͏_once.consoleLevelMethods(consoleLevelMethods);const consoleOtherMethods=freeze([["assert","error"],["timeLog","log"],["clear",void 0],["count","info"],["countReset",void 0],["dir","log"],["groupEnd","log"],["table","log"],["time","info"],["timeEnd","info"],["profile",void 0],["profileEnd",void 0],["timeStamp",void 0]]);$h͏_once.consoleOtherMethods(consoleOtherMethods);const consoleMethodPermits=freeze([...consoleLevelMethods,...consoleOtherMethods]),makeLoggingConsoleKit=(loggedErrorHandler,{shouldResetForDebugging:shouldResetForDebugging=!1}={})=>{shouldResetForDebugging&&loggedErrorHandler.resetErrorTagNum();let logArray=[];const loggingConsole=fromEntries(arrayMap(consoleMethodPermits,(([name,_])=>{const method=defineName(name,((...args)=>{arrayPush(logArray,[name,...args])}));return[name,freeze(method)]})));freeze(loggingConsole);const takeLog=()=>{const result=freeze(logArray);return logArray=[],result};freeze(takeLog);return freeze({loggingConsole:loggingConsole,takeLog:takeLog})};$h͏_once.makeLoggingConsoleKit(makeLoggingConsoleKit),freeze(makeLoggingConsoleKit);$h͏_once.pumpLogToConsole(((log,baseConsole)=>{for(const[name,...args]of log)baseConsole[name](...args)}));const ErrorInfo={NOTE:"ERROR_NOTE:",MESSAGE:"ERROR_MESSAGE:",CAUSE:"cause:",ERRORS:"errors:"};freeze(ErrorInfo);const makeCausalConsole=(baseConsole,loggedErrorHandler)=>{if(!baseConsole)return;const{getStackString:getStackString,tagError:tagError,takeMessageLogArgs:takeMessageLogArgs,takeNoteLogArgsArray:takeNoteLogArgsArray}=loggedErrorHandler,extractErrorArgs=(logArgs,subErrorsSink)=>arrayMap(logArgs,(arg=>isError(arg)?(arrayPush(subErrorsSink,arg),`(${tagError(arg)})`):arg)),logErrorInfo=(severity,error,kind,logArgs,subErrorsSink)=>{const errorTag=tagError(error),errorName=kind===ErrorInfo.MESSAGE?`${errorTag}:`:`${errorTag} ${kind}`,argTags=extractErrorArgs(logArgs,subErrorsSink);baseConsole[severity](errorName,...argTags)},logSubErrors=(severity,subErrors,optTag=undefined)=>{if(0===subErrors.length)return;if(1===subErrors.length&&void 0===optTag)return void logError(severity,subErrors[0]);let label;label=1===subErrors.length?"Nested error":`Nested ${subErrors.length} errors`,void 0!==optTag&&(label=`${label} under ${optTag}`),baseConsole.group(label);try{for(const subError of subErrors)logError(severity,subError)}finally{baseConsole.groupEnd&&baseConsole.groupEnd()}},errorsLogged=new WeakSet,logError=(severity,error)=>{if(weaksetHas(errorsLogged,error))return;const errorTag=tagError(error);weaksetAdd(errorsLogged,error);const subErrors=[],messageLogArgs=takeMessageLogArgs(error),noteLogArgsArray=takeNoteLogArgsArray(error,(severity=>(error,noteLogArgs)=>{const subErrors=[];logErrorInfo(severity,error,ErrorInfo.NOTE,noteLogArgs,subErrors),logSubErrors(severity,subErrors,tagError(error))})(severity));void 0===messageLogArgs?baseConsole[severity](`${errorTag}:`,error.message):logErrorInfo(severity,error,ErrorInfo.MESSAGE,messageLogArgs,subErrors);let stackString=getStackString(error);"string"==typeof stackString&&stackString.length>=1&&!stringEndsWith(stackString,"\n")&&(stackString+="\n"),baseConsole[severity](stackString),error.cause&&logErrorInfo(severity,error,ErrorInfo.CAUSE,[error.cause],subErrors),error.errors&&logErrorInfo(severity,error,ErrorInfo.ERRORS,error.errors,subErrors);for(const noteLogArgs of noteLogArgsArray)logErrorInfo(severity,error,ErrorInfo.NOTE,noteLogArgs,subErrors);logSubErrors(severity,subErrors,errorTag)},levelMethods=arrayMap(consoleLevelMethods,(([level,_])=>{const levelMethod=defineName(level,((...logArgs)=>{const subErrors=[],argTags=extractErrorArgs(logArgs,subErrors);baseConsole[level]&&baseConsole[level](...argTags),logSubErrors(level,subErrors)}));return[level,freeze(levelMethod)]})),otherMethodNames=arrayFilter(consoleOtherMethods,(([name,_])=>name in baseConsole)),otherMethods=arrayMap(otherMethodNames,(([name,_])=>{const otherMethod=defineName(name,((...args)=>{baseConsole[name](...args)}));return[name,freeze(otherMethod)]})),causalConsole=fromEntries([...levelMethods,...otherMethods]);return freeze(causalConsole)};$h͏_once.makeCausalConsole(makeCausalConsole),freeze(makeCausalConsole);const defineCausalConsoleFromLogger=loggedErrorHandler=>freeze((tlogger=>{const indents=[],logWithIndent=(...args)=>(indents.length>0&&(args=arrayFlatMap(args,(arg=>"string"==typeof arg&&stringIncludes(arg,"\n")?((str,sep,indents)=>{const[firstLine,...restLines]=stringSplit(str,sep);return["",firstLine,...arrayFlatMap(restLines,(line=>[sep,...indents,line]))]})(arg,"\n",indents):[arg])),args=[...indents,...args]),tlogger(...args)),baseConsole=fromEntries([...arrayMap(consoleLevelMethods,(([name])=>[name,defineName(name,((...args)=>logWithIndent(...args)))])),...arrayMap(consoleOtherMethods,(([name])=>[name,defineName(name,((...args)=>logWithIndent(name,...args)))]))]);for(const name of["group","groupCollapsed"])baseConsole[name]?baseConsole[name]=defineName(name,((...args)=>{args.length>=1&&logWithIndent(...args),arrayPush(indents," ")})):baseConsole[name]=defineName(name,(()=>{}));baseConsole.groupEnd=defineName("groupEnd",baseConsole.groupEnd?(...args)=>{arrayPop(indents)}:()=>{}),harden(baseConsole);return makeCausalConsole(baseConsole,loggedErrorHandler)}));$h͏_once.defineCausalConsoleFromLogger(defineCausalConsoleFromLogger),freeze(defineCausalConsoleFromLogger);const filterConsole=(baseConsole,filter,_topic=undefined)=>{const methodPermits=arrayFilter(consoleMethodPermits,(([name,_])=>name in baseConsole)),methods=arrayMap(methodPermits,(([name,severity])=>{const method=defineName(name,((...args)=>{(void 0===severity||filter.canLog(severity))&&baseConsole[name](...args)}));return[name,freeze(method)]})),filteringConsole=fromEntries(methods);return freeze(filteringConsole)};$h͏_once.filterConsole(filterConsole),freeze(filterConsole)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FinalizationRegistry,Map,mapGet,mapDelete,WeakMap,mapSet,finalizationRegistryRegister,weakmapSet,weakmapGet,mapEntries,mapHas;$h͏_imports([["../commons.js",[["FinalizationRegistry",[$h͏_a=>FinalizationRegistry=$h͏_a]],["Map",[$h͏_a=>Map=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["mapDelete",[$h͏_a=>mapDelete=$h͏_a]],["WeakMap",[$h͏_a=>WeakMap=$h͏_a]],["mapSet",[$h͏_a=>mapSet=$h͏_a]],["finalizationRegistryRegister",[$h͏_a=>finalizationRegistryRegister=$h͏_a]],["weakmapSet",[$h͏_a=>weakmapSet=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["mapEntries",[$h͏_a=>mapEntries=$h͏_a]],["mapHas",[$h͏_a=>mapHas=$h͏_a]]]]]);$h͏_once.makeRejectionHandlers((reportReason=>{if(void 0===FinalizationRegistry)return;let lastReasonId=0;const idToReason=new Map;let cancelChecking;const removeReasonId=reasonId=>{mapDelete(idToReason,reasonId),cancelChecking&&0===idToReason.size&&(cancelChecking(),cancelChecking=void 0)},promiseToReasonId=new WeakMap,promiseToReason=new FinalizationRegistry((heldReasonId=>{if(mapHas(idToReason,heldReasonId)){const reason=mapGet(idToReason,heldReasonId);removeReasonId(heldReasonId),reportReason(reason)}}));return{rejectionHandledHandler:pr=>{const reasonId=weakmapGet(promiseToReasonId,pr);removeReasonId(reasonId)},unhandledRejectionHandler:(reason,pr)=>{lastReasonId+=1;const reasonId=lastReasonId;mapSet(idToReason,reasonId,reason),weakmapSet(promiseToReasonId,pr,reasonId),finalizationRegistryRegister(promiseToReason,pr,reasonId,pr)},processTerminationHandler:()=>{for(const[reasonId,reason]of mapEntries(idToReason))removeReasonId(reasonId),reportReason(reason)}}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let TypeError,apply,defineProperty,freeze,globalThis,defaultHandler,makeCausalConsole,makeRejectionHandlers;$h͏_imports([["../commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./assert.js",[["loggedErrorHandler",[$h͏_a=>defaultHandler=$h͏_a]]]],["./console.js",[["makeCausalConsole",[$h͏_a=>makeCausalConsole=$h͏_a]]]],["./unhandled-rejection.js",[["makeRejectionHandlers",[$h͏_a=>makeRejectionHandlers=$h͏_a]]]]]);const failFast=message=>{throw TypeError(message)},wrapLogger=(logger,thisArg)=>freeze(((...args)=>apply(logger,thisArg,args)));$h͏_once.tameConsole(((consoleTaming="safe",errorTrapping="platform",unhandledRejectionTrapping="report",optGetStackString=undefined)=>{let loggedErrorHandler;loggedErrorHandler=void 0===optGetStackString?defaultHandler:{...defaultHandler,getStackString:optGetStackString};const originalConsole=void 0!==globalThis.console?globalThis.console:"function"==typeof globalThis.print?(p=wrapLogger(globalThis.print),freeze({debug:p,log:p,info:p,warn:p,error:p})):void 0;var p;if(originalConsole&&originalConsole.log)for(const methodName of["warn","error"])originalConsole[methodName]||defineProperty(originalConsole,methodName,{value:wrapLogger(originalConsole.log,originalConsole)});const ourConsole="unsafe"===consoleTaming?originalConsole:makeCausalConsole(originalConsole,loggedErrorHandler),globalProcess=globalThis.process||void 0;if("none"!==errorTrapping&&"object"==typeof globalProcess&&"function"==typeof globalProcess.on){let terminate;if("platform"===errorTrapping||"exit"===errorTrapping){const{exit:exit}=globalProcess;"function"==typeof exit||failFast("missing process.exit"),terminate=()=>exit(globalProcess.exitCode||-1)}else"abort"===errorTrapping&&(terminate=globalProcess.abort,"function"==typeof terminate||failFast("missing process.abort"));globalProcess.on("uncaughtException",(error=>{ourConsole.error("SES_UNCAUGHT_EXCEPTION:",error),terminate&&terminate()}))}if("none"!==unhandledRejectionTrapping&&"object"==typeof globalProcess&&"function"==typeof globalProcess.on){const h=makeRejectionHandlers((reason=>{ourConsole.error("SES_UNHANDLED_REJECTION:",reason)}));h&&(globalProcess.on("unhandledRejection",h.unhandledRejectionHandler),globalProcess.on("rejectionHandled",h.rejectionHandledHandler),globalProcess.on("exit",h.processTerminationHandler))}const globalWindow=globalThis.window||void 0;if("none"!==errorTrapping&&"object"==typeof globalWindow&&"function"==typeof globalWindow.addEventListener&&globalWindow.addEventListener("error",(event=>{event.preventDefault(),ourConsole.error("SES_UNCAUGHT_EXCEPTION:",event.error),"exit"!==errorTrapping&&"abort"!==errorTrapping||(globalWindow.location.href="about:blank")})),"none"!==unhandledRejectionTrapping&&"object"==typeof globalWindow&&"function"==typeof globalWindow.addEventListener){const h=makeRejectionHandlers((reason=>{ourConsole.error("SES_UNHANDLED_REJECTION:",reason)}));h&&(globalWindow.addEventListener("unhandledrejection",(event=>{event.preventDefault(),h.unhandledRejectionHandler(event.reason,event.promise)})),globalWindow.addEventListener("rejectionhandled",(event=>{event.preventDefault(),h.rejectionHandledHandler(event.promise)})),globalWindow.addEventListener("beforeunload",(_event=>{h.processTerminationHandler()})))}return{console:ourConsole}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let WeakMap,WeakSet,apply,arrayFilter,arrayJoin,arrayMap,arraySlice,create,defineProperties,fromEntries,reflectSet,regexpExec,regexpTest,weakmapGet,weakmapSet,weaksetAdd,weaksetHas,TypeError;$h͏_imports([["../commons.js",[["WeakMap",[$h͏_a=>WeakMap=$h͏_a]],["WeakSet",[$h͏_a=>WeakSet=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["arraySlice",[$h͏_a=>arraySlice=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["fromEntries",[$h͏_a=>fromEntries=$h͏_a]],["reflectSet",[$h͏_a=>reflectSet=$h͏_a]],["regexpExec",[$h͏_a=>regexpExec=$h͏_a]],["regexpTest",[$h͏_a=>regexpTest=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["weakmapSet",[$h͏_a=>weakmapSet=$h͏_a]],["weaksetAdd",[$h͏_a=>weaksetAdd=$h͏_a]],["weaksetHas",[$h͏_a=>weaksetHas=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]]]]]);const safeV8CallSiteMethodNames=["getTypeName","getFunctionName","getMethodName","getFileName","getLineNumber","getColumnNumber","getEvalOrigin","isToplevel","isEval","isNative","isConstructor","isAsync","getPosition","getScriptNameOrSourceURL","toString"],safeV8CallSiteFacet=callSite=>{const o=fromEntries(arrayMap(safeV8CallSiteMethodNames,(name=>{const method=callSite[name];return[name,()=>apply(method,callSite,[])]})));return create(o,{})},FILENAME_CENSORS=[/\/node_modules\//,/^(?:node:)?internal\//,/\/packages\/ses\/src\/error\/assert\.js$/,/\/packages\/eventual-send\/src\//,/\/packages\/ses-ava\/src\/ses-ava-test\.js$/],filterFileName=fileName=>{if(!fileName)return!0;for(const filter of FILENAME_CENSORS)if(regexpTest(filter,fileName))return!1;return!0};$h͏_once.filterFileName(filterFileName);const CALLSITE_PATTERNS=[/^((?:.*[( ])?)[:/\w_-]*\/\.\.\.\/(.+)$/,/^((?:.*[( ])?)\.\.\.\/(.+)$/,/^((?:.*[( ])?)[:/\w_-]*\/(packages\/.+)$/,/^((?:.*[( ])?)file:\/\/([^/].*)$/],shortenCallSiteString=callSiteString=>{for(const filter of CALLSITE_PATTERNS){const match=regexpExec(filter,callSiteString);if(match)return arrayJoin(arraySlice(match,1),"")}return callSiteString};$h͏_once.shortenCallSiteString(shortenCallSiteString);$h͏_once.tameV8ErrorConstructor(((OriginalError,InitialError,errorTaming,stackFiltering)=>{if("unsafe-debug"===errorTaming)throw TypeError("internal: v8+unsafe-debug special case should already be done");const originalCaptureStackTrace=OriginalError.captureStackTrace,omitFrames="concise"===stackFiltering||"omit-frames"===stackFiltering,shortenPaths="concise"===stackFiltering||"shorten-paths"===stackFiltering,callSiteFilter=callSite=>!omitFrames||filterFileName(callSite.getFileName()),callSiteStringifier=callSite=>{let callSiteString=`${callSite}`;return shortenPaths&&(callSiteString=shortenCallSiteString(callSiteString)),`\n  at ${callSiteString}`},stackStringFromSST=(_error,sst)=>arrayJoin(arrayMap(arrayFilter(sst,callSiteFilter),callSiteStringifier),""),stackInfos=new WeakMap,tamedMethods={captureStackTrace(error,optFn=tamedMethods.captureStackTrace){"function"!=typeof originalCaptureStackTrace?reflectSet(error,"stack",""):apply(originalCaptureStackTrace,OriginalError,[error,optFn])},getStackString(error){let stackInfo=weakmapGet(stackInfos,error);if(void 0===stackInfo&&(error.stack,stackInfo=weakmapGet(stackInfos,error),stackInfo||(stackInfo={stackString:""},weakmapSet(stackInfos,error,stackInfo))),void 0!==stackInfo.stackString)return stackInfo.stackString;const stackString=stackStringFromSST(0,stackInfo.callSites);return weakmapSet(stackInfos,error,{stackString:stackString}),stackString},prepareStackTrace(error,sst){if("unsafe"===errorTaming){const stackString=stackStringFromSST(0,sst);return weakmapSet(stackInfos,error,{stackString:stackString}),`${error}${stackString}`}return weakmapSet(stackInfos,error,{callSites:sst}),""}},defaultPrepareFn=tamedMethods.prepareStackTrace;OriginalError.prepareStackTrace=defaultPrepareFn;const systemPrepareFnSet=new WeakSet([defaultPrepareFn]),systemPrepareFnFor=inputPrepareFn=>{if(weaksetHas(systemPrepareFnSet,inputPrepareFn))return inputPrepareFn;const systemMethods={prepareStackTrace:(error,sst)=>(weakmapSet(stackInfos,error,{callSites:sst}),inputPrepareFn(error,(sst=>arrayMap(sst,safeV8CallSiteFacet))(sst)))};return weaksetAdd(systemPrepareFnSet,systemMethods.prepareStackTrace),systemMethods.prepareStackTrace};return defineProperties(InitialError,{captureStackTrace:{value:tamedMethods.captureStackTrace,writable:!0,enumerable:!1,configurable:!0},prepareStackTrace:{get:()=>OriginalError.prepareStackTrace,set(inputPrepareStackTraceFn){if("function"==typeof inputPrepareStackTraceFn){const systemPrepareFn=systemPrepareFnFor(inputPrepareStackTraceFn);OriginalError.prepareStackTrace=systemPrepareFn}else OriginalError.prepareStackTrace=defaultPrepareFn},enumerable:!1,configurable:!0}}),tamedMethods.getStackString}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_ERROR,apply,construct,defineProperties,setPrototypeOf,getOwnPropertyDescriptor,defineProperty,getOwnPropertyDescriptors,NativeErrors,tameV8ErrorConstructor;$h͏_imports([["../commons.js",[["FERAL_ERROR",[$h͏_a=>FERAL_ERROR=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["construct",[$h͏_a=>construct=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["setPrototypeOf",[$h͏_a=>setPrototypeOf=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]]]],["../permits.js",[["NativeErrors",[$h͏_a=>NativeErrors=$h͏_a]]]],["./tame-v8-error-constructor.js",[["tameV8ErrorConstructor",[$h͏_a=>tameV8ErrorConstructor=$h͏_a]]]]]);const stackDesc=getOwnPropertyDescriptor(FERAL_ERROR.prototype,"stack"),stackGetter=stackDesc&&stackDesc.get;let initialGetStackString={getStackString:error=>"function"==typeof stackGetter?apply(stackGetter,error,[]):"stack"in error?`${error.stack}`:""}.getStackString;$h͏_once.default((function(errorTaming="safe",stackFiltering="concise"){const ErrorPrototype=FERAL_ERROR.prototype,{captureStackTrace:originalCaptureStackTrace}=FERAL_ERROR,platform="function"==typeof originalCaptureStackTrace?"v8":"unknown",makeErrorConstructor=(_={})=>{const ResultError=function(...rest){let error;return error=void 0===new.target?apply(FERAL_ERROR,this,rest):construct(FERAL_ERROR,rest,new.target),"v8"===platform&&apply(originalCaptureStackTrace,FERAL_ERROR,[error,ResultError]),error};return defineProperties(ResultError,{length:{value:1},prototype:{value:ErrorPrototype,writable:!1,enumerable:!1,configurable:!1}}),ResultError},InitialError=makeErrorConstructor({powers:"original"}),SharedError=makeErrorConstructor({powers:"none"});defineProperties(ErrorPrototype,{constructor:{value:SharedError}});for(const NativeError of NativeErrors)setPrototypeOf(NativeError,SharedError);if(defineProperties(InitialError,{stackTraceLimit:{get(){if("number"==typeof FERAL_ERROR.stackTraceLimit)return FERAL_ERROR.stackTraceLimit},set(newLimit){"number"==typeof newLimit&&("number"!=typeof FERAL_ERROR.stackTraceLimit||(FERAL_ERROR.stackTraceLimit=newLimit))},enumerable:!1,configurable:!0}}),"unsafe-debug"===errorTaming&&"v8"===platform){defineProperties(InitialError,{prepareStackTrace:{get:()=>FERAL_ERROR.prepareStackTrace,set(newPrepareStackTrace){FERAL_ERROR.prepareStackTrace=newPrepareStackTrace},enumerable:!1,configurable:!0},captureStackTrace:{value:FERAL_ERROR.captureStackTrace,writable:!0,enumerable:!1,configurable:!0}});const descs=getOwnPropertyDescriptors(InitialError);return defineProperties(SharedError,{stackTraceLimit:descs.stackTraceLimit,prepareStackTrace:descs.prepareStackTrace,captureStackTrace:descs.captureStackTrace}),{"%InitialGetStackString%":initialGetStackString,"%InitialError%":InitialError,"%SharedError%":SharedError}}return defineProperties(SharedError,{stackTraceLimit:{get(){},set(_newLimit){},enumerable:!1,configurable:!0}}),"v8"===platform&&defineProperties(SharedError,{prepareStackTrace:{get:()=>()=>"",set(_prepareFn){},enumerable:!1,configurable:!0},captureStackTrace:{value:(errorish,_constructorOpt)=>{defineProperty(errorish,"stack",{value:""})},writable:!1,enumerable:!1,configurable:!0}}),"v8"===platform?initialGetStackString=tameV8ErrorConstructor(FERAL_ERROR,InitialError,errorTaming,stackFiltering):defineProperties(ErrorPrototype,"unsafe"===errorTaming||"unsafe-debug"===errorTaming?{stack:{get(){return initialGetStackString(this)},set(newValue){defineProperties(this,{stack:{value:newValue,writable:!0,enumerable:!0,configurable:!0}})}}}:{stack:{get(){return`${this}`},set(newValue){defineProperties(this,{stack:{value:newValue,writable:!0,enumerable:!0,configurable:!0}})}}}),{"%InitialGetStackString%":initialGetStackString,"%InitialError%":InitialError,"%SharedError%":SharedError}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let getenv,Map,Set,TypeError,arrayJoin,arrayMap,arrayPush,arraySome,create,freeze,generatorNext,generatorThrow,getOwnPropertyNames,isArray,isObject,mapGet,mapHas,mapSet,promiseThen,setAdd,values,weakmapGet,weakmapHas,makeError,annotateError,q,b,X;$h͏_imports([["@endo/env-options",[["getEnvironmentOption",[$h͏_a=>getenv=$h͏_a]]]],["./commons.js",[["Map",[$h͏_a=>Map=$h͏_a]],["Set",[$h͏_a=>Set=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayJoin",[$h͏_a=>arrayJoin=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["arraySome",[$h͏_a=>arraySome=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["generatorNext",[$h͏_a=>generatorNext=$h͏_a]],["generatorThrow",[$h͏_a=>generatorThrow=$h͏_a]],["getOwnPropertyNames",[$h͏_a=>getOwnPropertyNames=$h͏_a]],["isArray",[$h͏_a=>isArray=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["mapHas",[$h͏_a=>mapHas=$h͏_a]],["mapSet",[$h͏_a=>mapSet=$h͏_a]],["promiseThen",[$h͏_a=>promiseThen=$h͏_a]],["setAdd",[$h͏_a=>setAdd=$h͏_a]],["values",[$h͏_a=>values=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["weakmapHas",[$h͏_a=>weakmapHas=$h͏_a]]]],["./error/assert.js",[["makeError",[$h͏_a=>makeError=$h͏_a]],["annotateError",[$h͏_a=>annotateError=$h͏_a]],["q",[$h͏_a=>q=$h͏_a]],["b",[$h͏_a=>b=$h͏_a]],["X",[$h͏_a=>X=$h͏_a]]]]]);const noop=()=>{},asyncTrampoline=async(generatorFunc,args,errorWrapper)=>{await null;const iterator=generatorFunc(...args);let result=generatorNext(iterator);for(;!result.done;)try{const val=await result.value;result=generatorNext(iterator,val)}catch(error){result=generatorThrow(iterator,errorWrapper(error))}return result.value},syncTrampoline=(generatorFunc,args)=>{const iterator=generatorFunc(...args);let result=generatorNext(iterator);for(;!result.done;)try{result=generatorNext(iterator,result.value)}catch(error){result=generatorThrow(iterator,error)}return result.value};$h͏_once.makeAlias(((compartment,specifier)=>freeze({compartment:compartment,specifier:specifier})));const loadModuleSource=(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,moduleSource,enqueueJob,selectImplementation,moduleLoads,importMeta)=>{const{resolveHook:resolveHook,name:compartmentName}=weakmapGet(compartmentPrivateFields,compartment),{imports:imports}=moduleSource;if(!isArray(imports)||arraySome(imports,(specifier=>"string"!=typeof specifier)))throw makeError(X`Invalid module source: 'imports' must be an array of strings, got ${imports} for module ${q(moduleSpecifier)} of compartment ${q(compartmentName)}`);const resolvedImports=((imports,resolveHook,fullReferrerSpecifier)=>{const resolvedImports=create(null);for(const importSpecifier of imports){const fullSpecifier=resolveHook(importSpecifier,fullReferrerSpecifier);resolvedImports[importSpecifier]=fullSpecifier}return freeze(resolvedImports)})(imports,resolveHook,moduleSpecifier),moduleRecord=freeze({compartment:compartment,moduleSource:moduleSource,moduleSpecifier:moduleSpecifier,resolvedImports:resolvedImports,importMeta:importMeta});for(const fullSpecifier of values(resolvedImports))enqueueJob(memoizedLoadWithErrorAnnotation,[compartmentPrivateFields,moduleAliases,compartment,fullSpecifier,enqueueJob,selectImplementation,moduleLoads]);return moduleRecord};function*loadWithoutErrorAnnotation(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,enqueueJob,selectImplementation,moduleLoads){const{importHook:importHook,importNowHook:importNowHook,moduleMap:moduleMap,moduleMapHook:moduleMapHook,moduleRecords:moduleRecords,parentCompartment:parentCompartment}=weakmapGet(compartmentPrivateFields,compartment);if(mapHas(moduleRecords,moduleSpecifier))return mapGet(moduleRecords,moduleSpecifier);let moduleDescriptor=moduleMap[moduleSpecifier];if(void 0===moduleDescriptor&&void 0!==moduleMapHook&&(moduleDescriptor=moduleMapHook(moduleSpecifier)),void 0===moduleDescriptor){const moduleHook=selectImplementation(importHook,importNowHook);if(void 0===moduleHook){const moduleHookName=selectImplementation("importHook","importNowHook");throw makeError(X`${b(moduleHookName)} needed to load module ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`)}moduleDescriptor=moduleHook(moduleSpecifier),weakmapHas(moduleAliases,moduleDescriptor)||(moduleDescriptor=yield moduleDescriptor)}if("string"==typeof moduleDescriptor)throw makeError(X`Cannot map module ${q(moduleSpecifier)} to ${q(moduleDescriptor)} in parent compartment, use {source} module descriptor`,TypeError);if(isObject(moduleDescriptor)){let aliasDescriptor=weakmapGet(moduleAliases,moduleDescriptor);if(void 0!==aliasDescriptor&&(moduleDescriptor=aliasDescriptor),void 0!==moduleDescriptor.namespace){if("string"==typeof moduleDescriptor.namespace){const{compartment:aliasCompartment=parentCompartment,namespace:aliasSpecifier}=moduleDescriptor;if(!isObject(aliasCompartment)||!weakmapHas(compartmentPrivateFields,aliasCompartment))throw makeError(X`Invalid compartment in module descriptor for specifier ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`);const aliasRecord=yield memoizedLoadWithErrorAnnotation(compartmentPrivateFields,moduleAliases,aliasCompartment,aliasSpecifier,enqueueJob,selectImplementation,moduleLoads);return mapSet(moduleRecords,moduleSpecifier,aliasRecord),aliasRecord}if(!isObject(moduleDescriptor.namespace))throw makeError(X`Invalid compartment in module descriptor for specifier ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`);{const{namespace:namespace}=moduleDescriptor;if(aliasDescriptor=weakmapGet(moduleAliases,namespace),void 0===aliasDescriptor){const exports=getOwnPropertyNames(namespace),moduleRecord=loadModuleSource(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,{imports:[],exports:exports,execute(env){for(const name of exports)env[name]=namespace[name]}},enqueueJob,selectImplementation,moduleLoads,void 0);return mapSet(moduleRecords,moduleSpecifier,moduleRecord),moduleRecord}moduleDescriptor=aliasDescriptor}}if(void 0!==moduleDescriptor.source){if("string"==typeof moduleDescriptor.source){const{source:loaderSpecifier,specifier:instanceSpecifier=moduleSpecifier,compartment:loaderCompartment=parentCompartment,importMeta:importMeta}=moduleDescriptor,loaderRecord=yield memoizedLoadWithErrorAnnotation(compartmentPrivateFields,moduleAliases,loaderCompartment,loaderSpecifier,enqueueJob,selectImplementation,moduleLoads),{moduleSource:moduleSource}=loaderRecord,moduleRecord=loadModuleSource(compartmentPrivateFields,moduleAliases,compartment,instanceSpecifier,moduleSource,enqueueJob,selectImplementation,moduleLoads,importMeta);return mapSet(moduleRecords,moduleSpecifier,moduleRecord),moduleRecord}{const{source:moduleSource,specifier:aliasSpecifier=moduleSpecifier,importMeta:importMeta}=moduleDescriptor,aliasRecord=loadModuleSource(compartmentPrivateFields,moduleAliases,compartment,aliasSpecifier,moduleSource,enqueueJob,selectImplementation,moduleLoads,importMeta);return mapSet(moduleRecords,moduleSpecifier,aliasRecord),aliasRecord}}if(void 0!==moduleDescriptor.archive)throw makeError(X`Unsupported archive module descriptor for specifier ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`);if(void 0!==moduleDescriptor.record){const{compartment:aliasCompartment=compartment,specifier:aliasSpecifier=moduleSpecifier,record:moduleSource,importMeta:importMeta}=moduleDescriptor,aliasRecord=loadModuleSource(compartmentPrivateFields,moduleAliases,aliasCompartment,aliasSpecifier,moduleSource,enqueueJob,selectImplementation,moduleLoads,importMeta);return mapSet(moduleRecords,moduleSpecifier,aliasRecord),mapSet(moduleRecords,aliasSpecifier,aliasRecord),aliasRecord}if(void 0!==moduleDescriptor.compartment&&void 0!==moduleDescriptor.specifier){if(!isObject(moduleDescriptor.compartment)||!weakmapHas(compartmentPrivateFields,moduleDescriptor.compartment)||"string"!=typeof moduleDescriptor.specifier)throw makeError(X`Invalid compartment in module descriptor for specifier ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`);const aliasRecord=yield memoizedLoadWithErrorAnnotation(compartmentPrivateFields,moduleAliases,moduleDescriptor.compartment,moduleDescriptor.specifier,enqueueJob,selectImplementation,moduleLoads);return mapSet(moduleRecords,moduleSpecifier,aliasRecord),aliasRecord}const moduleRecord=loadModuleSource(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,moduleDescriptor,enqueueJob,selectImplementation,moduleLoads);return mapSet(moduleRecords,moduleSpecifier,moduleRecord),moduleRecord}throw makeError(X`module descriptor must be a string or object for specifier ${q(moduleSpecifier)} in compartment ${q(compartment.name)}`)}const memoizedLoadWithErrorAnnotation=(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,enqueueJob,selectImplementation,moduleLoads)=>{const{name:compartmentName}=weakmapGet(compartmentPrivateFields,compartment);let compartmentLoading=mapGet(moduleLoads,compartment);void 0===compartmentLoading&&(compartmentLoading=new Map,mapSet(moduleLoads,compartment,compartmentLoading));let moduleLoading=mapGet(compartmentLoading,moduleSpecifier);return void 0!==moduleLoading||(moduleLoading=selectImplementation(asyncTrampoline,syncTrampoline)(loadWithoutErrorAnnotation,[compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,enqueueJob,selectImplementation,moduleLoads],(error=>{throw annotateError(error,X`${error.message}, loading ${q(moduleSpecifier)} in compartment ${q(compartmentName)}`),error})),mapSet(compartmentLoading,moduleSpecifier,moduleLoading)),moduleLoading},throwAggregateError=({errors:errors,errorPrefix:errorPrefix})=>{if(errors.length>0){const verbose="verbose"===getenv("COMPARTMENT_LOAD_ERRORS","",["verbose"]);throw TypeError(`${errorPrefix} (${errors.length} underlying failures: ${arrayJoin(arrayMap(errors,(error=>error.message+(verbose?error.stack:""))),", ")}`)}},preferSync=(_asyncImpl,syncImpl)=>syncImpl,preferAsync=(asyncImpl,_syncImpl)=>asyncImpl;$h͏_once.load((async(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier)=>{const{name:compartmentName}=weakmapGet(compartmentPrivateFields,compartment),moduleLoads=new Map,{enqueueJob:enqueueJob,drainQueue:drainQueue,errors:errors}=((errors=[])=>{const pendingJobs=new Set;return{enqueueJob:(func,args)=>{setAdd(pendingJobs,promiseThen(func(...args),noop,(error=>{arrayPush(errors,error)})))},drainQueue:async()=>{await null;for(const job of pendingJobs)await job},errors:errors}})();enqueueJob(memoizedLoadWithErrorAnnotation,[compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,enqueueJob,preferAsync,moduleLoads]),await drainQueue(),throwAggregateError({errors:errors,errorPrefix:`Failed to load module ${q(moduleSpecifier)} in package ${q(compartmentName)}`})}));$h͏_once.loadNow(((compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier)=>{const{name:compartmentName}=weakmapGet(compartmentPrivateFields,compartment),moduleLoads=new Map,{enqueueJob:enqueueJob,drainQueue:drainQueue,errors:errors}=((errors=[])=>{let current=[],next=[];const drainQueue=()=>{for(const[func,args]of current)try{func(...args)}catch(error){arrayPush(errors,error)}current=next,next=[],current.length>0&&drainQueue()};return{enqueueJob:(func,args)=>{arrayPush(next,[func,args])},drainQueue:drainQueue,errors:errors}})();enqueueJob(memoizedLoadWithErrorAnnotation,[compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier,enqueueJob,preferSync,moduleLoads]),drainQueue(),throwAggregateError({errors:errors,errorPrefix:`Failed to load module ${q(moduleSpecifier)} in package ${q(compartmentName)}`})}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let makeAlias,Proxy,TypeError,create,freeze,mapGet,mapHas,mapSet,ownKeys,reflectGet,reflectGetOwnPropertyDescriptor,reflectHas,reflectIsExtensible,reflectPreventExtensions,toStringTagSymbol,weakmapSet,assert;$h͏_imports([["./module-load.js",[["makeAlias",[$h͏_a=>makeAlias=$h͏_a]]]],["./commons.js",[["Proxy",[$h͏_a=>Proxy=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["mapHas",[$h͏_a=>mapHas=$h͏_a]],["mapSet",[$h͏_a=>mapSet=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["reflectGet",[$h͏_a=>reflectGet=$h͏_a]],["reflectGetOwnPropertyDescriptor",[$h͏_a=>reflectGetOwnPropertyDescriptor=$h͏_a]],["reflectHas",[$h͏_a=>reflectHas=$h͏_a]],["reflectIsExtensible",[$h͏_a=>reflectIsExtensible=$h͏_a]],["reflectPreventExtensions",[$h͏_a=>reflectPreventExtensions=$h͏_a]],["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]],["weakmapSet",[$h͏_a=>weakmapSet=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const{quote:q}=assert,deferExports=()=>{let active=!1;const exportsTarget=create(null,{[toStringTagSymbol]:{value:"Module",writable:!1,enumerable:!1,configurable:!1}});return freeze({activate(){active=!0},exportsTarget:exportsTarget,exportsProxy:new Proxy(exportsTarget,{get(_target,name,receiver){if(!active)throw TypeError(`Cannot get property ${q(name)} of module exports namespace, the module has not yet begun to execute`);return reflectGet(exportsTarget,name,receiver)},set(_target,name,_value){throw TypeError(`Cannot set property ${q(name)} of module exports namespace`)},has(_target,name){if(!active)throw TypeError(`Cannot check property ${q(name)}, the module has not yet begun to execute`);return reflectHas(exportsTarget,name)},deleteProperty(_target,name){throw TypeError(`Cannot delete property ${q(name)}s of module exports namespace`)},ownKeys(_target){if(!active)throw TypeError("Cannot enumerate keys, the module has not yet begun to execute");return ownKeys(exportsTarget)},getOwnPropertyDescriptor(_target,name){if(!active)throw TypeError(`Cannot get own property descriptor ${q(name)}, the module has not yet begun to execute`);return reflectGetOwnPropertyDescriptor(exportsTarget,name)},preventExtensions(_target){if(!active)throw TypeError("Cannot prevent extensions of module exports namespace, the module has not yet begun to execute");return reflectPreventExtensions(exportsTarget)},isExtensible(){if(!active)throw TypeError("Cannot check extensibility of module exports namespace, the module has not yet begun to execute");return reflectIsExtensible(exportsTarget)},getPrototypeOf:_target=>null,setPrototypeOf(_target,_proto){throw TypeError("Cannot set prototype of module exports namespace")},defineProperty(_target,name,_descriptor){throw TypeError(`Cannot define property ${q(name)} of module exports namespace`)},apply(_target,_thisArg,_args){throw TypeError("Cannot call module exports namespace, it is not a function")},construct(_target,_args){throw TypeError("Cannot construct module exports namespace, it is not a constructor")}})})};$h͏_once.deferExports(deferExports);$h͏_once.getDeferredExports(((compartment,compartmentPrivateFields,moduleAliases,specifier)=>{const{deferredExports:deferredExports}=compartmentPrivateFields;if(!mapHas(deferredExports,specifier)){const deferred=deferExports();weakmapSet(moduleAliases,deferred.exportsProxy,makeAlias(compartment,specifier)),mapSet(deferredExports,specifier,deferred)}return mapGet(deferredExports,specifier)}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let TypeError,arrayPush,create,getOwnPropertyDescriptors,evadeHtmlCommentTest,evadeImportExpressionTest,rejectSomeDirectEvalExpressions,makeSafeEvaluator;$h͏_imports([["./commons.js",[["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]]]],["./transforms.js",[["evadeHtmlCommentTest",[$h͏_a=>evadeHtmlCommentTest=$h͏_a]],["evadeImportExpressionTest",[$h͏_a=>evadeImportExpressionTest=$h͏_a]],["rejectSomeDirectEvalExpressions",[$h͏_a=>rejectSomeDirectEvalExpressions=$h͏_a]]]],["./make-safe-evaluator.js",[["makeSafeEvaluator",[$h͏_a=>makeSafeEvaluator=$h͏_a]]]]]);const provideCompartmentEvaluator=(compartmentFields,options)=>{const{sloppyGlobalsMode:sloppyGlobalsMode=!1,__moduleShimLexicals__:__moduleShimLexicals__}=options;let safeEvaluate;if(void 0!==__moduleShimLexicals__||sloppyGlobalsMode){let{globalTransforms:globalTransforms}=compartmentFields;const{globalObject:globalObject}=compartmentFields;let moduleLexicals;void 0!==__moduleShimLexicals__&&(globalTransforms=void 0,moduleLexicals=create(null,getOwnPropertyDescriptors(__moduleShimLexicals__))),({safeEvaluate:safeEvaluate}=makeSafeEvaluator({globalObject:globalObject,moduleLexicals:moduleLexicals,globalTransforms:globalTransforms,sloppyGlobalsMode:sloppyGlobalsMode}))}else({safeEvaluate:safeEvaluate}=compartmentFields);return{safeEvaluate:safeEvaluate}};$h͏_once.provideCompartmentEvaluator(provideCompartmentEvaluator);$h͏_once.compartmentEvaluate(((compartmentFields,source,options)=>{if("string"!=typeof source)throw TypeError("first argument of evaluate() must be a string");const{transforms:transforms=[],__evadeHtmlCommentTest__:__evadeHtmlCommentTest__=!1,__evadeImportExpressionTest__:__evadeImportExpressionTest__=!1,__rejectSomeDirectEvalExpressions__:__rejectSomeDirectEvalExpressions__=!0}=options,localTransforms=[...transforms];!0===__evadeHtmlCommentTest__&&arrayPush(localTransforms,evadeHtmlCommentTest),!0===__evadeImportExpressionTest__&&arrayPush(localTransforms,evadeImportExpressionTest),!0===__rejectSomeDirectEvalExpressions__&&arrayPush(localTransforms,rejectSomeDirectEvalExpressions);const{safeEvaluate:safeEvaluate}=provideCompartmentEvaluator(compartmentFields,options);return safeEvaluate(source,{localTransforms:localTransforms})}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let assert,getDeferredExports,ReferenceError,SyntaxError,TypeError,arrayForEach,arrayIncludes,arrayPush,arraySome,arraySort,create,defineProperty,entries,freeze,isArray,keys,mapGet,weakmapGet,reflectHas,assign,compartmentEvaluate;$h͏_imports([["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]],["./module-proxy.js",[["getDeferredExports",[$h͏_a=>getDeferredExports=$h͏_a]]]],["./commons.js",[["ReferenceError",[$h͏_a=>ReferenceError=$h͏_a]],["SyntaxError",[$h͏_a=>SyntaxError=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayForEach",[$h͏_a=>arrayForEach=$h͏_a]],["arrayIncludes",[$h͏_a=>arrayIncludes=$h͏_a]],["arrayPush",[$h͏_a=>arrayPush=$h͏_a]],["arraySome",[$h͏_a=>arraySome=$h͏_a]],["arraySort",[$h͏_a=>arraySort=$h͏_a]],["create",[$h͏_a=>create=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["freeze",[$h͏_a=>freeze=$h͏_a]],["isArray",[$h͏_a=>isArray=$h͏_a]],["keys",[$h͏_a=>keys=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["reflectHas",[$h͏_a=>reflectHas=$h͏_a]],["assign",[$h͏_a=>assign=$h͏_a]]]],["./compartment-evaluate.js",[["compartmentEvaluate",[$h͏_a=>compartmentEvaluate=$h͏_a]]]]]);const{quote:q}=assert;$h͏_once.makeVirtualModuleInstance(((compartmentPrivateFields,moduleSource,compartment,moduleAliases,moduleSpecifier,resolvedImports)=>{const{exportsProxy:exportsProxy,exportsTarget:exportsTarget,activate:activate}=getDeferredExports(compartment,weakmapGet(compartmentPrivateFields,compartment),moduleAliases,moduleSpecifier),notifiers=create(null);if(moduleSource.exports){if(!isArray(moduleSource.exports)||arraySome(moduleSource.exports,(name=>"string"!=typeof name)))throw TypeError(`SES virtual module source "exports" property must be an array of strings for module ${moduleSpecifier}`);arrayForEach(moduleSource.exports,(name=>{let value=exportsTarget[name];const updaters=[];defineProperty(exportsTarget,name,{get:()=>value,set:newValue=>{value=newValue;for(const updater of updaters)updater(newValue)},enumerable:!0,configurable:!1}),notifiers[name]=update=>{arrayPush(updaters,update),update(value)}})),notifiers["*"]=update=>{update(exportsTarget)}}const localState={activated:!1};return freeze({notifiers:notifiers,exportsProxy:exportsProxy,execute(){if(reflectHas(localState,"errorFromExecute"))throw localState.errorFromExecute;if(!localState.activated){activate(),localState.activated=!0;try{moduleSource.execute(exportsTarget,compartment,resolvedImports)}catch(err){throw localState.errorFromExecute=err,err}}}})}));$h͏_once.makeModuleInstance(((privateFields,moduleAliases,moduleRecord,importedInstances)=>{const{compartment:compartment,moduleSpecifier:moduleSpecifier,moduleSource:moduleSource,importMeta:moduleRecordMeta}=moduleRecord,{reexports:exportAlls=[],__syncModuleProgram__:functorSource,__fixedExportMap__:fixedExportMap={},__liveExportMap__:liveExportMap={},__reexportMap__:reexportMap={},__needsImport__:needsImport=!1,__needsImportMeta__:needsImportMeta=!1,__syncModuleFunctor__:__syncModuleFunctor__}=moduleSource,compartmentFields=weakmapGet(privateFields,compartment),{__shimTransforms__:__shimTransforms__,resolveHook:resolveHook,importMetaHook:importMetaHook,compartmentImport:compartmentImport}=compartmentFields,{exportsProxy:exportsProxy,exportsTarget:exportsTarget,activate:activate}=getDeferredExports(compartment,compartmentFields,moduleAliases,moduleSpecifier),exportsProps=create(null),moduleLexicals=create(null),onceVar=create(null),liveVar=create(null),importMeta=create(null);let dynamicImport;moduleRecordMeta&&assign(importMeta,moduleRecordMeta),needsImportMeta&&importMetaHook&&importMetaHook(moduleSpecifier,importMeta),needsImport&&(dynamicImport=async importSpecifier=>compartmentImport(resolveHook(importSpecifier,moduleSpecifier)));const localGetNotify=create(null),notifiers=create(null);arrayForEach(entries(fixedExportMap),(([fixedExportName,[localName]])=>{let fixedGetNotify=localGetNotify[localName];if(!fixedGetNotify){let value,tdz=!0,optUpdaters=[];const get=()=>{if(tdz)throw ReferenceError(`binding ${q(localName)} not yet initialized`);return value},init=freeze((initValue=>{if(!tdz)throw TypeError(`Internal: binding ${q(localName)} already initialized`);value=initValue;const updaters=optUpdaters;optUpdaters=null,tdz=!1;for(const updater of updaters||[])updater(initValue);return initValue}));fixedGetNotify={get:get,notify:updater=>{updater!==init&&(tdz?arrayPush(optUpdaters||[],updater):updater(value))}},localGetNotify[localName]=fixedGetNotify,onceVar[localName]=init}exportsProps[fixedExportName]={get:fixedGetNotify.get,set:void 0,enumerable:!0,configurable:!1},notifiers[fixedExportName]=fixedGetNotify.notify})),arrayForEach(entries(liveExportMap),(([liveExportName,[localName,setProxyTrap]])=>{let liveGetNotify=localGetNotify[localName];if(!liveGetNotify){let value,tdz=!0;const updaters=[],get=()=>{if(tdz)throw ReferenceError(`binding ${q(liveExportName)} not yet initialized`);return value},update=freeze((newValue=>{value=newValue,tdz=!1;for(const updater of updaters)updater(newValue)})),set=newValue=>{if(tdz)throw ReferenceError(`binding ${q(localName)} not yet initialized`);value=newValue;for(const updater of updaters)updater(newValue)};liveGetNotify={get:get,notify:updater=>{updater!==update&&(arrayPush(updaters,updater),tdz||updater(value))}},localGetNotify[localName]=liveGetNotify,setProxyTrap&&defineProperty(moduleLexicals,localName,{get:get,set:set,enumerable:!0,configurable:!1}),liveVar[localName]=update}exportsProps[liveExportName]={get:liveGetNotify.get,set:void 0,enumerable:!0,configurable:!1},notifiers[liveExportName]=liveGetNotify.notify}));function imports(updateRecord){const candidateAll=create(null);candidateAll.default=!1;for(const[specifier,importUpdaters]of updateRecord){const instance=mapGet(importedInstances,specifier);instance.execute();const{notifiers:importNotifiers}=instance;for(const[importName,updaters]of importUpdaters){const importNotify=importNotifiers[importName];if(!importNotify)throw SyntaxError(`The requested module '${specifier}' does not provide an export named '${importName}'`);for(const updater of updaters)importNotify(updater)}if(arrayIncludes(exportAlls,specifier))for(const[importAndExportName,importNotify]of entries(importNotifiers))void 0===candidateAll[importAndExportName]?candidateAll[importAndExportName]=importNotify:candidateAll[importAndExportName]=!1;if(reexportMap[specifier])for(const[localName,exportedName]of reexportMap[specifier])candidateAll[exportedName]=importNotifiers[localName]}for(const[exportName,notify]of entries(candidateAll))if(!notifiers[exportName]&&!1!==notify){let value;notifiers[exportName]=notify;notify((newValue=>value=newValue)),exportsProps[exportName]={get:()=>value,set:void 0,enumerable:!0,configurable:!1}}arrayForEach(arraySort(keys(exportsProps)),(k=>defineProperty(exportsTarget,k,exportsProps[k]))),freeze(exportsTarget),activate()}let optFunctor;notifiers["*"]=update=>{update(exportsTarget)},optFunctor=void 0!==__syncModuleFunctor__?__syncModuleFunctor__:compartmentEvaluate(compartmentFields,functorSource,{globalObject:compartment.globalThis,transforms:__shimTransforms__,__moduleShimLexicals__:moduleLexicals});let thrownError,didThrow=!1;return freeze({notifiers:notifiers,exportsProxy:exportsProxy,execute:function(){if(optFunctor){const functor=optFunctor;optFunctor=null;try{functor(freeze({imports:freeze(imports),onceVar:freeze(onceVar),liveVar:freeze(liveVar),import:dynamicImport,importMeta:importMeta}))}catch(e){didThrow=!0,thrownError=e}}if(didThrow)throw thrownError}})}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let assert,makeModuleInstance,makeVirtualModuleInstance,Map,ReferenceError,TypeError,entries,isArray,isObject,mapGet,mapHas,mapSet,weakmapGet;$h͏_imports([["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]],["./module-instance.js",[["makeModuleInstance",[$h͏_a=>makeModuleInstance=$h͏_a]],["makeVirtualModuleInstance",[$h͏_a=>makeVirtualModuleInstance=$h͏_a]]]],["./commons.js",[["Map",[$h͏_a=>Map=$h͏_a]],["ReferenceError",[$h͏_a=>ReferenceError=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["isArray",[$h͏_a=>isArray=$h͏_a]],["isObject",[$h͏_a=>isObject=$h͏_a]],["mapGet",[$h͏_a=>mapGet=$h͏_a]],["mapHas",[$h͏_a=>mapHas=$h͏_a]],["mapSet",[$h͏_a=>mapSet=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]]]]]);const{Fail:Fail,quote:q}=assert,link=(compartmentPrivateFields,moduleAliases,compartment,moduleSpecifier)=>{const{name:compartmentName,moduleRecords:moduleRecords}=weakmapGet(compartmentPrivateFields,compartment),moduleRecord=mapGet(moduleRecords,moduleSpecifier);if(void 0===moduleRecord)throw ReferenceError(`Missing link to module ${q(moduleSpecifier)} from compartment ${q(compartmentName)}`);return instantiate(compartmentPrivateFields,moduleAliases,moduleRecord)};$h͏_once.link(link);const instantiate=(compartmentPrivateFields,moduleAliases,moduleRecord)=>{const{compartment:compartment,moduleSpecifier:moduleSpecifier,resolvedImports:resolvedImports,moduleSource:moduleSource}=moduleRecord,{instances:instances}=weakmapGet(compartmentPrivateFields,compartment);if(mapHas(instances,moduleSpecifier))return mapGet(instances,moduleSpecifier);!function(moduleSource,moduleSpecifier){isObject(moduleSource)||Fail`Invalid module source: must be of type object, got ${q(moduleSource)}, for module ${q(moduleSpecifier)}`;const{imports:imports,exports:exports,reexports:reexports=[]}=moduleSource;isArray(imports)||Fail`Invalid module source: 'imports' must be an array, got ${q(imports)}, for module ${q(moduleSpecifier)}`,isArray(exports)||Fail`Invalid module source: 'exports' must be an array, got ${q(exports)}, for module ${q(moduleSpecifier)}`,isArray(reexports)||Fail`Invalid module source: 'reexports' must be an array if present, got ${q(reexports)}, for module ${q(moduleSpecifier)}`}(moduleSource,moduleSpecifier);const importedInstances=new Map;let moduleInstance;if(function(moduleSource){return"string"==typeof moduleSource.__syncModuleProgram__}(moduleSource))!function(moduleSource,moduleSpecifier){const{__fixedExportMap__:__fixedExportMap__,__liveExportMap__:__liveExportMap__}=moduleSource;isObject(__fixedExportMap__)||Fail`Property '__fixedExportMap__' of a precompiled module source must be an object, got ${q(__fixedExportMap__)}, for module ${q(moduleSpecifier)}`,isObject(__liveExportMap__)||Fail`Property '__liveExportMap__' of a precompiled module source must be an object, got ${q(__liveExportMap__)}, for module ${q(moduleSpecifier)}`}(moduleSource,moduleSpecifier),moduleInstance=makeModuleInstance(compartmentPrivateFields,moduleAliases,moduleRecord,importedInstances);else{if(!function(moduleSource){return"function"==typeof moduleSource.execute}(moduleSource))throw TypeError(`Invalid module source, got ${q(moduleSource)}`);!function(moduleSource,moduleSpecifier){const{exports:exports}=moduleSource;isArray(exports)||Fail`Invalid module source: 'exports' of a virtual module source must be an array, got ${q(exports)}, for module ${q(moduleSpecifier)}`}(moduleSource,moduleSpecifier),moduleInstance=makeVirtualModuleInstance(compartmentPrivateFields,moduleSource,compartment,moduleAliases,moduleSpecifier,resolvedImports)}mapSet(instances,moduleSpecifier,moduleInstance);for(const[importSpecifier,resolvedSpecifier]of entries(resolvedImports)){const importedInstance=link(compartmentPrivateFields,moduleAliases,compartment,resolvedSpecifier);mapSet(importedInstances,importSpecifier,importedInstance)}return moduleInstance};$h͏_once.instantiate(instantiate)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Map,TypeError,WeakMap,arrayFlatMap,assign,defineProperties,identity,promiseThen,toStringTagSymbol,weakmapGet,weakmapSet,setGlobalObjectSymbolUnscopables,setGlobalObjectConstantProperties,setGlobalObjectMutableProperties,setGlobalObjectEvaluators,assert,assertEqual,q,sharedGlobalPropertyNames,load,loadNow,link,getDeferredExports,compartmentEvaluate,makeSafeEvaluator;$h͏_imports([["./commons.js",[["Map",[$h͏_a=>Map=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["WeakMap",[$h͏_a=>WeakMap=$h͏_a]],["arrayFlatMap",[$h͏_a=>arrayFlatMap=$h͏_a]],["assign",[$h͏_a=>assign=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["identity",[$h͏_a=>identity=$h͏_a]],["promiseThen",[$h͏_a=>promiseThen=$h͏_a]],["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]],["weakmapGet",[$h͏_a=>weakmapGet=$h͏_a]],["weakmapSet",[$h͏_a=>weakmapSet=$h͏_a]]]],["./global-object.js",[["setGlobalObjectSymbolUnscopables",[$h͏_a=>setGlobalObjectSymbolUnscopables=$h͏_a]],["setGlobalObjectConstantProperties",[$h͏_a=>setGlobalObjectConstantProperties=$h͏_a]],["setGlobalObjectMutableProperties",[$h͏_a=>setGlobalObjectMutableProperties=$h͏_a]],["setGlobalObjectEvaluators",[$h͏_a=>setGlobalObjectEvaluators=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]],["assertEqual",[$h͏_a=>assertEqual=$h͏_a]],["q",[$h͏_a=>q=$h͏_a]]]],["./permits.js",[["sharedGlobalPropertyNames",[$h͏_a=>sharedGlobalPropertyNames=$h͏_a]]]],["./module-load.js",[["load",[$h͏_a=>load=$h͏_a]],["loadNow",[$h͏_a=>loadNow=$h͏_a]]]],["./module-link.js",[["link",[$h͏_a=>link=$h͏_a]]]],["./module-proxy.js",[["getDeferredExports",[$h͏_a=>getDeferredExports=$h͏_a]]]],["./compartment-evaluate.js",[["compartmentEvaluate",[$h͏_a=>compartmentEvaluate=$h͏_a]]]],["./make-safe-evaluator.js",[["makeSafeEvaluator",[$h͏_a=>makeSafeEvaluator=$h͏_a]]]]]);const moduleAliases=new WeakMap,privateFields=new WeakMap,InertCompartment=function(_endowments={},_modules={},_options={}){throw TypeError("Compartment.prototype.constructor is not a valid constructor.")};$h͏_once.InertCompartment(InertCompartment);const compartmentImportNow=(compartment,specifier)=>{const{execute:execute,exportsProxy:exportsProxy}=link(privateFields,moduleAliases,compartment,specifier);return execute(),exportsProxy},CompartmentPrototype={constructor:InertCompartment,get globalThis(){return weakmapGet(privateFields,this).globalObject},get name(){return weakmapGet(privateFields,this).name},evaluate(source,options={}){const compartmentFields=weakmapGet(privateFields,this);return compartmentEvaluate(compartmentFields,source,options)},module(specifier){if("string"!=typeof specifier)throw TypeError("first argument of module() must be a string");const{exportsProxy:exportsProxy}=getDeferredExports(this,weakmapGet(privateFields,this),moduleAliases,specifier);return exportsProxy},async import(specifier){const{noNamespaceBox:noNamespaceBox}=weakmapGet(privateFields,this);if("string"!=typeof specifier)throw TypeError("first argument of import() must be a string");return promiseThen(load(privateFields,moduleAliases,this,specifier),(()=>{const namespace=compartmentImportNow(this,specifier);return noNamespaceBox?namespace:{namespace:namespace}}))},async load(specifier){if("string"!=typeof specifier)throw TypeError("first argument of load() must be a string");return load(privateFields,moduleAliases,this,specifier)},importNow(specifier){if("string"!=typeof specifier)throw TypeError("first argument of importNow() must be a string");return loadNow(privateFields,moduleAliases,this,specifier),compartmentImportNow(this,specifier)}};$h͏_once.CompartmentPrototype(CompartmentPrototype),defineProperties(CompartmentPrototype,{[toStringTagSymbol]:{value:"Compartment",writable:!1,enumerable:!1,configurable:!0}}),defineProperties(InertCompartment,{prototype:{value:CompartmentPrototype}});const compartmentOptions=(...args)=>{if(0===args.length)return{};if(1===args.length&&"object"==typeof args[0]&&null!==args[0]&&"__options__"in args[0]){const{__options__:__options__,...options}=args[0];return assert(!0===__options__,`Compartment constructor only supports true __options__ sigil, got ${__options__}`),options}{const[globals={},modules={},options={}]=args;return assertEqual(options.modules,void 0,"Compartment constructor must receive either a module map argument or modules option, not both"),assertEqual(options.globals,void 0,"Compartment constructor must receive either globals argument or option, not both"),{...options,globals:globals,modules:modules}}};$h͏_once.compartmentOptions(compartmentOptions);$h͏_once.makeCompartmentConstructor(((targetMakeCompartmentConstructor,intrinsics,markVirtualizedNativeFunction,{parentCompartment:parentCompartment,enforceNew:enforceNew=!1}={})=>{function Compartment(...args){if(enforceNew&&void 0===new.target)throw TypeError("Class constructor Compartment cannot be invoked without 'new'");const{name:name="<unknown>",transforms:transforms=[],__shimTransforms__:__shimTransforms__=[],globals:endowmentsOption={},modules:moduleMapOption={},resolveHook:resolveHook,importHook:importHook,importNowHook:importNowHook,moduleMapHook:moduleMapHook,importMetaHook:importMetaHook,__noNamespaceBox__:noNamespaceBox=!1}=compartmentOptions(...args),globalTransforms=arrayFlatMap([transforms,__shimTransforms__],identity),endowments={__proto__:null,...endowmentsOption},moduleMap={__proto__:null,...moduleMapOption},moduleRecords=new Map,instances=new Map,deferredExports=new Map,globalObject={},compartment=this;setGlobalObjectSymbolUnscopables(globalObject),setGlobalObjectConstantProperties(globalObject);const{safeEvaluate:safeEvaluate}=makeSafeEvaluator({globalObject:globalObject,globalTransforms:globalTransforms,sloppyGlobalsMode:!1});setGlobalObjectMutableProperties(globalObject,{intrinsics:intrinsics,newGlobalPropertyNames:sharedGlobalPropertyNames,makeCompartmentConstructor:targetMakeCompartmentConstructor,parentCompartment:this,markVirtualizedNativeFunction:markVirtualizedNativeFunction}),setGlobalObjectEvaluators(globalObject,safeEvaluate,markVirtualizedNativeFunction),assign(globalObject,endowments);weakmapSet(privateFields,this,{name:`${name}`,globalTransforms:globalTransforms,globalObject:globalObject,safeEvaluate:safeEvaluate,resolveHook:resolveHook,importHook:importHook,importNowHook:importNowHook,moduleMap:moduleMap,moduleMapHook:moduleMapHook,importMetaHook:importMetaHook,moduleRecords:moduleRecords,__shimTransforms__:__shimTransforms__,deferredExports:deferredExports,instances:instances,parentCompartment:parentCompartment,noNamespaceBox:noNamespaceBox,compartmentImport:async fullSpecifier=>{if("function"!=typeof resolveHook)throw TypeError(`Compartment does not support dynamic import: no configured resolveHook for compartment ${q(name)}`);await load(privateFields,moduleAliases,compartment,fullSpecifier);const{execute:execute,exportsProxy:exportsProxy}=link(privateFields,moduleAliases,compartment,fullSpecifier);return execute(),exportsProxy}})}return Compartment.prototype=CompartmentPrototype,Compartment}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let FERAL_FUNCTION,Float32Array,Map,Set,String,getOwnPropertyDescriptor,getPrototypeOf,iterateArray,iterateMap,iterateSet,iterateString,matchAllRegExp,matchAllSymbol,regexpPrototype,globalThis,assign,AsyncGeneratorFunctionInstance,ArrayBuffer,InertCompartment;function getConstructorOf(obj){return getPrototypeOf(obj).constructor}$h͏_imports([["./commons.js",[["FERAL_FUNCTION",[$h͏_a=>FERAL_FUNCTION=$h͏_a]],["Float32Array",[$h͏_a=>Float32Array=$h͏_a]],["Map",[$h͏_a=>Map=$h͏_a]],["Set",[$h͏_a=>Set=$h͏_a]],["String",[$h͏_a=>String=$h͏_a]],["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]],["iterateArray",[$h͏_a=>iterateArray=$h͏_a]],["iterateMap",[$h͏_a=>iterateMap=$h͏_a]],["iterateSet",[$h͏_a=>iterateSet=$h͏_a]],["iterateString",[$h͏_a=>iterateString=$h͏_a]],["matchAllRegExp",[$h͏_a=>matchAllRegExp=$h͏_a]],["matchAllSymbol",[$h͏_a=>matchAllSymbol=$h͏_a]],["regexpPrototype",[$h͏_a=>regexpPrototype=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["assign",[$h͏_a=>assign=$h͏_a]],["AsyncGeneratorFunctionInstance",[$h͏_a=>AsyncGeneratorFunctionInstance=$h͏_a]],["ArrayBuffer",[$h͏_a=>ArrayBuffer=$h͏_a]]]],["./compartment.js",[["InertCompartment",[$h͏_a=>InertCompartment=$h͏_a]]]]]);$h͏_once.getAnonymousIntrinsics((()=>{const InertFunction=FERAL_FUNCTION.prototype.constructor,argsCalleeDesc=getOwnPropertyDescriptor(function(){return arguments}(),"callee"),ThrowTypeError=argsCalleeDesc&&argsCalleeDesc.get,StringIteratorObject=iterateString(new String),StringIteratorPrototype=getPrototypeOf(StringIteratorObject),RegExpStringIterator=regexpPrototype[matchAllSymbol]&&matchAllRegExp(/./),RegExpStringIteratorPrototype=RegExpStringIterator&&getPrototypeOf(RegExpStringIterator),ArrayIteratorObject=iterateArray([]),ArrayIteratorPrototype=getPrototypeOf(ArrayIteratorObject),TypedArray=getPrototypeOf(Float32Array),MapIteratorObject=iterateMap(new Map),MapIteratorPrototype=getPrototypeOf(MapIteratorObject),SetIteratorObject=iterateSet(new Set),SetIteratorPrototype=getPrototypeOf(SetIteratorObject),IteratorPrototype=getPrototypeOf(ArrayIteratorPrototype);const GeneratorFunction=getConstructorOf((function*(){})),Generator=GeneratorFunction.prototype;const intrinsics={"%InertFunction%":InertFunction,"%ArrayIteratorPrototype%":ArrayIteratorPrototype,"%InertAsyncFunction%":getConstructorOf((async function(){})),"%Generator%":Generator,"%InertGeneratorFunction%":GeneratorFunction,"%IteratorPrototype%":IteratorPrototype,"%MapIteratorPrototype%":MapIteratorPrototype,"%RegExpStringIteratorPrototype%":RegExpStringIteratorPrototype,"%SetIteratorPrototype%":SetIteratorPrototype,"%StringIteratorPrototype%":StringIteratorPrototype,"%ThrowTypeError%":ThrowTypeError,"%TypedArray%":TypedArray,"%InertCompartment%":InertCompartment};if(void 0!==AsyncGeneratorFunctionInstance){const AsyncGeneratorFunction=getConstructorOf(AsyncGeneratorFunctionInstance),AsyncGenerator=AsyncGeneratorFunction.prototype,AsyncGeneratorPrototype=AsyncGenerator.prototype,AsyncIteratorPrototype=getPrototypeOf(AsyncGeneratorPrototype);assign(intrinsics,{"%AsyncGenerator%":AsyncGenerator,"%InertAsyncGeneratorFunction%":AsyncGeneratorFunction,"%AsyncGeneratorPrototype%":AsyncGeneratorPrototype,"%AsyncIteratorPrototype%":AsyncIteratorPrototype})}globalThis.Iterator&&(intrinsics["%IteratorHelperPrototype%"]=getPrototypeOf(globalThis.Iterator.from([]).take(0)),intrinsics["%WrapForValidIteratorPrototype%"]=getPrototypeOf(globalThis.Iterator.from({next:()=>({value:void 0})}))),globalThis.AsyncIterator&&(intrinsics["%AsyncIteratorHelperPrototype%"]=getPrototypeOf(globalThis.AsyncIterator.from([]).take(0)),intrinsics["%WrapForValidAsyncIteratorPrototype%"]=getPrototypeOf(globalThis.AsyncIterator.from({next(){}})));const iab=new ArrayBuffer(0).sliceToImmutable(),iabProto=getPrototypeOf(iab);return iabProto!==ArrayBuffer.prototype&&(intrinsics["%ImmutableArrayBufferPrototype%"]=iabProto),intrinsics}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let freeze;$h͏_imports([["./commons.js",[["freeze",[$h͏_a=>freeze=$h͏_a]]]]]);const tameHarden=(safeHarden,hardenTaming)=>{if("safe"===hardenTaming)return safeHarden;if(Object.isExtensible=()=>!1,Object.isFrozen=()=>!0,Object.isSealed=()=>!0,Reflect.isExtensible=()=>!1,safeHarden.isFake)return safeHarden;const fakeHarden=arg=>arg;return fakeHarden.isFake=!0,freeze(fakeHarden)};$h͏_once.tameHarden(tameHarden),freeze(tameHarden)}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let Symbol,entries,fromEntries,getOwnPropertyDescriptors,defineProperties,arrayMap,functionBind;$h͏_imports([["./commons.js",[["Symbol",[$h͏_a=>Symbol=$h͏_a]],["entries",[$h͏_a=>entries=$h͏_a]],["fromEntries",[$h͏_a=>fromEntries=$h͏_a]],["getOwnPropertyDescriptors",[$h͏_a=>getOwnPropertyDescriptors=$h͏_a]],["defineProperties",[$h͏_a=>defineProperties=$h͏_a]],["arrayMap",[$h͏_a=>arrayMap=$h͏_a]],["functionBind",[$h͏_a=>functionBind=$h͏_a]]]]]);$h͏_once.tameSymbolConstructor((()=>{const OriginalSymbol=Symbol,SymbolPrototype=OriginalSymbol.prototype,SharedSymbol=functionBind(Symbol,void 0);defineProperties(SymbolPrototype,{constructor:{value:SharedSymbol}});const originalDescsEntries=entries(getOwnPropertyDescriptors(OriginalSymbol)),descs=fromEntries(arrayMap(originalDescsEntries,(([name,desc])=>[name,{...desc,configurable:!0}])));return defineProperties(SharedSymbol,descs),{"%SharedSymbol%":SharedSymbol}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let getOwnPropertyDescriptor,apply,defineProperty,toStringTagSymbol;$h͏_imports([["./commons.js",[["getOwnPropertyDescriptor",[$h͏_a=>getOwnPropertyDescriptor=$h͏_a]],["apply",[$h͏_a=>apply=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["toStringTagSymbol",[$h͏_a=>toStringTagSymbol=$h͏_a]]]]]);const tameFauxDataProperty=(obj,prop,expectedValue)=>{if(void 0===obj)return!1;const desc=getOwnPropertyDescriptor(obj,prop);if(!desc||"value"in desc)return!1;const{get:get,set:set}=desc;if("function"!=typeof get||"function"!=typeof set)return!1;if(get()!==expectedValue)return!1;if(apply(get,obj,[])!==expectedValue)return!1;const testValue="Seems to be a setter",subject1={__proto__:null};if(apply(set,subject1,[testValue]),subject1[prop]!==testValue)return!1;const subject2={__proto__:obj};return apply(set,subject2,[testValue]),subject2[prop]===testValue&&(!!(thunk=>{try{return thunk(),!1}catch(er){return!0}})((()=>apply(set,obj,[expectedValue])))&&(!("originalValue"in get)&&(!1!==desc.configurable&&(defineProperty(obj,prop,{value:expectedValue,writable:!0,enumerable:desc.enumerable,configurable:!0}),!0))))};$h͏_once.tameFauxDataProperty(tameFauxDataProperty);$h͏_once.tameFauxDataProperties((intrinsics=>{tameFauxDataProperty(intrinsics["%IteratorPrototype%"],"constructor",intrinsics.Iterator),tameFauxDataProperty(intrinsics["%IteratorPrototype%"],toStringTagSymbol,"Iterator")}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let defineProperty,iteratorPrototype,iteratorSymbol,objectHasOwnProperty;$h͏_imports([["./commons.js",[["defineProperty",[$h͏_a=>defineProperty=$h͏_a]],["iteratorPrototype",[$h͏_a=>iteratorPrototype=$h͏_a]],["iteratorSymbol",[$h͏_a=>iteratorSymbol=$h͏_a]],["objectHasOwnProperty",[$h͏_a=>objectHasOwnProperty=$h͏_a]]]]]);$h͏_once.tameRegeneratorRuntime((()=>{const iter=iteratorPrototype[iteratorSymbol];defineProperty(iteratorPrototype,iteratorSymbol,{configurable:!0,get:()=>iter,set(value){this!==iteratorPrototype&&(objectHasOwnProperty(this,iteratorSymbol)&&(this[iteratorSymbol]=value),defineProperty(this,iteratorSymbol,{value:value,writable:!0,enumerable:!0,configurable:!0}))}})}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let ArrayBuffer,arrayBufferPrototype,arrayBufferSlice,arrayBufferGetByteLength,Uint8Array,typedArraySet,globalThis,TypeError,defineProperty;$h͏_imports([["./commons.js",[["ArrayBuffer",[$h͏_a=>ArrayBuffer=$h͏_a]],["arrayBufferPrototype",[$h͏_a=>arrayBufferPrototype=$h͏_a]],["arrayBufferSlice",[$h͏_a=>arrayBufferSlice=$h͏_a]],["arrayBufferGetByteLength",[$h͏_a=>arrayBufferGetByteLength=$h͏_a]],["Uint8Array",[$h͏_a=>Uint8Array=$h͏_a]],["typedArraySet",[$h͏_a=>typedArraySet=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["defineProperty",[$h͏_a=>defineProperty=$h͏_a]]]]]);$h͏_once.shimArrayBufferTransfer((()=>{if("function"==typeof arrayBufferPrototype.transfer)return{};const clone=globalThis.structuredClone;if("function"!=typeof clone)return{};return defineProperty(arrayBufferPrototype,"transfer",{value:{transfer(newLength=undefined){const oldLength=arrayBufferGetByteLength(this);if(void 0===newLength||newLength===oldLength)return clone(this,{transfer:[this]});if("number"!=typeof newLength)throw TypeError("transfer newLength if provided must be a number");if(newLength>oldLength){const result=new ArrayBuffer(newLength),taOld=new Uint8Array(this),taNew=new Uint8Array(result);return typedArraySet(taNew,taOld),clone(this,{transfer:[this]}),result}{const result=arrayBufferSlice(this,0,newLength);return clone(this,{transfer:[this]}),result}}}.transfer,writable:!0,enumerable:!1,configurable:!0}),{}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let functionBind,globalThis,assert;$h͏_imports([["./commons.js",[["functionBind",[$h͏_a=>functionBind=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]);const makeReportPrinter=print=>{let indent=!1;const printIndent=(...args)=>{indent?print(" ",...args):print(...args)};return{warn(...args){printIndent(...args)},error(...args){printIndent(...args)},groupCollapsed(...args){assert(!indent),print(...args),indent=!0},groupEnd(){indent=!1}}},mute=()=>{};$h͏_once.chooseReporter((reporting=>{if("none"===reporting)return makeReportPrinter(mute);if("console"===reporting||globalThis.window===globalThis||void 0!==globalThis.importScripts)return console;if(void 0!==globalThis.console){const console=globalThis.console,error=functionBind(console.error,console);return makeReportPrinter(error)}return void 0!==globalThis.print?makeReportPrinter(globalThis.print):makeReportPrinter(mute)}));$h͏_once.reportInGroup(((groupLabel,console,callback)=>{const{warn:warn,error:error,groupCollapsed:groupCollapsed,groupEnd:groupEnd}=console,grouping=groupCollapsed&&groupEnd;let groupStarted=!1;try{return callback({warn(...args){grouping&&!groupStarted&&(groupCollapsed(groupLabel),groupStarted=!0),warn(...args)},error(...args){grouping&&!groupStarted&&(groupCollapsed(groupLabel),groupStarted=!0),error(...args)}})}finally{grouping&&groupStarted&&(groupEnd(),groupStarted=!1)}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let getenv,FERAL_FUNCTION,FERAL_EVAL,TypeError,arrayFilter,globalThis,is,ownKeys,stringSplit,noEvalEvaluate,getOwnPropertyNames,getPrototypeOf,makeHardener,makeIntrinsicsCollector,removeUnpermittedIntrinsics,tameFunctionConstructors,tameDateConstructor,tameMathObject,tameRegExpConstructor,enablePropertyOverrides,tameLocaleMethods,setGlobalObjectConstantProperties,setGlobalObjectMutableProperties,setGlobalObjectEvaluators,makeSafeEvaluator,initialGlobalPropertyNames,tameFunctionToString,tameDomains,tameModuleSource,tameConsole,tameErrorConstructor,assert,makeAssert,getAnonymousIntrinsics,makeCompartmentConstructor,tameHarden,tameSymbolConstructor,tameFauxDataProperties,tameRegeneratorRuntime,shimArrayBufferTransfer,reportInGroup,chooseReporter;$h͏_imports([["@endo/env-options",[["getEnvironmentOption",[$h͏_a=>getenv=$h͏_a]]]],["@endo/immutable-arraybuffer/shim.js",[]],["./commons.js",[["FERAL_FUNCTION",[$h͏_a=>FERAL_FUNCTION=$h͏_a]],["FERAL_EVAL",[$h͏_a=>FERAL_EVAL=$h͏_a]],["TypeError",[$h͏_a=>TypeError=$h͏_a]],["arrayFilter",[$h͏_a=>arrayFilter=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]],["is",[$h͏_a=>is=$h͏_a]],["ownKeys",[$h͏_a=>ownKeys=$h͏_a]],["stringSplit",[$h͏_a=>stringSplit=$h͏_a]],["noEvalEvaluate",[$h͏_a=>noEvalEvaluate=$h͏_a]],["getOwnPropertyNames",[$h͏_a=>getOwnPropertyNames=$h͏_a]],["getPrototypeOf",[$h͏_a=>getPrototypeOf=$h͏_a]]]],["./make-hardener.js",[["makeHardener",[$h͏_a=>makeHardener=$h͏_a]]]],["./intrinsics.js",[["makeIntrinsicsCollector",[$h͏_a=>makeIntrinsicsCollector=$h͏_a]]]],["./permits-intrinsics.js",[["default",[$h͏_a=>removeUnpermittedIntrinsics=$h͏_a]]]],["./tame-function-constructors.js",[["default",[$h͏_a=>tameFunctionConstructors=$h͏_a]]]],["./tame-date-constructor.js",[["default",[$h͏_a=>tameDateConstructor=$h͏_a]]]],["./tame-math-object.js",[["default",[$h͏_a=>tameMathObject=$h͏_a]]]],["./tame-regexp-constructor.js",[["default",[$h͏_a=>tameRegExpConstructor=$h͏_a]]]],["./enable-property-overrides.js",[["default",[$h͏_a=>enablePropertyOverrides=$h͏_a]]]],["./tame-locale-methods.js",[["default",[$h͏_a=>tameLocaleMethods=$h͏_a]]]],["./global-object.js",[["setGlobalObjectConstantProperties",[$h͏_a=>setGlobalObjectConstantProperties=$h͏_a]],["setGlobalObjectMutableProperties",[$h͏_a=>setGlobalObjectMutableProperties=$h͏_a]],["setGlobalObjectEvaluators",[$h͏_a=>setGlobalObjectEvaluators=$h͏_a]]]],["./make-safe-evaluator.js",[["makeSafeEvaluator",[$h͏_a=>makeSafeEvaluator=$h͏_a]]]],["./permits.js",[["initialGlobalPropertyNames",[$h͏_a=>initialGlobalPropertyNames=$h͏_a]]]],["./tame-function-tostring.js",[["tameFunctionToString",[$h͏_a=>tameFunctionToString=$h͏_a]]]],["./tame-domains.js",[["tameDomains",[$h͏_a=>tameDomains=$h͏_a]]]],["./tame-module-source.js",[["tameModuleSource",[$h͏_a=>tameModuleSource=$h͏_a]]]],["./error/tame-console.js",[["tameConsole",[$h͏_a=>tameConsole=$h͏_a]]]],["./error/tame-error-constructor.js",[["default",[$h͏_a=>tameErrorConstructor=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]],["makeAssert",[$h͏_a=>makeAssert=$h͏_a]]]],["./get-anonymous-intrinsics.js",[["getAnonymousIntrinsics",[$h͏_a=>getAnonymousIntrinsics=$h͏_a]]]],["./compartment.js",[["makeCompartmentConstructor",[$h͏_a=>makeCompartmentConstructor=$h͏_a]]]],["./tame-harden.js",[["tameHarden",[$h͏_a=>tameHarden=$h͏_a]]]],["./tame-symbol-constructor.js",[["tameSymbolConstructor",[$h͏_a=>tameSymbolConstructor=$h͏_a]]]],["./tame-faux-data-properties.js",[["tameFauxDataProperties",[$h͏_a=>tameFauxDataProperties=$h͏_a]]]],["./tame-regenerator-runtime.js",[["tameRegeneratorRuntime",[$h͏_a=>tameRegeneratorRuntime=$h͏_a]]]],["./shim-arraybuffer-transfer.js",[["shimArrayBufferTransfer",[$h͏_a=>shimArrayBufferTransfer=$h͏_a]]]],["./reporting.js",[["reportInGroup",[$h͏_a=>reportInGroup=$h͏_a]],["chooseReporter",[$h͏_a=>chooseReporter=$h͏_a]]]]]);const{Fail:Fail,details:X,quote:q}=assert;let priorRepairIntrinsics,priorHardenIntrinsics;const safeHarden=makeHardener();$h͏_once.repairIntrinsics(((options={})=>{const{errorTaming:errorTaming=getenv("LOCKDOWN_ERROR_TAMING","safe",["unsafe","unsafe-debug"]),errorTrapping:errorTrapping=getenv("LOCKDOWN_ERROR_TRAPPING","platform",["none","report","abort","exit"]),reporting:reporting=getenv("LOCKDOWN_REPORTING","platform",["console","none"]),unhandledRejectionTrapping:unhandledRejectionTrapping=getenv("LOCKDOWN_UNHANDLED_REJECTION_TRAPPING","report",["none"]),regExpTaming:regExpTaming=getenv("LOCKDOWN_REGEXP_TAMING","safe",["unsafe"]),localeTaming:localeTaming=getenv("LOCKDOWN_LOCALE_TAMING","safe",["unsafe"]),consoleTaming:consoleTaming=getenv("LOCKDOWN_CONSOLE_TAMING","safe",["unsafe"]),overrideTaming:overrideTaming=getenv("LOCKDOWN_OVERRIDE_TAMING","moderate",["min","severe"]),stackFiltering:stackFiltering=getenv("LOCKDOWN_STACK_FILTERING","concise",["omit-frames","shorten-paths","verbose"]),domainTaming:domainTaming=getenv("LOCKDOWN_DOMAIN_TAMING","safe",["unsafe"]),evalTaming:evalTaming=getenv("LOCKDOWN_EVAL_TAMING","safe-eval",["unsafe-eval","no-eval","safeEval","unsafeEval","noEval"]),overrideDebug:overrideDebug=arrayFilter(stringSplit(getenv("LOCKDOWN_OVERRIDE_DEBUG",""),","),(debugName=>""!==debugName)),legacyRegeneratorRuntimeTaming:legacyRegeneratorRuntimeTaming=getenv("LOCKDOWN_LEGACY_REGENERATOR_RUNTIME_TAMING","safe",["unsafe-ignore"]),__hardenTaming__:__hardenTaming__=getenv("LOCKDOWN_HARDEN_TAMING","safe",["unsafe"]),dateTaming:dateTaming,mathTaming:mathTaming,...extraOptions}=options,extraOptionsNames=ownKeys(extraOptions);0===extraOptionsNames.length||Fail`lockdown(): non supported option ${q(extraOptionsNames)}`;const reporter=chooseReporter(reporting),{warn:warn}=reporter;void 0!==dateTaming&&warn("SES The 'dateTaming' option is deprecated and does nothing. In the future specifying it will be an error."),void 0!==mathTaming&&warn("SES The 'mathTaming' option is deprecated and does nothing. In the future specifying it will be an error."),void 0===priorRepairIntrinsics||assert.fail(X`Already locked down at ${priorRepairIntrinsics} (SES_ALREADY_LOCKED_DOWN)`,TypeError),priorRepairIntrinsics=TypeError("Prior lockdown (SES_ALREADY_LOCKED_DOWN)"),priorRepairIntrinsics.stack;const{functionAllowed:functionAllowed,evalAllowed:evalAllowed,directEvalAllowed:directEvalAllowed}=(()=>{let functionAllowed,evalAllowed,directEvalAllowed;try{functionAllowed=FERAL_FUNCTION("return true")()}catch(_error){functionAllowed=!1}try{evalAllowed=FERAL_EVAL("true")}catch(_error){evalAllowed=!1}return functionAllowed&&evalAllowed&&(directEvalAllowed=FERAL_FUNCTION("eval","SES_changed",'        eval("SES_changed = true");\n        return SES_changed;\n      ')(FERAL_EVAL,!1),directEvalAllowed||delete globalThis.SES_changed),{functionAllowed:functionAllowed,evalAllowed:evalAllowed,directEvalAllowed:directEvalAllowed}})();if(!1===directEvalAllowed&&"safe-eval"===evalTaming&&(functionAllowed||evalAllowed))throw TypeError("SES cannot initialize unless 'eval' is the original intrinsic 'eval', suitable for direct eval (dynamically scoped eval) (SES_DIRECT_EVAL)");if(globalThis.Function.prototype.constructor!==globalThis.Function&&"function"==typeof globalThis.harden&&"function"==typeof globalThis.lockdown&&globalThis.Date.prototype.constructor!==globalThis.Date&&"function"==typeof globalThis.Date.now&&is(globalThis.Date.prototype.constructor.now(),NaN))throw TypeError("Already locked down but not by this SES instance (SES_MULTIPLE_INSTANCES)");tameDomains(domainTaming);const markVirtualizedNativeFunction=tameFunctionToString(),{addIntrinsics:addIntrinsics,completePrototypes:completePrototypes,finalIntrinsics:finalIntrinsics}=makeIntrinsicsCollector(reporter),tamedHarden=tameHarden(safeHarden,__hardenTaming__);addIntrinsics({harden:tamedHarden}),addIntrinsics(tameFunctionConstructors()),addIntrinsics(tameDateConstructor()),addIntrinsics(tameErrorConstructor(errorTaming,stackFiltering)),addIntrinsics(tameMathObject()),addIntrinsics(tameRegExpConstructor(regExpTaming)),addIntrinsics(tameSymbolConstructor()),addIntrinsics(shimArrayBufferTransfer()),addIntrinsics(tameModuleSource()),addIntrinsics(getAnonymousIntrinsics()),completePrototypes();const intrinsics=finalIntrinsics(),hostIntrinsics={__proto__:null};let optGetStackString;"function"==typeof globalThis.Buffer&&(hostIntrinsics.Buffer=globalThis.Buffer),"safe"===errorTaming&&(optGetStackString=intrinsics["%InitialGetStackString%"]);const consoleRecord=tameConsole(consoleTaming,errorTrapping,unhandledRejectionTrapping,optGetStackString);if(globalThis.console=consoleRecord.console,"object"==typeof consoleRecord.console._times&&(hostIntrinsics.SafeMap=getPrototypeOf(consoleRecord.console._times)),"unsafe"!==errorTaming&&"unsafe-debug"!==errorTaming||globalThis.assert!==assert||(globalThis.assert=makeAssert(void 0,!0)),tameLocaleMethods(intrinsics,localeTaming),tameFauxDataProperties(intrinsics),reportInGroup("SES Removing unpermitted intrinsics",reporter,(groupReporter=>removeUnpermittedIntrinsics(intrinsics,markVirtualizedNativeFunction,groupReporter))),setGlobalObjectConstantProperties(globalThis),setGlobalObjectMutableProperties(globalThis,{intrinsics:intrinsics,newGlobalPropertyNames:initialGlobalPropertyNames,makeCompartmentConstructor:makeCompartmentConstructor,markVirtualizedNativeFunction:markVirtualizedNativeFunction}),"no-eval"===evalTaming||"noEval"===evalTaming)setGlobalObjectEvaluators(globalThis,noEvalEvaluate,markVirtualizedNativeFunction);else if("safe-eval"===evalTaming||"safeEval"===evalTaming){const{safeEvaluate:safeEvaluate}=makeSafeEvaluator({globalObject:globalThis});setGlobalObjectEvaluators(globalThis,safeEvaluate,markVirtualizedNativeFunction)}return()=>{void 0===priorHardenIntrinsics||assert.fail(X`Already locked down at ${priorHardenIntrinsics} (SES_ALREADY_LOCKED_DOWN)`,TypeError),priorHardenIntrinsics=TypeError("Prior lockdown (SES_ALREADY_LOCKED_DOWN)"),priorHardenIntrinsics.stack,reportInGroup("SES Enabling property overrides",reporter,(groupReporter=>enablePropertyOverrides(intrinsics,overrideTaming,groupReporter,overrideDebug))),"unsafe-ignore"===legacyRegeneratorRuntimeTaming&&tameRegeneratorRuntime();const toHarden={intrinsics:intrinsics,hostIntrinsics:hostIntrinsics,globals:{Function:globalThis.Function,eval:globalThis.eval,Compartment:globalThis.Compartment,Symbol:globalThis.Symbol}};for(const prop of getOwnPropertyNames(initialGlobalPropertyNames))toHarden.globals[prop]=globalThis[prop];return tamedHarden(toHarden),tamedHarden}}))}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let globalThis,repairIntrinsics;$h͏_imports([["./assert-sloppy-mode.js",[]],["./commons.js",[["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./lockdown.js",[["repairIntrinsics",[$h͏_a=>repairIntrinsics=$h͏_a]]]]]),globalThis.lockdown=options=>{const hardenIntrinsics=repairIntrinsics(options);globalThis.harden=hardenIntrinsics()},globalThis.repairIntrinsics=options=>{const hardenIntrinsics=repairIntrinsics(options);globalThis.hardenIntrinsics=()=>{globalThis.harden=hardenIntrinsics()}}}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let globalThis,makeCompartmentConstructor,tameFunctionToString,getGlobalIntrinsics,chooseReporter;$h͏_imports([["./commons.js",[["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./compartment.js",[["makeCompartmentConstructor",[$h͏_a=>makeCompartmentConstructor=$h͏_a]]]],["./tame-function-tostring.js",[["tameFunctionToString",[$h͏_a=>tameFunctionToString=$h͏_a]]]],["./intrinsics.js",[["getGlobalIntrinsics",[$h͏_a=>getGlobalIntrinsics=$h͏_a]]]],["./reporting.js",[["chooseReporter",[$h͏_a=>chooseReporter=$h͏_a]]]]]);const markVirtualizedNativeFunction=tameFunctionToString(),muteReporter=chooseReporter("none");globalThis.Compartment=makeCompartmentConstructor(makeCompartmentConstructor,getGlobalIntrinsics(globalThis,muteReporter),markVirtualizedNativeFunction,{enforceNew:!0})}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let globalThis,assert;$h͏_imports([["./commons.js",[["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./error/assert.js",[["assert",[$h͏_a=>assert=$h͏_a]]]]]),globalThis.assert=assert}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";let symbolFor,globalThis,defineCausalConsoleFromLogger,loggedErrorHandler;$h͏_imports([["./commons.js",[["symbolFor",[$h͏_a=>symbolFor=$h͏_a]],["globalThis",[$h͏_a=>globalThis=$h͏_a]]]],["./error/console.js",[["defineCausalConsoleFromLogger",[$h͏_a=>defineCausalConsoleFromLogger=$h͏_a]]]],["./error/assert.js",[["loggedErrorHandler",[$h͏_a=>loggedErrorHandler=$h͏_a]]]]]);const makeCausalConsoleFromLoggerForSesAva=defineCausalConsoleFromLogger(loggedErrorHandler),MAKE_CAUSAL_CONSOLE_FROM_LOGGER_KEY_FOR_SES_AVA=symbolFor("MAKE_CAUSAL_CONSOLE_FROM_LOGGER_KEY_FOR_SES_AVA");globalThis[MAKE_CAUSAL_CONSOLE_FROM_LOGGER_KEY_FOR_SES_AVA]=makeCausalConsoleFromLoggerForSesAva}(),({imports:$h͏_imports,liveVar:$h͏_live,onceVar:$h͏_once,import:$h͏_import,importMeta:$h͏____meta})=>function(){"use strict";$h͏_imports([["./src/lockdown-shim.js",[]],["./src/compartment-shim.js",[]],["./src/assert-shim.js",[]],["./src/console-shim.js",[]]])}()])();