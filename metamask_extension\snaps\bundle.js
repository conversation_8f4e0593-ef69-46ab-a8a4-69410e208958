/*! For license information please see bundle.js.LICENSE.txt */
(()=>{var __webpack_modules__={4:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{J:()=>r});var e=__webpack_require__(8464),t=__webpack_require__(4729);function r(){return(0,e.YP)((0,t.Yj)(),"SVG",(e=>!!e.includes("<svg")||"Value is not a valid SVG."))}}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},46:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t="object"==typeof Reflect?Reflect:null,r=t&&"function"==typeof t.apply?t.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};e=t&&"function"==typeof t.ownKeys?t.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var n=Number.isNaN||function(e){return e!=e};function _(){_.init.call(this)}module.exports=_,module.exports.once=function(e,t){return new Promise((function(r,n){function _(r){e.removeListener(t,i),n(r)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",_),r([].slice.call(arguments))}f(e,t,i,{once:!0}),"error"!==t&&function(e,t){"function"==typeof e.on&&f(e,"error",t,{once:!0})}(e,_)}))},_.EventEmitter=_,_.prototype._events=undefined,_.prototype._eventsCount=0,_.prototype._maxListeners=undefined;var i=10;function a(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function o(e){return e._maxListeners===undefined?_.defaultMaxListeners:e._maxListeners}function s(e,t,r,n){var _,i,s,u;if(a(r),(i=e._events)===undefined?(i=e._events=Object.create(null),e._eventsCount=0):(i.newListener!==undefined&&(e.emit("newListener",t,r.listener?r.listener:r),i=e._events),s=i[t]),s===undefined)s=i[t]=r,++e._eventsCount;else if("function"==typeof s?s=i[t]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(_=o(e))>0&&s.length>_&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=s.length,u=c,console&&console.warn&&console.warn(u)}return e}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function c(e,t,r){var n={fired:!1,wrapFn:undefined,target:e,type:t,listener:r},_=u.bind(n);return _.listener=r,n.wrapFn=_,_}function p(e,t,r){var n=e._events;if(n===undefined)return[];var _=n[t];return _===undefined?[]:"function"==typeof _?r?[_.listener||_]:[_]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(_):h(_,_.length)}function l(e){var t=this._events;if(t!==undefined){var r=t[e];if("function"==typeof r)return 1;if(r!==undefined)return r.length}return 0}function h(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function f(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function _(i){n.once&&e.removeEventListener(t,_),r(i)}))}}Object.defineProperty(_,"defaultMaxListeners",{enumerable:!0,get:function(){return i},set:function(e){if("number"!=typeof e||e<0||n(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");i=e}}),_.init=function(){this._events!==undefined&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||undefined},_.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||n(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},_.prototype.getMaxListeners=function(){return o(this)},_.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var _="error"===e,i=this._events;if(i!==undefined)_=_&&i.error===undefined;else if(!_)return!1;if(_){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var o=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw o.context=a,o}var s=i[e];if(s===undefined)return!1;if("function"==typeof s)r(s,this,t);else{var u=s.length,c=h(s,u);for(n=0;n<u;++n)r(c[n],this,t)}return!0},_.prototype.addListener=function(e,t){return s(this,e,t,!1)},_.prototype.on=_.prototype.addListener,_.prototype.prependListener=function(e,t){return s(this,e,t,!0)},_.prototype.once=function(e,t){return a(t),this.on(e,c(this,e,t)),this},_.prototype.prependOnceListener=function(e,t){return a(t),this.prependListener(e,c(this,e,t)),this},_.prototype.removeListener=function(e,t){var r,n,_,i,o;if(a(t),(n=this._events)===undefined)return this;if((r=n[e])===undefined)return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(_=-1,i=r.length-1;i>=0;i--)if(r[i]===t||r[i].listener===t){o=r[i].listener,_=i;break}if(_<0)return this;0===_?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,_),1===r.length&&(n[e]=r[0]),n.removeListener!==undefined&&this.emit("removeListener",e,o||t)}return this},_.prototype.off=_.prototype.removeListener,_.prototype.removeAllListeners=function(e){var t,r,n;if((r=this._events)===undefined)return this;if(r.removeListener===undefined)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):r[e]!==undefined&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var _,i=Object.keys(r);for(n=0;n<i.length;++n)"removeListener"!==(_=i[n])&&this.removeAllListeners(_);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t!==undefined)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},_.prototype.listeners=function(e){return p(this,e,!0)},_.prototype.rawListeners=function(e){return p(this,e,!1)},_.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):l.call(e,t)},_.prototype.listenerCount=l,_.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]}}}).call(__webpack_require__._LM_("13",{module,__webpack_require__}))()},82:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function t(t){for(var n=1;n<arguments.length;n++){var _=null!=arguments[n]?arguments[n]:{};n%2?e(Object(_),!0).forEach((function(e){r(t,e,_[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(_)):e(Object(_)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(_,e))}))}return t}function r(e,t,r){return(t=_(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_(n.key),n)}}function _(e){var t=function(e){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(t!==undefined){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var i=__webpack_require__(1048).Buffer,a=__webpack_require__(3011).inspect,o=a&&a.custom||"inspect";module.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}var r,_;return r=e,(_=[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r}},{key:"concat",value:function(e){if(0===this.length)return i.alloc(0);for(var t,r,n,_=i.allocUnsafe(e>>>0),a=this.head,o=0;a;)t=a.data,r=_,n=o,i.prototype.copy.call(t,r,n),o+=a.data.length,a=a.next;return _}},{key:"consume",value:function(e,t){var r;return e<this.head.data.length?(r=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):r=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,r=1,n=t.data;for(e-=n.length;t=t.next;){var _=t.data,i=e>_.length?_.length:e;if(i===_.length?n+=_:n+=_.slice(0,e),0==(e-=i)){i===_.length?(++r,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=_.slice(i));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(e){var t=i.allocUnsafe(e),r=this.head,n=1;for(r.data.copy(t),e-=r.data.length;r=r.next;){var _=r.data,a=e>_.length?_.length:e;if(_.copy(t,t.length-e,0,a),0==(e-=a)){a===_.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=_.slice(a));break}++n}return this.length-=n,t}},{key:o,value:function(e,r){return a(this,t(t({},r),{},{depth:0,customInspect:!1}))}}])&&n(r.prototype,_),Object.defineProperty(r,"prototype",{writable:!1}),e}()}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},124:(module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(9907);exports.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+module.exports.humanize(this.diff),!this.useColors)return;const t="color: "+this.color;e.splice(1,0,t,"color: inherit");let r=0,n=0;e[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(n=r))})),e.splice(n,0,t)},exports.save=function(e){try{e?exports.storage.setItem("debug",e):exports.storage.removeItem("debug")}catch(e){}},exports.load=function(){let t;try{t=exports.storage.getItem("debug")}catch(e){}return!t&&void 0!==e&&"env"in e&&(t=e.env.DEBUG),t},exports.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},exports.storage=function(){try{return localStorage}catch(e){}}(),exports.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),exports.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],exports.log=console.debug||console.log||(()=>{}),module.exports=__webpack_require__(7891)(exports);const{formatters:t}=module.exports;t.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}}).call(__webpack_require__._LM_("12",{exports,module,__webpack_require__}))()},282:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=i,i.default=i,i.stable=u,i.stableStringify=u;var e="[...]",t="[Circular]",r=[],n=[];function _(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function i(e,t,i,a){var s;void 0===a&&(a=_()),o(e,"",0,[],undefined,0,a);try{s=0===n.length?JSON.stringify(e,t,i):JSON.stringify(e,p(t),i)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var u=r.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return s}function a(e,t,_,i){var a=Object.getOwnPropertyDescriptor(i,_);a.get!==undefined?a.configurable?(Object.defineProperty(i,_,{value:e}),r.push([i,_,t,a])):n.push([t,_,e]):(i[_]=e,r.push([i,_,t]))}function o(r,n,_,i,s,u,c){var p;if(u+=1,"object"==typeof r&&null!==r){for(p=0;p<i.length;p++)if(i[p]===r)return void a(t,r,n,s);if(void 0!==c.depthLimit&&u>c.depthLimit)return void a(e,r,n,s);if(void 0!==c.edgesLimit&&_+1>c.edgesLimit)return void a(e,r,n,s);if(i.push(r),Array.isArray(r))for(p=0;p<r.length;p++)o(r[p],p,p,i,r,u,c);else{var l=Object.keys(r);for(p=0;p<l.length;p++){var h=l[p];o(r[h],h,p,i,r,u,c)}}i.pop()}}function s(e,t){return e<t?-1:e>t?1:0}function u(e,t,i,a){void 0===a&&(a=_());var o,s=c(e,"",0,[],undefined,0,a)||e;try{o=0===n.length?JSON.stringify(s,t,i):JSON.stringify(s,p(t),i)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r.length;){var u=r.pop();4===u.length?Object.defineProperty(u[0],u[1],u[3]):u[0][u[1]]=u[2]}}return o}function c(n,_,i,o,u,p,l){var h;if(p+=1,"object"==typeof n&&null!==n){for(h=0;h<o.length;h++)if(o[h]===n)return void a(t,n,_,u);try{if("function"==typeof n.toJSON)return}catch(e){return}if(void 0!==l.depthLimit&&p>l.depthLimit)return void a(e,n,_,u);if(void 0!==l.edgesLimit&&i+1>l.edgesLimit)return void a(e,n,_,u);if(o.push(n),Array.isArray(n))for(h=0;h<n.length;h++)c(n[h],h,h,o,n,p,l);else{var f={},d=Object.keys(n).sort(s);for(h=0;h<d.length;h++){var w=d[h];c(n[w],w,h,o,n,p,l),f[w]=n[w]}if(void 0===u)return f;r.push([u,_,n]),u[_]=f}o.pop()}}function p(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(n.length>0)for(var _=0;_<n.length;_++){var i=n[_];if(i[1]===t&&i[0]===r){r=i[2],n.splice(_,1);break}}return e.call(this,t,r)}}}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},511:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Tl:()=>_,Wu:()=>n,Z6:()=>e});var e,t=__webpack_require__(4729),r=__webpack_require__(7566);!function(e){e.Copyable="copyable",e.Divider="divider",e.Heading="heading",e.Panel="panel",e.Spinner="spinner",e.Text="text",e.Image="image",e.Row="row",e.Address="address",e.Button="button",e.Input="input",e.Form="form"}(e||(e={}));const n=(0,t.Ik)({type:(0,t.Yj)()}),_=(0,r.kp)(n,(0,t.Ik)({value:(0,t.L5)()}))}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},534:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t=__webpack_require__(9907);function r(e,t,r){return(t=function(e){var t=function(e){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(t!==undefined){var r=t.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var n=__webpack_require__(4869),_=Symbol("lastResolve"),i=Symbol("lastReject"),a=Symbol("error"),o=Symbol("ended"),s=Symbol("lastPromise"),u=Symbol("handlePromise"),c=Symbol("stream");function p(e,t){return{value:e,done:t}}function l(e){var t=e[_];if(null!==t){var r=e[c].read();null!==r&&(e[s]=null,e[_]=null,e[i]=null,t(p(r,!1)))}}function h(e){t.nextTick(l,e)}var f=Object.getPrototypeOf((function(){})),d=Object.setPrototypeOf((r(e={get stream(){return this[c]},next:function(){var e=this,r=this[a];if(null!==r)return Promise.reject(r);if(this[o])return Promise.resolve(p(undefined,!0));if(this[c].destroyed)return new Promise((function(r,n){t.nextTick((function(){e[a]?n(e[a]):r(p(undefined,!0))}))}));var n,_=this[s];if(_)n=new Promise(function(e,t){return function(r,n){e.then((function(){t[o]?r(p(undefined,!0)):t[u](r,n)}),n)}}(_,this));else{var i=this[c].read();if(null!==i)return Promise.resolve(p(i,!1));n=new Promise(this[u])}return this[s]=n,n}},Symbol.asyncIterator,(function(){return this})),r(e,"return",(function(){var e=this;return new Promise((function(t,r){e[c].destroy(null,(function(e){e?r(e):t(p(undefined,!0))}))}))})),e),f);module.exports=function(e){var t,l=Object.create(d,(r(t={},c,{value:e,writable:!0}),r(t,_,{value:null,writable:!0}),r(t,i,{value:null,writable:!0}),r(t,a,{value:null,writable:!0}),r(t,o,{value:e._readableState.endEmitted,writable:!0}),r(t,u,{value:function(e,t){var r=l[c].read();r?(l[s]=null,l[_]=null,l[i]=null,e(p(r,!1))):(l[_]=e,l[i]=t)},writable:!0}),t));return l[s]=null,n(e,(function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=l[i];return null!==t&&(l[s]=null,l[_]=null,l[i]=null,t(e)),void(l[a]=e)}var r=l[_];null!==r&&(l[s]=null,l[_]=null,l[i]=null,r(p(undefined,!0))),l[o]=!0})),e.on("readable",h.bind(null,l)),l}}}).call(__webpack_require__._LM_("16",{__webpack_require__,module}))()},544:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{$:()=>i});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(6725),_=__webpack_require__(511);const i=(0,e.kp)(_.Tl,(0,t.Ik)({type:(0,r.eu)(_.Z6.Copyable),value:(0,t.Yj)(),sensitive:(0,t.lq)((0,t.zM)())}));(0,n.I)(_.Z6.Copyable,i,["value","sensitive"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},583:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7476),t=__webpack_require__(1565),{ANY:r}=t,n=__webpack_require__(7229),_=__webpack_require__(7851),i=[new t(">=0.0.0-0")],a=[new t(">=0.0.0")],o=(e,t,o)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===r){if(1===t.length&&t[0].semver===r)return!0;e=o.includePrerelease?i:a}if(1===t.length&&t[0].semver===r){if(o.includePrerelease)return!0;t=a}const c=new Set;let p,l,h,f,d,w,b;for(const t of e)">"===t.operator||">="===t.operator?p=s(p,t,o):"<"===t.operator||"<="===t.operator?l=u(l,t,o):c.add(t.semver);if(c.size>1)return null;if(p&&l){if(h=_(p.semver,l.semver,o),h>0)return null;if(0===h&&(">="!==p.operator||"<="!==l.operator))return null}for(const e of c){if(p&&!n(e,String(p),o))return null;if(l&&!n(e,String(l),o))return null;for(const r of t)if(!n(e,String(r),o))return!1;return!0}let k=!(!l||o.includePrerelease||!l.semver.prerelease.length)&&l.semver,m=!(!p||o.includePrerelease||!p.semver.prerelease.length)&&p.semver;k&&1===k.prerelease.length&&"<"===l.operator&&0===k.prerelease[0]&&(k=!1);for(const e of t){if(b=b||">"===e.operator||">="===e.operator,w=w||"<"===e.operator||"<="===e.operator,p)if(m&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===m.major&&e.semver.minor===m.minor&&e.semver.patch===m.patch&&(m=!1),">"===e.operator||">="===e.operator){if(f=s(p,e,o),f===e&&f!==p)return!1}else if(">="===p.operator&&!n(p.semver,String(e),o))return!1;if(l)if(k&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===k.major&&e.semver.minor===k.minor&&e.semver.patch===k.patch&&(k=!1),"<"===e.operator||"<="===e.operator){if(d=u(l,e,o),d===e&&d!==l)return!1}else if("<="===l.operator&&!n(l.semver,String(e),o))return!1;if(!e.operator&&(l||p)&&0!==h)return!1}return!(p&&w&&!l&&0!==h||l&&b&&!p&&0!==h||m||k)},s=(e,t,r)=>{if(!e)return t;const n=_(e.semver,t.semver,r);return n>0?e:n<0||">"===t.operator&&">="===e.operator?t:e},u=(e,t,r)=>{if(!e)return t;const n=_(e.semver,t.semver,r);return n<0?e:n>0||"<"===t.operator&&"<="===e.operator?t:e};module.exports=(t,r,n={})=>{if(t===r)return!0;t=new e(t,n),r=new e(r,n);let _=!1;e:for(const e of t.set){for(const t of r.set){const r=o(e,t,n);if(_=_||null!==r,r)continue e}if(_)return!1}return!0}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},776:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{s:()=>t});var e=__webpack_require__(7566);function t(t,r){return(0,e.E8)(t,(e=>"string"==typeof e&&r.test(e)))}}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},845:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{r:()=>n});var e=__webpack_require__(7012),t=__webpack_require__(7822),r=__webpack_require__(6231);const n={parse:e=>_(t.f.rpc.parse,e),invalidRequest:e=>_(t.f.rpc.invalidRequest,e),invalidParams:e=>_(t.f.rpc.invalidParams,e),methodNotFound:e=>_(t.f.rpc.methodNotFound,e),internal:e=>_(t.f.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw new Error("Ethereum RPC Server errors must provide single object argument.");const{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw new Error('"code" must be an integer such that: -32099 <= code <= -32005');return _(t,e)},invalidInput:e=>_(t.f.rpc.invalidInput,e),resourceNotFound:e=>_(t.f.rpc.resourceNotFound,e),resourceUnavailable:e=>_(t.f.rpc.resourceUnavailable,e),transactionRejected:e=>_(t.f.rpc.transactionRejected,e),methodNotSupported:e=>_(t.f.rpc.methodNotSupported,e),limitExceeded:e=>_(t.f.rpc.limitExceeded,e)};function _(t,n){const[_,i]=function(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&"string"!=typeof t)throw new Error("Must specify string message.");return[t??undefined,r]}}return[]}(n);return new e.G(t,_??(0,r.DW)(t),i)}}}).call(__webpack_require__._LM_("6",{__webpack_require__,__webpack_exports__}))()},872:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{G:()=>s});var e=__webpack_require__(5875),t=__webpack_require__.n(e),r=__webpack_require__(992),n=__webpack_require__(8323),_=__webpack_require__(2147),i=__webpack_require__(4156),a=__webpack_require__(8662),o=__webpack_require__(949);class s extends a.j{static initialize(e=new r.UI({name:"child",target:"parent",targetWindow:self.parent,targetOrigin:"*"})){(0,o.R)("Worker: Connecting to parent.");const a=new(t());(0,i.pipeline)(e,a,e,(e=>{e&&(0,n.vV)("Parent stream failure, closing worker.",e),self.close()}));const u=a.createStream(_.FI.COMMAND),c=a.createStream(_.FI.JSON_RPC);return new s(u,c)}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},949:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{R:()=>t});var e=__webpack_require__(8323);const t=(0,__webpack_require__(2869).X)(e.K1,"snaps-execution-environments")}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},992:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{UI:()=>e.U});var e=__webpack_require__(9950);__webpack_require__(1427),__webpack_require__(7123),__webpack_require__(9886),__webpack_require__(2866)}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},1010:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{t:()=>i});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(6725),_=__webpack_require__(511);const i=(0,e.kp)(_.Wu,(0,t.Ik)({type:(0,r.eu)(_.Z6.Spinner)}));(0,n.I)(_.Z6.Spinner,i)}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},1048:(__unused_webpack_module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7991),t=__webpack_require__(9318),r="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;exports.Buffer=i,exports.SlowBuffer=function(e){return+e!=e&&(e=0),i.alloc(+e)},exports.INSPECT_MAX_BYTES=50;const n=2147483647;function _(e){if(e>n)throw new RangeError('The value "'+e+'" is invalid for option "size"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,i.prototype),t}function i(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return s(e)}return a(e,t,r)}function a(e,t,r){if("string"==typeof e)return function(e,t){if("string"==typeof t&&""!==t||(t="utf8"),!i.isEncoding(t))throw new TypeError("Unknown encoding: "+t);const r=0|l(e,t);let n=_(r);const a=n.write(e,t);return a!==r&&(n=n.slice(0,a)),n}(e,t);if(ArrayBuffer.isView(e))return function(e){if(z(e,Uint8Array)){const t=new Uint8Array(e);return c(t.buffer,t.byteOffset,t.byteLength)}return u(e)}(e);if(null==e)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(z(e,ArrayBuffer)||e&&z(e.buffer,ArrayBuffer))return c(e,t,r);if("undefined"!=typeof SharedArrayBuffer&&(z(e,SharedArrayBuffer)||e&&z(e.buffer,SharedArrayBuffer)))return c(e,t,r);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');const n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return i.from(n,t,r);const a=function(e){if(i.isBuffer(e)){const t=0|p(e.length),r=_(t);return 0===r.length||e.copy(r,0,0,t),r}return e.length!==undefined?"number"!=typeof e.length||W(e.length)?_(0):u(e):"Buffer"===e.type&&Array.isArray(e.data)?u(e.data):void 0}(e);if(a)return a;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return i.from(e[Symbol.toPrimitive]("string"),t,r);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function o(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function s(e){return o(e),_(e<0?0:0|p(e))}function u(e){const t=e.length<0?0:0|p(e.length),r=_(t);for(let n=0;n<t;n+=1)r[n]=255&e[n];return r}function c(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('"length" is outside of buffer bounds');let n;return n=t===undefined&&r===undefined?new Uint8Array(e):r===undefined?new Uint8Array(e,t):new Uint8Array(e,t,r),Object.setPrototypeOf(n,i.prototype),n}function p(e){if(e>=n)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n.toString(16)+" bytes");return 0|e}function l(e,t){if(i.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||z(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let _=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return U(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return B(e).length;default:if(_)return n?-1:U(e).length;t=(""+t).toLowerCase(),_=!0}}function h(e,t,r){let n=!1;if((t===undefined||t<0)&&(t=0),t>this.length)return"";if((r===undefined||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return R(this,t,r);case"utf8":case"utf-8":return v(this,t,r);case"ascii":return S(this,t,r);case"latin1":case"binary":return x(this,t,r);case"base64":return q(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function f(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function d(e,t,r,n,_){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),W(r=+r)&&(r=_?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(_)return-1;r=e.length-1}else if(r<0){if(!_)return-1;r=0}if("string"==typeof t&&(t=i.from(t,n)),i.isBuffer(t))return 0===t.length?-1:w(e,t,r,n,_);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?_?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):w(e,[t],r,n,_);throw new TypeError("val must be string, number or Buffer")}function w(e,t,r,n,_){let i,a=1,o=e.length,s=t.length;if(n!==undefined&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,o/=2,s/=2,r/=2}function u(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(_){let n=-1;for(i=r;i<o;i++)if(u(e,i)===u(t,-1===n?0:i-n)){if(-1===n&&(n=i),i-n+1===s)return n*a}else-1!==n&&(i-=i-n),n=-1}else for(r+s>o&&(r=o-s),i=r;i>=0;i--){let r=!0;for(let n=0;n<s;n++)if(u(e,i+n)!==u(t,n)){r=!1;break}if(r)return i}return-1}function b(e,t,r,n){r=Number(r)||0;const _=e.length-r;n?(n=Number(n))>_&&(n=_):n=_;const i=t.length;let a;for(n>i/2&&(n=i/2),a=0;a<n;++a){const n=parseInt(t.substr(2*a,2),16);if(W(n))return a;e[r+a]=n}return a}function k(e,t,r,n){return Y(U(t,e.length-r),e,r,n)}function m(e,t,r,n){return Y(function(e){const t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function g(e,t,r,n){return Y(B(t),e,r,n)}function y(e,t,r,n){return Y(function(e,t){let r,n,_;const i=[];for(let a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,_=r%256,i.push(_),i.push(n);return i}(t,e.length-r),e,r,n)}function q(t,r,n){return 0===r&&n===t.length?e.fromByteArray(t):e.fromByteArray(t.slice(r,n))}function v(e,t,r){r=Math.min(e.length,r);const n=[];let _=t;for(;_<r;){const t=e[_];let i=null,a=t>239?4:t>223?3:t>191?2:1;if(_+a<=r){let r,n,o,s;switch(a){case 1:t<128&&(i=t);break;case 2:r=e[_+1],128==(192&r)&&(s=(31&t)<<6|63&r,s>127&&(i=s));break;case 3:r=e[_+1],n=e[_+2],128==(192&r)&&128==(192&n)&&(s=(15&t)<<12|(63&r)<<6|63&n,s>2047&&(s<55296||s>57343)&&(i=s));break;case 4:r=e[_+1],n=e[_+2],o=e[_+3],128==(192&r)&&128==(192&n)&&128==(192&o)&&(s=(15&t)<<18|(63&r)<<12|(63&n)<<6|63&o,s>65535&&s<1114112&&(i=s))}}null===i?(i=65533,a=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|1023&i),n.push(i),_+=a}return function(e){const t=e.length;if(t<=E)return String.fromCharCode.apply(String,e);let r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=E));return r}(n)}exports.kMaxLength=n,i.TYPED_ARRAY_SUPPORT=function(){try{const e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),i.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(i.prototype,"parent",{enumerable:!0,get:function(){return i.isBuffer(this)?this.buffer:undefined}}),Object.defineProperty(i.prototype,"offset",{enumerable:!0,get:function(){return i.isBuffer(this)?this.byteOffset:undefined}}),i.poolSize=8192,i.from=function(e,t,r){return a(e,t,r)},Object.setPrototypeOf(i.prototype,Uint8Array.prototype),Object.setPrototypeOf(i,Uint8Array),i.alloc=function(e,t,r){return function(e,t,r){return o(e),e<=0?_(e):t!==undefined?"string"==typeof r?_(e).fill(t,r):_(e).fill(t):_(e)}(e,t,r)},i.allocUnsafe=function(e){return s(e)},i.allocUnsafeSlow=function(e){return s(e)},i.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==i.prototype},i.compare=function(e,t){if(z(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),z(t,Uint8Array)&&(t=i.from(t,t.offset,t.byteLength)),!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,n=t.length;for(let _=0,i=Math.min(r,n);_<i;++_)if(e[_]!==t[_]){r=e[_],n=t[_];break}return r<n?-1:n<r?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);let r;if(t===undefined)for(t=0,r=0;r<e.length;++r)t+=e[r].length;const n=i.allocUnsafe(t);let _=0;for(r=0;r<e.length;++r){let t=e[r];if(z(t,Uint8Array))_+t.length>n.length?(i.isBuffer(t)||(t=i.from(t)),t.copy(n,_)):Uint8Array.prototype.set.call(n,t,_);else{if(!i.isBuffer(t))throw new TypeError('"list" argument must be an Array of Buffers');t.copy(n,_)}_+=t.length}return n},i.byteLength=l,i.prototype._isBuffer=!0,i.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)f(this,t,t+1);return this},i.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)f(this,t,t+3),f(this,t+1,t+2);return this},i.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)f(this,t,t+7),f(this,t+1,t+6),f(this,t+2,t+5),f(this,t+3,t+4);return this},i.prototype.toString=function(){const e=this.length;return 0===e?"":0===arguments.length?v(this,0,e):h.apply(this,arguments)},i.prototype.toLocaleString=i.prototype.toString,i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){let e="";const t=exports.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},r&&(i.prototype[r]=i.prototype.inspect),i.prototype.compare=function(e,t,r,n,_){if(z(e,Uint8Array)&&(e=i.from(e,e.offset,e.byteLength)),!i.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===undefined&&(t=0),r===undefined&&(r=e?e.length:0),n===undefined&&(n=0),_===undefined&&(_=this.length),t<0||r>e.length||n<0||_>this.length)throw new RangeError("out of range index");if(n>=_&&t>=r)return 0;if(n>=_)return-1;if(t>=r)return 1;if(this===e)return 0;let a=(_>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0);const s=Math.min(a,o),u=this.slice(n,_),c=e.slice(t,r);for(let e=0;e<s;++e)if(u[e]!==c[e]){a=u[e],o=c[e];break}return a<o?-1:o<a?1:0},i.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},i.prototype.indexOf=function(e,t,r){return d(this,e,t,r,!0)},i.prototype.lastIndexOf=function(e,t,r){return d(this,e,t,r,!1)},i.prototype.write=function(e,t,r,n){if(t===undefined)n="utf8",r=this.length,t=0;else if(r===undefined&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,n===undefined&&(n="utf8")):(n=r,r=undefined)}const _=this.length-t;if((r===undefined||r>_)&&(r=_),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let i=!1;for(;;)switch(n){case"hex":return b(this,e,t,r);case"utf8":case"utf-8":return k(this,e,t,r);case"ascii":case"latin1":case"binary":return m(this,e,t,r);case"base64":return g(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return y(this,e,t,r);default:if(i)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};const E=4096;function S(e,t,r){let n="";r=Math.min(e.length,r);for(let _=t;_<r;++_)n+=String.fromCharCode(127&e[_]);return n}function x(e,t,r){let n="";r=Math.min(e.length,r);for(let _=t;_<r;++_)n+=String.fromCharCode(e[_]);return n}function R(e,t,r){const n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);let _="";for(let n=t;n<r;++n)_+=Z[e[n]];return _}function T(e,t,r){const n=e.slice(t,r);let _="";for(let e=0;e<n.length-1;e+=2)_+=String.fromCharCode(n[e]+256*n[e+1]);return _}function M(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function O(e,t,r,n,_,a){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>_||t<a)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function L(e,t,r,n,_){G(t,n,_,e,r,7);let i=Number(t&BigInt(4294967295));e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i;let a=Number(t>>BigInt(32)&BigInt(4294967295));return e[r++]=a,a>>=8,e[r++]=a,a>>=8,e[r++]=a,a>>=8,e[r++]=a,r}function A(e,t,r,n,_){G(t,n,_,e,r,7);let i=Number(t&BigInt(4294967295));e[r+7]=i,i>>=8,e[r+6]=i,i>>=8,e[r+5]=i,i>>=8,e[r+4]=i;let a=Number(t>>BigInt(32)&BigInt(4294967295));return e[r+3]=a,a>>=8,e[r+2]=a,a>>=8,e[r+1]=a,a>>=8,e[r]=a,r+8}function I(e,t,r,n,_,i){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function C(e,r,n,_,i){return r=+r,n>>>=0,i||I(e,0,n,4),t.write(e,r,n,_,23,4),n+4}function j(e,r,n,_,i){return r=+r,n>>>=0,i||I(e,0,n,8),t.write(e,r,n,_,52,8),n+8}i.prototype.slice=function(e,t){const r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=t===undefined?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);const n=this.subarray(e,t);return Object.setPrototypeOf(n,i.prototype),n},i.prototype.readUintLE=i.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||M(e,t,this.length);let n=this[e],_=1,i=0;for(;++i<t&&(_*=256);)n+=this[e+i]*_;return n},i.prototype.readUintBE=i.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||M(e,t,this.length);let n=this[e+--t],_=1;for(;t>0&&(_*=256);)n+=this[e+--t]*_;return n},i.prototype.readUint8=i.prototype.readUInt8=function(e,t){return e>>>=0,t||M(e,1,this.length),this[e]},i.prototype.readUint16LE=i.prototype.readUInt16LE=function(e,t){return e>>>=0,t||M(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUint16BE=i.prototype.readUInt16BE=function(e,t){return e>>>=0,t||M(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUint32LE=i.prototype.readUInt32LE=function(e,t){return e>>>=0,t||M(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUint32BE=i.prototype.readUInt32BE=function(e,t){return e>>>=0,t||M(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readBigUInt64LE=J((function(e){F(e>>>=0,"offset");const t=this[e],r=this[e+7];t!==undefined&&r!==undefined||H(e,this.length-8);const n=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,_=this[++e]+256*this[++e]+65536*this[++e]+r*2**24;return BigInt(n)+(BigInt(_)<<BigInt(32))})),i.prototype.readBigUInt64BE=J((function(e){F(e>>>=0,"offset");const t=this[e],r=this[e+7];t!==undefined&&r!==undefined||H(e,this.length-8);const n=t*2**24+65536*this[++e]+256*this[++e]+this[++e],_=this[++e]*2**24+65536*this[++e]+256*this[++e]+r;return(BigInt(n)<<BigInt(32))+BigInt(_)})),i.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||M(e,t,this.length);let n=this[e],_=1,i=0;for(;++i<t&&(_*=256);)n+=this[e+i]*_;return _*=128,n>=_&&(n-=Math.pow(2,8*t)),n},i.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||M(e,t,this.length);let n=t,_=1,i=this[e+--n];for(;n>0&&(_*=256);)i+=this[e+--n]*_;return _*=128,i>=_&&(i-=Math.pow(2,8*t)),i},i.prototype.readInt8=function(e,t){return e>>>=0,t||M(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){e>>>=0,t||M(e,2,this.length);const r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt16BE=function(e,t){e>>>=0,t||M(e,2,this.length);const r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt32LE=function(e,t){return e>>>=0,t||M(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return e>>>=0,t||M(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readBigInt64LE=J((function(e){F(e>>>=0,"offset");const t=this[e],r=this[e+7];t!==undefined&&r!==undefined||H(e,this.length-8);const n=this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),i.prototype.readBigInt64BE=J((function(e){F(e>>>=0,"offset");const t=this[e],r=this[e+7];t!==undefined&&r!==undefined||H(e,this.length-8);const n=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(n)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+r)})),i.prototype.readFloatLE=function(e,r){return e>>>=0,r||M(e,4,this.length),t.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,r){return e>>>=0,r||M(e,4,this.length),t.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,r){return e>>>=0,r||M(e,8,this.length),t.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,r){return e>>>=0,r||M(e,8,this.length),t.read(this,e,!1,52,8)},i.prototype.writeUintLE=i.prototype.writeUIntLE=function(e,t,r,n){e=+e,t>>>=0,r>>>=0,n||O(this,e,t,r,Math.pow(2,8*r)-1,0);let _=1,i=0;for(this[t]=255&e;++i<r&&(_*=256);)this[t+i]=e/_&255;return t+r},i.prototype.writeUintBE=i.prototype.writeUIntBE=function(e,t,r,n){e=+e,t>>>=0,r>>>=0,n||O(this,e,t,r,Math.pow(2,8*r)-1,0);let _=r-1,i=1;for(this[t+_]=255&e;--_>=0&&(i*=256);)this[t+_]=e/i&255;return t+r},i.prototype.writeUint8=i.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,1,255,0),this[t]=255&e,t+1},i.prototype.writeUint16LE=i.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeUint16BE=i.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeUint32LE=i.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},i.prototype.writeUint32BE=i.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeBigUInt64LE=J((function(e,t=0){return L(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),i.prototype.writeBigUInt64BE=J((function(e,t=0){return A(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))})),i.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);O(this,e,t,r,n-1,-n)}let _=0,i=1,a=0;for(this[t]=255&e;++_<r&&(i*=256);)e<0&&0===a&&0!==this[t+_-1]&&(a=1),this[t+_]=(e/i|0)-a&255;return t+r},i.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);O(this,e,t,r,n-1,-n)}let _=r-1,i=1,a=0;for(this[t+_]=255&e;--_>=0&&(i*=256);)e<0&&0===a&&0!==this[t+_+1]&&(a=1),this[t+_]=(e/i|0)-a&255;return t+r},i.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},i.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||O(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeBigInt64LE=J((function(e,t=0){return L(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),i.prototype.writeBigInt64BE=J((function(e,t=0){return A(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))})),i.prototype.writeFloatLE=function(e,t,r){return C(this,e,t,!0,r)},i.prototype.writeFloatBE=function(e,t,r){return C(this,e,t,!1,r)},i.prototype.writeDoubleLE=function(e,t,r){return j(this,e,t,!0,r)},i.prototype.writeDoubleBE=function(e,t,r){return j(this,e,t,!1,r)},i.prototype.copy=function(e,t,r,n){if(!i.isBuffer(e))throw new TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);const _=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),_},i.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),n!==undefined&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!i.isEncoding(n))throw new TypeError("Unknown encoding: "+n);if(1===e.length){const t=e.charCodeAt(0);("utf8"===n&&t<128||"latin1"===n)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;let _;if(t>>>=0,r=r===undefined?this.length:r>>>0,e||(e=0),"number"==typeof e)for(_=t;_<r;++_)this[_]=e;else{const a=i.isBuffer(e)?e:i.from(e,n),o=a.length;if(0===o)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(_=0;_<r-t;++_)this[_+t]=a[_%o]}return this};const P={};function $(e,t,r){P[e]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function N(e){let t="",r=e.length;const n="-"===e[0]?1:0;for(;r>=n+4;r-=3)t=`_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function G(e,t,r,n,_,i){if(e>r||e<t){const n="bigint"==typeof t?"n":"";let _;throw _=i>3?0===t||t===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(i+1)}${n}`:`>= -(2${n} ** ${8*(i+1)-1}${n}) and < 2 ** ${8*(i+1)-1}${n}`:`>= ${t}${n} and <= ${r}${n}`,new P.ERR_OUT_OF_RANGE("value",_,e)}!function(e,t,r){F(t,"offset"),e[t]!==undefined&&e[t+r]!==undefined||H(t,e.length-(r+1))}(n,_,i)}function F(e,t){if("number"!=typeof e)throw new P.ERR_INVALID_ARG_TYPE(t,"number",e)}function H(e,t,r){if(Math.floor(e)!==e)throw F(e,r),new P.ERR_OUT_OF_RANGE(r||"offset","an integer",e);if(t<0)throw new P.ERR_BUFFER_OUT_OF_BOUNDS;throw new P.ERR_OUT_OF_RANGE(r||"offset",`>= ${r?1:0} and <= ${t}`,e)}$("ERR_BUFFER_OUT_OF_BOUNDS",(function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"}),RangeError),$("ERR_INVALID_ARG_TYPE",(function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`}),TypeError),$("ERR_OUT_OF_RANGE",(function(e,t,r){let n=`The value of "${e}" is out of range.`,_=r;return Number.isInteger(r)&&Math.abs(r)>2**32?_=N(String(r)):"bigint"==typeof r&&(_=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(_=N(_)),_+="n"),n+=` It must be ${t}. Received ${_}`,n}),RangeError);const D=/[^+/0-9A-Za-z-_]/g;function U(e,t){let r;t=t||Infinity;const n=e.length;let _=null;const i=[];for(let a=0;a<n;++a){if(r=e.charCodeAt(a),r>55295&&r<57344){if(!_){if(r>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&i.push(239,191,189);continue}_=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),_=r;continue}r=65536+(_-55296<<10|r-56320)}else _&&(t-=3)>-1&&i.push(239,191,189);if(_=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function B(t){return e.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(D,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(t))}function Y(e,t,r,n){let _;for(_=0;_<n&&!(_+r>=t.length||_>=e.length);++_)t[_+r]=e[_];return _}function z(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function W(e){return e!=e}const Z=function(){const e="0123456789abcdef",t=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let _=0;_<16;++_)t[n+_]=e[r]+e[_]}return t}();function J(e){return"undefined"==typeof BigInt?V:e}function V(){throw new Error("BigInt not supported")}}}).call(__webpack_require__._LM_("11",{exports,__webpack_require__}))()},1089:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{UG:()=>r});var e=__webpack_require__(8464),t=__webpack_require__(4729);(0,e.T1)((0,t.Yj)(),/^(?:0x)?[0-9a-f]+$/iu),(0,e.T1)((0,t.Yj)(),/^0x[0-9a-f]+$/iu),(0,e.T1)((0,t.Yj)(),/^0x[0-9a-f]{40}$/u);const r=(0,e.T1)((0,t.Yj)(),/^0x[0-9a-fA-F]{40}$/u)}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},1260:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=function(){throw new Error("Readable.from is not available in the browser")}}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},1262:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>e(t,r,n)<0}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},1265:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(9907),t=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};module.exports=o;var r=__webpack_require__(8199),n=__webpack_require__(5291);__webpack_require__(5615)(o,r);for(var _=t(n.prototype),i=0;i<_.length;i++){var a=_[i];o.prototype[a]||(o.prototype[a]=n.prototype[a])}function o(e){if(!(this instanceof o))return new o(e);r.call(this,e),n.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",s)))}function s(){this._writableState.ended||e.nextTick(u,this)}function u(e){e.end()}Object.defineProperty(o.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(o.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(o.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(o.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState!==undefined&&this._writableState!==undefined&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){this._readableState!==undefined&&this._writableState!==undefined&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}})}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},1280:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517),t=__webpack_require__(7476),r=__webpack_require__(9761);module.exports=(n,_)=>{n=new t(n,_);let i=new e("0.0.0");if(n.test(i))return i;if(i=new e("0.0.0-0"),n.test(i))return i;i=null;for(let t=0;t<n.set.length;++t){const _=n.set[t];let a=null;_.forEach((t=>{const n=new e(t.semver.version);switch(t.operator){case">":0===n.prerelease.length?n.patch++:n.prerelease.push(0),n.raw=n.format();case"":case">=":a&&!r(n,a)||(a=n);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${t.operator}`)}})),!a||i&&!r(i,a)||(i=a)}return i&&n.test(i)?i:null}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},1353:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r)=>new e(t,r).minor}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},1361:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(9907);const t="object"==typeof e&&e.env&&e.env.NODE_DEBUG&&/\bsemver\b/i.test(e.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};module.exports=t}}).call(__webpack_require__._LM_("18",{__webpack_require__,module}))()},1417:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{M8:()=>l,R2:()=>o,Xe:()=>c,fR:()=>p,gL:()=>u});var e=__webpack_require__(845),t=__webpack_require__(1750),r=__webpack_require__(9400),n=__webpack_require__(5259),_=__webpack_require__(2462),i=__webpack_require__(949);const a=64e6;async function o(e,t){const r=t.lastTeardown;return new Promise(((n,_)=>{e.then((e=>{t.lastTeardown===r?n(e):(0,i.R)("Late promise received after Snap finished execution. Promise will be dropped.")})).catch((e=>{t.lastTeardown===r?_(e):(0,i.R)("Late promise received after Snap finished execution. Promise will be dropped.")}))}))}const s=Object.freeze(["wallet_requestPermissions","wallet_revokePermissions","eth_sendTransaction","eth_decrypt","eth_getEncryptionPublicKey","wallet_addEthereumChain","wallet_watchAsset","wallet_registerOnboarding","wallet_scanQRCode"]);function u(t){(0,r.vA)(String.prototype.startsWith.call(t.method,"wallet_")||String.prototype.startsWith.call(t.method,"snap_"),"The global Snap API only allows RPC methods starting with `wallet_*` and `snap_*`.",e.r.methodNotSupported),(0,r.vA)(!s.includes(t.method),e.r.methodNotFound({data:{method:t.method}}))}function c(t){(0,r.vA)(!String.prototype.startsWith.call(t.method,"snap_"),e.r.methodNotFound({data:{method:t.method}})),(0,r.vA)(!s.includes(t.method),e.r.methodNotFound({data:{method:t.method}}))}function p(e){const t=JSON.parse(JSON.stringify(e));return(0,n.SS)(t)}function l(e){return!!(0,_.Gv)(e)&&(0,t.m)(e)<a}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},1427:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(2866);__webpack_require__(7996),e.l}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},1539:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>r});var e=__webpack_require__(6932),t=__webpack_require__(8746);const r={names:["Math"],factory:function(){const r=Object.getOwnPropertyNames(t.h.Math).reduce(((e,r)=>"random"===r?e:{...e,[r]:t.h.Math[r]}),{}),{crypto:n}=(0,e.m)();return harden({Math:{...r,random:()=>n.getRandomValues(new Uint32Array(1))[0]/2**32}})}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},1565:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(n,_){if(_=r(_),n instanceof t){if(n.loose===!!_.loose)return n;n=n.value}n=n.trim().split(/\s+/).join(" "),a("comparator",n,_),this.options=_,this.loose=!!_.loose,this.parse(n),this.semver===e?this.value="":this.value=this.operator+this.semver.version,a("comp",this)}parse(t){const r=this.options.loose?n[_.COMPARATORLOOSE]:n[_.COMPARATOR],i=t.match(r);if(!i)throw new TypeError(`Invalid comparator: ${t}`);this.operator=i[1]!==undefined?i[1]:"","="===this.operator&&(this.operator=""),i[2]?this.semver=new o(i[2],this.options.loose):this.semver=e}toString(){return this.value}test(t){if(a("Comparator.test",t,this.options.loose),this.semver===e||t===e)return!0;if("string"==typeof t)try{t=new o(t,this.options)}catch(e){return!1}return i(t,this.operator,this.semver,this.options)}intersects(e,n){if(!(e instanceof t))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new s(e.value,n).test(this.value):""===e.operator?""===e.value||new s(this.value,n).test(e.semver):!((n=r(n)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!n.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0"))||(!this.operator.startsWith(">")||!e.operator.startsWith(">"))&&(!this.operator.startsWith("<")||!e.operator.startsWith("<"))&&(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))&&!(i(this.semver,"<",e.semver,n)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))&&!(i(this.semver,">",e.semver,n)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))}}module.exports=t;const r=__webpack_require__(3990),{safeRe:n,t:_}=__webpack_require__(2841),i=__webpack_require__(4004),a=__webpack_require__(1361),o=__webpack_require__(4517),s=__webpack_require__(7476)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},1659:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>p});var e=__webpack_require__(5036),t=__webpack_require__(6932),r=__webpack_require__(2857),n=__webpack_require__(4212),_=__webpack_require__(1539),i=__webpack_require__(9563),a=__webpack_require__(5204),o=__webpack_require__(9272),s=__webpack_require__(1726),u=__webpack_require__(8746);const c=[{endowment:AbortController,name:"AbortController"},{endowment:AbortSignal,name:"AbortSignal"},{endowment:ArrayBuffer,name:"ArrayBuffer"},{endowment:atob,name:"atob",bind:!0},{endowment:BigInt,name:"BigInt"},{endowment:BigInt64Array,name:"BigInt64Array"},{endowment:BigUint64Array,name:"BigUint64Array"},{endowment:btoa,name:"btoa",bind:!0},{endowment:DataView,name:"DataView"},{endowment:Float32Array,name:"Float32Array"},{endowment:Float64Array,name:"Float64Array"},{endowment:Intl,name:"Intl"},{endowment:Int8Array,name:"Int8Array"},{endowment:Int16Array,name:"Int16Array"},{endowment:Int32Array,name:"Int32Array"},{endowment:globalThis.isSecureContext,name:"isSecureContext"},{endowment:Uint8Array,name:"Uint8Array"},{endowment:Uint8ClampedArray,name:"Uint8ClampedArray"},{endowment:Uint16Array,name:"Uint16Array"},{endowment:Uint32Array,name:"Uint32Array"},{endowment:URL,name:"URL"},{endowment:URLSearchParams,name:"URLSearchParams"},{endowment:WebAssembly,name:"WebAssembly"}],p=()=>{const p=[t.A,n.A,_.A,i.A,s.A,a.A,o.A,r.A,e.Ay];return c.forEach((e=>{const t={names:[e.name],factory:()=>{const t="function"==typeof e.endowment&&e.bind?e.endowment.bind(u.h):e.endowment;return{[e.name]:harden(t)}}};p.push(t)})),p}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},1705:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>a});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(1089),n=__webpack_require__(4980),_=__webpack_require__(6725),i=__webpack_require__(511);const a=(0,e.kp)(i.Tl,(0,t.Ik)({type:(0,n.eu)(i.Z6.Address),value:r.UG}));(0,_.I)(i.Z6.Address,a,["value"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},1726:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>t});var e=__webpack_require__(845);const t={names:["setTimeout","clearTimeout"],factory:()=>{const t=new Map,r=e=>{const r=t.get(e);r!==undefined&&(clearTimeout(r),t.delete(e))};return{setTimeout:harden(((r,n,..._)=>{if("function"!=typeof r)throw e.r.internal(`The timeout handler must be a function. Received: ${typeof r}.`);harden(r);const i=Object.freeze(Object.create(null)),a=setTimeout(((...e)=>{t.delete(i),r(...e)}),Math.max(10,n??0),..._);return t.set(i,a),i})),clearTimeout:harden(r),teardownFunction:()=>{for(const e of t.keys())r(e)}}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},1750:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e){return JSON.stringify(e).length}__webpack_require__.d(__webpack_exports__,{m:()=>e})}}).call(__webpack_require__._LM_("9",{__webpack_exports__,__webpack_require__}))()},1964:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{c:()=>e});const e=(e,t)=>{if(!t)return[];if(t instanceof Array)return t;const r=e.reduce(((e,t,r)=>({...e,[t]:r})),{});return Object.entries(t).sort((([e,t],[n,_])=>r[e]-r[n])).map((([e,t])=>t))}}}).call(__webpack_require__._LM_("0",{__webpack_exports__,__webpack_require__}))()},1993:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>n});var e=__webpack_require__(7950),t=__webpack_require__(2462),r=__webpack_require__(4156);function n(n={}){const _={},i=new r.Duplex({objectMode:!0,read:()=>undefined,write:function(e,r,i){let s=null;try{(0,t.i5)(e,"id")?function(e){const{id:t}=e;if(null===t)return;const r=_[t];r?(delete _[t],Object.assign(r.res,e),setTimeout(r.end)):console.warn(`StreamMiddleware - Unknown response id "${t}"`)}(e):(u=e,n?.retryOnMessage&&u.method===n.retryOnMessage&&Object.values(_).forEach((({req:e,retryCount:t=0})=>{if(!e.id)return;if(t>=3)throw new Error(`StreamMiddleware - Retry limit exceeded for request id "${e.id}"`);const r=_[e.id];r&&(r.retryCount=t+1),o(e)})),a.emit("notification",u))}catch(e){s=e}var u;i(s)}}),a=new e.A;return{events:a,middleware:(e,t,r,n)=>{_[e.id]={req:e,res:t,next:r,end:n},o(e)},stream:i};function o(e){i.push(e)}}}}).call(__webpack_require__._LM_("2",{__webpack_require__,__webpack_exports__}))()},2132:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r)=>e(t,r,!0)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},2147:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{FI:()=>o});var e,t=__webpack_require__(8464),r=__webpack_require__(4729),n=__webpack_require__(776),_=__webpack_require__(5953);!function(e){e.PackageJson="package.json",e.Manifest="snap.manifest.json"}(e||(e={}));const i=(0,t.Ej)((0,n.s)("Snap Name",/^(?:@[a-z0-9-*~][a-z0-9-*._~]*\/)?[a-z0-9-~][a-z0-9-._~]*$/u),1,214);var a,o;(0,r.NW)({version:_.mz,name:i,main:(0,r.lq)((0,t.Ej)((0,r.Yj)(),1,Infinity)),repository:(0,r.lq)((0,r.NW)({type:(0,t.Ej)((0,r.Yj)(),1,Infinity),url:(0,t.Ej)((0,r.Yj)(),1,Infinity)}))}),function(e){e.npm="npm:",e.local="local:"}(a||(a={})),function(e){e.JSON_RPC="jsonRpc",e.COMMAND="command"}(o||(o={}))}}).call(__webpack_require__._LM_("9",{__webpack_require__,__webpack_exports__}))()},2153:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{K:()=>r});var e=__webpack_require__(2462);const t=new Map;function r(){t.forEach(((e,t)=>{for(const r of e)Object.defineProperty(t,r,{value:undefined,configurable:!1,writable:!1})}))}(0,e.i5)(globalThis,"UIEvent")&&t.set(UIEvent.prototype,["view"]),(0,e.i5)(globalThis,"MutationEvent")&&t.set(MutationEvent.prototype,["relatedNode"]),(0,e.i5)(globalThis,"MessageEvent")&&t.set(MessageEvent.prototype,["source"]),(0,e.i5)(globalThis,"FocusEvent")&&t.set(FocusEvent.prototype,["relatedTarget"]),(0,e.i5)(globalThis,"MouseEvent")&&t.set(MouseEvent.prototype,["relatedTarget","fromElement","toElement"]),(0,e.i5)(globalThis,"TouchEvent")&&t.set(TouchEvent.prototype,["targetTouches","touches"]),(0,e.i5)(globalThis,"Event")&&t.set(Event.prototype,["target","currentTarget","srcElement","composedPath"])}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},2171:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>y});var e,t,r,n,_,i,a,o,s,u,c,p,l,h=__webpack_require__(7012),f=__webpack_require__(7822),d=__webpack_require__(6231),w=__webpack_require__(7950),b=__webpack_require__(5259),k=__webpack_require__(2462),m=undefined&&undefined.__classPrivateFieldSet||function(e,t,r,n,_){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!_)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!_:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?_.call(e,r):_?_.value=r:t.set(e,r),r},g=undefined&&undefined.__classPrivateFieldGet||function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class y extends w.A{constructor({notificationHandler:t}={}){super(),e.add(this),r.set(this,!1),n.set(this,void 0),_.set(this,void 0),m(this,n,[],"f"),m(this,_,t,"f")}destroy(){g(this,n,"f").forEach((e=>{"destroy"in e&&"function"==typeof e.destroy&&e.destroy()})),m(this,n,[],"f"),m(this,r,!0,"f")}push(t){g(this,e,"m",i).call(this),g(this,n,"f").push(t)}handle(t,r){if(g(this,e,"m",i).call(this),r&&"function"!=typeof r)throw new Error('"callback" must be a function if provided.');return Array.isArray(t)?r?g(this,e,"m",a).call(this,t,r):g(this,e,"m",a).call(this,t):r?g(this,e,"m",o).call(this,t,r):this._promiseHandle(t)}asMiddleware(){return g(this,e,"m",i).call(this),async(e,r,_,i)=>{try{const[a,o,s]=await g(t,t,"m",u).call(t,e,r,g(this,n,"f"));return o?(await g(t,t,"m",p).call(t,s),i(a)):_((async e=>{try{await g(t,t,"m",p).call(t,s)}catch(t){return e(t)}return e()}))}catch(e){return i(e)}}}async _promiseHandle(t){return new Promise(((r,n)=>{g(this,e,"m",o).call(this,t,((e,t)=>{e&&t===undefined?n(e):r(t)})).catch(n)}))}}function q(e){return JSON.stringify(e,null,2)}t=y,r=new WeakMap,n=new WeakMap,_=new WeakMap,e=new WeakSet,i=function(){if(g(this,r,"f"))throw new Error("This engine is destroyed and can no longer be used.")},a=async function(e,t){try{if(0===e.length){const e=[{id:null,jsonrpc:"2.0",error:new h.G(f.f.rpc.invalidRequest,"Request batch must contain plain objects. Received an empty array")}];return t?t(null,e):e}const r=(await Promise.all(e.map(this._promiseHandle.bind(this)))).filter((e=>e!==undefined));return t?t(null,r):r}catch(e){if(t)return t(e);throw e}},o=async function(e,r){if(!e||Array.isArray(e)||"object"!=typeof e){const t=new h.G(f.f.rpc.invalidRequest,"Requests must be plain objects. Received: "+typeof e,{request:e});return r(t,{id:null,jsonrpc:"2.0",error:t})}if("string"!=typeof e.method){const t=new h.G(f.f.rpc.invalidRequest,"Must specify a string method. Received: "+typeof e.method,{request:e});return g(this,_,"f")&&!(0,b.p3)(e)?r(null):r(t,{id:e.id??null,jsonrpc:"2.0",error:t})}if(g(this,_,"f")&&(0,b.tQ)(e)&&!(0,b.p3)(e)){try{await g(this,_,"f").call(this,e)}catch(i){return r(i)}return r(null)}let i=null;const a={...e},o={id:a.id,jsonrpc:a.jsonrpc};try{await g(t,t,"m",s).call(t,a,o,g(this,n,"f"))}catch(e){i=e}return i&&(delete o.result,o.error||(o.error=(0,d.P5)(i))),r(i,o)},s=async function(e,r,n){const[_,i,a]=await g(t,t,"m",u).call(t,e,r,n);if(g(t,t,"m",l).call(t,e,r,i),await g(t,t,"m",p).call(t,a),_)throw _},u=async function(e,r,n){const _=[];let i=null,a=!1;for(const o of n)if([i,a]=await g(t,t,"m",c).call(t,e,r,o,_),a)break;return[i,a,_.reverse()]},c=async function(e,t,r,n){return new Promise((_=>{const i=e=>{const r=e||t.error;r&&(t.error=(0,d.P5)(r)),_([r,!0])},a=r=>{t.error?i(t.error):(r&&("function"!=typeof r&&i(new h.G(f.f.rpc.internal,`JsonRpcEngine: "next" return handlers must be functions. Received "${typeof r}" for request:\n${q(e)}`,{request:e})),n.push(r)),_([null,!1]))};try{r(e,t,a,i)}catch(e){i(e)}}))},p=async function(e){for(const t of e)await new Promise(((e,r)=>{t((t=>t?r(t):e()))}))},l=function(e,t,r){if(!(0,k.i5)(t,"result")&&!(0,k.i5)(t,"error"))throw new h.G(f.f.rpc.internal,`JsonRpcEngine: Response has no error or result for request:\n${q(e)}`,{request:e});if(!r)throw new h.G(f.f.rpc.internal,`JsonRpcEngine: Nothing ended request:\n${q(e)}`,{request:e})}}}).call(__webpack_require__._LM_("1",{__webpack_require__,__webpack_exports__}))()},2250:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{cs:()=>c});var e=__webpack_require__(4729),t=__webpack_require__(2521),r=__webpack_require__(2462),n=__webpack_require__(5259),_=__webpack_require__(6219),i=__webpack_require__(4980),a=__webpack_require__(6540),o=__webpack_require__(3603);const s=(0,e.KC)([_.rI,_.Es,_.ux,(0,e.Yj)(),(0,e.zM)(),t.FX]),u=(0,e.g1)((0,e.Yj)(),(0,e.me)(s)),c=((0,e.g1)((0,e.Yj)(),(0,e.KC)([u,(0,e.me)(s)])),(0,i.E$)((e=>(0,r.Gv)(e)&&!(0,r.i5)(e,"props")?o.vb:a.Pm)),(0,e.g1)((0,e.Yj)(),n.zC));var p;!function(e){e.Insight="Insight",e.Dialog="Dialog",e.Notification="Notification",e.HomePage="HomePage"}(p||(p={}))}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},2281:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(3955);module.exports=(t,r)=>{const n=e(t.trim().replace(/^[=v]+/,""),r);return n?n.version:null}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},2295:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{D:()=>h});var e,t,r=__webpack_require__(2171),n=__webpack_require__(845),_=__webpack_require__(7012),i=__webpack_require__(7950),a=__webpack_require__(8792),o=__webpack_require__(5303),s=__webpack_require__(9306),u=undefined&&undefined.__classPrivateFieldSet||function(e,t,r,n,_){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!_)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!_:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?_.call(e,r):_?_.value=r:t.set(e,r),r},c=undefined&&undefined.__classPrivateFieldGet||function(e,t,r,n){if("a"===r&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};const p=(l=a,l?.__esModule?l.default:l);var l;class h extends i.A{constructor({logger:n=console,maxEventListeners:_=100,rpcMiddleware:i=[]}={}){super(),e.set(this,void 0),t.set(this,void 0),this._log=n,this.setMaxListeners(_),this._state={...h._defaultState},u(this,t,null,"f"),u(this,e,null,"f"),this._handleAccountsChanged=this._handleAccountsChanged.bind(this),this._handleConnect=this._handleConnect.bind(this),this._handleChainChanged=this._handleChainChanged.bind(this),this._handleDisconnect=this._handleDisconnect.bind(this),this._rpcRequest=this._rpcRequest.bind(this),this.request=this.request.bind(this);const a=new r.A;i.forEach((e=>a.push(e))),this._rpcEngine=a}get chainId(){return c(this,e,"f")}get selectedAddress(){return c(this,t,"f")}isConnected(){return this._state.isConnected}async request(e){if(!e||"object"!=typeof e||Array.isArray(e))throw n.r.invalidRequest({message:o.A.errors.invalidRequestArgs(),data:e});const{method:t,params:r}=e;if("string"!=typeof t||0===t.length)throw n.r.invalidRequest({message:o.A.errors.invalidRequestMethod(),data:e});if(r!==undefined&&!Array.isArray(r)&&("object"!=typeof r||null===r))throw n.r.invalidRequest({message:o.A.errors.invalidRequestParams(),data:e});const _=r===undefined||null===r?{method:t}:{method:t,params:r};return new Promise(((e,t)=>{this._rpcRequest(_,(0,s.m4)(e,t))}))}_initializeState(e){if(this._state.initialized)throw new Error("Provider already initialized.");if(e){const{accounts:t,chainId:r,networkVersion:n,isConnected:_}=e;this._handleConnect({chainId:r,isConnected:_}),this._handleChainChanged({chainId:r,networkVersion:n,isConnected:_}),this._handleAccountsChanged(t)}this._state.initialized=!0,this.emit("_initialized")}_rpcRequest(e,t){let r=t;return Array.isArray(e)||(e.jsonrpc||(e.jsonrpc="2.0"),"eth_accounts"!==e.method&&"eth_requestAccounts"!==e.method||(r=(r,n)=>{this._handleAccountsChanged(n.result??[],"eth_accounts"===e.method),t(r,n)})),this._rpcEngine.handle(e,r)}_handleConnect({chainId:e,isConnected:t}){!this._state.isConnected&&t&&(this._state.isConnected=!0,this.emit("connect",{chainId:e}),this._log.debug(o.A.info.connected(e)))}_handleDisconnect(r,n){if(this._state.isConnected||!this._state.isPermanentlyDisconnected&&!r){let i;this._state.isConnected=!1,r?(i=new _.G(1013,n??o.A.errors.disconnected()),this._log.debug(i)):(i=new _.G(1011,n??o.A.errors.permanentlyDisconnected()),this._log.error(i),u(this,e,null,"f"),this._state.accounts=null,u(this,t,null,"f"),this._state.isPermanentlyDisconnected=!0),this.emit("disconnect",i)}}_handleChainChanged({chainId:t,isConnected:r}={}){(0,s.Jx)(t)?(this._handleConnect({chainId:t,isConnected:r}),t!==c(this,e,"f")&&(u(this,e,t,"f"),this._state.initialized&&this.emit("chainChanged",c(this,e,"f")))):this._log.error(o.A.errors.invalidNetworkParams(),{chainId:t})}_handleAccountsChanged(e,r=!1){let n=e;Array.isArray(e)||(this._log.error("MetaMask: Received invalid accounts parameter. Please report this bug.",e),n=[]);for(const t of e)if("string"!=typeof t){this._log.error("MetaMask: Received non-string account. Please report this bug.",e),n=[];break}if(!p(this._state.accounts,n)&&(r&&null!==this._state.accounts&&this._log.error("MetaMask: 'eth_accounts' unexpectedly updated accounts. Please report this bug.",n),this._state.accounts=n,c(this,t,"f")!==n[0]&&u(this,t,n[0]||null,"f"),this._state.initialized)){const e=[...n];this.emit("accountsChanged",e)}}}e=new WeakMap,t=new WeakMap,h._defaultState={accounts:null,isConnected:!1,initialized:!1,isPermanentlyDisconnected:!1}}}).call(__webpack_require__._LM_("5",{__webpack_require__,__webpack_exports__}))()},2386:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>e(t,r,n)>=0}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},2457:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{f:()=>a});var e=__webpack_require__(845),t=__webpack_require__(8323),r=__webpack_require__(2462),n=__webpack_require__(1659),_=__webpack_require__(8746);const i=(0,n.A)().reduce(((e,t)=>(t.names.forEach((r=>{e.set(r,t.factory)})),e)),new Map);function a({snap:n,ethereum:a,snapId:o,endowments:s,notify:u}){const c={},p=s.reduce((({allEndowments:n,teardowns:s},p)=>{if(i.has(p)){if(!(0,r.i5)(c,p)){const{teardownFunction:e,...t}=i.get(p)({snapId:o,notify:u});Object.assign(c,t),e&&s.push(e)}n[p]=c[p]}else if("ethereum"===p)n[p]=a;else{if(!(p in _.h))throw e.r.internal(`Unknown endowment: "${p}".`);{(0,t.FF)(`Access to unhardened global ${p}.`);const e=_.h[p];n[p]=e}}return{allEndowments:n,teardowns:s}}),{allEndowments:{snap:n},teardowns:[]});return{endowments:p.allEndowments,teardown:async()=>{await Promise.all(p.teardowns.map((async e=>e())))}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},2462:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e){return null===e||e===undefined}function t(e){return Boolean(e)&&"object"==typeof e&&!Array.isArray(e)}__webpack_require__.d(__webpack_exports__,{Gv:()=>t,Qd:()=>_,hX:()=>e,i5:()=>r});const r=(e,t)=>Object.hasOwnProperty.call(e,t);var n;function _(e){if("object"!=typeof e||null===e)return!1;try{let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}catch(e){return!1}}!function(e){e[e.Null=4]="Null",e[e.Comma=1]="Comma",e[e.Wrapper=1]="Wrapper",e[e.True=4]="True",e[e.False=5]="False",e[e.Quote=1]="Quote",e[e.Colon=1]="Colon",e[e.Date=24]="Date"}(n=n||(n={}))}}).call(__webpack_require__._LM_("10",{__webpack_exports__,__webpack_require__}))()},2487:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{$:()=>e});var e,t=__webpack_require__(8546);!function(e){e.AddSquare="add-square",e.Add="add",e.Arrow2Down="arrow-2-down",e.Arrow2Left="arrow-2-left",e.Arrow2Right="arrow-2-right",e.Arrow2Up="arrow-2-up",e.Arrow2UpRight="arrow-2-up-right",e.ArrowDoubleLeft="arrow-double-left",e.ArrowDoubleRight="arrow-double-right",e.ArrowDown="arrow-down",e.ArrowLeft="arrow-left",e.ArrowRight="arrow-right",e.ArrowUp="arrow-up",e.BankToken="bank-token",e.Bank="bank",e.Book="book",e.Bookmark="bookmark",e.Bridge="bridge",e.Calculator="calculator",e.CardPos="card-pos",e.CardToken="card-token",e.Card="card",e.Category="category",e.Chart="chart",e.CheckBold="check-bold",e.Check="check",e.Clock="clock",e.Close="close",e.CodeCircle="code-circle",e.Coin="coin",e.Confirmation="confirmation",e.Connect="connect",e.CopySuccess="copy-success",e.Copy="copy",e.Customize="customize",e.Danger="danger",e.Dark="dark",e.Data="data",e.Diagram="diagram",e.DocumentCode="document-code",e.DragDrop="drag-drop",e.DraggingAnimation="dragging-animation",e.PinningAnimation="pinning-animation",e.Edit="edit",e.Eraser="eraser",e.Ethereum="ethereum",e.Expand="expand",e.Explore="explore",e.Export="export",e.EyeSlash="eye-slash",e.Eye="eye",e.Filter="filter",e.Flag="flag",e.FlashSlash="flash-slash",e.Flash="flash",e.FullCircle="full-circle",e.Gas="gas",e.GlobalSearch="global-search",e.Global="global",e.Graph="graph",e.Hardware="hardware",e.Heart="heart",e.Hierarchy="hierarchy",e.Home="home",e.Import="import",e.Info="info",e.Key="key",e.Light="light",e.Link="link",e.Loading="loading",e.LockCircle="lock-circle",e.LockSlash="lock-slash",e.Lock="lock",e.Login="login",e.Logout="logout",e.Menu="menu",e.MessageQuestion="message-question",e.Messages="messages",e.MinusBold="minus-bold",e.MinusSquare="minus-square",e.Minus="minus",e.Mobile="mobile",e.Money="money",e.Monitor="monitor",e.MoreHorizontal="more-horizontal",e.MoreVertical="more-vertical",e.NotificationCircle="notification-circle",e.Notification="notification",e.PasswordCheck="password-check",e.People="people",e.Pin="pin",e.ProgrammingArrows="programming-arrows",e.Custody="custody",e.Question="question",e.Received="received",e.Refresh="refresh",e.Save="save",e.ScanBarcode="scan-barcode",e.ScanFocus="scan-focus",e.Scan="scan",e.Scroll="scroll",e.Search="search",e.SecurityCard="security-card",e.SecurityCross="security-cross",e.SecurityKey="security-key",e.SecuritySearch="security-search",e.SecuritySlash="security-slash",e.SecurityTick="security-tick",e.SecurityTime="security-time",e.SecurityUser="security-user",e.Security="security",e.Send1="send-1",e.Send2="send-2",e.Setting="setting",e.Slash="slash",e.SnapsMobile="snaps-mobile",e.SnapsPlus="snaps-plus",e.Snaps="snaps",e.Speedometer="speedometer",e.Star="star",e.Stake="stake",e.Student="student",e.SwapHorizontal="swap-horizontal",e.SwapVertical="swap-vertical",e.Tag="tag",e.Tilde="tilde",e.Timer="timer",e.Trash="trash",e.TrendDown="trend-down",e.TrendUp="trend-up",e.UserCircleAdd="user-circle-add",e.UserCircleLink="user-circle-link",e.UserCircleRemove="user-circle-remove",e.UserCircle="user-circle",e.User="user",e.WalletCard="wallet-card",e.WalletMoney="wallet-money",e.Wallet="wallet",e.Warning="warning",e.Twitter="twitter",e.QrCode="qr-code",e.UserCheck="user-check",e.Unpin="unpin",e.Ban="ban",e.Bold="bold",e.CircleX="circle-x",e.Download="download",e.FileIcon="file",e.Flask="flask",e.Plug="plug",e.Share="share",e.Square="square",e.Tint="tint",e.Upload="upload",e.Usb="usb",e.Wifi="wifi",e.PlusMinus="plus-minus"}(e||(e={})),(0,t.K)("Icon")}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},2489:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{J:()=>t});var e=__webpack_require__(4980);function t(t){return(0,e.KC)(t)}}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},2521:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Dz:()=>u,FX:()=>i,Ts:()=>c,dg:()=>_,jx:()=>a,rW:()=>s,wV:()=>o});var e=__webpack_require__(776);const t=/^(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32})$/u,r=/^(?<chainId>(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32})):(?<accountAddress>[-.%a-zA-Z0-9]{1,128})$/u,n=/^(?<chainId>(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32}))\/(?<assetNamespace>[-a-z0-9]{3,8}):(?<assetReference>[-.%a-zA-Z0-9]{1,128})$/u,_=(0,e.s)("CaipChainId",t),i=((0,e.s)("CaipNamespace",/^[-a-z0-9]{3,8}$/u),(0,e.s)("CaipReference",/^[-_a-zA-Z0-9]{1,32}$/u),(0,e.s)("CaipAccountId",r)),a=((0,e.s)("CaipAccountAddress",/^[-.%a-zA-Z0-9]{1,128}$/u),(0,e.s)("CaipAssetNamespace",/^[-a-z0-9]{3,8}$/u),(0,e.s)("CaipAssetReference",/^[-.%a-zA-Z0-9]{1,128}$/u),(0,e.s)("CaipTokenId",/^[-.%a-zA-Z0-9]{1,78}$/u),(0,e.s)("CaipAssetType",n));var o;function s(e){const r=t.exec(e);if(!r?.groups)throw new Error("Invalid CAIP chain ID.");return{namespace:r.groups.namespace,reference:r.groups.reference}}function u(e){const t=r.exec(e);if(!t?.groups)throw new Error("Invalid CAIP account ID.");return{address:t.groups.accountAddress,chainId:t.groups.chainId,chain:{namespace:t.groups.namespace,reference:t.groups.reference}}}function c(e){const t=n.exec(e);if(!t?.groups)throw new Error("Invalid CAIP asset type.");return{assetNamespace:t.groups.assetNamespace,assetReference:t.groups.assetReference,chainId:t.groups.chainId,chain:{namespace:t.groups.namespace,reference:t.groups.reference}}}(0,e.s)("CaipAssetId",/^(?<chainId>(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32}))\/(?<assetNamespace>[-a-z0-9]{3,8}):(?<assetReference>[-.%a-zA-Z0-9]{1,128})\/(?<tokenId>[-.%a-zA-Z0-9]{1,78})$/u),(0,e.s)("CaipAssetTypeOrId",/^(?<chainId>(?<namespace>[-a-z0-9]{3,8}):(?<reference>[-_a-zA-Z0-9]{1,32}))\/(?<assetNamespace>[-a-z0-9]{3,8}):(?<assetReference>[-.%a-zA-Z0-9]{1,128})(\/(?<tokenId>[-.%a-zA-Z0-9]{1,78}))?$/u),function(e){e.Bip122="bip122",e.Solana="solana",e.Eip155="eip155",e.Wallet="wallet"}(o=o||(o={}))}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},2565:function(__unused_webpack_module,exports,__webpack_require__){(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=this&&this.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var _=0;for(n=Object.getOwnPropertySymbols(e);_<n.length;_++)t.indexOf(n[_])<0&&Object.prototype.propertyIsEnumerable.call(e,n[_])&&(r[n[_]]=e[n[_]])}return r};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Substream=void 0;const t=__webpack_require__(4156);class r extends t.Duplex{constructor(t){var{parent:r,name:n}=t,_=e(t,["parent","name"]);super(Object.assign({objectMode:!0},_)),this._parent=r,this._name=n}_read(){return undefined}_write(e,t,r){this._parent.push({name:this._name,data:e}),r()}}exports.Substream=r}}).call(__webpack_require__._LM_("3",{exports,__webpack_require__})).bind(exports)()},2722:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(2841),t=__webpack_require__(9543),r=__webpack_require__(4517),n=__webpack_require__(3806),_=__webpack_require__(3955),i=__webpack_require__(8474),a=__webpack_require__(2281),o=__webpack_require__(8868),s=__webpack_require__(3269),u=__webpack_require__(6381),c=__webpack_require__(1353),p=__webpack_require__(6082),l=__webpack_require__(9428),h=__webpack_require__(7851),f=__webpack_require__(7555),d=__webpack_require__(2132),w=__webpack_require__(6106),b=__webpack_require__(4042),k=__webpack_require__(3810),m=__webpack_require__(9761),g=__webpack_require__(1262),y=__webpack_require__(8848),q=__webpack_require__(8220),v=__webpack_require__(2386),E=__webpack_require__(9639),S=__webpack_require__(4004),x=__webpack_require__(6783),R=__webpack_require__(1565),T=__webpack_require__(7476),M=__webpack_require__(7229),O=__webpack_require__(6364),L=__webpack_require__(5039),A=__webpack_require__(5357),I=__webpack_require__(1280),C=__webpack_require__(7403),j=__webpack_require__(8854),P=__webpack_require__(7226),$=__webpack_require__(7183),N=__webpack_require__(8623),G=__webpack_require__(6486),F=__webpack_require__(583);module.exports={parse:_,valid:i,clean:a,inc:o,diff:s,major:u,minor:c,patch:p,prerelease:l,compare:h,rcompare:f,compareLoose:d,compareBuild:w,sort:b,rsort:k,gt:m,lt:g,eq:y,neq:q,gte:v,lte:E,cmp:S,coerce:x,Comparator:R,Range:T,satisfies:M,toComparators:O,maxSatisfying:L,minSatisfying:A,minVersion:I,validRange:C,outside:j,gtr:P,ltr:$,intersects:N,simplifyRange:G,subset:F,SemVer:r,re:e.re,src:e.src,tokens:e.t,SEMVER_SPEC_VERSION:t.SEMVER_SPEC_VERSION,RELEASE_TYPES:t.RELEASE_TYPES,compareIdentifiers:n.compareIdentifiers,rcompareIdentifiers:n.rcompareIdentifiers}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},2841:(module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const{MAX_SAFE_COMPONENT_LENGTH:e,MAX_SAFE_BUILD_LENGTH:t,MAX_LENGTH:r}=__webpack_require__(9543),n=__webpack_require__(1361),_=(exports=module.exports={}).re=[],i=exports.safeRe=[],a=exports.src=[],o=exports.safeSrc=[],s=exports.t={};let u=0;const c="[a-zA-Z0-9-]",p=[["\\s",1],["\\d",r],[c,t]],l=(e,t,r)=>{const c=(e=>{for(const[t,r]of p)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e})(t),l=u++;n(e,l,t),s[e]=l,a[l]=t,o[l]=c,_[l]=new RegExp(t,r?"g":undefined),i[l]=new RegExp(c,r?"g":undefined)};l("NUMERICIDENTIFIER","0|[1-9]\\d*"),l("NUMERICIDENTIFIERLOOSE","\\d+"),l("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${c}*`),l("MAINVERSION",`(${a[s.NUMERICIDENTIFIER]})\\.(${a[s.NUMERICIDENTIFIER]})\\.(${a[s.NUMERICIDENTIFIER]})`),l("MAINVERSIONLOOSE",`(${a[s.NUMERICIDENTIFIERLOOSE]})\\.(${a[s.NUMERICIDENTIFIERLOOSE]})\\.(${a[s.NUMERICIDENTIFIERLOOSE]})`),l("PRERELEASEIDENTIFIER",`(?:${a[s.NUMERICIDENTIFIER]}|${a[s.NONNUMERICIDENTIFIER]})`),l("PRERELEASEIDENTIFIERLOOSE",`(?:${a[s.NUMERICIDENTIFIERLOOSE]}|${a[s.NONNUMERICIDENTIFIER]})`),l("PRERELEASE",`(?:-(${a[s.PRERELEASEIDENTIFIER]}(?:\\.${a[s.PRERELEASEIDENTIFIER]})*))`),l("PRERELEASELOOSE",`(?:-?(${a[s.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${a[s.PRERELEASEIDENTIFIERLOOSE]})*))`),l("BUILDIDENTIFIER",`${c}+`),l("BUILD",`(?:\\+(${a[s.BUILDIDENTIFIER]}(?:\\.${a[s.BUILDIDENTIFIER]})*))`),l("FULLPLAIN",`v?${a[s.MAINVERSION]}${a[s.PRERELEASE]}?${a[s.BUILD]}?`),l("FULL",`^${a[s.FULLPLAIN]}$`),l("LOOSEPLAIN",`[v=\\s]*${a[s.MAINVERSIONLOOSE]}${a[s.PRERELEASELOOSE]}?${a[s.BUILD]}?`),l("LOOSE",`^${a[s.LOOSEPLAIN]}$`),l("GTLT","((?:<|>)?=?)"),l("XRANGEIDENTIFIERLOOSE",`${a[s.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),l("XRANGEIDENTIFIER",`${a[s.NUMERICIDENTIFIER]}|x|X|\\*`),l("XRANGEPLAIN",`[v=\\s]*(${a[s.XRANGEIDENTIFIER]})(?:\\.(${a[s.XRANGEIDENTIFIER]})(?:\\.(${a[s.XRANGEIDENTIFIER]})(?:${a[s.PRERELEASE]})?${a[s.BUILD]}?)?)?`),l("XRANGEPLAINLOOSE",`[v=\\s]*(${a[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${a[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${a[s.XRANGEIDENTIFIERLOOSE]})(?:${a[s.PRERELEASELOOSE]})?${a[s.BUILD]}?)?)?`),l("XRANGE",`^${a[s.GTLT]}\\s*${a[s.XRANGEPLAIN]}$`),l("XRANGELOOSE",`^${a[s.GTLT]}\\s*${a[s.XRANGEPLAINLOOSE]}$`),l("COERCEPLAIN",`(^|[^\\d])(\\d{1,${e}})(?:\\.(\\d{1,${e}}))?(?:\\.(\\d{1,${e}}))?`),l("COERCE",`${a[s.COERCEPLAIN]}(?:$|[^\\d])`),l("COERCEFULL",a[s.COERCEPLAIN]+`(?:${a[s.PRERELEASE]})?`+`(?:${a[s.BUILD]})?(?:$|[^\\d])`),l("COERCERTL",a[s.COERCE],!0),l("COERCERTLFULL",a[s.COERCEFULL],!0),l("LONETILDE","(?:~>?)"),l("TILDETRIM",`(\\s*)${a[s.LONETILDE]}\\s+`,!0),exports.tildeTrimReplace="$1~",l("TILDE",`^${a[s.LONETILDE]}${a[s.XRANGEPLAIN]}$`),l("TILDELOOSE",`^${a[s.LONETILDE]}${a[s.XRANGEPLAINLOOSE]}$`),l("LONECARET","(?:\\^)"),l("CARETTRIM",`(\\s*)${a[s.LONECARET]}\\s+`,!0),exports.caretTrimReplace="$1^",l("CARET",`^${a[s.LONECARET]}${a[s.XRANGEPLAIN]}$`),l("CARETLOOSE",`^${a[s.LONECARET]}${a[s.XRANGEPLAINLOOSE]}$`),l("COMPARATORLOOSE",`^${a[s.GTLT]}\\s*(${a[s.LOOSEPLAIN]})$|^$`),l("COMPARATOR",`^${a[s.GTLT]}\\s*(${a[s.FULLPLAIN]})$|^$`),l("COMPARATORTRIM",`(\\s*)${a[s.GTLT]}\\s*(${a[s.LOOSEPLAIN]}|${a[s.XRANGEPLAIN]})`,!0),exports.comparatorTrimReplace="$1$2$3",l("HYPHENRANGE",`^\\s*(${a[s.XRANGEPLAIN]})\\s+-\\s+(${a[s.XRANGEPLAIN]})\\s*$`),l("HYPHENRANGELOOSE",`^\\s*(${a[s.XRANGEPLAINLOOSE]})\\s+-\\s+(${a[s.XRANGEPLAINLOOSE]})\\s*$`),l("STAR","(<|>)?=?\\s*\\*"),l("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),l("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}}).call(__webpack_require__._LM_("18",{exports,module,__webpack_require__}))()},2857:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>t});var e=__webpack_require__(8746);const t={names:["Date"],factory:function(){const t=Object.getOwnPropertyNames(e.h.Date);let r=0;const n=()=>{const t=e.h.Date.now(),n=Math.round(t+Math.random());return n>r&&(r=n),r},_=function(...t){return Reflect.construct(e.h.Date,0===t.length?[n()]:t,new.target)};return t.forEach((t=>{Reflect.defineProperty(_,t,{configurable:!1,writable:!1,value:"now"===t?n:e.h.Date[t]})})),{Date:harden(_)}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},2866:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{l:()=>_});var e=__webpack_require__(4156);const t=()=>undefined,r="SYN",n="ACK";class _ extends e.Duplex{constructor(e){super(Object.assign({objectMode:!0},e)),this._init=!1,this._haveSyn=!1,this._log=()=>null}_handshake(){this._write(r,null,t),this.cork()}_onData(e){if(this._init)try{this.push(e),this._log(e,!1)}catch(e){this.emit("error",e)}else e===r?(this._haveSyn=!0,this._write(n,null,t)):e===n&&(this._init=!0,this._haveSyn||this._write(n,null,t),this.uncork())}_read(){return undefined}_write(e,t,_){e!==n&&e!==r&&this._log(e,!0),this._postMessage(e),_()}_setLogger(e){this._log=e}}}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},2869:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{X:()=>r,u:()=>t});const e=__webpack_require__(124)("metamask");function t(t){return e.extend(t)}function r(e,t){return e.extend(t)}}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},2945:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A4:()=>w,G2:()=>R,J6:()=>U,UE:()=>d,W8:()=>j,aM:()=>I,bi:()=>f,it:()=>b,lK:()=>g,o3:()=>M,sq:()=>L,tQ:()=>$,x7:()=>q});var e=__webpack_require__(845),t=__webpack_require__(6219),r=__webpack_require__(2250),n=__webpack_require__(4980),_=__webpack_require__(6157),i=__webpack_require__(4729),a=__webpack_require__(7566),o=__webpack_require__(8464),s=__webpack_require__(5259),u=__webpack_require__(9400),c=__webpack_require__(2521);const p=(0,i.Ik)({jsonrpc:(0,i.lq)(s.bo),id:(0,i.lq)(s.SZ),method:(0,i.Yj)(),params:(0,i.lq)(s.x8)}),l=(0,i.Yj)(),h=(0,i.eu)("OK"),f=(0,i.lq)((0,i.KC)([(0,i.eu)(undefined),(0,i.YO)()])),d=(0,i.KC)([(0,i.eu)(undefined),(0,i.YO)()]),w=(0,i.PV)([(0,i.Yj)(),(0,i.Yj)(),(0,i.YO)(l)]),b=(0,i.PV)([(0,i.Yj)(),(0,i.vP)(Object.values(_.Z)),(0,i.Yj)(),(0,a.kp)(p,(0,i.Ik)({params:(0,i.lq)((0,i.g1)((0,i.Yj)(),(0,i.bz)()))}))]);function k(t,r){(0,u.ut)(t,r,"Invalid request params",e.r.invalidParams)}const m=(0,i.Ik)({transaction:(0,i.g1)((0,i.Yj)(),s.zC),chainId:c.dg,transactionOrigin:(0,i.me)((0,i.Yj)())});function g(e){k(e,m)}const y=(0,i.Ik)({signature:(0,i.g1)((0,i.Yj)(),s.zC),signatureOrigin:(0,i.me)((0,i.Yj)())});function q(e){k(e,y)}const v={chainId:c.dg},E=(0,i.Ik)({...v,address:(0,i.Yj)()}),S=(0,i.Ik)({...v,domain:(0,i.Yj)()}),x=(0,i.KC)([E,S]);function R(e){k(e,x)}const T=(0,i.Ik)({from:c.jx,to:c.jx});function M(e){k(e,T)}const O=(0,i.Ik)({assets:(0,o.Ej)((0,i.YO)(c.jx),1,Infinity)});function L(e){k(e,O)}const A=(0,i.Ik)({conversions:(0,o.Ej)((0,i.YO)((0,i.Ik)({from:c.jx,to:c.jx})),1,Infinity),includeMarketData:(0,i.lq)((0,i.zM)())});function I(e){k(e,A)}const C=(0,i.Ik)({id:(0,i.Yj)(),event:t.pz,context:(0,i.lq)((0,i.me)(r.cs))});function j(e){k(e,C)}const P=(0,i.Ik)({scope:c.dg,request:s.Aw});function $(e){k(e,P)}const N=(0,i.Ik)({type:(0,n.eu)("open"),id:(0,i.Yj)(),origin:(0,i.Yj)()}),G=(0,i.Ik)({type:(0,n.eu)("close"),id:(0,i.Yj)(),origin:(0,i.Yj)(),code:(0,i.ai)(),reason:(0,i.me)((0,i.Yj)()),wasClean:(0,i.me)((0,i.zM)())}),F=(0,i.Ik)({type:(0,n.eu)("message"),id:(0,i.Yj)(),origin:(0,i.Yj)(),data:(0,n.i5)([(0,i.Ik)({type:(0,n.eu)("text"),message:(0,i.Yj)()}),(0,i.Ik)({type:(0,n.eu)("binary"),message:(0,i.YO)((0,i.ai)())})])}),H=(0,n.i5)([N,G,F]),D=(0,i.Ik)({event:H});function U(e){k(e,D)}(0,i.Ik)({id:s.SZ,jsonrpc:s.bo,result:h})}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},3011:()=>{},3071:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{M:()=>t});var e=__webpack_require__(3952);class t extends e.K{initializeSync(){this._initializeState()}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},3269:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(3955);module.exports=(t,r)=>{const n=e(t,null,!0),_=e(r,null,!0),i=n.compare(_);if(0===i)return null;const a=i>0,o=a?n:_,s=a?_:n,u=!!o.prerelease.length;if(s.prerelease.length&&!u){if(!s.patch&&!s.minor)return"major";if(0===s.compareMain(o))return s.minor&&!s.patch?"minor":"patch"}const c=u?"pre":"";return n.major!==_.major?c+"major":n.minor!==_.minor?c+"minor":n.patch!==_.patch?c+"patch":"prerelease"}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},3349:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{ox:()=>p,sZ:()=>s});var e=__webpack_require__(6231),t=__webpack_require__(7012),r=__webpack_require__(7822),n=__webpack_require__(7995),_=__webpack_require__(2462),i=__webpack_require__(5259);const a=-31001,o="Wrapped Snap Error";class s extends Error{#e;#t;#r;constructor(e){const t=(0,n.u1)(e);super(t),this.#e=e,this.#t=t,this.#r=(0,n.N2)(e)}get name(){return"WrappedSnapError"}get message(){return this.#t}get stack(){return this.#r}toJSON(){const t=function(e){if((0,_.Gv)(e)&&"serialize"in e&&"function"==typeof e.serialize){const t=e.serialize();return(0,i.U$)(t)&&u(t)}return!1}(this.#e)?this.#e.serialize():(0,e._K)(this.#e);return{code:a,message:o,data:{cause:t}}}serialize(){return this.toJSON()}}function u(e){return e.code===n.iQ&&e.message===n.A$}function c(e,r,n,_){const i=new t.G(e,r,_);return i.stack=n,i}function p(e){if(function(e){return(0,i.U$)(e)&&e.code===a&&e.message===o}(e)){if((0,i.U$)(e.data.cause)){if(u(e.data.cause)){const{code:t,message:r,stack:n,data:_}=e.data.cause.data.cause;return[c(t,r,n,_),!0]}if((0,_.Gv)(e.data.cause.data)&&(0,_.Gv)(e.data.cause.data.cause)&&e.data.cause.message===e.data.cause.data.cause.message){const t=e.data.cause.data.cause,{code:r,message:_}=e.data.cause;return[c(r,_,(0,n.N2)(t)),!1]}const{code:t,message:r,stack:i,data:a}=e.data.cause;return[c(t,r,i,a),!1]}return[c(r.f.rpc.internal,(0,n.u1)(e.data.cause),(0,n.N2)(e.data.cause)),!1]}if((0,i.U$)(e)){const{code:t,message:r,stack:n,data:_}=e;return[c(t,r,n,_),!1]}return[c(r.f.rpc.internal,(0,n.u1)(e),(0,n.N2)(e)),!1]}}}).call(__webpack_require__._LM_("9",{__webpack_require__,__webpack_exports__}))()},3358:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(7086);function t(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function r(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}module.exports=e(t),module.exports.strict=e(r),t.proto=t((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return t(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return r(this)},configurable:!0})}))}}).call(__webpack_require__._LM_("14",{module,__webpack_require__}))()},3603:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{vb:()=>k});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(1705),n=__webpack_require__(7131),_=__webpack_require__(544),i=__webpack_require__(7346),a=__webpack_require__(7569),o=__webpack_require__(5791),s=__webpack_require__(7914),u=__webpack_require__(6857),c=__webpack_require__(4937),p=__webpack_require__(1010),l=__webpack_require__(4102),h=__webpack_require__(4980),f=__webpack_require__(6725),d=__webpack_require__(511);const w=(0,e.kp)(d.Wu,(0,t.Ik)({children:(0,t.YO)((0,e.RZ)((()=>k)))})),b=(0,e.kp)(w,(0,t.Ik)({type:(0,h.eu)(d.Z6.Panel)})),k=((0,f.I)(d.Z6.Panel,b,["children"]),(0,h.i5)([_.$,i.z,o.Y,s.x,b,p.t,l.z,c.Ee,r.A,u.y9,a.W7,n.s3]))}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},3806:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=/^[0-9]+$/,t=(t,r)=>{const n=e.test(t),_=e.test(r);return n&&_&&(t=+t,r=+r),t===r?0:n&&!_?-1:_&&!n?1:t<r?-1:1};module.exports={compareIdentifiers:t,rcompareIdentifiers:(e,r)=>t(r,e)}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},3810:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(6106);module.exports=(t,r)=>t.sort(((t,n)=>e(n,t,r)))}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},3837:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{au:()=>t});var e=__webpack_require__(6848);function t(t,r,n){return new e._k({...t,coercer:(_,i)=>(0,e.is)(_,r)?t.coercer(n(_,i),i):t.coercer(_,i)})}}}).call(__webpack_require__._LM_("undefined",{__webpack_require__,__webpack_exports__}))()},3889:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";Error}}).call(__webpack_require__._LM_("undefined",{__webpack_exports__,__webpack_require__}))()},3951:()=>{},3952:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{K:()=>s});var e=__webpack_require__(1993),t=__webpack_require__(6605),r=__webpack_require__(4156),n=__webpack_require__(2295),_=__webpack_require__(5303),i=__webpack_require__(9306);const{duplex:a}=t;class o extends n.D{constructor(t,{logger:n=console,maxEventListeners:o=100,rpcMiddleware:s=[]}={}){if(super({logger:n,maxEventListeners:o,rpcMiddleware:s}),!a(t))throw new Error(_.A.errors.invalidDuplexStream());this._handleStreamDisconnect=this._handleStreamDisconnect.bind(this),this._jsonRpcConnection=(0,e.A)({retryOnMessage:"METAMASK_EXTENSION_CONNECT_CAN_RETRY"}),(0,r.pipeline)(t,this._jsonRpcConnection.stream,t,this._handleStreamDisconnect.bind(this,"MetaMask RpcProvider")),this._rpcEngine.push(this._jsonRpcConnection.middleware),this._jsonRpcConnection.events.on("notification",(e=>{const{method:r,params:n}=e;"metamask_accountsChanged"===r?this._handleAccountsChanged(n):"metamask_chainChanged"===r?this._handleChainChanged(n):i.a.includes(r)?this.emit("message",{type:r,data:n}):"METAMASK_STREAM_FAILURE"===r&&t.destroy(new Error(_.A.errors.permanentlyDisconnected()))}))}async _initializeStateAsync(){let e;try{e=await this.request({method:"metamask_getProviderState"})}catch(e){this._log.error("MetaMask: Failed to get initial state. Please report this bug.",e)}this._initializeState(e)}_handleStreamDisconnect(e,t){let r=`MetaMask: Lost connection to "${e}".`;t?.stack&&(r+=`\n${t.stack}`),this._log.warn(r),this.listenerCount("error")>0&&this.emit("error",r),this._handleDisconnect(!1,t?t.message:undefined)}_handleChainChanged({chainId:e,networkVersion:t,isConnected:r}={}){(0,i.Jx)(e)&&(0,i.Xi)(t)?(super._handleChainChanged({chainId:e,isConnected:r}),r||this._handleDisconnect(!0)):this._log.error(_.A.errors.invalidNetworkParams(),{chainId:e,networkVersion:t})}}class s extends o{async initialize(){return this._initializeStateAsync()}}}}).call(__webpack_require__._LM_("5",{__webpack_require__,__webpack_exports__}))()},3955:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r,n=!1)=>{if(t instanceof e)return t;try{return new e(t,r)}catch(e){if(!n)return null;throw e}}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},3990:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=Object.freeze({loose:!0}),t=Object.freeze({});module.exports=r=>r?"object"!=typeof r?e:r:t}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},4004:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(8848),t=__webpack_require__(8220),r=__webpack_require__(9761),n=__webpack_require__(2386),_=__webpack_require__(1262),i=__webpack_require__(9639);module.exports=(a,o,s,u)=>{switch(o){case"===":return"object"==typeof a&&(a=a.version),"object"==typeof s&&(s=s.version),a===s;case"!==":return"object"==typeof a&&(a=a.version),"object"==typeof s&&(s=s.version),a!==s;case"":case"=":case"==":return e(a,s,u);case"!=":return t(a,s,u);case">":return r(a,s,u);case">=":return n(a,s,u);case"<":return _(a,s,u);case"<=":return i(a,s,u);default:throw new TypeError(`Invalid operator: ${o}`)}}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},4042:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(6106);module.exports=(t,r)=>t.sort(((t,n)=>e(t,n,r)))}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},4102:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{z:()=>i});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(6725),_=__webpack_require__(511);const i=(0,e.kp)(_.Tl,(0,t.Ik)({type:(0,r.eu)(_.Z6.Text),value:(0,t.Yj)(),markdown:(0,t.lq)((0,t.zM)())}));(0,n.I)(_.Z6.Text,i,["value","markdown"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},4156:(module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";(exports=module.exports=__webpack_require__(8199)).Stream=exports,exports.Readable=exports,exports.Writable=__webpack_require__(5291),exports.Duplex=__webpack_require__(1265),exports.Transform=__webpack_require__(9415),exports.PassThrough=__webpack_require__(4421),exports.finished=__webpack_require__(4869),exports.pipeline=__webpack_require__(6815)}}).call(__webpack_require__._LM_("16",{exports,module,__webpack_require__}))()},4212:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>t});var e=__webpack_require__(845);const t={names:["setInterval","clearInterval"],factory:()=>{const t=new Map,r=e=>{harden(e);const r=t.get(e);r!==undefined&&(clearInterval(r),t.delete(e))};return{setInterval:harden(((r,n,..._)=>{if("function"!=typeof r)throw e.r.invalidInput(`The interval handler must be a function. Received: ${typeof r}.`);harden(r);const i=Object.freeze(Object.create(null)),a=setInterval(r,Math.max(10,n??0),..._);return t.set(i,a),i})),clearInterval:harden(r),teardownFunction:()=>{for(const e of t.keys())r(e)}}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},4389:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{m:()=>t});var e=__webpack_require__(8323);function t(){try{const e=Reflect.ownKeys((new Compartment).globalThis),t=new Set(["eval","Function"]);new Set([...e]).forEach((e=>{const r=Reflect.getOwnPropertyDescriptor(globalThis,e);r&&(r.configurable&&(function(e){return"set"in e||"get"in e}(r)?Object.defineProperty(globalThis,e,{configurable:!1}):Object.defineProperty(globalThis,e,{configurable:!1,writable:!1})),t.has(e)&&harden(globalThis[e]))}))}catch(t){throw(0,e.vV)("Protecting intrinsics failed:",t),t}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},4394:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e){return"object"==typeof e&&null!==e}function t(e){return"symbol"==typeof e?e.toString():"string"==typeof e?JSON.stringify(e):`${e}`}function r(e){const{done:t,value:r}=e.next();return t?undefined:r}function n(e,r,n,_){if(!0===e)return undefined;!1===e?e={}:"string"==typeof e&&(e={message:e});const{path:i,branch:a}=r,{type:o}=n,{refinement:s,message:u=`Expected a value of type \`${o}\`${s?` with refinement \`${s}\``:""}, but received: \`${t(_)}\``}=e;return{value:_,type:o,refinement:s,key:i[i.length-1],path:i,branch:a,...e,message:u}}function*_(t,r,_,i){(function(t){return e(t)&&"function"==typeof t[Symbol.iterator]})(t)||(t=[t]);for(const e of t){const t=n(e,r,_,i);t&&(yield t)}}function*i(t,r,n={}){const{path:_=[],branch:a=[t],coerce:o=!1,mask:s=!1}=n,u={path:_,branch:a};if(o&&(t=r.coercer(t,u),s&&"type"!==r.type&&e(r.schema)&&e(t)&&!Array.isArray(t)))for(const e in t)r.schema[e]===undefined&&delete t[e];let c="valid";for(const e of r.validator(t,u))e.explanation=n.message,c="not_valid",yield[e,undefined];for(let[p,l,h]of r.entries(t,u)){const r=i(l,h,{path:p===undefined?_:[..._,p],branch:p===undefined?a:[...a,l],coerce:o,mask:s,message:n.message});for(const n of r)n[0]?(c=null===n[0].refinement||n[0].refinement===undefined?"not_valid":"not_refined",yield[n[0],undefined]):o&&(l=n[1],p===undefined?t=l:t instanceof Map?t.set(p,l):t instanceof Set?t.add(l):e(t)&&(l!==undefined||p in t)&&(t[p]=l))}if("not_valid"!==c)for(const e of r.refiner(t,u))e.explanation=n.message,c="not_refined",yield[e,undefined];"valid"===c&&(yield[undefined,t])}__webpack_require__.d(__webpack_exports__,{Gv:()=>e,RF:()=>_,eF:()=>i,jT:()=>r,yy:()=>t})}}).call(__webpack_require__._LM_("undefined",{__webpack_exports__,__webpack_require__}))()},4421:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=t;var e=__webpack_require__(9415);function t(r){if(!(this instanceof t))return new t(r);e.call(this,r)}__webpack_require__(5615)(t,e),t.prototype._transform=function(e,t,r){r(null,e)}}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},4517:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(1361),{MAX_LENGTH:t,MAX_SAFE_INTEGER:r}=__webpack_require__(9543),{safeRe:n,safeSrc:_,t:i}=__webpack_require__(2841),a=__webpack_require__(3990),{compareIdentifiers:o}=__webpack_require__(3806);class s{constructor(_,o){if(o=a(o),_ instanceof s){if(_.loose===!!o.loose&&_.includePrerelease===!!o.includePrerelease)return _;_=_.version}else if("string"!=typeof _)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof _}".`);if(_.length>t)throw new TypeError(`version is longer than ${t} characters`);e("SemVer",_,o),this.options=o,this.loose=!!o.loose,this.includePrerelease=!!o.includePrerelease;const u=_.trim().match(o.loose?n[i.LOOSE]:n[i.FULL]);if(!u)throw new TypeError(`Invalid Version: ${_}`);if(this.raw=_,this.major=+u[1],this.minor=+u[2],this.patch=+u[3],this.major>r||this.major<0)throw new TypeError("Invalid major version");if(this.minor>r||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>r||this.patch<0)throw new TypeError("Invalid patch version");u[4]?this.prerelease=u[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<r)return t}return e})):this.prerelease=[],this.build=u[5]?u[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(e("SemVer.compare",this.version,this.options,t),!(t instanceof s)){if("string"==typeof t&&t===this.version)return 0;t=new s(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(e){return e instanceof s||(e=new s(e,this.options)),o(this.major,e.major)||o(this.minor,e.minor)||o(this.patch,e.patch)}comparePre(t){if(t instanceof s||(t=new s(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let r=0;do{const n=this.prerelease[r],_=t.prerelease[r];if(e("prerelease compare",r,n,_),n===undefined&&_===undefined)return 0;if(_===undefined)return 1;if(n===undefined)return-1;if(n!==_)return o(n,_)}while(++r)}compareBuild(t){t instanceof s||(t=new s(t,this.options));let r=0;do{const n=this.build[r],_=t.build[r];if(e("build compare",r,n,_),n===undefined&&_===undefined)return 0;if(_===undefined)return 1;if(n===undefined)return-1;if(n!==_)return o(n,_)}while(++r)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw new Error("invalid increment argument: identifier is empty");if(t){const e=new RegExp(`^${this.options.loose?_[i.PRERELEASELOOSE]:_[i.PRERELEASE]}$`),r=`-${t}`.match(e);if(!r||r[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(r)?1:0;if(0===this.prerelease.length)this.prerelease=[e];else{let n=this.prerelease.length;for(;--n>=0;)"number"==typeof this.prerelease[n]&&(this.prerelease[n]++,n=-2);if(-1===n){if(t===this.prerelease.join(".")&&!1===r)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let n=[t,e];!1===r&&(n=[t]),0===o(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=n):this.prerelease=n}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}module.exports=s}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},4663:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{C:()=>e});class e extends TypeError{constructor(e,t){let r;const{message:n,explanation:_,...i}=e,{path:a}=e,o=0===a.length?n:`At path: ${a.join(".")} -- ${n}`;super(_??o),null!==_&&_!==undefined&&(this.cause=o),Object.assign(this,i),this.name=this.constructor.name,this.failures=()=>r??(r=[e,...t()])}}}}).call(__webpack_require__._LM_("undefined",{__webpack_exports__,__webpack_require__}))()},4729:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Ik:()=>l,KC:()=>k,L5:()=>m,NW:()=>b,PV:()=>w,YO:()=>_,Yj:()=>d,ai:()=>p,bz:()=>n,eu:()=>s,g1:()=>f,lq:()=>h,me:()=>c,nd:()=>o,vP:()=>a,zM:()=>i});var e=__webpack_require__(6848),t=__webpack_require__(4394),r=__webpack_require__(7566);function n(){return(0,r.E8)("any",(()=>!0))}function _(r){return new e._k({type:"array",schema:r,*entries(e){if(r&&Array.isArray(e))for(const[t,n]of e.entries())yield[t,n,r]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||`Expected an array value, but received: ${(0,t.yy)(e)}`})}function i(){return(0,r.E8)("boolean",(e=>"boolean"==typeof e))}function a(r){const n={},_=r.map((e=>(0,t.yy)(e))).join();for(const e of r)n[e]=e;return new e._k({type:"enums",schema:n,validator:e=>r.includes(e)||`Expected one of \`${_}\`, but received: ${(0,t.yy)(e)}`})}function o(){return(0,r.E8)("integer",(e=>"number"==typeof e&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${(0,t.yy)(e)}`))}function s(r){const n=(0,t.yy)(r),_=typeof r;return new e._k({type:"literal",schema:"string"===_||"number"===_||"boolean"===_?r:null,validator:e=>e===r||`Expected the literal \`${n}\`, but received: ${(0,t.yy)(e)}`})}function u(){return(0,r.E8)("never",(()=>!1))}function c(t){return new e._k({...t,validator:(e,r)=>null===e||t.validator(e,r),refiner:(e,r)=>null===e||t.refiner(e,r)})}function p(){return(0,r.E8)("number",(e=>"number"==typeof e&&!isNaN(e)||`Expected a number, but received: ${(0,t.yy)(e)}`))}function l(r){const n=r?Object.keys(r):[],_=u();return new e._k({type:"object",schema:r??null,*entries(i){if(r&&(0,t.Gv)(i)){const t=new Set(Object.keys(i));for(const _ of n){t.delete(_);const n=r[_];e.P$.isExactOptional(n)&&!Object.prototype.hasOwnProperty.call(i,_)||(yield[_,i[_],r[_]])}for(const e of t)yield[e,i[e],_]}},validator:e=>(0,t.Gv)(e)||`Expected an object, but received: ${(0,t.yy)(e)}`,coercer:e=>(0,t.Gv)(e)?{...e}:e})}function h(t){return new e._k({...t,validator:(e,r)=>e===undefined||t.validator(e,r),refiner:(e,r)=>e===undefined||t.refiner(e,r)})}function f(r,n){return new e._k({type:"record",schema:null,*entries(e){if((0,t.Gv)(e))for(const t in e){const _=e[t];yield[t,t,r],yield[t,_,n]}},validator:e=>(0,t.Gv)(e)||`Expected an object, but received: ${(0,t.yy)(e)}`})}function d(){return(0,r.E8)("string",(e=>"string"==typeof e||`Expected a string, but received: ${(0,t.yy)(e)}`))}function w(r){const n=u();return new e._k({type:"tuple",schema:null,*entries(e){if(Array.isArray(e)){const t=Math.max(r.length,e.length);for(let _=0;_<t;_++)yield[_,e[_],r[_]||n]}},validator:e=>Array.isArray(e)||`Expected an array, but received: ${(0,t.yy)(e)}`})}function b(r){const n=Object.keys(r);return new e._k({type:"type",schema:r,*entries(e){if((0,t.Gv)(e))for(const t of n)yield[t,e[t],r[t]]},validator:e=>(0,t.Gv)(e)||`Expected an object, but received: ${(0,t.yy)(e)}`,coercer:e=>(0,t.Gv)(e)?{...e}:e})}function k(r){const n=r.map((e=>e.type)).join(" | ");return new e._k({type:"union",schema:null,coercer(e){for(const t of r){const[r,n]=t.validate(e,{coerce:!0});if(!r)return n}return e},validator(e,_){const i=[];for(const n of r){const[...r]=(0,t.eF)(e,n,_),[a]=r;if(!a?.[0])return[];for(const[e]of r)e&&i.push(e)}return[`Expected the value to satisfy a union of \`${n}\`, but received: ${(0,t.yy)(e)}`,...i]}})}function m(){return(0,r.E8)("unknown",(()=>!0))}}}).call(__webpack_require__._LM_("undefined",{__webpack_require__,__webpack_exports__}))()},4856:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=__webpack_require__(46).EventEmitter}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},4869:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(5699).F.ERR_STREAM_PREMATURE_CLOSE;function t(){}module.exports=function r(n,_,i){if("function"==typeof _)return r(n,null,_);_||(_={}),i=function(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),_=0;_<r;_++)n[_]=arguments[_];e.apply(this,n)}}}(i||t);var a=_.readable||!1!==_.readable&&n.readable,o=_.writable||!1!==_.writable&&n.writable,s=function(){n.writable||c()},u=n._writableState&&n._writableState.finished,c=function(){o=!1,u=!0,a||i.call(n)},p=n._readableState&&n._readableState.endEmitted,l=function(){a=!1,p=!0,o||i.call(n)},h=function(e){i.call(n,e)},f=function(){var t;return a&&!p?(n._readableState&&n._readableState.ended||(t=new e),i.call(n,t)):o&&!u?(n._writableState&&n._writableState.ended||(t=new e),i.call(n,t)):void 0},d=function(){n.req.on("finish",c)};return function(e){return e.setHeader&&"function"==typeof e.abort}(n)?(n.on("complete",c),n.on("abort",f),n.req?d():n.on("request",d)):o&&!n._writableState&&(n.on("end",s),n.on("close",s)),n.on("end",l),n.on("finish",c),!1!==_.error&&n.on("error",h),n.on("close",f),function(){n.removeListener("complete",c),n.removeListener("abort",f),n.removeListener("request",d),n.req&&n.req.removeListener("finish",c),n.removeListener("end",s),n.removeListener("close",s),n.removeListener("finish",c),n.removeListener("end",l),n.removeListener("error",h),n.removeListener("close",f)}}}}).call(__webpack_require__._LM_("16",{__webpack_require__,module}))()},4937:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Ee:()=>c});var e,t=__webpack_require__(4729),r=__webpack_require__(7566),n=__webpack_require__(1705),_=__webpack_require__(7914),i=__webpack_require__(4102),a=__webpack_require__(4980),o=__webpack_require__(6725),s=__webpack_require__(511);!function(e){e.Default="default",e.Critical="critical",e.Warning="warning"}(e||(e={}));const u=(0,t.KC)([_.x,i.z,n.A]),c=(0,r.kp)(s.Tl,(0,t.Ik)({type:(0,a.eu)(s.Z6.Row),variant:(0,t.lq)((0,t.KC)([(0,a.$9)(e.Default),(0,a.$9)(e.Critical),(0,a.$9)(e.Warning)])),label:(0,t.Yj)(),value:u}));(0,o.I)(s.Z6.Row,c,["label","value","variant"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},4980:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{$9:()=>a,E$:()=>s,KC:()=>i,eu:()=>_,i5:()=>o});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(6848),n=__webpack_require__(2462);function _(r){return(0,e.E8)(JSON.stringify(r),(0,t.eu)(r).validator)}function i([e,...n]){const _=(0,t.KC)([e,...n]);return new r._k({..._,schema:[e,...n]})}function a(e){return _(e)}function o(e){const t=e.map((e=>"union"===e.type&&Array.isArray(e.schema)?e.schema:e)).flat(Infinity),_=t.map((({schema:e})=>e.type.type)),i=t.reduce(((e,t)=>(e[JSON.parse(t.schema.type.type)]=t,e)),{});return new r._k({type:"union",schema:t,*entries(e,t){if(!(0,n.Gv)(e)||!(0,n.i5)(e,"type")||"string"!=typeof e.type)return;const{type:r}=e,_=i[r];if(_)for(const r of _.entries(e,t))yield r},coercer(e,t){if(!(0,n.Gv)(e)||!(0,n.i5)(e,"type")||"string"!=typeof e.type)return e;const{type:r}=e,_=i[r];return _?_.coercer(e,t):e},*refiner(e,t){const r=i[e.type];yield*r.refiner(e,t)},validator(e,t){if(!(0,n.Gv)(e)||!(0,n.i5)(e,"type")||"string"!=typeof e.type)return`Expected type to be one of: ${_.join(", ")}, but received: undefined`;const{type:r}=e,a=i[r];return a?a.validator(e,t):`Expected type to be one of: ${_.join(", ")}, but received: "${r}"`}})}function s(e){return new r._k({type:"union",schema:null,*entries(t,r){const n=e(t);for(const e of n.entries(t,r))yield e},*refiner(t,r){const n=e(t);yield*n.refiner(t,r)},coercer:(t,r)=>e(t).coercer(t,r),validator:(t,r)=>e(t).validator(t,r)})}}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},5036:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Ay:()=>a});var e=__webpack_require__(9400),t=__webpack_require__(8746);const r=new Set(["log","assert","error","debug","info","warn"]),n=new Set(["debug","error","info","log","warn","dir","dirxml","table","trace","group","groupCollapsed","groupEnd","clear","count","countReset","assert","profile","profileEnd","time","timeLog","timeEnd","timeStamp","context"]),_=["log","error","debug","info","warn"];function i(e,t,...r){const n=`[Snap: ${e}]`;return"string"==typeof t?[`${n} ${t}`,...r]:[n,t,...r]}const a={names:["console"],factory:function({snapId:a}={}){(0,e.vA)(a!==undefined);const o=Object.getOwnPropertyNames(t.h.console).reduce(((e,_)=>n.has(_)&&!r.has(_)?{...e,[_]:t.h.console[_]}:e),{});return harden({console:{...o,assert:(e,r,...n)=>{t.h.console.assert(e,...i(a,r,...n))},..._.reduce(((e,r)=>({...e,[r]:(e,...n)=>{t.h.console[r](...i(a,e,...n))}})),{})}})}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},5039:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517),t=__webpack_require__(7476);module.exports=(r,n,_)=>{let i=null,a=null,o=null;try{o=new t(n,_)}catch(e){return null}return r.forEach((t=>{o.test(t)&&(i&&-1!==a.compare(t)||(i=t,a=new e(i,_)))})),i}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},5204:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>e});const e={names:["TextDecoder"],factory:()=>({TextDecoder:harden(TextDecoder)})}}}).call(__webpack_require__._LM_("0",{__webpack_exports__,__webpack_require__}))()},5259:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Aw:()=>k,SS:()=>h,SZ:()=>d,U$:()=>E,bo:()=>f,jU:()=>l,p3:()=>y,tQ:()=>g,x8:()=>b,zC:()=>p});var e=__webpack_require__(4729),t=__webpack_require__(6848),r=__webpack_require__(7566),n=__webpack_require__(3837),_=__webpack_require__(8464),i=__webpack_require__(2462);const a=t=>(0,e.Ik)(t);function o({path:e,branch:t}){const r=e[e.length-1];return(0,i.i5)(t[t.length-2],r)}function s(e){return new t._k({...e,type:`optional ${e.type}`,validator:(t,r)=>!o(r)||e.validator(t,r),refiner:(t,r)=>!o(r)||e.refiner(t,r)})}function u(e){if(null===e||"boolean"==typeof e||"string"==typeof e)return!0;if("number"==typeof e&&Number.isFinite(e))return!0;if("object"==typeof e){let t=!0;if(Array.isArray(e)){for(let r=0;r<e.length;r++)if(!u(e[r])){t=!1;break}return t}const r=Object.entries(e);for(let e=0;e<r.length;e++)if("string"!=typeof r[e][0]||!u(r[e][1])){t=!1;break}return t}return!1}const c=(0,r.E8)("JSON",(e=>u(e))),p=(0,n.au)(c,(0,_.YP)((0,e.bz)(),"JSON",(e=>(0,t.is)(e,c))),(e=>JSON.parse(JSON.stringify(e,((e,t)=>"__proto__"===e||"constructor"===e?undefined:t)))));function l(e){try{return h(e),!0}catch{return!1}}function h(e){return(0,t.vt)(e,p)}const f=(0,e.eu)("2.0"),d=(0,e.me)((0,e.KC)([(0,e.ai)(),(0,e.Yj)()])),w=a({code:(0,e.nd)(),message:(0,e.Yj)(),data:s(p),stack:s((0,e.Yj)())}),b=(0,e.KC)([(0,e.g1)((0,e.Yj)(),p),(0,e.YO)(p)]),k=a({id:d,jsonrpc:f,method:(0,e.Yj)(),params:s(b)}),m=a({jsonrpc:f,method:(0,e.Yj)(),params:s(b)});function g(e){return(0,t.is)(e,m)}function y(e){return(0,t.is)(e,k)}(0,e.Ik)({id:d,jsonrpc:f,result:(0,e.lq)((0,e.L5)()),error:(0,e.lq)(w)});const q=a({id:d,jsonrpc:f,result:p}),v=a({id:d,jsonrpc:f,error:w});function E(e){return(0,t.is)(e,w)}(0,e.KC)([q,v])}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},5291:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t=__webpack_require__(9907);function r(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t){var r=e.entry;for(e.entry=null;r;){var n=r.callback;t.pendingcb--,n(undefined),r=r.next}t.corkedRequestsFree.next=e}(t,e)}}module.exports=q,q.WritableState=y;var n,_={deprecate:__webpack_require__(6732)},i=__webpack_require__(4856),a=__webpack_require__(1048).Buffer,o=(void 0!==__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},s=__webpack_require__(6527),u=__webpack_require__(9952).getHighWaterMark,c=__webpack_require__(5699).F,p=c.ERR_INVALID_ARG_TYPE,l=c.ERR_METHOD_NOT_IMPLEMENTED,h=c.ERR_MULTIPLE_CALLBACK,f=c.ERR_STREAM_CANNOT_PIPE,d=c.ERR_STREAM_DESTROYED,w=c.ERR_STREAM_NULL_VALUES,b=c.ERR_STREAM_WRITE_AFTER_END,k=c.ERR_UNKNOWN_ENCODING,m=s.errorOrDestroy;function g(){}function y(n,_,i){e=e||__webpack_require__(1265),n=n||{},"boolean"!=typeof i&&(i=_ instanceof e),this.objectMode=!!n.objectMode,i&&(this.objectMode=this.objectMode||!!n.writableObjectMode),this.highWaterMark=u(this,n,"writableHighWaterMark",i),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var a=!1===n.decodeStrings;this.decodeStrings=!a,this.defaultEncoding=n.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,r){var n=e._writableState,_=n.sync,i=n.writecb;if("function"!=typeof i)throw new h;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),r)!function(e,r,n,_,i){--r.pendingcb,n?(t.nextTick(i,_),t.nextTick(T,e,r),e._writableState.errorEmitted=!0,m(e,_)):(i(_),e._writableState.errorEmitted=!0,m(e,_),T(e,r))}(e,n,_,r,i);else{var a=x(n)||e.destroyed;a||n.corked||n.bufferProcessing||!n.bufferedRequest||S(e,n),_?t.nextTick(E,e,n,a,i):E(e,n,a,i)}}(_,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==n.emitClose,this.autoDestroy=!!n.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new r(this)}function q(t){var r=this instanceof(e=e||__webpack_require__(1265));if(!r&&!n.call(q,this))return new q(t);this._writableState=new y(t,this,r),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),i.call(this)}function v(e,t,r,n,_,i,a){t.writelen=n,t.writecb=a,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new d("write")):r?e._writev(_,t.onwrite):e._write(_,i,t.onwrite),t.sync=!1}function E(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),T(e,t)}function S(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var _=t.bufferedRequestCount,i=new Array(_),a=t.corkedRequestsFree;a.entry=n;for(var o=0,s=!0;n;)i[o]=n,n.isBuf||(s=!1),n=n.next,o+=1;i.allBuffers=s,v(e,t,!0,t.length,i,"",a.finish),t.pendingcb++,t.lastBufferedRequest=null,a.next?(t.corkedRequestsFree=a.next,a.next=null):t.corkedRequestsFree=new r(t),t.bufferedRequestCount=0}else{for(;n;){var u=n.chunk,c=n.encoding,p=n.callback;if(v(e,t,!1,t.objectMode?1:u.length,u,c,p),n=n.next,t.bufferedRequestCount--,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequest=n,t.bufferProcessing=!1}function x(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function R(e,t){e._final((function(r){t.pendingcb--,r&&m(e,r),t.prefinished=!0,e.emit("prefinish"),T(e,t)}))}function T(e,r){var n=x(r);if(n&&(function(e,r){r.prefinished||r.finalCalled||("function"!=typeof e._final||r.destroyed?(r.prefinished=!0,e.emit("prefinish")):(r.pendingcb++,r.finalCalled=!0,t.nextTick(R,e,r)))}(e,r),0===r.pendingcb&&(r.finished=!0,e.emit("finish"),r.autoDestroy))){var _=e._readableState;(!_||_.autoDestroy&&_.endEmitted)&&e.destroy()}return n}__webpack_require__(5615)(q,i),y.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(y.prototype,"buffer",{get:_.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(n=Function.prototype[Symbol.hasInstance],Object.defineProperty(q,Symbol.hasInstance,{value:function(e){return!!n.call(this,e)||this===q&&e&&e._writableState instanceof y}})):n=function(e){return e instanceof this},q.prototype.pipe=function(){m(this,new f)},q.prototype.write=function(e,r,n){var _,i=this._writableState,s=!1,u=!i.objectMode&&(_=e,a.isBuffer(_)||_ instanceof o);return u&&!a.isBuffer(e)&&(e=function(e){return a.from(e)}(e)),"function"==typeof r&&(n=r,r=null),u?r="buffer":r||(r=i.defaultEncoding),"function"!=typeof n&&(n=g),i.ending?function(e,r){var n=new b;m(e,n),t.nextTick(r,n)}(this,n):(u||function(e,r,n,_){var i;return null===n?i=new w:"string"==typeof n||r.objectMode||(i=new p("chunk",["string","Buffer"],n)),!i||(m(e,i),t.nextTick(_,i),!1)}(this,i,e,n))&&(i.pendingcb++,s=function(e,t,r,n,_,i){if(!r){var o=function(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=a.from(t,r)),t}(t,n,_);n!==o&&(r=!0,_="buffer",n=o)}var s=t.objectMode?1:n.length;t.length+=s;var u=t.length<t.highWaterMark;if(u||(t.needDrain=!0),t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:_,isBuf:r,callback:i,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else v(e,t,!1,s,n,_,i);return u}(this,i,u,e,r,n)),s},q.prototype.cork=function(){this._writableState.corked++},q.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||S(this,e))},q.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new k(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(q.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(q.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),q.prototype._write=function(e,t,r){r(new l("_write()"))},q.prototype._writev=null,q.prototype.end=function(e,r,n){var _=this._writableState;return"function"==typeof e?(n=e,e=null,r=null):"function"==typeof r&&(n=r,r=null),null!==e&&e!==undefined&&this.write(e,r),_.corked&&(_.corked=1,this.uncork()),_.ending||function(e,r,n){r.ending=!0,T(e,r),n&&(r.finished?t.nextTick(n):e.once("finish",n)),r.ended=!0,e.writable=!1}(this,_,n),this},Object.defineProperty(q.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(q.prototype,"destroyed",{enumerable:!1,get:function(){return this._writableState!==undefined&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),q.prototype.destroy=s.destroy,q.prototype._undestroy=s.undestroy,q.prototype._destroy=function(e,t){t(e)}}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},5303:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>e});const e={errors:{disconnected:()=>"MetaMask: Disconnected from chain. Attempting to connect.",permanentlyDisconnected:()=>"MetaMask: Disconnected from MetaMask background. Page reload required.",sendSiteMetadata:()=>"MetaMask: Failed to send site metadata. This is an internal error, please report this bug.",unsupportedSync:e=>`MetaMask: The MetaMask Ethereum provider does not support synchronous methods like ${e} without a callback parameter.`,invalidDuplexStream:()=>"Must provide a Node.js-style duplex stream.",invalidNetworkParams:()=>"MetaMask: Received invalid network parameters. Please report this bug.",invalidRequestArgs:()=>"Expected a single, non-array, object argument.",invalidRequestMethod:()=>"'args.method' must be a non-empty string.",invalidRequestParams:()=>"'args.params' must be an object or array if provided.",invalidLoggerObject:()=>"'args.logger' must be an object if provided.",invalidLoggerMethod:e=>`'args.logger' must include required method '${e}'.`},info:{connected:e=>`MetaMask: Connected to chain with ID "${e}".`},warnings:{enableDeprecation:"MetaMask: 'ethereum.enable()' is deprecated and may be removed in the future. Please use the 'eth_requestAccounts' RPC method instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1102",sendDeprecation:"MetaMask: 'ethereum.send(...)' is deprecated and may be removed in the future. Please use 'ethereum.sendAsync(...)' or 'ethereum.request(...)' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193",events:{close:"MetaMask: The event 'close' is deprecated and may be removed in the future. Please use 'disconnect' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#disconnect",data:"MetaMask: The event 'data' is deprecated and will be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message",networkChanged:"MetaMask: The event 'networkChanged' is deprecated and may be removed in the future. Use 'chainChanged' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#chainchanged",notification:"MetaMask: The event 'notification' is deprecated and may be removed in the future. Use 'message' instead.\nFor more information, see: https://eips.ethereum.org/EIPS/eip-1193#message"},rpc:{ethDecryptDeprecation:"MetaMask: The RPC method 'eth_decrypt' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",ethGetEncryptionPublicKeyDeprecation:"MetaMask: The RPC method 'eth_getEncryptionPublicKey' is deprecated and may be removed in the future.\nFor more information, see: https://medium.com/metamask/metamask-api-method-deprecation-2b0564a84686",walletWatchAssetNFTExperimental:"MetaMask: The RPC method 'wallet_watchAsset' is experimental for ERC721/ERC1155 assets and may change in the future.\nFor more information, see: https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-1.md and https://github.com/MetaMask/metamask-improvement-proposals/blob/main/PROCESS-GUIDE.md#proposal-lifecycle"},experimentalMethods:"MetaMask: 'ethereum._metamask' exposes non-standard, experimental methods. They may be removed or changed without warning."}}}}).call(__webpack_require__._LM_("5",{__webpack_exports__,__webpack_require__}))()},5316:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{a:()=>n});var e=__webpack_require__(6157),t=__webpack_require__(9400),r=__webpack_require__(2945);function n(n,_,i){return{ping:async()=>Promise.resolve("OK"),terminate:async()=>(i(),Promise.resolve("OK")),executeSnap:async(e,t,r)=>(await n(e,t,r),"OK"),snapRpc:async(n,i,a,o)=>await _(n,i,function(n,_,i){switch(_){case e.Z.OnTransaction:{(0,r.lK)(i.params);const{transaction:e,chainId:t,transactionOrigin:n}=i.params;return{transaction:e,chainId:t,transactionOrigin:n}}case e.Z.OnSignature:{(0,r.x7)(i.params);const{signature:e,signatureOrigin:t}=i.params;return{signature:e,signatureOrigin:t}}case e.Z.OnAssetHistoricalPrice:{(0,r.o3)(i.params);const{from:e,to:t}=i.params;return{from:e,to:t}}case e.Z.OnAssetsLookup:{(0,r.sq)(i.params);const{assets:e}=i.params;return{assets:e}}case e.Z.OnAssetsConversion:{(0,r.aM)(i.params);const{conversions:e,includeMarketData:t}=i.params;return{conversions:e,includeMarketData:t}}case e.Z.OnNameLookup:{(0,r.G2)(i.params);const{chainId:e,domain:t,address:n}=i.params;return t?{chainId:e,domain:t}:{chainId:e,address:n}}case e.Z.OnProtocolRequest:{(0,r.tQ)(i.params);const{request:e,scope:t}=i.params;return{origin:n,request:e,scope:t}}case e.Z.OnWebSocketEvent:{(0,r.J6)(i.params);const{event:e}=i.params;return{event:e}}case e.Z.OnRpcRequest:case e.Z.OnKeyringRequest:return{origin:n,request:i};case e.Z.OnClientRequest:case e.Z.OnCronjob:return{request:i};case e.Z.OnInstall:case e.Z.OnUpdate:case e.Z.OnStart:return{origin:n};case e.Z.OnHomePage:case e.Z.OnSettingsPage:return{};case e.Z.OnUserInput:{(0,r.W8)(i.params);const{id:e,event:t,context:n}=i.params;return{id:e,event:t,context:n}}default:return(0,t.Bd)(_)}}(a,i,o))??null}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},5357:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517),t=__webpack_require__(7476);module.exports=(r,n,_)=>{let i=null,a=null,o=null;try{o=new t(n,_)}catch(e){return null}return r.forEach((t=>{o.test(t)&&(i&&1!==a.compare(t)||(i=t,a=new e(i,_)))})),i}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},5615:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";"function"==typeof Object.create?module.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:module.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},5636:(module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(1048),t=e.Buffer;function r(e,t){for(var r in e)t[r]=e[r]}function n(e,r,n){return t(e,r,n)}t.from&&t.alloc&&t.allocUnsafe&&t.allocUnsafeSlow?module.exports=e:(r(e,exports),exports.Buffer=n),n.prototype=Object.create(t.prototype),r(t,n),n.from=function(e,r,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return t(e,r,n)},n.alloc=function(e,r,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var _=t(e);return r!==undefined?"string"==typeof n?_.fill(r,n):_.fill(r):_.fill(0),_},n.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return t(e)},n.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return e.SlowBuffer(t)}}}).call(__webpack_require__._LM_("17",{module,exports,__webpack_require__}))()},5699:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e={};function t(t,r,n){n||(n=Error);var _=function(e){var t,n;function _(t,n,_){return e.call(this,function(e,t,n){return"string"==typeof r?r:r(e,t,n)}(t,n,_))||this}return n=e,(t=_).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n,_}(n);_.prototype.name=n.name,_.prototype.code=t,e[t]=_}function r(e,t){if(Array.isArray(e)){var r=e.length;return e=e.map((function(e){return String(e)})),r>2?"one of ".concat(t," ").concat(e.slice(0,r-1).join(", "),", or ")+e[r-1]:2===r?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}return"of ".concat(t," ").concat(String(e))}t("ERR_INVALID_OPT_VALUE",(function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'}),TypeError),t("ERR_INVALID_ARG_TYPE",(function(e,t,n){var _,i,a,o,s;if("string"==typeof t&&(i="not ",t.substr(0,4)===i)?(_="must not be",t=t.replace(/^not /,"")):_="must be",function(e,t,r){return(r===undefined||r>e.length)&&(r=e.length),e.substring(r-9,r)===t}(e," argument"))a="The ".concat(e," ").concat(_," ").concat(r(t,"type"));else{var u=("number"!=typeof s&&(s=0),s+1>(o=e).length||-1===o.indexOf(".",s)?"argument":"property");a='The "'.concat(e,'" ').concat(u," ").concat(_," ").concat(r(t,"type"))}return a+". Received type ".concat(typeof n)}),TypeError),t("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),t("ERR_METHOD_NOT_IMPLEMENTED",(function(e){return"The "+e+" method is not implemented"})),t("ERR_STREAM_PREMATURE_CLOSE","Premature close"),t("ERR_STREAM_DESTROYED",(function(e){return"Cannot call "+e+" after a stream was destroyed"})),t("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),t("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),t("ERR_STREAM_WRITE_AFTER_END","write after end"),t("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),t("ERR_UNKNOWN_ENCODING",(function(e){return"Unknown encoding: "+e}),TypeError),t("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),module.exports.F=e}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},5791:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Y:()=>i});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(6725),_=__webpack_require__(511);const i=(0,e.kp)(_.Tl,(0,t.Ik)({type:(0,r.eu)(_.Z6.Heading),value:(0,t.Yj)()}));(0,n.I)(_.Z6.Heading,i,["value"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},5875:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7338);module.exports=e.ObjectMultiplex}}).call(__webpack_require__._LM_("3",{module,__webpack_require__}))()},5953:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{mz:()=>s});var e=__webpack_require__(8464),t=__webpack_require__(4729),r=__webpack_require__(2722);const{gt:n,gtr:_,satisfies:i,valid:a,validRange:o}=r,s=(0,e.YP)((0,t.Yj)(),"Version",(e=>null!==a(e)||`Expected SemVer version, got "${e}"`));(0,e.YP)((0,t.Yj)(),"Version range",(e=>null!==o(e)||`Expected SemVer range, got "${e}"`))}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},6082:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r)=>new e(t,r).patch}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6106:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r,n)=>{const _=new e(t,n),i=new e(r,n);return _.compare(i)||_.compareBuild(i)}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6157:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e;__webpack_require__.d(__webpack_exports__,{Z:()=>e}),function(e){e.OnRpcRequest="onRpcRequest",e.OnSignature="onSignature",e.OnTransaction="onTransaction",e.OnCronjob="onCronjob",e.OnInstall="onInstall",e.OnUpdate="onUpdate",e.OnStart="onStart",e.OnNameLookup="onNameLookup",e.OnKeyringRequest="onKeyringRequest",e.OnHomePage="onHomePage",e.OnSettingsPage="onSettingsPage",e.OnUserInput="onUserInput",e.OnAssetsLookup="onAssetsLookup",e.OnAssetsConversion="onAssetsConversion",e.OnAssetHistoricalPrice="onAssetHistoricalPrice",e.OnProtocolRequest="onProtocolRequest",e.OnClientRequest="onClientRequest",e.OnWebSocketEvent="onWebSocketEvent"}(e||(e={}))}}).call(__webpack_require__._LM_("9",{__webpack_exports__,__webpack_require__}))()},6219:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Es:()=>u,pz:()=>h,rI:()=>o,ux:()=>s});var e,t=__webpack_require__(4729),r=__webpack_require__(7566),n=__webpack_require__(2521),_=__webpack_require__(4980);!function(e){e.ButtonClickEvent="ButtonClickEvent",e.FormSubmitEvent="FormSubmitEvent",e.InputChangeEvent="InputChangeEvent",e.FileUploadEvent="FileUploadEvent"}(e||(e={}));const i=(0,t.Ik)({type:(0,t.Yj)(),name:(0,t.lq)((0,t.Yj)())}),a=(0,r.kp)(i,(0,t.Ik)({type:(0,_.eu)(e.ButtonClickEvent),name:(0,t.lq)((0,t.Yj)())})),o=(0,t.Ik)({accountId:(0,t.Yj)(),addresses:(0,t.YO)(n.FX)}),s=(0,t.Ik)({name:(0,t.Yj)(),size:(0,t.ai)(),contentType:(0,t.Yj)(),contents:(0,t.Yj)()}),u=(0,t.Ik)({asset:n.jx,name:(0,t.Yj)(),symbol:(0,t.Yj)()}),c=(0,r.kp)(i,(0,t.Ik)({type:(0,_.eu)(e.FormSubmitEvent),value:(0,t.g1)((0,t.Yj)(),(0,t.me)((0,t.KC)([(0,t.Yj)(),s,(0,t.zM)(),o,u]))),name:(0,t.Yj)()})),p=(0,r.kp)(i,(0,t.Ik)({type:(0,_.eu)(e.InputChangeEvent),name:(0,t.Yj)(),value:(0,t.KC)([(0,t.Yj)(),(0,t.zM)(),o,u])})),l=(0,r.kp)(i,(0,t.Ik)({type:(0,_.eu)(e.FileUploadEvent),name:(0,t.Yj)(),file:(0,t.me)(s)})),h=(0,_.i5)([a,c,p,l])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},6231:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{DW:()=>o,P5:()=>s,_K:()=>u,el:()=>p});var e=__webpack_require__(2462),t=__webpack_require__(5259),r=__webpack_require__(7822);const n=r.f.rpc.internal,_="Unspecified error message. This is a bug, please report it.",i={code:n,message:o(n)},a="Unspecified server error.";function o(t,n=_){if(function(e){return Number.isInteger(e)}(t)){const n=t.toString();if((0,e.i5)(r.z,n))return r.z[n].message;if(function(e){return e>=-32099&&e<=-32e3}(t))return a}return n}function s(r,{fallbackError:n=i,shouldIncludeStack:_=!0,shouldPreserveMessage:a=!0}={}){if(!(0,t.U$)(n))throw new Error("Must provide fallback error with integer number code and string message.");const o=function(r,n,_){if(r&&"object"==typeof r&&"serialize"in r&&"function"==typeof r.serialize)return r.serialize();if((0,t.U$)(r))return r;const i=function(t){return(0,e.Gv)(t)&&(0,e.i5)(t,"message")&&"string"==typeof t.message&&t.message.length>0?t.message:undefined}(r);return{...n,..._&&i&&{message:i},data:{cause:u(r)}}}(r,n,a);return _||delete o.stack,o}function u(r){return Array.isArray(r)?r.map((r=>(0,t.jU)(r)?r:(0,e.Gv)(r)?c(r):null)):(0,e.Gv)(r)?c(r):(0,t.jU)(r)?r:null}function c(e){return Object.getOwnPropertyNames(e).reduce(((r,n)=>{const _=e[n];return(0,t.jU)(_)&&(r[n]=_),r}),{})}function p(t){return(0,e.Gv)(t)&&(0,e.i5)(t,"cause")&&(0,e.Gv)(t.cause)}}}).call(__webpack_require__._LM_("6",{__webpack_require__,__webpack_exports__}))()},6364:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7476);module.exports=(t,r)=>new e(t,r).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6381:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r)=>new e(t,r).major}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6486:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7229),t=__webpack_require__(7851);module.exports=(r,n,_)=>{const i=[];let a=null,o=null;const s=r.sort(((e,r)=>t(e,r,_)));for(const t of s)e(t,n,_)?(o=t,a||(a=t)):(o&&i.push([a,o]),o=null,a=null);a&&i.push([a,null]);const u=[];for(const[e,t]of i)e===t?u.push(e):t||e!==s[0]?t?e===s[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");const c=u.join(" || "),p="string"==typeof n.raw?n.raw:String(n);return c.length<p.length?c:n}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6527:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(9907);function t(e,t){n(e,t),r(e)}function r(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function n(e,t){e.emit("error",t)}module.exports={destroy:function(_,i){var a=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(i?i(_):_&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(n,this,_)):e.nextTick(n,this,_)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(_||null,(function(n){!i&&n?a._writableState?a._writableState.errorEmitted?e.nextTick(r,a):(a._writableState.errorEmitted=!0,e.nextTick(t,a,n)):e.nextTick(t,a,n):i?(e.nextTick(r,a),i(n)):e.nextTick(r,a)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}}}}).call(__webpack_require__._LM_("16",{__webpack_require__,module}))()},6540:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Pm:()=>ke});var e=__webpack_require__(4729),t=__webpack_require__(7566),r=__webpack_require__(8464),n=__webpack_require__(5259),_=__webpack_require__(2462),i=__webpack_require__(2521),a=__webpack_require__(1089),o=__webpack_require__(2487),s=__webpack_require__(2489),u=__webpack_require__(4980),c=__webpack_require__(4),p=__webpack_require__(7002);const l=(0,s.J)([(0,e.Yj)(),(0,e.ai)()]),h=f([(0,e.Yj)()]);function f(r){const n=1===r.length?r[0]:(0,s.J)(r);return function(r){const n=(0,u.E$)((_=>Array.isArray(_)?(0,e.YO)((0,t.RZ)((()=>n))):r));return n}((0,e.me)((0,u.E$)((t=>"boolean"==typeof t?(0,e.zM)():n))))}function d(t){return(0,e.me)((0,u.E$)((r=>"boolean"==typeof r?(0,e.zM)():t)))}function w(t,r={}){return(0,e.Ik)({type:(0,u.eu)(t),props:(0,e.Ik)(r),key:(0,e.me)(l)})}(0,e.Ik)({type:(0,e.Yj)(),props:(0,e.g1)((0,e.Yj)(),n.zC),key:(0,e.me)(l)});const b=(0,s.J)([(0,u.eu)("none"),(0,u.eu)("medium"),(0,u.eu)("full")]),k=w("Image",{src:(0,c.J)(),alt:(0,e.lq)((0,e.Yj)()),borderRadius:(0,e.lq)(b)}),m=(0,s.J)(Object.values(o.$).map((e=>(0,u.eu)(e)))),g=w("Icon",{name:m,color:(0,e.lq)((0,s.J)([(0,u.eu)("default"),(0,u.eu)("primary"),(0,u.eu)("muted")])),size:(0,e.lq)((0,s.J)([(0,u.eu)("md"),(0,u.eu)("inherit")]))}),y=w("Button",{children:f([h,k,g]),name:(0,e.lq)((0,e.Yj)()),type:(0,e.lq)((0,s.J)([(0,u.eu)("button"),(0,u.eu)("submit")])),variant:(0,e.lq)((0,s.J)([(0,u.eu)("primary"),(0,u.eu)("destructive")])),size:(0,e.lq)((0,s.J)([(0,u.eu)("sm"),(0,u.eu)("md")])),disabled:(0,e.lq)((0,e.zM)()),loading:(0,e.lq)((0,e.zM)()),form:(0,e.lq)((0,e.Yj)())}),q=w("Checkbox",{name:(0,e.Yj)(),checked:(0,e.lq)((0,e.zM)()),label:(0,e.lq)((0,e.Yj)()),variant:(0,e.lq)((0,s.J)([(0,u.eu)("default"),(0,u.eu)("toggle")])),disabled:(0,e.lq)((0,e.zM)())}),v=(0,e.Ik)({name:(0,e.Yj)(),value:(0,e.lq)((0,e.Yj)()),placeholder:(0,e.lq)((0,e.Yj)()),disabled:(0,e.lq)((0,e.zM)())}),E=(0,t.kp)(v,(0,e.Ik)({type:(0,u.eu)("text")})),S=(0,t.kp)(v,(0,e.Ik)({type:(0,u.eu)("password")})),x=(0,t.kp)(v,(0,e.Ik)({type:(0,u.eu)("number"),min:(0,e.lq)((0,e.ai)()),max:(0,e.lq)((0,e.ai)()),step:(0,e.lq)((0,e.ai)())})),R=(T=e=>{if((0,_.Qd)(e)&&(0,_.i5)(e,"type"))switch(e.type){case"text":return E;case"password":return S;case"number":return x;default:return v}return v},(0,e.Ik)({type:(0,u.eu)("Input"),props:(0,u.E$)(T),key:(0,e.me)(l)}));var T;const M=w("AddressInput",{name:(0,e.Yj)(),chainId:i.dg,value:(0,e.lq)((0,e.Yj)()),placeholder:(0,e.lq)((0,e.Yj)()),disabled:(0,e.lq)((0,e.zM)()),displayAvatar:(0,e.lq)((0,e.zM)())}),O=w("Option",{value:(0,e.Yj)(),children:(0,e.Yj)(),disabled:(0,e.lq)((0,e.zM)())}),L=w("Dropdown",{name:(0,e.Yj)(),value:(0,e.lq)((0,e.Yj)()),children:f([O]),disabled:(0,e.lq)((0,e.zM)())}),A=w("Address",{address:(0,u.E$)((e=>"string"==typeof e&&e.startsWith("0x")?a.UG:i.FX)),truncate:(0,e.lq)((0,e.zM)()),displayName:(0,e.lq)((0,e.zM)()),avatar:(0,e.lq)((0,e.zM)())}),I=w("AccountSelector",{name:(0,e.Yj)(),hideExternalAccounts:(0,e.lq)((0,e.zM)()),chainIds:(0,e.lq)((0,e.YO)(i.dg)),switchGlobalAccount:(0,e.lq)((0,e.zM)()),value:(0,e.lq)(i.FX),disabled:(0,e.lq)((0,e.zM)())}),C=w("Card",{image:(0,e.lq)((0,e.Yj)()),title:(0,u.E$)((t=>"object"==typeof t?A:(0,e.Yj)())),description:(0,e.lq)((0,e.Yj)()),value:(0,e.Yj)(),extra:(0,e.lq)((0,e.Yj)())}),j=w("SelectorOption",{value:(0,e.Yj)(),children:C,disabled:(0,e.lq)((0,e.zM)())}),P=w("Selector",{name:(0,e.Yj)(),title:(0,e.Yj)(),value:(0,e.lq)((0,e.Yj)()),children:f([j]),disabled:(0,e.lq)((0,e.zM)())}),$=w("AssetSelector",{name:(0,e.Yj)(),addresses:p.j6,chainIds:(0,e.lq)((0,e.YO)(p.s1)),value:(0,e.lq)(p._g),disabled:(0,e.lq)((0,e.zM)())}),N=w("Radio",{value:(0,e.Yj)(),children:(0,e.Yj)(),disabled:(0,e.lq)((0,e.zM)())}),G=w("RadioGroup",{name:(0,e.Yj)(),value:(0,e.lq)((0,e.Yj)()),children:f([N]),disabled:(0,e.lq)((0,e.zM)())}),F=w("FileInput",{name:(0,e.Yj)(),accept:(0,s.J)([(0,e.lq)((0,e.YO)((0,e.Yj)()))]),compact:(0,e.lq)((0,e.zM)()),disabled:(0,e.lq)((0,e.zM)())}),H=[d((0,t.RZ)((()=>we))),R],D=[R,d((0,t.RZ)((()=>we)))],U=[d((0,t.RZ)((()=>we))),R,d((0,t.RZ)((()=>we)))],B=[$,M,I,R,L,G,F,q,P],Y=((0,s.J)([...B,...H,...D,...U]),(0,u.E$)((t=>{const r=Array.isArray(t);return r&&3===t.length?(0,e.PV)(U):r&&2===t.length?"Box"===t[0]?.type?(0,e.PV)(H):(0,e.PV)(D):(0,u.i5)(B)}))),z=w("Field",{label:(0,e.lq)((0,e.Yj)()),error:(0,e.lq)((0,e.Yj)()),children:Y}),W=w("Bold",{children:f([(0,e.Yj)(),(0,t.RZ)((()=>Z))])}),Z=w("Italic",{children:f([(0,e.Yj)(),(0,t.RZ)((()=>W))])}),J=(0,u.i5)([W,Z]),V=w("Avatar",{address:i.FX,size:(0,e.lq)((0,s.J)([(0,u.eu)("sm"),(0,u.eu)("md"),(0,u.eu)("lg")]))}),K=f([(0,t.RZ)((()=>we))]),X=w("Box",{children:K,direction:(0,e.lq)((0,s.J)([(0,u.eu)("horizontal"),(0,u.eu)("vertical")])),alignment:(0,e.lq)((0,s.J)([(0,u.eu)("start"),(0,u.eu)("center"),(0,u.eu)("end"),(0,u.eu)("space-between"),(0,u.eu)("space-around")])),crossAlignment:(0,e.lq)((0,s.J)([(0,u.eu)("start"),(0,u.eu)("center"),(0,u.eu)("end")])),center:(0,e.lq)((0,e.zM)())}),Q=w("Form",{children:K,name:(0,e.Yj)()}),ee=(0,r.YP)(y,"FooterButton",(e=>"string"==typeof e.props.children||"boolean"==typeof e.props.children||null===e.props.children||(!(!Array.isArray(e.props.children)||e.props.children.some((e=>"string"!=typeof e&&"boolean"!=typeof e&&null!==e)))||"Footer buttons may only contain text."))),te=w("Section",{children:K,direction:(0,e.lq)((0,s.J)([(0,u.eu)("horizontal"),(0,u.eu)("vertical")])),alignment:(0,e.lq)((0,s.J)([(0,u.eu)("start"),(0,u.eu)("center"),(0,u.eu)("end"),(0,u.eu)("space-between"),(0,u.eu)("space-around")]))}),re=w("Footer",{children:(0,u.E$)((t=>Array.isArray(t)?(0,e.PV)([ee,ee]):ee))}),ne=w("Copyable",{value:(0,e.Yj)(),sensitive:(0,e.lq)((0,e.zM)())}),_e=w("Divider"),ie=w("Heading",{children:h,size:(0,e.lq)((0,s.J)([(0,u.eu)("sm"),(0,u.eu)("md"),(0,u.eu)("lg")]))}),ae=w("Link",{href:(0,e.Yj)(),children:f([J,(0,e.Yj)(),g,k,A])}),oe=w("Skeleton",{width:(0,e.lq)((0,e.KC)([(0,e.ai)(),(0,e.Yj)()])),height:(0,e.lq)((0,e.KC)([(0,e.ai)(),(0,e.Yj)()])),borderRadius:(0,e.lq)(b)}),se=w("Text",{children:f([(0,u.E$)((t=>"string"==typeof t?(0,e.Yj)():(0,u.i5)([W,Z,ae,g,oe])))]),alignment:(0,e.lq)((0,s.J)([(0,u.eu)("start"),(0,u.eu)("center"),(0,u.eu)("end")])),color:(0,e.lq)((0,s.J)([(0,u.eu)("default"),(0,u.eu)("alternative"),(0,u.eu)("muted"),(0,u.eu)("error"),(0,u.eu)("success"),(0,u.eu)("warning")])),size:(0,e.lq)((0,s.J)([(0,u.eu)("sm"),(0,u.eu)("md")])),fontWeight:(0,e.lq)((0,s.J)([(0,u.eu)("regular"),(0,u.eu)("medium"),(0,u.eu)("bold")]))}),ue=w("Value",{value:(0,u.E$)((t=>"string"==typeof t?(0,e.Yj)():se)),extra:(0,u.E$)((t=>"string"==typeof t?(0,e.Yj)():se))}),ce=(0,u.E$)((t=>"boolean"==typeof t?(0,e.zM)():(0,u.i5)([se,W,Z,ae,k,g]))),pe=(0,u.E$)((t=>"string"==typeof t?(0,e.Yj)():(0,u.i5)([se,W,Z,ae,g]))),le=w("Tooltip",{children:(0,e.me)(ce),content:pe}),he=w("Banner",{children:f([se,ae,g,y,W,Z,oe]),title:(0,e.Yj)(),severity:(0,e.KC)([(0,u.eu)("danger"),(0,u.eu)("info"),(0,u.eu)("success"),(0,u.eu)("warning")])}),fe=w("Row",{label:(0,e.Yj)(),children:(0,u.i5)([A,k,se,ue,ae,oe]),variant:(0,e.lq)((0,s.J)([(0,u.eu)("default"),(0,u.eu)("warning"),(0,u.eu)("critical")])),tooltip:(0,e.lq)((0,e.Yj)())}),de=w("Spinner"),we=(0,u.i5)([I,A,$,M,W,X,y,ne,_e,L,G,z,F,Q,ie,R,k,Z,ae,fe,de,se,le,q,C,g,P,te,V,he,oe]),be=w("Container",{children:(0,u.E$)((t=>Array.isArray(t)?(0,e.PV)([we,re]):we)),backgroundColor:(0,e.lq)((0,s.J)([(0,u.eu)("default"),(0,u.eu)("alternative")]))}),ke=(0,u.i5)([we,be]);(0,u.i5)([$,M,I,y,R,F,z,Q,W,Z,A,X,ne,_e,ie,k,ae,fe,de,se,L,O,G,N,ue,le,q,re,be,C,g,P,j,te,V,he,oe])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},6605:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=e=>null!==e&&"object"==typeof e&&"function"==typeof e.pipe;e.writable=t=>e(t)&&!1!==t.writable&&"function"==typeof t._write&&"object"==typeof t._writableState,e.readable=t=>e(t)&&!1!==t.readable&&"function"==typeof t._read&&"object"==typeof t._readableState,e.duplex=t=>e.writable(t)&&e.readable(t),e.transform=t=>e.duplex(t)&&"function"==typeof t._transform&&"object"==typeof t._transformState,module.exports=e}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},6725:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{I:()=>r});var e=__webpack_require__(2462),t=__webpack_require__(9400);function r(r,n,_=[]){return(...i)=>{if(1===i.length&&(0,e.Qd)(i[0])){const e={...i[0],type:r};return(0,t.ut)(e,n,`Invalid ${r} component`),e}const a=_.reduce(((e,t,r)=>i[r]!==undefined?{...e,[t]:i[r]}:e),{type:r});return(0,t.ut)(a,n,`Invalid ${r} component`),a}}}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},6732:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e){try{if(!__webpack_require__.g.localStorage)return!1}catch(e){return!1}var t=__webpack_require__.g.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}module.exports=function(t,r){if(e("noDeprecation"))return t;var n=!1;return function(){if(!n){if(e("throwDeprecation"))throw new Error(r);e("traceDeprecation")?console.trace(r):console.warn(r),n=!0}return t.apply(this,arguments)}}}}).call(__webpack_require__._LM_("20",{module,__webpack_require__}))()},6783:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517),t=__webpack_require__(3955),{safeRe:r,t:n}=__webpack_require__(2841);module.exports=(_,i)=>{if(_ instanceof e)return _;if("number"==typeof _&&(_=String(_)),"string"!=typeof _)return null;let a=null;if((i=i||{}).rtl){const e=i.includePrerelease?r[n.COERCERTLFULL]:r[n.COERCERTL];let t;for(;(t=e.exec(_))&&(!a||a.index+a[0].length!==_.length);)a&&t.index+t[0].length===a.index+a[0].length||(a=t),e.lastIndex=t.index+t[1].length+t[2].length;e.lastIndex=-1}else a=_.match(i.includePrerelease?r[n.COERCEFULL]:r[n.COERCE]);if(null===a)return null;const o=a[2],s=a[3]||"0",u=a[4]||"0",c=i.includePrerelease&&a[5]?`-${a[5]}`:"",p=i.includePrerelease&&a[6]?`+${a[6]}`:"";return t(`${o}.${s}.${u}${c}${p}`,i)}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},6815:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t=__webpack_require__(5699).F,r=t.ERR_MISSING_ARGS,n=t.ERR_STREAM_DESTROYED;function _(e){if(e)throw e}function i(e){e()}function a(e,t){return e.pipe(t)}module.exports=function(){for(var t=arguments.length,o=new Array(t),s=0;s<t;s++)o[s]=arguments[s];var u,c=function(e){return e.length?"function"!=typeof e[e.length-1]?_:e.pop():_}(o);if(Array.isArray(o[0])&&(o=o[0]),o.length<2)throw new r("streams");var p=o.map((function(t,r){var _=r<o.length-1;return function(t,r,_,i){i=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(i);var a=!1;t.on("close",(function(){a=!0})),e===undefined&&(e=__webpack_require__(4869)),e(t,{readable:r,writable:_},(function(e){if(e)return i(e);a=!0,i()}));var o=!1;return function(e){if(!a&&!o)return o=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(t)?t.abort():"function"==typeof t.destroy?t.destroy():void i(e||new n("pipe"))}}(t,_,r>0,(function(e){u||(u=e),e&&p.forEach(i),_||(p.forEach(i),c(u))}))}));return o.reduce(a)}}}).call(__webpack_require__._LM_("16",{__webpack_require__,module}))()},6848:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{P$:()=>_,_k:()=>r,is:()=>o,tf:()=>s,vA:()=>i,vt:()=>a});var e=__webpack_require__(4663),t=__webpack_require__(4394);class r{constructor(e){const{type:r,schema:n,validator:_,refiner:i,coercer:a=e=>e,entries:o=function*(){}}=e;this.type=r,this.schema=n,this.entries=o,this.coercer=a,this.validator=_?(e,r)=>{const n=_(e,r);return(0,t.RF)(n,r,this,e)}:()=>[],this.refiner=i?(e,r)=>{const n=i(e,r);return(0,t.RF)(n,r,this,e)}:()=>[]}assert(e,t){return i(e,this,t)}create(e,t){return a(e,this,t)}is(e){return o(e,this)}mask(e,t){return function(e,t,r){const n=s(e,t,{coerce:!0,mask:!0,message:r});if(n[0])throw n[0];return n[1]}(e,this,t)}validate(e,t={}){return s(e,this,t)}}const n="EXACT_OPTIONAL";class _ extends r{constructor(e){super({...e,type:`exact optional ${e.type}`}),this.brand=n}static isExactOptional(e){return(0,t.Gv)(e)&&"brand"in e&&e.brand===n}}function i(e,t,r){const n=s(e,t,{message:r});if(n[0])throw n[0]}function a(e,t,r){const n=s(e,t,{coerce:!0,message:r});if(n[0])throw n[0];return n[1]}function o(e,t){return!s(e,t)[0]}function s(r,n,_={}){const i=(0,t.eF)(r,n,_),a=(0,t.jT)(i);if(a[0])return[new e.C(a[0],(function*(){for(const e of i)e[0]&&(yield e[0])})),undefined];const o=a[1];return[undefined,o]}}}).call(__webpack_require__._LM_("undefined",{__webpack_require__,__webpack_exports__}))()},6857:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{y9:()=>a});var e,t=__webpack_require__(7566),r=__webpack_require__(4729),n=__webpack_require__(4980),_=__webpack_require__(6725),i=__webpack_require__(511);!function(e){e.Text="text",e.Number="number",e.Password="password"}(e||(e={}));const a=(0,t.kp)(i.Tl,(0,r.Ik)({type:(0,n.eu)(i.Z6.Input),value:(0,r.lq)((0,r.Yj)()),name:(0,r.Yj)(),inputType:(0,r.lq)((0,r.KC)([(0,n.$9)(e.Text),(0,n.$9)(e.Password),(0,n.$9)(e.Number)])),placeholder:(0,r.lq)((0,r.Yj)()),label:(0,r.lq)((0,r.Yj)()),error:(0,r.lq)((0,r.Yj)())}));(0,_.I)(i.Z6.Input,a,["name","inputType","placeholder","value","label"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},6932:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>n,m:()=>r});var e=__webpack_require__(9400),t=__webpack_require__(8746);const r=()=>((0,e.vA)(t.h.crypto,"Crypto endowment requires `globalThis.crypto` to be defined."),(0,e.vA)(t.h.SubtleCrypto,"Crypto endowment requires `globalThis.SubtleCrypto` to be defined."),{crypto:harden(t.h.crypto),SubtleCrypto:harden(t.h.SubtleCrypto)}),n={names:["crypto","SubtleCrypto"],factory:r}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},7002:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{_g:()=>i,j6:()=>n,s1:()=>_});var e=__webpack_require__(8464),t=__webpack_require__(4729),r=__webpack_require__(2521);const n=(0,e.YP)((0,t.YO)(r.FX),"Non-EIP-155 Matching Addresses Account ID List",(e=>{const t=e.map((e=>(0,r.Dz)(e)));return t.every((({address:e,chain:{namespace:r}})=>e===t[0].address&&r===t[0].chain.namespace))?!t.some((({chain:{namespace:e}})=>e===r.wV.Eip155))||"All account IDs must have non-EIP-155 namespaces.":"All account IDs must have the same address and namespace."})),_=(0,e.YP)(r.dg,"Non-EIP-155 Chain ID",(e=>{const{namespace:t}=(0,r.rW)(e);return t!==r.wV.Eip155||"Chain ID must not be an EIP-155 chain ID."})),i=(0,e.YP)(r.jx,"Non-EIP-155 Asset Type",(e=>{const{chain:{namespace:t}}=(0,r.Ts)(e);return t!==r.wV.Eip155||"Asset type must not be an EIP-155 asset type."}))}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7012:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{B:()=>a,G:()=>i});var e=__webpack_require__(2462),t=__webpack_require__(282),r=__webpack_require__(6231);const n=(_=t,_?.__esModule?_.default:_);var _;class i extends Error{constructor(t,n,_){if(!Number.isInteger(t))throw new Error('"code" must be an integer.');if(!n||"string"!=typeof n)throw new Error('"message" must be a non-empty string.');(0,r.el)(_)?(super(n,{cause:_.cause}),(0,e.i5)(this,"cause")||Object.assign(this,{cause:_.cause})):super(n),_!==undefined&&(this.data=_),this.code=t}serialize(){const t={code:this.code,message:this.message};return this.data!==undefined&&(t.data=this.data,(0,e.Qd)(this.data)&&(t.data.cause=(0,r._K)(this.data.cause))),this.stack&&(t.stack=this.stack),t}toString(){return n(this.serialize(),o,2)}}class a extends i{constructor(e,t,r){if(!function(e){return Number.isInteger(e)&&e>=1e3&&e<=4999}(e))throw new Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,r)}}function o(e,t){return"[Circular]"===t?undefined:t}}}).call(__webpack_require__._LM_("6",{__webpack_require__,__webpack_exports__}))()},7076:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{$:()=>t});var e=__webpack_require__(9612);function t(){return(t,r,n,_)=>{const i=t.id,a=(0,e.L)();t.id=a,r.id=a,n((e=>{t.id=i,r.id=i,e()}))}}}}).call(__webpack_require__._LM_("1",{__webpack_require__,__webpack_exports__}))()},7086:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!=typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),_=e[e.length-1];return"function"==typeof n&&n!==_&&Object.keys(_).forEach((function(e){n[e]=_[e]})),n}}}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},7123:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(2866);__webpack_require__(7996),undefined&&undefined.__rest,e.l}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},7131:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{s3:()=>o});var e,t,r=__webpack_require__(7566),n=__webpack_require__(4729),_=__webpack_require__(4980),i=__webpack_require__(6725),a=__webpack_require__(511);!function(e){e.Primary="primary",e.Secondary="secondary"}(e||(e={})),function(e){e.Button="button",e.Submit="submit"}(t||(t={}));const o=(0,r.kp)(a.Tl,(0,n.Ik)({type:(0,_.eu)(a.Z6.Button),value:(0,n.Yj)(),variant:(0,n.lq)((0,n.KC)([(0,_.$9)(e.Primary),(0,_.$9)(e.Secondary)])),buttonType:(0,n.lq)((0,n.KC)([(0,_.$9)(t.Button),(0,_.$9)(t.Submit)])),name:(0,n.lq)((0,n.Yj)())}));(0,i.I)(a.Z6.Button,o,["value","buttonType","name","variant"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7183:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(8854);module.exports=(t,r,n)=>e(t,r,"<",n)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7226:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(8854);module.exports=(t,r,n)=>e(t,r,">",n)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7229:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7476);module.exports=(t,r,n)=>{try{r=new e(r,n)}catch(e){return!1}return r.test(t)}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7250:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=1e3,t=60*e,r=60*t,n=24*r,_=7*n;function i(e,t,r,n){var _=t>=1.5*r;return Math.round(e/r)+" "+n+(_?"s":"")}module.exports=function(a,o){o=o||{};var s,u,c=typeof a;if("string"===c&&a.length>0)return function(i){if(!((i=String(i)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(a){var o=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*o;case"weeks":case"week":case"w":return o*_;case"days":case"day":case"d":return o*n;case"hours":case"hour":case"hrs":case"hr":case"h":return o*r;case"minutes":case"minute":case"mins":case"min":case"m":return o*t;case"seconds":case"second":case"secs":case"sec":case"s":return o*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return o;default:return undefined}}}}(a);if("number"===c&&isFinite(a))return o.long?(s=a,(u=Math.abs(s))>=n?i(s,u,n,"day"):u>=r?i(s,u,r,"hour"):u>=t?i(s,u,t,"minute"):u>=e?i(s,u,e,"second"):s+" ms"):function(_){var i=Math.abs(_);return i>=n?Math.round(_/n)+"d":i>=r?Math.round(_/r)+"h":i>=t?Math.round(_/t)+"m":i>=e?Math.round(_/e)+"s":_+"ms"}(a);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(a))}}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},7338:function(__unused_webpack_module,exports,__webpack_require__){(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.ObjectMultiplex=void 0;const t=__webpack_require__(4156),r=e(__webpack_require__(3358)),n=__webpack_require__(2565),_=Symbol("IGNORE_SUBSTREAM");class i extends t.Duplex{constructor(e={}){super(Object.assign({objectMode:!0},e)),this._substreams={}}createStream(e,_={}){if(this.destroyed)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already destroyed`);if(this._readableState.ended||this._writableState.ended)throw new Error(`ObjectMultiplex - parent stream for name "${e}" already ended`);if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);const i=new n.Substream(Object.assign({name:e,parent:this},_));return this._substreams[e]=i,function(e){const n=(0,r.default)((e=>i.destroy(e||undefined)));(0,t.finished)(e,{readable:!1},n),(0,t.finished)(e,{writable:!1},n)}(this),i}ignoreStream(e){if(!e)throw new Error("ObjectMultiplex - name must not be empty");if(this._substreams[e])throw new Error(`ObjectMultiplex - Substream for name "${e}" already exists`);this._substreams[e]=_}_read(){return undefined}_write(e,t,r){const{name:n,data:i}=e;if(!n)return console.warn(`ObjectMultiplex - malformed chunk without name "${e}"`),r();const a=this._substreams[n];return a?(a!==_&&a.push(i),r()):(console.warn(`ObjectMultiplex - orphaned data for stream "${n}"`),r())}}exports.ObjectMultiplex=i}}).call(__webpack_require__._LM_("3",{exports,__webpack_require__})).bind(exports)()},7346:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{z:()=>i});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(6725),_=__webpack_require__(511);const i=(0,e.kp)(_.Wu,(0,t.Ik)({type:(0,r.eu)(_.Z6.Divider)}));(0,n.I)(_.Z6.Divider,i)}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7347:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{V:()=>r,Z:()=>t});var e=__webpack_require__(6157);const t={[e.Z.OnRpcRequest]:{type:e.Z.OnRpcRequest,required:!0,validator:e=>"function"==typeof e},[e.Z.OnTransaction]:{type:e.Z.OnTransaction,required:!0,validator:e=>"function"==typeof e},[e.Z.OnCronjob]:{type:e.Z.OnCronjob,required:!0,validator:e=>"function"==typeof e},[e.Z.OnNameLookup]:{type:e.Z.OnNameLookup,required:!0,validator:e=>"function"==typeof e},[e.Z.OnInstall]:{type:e.Z.OnInstall,required:!1,validator:e=>"function"==typeof e},[e.Z.OnUpdate]:{type:e.Z.OnUpdate,required:!1,validator:e=>"function"==typeof e},[e.Z.OnStart]:{type:e.Z.OnStart,required:!1,validator:e=>"function"==typeof e},[e.Z.OnKeyringRequest]:{type:e.Z.OnKeyringRequest,required:!0,validator:e=>"function"==typeof e},[e.Z.OnHomePage]:{type:e.Z.OnHomePage,required:!0,validator:e=>"function"==typeof e},[e.Z.OnSettingsPage]:{type:e.Z.OnSettingsPage,required:!0,validator:e=>"function"==typeof e},[e.Z.OnSignature]:{type:e.Z.OnSignature,required:!0,validator:e=>"function"==typeof e},[e.Z.OnUserInput]:{type:e.Z.OnUserInput,required:!1,validator:e=>"function"==typeof e},[e.Z.OnAssetHistoricalPrice]:{type:e.Z.OnAssetHistoricalPrice,required:!0,validator:e=>"function"==typeof e},[e.Z.OnAssetsLookup]:{type:e.Z.OnAssetsLookup,required:!0,validator:e=>"function"==typeof e},[e.Z.OnAssetsConversion]:{type:e.Z.OnAssetsConversion,required:!0,validator:e=>"function"==typeof e},[e.Z.OnProtocolRequest]:{type:e.Z.OnProtocolRequest,required:!0,validator:e=>"function"==typeof e},[e.Z.OnClientRequest]:{type:e.Z.OnClientRequest,required:!0,validator:e=>"function"==typeof e},[e.Z.OnWebSocketEvent]:{type:e.Z.OnWebSocketEvent,required:!0,validator:e=>"function"==typeof e}},r=Object.values(e.Z)}}).call(__webpack_require__._LM_("9",{__webpack_require__,__webpack_exports__}))()},7403:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7476);module.exports=(t,r)=>{try{return new e(t,r).range||"*"}catch(e){return null}}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7476:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=/\s+/g;class t{constructor(r,i){if(i=n(i),r instanceof t)return r.loose===!!i.loose&&r.includePrerelease===!!i.includePrerelease?r:new t(r.raw,i);if(r instanceof _)return this.raw=r.value,this.set=[[r]],this.formatted=undefined,this;if(this.options=i,this.loose=!!i.loose,this.includePrerelease=!!i.includePrerelease,this.raw=r.trim().replace(e," "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!f(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&d(e[0])){this.set=[e];break}}this.formatted=undefined}get range(){if(this.formatted===undefined){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&l)|(this.options.loose&&h))+":"+e,n=r.get(t);if(n)return n;const a=this.options.loose,d=a?o[s.HYPHENRANGELOOSE]:o[s.HYPHENRANGE];e=e.replace(d,R(this.options.includePrerelease)),i("hyphen replace",e),e=e.replace(o[s.COMPARATORTRIM],u),i("comparator trim",e),e=e.replace(o[s.TILDETRIM],c),i("tilde trim",e),e=e.replace(o[s.CARETTRIM],p),i("caret trim",e);let w=e.split(" ").map((e=>b(e,this.options))).join(" ").split(/\s+/).map((e=>x(e,this.options)));a&&(w=w.filter((e=>(i("loose invalid filter",e,this.options),!!e.match(o[s.COMPARATORLOOSE]))))),i("range list",w);const k=new Map,m=w.map((e=>new _(e,this.options)));for(const e of m){if(f(e))return[e];k.set(e.value,e)}k.size>1&&k.has("")&&k.delete("");const g=[...k.values()];return r.set(t,g),g}intersects(e,r){if(!(e instanceof t))throw new TypeError("a Range is required");return this.set.some((t=>w(t,r)&&e.set.some((e=>w(e,r)&&t.every((t=>e.every((e=>t.intersects(e,r)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new a(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(T(this.set[t],e,this.options))return!0;return!1}}module.exports=t;const r=new(__webpack_require__(8953)),n=__webpack_require__(3990),_=__webpack_require__(1565),i=__webpack_require__(1361),a=__webpack_require__(4517),{safeRe:o,t:s,comparatorTrimReplace:u,tildeTrimReplace:c,caretTrimReplace:p}=__webpack_require__(2841),{FLAG_INCLUDE_PRERELEASE:l,FLAG_LOOSE:h}=__webpack_require__(9543),f=e=>"<0.0.0-0"===e.value,d=e=>""===e.value,w=(e,t)=>{let r=!0;const n=e.slice();let _=n.pop();for(;r&&n.length;)r=n.every((e=>_.intersects(e,t))),_=n.pop();return r},b=(e,t)=>(i("comp",e,t),e=y(e,t),i("caret",e),e=m(e,t),i("tildes",e),e=v(e,t),i("xrange",e),e=S(e,t),i("stars",e),e),k=e=>!e||"x"===e.toLowerCase()||"*"===e,m=(e,t)=>e.trim().split(/\s+/).map((e=>g(e,t))).join(" "),g=(e,t)=>{const r=t.loose?o[s.TILDELOOSE]:o[s.TILDE];return e.replace(r,((t,r,n,_,a)=>{let o;return i("tilde",e,t,r,n,_,a),k(r)?o="":k(n)?o=`>=${r}.0.0 <${+r+1}.0.0-0`:k(_)?o=`>=${r}.${n}.0 <${r}.${+n+1}.0-0`:a?(i("replaceTilde pr",a),o=`>=${r}.${n}.${_}-${a} <${r}.${+n+1}.0-0`):o=`>=${r}.${n}.${_} <${r}.${+n+1}.0-0`,i("tilde return",o),o}))},y=(e,t)=>e.trim().split(/\s+/).map((e=>q(e,t))).join(" "),q=(e,t)=>{i("caret",e,t);const r=t.loose?o[s.CARETLOOSE]:o[s.CARET],n=t.includePrerelease?"-0":"";return e.replace(r,((t,r,_,a,o)=>{let s;return i("caret",e,t,r,_,a,o),k(r)?s="":k(_)?s=`>=${r}.0.0${n} <${+r+1}.0.0-0`:k(a)?s="0"===r?`>=${r}.${_}.0${n} <${r}.${+_+1}.0-0`:`>=${r}.${_}.0${n} <${+r+1}.0.0-0`:o?(i("replaceCaret pr",o),s="0"===r?"0"===_?`>=${r}.${_}.${a}-${o} <${r}.${_}.${+a+1}-0`:`>=${r}.${_}.${a}-${o} <${r}.${+_+1}.0-0`:`>=${r}.${_}.${a}-${o} <${+r+1}.0.0-0`):(i("no pr"),s="0"===r?"0"===_?`>=${r}.${_}.${a}${n} <${r}.${_}.${+a+1}-0`:`>=${r}.${_}.${a}${n} <${r}.${+_+1}.0-0`:`>=${r}.${_}.${a} <${+r+1}.0.0-0`),i("caret return",s),s}))},v=(e,t)=>(i("replaceXRanges",e,t),e.split(/\s+/).map((e=>E(e,t))).join(" ")),E=(e,t)=>{e=e.trim();const r=t.loose?o[s.XRANGELOOSE]:o[s.XRANGE];return e.replace(r,((r,n,_,a,o,s)=>{i("xRange",e,r,n,_,a,o,s);const u=k(_),c=u||k(a),p=c||k(o),l=p;return"="===n&&l&&(n=""),s=t.includePrerelease?"-0":"",u?r=">"===n||"<"===n?"<0.0.0-0":"*":n&&l?(c&&(a=0),o=0,">"===n?(n=">=",c?(_=+_+1,a=0,o=0):(a=+a+1,o=0)):"<="===n&&(n="<",c?_=+_+1:a=+a+1),"<"===n&&(s="-0"),r=`${n+_}.${a}.${o}${s}`):c?r=`>=${_}.0.0${s} <${+_+1}.0.0-0`:p&&(r=`>=${_}.${a}.0${s} <${_}.${+a+1}.0-0`),i("xRange return",r),r}))},S=(e,t)=>(i("replaceStars",e,t),e.trim().replace(o[s.STAR],"")),x=(e,t)=>(i("replaceGTE0",e,t),e.trim().replace(o[t.includePrerelease?s.GTE0PRE:s.GTE0],"")),R=e=>(t,r,n,_,i,a,o,s,u,c,p,l)=>`${r=k(n)?"":k(_)?`>=${n}.0.0${e?"-0":""}`:k(i)?`>=${n}.${_}.0${e?"-0":""}`:a?`>=${r}`:`>=${r}${e?"-0":""}`} ${s=k(u)?"":k(c)?`<${+u+1}.0.0-0`:k(p)?`<${u}.${+c+1}.0-0`:l?`<=${u}.${c}.${p}-${l}`:e?`<${u}.${c}.${+p+1}-0`:`<=${s}`}`.trim(),T=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(i(e[r].semver),e[r].semver!==_.ANY&&e[r].semver.prerelease.length>0){const n=e[r].semver;if(n.major===t.major&&n.minor===t.minor&&n.patch===t.patch)return!0}return!1}return!0}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7555:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>e(r,t,n)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7566:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{E8:()=>n,RZ:()=>_,kp:()=>r});var e=__webpack_require__(6848),t=__webpack_require__(4729);function r(...e){const r="type"===e[0]?.type,n=e.map((({schema:e})=>e)),_=Object.assign({},...n);return r?(0,t.NW)(_):(0,t.Ik)(_)}function n(t,r){return new e._k({type:t,schema:null,validator:r})}function _(t){let r;return new e._k({type:"lazy",schema:null,*entries(e,n){r??(r=t()),yield*r.entries(e,n)},validator:(e,n)=>(r??(r=t()),r.validator(e,n)),coercer:(e,n)=>(r??(r=t()),r.coercer(e,n)),refiner:(e,n)=>(r??(r=t()),r.refiner(e,n))})}}}).call(__webpack_require__._LM_("undefined",{__webpack_require__,__webpack_exports__}))()},7569:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{W7:()=>s});var e=__webpack_require__(4729),t=__webpack_require__(7566),r=__webpack_require__(7131),n=__webpack_require__(6857),_=__webpack_require__(4980),i=__webpack_require__(6725),a=__webpack_require__(511);const o=(0,e.KC)([n.y9,r.s3]),s=(0,t.kp)(a.Wu,(0,e.Ik)({type:(0,_.eu)(a.Z6.Form),children:(0,e.YO)(o),name:(0,e.Yj)()}));(0,i.I)(a.Z6.Form,s,["name","children"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7640:(__unused_webpack___webpack_module__,__unused_webpack___webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__(3889)}}).call(__webpack_require__._LM_("undefined",{__webpack_require__}))()},7822:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{f:()=>e,z:()=>t});const e={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901}},t={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."}}}}).call(__webpack_require__._LM_("6",{__webpack_exports__,__webpack_require__}))()},7851:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r,n)=>new e(t,n).compare(new e(r,n))}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},7891:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=function(e){function t(e){let n,_,i,a=null;function o(...e){if(!o.enabled)return;const r=o,_=Number(new Date),i=_-(n||_);r.diff=i,r.prev=n,r.curr=_,n=_,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,_)=>{if("%%"===n)return"%";a++;const i=t.formatters[_];if("function"==typeof i){const t=e[a];n=i.call(r,t),e.splice(a,1),a--}return n})),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=r,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==a?a:(_!==t.namespaces&&(_=t.namespaces,i=t.enabled(e)),i),set:e=>{a=e}}),"function"==typeof t.init&&t.init(o),o}function r(e,r){const n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function n(e,t){let r=0,n=0,_=-1,i=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(_=n,i=r,n++):(r++,n++);else{if(-1===_)return!1;n=_+1,i++,r=i}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names,...t.skips.map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").trim().replace(" ",",").split(",").filter(Boolean);for(const e of r)"-"===e[0]?t.skips.push(e.slice(1)):t.names.push(e)},t.enabled=function(e){for(const r of t.skips)if(n(e,r))return!1;for(const r of t.names)if(n(e,r))return!0;return!1},t.humanize=__webpack_require__(7250),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((r=>{t[r]=e[r]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t),r|=0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}}}).call(__webpack_require__._LM_("12",{module,__webpack_require__}))()},7914:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{x:()=>a});var e=__webpack_require__(7566),t=__webpack_require__(4729),r=__webpack_require__(4980),n=__webpack_require__(4),_=__webpack_require__(6725),i=__webpack_require__(511);const a=(0,e.kp)(i.Wu,(0,t.Ik)({type:(0,r.eu)(i.Z6.Image),value:(0,n.J)()}));(0,_.I)(i.Z6.Image,a,["value"])}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7923:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{u1:()=>t}),__webpack_require__(7640);var e=__webpack_require__(2462);function t(t){return function(e){return"object"==typeof e&&null!==e&&"message"in e}(t)&&"string"==typeof t.message?t.message:(0,e.hX)(t)?"":String(t)}}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},7950:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>r});var e=__webpack_require__(46);function t(e,t,r){try{Reflect.apply(e,t,r)}catch(e){setTimeout((()=>{throw e}))}}class r extends e.EventEmitter{emit(e,...r){let n="error"===e;const _=this._events;if(_!==undefined)n=n&&_.error===undefined;else if(!n)return!1;if(n){let e;if(r.length>0&&([e]=r),e instanceof Error)throw e;const t=new Error("Unhandled error."+(e?` (${e.message})`:""));throw t.context=e,t}const i=_[e];if(i===undefined)return!1;if("function"==typeof i)t(i,this,r);else{const e=i.length,n=function(e){const t=e.length,r=new Array(t);for(let n=0;n<t;n+=1)r[n]=e[n];return r}(i);for(let _=0;_<e;_+=1)t(n[_],this,r)}return!0}}}}).call(__webpack_require__._LM_("7",{__webpack_require__,__webpack_exports__}))()},7991:(__unused_webpack_module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";exports.byteLength=function(e){var t=i(e),r=t[0],n=t[1];return 3*(r+n)/4-n},exports.toByteArray=function(e){var n,_,a=i(e),o=a[0],s=a[1],u=new r(function(e,t,r){return 3*(t+r)/4-r}(0,o,s)),c=0,p=s>0?o-4:o;for(_=0;_<p;_+=4)n=t[e.charCodeAt(_)]<<18|t[e.charCodeAt(_+1)]<<12|t[e.charCodeAt(_+2)]<<6|t[e.charCodeAt(_+3)],u[c++]=n>>16&255,u[c++]=n>>8&255,u[c++]=255&n;return 2===s&&(n=t[e.charCodeAt(_)]<<2|t[e.charCodeAt(_+1)]>>4,u[c++]=255&n),1===s&&(n=t[e.charCodeAt(_)]<<10|t[e.charCodeAt(_+1)]<<4|t[e.charCodeAt(_+2)]>>2,u[c++]=n>>8&255,u[c++]=255&n),u},exports.fromByteArray=function(t){for(var r,n=t.length,_=n%3,i=[],o=16383,s=0,u=n-_;s<u;s+=o)i.push(a(t,s,s+o>u?u:s+o));return 1===_?(r=t[n-1],i.push(e[r>>2]+e[r<<4&63]+"==")):2===_&&(r=(t[n-2]<<8)+t[n-1],i.push(e[r>>10]+e[r>>4&63]+e[r<<2&63]+"=")),i.join("")};for(var e=[],t=[],r="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_=0;_<64;++_)e[_]=n[_],t[n.charCodeAt(_)]=_;function i(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function a(t,r,n){for(var _,i,a=[],o=r;o<n;o+=3)_=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]),a.push(e[(i=_)>>18&63]+e[i>>12&63]+e[i>>6&63]+e[63&i]);return a.join("")}t["-".charCodeAt(0)]=62,t["_".charCodeAt(0)]=63}}).call(__webpack_require__._LM_("undefined",{exports,__webpack_require__}))()},7995:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A$:()=>n,N2:()=>i,fC:()=>a,iQ:()=>r,u1:()=>_});var e=__webpack_require__(2462),t=__webpack_require__(5259);const r=-31002,n="Snap Error";function _(t){return(0,e.Gv)(t)&&(0,e.i5)(t,"message")&&"string"==typeof t.message?t.message:String(t)}function i(t){return(0,e.Gv)(t)&&(0,e.i5)(t,"stack")&&"string"==typeof t.stack?t.stack:undefined}function a(r){return(0,e.Gv)(r)&&(0,e.i5)(r,"data")&&"object"==typeof r.data&&null!==r.data&&(0,t.jU)(r.data)&&!Array.isArray(r.data)?r.data:{}}}}).call(__webpack_require__._LM_("8",{__webpack_require__,__webpack_exports__}))()},7996:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{E:()=>r,z:()=>t});var e=__webpack_require__(2462);const t="dedicatedWorker";function r(t){return(0,e.Gv)(t)&&Boolean(t.data)&&("number"==typeof t.data||"object"==typeof t.data||"string"==typeof t.data)}}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},8199:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t=__webpack_require__(9907);module.exports=q,q.ReadableState=y,__webpack_require__(46).EventEmitter;var r,n=function(e,t){return e.listeners(t).length},_=__webpack_require__(4856),i=__webpack_require__(1048).Buffer,a=(void 0!==__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){},o=__webpack_require__(3951);r=o&&o.debuglog?o.debuglog("stream"):function(){};var s,u,c,p=__webpack_require__(82),l=__webpack_require__(6527),h=__webpack_require__(9952).getHighWaterMark,f=__webpack_require__(5699).F,d=f.ERR_INVALID_ARG_TYPE,w=f.ERR_STREAM_PUSH_AFTER_EOF,b=f.ERR_METHOD_NOT_IMPLEMENTED,k=f.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;__webpack_require__(5615)(q,_);var m=l.errorOrDestroy,g=["error","close","destroy","pause","resume"];function y(t,r,n){e=e||__webpack_require__(1265),t=t||{},"boolean"!=typeof n&&(n=r instanceof e),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=h(this,t,"readableHighWaterMark",n),this.buffer=new p,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(s||(s=__webpack_require__(8888).I),this.decoder=new s(t.encoding),this.encoding=t.encoding)}function q(t){if(e=e||__webpack_require__(1265),!(this instanceof q))return new q(t);var r=this instanceof e;this._readableState=new y(t,this,r),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),_.call(this)}function v(e,t,n,_,o){r("readableAddChunk",t);var s,u=e._readableState;if(null===t)u.reading=!1,function(e,t){if(r("onEofChunk"),!t.ended){if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,t.sync?R(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,T(e)))}}(e,u);else if(o||(s=function(e,t){var r,n;return n=t,i.isBuffer(n)||n instanceof a||"string"==typeof t||t===undefined||e.objectMode||(r=new d("chunk",["string","Buffer","Uint8Array"],t)),r}(u,t)),s)m(e,s);else if(u.objectMode||t&&t.length>0)if("string"==typeof t||u.objectMode||Object.getPrototypeOf(t)===i.prototype||(t=function(e){return i.from(e)}(t)),_)u.endEmitted?m(e,new k):E(e,u,t,!0);else if(u.ended)m(e,new w);else{if(u.destroyed)return!1;u.reading=!1,u.decoder&&!n?(t=u.decoder.write(t),u.objectMode||0!==t.length?E(e,u,t,!1):M(e,u)):E(e,u,t,!1)}else _||(u.reading=!1,M(e,u));return!u.ended&&(u.length<u.highWaterMark||0===u.length)}function E(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&R(e)),M(e,t)}Object.defineProperty(q.prototype,"destroyed",{enumerable:!1,get:function(){return this._readableState!==undefined&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),q.prototype.destroy=l.destroy,q.prototype._undestroy=l.undestroy,q.prototype._destroy=function(e,t){t(e)},q.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=i.from(e,t),t=""),r=!0),v(this,e,t,!1,r)},q.prototype.unshift=function(e){return v(this,e,null,!0,!1)},q.prototype.isPaused=function(){return!1===this._readableState.flowing},q.prototype.setEncoding=function(e){s||(s=__webpack_require__(8888).I);var t=new s(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,n="";null!==r;)n+=t.write(r.data),r=r.next;return this._readableState.buffer.clear(),""!==n&&this._readableState.buffer.push(n),this._readableState.length=n.length,this};var S=1073741824;function x(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=S?e=S:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function R(e){var n=e._readableState;r("emitReadable",n.needReadable,n.emittedReadable),n.needReadable=!1,n.emittedReadable||(r("emitReadable",n.flowing),n.emittedReadable=!0,t.nextTick(T,e))}function T(e){var t=e._readableState;r("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,C(e)}function M(e,r){r.readingMore||(r.readingMore=!0,t.nextTick(O,e,r))}function O(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var n=t.length;if(r("maybeReadMore read 0"),e.read(0),n===t.length)break}t.readingMore=!1}function L(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function A(e){r("readable nexttick read 0"),e.read(0)}function I(e,t){r("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),C(e),t.flowing&&!t.reading&&e.read(0)}function C(e){var t=e._readableState;for(r("flow",t.flowing);t.flowing&&null!==e.read(););}function j(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r);var r}function P(e){var n=e._readableState;r("endReadable",n.endEmitted),n.endEmitted||(n.ended=!0,t.nextTick($,n,e))}function $(e,t){if(r("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var n=t._writableState;(!n||n.autoDestroy&&n.finished)&&t.destroy()}}function N(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}q.prototype.read=function(e){r("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return r("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?P(this):R(this),null;if(0===(e=x(e,t))&&t.ended)return 0===t.length&&P(this),null;var _,i=t.needReadable;return r("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&r("length less than watermark",i=!0),t.ended||t.reading?r("reading or ended",i=!1):i&&(r("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=x(n,t))),null===(_=e>0?j(e,t):null)?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&P(this)),null!==_&&this.emit("data",_),_},q.prototype._read=function(e){m(this,new b("_read()"))},q.prototype.pipe=function(e,_){var i=this,a=this._readableState;switch(a.pipesCount){case 0:a.pipes=e;break;case 1:a.pipes=[a.pipes,e];break;default:a.pipes.push(e)}a.pipesCount+=1,r("pipe count=%d opts=%j",a.pipesCount,_);var o=_&&!1===_.end||e===t.stdout||e===t.stderr?d:s;function s(){r("onend"),e.end()}a.endEmitted?t.nextTick(o):i.once("end",o),e.on("unpipe",(function t(n,_){r("onunpipe"),n===i&&_&&!1===_.hasUnpiped&&(_.hasUnpiped=!0,r("cleanup"),e.removeListener("close",h),e.removeListener("finish",f),e.removeListener("drain",u),e.removeListener("error",l),e.removeListener("unpipe",t),i.removeListener("end",s),i.removeListener("end",d),i.removeListener("data",p),c=!0,!a.awaitDrain||e._writableState&&!e._writableState.needDrain||u())}));var u=function(e){return function(){var t=e._readableState;r("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&n(e,"data")&&(t.flowing=!0,C(e))}}(i);e.on("drain",u);var c=!1;function p(t){r("ondata");var n=e.write(t);r("dest.write",n),!1===n&&((1===a.pipesCount&&a.pipes===e||a.pipesCount>1&&-1!==N(a.pipes,e))&&!c&&(r("false write response, pause",a.awaitDrain),a.awaitDrain++),i.pause())}function l(t){r("onerror",t),d(),e.removeListener("error",l),0===n(e,"error")&&m(e,t)}function h(){e.removeListener("finish",f),d()}function f(){r("onfinish"),e.removeListener("close",h),d()}function d(){r("unpipe"),i.unpipe(e)}return i.on("data",p),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",l),e.once("close",h),e.once("finish",f),e.emit("pipe",i),a.flowing||(r("pipe resume"),i.resume()),e},q.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,_=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<_;i++)n[i].emit("unpipe",this,{hasUnpiped:!1});return this}var a=N(t.pipes,e);return-1===a||(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},q.prototype.on=function(e,n){var i=_.prototype.on.call(this,e,n),a=this._readableState;return"data"===e?(a.readableListening=this.listenerCount("readable")>0,!1!==a.flowing&&this.resume()):"readable"===e&&(a.endEmitted||a.readableListening||(a.readableListening=a.needReadable=!0,a.flowing=!1,a.emittedReadable=!1,r("on readable",a.length,a.reading),a.length?R(this):a.reading||t.nextTick(A,this))),i},q.prototype.addListener=q.prototype.on,q.prototype.removeListener=function(e,r){var n=_.prototype.removeListener.call(this,e,r);return"readable"===e&&t.nextTick(L,this),n},q.prototype.removeAllListeners=function(e){var r=_.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&e!==undefined||t.nextTick(L,this),r},q.prototype.resume=function(){var e=this._readableState;return e.flowing||(r("resume"),e.flowing=!e.readableListening,function(e,r){r.resumeScheduled||(r.resumeScheduled=!0,t.nextTick(I,e,r))}(this,e)),e.paused=!1,this},q.prototype.pause=function(){return r("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(r("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},q.prototype.wrap=function(e){var t=this,n=this._readableState,_=!1;for(var i in e.on("end",(function(){if(r("wrapped end"),n.decoder&&!n.ended){var e=n.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(i){r("wrapped data"),n.decoder&&(i=n.decoder.write(i)),(!n.objectMode||null!==i&&i!==undefined)&&(n.objectMode||i&&i.length)&&(t.push(i)||(_=!0,e.pause()))})),e)this[i]===undefined&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var a=0;a<g.length;a++)e.on(g[a],this.emit.bind(this,g[a]));return this._read=function(t){r("wrapped _read",t),_&&(_=!1,e.resume())},this},"function"==typeof Symbol&&(q.prototype[Symbol.asyncIterator]=function(){return u===undefined&&(u=__webpack_require__(534)),u(this)}),Object.defineProperty(q.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(q.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(q.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),q._fromList=j,Object.defineProperty(q.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(q.from=function(e,t){return c===undefined&&(c=__webpack_require__(1260)),c(q,e,t)})}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},8220:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>0!==e(t,r,n)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8323:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{FF:()=>n,K1:()=>e,fH:()=>t,vV:()=>r});const e=(0,__webpack_require__(2869).u)("snaps");function t(e,...t){console.log(e,...t)}function r(e,...t){console.error(e,...t)}function n(e,...t){console.warn(e,...t)}}}).call(__webpack_require__._LM_("9",{__webpack_require__,__webpack_exports__}))()},8464:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Ej:()=>n,T1:()=>r,YP:()=>_});var e=__webpack_require__(6848),t=__webpack_require__(4394);function r(e,t){return _(e,"pattern",(r=>t.test(r)||`Expected a ${e.type} matching \`/${t.source}/\` but received "${r}"`))}function n(e,t,r=t){const n=`Expected a ${e.type}`,i=t===r?`of \`${t}\``:`between \`${t}\` and \`${r}\``;return _(e,"size",(e=>{if("number"==typeof e||e instanceof Date)return t<=e&&e<=r||`${n} ${i} but received \`${e}\``;if(e instanceof Map||e instanceof Set){const{size:_}=e;return t<=_&&_<=r||`${n} with a size ${i} but received one with a size of \`${_}\``}const{length:_}=e;return t<=_&&_<=r||`${n} with a length ${i} but received one with a length of \`${_}\``}))}function _(r,n,_){return new e._k({...r,*refiner(e,i){yield*r.refiner(e,i);const a=_(e,i),o=(0,t.RF)(a,i,r,e);for(const e of o)yield{...e,refinement:n}}})}}}).call(__webpack_require__._LM_("undefined",{__webpack_require__,__webpack_exports__}))()},8474:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(3955);module.exports=(t,r)=>{const n=e(t,r);return n?n.version:null}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8546:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";function e(e){return Object.fromEntries(Object.entries(e).filter((([,e])=>e!==undefined)))}function t(t){return r=>{const{key:n=null,..._}=r;return{type:t,props:e(_),key:n}}}__webpack_require__.d(__webpack_exports__,{K:()=>t})}}).call(__webpack_require__._LM_("8",{__webpack_exports__,__webpack_require__}))()},8623:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7476);module.exports=(t,r,n)=>(t=new e(t,n),r=new e(r,n),t.intersects(r,n))}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8662:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{j:()=>x});var e=__webpack_require__(7076),t=__webpack_require__(5875),r=__webpack_require__.n(t),n=__webpack_require__(7822),_=__webpack_require__(845),i=__webpack_require__(6231),a=__webpack_require__(7995),o=__webpack_require__(8323),s=__webpack_require__(7347),u=__webpack_require__(3349),c=__webpack_require__(6848),p=__webpack_require__(9400),l=__webpack_require__(5259),h=__webpack_require__(2462),f=__webpack_require__(4156),d=__webpack_require__(5316),w=__webpack_require__(2457),b=__webpack_require__(9860),k=__webpack_require__(3071),m=__webpack_require__(1964),g=__webpack_require__(1417),y=__webpack_require__(2945),q=__webpack_require__(949);const v={code:n.f.rpc.internal,message:"Execution Environment Error"},E=_.r.internal({message:"Unhandled Snap Error"}).serialize(),S={ping:{struct:y.bi,params:[]},executeSnap:{struct:y.A4,params:["snapId","sourceCode","endowments"]},terminate:{struct:y.UE,params:[]},snapRpc:{struct:y.it,params:["target","handler","origin","request"]}};class x{snapData;commandStream;rpcStream;methods;snapErrorHandler;snapPromiseErrorHandler;lastTeardown=0;constructor(e,t){this.snapData=new Map,this.commandStream=e,this.commandStream.on("data",(e=>{this.onCommandRequest(e).catch((e=>{(0,o.vV)(e)}))})),this.rpcStream=t,this.methods=(0,d.a)(this.startSnap.bind(this),(async(e,t,r)=>{const n=this.snapData.get(e),i=n?.exports[t],{required:a}=s.Z[t];if((0,p.vA)(!a||i!==undefined,`No ${t} handler exported for snap "${e}`,_.r.methodNotSupported),!i)return null;const o=await this.executeInSnapContext(e,(async()=>i(r)));if(o===undefined||null===o)return null;try{return(0,l.SS)(o)}catch(e){throw _.r.internal(`Received non-JSON-serializable value: ${e.message.replace(/^Assertion failed: /u,"")}`)}}),this.onTerminate.bind(this))}errorHandler(e,t){const r=(0,i.P5)(e,{fallbackError:E,shouldIncludeStack:!1,shouldPreserveMessage:!1}),n=(0,a.fC)(r);this.#n({method:"UnhandledError",params:{error:{...r,data:{...n,...t}}}}).catch((e=>{(0,o.vV)(e)}))}async onCommandRequest(e){if(!(0,l.p3)(e))return void((0,h.i5)(e,"id")&&(0,c.is)(e.id,l.SZ)?await this.#_({error:(0,i.P5)(_.r.internal("JSON-RPC requests must be JSON serializable objects.")),id:e.id,jsonrpc:"2.0"}):(0,o.fH)("Command stream received a non-JSON-RPC request, and was unable to respond."));const{id:t,method:r,params:n}=e;if(!(0,h.i5)(S,r))return void await this.#i(t,{error:_.r.methodNotFound({data:{method:r}}).serialize()});const a=S[r],s=(0,m.c)(a.params,n),[u]=(0,c.tf)(s,a.struct);if(u)await this.#i(t,{error:_.r.invalidParams({message:`Invalid parameters for method "${r}": ${u.message}.`,data:{method:r,params:s}}).serialize()});else try{const e=await this.methods[r](...s);await this.#i(t,{result:e})}catch(e){await this.#i(t,{error:(0,i.P5)(e,{fallbackError:v,shouldPreserveMessage:!1})})}}async#_(e){return new Promise(((t,r)=>{this.commandStream.write(e,(e=>{e?r(e):t()}))}))}async#n(e){if(!(0,g.M8)(e))throw _.r.internal("JSON-RPC notifications must be JSON serializable objects smaller than 64 MB.");await this.#_({...e,jsonrpc:"2.0"})}async#i(e,t){(0,g.M8)(t)?await this.#_({...t,id:e,jsonrpc:"2.0"}):await this.#_({error:(0,i.P5)(_.r.internal("JSON-RPC responses must be JSON serializable objects smaller than 64 MB.")),id:e,jsonrpc:"2.0"})}async startSnap(t,n,i){(0,q.R)(`Starting snap '${t}' in worker.`),this.snapPromiseErrorHandler&&(0,b.f)("unhandledrejection",this.snapPromiseErrorHandler),this.snapErrorHandler&&(0,b.f)("error",this.snapErrorHandler),this.snapErrorHandler=e=>{this.errorHandler(e.error,{snapId:t})},this.snapPromiseErrorHandler=e=>{this.errorHandler(e instanceof Error?e:e.reason,{snapId:t})};const a=new(r());(0,f.pipeline)(this.rpcStream,a,this.rpcStream,(e=>{e&&(0,o.vV)("Provider stream failure.",e)}));const s=new k.M(a.createStream("metamask-provider"),{rpcMiddleware:[(0,e.$)()]});s.initializeSync();const c=this.createSnapGlobal(s),p=this.createEIP1193Provider(s),l={exports:{}};try{const{endowments:e,teardown:r}=(0,w.f)({snap:c,ethereum:p,snapId:t,endowments:i,notify:this.#n.bind(this)});this.snapData.set(t,{idleTeardown:r,runningEvaluations:new Set,exports:{}}),(0,b.q)("unhandledRejection",this.snapPromiseErrorHandler),(0,b.q)("error",this.snapErrorHandler);const _=new Compartment({...e,module:l,exports:l.exports});_.globalThis.self=_.globalThis,_.globalThis.global=_.globalThis,_.globalThis.window=_.globalThis,await this.executeInSnapContext(t,(async()=>{_.evaluate(n),await this.registerSnapExports(t,l)}))}catch(e){this.removeSnap(t);const[r]=(0,u.ox)(e);throw _.r.internal({message:`Error while running snap '${t}': ${r.message}`,data:{cause:r.serialize()}})}}onTerminate(){this.snapData.forEach((e=>e.runningEvaluations.forEach((e=>e.stop())))),this.snapData.clear()}async registerSnapExports(e,t){const r=this.snapData.get(e);if(!r)return;const n=await t.exports;r.exports=s.V.reduce(((e,t)=>{const r=n[t],{validator:_}=s.Z[t];return _(r)?{...e,[t]:r}:e}),{}),(0,p.vA)(Object.keys(r.exports).length>0,"Snap has no valid exports.")}createSnapGlobal(e){const t=e.request.bind(e);return harden({request:async e=>{const r=(0,g.fR)(e);return(0,g.gL)(r),await(0,g.R2)((async()=>{try{const e=t(r);return await this.#n({method:"OutboundRequest",params:{source:"snap.request"}}),await e}finally{await this.#n({method:"OutboundResponse",params:{source:"snap.request"}})}})(),this)}})}createEIP1193Provider(e){const t=e.request.bind(e);return harden({request:async e=>{const r=(0,g.fR)(e);return(0,g.Xe)(r),await(0,g.R2)((async()=>{try{const e=t(r);return await this.#n({method:"OutboundRequest",params:{source:"ethereum.request"}}),await e}finally{await this.#n({method:"OutboundResponse",params:{source:"ethereum.request"}})}})(),this)}})}removeSnap(e){this.snapData.delete(e)}async executeInSnapContext(e,t){const r=this.snapData.get(e);if(r===undefined)throw _.r.internal(`Tried to execute in context of unknown snap: "${e}".`);let n;const i=new Promise(((t,r)=>n=()=>r(_.r.internal(`The snap "${e}" has been terminated during execution.`)))),a={stop:n};try{return r.runningEvaluations.add(a),await Promise.race([t(),i])}catch(e){throw new u.sZ(e)}finally{r.runningEvaluations.delete(a),0===r.runningEvaluations.size&&(this.lastTeardown+=1,await r.idleTeardown())}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},8746:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e;let t,r;if(__webpack_require__.d(__webpack_exports__,{h:()=>n}),function(e){e.globalThis="globalThis",e.global="global",e.self="self",e.window="window"}(e||(e={})),"undefined"!=typeof globalThis)t=globalThis,r=e.globalThis;else if("undefined"!=typeof self)t=self,r=e.self;else if("undefined"!=typeof window)t=window,r=e.window;else{if(void 0===__webpack_require__.g)throw new Error("Unknown realm type; failed to identify global object.");t=__webpack_require__.g,r=e.global}const n=t}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},8792:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=function e(t,r){if(t===r)return!0;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,_,i;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(_=n;0!=_--;)if(!e(t[_],r[_]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(_=n;0!=_--;)if(!Object.prototype.hasOwnProperty.call(r,i[_]))return!1;for(_=n;0!=_--;){var a=i[_];if(!e(t[a],r[a]))return!1}return!0}return t!=t&&r!=r}}}).call(__webpack_require__._LM_("undefined",{module,__webpack_require__}))()},8848:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>0===e(t,r,n)}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8854:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517),t=__webpack_require__(1565),{ANY:r}=t,n=__webpack_require__(7476),_=__webpack_require__(7229),i=__webpack_require__(9761),a=__webpack_require__(1262),o=__webpack_require__(9639),s=__webpack_require__(2386);module.exports=(u,c,p,l)=>{let h,f,d,w,b;switch(u=new e(u,l),c=new n(c,l),p){case">":h=i,f=o,d=a,w=">",b=">=";break;case"<":h=a,f=s,d=i,w="<",b="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(_(u,c,l))return!1;for(let e=0;e<c.set.length;++e){const n=c.set[e];let _=null,i=null;if(n.forEach((e=>{e.semver===r&&(e=new t(">=0.0.0")),_=_||e,i=i||e,h(e.semver,_.semver,l)?_=e:d(e.semver,i.semver,l)&&(i=e)})),_.operator===w||_.operator===b)return!1;if((!i.operator||i.operator===w)&&f(u,i.semver))return!1;if(i.operator===b&&d(u,i.semver))return!1}return!0}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8868:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(4517);module.exports=(t,r,n,_,i)=>{"string"==typeof n&&(i=_,_=n,n=undefined);try{return new e(t instanceof e?t.version:t,n).inc(r,_,i).version}catch(e){return null}}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},8888:(__unused_webpack_module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(5636).Buffer,t=e.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function r(r){var n;switch(this.encoding=function(r){var n=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(r);if("string"!=typeof n&&(e.isEncoding===t||!t(r)))throw new Error("Unknown encoding: "+r);return n||r}(r),this.encoding){case"utf16le":this.text=i,this.end=a,n=4;break;case"utf8":this.fillLast=_,n=4;break;case"base64":this.text=o,this.end=s,n=3;break;default:return this.write=u,void(this.end=c)}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(n)}function n(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function _(e){var t=this.lastTotal-this.lastNeed,r=function(e,t){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return r!==undefined?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function i(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function a(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function o(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function s(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function u(e){return e.toString(this.encoding)}function c(e){return e&&e.length?this.write(e):""}exports.I=r,r.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if((t=this.fillLast(e))===undefined)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},r.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},r.prototype.text=function(e,t){var r=function(e,t,r){var _=t.length-1;if(_<r)return 0;var i=n(t[_]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--_<r||-2===i?0:(i=n(t[_]))>=0?(i>0&&(e.lastNeed=i-2),i):--_<r||-2===i?0:(i=n(t[_]))>=0?(i>0&&(2===i?i=0:e.lastNeed=i-3),i):0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var _=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,_),e.toString("utf8",t,_)},r.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}}}).call(__webpack_require__._LM_("19",{__webpack_require__,exports}))()},8953:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return t===undefined?undefined:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&t!==undefined){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},9272:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>e});const e={names:["TextEncoder"],factory:()=>({TextEncoder:harden(TextEncoder)})}}}).call(__webpack_require__._LM_("0",{__webpack_exports__,__webpack_require__}))()},9306:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Jx:()=>n,Xi:()=>_,a:()=>t,m4:()=>r});const e=/^(\d*[1-9]\d*|0)$/u,t=Object.freeze(["eth_subscription"]),r=(e,t,r=!0)=>(n,_)=>{n||_.error?t(n||_.error):!r||Array.isArray(_)?e(_):e(_.result)},n=e=>Boolean(e)&&"string"==typeof e&&e.startsWith("0x"),_=t=>"string"==typeof t&&(e.test(t)||"loading"===t)}}).call(__webpack_require__._LM_("5",{__webpack_exports__,__webpack_require__}))()},9318:(__unused_webpack_module,exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";exports.read=function(e,t,r,n,_){var i,a,o=8*_-n-1,s=(1<<o)-1,u=s>>1,c=-7,p=r?_-1:0,l=r?-1:1,h=e[t+p];for(p+=l,i=h&(1<<-c)-1,h>>=-c,c+=o;c>0;i=256*i+e[t+p],p+=l,c-=8);for(a=i&(1<<-c)-1,i>>=-c,c+=n;c>0;a=256*a+e[t+p],p+=l,c-=8);if(0===i)i=1-u;else{if(i===s)return a?NaN:(h?-1:1)*Infinity;a+=Math.pow(2,n),i-=u}return(h?-1:1)*a*Math.pow(2,i-n)},exports.write=function(e,t,r,n,_,i){var a,o,s,u=8*i-_-1,c=(1<<u)-1,p=c>>1,l=23===_?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:i-1,f=n?1:-1,d=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===Infinity?(o=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(s=Math.pow(2,-a))<1&&(a--,s*=2),(t+=a+p>=1?l/s:l*Math.pow(2,1-p))*s>=2&&(a++,s/=2),a+p>=c?(o=0,a=c):a+p>=1?(o=(t*s-1)*Math.pow(2,_),a+=p):(o=t*Math.pow(2,p-1)*Math.pow(2,_),a=0));_>=8;e[r+h]=255&o,h+=f,o/=256,_-=8);for(a=a<<_|o,u+=_;u>0;e[r+h]=255&a,h+=f,a/=256,u-=8);e[r+h-f]|=128*d}}}).call(__webpack_require__._LM_("undefined",{exports,__webpack_require__}))()},9400:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{Bd:()=>a,ut:()=>i,vA:()=>_});var e=__webpack_require__(6848),t=__webpack_require__(7923);function r(e,t){return r=e,Boolean("string"==typeof r?.prototype?.constructor?.name)?new e({message:t}):e({message:t});var r}class n extends Error{constructor(e){super(e.message),this.code="ERR_ASSERTION"}}function _(e,t="Assertion failed.",_=n){if(!e){if(t instanceof Error)throw t;throw r(_,t)}}function i(_,i,a="Assertion failed",o=n){try{(0,e.vA)(_,i)}catch(e){throw r(o,`${a}: ${function(e){return(0,t.u1)(e).replace(/\.$/u,"")}(e)}.`)}}function a(e){throw new Error("Invalid branch reached. Should be detected during compilation.")}}}).call(__webpack_require__._LM_("10",{__webpack_require__,__webpack_exports__}))()},9415:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";module.exports=o;var e=__webpack_require__(5699).F,t=e.ERR_METHOD_NOT_IMPLEMENTED,r=e.ERR_MULTIPLE_CALLBACK,n=e.ERR_TRANSFORM_ALREADY_TRANSFORMING,_=e.ERR_TRANSFORM_WITH_LENGTH_0,i=__webpack_require__(1265);function a(e,t){var n=this._transformState;n.transforming=!1;var _=n.writecb;if(null===_)return this.emit("error",new r);n.writechunk=null,n.writecb=null,null!=t&&this.push(t),_(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function o(e){if(!(this instanceof o))return new o(e);i.call(this,e),this._transformState={afterTransform:a.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",s)}function s(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?u(this,null,null):this._flush((function(t,r){u(e,t,r)}))}function u(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new _;if(e._transformState.transforming)throw new n;return e.push(null)}__webpack_require__(5615)(o,i),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,i.prototype.push.call(this,e,t)},o.prototype._transform=function(e,r,n){n(new t("_transform()"))},o.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var _=this._readableState;(n.needTransform||_.needReadable||_.length<_.highWaterMark)&&this._read(_.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},o.prototype._destroy=function(e,t){i.prototype._destroy.call(this,e,(function(e){t(e)}))}}}).call(__webpack_require__._LM_("16",{module,__webpack_require__}))()},9428:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(3955);module.exports=(t,r)=>{const n=e(t,r);return n&&n.prerelease.length?n.prerelease:null}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},9543:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=Number.MAX_SAFE_INTEGER||9007199254740991;module.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:e,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},9563:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{A:()=>_});var e=__webpack_require__(9400),t=__webpack_require__(1417);class r{#a;#o;#s;#u;constructor(e,t,r,n){this.#o=e,this.#a=t,this.#s=r,this.#u=n}get body(){return this.#o.body}get bodyUsed(){return this.#o.bodyUsed}get headers(){return this.#o.headers}get ok(){return this.#o.ok}get redirected(){return this.#o.redirected}get status(){return this.#o.status}get statusText(){return this.#o.statusText}get type(){return this.#o.type}get url(){return this.#o.url}async text(){return await(0,t.R2)((async()=>{await this.#s();try{return await this.#o.text()}finally{await this.#u()}})(),this.#a)}async arrayBuffer(){return await(0,t.R2)((async()=>{await this.#s();try{return await this.#o.arrayBuffer()}finally{await this.#u()}})(),this.#a)}async blob(){return await(0,t.R2)((async()=>{await this.#s();try{return await this.#o.blob()}finally{await this.#u()}})(),this.#a)}clone(){const e=this.#o.clone();return new r(e,this.#a,this.#s,this.#u)}async formData(){return await(0,t.R2)((async()=>{await this.#s();try{return await this.#o.formData()}finally{await this.#u()}})(),this.#a)}async json(){return await(0,t.R2)((async()=>{await this.#s();try{return await this.#o.json()}finally{await this.#u()}})(),this.#a)}}class n extends Response{static[Symbol.hasInstance](e){return e instanceof Response||e instanceof r}}const _={names:["fetch","Request","Headers","Response"],factory:({notify:_}={})=>{(0,e.vA)(_,"Notify must be passed to network endowment factory");const i=new Set,a={lastTeardown:0},o=new FinalizationRegistry((e=>e()));return{fetch:harden((async(e,n)=>{const s=new AbortController;if(null!==n?.signal&&n?.signal!==undefined){const e=n.signal;e.addEventListener("abort",(()=>{s.abort(e.reason)}),{once:!0})}let u=!1;const c=async()=>{u||(u=!0,await _({method:"OutboundRequest",params:{source:"fetch"}}))};let p=!1;const l=async()=>{p||(p=!0,await _({method:"OutboundResponse",params:{source:"fetch"}}))};let h,f;return await(0,t.R2)((async()=>{try{const t=fetch(e,{...n,signal:s.signal});await _({method:"OutboundRequest",params:{source:"fetch"}}),f={cancel:async()=>{s.abort();try{await t}catch{}}},i.add(f),h=new r(await t,a,c,l)}finally{f!==undefined&&i.delete(f),await _({method:"OutboundResponse",params:{source:"fetch"}})}if(null!==h.body){const e=new WeakRef(h.body),t={cancel:async()=>{try{await(e.deref()?.cancel())}catch{}}};i.add(t),o.register(h.body,(()=>i.delete(t)))}return harden(h)})(),a)})),Request:harden(Request),Headers:harden(Headers),Response:harden(n),teardownFunction:async()=>{a.lastTeardown+=1;const e=[];i.forEach((({cancel:t})=>e.push(t()))),i.clear(),await Promise.all(e)}}}}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},9612:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{L:()=>r});const e=4294967295;let t=Math.floor(Math.random()*e);function r(){return t=(t+1)%e,t}}}).call(__webpack_require__._LM_("1",{__webpack_exports__,__webpack_require__}))()},9639:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>e(t,r,n)<=0}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},9761:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";const e=__webpack_require__(7851);module.exports=(t,r,n)=>e(t,r,n)>0}}).call(__webpack_require__._LM_("18",{module,__webpack_require__}))()},9860:(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{f:()=>n,q:()=>r});var e=__webpack_require__(845),t=__webpack_require__(8746);function r(r,n){if("addEventListener"in t.h&&"function"==typeof t.h.addEventListener)return t.h.addEventListener(r.toLowerCase(),n);if(t.h.process&&"on"in t.h.process&&"function"==typeof t.h.process.on)return t.h.process.on(r,n);throw e.r.internal("Platform agnostic addEventListener failed.")}function n(e,r){if("removeEventListener"in t.h&&"function"==typeof t.h.removeEventListener)return t.h.removeEventListener(e.toLowerCase(),r);if(t.h.process&&"removeListener"in t.h.process&&"function"==typeof t.h.process.removeListener)return t.h.process.removeListener(e,r);throw new Error("Platform agnostic removeEventListener failed")}}}).call(__webpack_require__._LM_("0",{__webpack_require__,__webpack_exports__}))()},9886:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(2866);__webpack_require__(7996),undefined&&undefined.__classPrivateFieldSet,undefined&&undefined.__classPrivateFieldGet,undefined&&undefined.__rest,e.l,new WeakMap,new WeakMap}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},9907:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e,t,r=module.exports={};function n(){throw new Error("setTimeout has not been defined")}function _(){throw new Error("clearTimeout has not been defined")}function i(t){if(e===setTimeout)return setTimeout(t,0);if((e===n||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(r){try{return e.call(null,t,0)}catch(r){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:n}catch(t){e=n}try{t="function"==typeof clearTimeout?clearTimeout:_}catch(e){t=_}}();var a,o=[],s=!1,u=-1;function c(){s&&a&&(s=!1,a.length?o=a.concat(o):u=-1,o.length&&p())}function p(){if(!s){var e=i(c);s=!0;for(var r=o.length;r;){for(a=o,o=[];++u<r;)a&&a[u].run();u=-1,r=o.length}a=null,s=!1,function(e){if(t===clearTimeout)return clearTimeout(e);if((t===_||!t)&&clearTimeout)return t=clearTimeout,clearTimeout(e);try{return t(e)}catch(r){try{return t.call(null,e)}catch(r){return t.call(this,e)}}}(e)}}function l(e,t){this.fun=e,this.array=t}function h(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];o.push(new l(e,t)),1!==o.length||s||i(p)},l.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=h,r.addListener=h,r.once=h,r.off=h,r.removeListener=h,r.removeAllListeners=h,r.emit=h,r.prependListener=h,r.prependOnceListener=h,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}}}).call(__webpack_require__._LM_("15",{module,__webpack_require__}))()},9950:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";__webpack_require__.d(__webpack_exports__,{U:()=>s});var e,t,r=__webpack_require__(9400),n=__webpack_require__(2866),_=__webpack_require__(7996),i=undefined&&undefined.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var _=0;for(n=Object.getOwnPropertySymbols(e);_<n.length;_++)t.indexOf(n[_])<0&&Object.prototype.propertyIsEnumerable.call(e,n[_])&&(r[n[_]]=e[n[_]])}return r};const a=null===(e=Object.getOwnPropertyDescriptor(MessageEvent.prototype,"source"))||void 0===e?void 0:e.get;(0,r.vA)(a,"MessageEvent.prototype.source getter is not defined.");const o=null===(t=Object.getOwnPropertyDescriptor(MessageEvent.prototype,"origin"))||void 0===t?void 0:t.get;(0,r.vA)(o,"MessageEvent.prototype.origin getter is not defined.");class s extends n.l{constructor(e){var{name:t,target:r,targetOrigin:n=location.origin,targetWindow:_=window}=e;if(super(i(e,["name","target","targetOrigin","targetWindow"])),"undefined"==typeof window||"function"!=typeof window.postMessage)throw new Error("window.postMessage is not a function. This class should only be instantiated in a Window.");this._name=t,this._target=r,this._targetOrigin=n,this._targetWindow=_,this._onMessage=this._onMessage.bind(this),window.addEventListener("message",this._onMessage,!1),this._handshake()}_postMessage(e){this._targetWindow.postMessage({target:this._target,data:e},this._targetOrigin)}_onMessage(e){const t=e.data;"*"!==this._targetOrigin&&o.call(e)!==this._targetOrigin||a.call(e)!==this._targetWindow||!(0,_.E)(t)||t.target!==this._name||this._onData(t.data)}_destroy(){window.removeEventListener("message",this._onMessage,!1)}}}}).call(__webpack_require__._LM_("4",{__webpack_require__,__webpack_exports__}))()},9952:(module,__unused_webpack_exports,__webpack_require__)=>{(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(5699).F.ERR_INVALID_OPT_VALUE;module.exports={getHighWaterMark:function(t,r,n,_){var i=function(e,t,r){return null!=e.highWaterMark?e.highWaterMark:t?e[r]:null}(r,_,n);if(null!=i){if(!isFinite(i)||Math.floor(i)!==i||i<0)throw new e(_?n:"highWaterMark",i);return Math.floor(i)}return t.objectMode?16:16384}}}}).call(__webpack_require__._LM_("16",{__webpack_require__,module}))()}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(r.exports,r,r.exports,__webpack_require__),r.exports}(()=>{const e=Object.create(null);e.root="0",e.idmap=[["0",[7541,872,8662,5316,2945,2457,1659,5036,8746,6932,2857,4212,1539,9563,1417,949,5204,9272,1726,9860,3071,1964,2153,4389]],["1",[2171,7076,9612]],["2",[1993]],["3",[5875,7338,2565]],["4",[992,9950,2866,7996,1427,7123,9886]],["5",[3952,2295,5303,9306]],["6",[845,7012,6231,7822]],["7",[7950]],["8",[6219,4980,2250,3603,1705,6725,511,7131,544,7346,7569,6857,5791,7914,4,4937,4102,1010,6540,2489,2487,8546,7002,7995]],["9",[6157,8323,1750,7347,3349,2147]],["10",[2462,9400,7923,5259,2521,776,1089,2869,5953]],["11",[1048]],["12",[124,7891]],["13",[46]],["14",[3358]],["15",[9907]],["16",[4156,8199,4856,82,6527,9952,5699,1265,5291,534,4869,1260,9415,4421,6815]],["17",[5636]],["18",[2722,2841,9543,1361,4517,3990,3806,3955,8474,2281,8868,3269,6381,1353,6082,9428,7851,7555,2132,6106,4042,3810,9761,1262,8848,8220,2386,9639,4004,6783,1565,7476,8953,7229,6364,5039,5357,1280,7403,8854,7226,7183,8623,6486,583]],["19",[8888]],["20",[6732]],["undefined",[7991,9318,5615,7086,7640,3889,6848,4663,4394,4729,7566,3837,8464,282,7250,6605,8792]]],e.unenforceable=[3951,3011],e.externals={},e.options={scuttleGlobalThis:{enabled:!0,exceptions:["Object","postMessage","Reflect","Set"]},lockdown:{dateTaming:"unsafe",mathTaming:"unsafe",errorTaming:"unsafe",stackFiltering:"verbose",overrideTaming:"severe",localeTaming:"unsafe"}},(()=>{const t={exports:{}},{Object:r,Array:n,Error:_,RegExp:i,Set:a,console:o,Proxy:s,Reflect:u}=(t.exports,globalThis),{assign:c,getOwnPropertyNames:p,getOwnPropertyDescriptor:l,create:h,defineProperty:f}=r,{isArray:d,from:w}=n,{getPrototypeOf:b}=u,{warn:k}=o;t.exports={scuttle:function(e,t){const r=function(e,t=h(null)){const r={enabled:!0,exceptions:[],scuttlerName:""},n=c(h(null),!0===t?r:t,{scuttlerFunc:(e,t)=>t(e)},{exceptions:(t?.exceptions||r.exceptions).map((e=>function(e){if(!e.startsWith("/"))return e;const t=e.split("/"),r=t.slice(1,-1).join("/"),n=t[t.length-1];return new i(r,n)}(e)))});if(n.scuttlerName){if(!e[n.scuttlerName])throw new _(`LavaMoat - 'scuttlerName' function "${n.scuttlerName}" expected on globalRef.To learn more visit https://github.com/LavaMoat/LavaMoat/pull/462.`);n.scuttlerFunc=e[n.scuttlerName]}return n}(e,t);if(r.enabled){if(!d(r.exceptions))throw new _("LavaMoat - exceptions must be an array, got "+typeof r.exceptions);r.scuttlerFunc(e,(e=>function(e,t=[]){const r=[];(function(e){const t=[];let r=e;for(;r&&("object"==typeof r||"function"==typeof r);)t.push(r),r=b(r);return t})(e).forEach((e=>r.push(...p(e))));const n=new a(["Compartment","Error","globalThis",...t]),o=h(null);r.forEach((t=>{const{get:r,set:a}=function(e){return{get:function(){throw new _(`LavaMoat - property "${e}" of globalThis is inaccessible under scuttling mode. To learn more visit https://github.com/LavaMoat/LavaMoat/pull/360.`)},set:function(){k(`LavaMoat - property "${e}" of globalThis cannot be set under scuttling mode. To learn more visit https://github.com/LavaMoat/LavaMoat/pull/360.`)}}}(t);if(((e,t)=>w(e).some((e=>"string"==typeof e&&e===t||e instanceof i&&e.test(t))))(n,t))return;let u=l(e,t);if(!0===u?.configurable)u={configurable:!1,set:a,get:r};else{if(!0!==u?.writable)return;u={configurable:!1,writable:!1,value:new s(o,{getPrototypeOf:r,get:r,set:a})}}f(e,t,u)}))}(e,r.exceptions)))}}},e.scuttling=t.exports})(),e.policy={resources:{1:{packages:{6:!0,7:!0,10:!0}},2:{globals:{"console.warn":!0,setTimeout:!0},packages:{7:!0,10:!0,16:!0}},3:{globals:{"console.warn":!0},packages:{14:!0,16:!0}},4:{globals:{"MessageEvent.prototype":!0,WorkerGlobalScope:!0,addEventListener:!0,browser:!0,chrome:!0,"location.origin":!0,postMessage:!0,removeEventListener:!0},packages:{10:!0,16:!0}},5:{globals:{console:!0},packages:{1:!0,2:!0,6:!0,7:!0,16:!0,undefined:!0}},6:{packages:{10:!0,undefined:!0}},7:{globals:{setTimeout:!0},packages:{13:!0}},8:{packages:{10:!0,undefined:!0}},9:{globals:{URL:!0,"console.error":!0,"console.log":!0,"console.warn":!0},packages:{6:!0,8:!0,10:!0,undefined:!0}},10:{globals:{TextEncoder:!0},packages:{12:!0,18:!0,undefined:!0}},11:{globals:{console:!0},packages:{undefined:!0}},12:{globals:{console:!0,document:!0,localStorage:!0,navigator:!0,process:!0},packages:{15:!0,undefined:!0}},13:{globals:{console:!0}},14:{packages:{undefined:!0}},15:{globals:{clearTimeout:!0,setTimeout:!0}},16:{globals:{"process.nextTick":!0,"process.stderr":!0,"process.stdout":!0},packages:{11:!0,13:!0,15:!0,19:!0,20:!0,undefined:!0}},17:{packages:{11:!0}},18:{globals:{"console.error":!0,process:!0},packages:{15:!0}},19:{packages:{17:!0}},20:{globals:{"console.trace":!0,"console.warn":!0,localStorage:!0}}}},e.ENUM={NAME_globalThis:"G",NAME_scopeTerminator:"ST",NAME_runtimeHandler:"RH",RUNTIME_KEY:"_LM_"},(()=>{const t={exports:{}};function r(e,t){return e&&0!==e.length?`${e}.${t}`:t}function n(e,t,n){const _=Reflect.getOwnPropertyDescriptor(t,e[0])?.enumerable,i={get:()=>{let r=t[e[0]],n=t;for(let t=1;t<e.length;t++)n=r,r=r[e[t]];return"function"==typeof r&&(r=r.bind(n)),r},writeable:!1,enumerable:_,configurable:!1};let a=n,o="";for(let t=0;t<e.length-1;t++){o=r(o,e[t]);const n=e[t];if(Reflect.getOwnPropertyDescriptor(a,n)?.get)throw Error(`LavaMoat - "${e[0]}" is writeable elsewhere and both "${o}" and "${e.join(".")}" are allowed for one package. One of these entries is redundant.`);"object"!=typeof a[n]&&(a[n]={}),a=a[n]}const s=e[e.length-1];Reflect.defineProperty(a,s,i)}function _(e,t,r){const n=function(...n){if(new.target)return Reflect.construct(e,n,new.target);{const _=t(this)?r:this;return Reflect.apply(e,_,n)}};return Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)),n}t.exports,t.exports=function({createFunctionWrapper:e=_,handleGlobalWrite:t=!1,knownWritableFields:i=new Set}={}){return{getEndowmentsForConfig:function(e,r,n,_){if(!r.globals)return{};const i=[],o=new Set,s=[];return Object.entries(r.globals).forEach((([e,r])=>{const n=e.split(".");if(n.some((e=>"__proto__"===e)))throw new Error(`Lavamoat - "__proto__" disallowed when creating minimal view. saw "${e}"`);if(!1!==r){if("write"===r){if(!t)return;if(n.length>1)throw new Error(`LavaMoat - write access is only allowed at the top level, saw "${e}"`);return o.add(e),void i.push(e)}if(!0!==r)throw new Error(`LavaMoat - unrecognizable policy value (${typeof r}) for path "${e}"`);i.push(e)}else s.push(e)})),i.sort(((e,t)=>e.length-t.length)),a(e,i,n,_,s,o)},copyWrappedGlobals:function(e,t,r=["globalThis"]){const n=function(e){const t=[];let r=e;for(;r&&("object"==typeof r||"function"==typeof r);)t.push(r),r=Reflect.getPrototypeOf(r);return t}(e),_=n.findIndex((e=>e===Object.prototype));if(-1===_)throw new Error("Lavamoat - unable to find common prototype between Compartment and globalRef");const i=n.slice(0,_);i.forEach((t=>{const r=Object.getOwnPropertyDescriptors(t);Object.values(r).forEach((t=>{if("get"in t&&t.get)try{Reflect.apply(t.get,e,[])}catch{}}))}));const a=i.map((e=>Object.getOwnPropertyDescriptors(e))),o=Object.assign(Object.create(null),...a.reverse());Object.entries(o).filter((([e])=>!(e in t))).filter((([e])=>!r.includes(e))).forEach((([r,n])=>{const _=s(n,t,e);Reflect.defineProperty(t,r,_)}));for(const e of r)e in t||(t[e]=t);return t},getBuiltinForConfig:function(e,t,r){const n=[],_=[];return Object.entries(r).forEach((([e,r])=>{const i=e.split(".");if(t===i[0]){const e=i.slice(1).join(".");!0===r?n.push(e):!1===r&&_.push(e)}})),a(e,n.sort(),void 0,void 0,_)},createFunctionWrapper:e,makeMinimalViewOfRef:a,copyValueAtPath:o,applyGetSetPropDescTransforms:u,applyEndowmentPropDescTransforms:s};function a(e,t,r,_,a=[],s=new Set){const u={};return t.forEach((t=>{const c=t.split(".");i.has(c[0])?s.has(c[0])?function(e,t,r){const n=Reflect.getOwnPropertyDescriptor(t,e)?.enumerable;Reflect.defineProperty(r,e,{configurable:!1,enumerable:n,set(r){t[e]=r},get:()=>t[e]})}(c[0],e,u):n(c,e,u):o("",c,a,e,u,r,_)})),u}function o(t,n,_,i,a,s=i,c=a){if(0===n.length)throw new Error("unable to copy, must have pathParts, was empty");const[p,...l]=n,h=r(t,p),{prop:f}=function(e,t){let r=e;for(;;){if(!r)return{prop:null,receiver:null};const e=typeof r;if("object"===e||"function"===e){const e=Reflect.getOwnPropertyDescriptor(r,t);if(e)return{receiver:r,prop:e};r=Reflect.getPrototypeOf(r)}else r=r.__proto__}}(i,p);if(!f)return;const d=Reflect.getOwnPropertyDescriptor(a,p);if(d){if(!("value"in d))throw new Error(`unable to copy on to targetRef, targetRef has a getter at "${p}"`);const e=typeof d.value;if("object"!==e&&"function"!==e)throw new Error(`unable to copy on to targetRef, targetRef value is not an obj or func "${p}"`)}if(l.length>0){const{sourceValue:e,sourceWritable:t}=m(f),r=e;let n;if(d&&!_.includes(h))n=d.value;else{const e={},r={value:e,writable:t,enumerable:f.enumerable,configurable:f.configurable};Reflect.defineProperty(a,p,r),n=e}return void o(h,l,_,r,n)}if(_.includes(h))return void console.warn(`LavaMoat - conflicting rules exist for "${h}"`);if(!("value"in f)){const e=u(f,c,s);return void Reflect.defineProperty(a,p,e)}const{sourceValue:w,sourceWritable:b}=m(f);if("function"!=typeof w)return void Reflect.defineProperty(a,p,f);const k={value:e(w,(e=>e===c),s),writable:b,enumerable:f.enumerable,configurable:f.configurable};function m(e){let t,r;if("value"in e)t=e.value,r=e.writable;else{if(!("get"in e)||!e.get)throw new Error("getEndowmentsForConfig - property descriptor missing a getter");t=e.get.call(s),r="set"in e}return{sourceValue:t,sourceWritable:r}}Reflect.defineProperty(a,p,k)}function s(t,r,n){let _=t;return _=function(t,r,n){if(!("value"in t)||"function"!=typeof t.value)return t;const _=e(t.value,(e=>e===r),n);return{...t,value:_}}(_,r,n),_=u(_,r,n),_}function u(t,r,n){const _={...t};return t.get&&(_.get=function(){const _=this===r?n:this,i=Reflect.apply(t.get,_,[]);return"function"==typeof i?e(i,(e=>e===r),n):i}),t.set&&(_.set=function(e){const _=this===r?n:this;return Reflect.apply(t.set,_,[e])}),_}},t.exports._test={instrumentDynamicValueAtPath:n},e.endowmentsToolkit=t.exports})();const{keys:t,create:r,freeze:n,assign:_,defineProperty:i,defineProperties:a,getOwnPropertyDescriptors:o,fromEntries:s,entries:u,values:c}=Object,{lockdown:p,Proxy:l,Math:h,Date:f}=globalThis,d="object"==typeof console?console.warn:()=>{},w=void 0!==p;w?p(e.options.lockdown):d("LavaMoat: runtime execution started without SES present, switching to no-op.");const{harden:b}=globalThis,k=new Set;c(e.policy.resources).forEach((e=>{e.globals&&"object"==typeof e.globals&&u(e.globals).forEach((([e,t])=>{"write"===t&&k.add(e)}))}));const{getEndowmentsForConfig:m,copyWrappedGlobals:g,getBuiltinForConfig:y}=e.endowmentsToolkit({handleGlobalWrite:!0,knownWritableFields:k}),{NAME_globalThis:q,NAME_scopeTerminator:v,NAME_runtimeHandler:E}=e.ENUM,S=n(new l(n(r(null)),n({has:n((()=>!0))}))),x=globalThis;let R;const T=["globalThis","window","self"],M=new Map,O=(r,n)=>function(_,...i){"number"!=typeof _&&(_=`${_}`);const a=r.bind(this,_,...i);return((r,n,_)=>{if(void 0===r)throw Error("Requested specifier is undefined");if(e.unenforceable.includes(r)||n===e.root)return _();const i=e.policy.resources[n]||{};if(i.builtin&&e.externals[r]){const n=e.externals[r];if(i.builtin[n])return _();if(n&&!n.includes(".")&&t(i.builtin).some((e=>e.startsWith(`${n}.`))))return y(_(),n,i.builtin)}const a=(t=>{const r=e.idmap.find((([,e])=>e.includes(t)));if(r)return r[0]})(r);if(!a)throw Error(`Requested specifier ${r} is not allowed as a builtin and not a known dependency of ${n}. Regenerate policy or add it to policy-override.json.`);if(a===n)return _();if(i.packages?.[a])return _();throw Error(`Policy does not allow importing ${a} from ${n}`)})(_,n,a)};e.defaultExport=n(((t,u)=>{if(!w)return r(null);if(!M.has(t)){const r=new Compartment({Math:h,Date:f});((t,r)=>{if(t===e.root)R=r,g(x,R,T),e?.scuttling?.scuttle(x,e.options.scuttleGlobalThis);else{const n=m(R,e.policy.resources[t]||{},globalThis,r);a(r,{...o(n),...s(T.map((e=>[e,{value:r}])))}),e.debug&&e.debug.debugProxy(r,R,t)}})(t,r.globalThis),M.set(t,r)}const c=r(null),{__webpack_require__:p}=u;let{module:k}=u;if(p){const e=O(p,t),r=["O","n","d","o","r","s","t","b"];for(const t of r)e[t]=b(p[t]);e.m=new l({},{has:(e,t)=>(d(`A module attempted to read ${String(t)} directly from webpack's module cache`),!1)}),e.g=M.get(t).globalThis,e.nmd=e=>e===k?p.nmd(k):e,e.hmd=e=>e===k?p.hmd(k):e,c.__webpack_require__=e}const y=_(r(null),u,c);i(y,"module",{get:()=>k,set:()=>{}});let L=y.exports;return i(y,"exports",{get:()=>L,set:e=>{L=e}}),n(y),{[v]:S,[E]:y,[q]:M.get(t).globalThis}})),__webpack_require__._LM_=e.defaultExport,void 0!==b&&b(__webpack_require__._LM_)})(),__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var r in t)__webpack_require__.o(t,r)&&!__webpack_require__.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var __webpack_exports__={};(function(){if(!this.ST)return()=>{};with(this.ST)with(this.RH)with(this.G)return function(){"use strict";var e=__webpack_require__(872),t=__webpack_require__(2153);(0,__webpack_require__(4389).m)(),(0,t.K)(),e.G.initialize()}}).call(__webpack_require__._LM_("0",{__webpack_require__}))()})();