{"QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareWalletImporterTitle": {"message": "Nuskaityti QR kodą"}, "about": {"message": "<PERSON><PERSON>"}, "accessingYourCamera": {"message": "<PERSON><PERSON>ipiamasi į kamerą..."}, "account": {"message": "Paskyra"}, "accountDetails": {"message": "Paskyros informacija"}, "accountName": {"message": "<PERSON><PERSON><PERSON>"}, "accountOptions": {"message": "<PERSON><PERSON><PERSON>"}, "accountSelectionRequired": {"message": "Jums reikia pasirinkti paskyrą!"}, "activityLog": {"message": "<PERSON><PERSON><PERSON>"}, "addAcquiredTokens": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuos gavote naudodamiesi „MetaMask“"}, "addAlias": {"message": "Pridėti alternatyvųjį vardą"}, "addNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "addSuggestedTokens": {"message": "Pridėti siūlomų žetonų"}, "addToken": {"message": "Pridėti <PERSON>"}, "advanced": {"message": "Išplėstiniai"}, "amount": {"message": "<PERSON><PERSON>"}, "appDescription": {"message": "„Ethereum“ piniginė jūsų naršyklėje", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "asset": {"message": "<PERSON><PERSON><PERSON>"}, "attributions": {"message": "Požymiai"}, "autoLockTimeLimit": {"message": "Automatinio <PERSON> (minutės)"}, "autoLockTimeLimitDescription": {"message": "Nustatykite laukimo  trukmę minutėmis iki „MetaMask“ automatinio atsijungimo"}, "average": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "back": {"message": "Grįžti"}, "backupApprovalInfo": {"message": "Šis slaptas kodas reikalingas norint atkurti jūsų slaptažodinę, jeigu pamestumėte savo įrenginį, p<PERSON><PERSON><PERSON>tumėte savo slaptažodį, iš naujo įdiegtumėte „MetaMask“, taip pat norint pasiekti slaptažodinę iš kito įrenginio."}, "backupApprovalNotice": {"message": "Pasidarykite atsarginę savo slapto atkūrimo kodo kopiją, kad jūs<PERSON> slaptažodinė ir fondai būtų saugūs."}, "backupNow": {"message": "Daryti atsarginę kopiją dabar"}, "balance": {"message": "<PERSON><PERSON><PERSON>"}, "balanceOutdated": {"message": "<PERSON>ku<PERSON> gali būti pasen<PERSON>s"}, "basic": {"message": "<PERSON><PERSON><PERSON>"}, "blockExplorerUrl": {"message": "Blokuoti naršyklę"}, "blockExplorerView": {"message": "Peržiūrėti paskyrą $1", "description": "$1 replaced by URL for custom block explorer"}, "browserNotSupported": {"message": "<PERSON><PERSON><PERSON><PERSON> naršyklė neatpažįstama..."}, "bytes": {"message": "Baitai"}, "cancel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "cancelled": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "chainId": {"message": "Grandinės ID"}, "chromeRequiredForHardwareWallets": {"message": "Norėdami prisijungti prie aparatinės įrangos slap<PERSON><PERSON><PERSON>ės, „MetaMask“ naudokitės „Google Chrome“ naršyklėje."}, "close": {"message": "Uždaryti"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmPassword": {"message": "Patvirtinkite slaptažodį"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "connect": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "connectingTo": {"message": "Jungiamasi prie $1"}, "connectingToGoerli": {"message": "Jungiamasi prie <PERSON><PERSON>li“ bandomojo tinklo"}, "connectingToLineaGoerli": {"message": "Jungiamasi prie „Linea“ bandomojo tinklo"}, "connectingToMainnet": {"message": "Jungiamasi prie pagrindinio „Ethereum“ tinklo"}, "contractDeployment": {"message": "Sutarties išdėstymas"}, "contractInteraction": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "copiedExclamation": {"message": "Nukopijuota!"}, "copyAddress": {"message": "Kopijuoti adresą į iškarpinę"}, "copyToClipboard": {"message": "Kopijuoti į iškarpinę"}, "copyTransactionId": {"message": "Kopijuoti operacijos ID"}, "create": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "createPassword": {"message": "Sukurti slaptažodį"}, "currencyConversion": {"message": "Valiutos keit<PERSON>"}, "currentLanguage": {"message": "<PERSON><PERSON><PERSON><PERSON> kalba"}, "custom": {"message": "Išplėstiniai"}, "customToken": {"message": "Pritaikytas žetonas"}, "decimal": {"message": "Skaičiai po kablelio"}, "decimalsMustZerotoTen": {"message": "<PERSON><PERSON> bent 0 skaitmen<PERSON> po ka<PERSON>, bet ne daugiau kaip 36."}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "details": {"message": "Išsami informacija"}, "done": {"message": "Atlikta"}, "downloadGoogleChrome": {"message": "Atsiunčiama „Google Chrome“"}, "downloadStateLogs": {"message": "Atsisiųsti b<PERSON><PERSON>"}, "dropped": {"message": "Numesta"}, "edit": {"message": "Red<PERSON><PERSON><PERSON>"}, "editContact": {"message": "<PERSON><PERSON><PERSON>"}, "enterPasswordContinue": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>, įveskite slaptažodį"}, "ethereumPublicAddress": {"message": "„Ethereum“ viešasis adresas"}, "etherscanView": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> „Etherscan“ programoje"}, "expandView": {"message": "Išskleisti rodinį"}, "failed": {"message": "Nepavyko"}, "fast": {"message": "G<PERSON><PERSON>"}, "fileImportFail": {"message": "Failo importavimas neve<PERSON>? Spustelėkite čia!", "description": "Helps user import their account from a JSON file"}, "forgetDevice": {"message": "<PERSON><PERSON><PERSON><PERSON> šį įrenginį"}, "from": {"message": "<PERSON><PERSON>"}, "gasLimit": {"message": "Dujų apribojimas"}, "gasLimitTooLow": {"message": "<PERSON><PERSON><PERSON> apribojimas turi bū<PERSON> bent 21000"}, "gasUsed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "general": {"message": "Bendra"}, "goerli": {"message": "„<PERSON><PERSON><PERSON><PERSON> band<PERSON> tin<PERSON>s"}, "hardware": {"message": "aparatinė įranga"}, "hardwareWalletConnected": {"message": "Aparatinės įrangos <PERSON><PERSON><PERSON><PERSON><PERSON> susieta"}, "hardwareWallets": {"message": "<PERSON>ti aparatinės įrangos <PERSON><PERSON><PERSON><PERSON>"}, "hardwareWalletsMsg": {"message": "Pasirinkite aparatinės įrangos <PERSON><PERSON>, k<PERSON><PERSON>ujate naudoti su „MetaTask“"}, "here": {"message": "čia", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Šešioliktainiai duomenys"}, "hide": {"message": "Slėpti"}, "hideTokenPrompt": {"message": "Slėpti prieigos raktą?"}, "history": {"message": "Istorija"}, "import": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": "Importuotos paskyros nebus susietos su pirmiau sukurta „MetaMask“ paskyros atkūrimo fraze. Sužinokite daugiau apie importuotas paskyras"}, "imported": {"message": "I<PERSON>rt<PERSON><PERSON>", "description": "status showing that an account has been fully loaded into the keyring"}, "initialTransactionConfirmed": {"message": "Jūsų pradinė operacija patvirtinta tinkle. Norėdami grįžti, spustelėkite „Gerai“."}, "insufficientBalance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lik<PERSON>."}, "insufficientFunds": {"message": "Nepakanka lėšų."}, "insufficientTokens": {"message": "Nepakanka žetonų."}, "invalidAddress": {"message": "Netinkamas ad<PERSON>"}, "invalidAddressRecipient": {"message": "G<PERSON><PERSON><PERSON> ad<PERSON>"}, "invalidRPC": {"message": "Netinkamas RPC URL"}, "invalidSeedPhrase": {"message": "Netinkama atkūrimo frazė"}, "jsonFile": {"message": "JSON failas", "description": "format for importing an account"}, "knownAddressRecipient": {"message": "Žinomas sutarties adresas."}, "learnMore": {"message": "Sužinokite daugiau"}, "learnMoreUpperCase": {"message": "Sužinokite daugiau"}, "ledgerAccountRestriction": {"message": "<PERSON><PERSON><PERSON> nauj<PERSON>, turite pasi<PERSON><PERSON><PERSON> paskuti<PERSON> pask<PERSON>."}, "likeToImportTokens": {"message": "<PERSON>r <PERSON><PERSON><PERSON><PERSON> p<PERSON>ėti š<PERSON>?"}, "lineaGoerli": {"message": "„Linea“ band<PERSON>"}, "links": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "loadMore": {"message": "Įkelti daugiau"}, "loading": {"message": "Įkeliama..."}, "localhost": {"message": "Vietinis serveris 8545"}, "lock": {"message": "<PERSON>si<PERSON><PERSON><PERSON>"}, "mainnet": {"message": "Pagrindinis „Ethereum“ tinklas"}, "max": {"message": "Ma<PERSON>."}, "memo": {"message": "pastaba"}, "message": {"message": "Pranešimas"}, "metamaskVersion": {"message": "„MetaMask“ versija"}, "mustSelectOne": {"message": "Turite pasirinkti bent 1 žetoną."}, "needImportFile": {"message": "Turite pasirinkti fail<PERSON>, kurį pageidaujate importuoti.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Neigiamų ETH sumų siųsti negalima."}, "networkName": {"message": "<PERSON><PERSON><PERSON>"}, "networks": {"message": "<PERSON><PERSON><PERSON>"}, "nevermind": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "newAccount": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "newAccountNumberName": {"message": "Paskyra $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "<PERSON><PERSON><PERSON> k<PERSON>ak<PERSON>"}, "newContract": {"message": "<PERSON><PERSON><PERSON>"}, "newPassword": {"message": "<PERSON><PERSON><PERSON> (bent 8 ženklai)"}, "next": {"message": "Toliau"}, "noConversionRateAvailable": {"message": "Nėra keitimo k<PERSON>o"}, "noWebcamFound": {"message": "Jūsų kompiuterio vaizdo kamera nerasta. Bandykite dar kartą."}, "noWebcamFoundTitle": {"message": "<PERSON><PERSON><PERSON> vaizdo kamera nerasta"}, "notEnoughGas": {"message": "Nepakanka dujų"}, "ofTextNofM": {"message": "nuo"}, "off": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ok": {"message": "G<PERSON><PERSON>"}, "on": {"message": "Įjungta"}, "origin": {"message": "<PERSON><PERSON><PERSON>"}, "participateInMetaMetrics": {"message": "Dalyvaukite „MetaMetrics“"}, "participateInMetaMetricsDescription": {"message": "Dalyvaukite „MetaMetrics“ ir padėkite mums to<PERSON><PERSON> „MetaMask“"}, "password": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "passwordNotLongEnough": {"message": "S<PERSON><PERSON><PERSON><PERSON><PERSON> per trumpas"}, "passwordsDontMatch": {"message": "Slaptažodžiai nesutampa"}, "pastePrivateKey": {"message": "Čia įklijuokite as<PERSON>inio rakto e<PERSON>:", "description": "For importing an account from a private key"}, "pending": {"message": "la<PERSON><PERSON> pat<PERSON><PERSON><PERSON>"}, "personalAddressDetected": {"message": "Aptiktas asmeninis adresas. Įveskite žetono sutarties adresą."}, "prev": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "privacyMsg": {"message": "Privatumo politika"}, "privateKey": {"message": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Dėmesio. Niekada neatskleiskite šio rakto kitiems. Bet kuris jūsų asmeninius raktus turintis asmuo gali pavogti bet kokį jūsų paskyroje laikomą turtą."}, "privateNetwork": {"message": "<PERSON><PERSON><PERSON><PERSON> tin<PERSON>"}, "readdToken": {"message": "Šį žetoną galite bet kada galite įtraukti ir v<PERSON>l, tiesiog savo paskyros parinkčių meniu nueikite  į „Įtraukti žetoną“."}, "reject": {"message": "<PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "rejectTxsDescription": {"message": "Vienu metu atmesite $1 operac."}, "rejectTxsN": {"message": "Atmesti $1 operac."}, "rejected": {"message": "Atmes<PERSON>"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removeAccount": {"message": "Pašalinti p<PERSON>yrą"}, "removeAccountDescription": {"message": "Ši paskyra bus pašalinta iš jūsų piniginės. <PERSON><PERSON><PERSON> tę<PERSON>dami įsitikinkite, kad turite šios importuotos paskyros pradinę atkūrimo frazę arba asmeninį raktą. Paskyras importuoti ir vėl susikurti galite paskyros išskleidžiamajame meniu."}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "Nustatyti iš naujo"}, "restore": {"message": "Atkurti"}, "revealSeedWords": {"message": "Atskleisti atkūrimo <PERSON>"}, "revealSeedWordsWarning": {"message": "Šiuos <PERSON> galima panaudoti visoms jūsų paskyroms pavogti.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "rpcUrl": {"message": "Naujas RPC URL"}, "save": {"message": "<PERSON>š<PERSON>ug<PERSON><PERSON>"}, "scanInstructions": {"message": "Laikykite QR kodą priešais kamerą"}, "scanQrCode": {"message": "Nuskaityti QR kodą"}, "search": {"message": "Ieškoti"}, "securityAndPrivacy": {"message": "Sauga ir privatumas"}, "seedPhraseReq": {"message": "Atkūrimo frazės yra 12 žodžių ilgio"}, "selectAnAccount": {"message": "Pasirinkite paskyrą"}, "selectHdPath": {"message": "Pasirinkite HD kelią"}, "selectPathHelp": {"message": "<PERSON><PERSON>u toliau nematote savo esamų operacijų sąskaitų, mėginkite pakeisti kelius į „Legacy (MEW / MyCrypto)“"}, "selectType": {"message": "Pasirinkti <PERSON>"}, "send": {"message": "Si<PERSON>sti"}, "settings": {"message": "Nustatymai"}, "showFiatConversionInTestnets": {"message": "Rod<PERSON><PERSON> keitimą „Testnet“"}, "showFiatConversionInTestnetsDescription": {"message": "Pasirink<PERSON> tai, kad b<PERSON><PERSON><PERSON> rodoma<PERSON> standart<PERSON> valiuto<PERSON> keit<PERSON> „Testnet“ programoje"}, "showHexData": {"message": "<PERSON><PERSON><PERSON>š<PERSON><PERSON> duomenis"}, "showHexDataDescription": {"message": "Pasir<PERSON><PERSON> tai, kad siuntimo ekrane būt<PERSON> rodoma<PERSON>šioliktainių duomenų laukas"}, "sign": {"message": "Prisijunkite"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON>"}, "signed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "somethingWentWrong": {"message": "Vaje! Kažkas negerai."}, "speedUp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "Pagreitinti šį atšaukimą"}, "speedUpTransaction": {"message": "Paspartinti šią operaciją"}, "stateLogError": {"message": "<PERSON><PERSON><PERSON> gau<PERSON> b<PERSON><PERSON>."}, "stateLogs": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "stateLogsDescription": {"message": "Būsenos žurnaluose yra jūsų viešos paskyros adresų ir išsiųstų operacijų."}, "submitted": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "supportCenter": {"message": "Apsilankykite mūsų pagalbos centre"}, "switchNetworks": {"message": "<PERSON><PERSON><PERSON><PERSON> tinklus"}, "symbol": {"message": "Si<PERSON><PERSON>"}, "symbolBetweenZeroTwelve": {"message": "Simbolis turi būti ne ilgesnis nei 11 simbolių."}, "terms": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "to": {"message": "<PERSON><PERSON>"}, "token": {"message": "Žetonas"}, "tokenAlreadyAdded": {"message": "Žetonas jau pridėtas."}, "tokenContractAddress": {"message": "<PERSON><PERSON><PERSON> k<PERSON>aktin<PERSON> ad<PERSON>"}, "tokenSymbol": {"message": "Žetono ženklas"}, "total": {"message": "<PERSON><PERSON> viso"}, "transaction": {"message": "operacija"}, "transactionCancelAttempted": {"message": "Operaciją mėginta atšaukti $2 su $1 duj<PERSON> mokesčiu"}, "transactionCancelSuccess": {"message": "Operacija sėkmingai atšaukta $2"}, "transactionConfirmed": {"message": "Operacijos patvirtinimas $2."}, "transactionCreated": {"message": "Operacija sukurta $2 su $1 verte."}, "transactionDropped": {"message": "Operacija numesta prie$2."}, "transactionError": {"message": "Operacijos klaida. Sutarties kodo numetimo išimtis."}, "transactionErrorNoContract": {"message": "Mėginama iškviesti funkciją nekontaktiniu adresu."}, "transactionErrored": {"message": "Įvyko operacijos klaida"}, "transactionResubmitted": {"message": "Operacija pakartotinai pateikta$2 su iki $1 padidintu dujų mokesčiu"}, "transactionSubmitted": {"message": "Operacija pateikta $2 su $1 duj<PERSON> mokesčiu."}, "transactionUpdated": {"message": "Operacija atnaujinta$2."}, "transfer": {"message": "<PERSON><PERSON><PERSON>"}, "transferFrom": {"message": "Perved<PERSON><PERSON> iš"}, "tryAgain": {"message": "Bandyti dar kartą"}, "unapproved": {"message": "Nepatvirtinta"}, "units": {"message": "vienetai"}, "unknown": {"message": "Nežinoma"}, "unknownQrCode": {"message": "Klaida: negalime identifikuoti šio QR kodo"}, "unlock": {"message": "Atrakinti"}, "urlErrorMsg": {"message": "URI reikia atitinkamo HTTP/HTTPS priešdėlio."}, "usedByClients": {"message": "Naudojama da<PERSON>lio skirtingų klientų"}, "userName": {"message": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>"}, "viewContact": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "visitWebSite": {"message": "Apsilankykite mūsų svetainėje"}, "welcomeBack": {"message": "Sveiki sugrįžę!"}, "youNeedToAllowCameraAccess": {"message": "<PERSON><PERSON> na<PERSON>, reikia leisti prieigą prie kameros.."}, "yourPrivateSeedPhrase": {"message": "Jūsų as<PERSON>inė atkūrimo frazė"}}