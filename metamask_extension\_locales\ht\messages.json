{"QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareWalletImporterTitle": {"message": "Enspeksyon QR Kòd"}, "accessingYourCamera": {"message": "<PERSON><PERSON><PERSON>"}, "account": {"message": "<PERSON><PERSON>"}, "accountDetails": {"message": "<PERSON><PERSON>"}, "accountName": {"message": "Non Kont"}, "accountOptions": {"message": "Opsyon kont"}, "accountSelectionRequired": {"message": "Ou bezwen chwazi yon kont!"}, "activityLog": {"message": "aktivite ki fèt"}, "addAcquiredTokens": {"message": "Ajoute tokens yo ou te achte lè l sèvi avèk MetaMask"}, "addSuggestedTokens": {"message": "Ajoute Token Yo <PERSON> W"}, "addToken": {"message": "<PERSON><PERSON><PERSON>"}, "amount": {"message": "<PERSON><PERSON><PERSON>"}, "appDescription": {"message": "Ekstansyon Navigatè Ethereum", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approved": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "attributions": {"message": "Atribisyon"}, "back": {"message": "Re<PERSON><PERSON><PERSON>"}, "balance": {"message": "Balans"}, "browserNotSupported": {"message": "Navigatè ou a pa sipòte..."}, "cancel": {"message": "Anile"}, "chromeRequiredForHardwareWallets": {"message": "<PERSON>u bezwen sèvi ak MetaMask sou Google Chrome yo nan lòd yo konekte sou Hardware Wallet."}, "close": {"message": "Fèmen"}, "confirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "connect": {"message": "Konekte"}, "connectingToMainnet": {"message": "Konekte ak Prensipal Ethereum Rezo a"}, "contractDeployment": {"message": "<PERSON><PERSON><PERSON>"}, "copiedExclamation": {"message": "<PERSON><PERSON><PERSON>!"}, "copyAddress": {"message": "<PERSON><PERSON><PERSON> clipboard"}, "copyToClipboard": {"message": "Kopi clipboard"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "currentLanguage": {"message": "Lang Aktyèl"}, "customToken": {"message": "<PERSON><PERSON><PERSON>"}, "decimal": {"message": "Presizyon desimal la"}, "decimalsMustZerotoTen": {"message": "Desimal yo dwe omwen 0, epi pa dwe plis pase 36."}, "details": {"message": "Detay yo"}, "done": {"message": "<PERSON><PERSON>"}, "downloadGoogleChrome": {"message": "Telechaje Google Chrome"}, "downloadStateLogs": {"message": "Telechaje State Logs"}, "dropped": {"message": "<PERSON><PERSON><PERSON>"}, "edit": {"message": "<PERSON><PERSON><PERSON>"}, "enterPasswordContinue": {"message": "Mete modpas pou kontinye"}, "etherscanView": {"message": "Gade kont sou Etherscan"}, "expandView": {"message": "<PERSON><PERSON><PERSON>"}, "failed": {"message": "<PERSON><PERSON><PERSON>"}, "fileImportFail": {"message": "Enpòte dosye ki pa travay? Klike la a!", "description": "Helps user import their account from a JSON file"}, "forgetDevice": {"message": "Bliye aparèy sa a"}, "from": {"message": "<PERSON><PERSON> nan"}, "gasLimit": {"message": "Limit gaz"}, "gasLimitTooLow": {"message": "Limit gaz dwe omwen 21000"}, "gasUsed": {"message": "Gaz yo Itilize"}, "hardware": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "hardwareWalletConnected": {"message": "<PERSON><PERSON><PERSON><PERSON> konekte"}, "hardwareWallets": {"message": "<PERSON><PERSON><PERSON><PERSON> konekte"}, "hardwareWalletsMsg": {"message": "<PERSON><PERSON><PERSON> yon <PERSON><PERSON><PERSON> ou ta renmen itilize ak MetaMask"}, "here": {"message": "isit la", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hide": {"message": "<PERSON><PERSON>"}, "hideTokenPrompt": {"message": "<PERSON><PERSON>?"}, "history": {"message": "Istwa"}, "import": {"message": "Pòte", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": " Kont pòte pa pral asosye avèk orijinal ou te kreye nan kont MetaMask seed fraz. Aprann plis sou kont enpòte "}, "imported": {"message": "Pòte", "description": "status showing that an account has been fully loaded into the keyring"}, "initialTransactionConfirmed": {"message": "<PERSON><PERSON><PERSON> ou konfime sou rezo a. Klike sou OK pou tounen."}, "insufficientFunds": {"message": "<PERSON><PERSON>."}, "insufficientTokens": {"message": "Tokens pa valab."}, "invalidAddress": {"message": "<PERSON><PERSON><PERSON> pa valab"}, "invalidAddressRecipient": {"message": "<PERSON>un ki resevwa adrès la pa valab"}, "invalidRPC": {"message": "RPC URI pa valab"}, "invalidSeedPhrase": {"message": "Seed fraz pa valab"}, "jsonFile": {"message": "JSON <PERSON>e", "description": "format for importing an account"}, "learnMore": {"message": "Aprann plis"}, "learnMoreUpperCase": {"message": "Aprann plis"}, "ledgerAccountRestriction": {"message": "<PERSON>u bezwen sèvi ak dènye kont ou anvan ou ka ajoute yon nouvo."}, "likeToImportTokens": {"message": "Èske ou ta renmen ajoute sa nan tokens?"}, "links": {"message": "Lyen"}, "loading": {"message": "Telechaje..."}, "lock": {"message": "Dekonekte"}, "mainnet": {"message": "Prensipal Ethereum Rezo a"}, "max": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "message": {"message": "Mesaje"}, "metamaskVersion": {"message": "MetaMask Vèsyon"}, "mustSelectOne": {"message": "Ou dwe chwazi omwen 1 token."}, "needImportFile": {"message": "Ou dwe chwazi yon dosye pou enpòte.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Pa ka voye kantite lajan negatif ETH."}, "networks": {"message": "<PERSON><PERSON>"}, "nevermind": {"message": "<PERSON> pwoblèm"}, "newAccount": {"message": "<PERSON><PERSON><PERSON>"}, "newAccountNumberName": {"message": "Kont $1", "description": "Default name of next account to be created on create account screen"}, "newContract": {"message": "Nouvo Kontra"}, "newPassword": {"message": "Nouvo modpas (minit 8)"}, "next": {"message": "<PERSON><PERSON> sa"}, "noConversionRateAvailable": {"message": "Pa gen okenn Konvèsyon Disponib"}, "noWebcamFound": {"message": "<PERSON>u pakay jwenn webcam òdinatè ou. Tanpri eseye ankò."}, "noWebcamFoundTitle": {"message": "Pa jwenn webcam"}, "ok": {"message": "<PERSON>e"}, "origin": {"message": "Orijin"}, "password": {"message": "Modpas"}, "passwordNotLongEnough": {"message": "Modpas pa lontan ase"}, "passwordsDontMatch": {"message": "Modpas pa matche"}, "pastePrivateKey": {"message": "<PERSON>le fraz prive ou a la:", "description": "For importing an account from a private key"}, "pending": {"message": "l ap mache"}, "personalAddressDetected": {"message": "<PERSON><PERSON><PERSON> p<PERSON>èl detekte. Antre adrès kontra token la."}, "prev": {"message": "Avan"}, "privacyMsg": {"message": "Règleman sou enfòmasyon prive"}, "privateKey": {"message": "<PERSON><PERSON> kle", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Atansyon: pa janm divilge kle sa. Nenpòt moun kapab avèk kle prive ou a vòlè sa ou gen ou sou kont ou a."}, "privateNetwork": {"message": "<PERSON><PERSON>"}, "readdToken": {"message": "Ou ka ajoute token sa aprè sa ankò ou prale nan  \"Ajoute token\" nan opsyon meni kont ou an."}, "reject": {"message": "<PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON>"}, "rejectTxsDescription": {"message": "Ou se sou rejte $ 1 yon anpil nan tranzaksyon yo."}, "rejectTxsN": {"message": "Rejete $ 1 tranzaksyon"}, "rejected": {"message": "<PERSON><PERSON><PERSON>"}, "remove": {"message": "retire"}, "removeAccount": {"message": "Re<PERSON><PERSON> kont"}, "removeAccountDescription": {"message": "Kont sa a pral retire nan <PERSON>et ou. <PERSON><PERSON><PERSON>, asire ou ke ou gen orijinal fraz seed la oubyen kle prive pou rantre kont lan avan ou kontinye. Oubyen ou ka rantre kont ou ankò apati kont \"drop-down\" ou an."}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "Repwograme"}, "restore": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "Revele Seed <PERSON>"}, "revealSeedWordsWarning": {"message": "Yo ka itilize mo sa pou vòlè kont ou.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "save": {"message": "Sove"}, "scanInstructions": {"message": "<PERSON><PERSON> k<PERSON>d QR la devan kamera ou"}, "scanQrCode": {"message": "Enspeksyon QR Kòd"}, "search": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "seedPhraseReq": {"message": "Seed fraz yo se 12 long mo"}, "selectAnAccount": {"message": "<PERSON><PERSON><PERSON> yon kont"}, "selectHdPath": {"message": "Chwazi chemen HD"}, "selectPathHelp": {"message": "<PERSON> ou pa wè kont Ledger ou te genyen an anba a, eseye chanje chemen an \"Eritaj (MEW / MyCrypto)\""}, "selectType": {"message": "Chwazi Kalite"}, "send": {"message": "<PERSON><PERSON><PERSON>"}, "settings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "showHexData": {"message": "<PERSON><PERSON>"}, "showHexDataDescription": {"message": "Pran sa pouw ka montre chan entèfas hex data a"}, "sign": {"message": "<PERSON><PERSON><PERSON>"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON>"}, "signed": {"message": "Te Si<PERSON>n"}, "speedUp": {"message": "pi vit"}, "stateLogError": {"message": "<PERSON>r<PERSON> nan retwouve State Logs yo."}, "stateLogsDescription": {"message": "State logs gen ad<PERSON><PERSON> kont piblik ou yo epi tranzaksyon ou te voye yo."}, "submitted": {"message": "<PERSON>um<PERSON>"}, "supportCenter": {"message": "Vizite Sant <PERSON>"}, "symbolBetweenZeroTwelve": {"message": "<PERSON><PERSON><PERSON><PERSON> yo dwe 11 karaktè oswa mwens."}, "terms": {"message": "<PERSON><PERSON><PERSON> pou itilize"}, "to": {"message": "<PERSON><PERSON>"}, "tokenAlreadyAdded": {"message": "Ou te deja ajoute token."}, "tokenSymbol": {"message": "<PERSON><PERSON>"}, "transaction": {"message": "tranzaksyon yo"}, "transactionConfirmed": {"message": "Tranzaksyon ou te konfime pou $2."}, "transactionCreated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou te kreye avèk on valè de $1 pou $2."}, "transactionDropped": {"message": "Tranzaksyon ou te tonbe a $2."}, "transactionError": {"message": "<PERSON><PERSON><PERSON>. Eksepsyon jete nan kòd kontra."}, "transactionSubmitted": {"message": "Tranzaksyon ou te soumèt a $2."}, "transactionUpdated": {"message": "Tranzaksyon ou te aktyalize a $2."}, "transfer": {"message": "Transfè"}, "tryAgain": {"message": "<PERSON><PERSON><PERSON>ko"}, "unapproved": {"message": "<PERSON> a<PERSON>"}, "units": {"message": "inite yo"}, "unknown": {"message": "<PERSON><PERSON><PERSON>"}, "unknownQrCode": {"message": "Erè: <PERSON>u pa t kapab idantifye QR kòd sa"}, "unlock": {"message": "Deb<PERSON>ke"}, "urlErrorMsg": {"message": "URIs mande pou apwopriye prefiks HTTP / HTTPS a."}, "usedByClients": {"message": "Itilize pa yon varyete de kliyan diferan"}, "visitWebSite": {"message": "Vizite sit entènèt nou an"}, "welcomeBack": {"message": "Bon Retou!"}, "youNeedToAllowCameraAccess": {"message": "Ou bezwen bay kamera aksè pou sèvi ak fonksyon sa."}, "yourPrivateSeedPhrase": {"message": "Seed fraz prive ou a"}}