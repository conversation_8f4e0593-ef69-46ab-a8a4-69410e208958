const { chromium } = require('playwright');

async function testPaymentAmountFix() {
    console.log('🔍 测试支付金额修复...');
    
    const browser = await chromium.launch({
        headless: false,
        args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions-except=./metamask_extension',
            '--load-extension=./metamask_extension'
        ]
    });

    const context = await browser.newContext({
        permissions: ['clipboard-read', 'clipboard-write']
    });

    const page = await context.newPage();

    try {
        // 1. 访问支付页面
        console.log('📱 访问支付页面...');
        await page.goto('http://127.0.0.1:8000/orders/ORD-20250727-001/payment/');
        await page.waitForTimeout(3000);

        // 2. 监听控制台日志，查看支付数据
        page.on('console', msg => {
            const text = msg.text();
            if (text.includes('支付数据') || text.includes('Payment') || text.includes('代币') || text.includes('金额')) {
                console.log('🔍 Console:', text);
            }
        });

        // 3. 选择NowPayments支付方式
        console.log('💳 选择NowPayments支付方式...');
        await page.click('[data-payment-method="nowpayments"]');
        await page.waitForTimeout(2000);

        // 4. 选择USDT币种
        console.log('💰 选择USDT币种...');
        const usdtOption = await page.locator('[data-crypto="usdterc20"], [data-crypto="USDTERC20"]').first();
        if (await usdtOption.count() > 0) {
            await usdtOption.click();
            console.log('✅ 选择了USDTERC20');
        } else {
            // 尝试其他USDT选项
            const usdtOptions = await page.locator('[data-crypto*="usdt"], [data-crypto*="USDT"]').all();
            if (usdtOptions.length > 0) {
                await usdtOptions[0].click();
                console.log('✅ 选择了第一个USDT选项');
            } else {
                console.log('❌ 未找到USDT选项');
                return;
            }
        }
        await page.waitForTimeout(2000);

        // 5. 点击支付按钮
        console.log('🔄 点击支付按钮...');
        await page.click('#pay-button');
        await page.waitForTimeout(5000);

        // 6. 等待支付详情显示
        console.log('⏳ 等待支付详情显示...');
        await page.waitForSelector('.payment-card', { timeout: 10000 });

        // 7. 检查支付金额显示
        const amountInput = await page.locator('input[value*="15"]').first();
        if (await amountInput.count() > 0) {
            const amountValue = await amountInput.getAttribute('value');
            console.log('✅ 支付金额显示:', amountValue);
            
            if (amountValue.includes('15') && !amountValue.includes('0 ')) {
                console.log('✅ 支付金额正确显示');
            } else {
                console.log('❌ 支付金额显示异常:', amountValue);
            }
        } else {
            console.log('❌ 未找到支付金额显示');
        }

        // 8. 查找钱包连接按钮
        const walletButtons = await page.locator('button:has-text("Connect"), button:has-text("连接"), .wallet-connect-btn').all();
        if (walletButtons.length > 0) {
            console.log('💼 找到钱包连接按钮，点击测试...');
            await walletButtons[0].click();
            await page.waitForTimeout(3000);

            // 检查是否有错误信息
            const errorMessages = await page.locator('.alert-danger, .error-message').all();
            if (errorMessages.length > 0) {
                for (const error of errorMessages) {
                    const errorText = await error.textContent();
                    console.log('❌ 错误信息:', errorText);
                }
            } else {
                console.log('✅ 没有发现错误信息');
            }
        }

        // 9. 检查控制台是否有关键调试信息
        await page.waitForTimeout(2000);
        console.log('🔍 测试完成，检查控制台日志以获取详细信息');

    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await page.waitForTimeout(5000);
        await browser.close();
    }
}

// 运行测试
testPaymentAmountFix().catch(console.error);
