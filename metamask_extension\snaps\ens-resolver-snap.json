{"snapId": "npm:@metamask/ens-resolver-snap", "manifest": {"version": "0.1.2", "description": "A Snap used for ENS name resolution", "proposedName": "Ethereum Name Service resolver", "repository": {"type": "git", "url": "https://github.com/MetaMask/ens-resolver-snap.git"}, "source": {"shasum": "BizRmzfV+oKEIlvph12McsIqzzDECIw/Td7Lx+/cios=", "location": {"npm": {"filePath": "dist/bundle.js", "iconPath": "images/icon.svg", "packageName": "@metamask/ens-resolver-snap", "registry": "https://registry.npmjs.org/"}}}, "initialPermissions": {"endowment:name-lookup": {}, "endowment:network-access": {}, "endowment:ethereum-provider": {}}, "manifestVersion": "0.1"}, "files": [{"path": "images/icon.svg", "value": "<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" height=\"3rem\">\n  <path fill=\"url(#ENSWithGradient_svg__a)\"\n        d=\"M4.049 5.663a2.9 2.9 0 0 1 .942-1.038L11.611 0 4.828 11.222s-.592-1.002-.823-1.51a4.8 4.8 0 0 1 .044-4.05m-2.732 7.74a7.55 7.55 0 0 0 2.963 5.489L11.602 24s-4.58-6.606-8.445-13.178a6.6 6.6 0 0 1-.776-2.242 3.6 3.6 0 0 1 0-1.076c-.101.187-.297.57-.297.57a8.7 8.7 0 0 0-.79 2.534c-.077.93-.07 1.867.023 2.796m18.668.89c-.237-.507-.824-1.509-.824-1.509L12.391 24l6.62-4.622c.394-.263.717-.62.942-1.038a4.8 4.8 0 0 0 .044-4.05zm2.688-3.694a7.55 7.55 0 0 0-2.963-5.488L12.4 0s4.577 6.606 8.445 13.179a6.6 6.6 0 0 1 .773 2.24c.054.358.054.72 0 1.077.1-.187.296-.57.296-.57.392-.799.659-1.654.791-2.534.078-.93.07-1.866-.02-2.796z\"/>\n  <defs>\n    <linearGradient id=\"ENSWithGradient_svg__a\" x1=\"15.574\" x2=\"-8.982\" y1=\"26.845\" y2=\"-11.906\"\n                    gradientUnits=\"userSpaceOnUse\">\n      <stop stop-color=\"#44BCF0\"/>\n      <stop offset=\"0.428\" stop-color=\"#628BF3\"/>\n      <stop offset=\"1\" stop-color=\"#A099FF\"/>\n    </linearGradient>\n  </defs>\n</svg>\n"}, {"path": "dist/bundle.js", "value": "(()=>{\"use strict\";var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})}},e={};t.r(e),t.d(e,{onNameLookup:()=>ol});var n={};t.r(n),t.d(n,{OG:()=>po,My:()=>so,bytesToNumberBE:()=>ao,lX:()=>co,Id:()=>fo,fg:()=>yo,qj:()=>ho,hexToBytes:()=>oo,lq:()=>lo,z:()=>uo,Q5:()=>bo});const r=\"6.13.1\";function s(t,e,n){const r=e.split(\"|\").map((t=>t.trim()));for(let n=0;n<r.length;n++)switch(e){case\"any\":return;case\"bigint\":case\"boolean\":case\"number\":case\"string\":if(typeof t===e)return}const s=new Error(`invalid value for type ${e}`);throw s.code=\"INVALID_ARGUMENT\",s.argument=`value.${n}`,s.value=t,s}async function i(t){const e=Object.keys(t);return(await Promise.all(e.map((e=>Promise.resolve(t[e]))))).reduce(((t,n,r)=>(t[e[r]]=n,t)),{})}function o(t,e,n){for(let r in e){let i=e[r];const o=n?n[r]:null;o&&s(i,o,r),Object.defineProperty(t,r,{enumerable:!0,value:i,writable:!1})}}function a(t){if(null==t)return\"null\";if(Array.isArray(t))return\"[ \"+t.map(a).join(\", \")+\" ]\";if(t instanceof Uint8Array){const e=\"0123456789abcdef\";let n=\"0x\";for(let r=0;r<t.length;r++)n+=e[t[r]>>4],n+=e[15&t[r]];return n}if(\"object\"==typeof t&&\"function\"==typeof t.toJSON)return a(t.toJSON());switch(typeof t){case\"boolean\":case\"symbol\":case\"number\":return t.toString();case\"bigint\":return BigInt(t).toString();case\"string\":return JSON.stringify(t);case\"object\":{const e=Object.keys(t);return e.sort(),\"{ \"+e.map((e=>`${a(e)}: ${a(t[e])}`)).join(\", \")+\" }\"}}return\"[ COULD NOT SERIALIZE ]\"}function c(t,e){return t&&t.code===e}function l(t){return c(t,\"CALL_EXCEPTION\")}function u(t,e,n){let s,i=t;{const s=[];if(n){if(\"message\"in n||\"code\"in n||\"name\"in n)throw new Error(`value will overwrite populated values: ${a(n)}`);for(const t in n){if(\"shortMessage\"===t)continue;const e=n[t];s.push(t+\"=\"+a(e))}}s.push(`code=${e}`),s.push(`version=${r}`),s.length&&(t+=\" (\"+s.join(\", \")+\")\")}switch(e){case\"INVALID_ARGUMENT\":s=new TypeError(t);break;case\"NUMERIC_FAULT\":case\"BUFFER_OVERRUN\":s=new RangeError(t);break;default:s=new Error(t)}return o(s,{code:e}),n&&Object.assign(s,n),null==s.shortMessage&&o(s,{shortMessage:i}),s}function h(t,e,n,r){if(!t)throw u(e,n,r)}function f(t,e,n,r){h(t,e,\"INVALID_ARGUMENT\",{argument:n,value:r})}function d(t,e,n){null==n&&(n=\"\"),n&&(n=\": \"+n),h(t>=e,\"missing arguemnt\"+n,\"MISSING_ARGUMENT\",{count:t,expectedCount:e}),h(t<=e,\"too many arguments\"+n,\"UNEXPECTED_ARGUMENT\",{count:t,expectedCount:e})}const p=[\"NFD\",\"NFC\",\"NFKD\",\"NFKC\"].reduce(((t,e)=>{try{if(\"test\"!==\"test\".normalize(e))throw new Error(\"bad\");if(\"NFD\"===e){const t=String.fromCharCode(233).normalize(\"NFD\");if(t!==String.fromCharCode(101,769))throw new Error(\"broken\")}t.push(e)}catch(t){}return t}),[]);function g(t,e,n){if(null==n&&(n=\"\"),t!==e){let t=n,e=\"new\";n&&(t+=\".\",e+=\" \"+n),h(!1,`private constructor; use ${t}from* methods`,\"UNSUPPORTED_OPERATION\",{operation:e})}}function m(t,e,n){if(t instanceof Uint8Array)return n?new Uint8Array(t):t;if(\"string\"==typeof t&&t.match(/^0x(?:[0-9a-f][0-9a-f])*$/i)){const e=new Uint8Array((t.length-2)/2);let n=2;for(let r=0;r<e.length;r++)e[r]=parseInt(t.substring(n,n+2),16),n+=2;return e}f(!1,\"invalid BytesLike value\",e||\"value\",t)}function y(t,e){return m(t,e,!1)}function w(t,e){return m(t,e,!0)}function b(t,e){return!(\"string\"!=typeof t||!t.match(/^0x[0-9A-Fa-f]*$/))&&((\"number\"!=typeof e||t.length===2+2*e)&&(!0!==e||t.length%2==0))}function A(t){return b(t,!0)||t instanceof Uint8Array}const v=\"0123456789abcdef\";function E(t){const e=y(t);let n=\"0x\";for(let t=0;t<e.length;t++){const r=e[t];n+=v[(240&r)>>4]+v[15&r]}return n}function k(t){return\"0x\"+t.map((t=>E(t).substring(2))).join(\"\")}function P(t){return b(t,!0)?(t.length-2)/2:y(t).length}function x(t,e,n){const r=y(t);return null!=n&&n>r.length&&h(!1,\"cannot slice beyond data bounds\",\"BUFFER_OVERRUN\",{buffer:r,length:r.length,offset:n}),E(r.slice(null==e?0:e,null==n?r.length:n))}function N(t,e,n){const r=y(t);h(e>=r.length,\"padding exceeds data length\",\"BUFFER_OVERRUN\",{buffer:new Uint8Array(r),length:e,offset:e+1});const s=new Uint8Array(e);return s.fill(0),n?s.set(r,e-r.length):s.set(r,0),E(s)}function B(t,e){return N(t,e,!0)}const I=BigInt(0),C=BigInt(1),O=9007199254740991;function R(t,e){let n=S(t,\"value\");const r=BigInt(L(e,\"width\")),s=C<<r-C;if(n<I){n=-n,h(n<=s,\"too low\",\"NUMERIC_FAULT\",{operation:\"toTwos\",fault:\"overflow\",value:t});return(~n&(C<<r)-C)+C}return h(n<s,\"too high\",\"NUMERIC_FAULT\",{operation:\"toTwos\",fault:\"overflow\",value:t}),n}function T(t,e){const n=F(t,\"value\"),r=BigInt(L(e,\"bits\"));return n&(C<<r)-C}function S(t,e){switch(typeof t){case\"bigint\":return t;case\"number\":return f(Number.isInteger(t),\"underflow\",e||\"value\",t),f(t>=-O&&t<=O,\"overflow\",e||\"value\",t),BigInt(t);case\"string\":try{if(\"\"===t)throw new Error(\"empty string\");return\"-\"===t[0]&&\"-\"!==t[1]?-BigInt(t.substring(1)):BigInt(t)}catch(n){f(!1,`invalid BigNumberish string: ${n.message}`,e||\"value\",t)}}f(!1,\"invalid BigNumberish value\",e||\"value\",t)}function F(t,e){const n=S(t,e);return h(n>=I,\"unsigned value cannot be negative\",\"NUMERIC_FAULT\",{fault:\"overflow\",operation:\"getUint\",value:t}),n}const U=\"0123456789abcdef\";function D(t){if(t instanceof Uint8Array){let e=\"0x0\";for(const n of t)e+=U[n>>4],e+=U[15&n];return BigInt(e)}return S(t)}function L(t,e){switch(typeof t){case\"bigint\":return f(t>=-O&&t<=O,\"overflow\",e||\"value\",t),Number(t);case\"number\":return f(Number.isInteger(t),\"underflow\",e||\"value\",t),f(t>=-O&&t<=O,\"overflow\",e||\"value\",t),t;case\"string\":try{if(\"\"===t)throw new Error(\"empty string\");return L(BigInt(t),e)}catch(n){f(!1,`invalid numeric string: ${n.message}`,e||\"value\",t)}}f(!1,\"invalid numeric value\",e||\"value\",t)}function M(t,e){let n=F(t,\"value\").toString(16);if(null==e)n.length%2&&(n=\"0\"+n);else{const r=L(e,\"width\");for(h(2*r>=n.length,`value exceeds width (${r} bytes)`,\"NUMERIC_FAULT\",{operation:\"toBeHex\",fault:\"overflow\",value:t});n.length<2*r;)n=\"0\"+n}return\"0x\"+n}function G(t){const e=F(t,\"value\");if(e===I)return new Uint8Array([]);let n=e.toString(16);n.length%2&&(n=\"0\"+n);const r=new Uint8Array(n.length/2);for(let t=0;t<r.length;t++){const e=2*t;r[t]=parseInt(n.substring(e,e+2),16)}return r}function H(t){let e=E(A(t)?t:G(t)).substring(2);for(;e.startsWith(\"0\");)e=e.substring(1);return\"\"===e&&(e=\"0\"),\"0x\"+e}const Q=32,j=new Uint8Array(Q),V=[\"then\"],J={},z=new WeakMap;function K(t){return z.get(t)}function q(t,e){z.set(t,e)}function _(t,e){const n=new Error(`deferred error during ABI decoding triggered accessing ${t}`);throw n.error=e,n}function Z(t,e,n){return t.indexOf(null)>=0?e.map(((t,e)=>t instanceof W?Z(K(t),t,n):t)):t.reduce(((t,r,s)=>{let i=e.getValue(r);return r in t||(n&&i instanceof W&&(i=Z(K(i),i,n)),t[r]=i),t}),{})}class W extends Array{#t;constructor(...t){const e=t[0];let n=t[1],r=(t[2]||[]).slice(),s=!0;e!==J&&(n=t,r=[],s=!1),super(n.length),n.forEach(((t,e)=>{this[e]=t}));const i=r.reduce(((t,e)=>(\"string\"==typeof e&&t.set(e,(t.get(e)||0)+1),t)),new Map);if(q(this,Object.freeze(n.map(((t,e)=>{const n=r[e];return null!=n&&1===i.get(n)?n:null})))),this.#t=[],null==this.#t&&this.#t,!s)return;Object.freeze(this);const o=new Proxy(this,{get:(t,e,n)=>{if(\"string\"==typeof e){if(e.match(/^[0-9]+$/)){const n=L(e,\"%index\");if(n<0||n>=this.length)throw new RangeError(\"out of result range\");const r=t[n];return r instanceof Error&&_(`index ${n}`,r),r}if(V.indexOf(e)>=0)return Reflect.get(t,e,n);const r=t[e];if(r instanceof Function)return function(...e){return r.apply(this===n?t:this,e)};if(!(e in t))return t.getValue.apply(this===n?t:this,[e])}return Reflect.get(t,e,n)}});return q(o,K(this)),o}toArray(t){const e=[];return this.forEach(((n,r)=>{n instanceof Error&&_(`index ${r}`,n),t&&n instanceof W&&(n=n.toArray(t)),e.push(n)})),e}toObject(t){const e=K(this);return e.reduce(((n,r,s)=>(h(null!=r,`value at index ${s} unnamed`,\"UNSUPPORTED_OPERATION\",{operation:\"toObject()\"}),Z(e,this,t))),{})}slice(t,e){null==t&&(t=0),t<0&&(t+=this.length)<0&&(t=0),null==e&&(e=this.length),e<0&&(e+=this.length)<0&&(e=0),e>this.length&&(e=this.length);const n=K(this),r=[],s=[];for(let i=t;i<e;i++)r.push(this[i]),s.push(n[i]);return new W(J,r,s)}filter(t,e){const n=K(this),r=[],s=[];for(let i=0;i<this.length;i++){const o=this[i];o instanceof Error&&_(`index ${i}`,o),t.call(e,o,i,this)&&(r.push(o),s.push(n[i]))}return new W(J,r,s)}map(t,e){const n=[];for(let r=0;r<this.length;r++){const s=this[r];s instanceof Error&&_(`index ${r}`,s),n.push(t.call(e,s,r,this))}return n}getValue(t){const e=K(this).indexOf(t);if(-1===e)return;const n=this[e];return n instanceof Error&&_(`property ${JSON.stringify(t)}`,n.error),n}static fromItems(t,e){return new W(J,t,e)}}function Y(t){let e=G(t);return h(e.length<=Q,\"value out-of-bounds\",\"BUFFER_OVERRUN\",{buffer:e,length:Q,offset:e.length}),e.length!==Q&&(e=w(k([j.slice(e.length%Q),e]))),e}class X{name;type;localName;dynamic;constructor(t,e,n,r){o(this,{name:t,type:e,localName:n,dynamic:r},{name:\"string\",type:\"string\",localName:\"string\",dynamic:\"boolean\"})}_throwError(t,e){f(!1,t,this.localName,e)}}class ${#e;#n;constructor(){this.#e=[],this.#n=0}get data(){return k(this.#e)}get length(){return this.#n}#r(t){return this.#e.push(t),this.#n+=t.length,t.length}appendWriter(t){return this.#r(w(t.data))}writeBytes(t){let e=w(t);const n=e.length%Q;return n&&(e=w(k([e,j.slice(n)]))),this.#r(e)}writeValue(t){return this.#r(Y(t))}writeUpdatableValue(){const t=this.#e.length;return this.#e.push(j),this.#n+=Q,e=>{this.#e[t]=Y(e)}}}class tt{allowLoose;#e;#s;#i;#o;#a;constructor(t,e,n){o(this,{allowLoose:!!e}),this.#e=w(t),this.#i=0,this.#o=null,this.#a=null!=n?n:1024,this.#s=0}get data(){return E(this.#e)}get dataLength(){return this.#e.length}get consumed(){return this.#s}get bytes(){return new Uint8Array(this.#e)}#c(t){if(this.#o)return this.#o.#c(t);this.#i+=t,h(this.#a<1||this.#i<=this.#a*this.dataLength,`compressed ABI data exceeds inflation ratio of ${this.#a} ( see: https://github.com/ethers-io/ethers.js/issues/4537 )`,\"BUFFER_OVERRUN\",{buffer:w(this.#e),offset:this.#s,length:t,info:{bytesRead:this.#i,dataLength:this.dataLength}})}#l(t,e,n){let r=Math.ceil(e/Q)*Q;return this.#s+r>this.#e.length&&(this.allowLoose&&n&&this.#s+e<=this.#e.length?r=e:h(!1,\"data out-of-bounds\",\"BUFFER_OVERRUN\",{buffer:w(this.#e),length:this.#e.length,offset:this.#s+r})),this.#e.slice(this.#s,this.#s+r)}subReader(t){const e=new tt(this.#e.slice(this.#s+t),this.allowLoose,this.#a);return e.#o=this,e}readBytes(t,e){let n=this.#l(0,t,!!e);return this.#c(t),this.#s+=n.length,n.slice(0,t)}readValue(){return D(this.readBytes(Q))}readIndex(){return L(D(this.readBytes(Q)))}}function et(t){if(!Number.isSafeInteger(t)||t<0)throw new Error(`Wrong positive integer: ${t}`)}function nt(t,...e){if(!(t instanceof Uint8Array))throw new Error(\"Expected Uint8Array\");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}function rt(t){if(\"function\"!=typeof t||\"function\"!=typeof t.create)throw new Error(\"Hash should be wrapped by utils.wrapConstructor\");et(t.outputLen),et(t.blockLen)}function st(t,e=!0){if(t.destroyed)throw new Error(\"Hash instance has been destroyed\");if(e&&t.finished)throw new Error(\"Hash#digest() has already been called\")}function it(t,e){nt(t);const n=e.outputLen;if(t.length<n)throw new Error(`digestInto() expects output buffer of length at least ${n}`)}const ot=BigInt(2**32-1),at=BigInt(32);function ct(t,e=!1){return e?{h:Number(t&ot),l:Number(t>>at&ot)}:{h:0|Number(t>>at&ot),l:0|Number(t&ot)}}function lt(t,e=!1){let n=new Uint32Array(t.length),r=new Uint32Array(t.length);for(let s=0;s<t.length;s++){const{h:i,l:o}=ct(t[s],e);[n[s],r[s]]=[i,o]}return[n,r]}const ut=(t,e,n)=>t<<n|e>>>32-n,ht=(t,e,n)=>e<<n|t>>>32-n,ft=(t,e,n)=>e<<n-32|t>>>64-n,dt=(t,e,n)=>t<<n-32|e>>>64-n;const pt={fromBig:ct,split:lt,toBig:(t,e)=>BigInt(t>>>0)<<at|BigInt(e>>>0),shrSH:(t,e,n)=>t>>>n,shrSL:(t,e,n)=>t<<32-n|e>>>n,rotrSH:(t,e,n)=>t>>>n|e<<32-n,rotrSL:(t,e,n)=>t<<32-n|e>>>n,rotrBH:(t,e,n)=>t<<64-n|e>>>n-32,rotrBL:(t,e,n)=>t>>>n-32|e<<64-n,rotr32H:(t,e)=>e,rotr32L:(t,e)=>t,rotlSH:ut,rotlSL:ht,rotlBH:ft,rotlBL:dt,add:function(t,e,n,r){const s=(e>>>0)+(r>>>0);return{h:t+n+(s/2**32|0)|0,l:0|s}},add3L:(t,e,n)=>(t>>>0)+(e>>>0)+(n>>>0),add3H:(t,e,n,r)=>e+n+r+(t/2**32|0)|0,add4L:(t,e,n,r)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0),add4H:(t,e,n,r,s)=>e+n+r+s+(t/2**32|0)|0,add5H:(t,e,n,r,s,i)=>e+n+r+s+i+(t/2**32|0)|0,add5L:(t,e,n,r,s)=>(t>>>0)+(e>>>0)+(n>>>0)+(r>>>0)+(s>>>0)},gt=\"object\"==typeof globalThis&&\"crypto\"in globalThis?globalThis.crypto:void 0,mt=t=>t instanceof Uint8Array,yt=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),wt=(t,e)=>t<<32-e|t>>>e;if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error(\"Non little-endian hardware is not supported\");Array.from({length:256},((t,e)=>e.toString(16).padStart(2,\"0\")));function bt(t){if(\"string\"==typeof t&&(t=function(t){if(\"string\"!=typeof t)throw new Error(\"utf8ToBytes expected string, got \"+typeof t);return new Uint8Array((new TextEncoder).encode(t))}(t)),!mt(t))throw new Error(\"expected Uint8Array, got \"+typeof t);return t}class At{clone(){return this._cloneInto()}}function vt(t){const e=e=>t().update(bt(e)).digest(),n=t();return e.outputLen=n.outputLen,e.blockLen=n.blockLen,e.create=()=>t(),e}function Et(t=32){if(gt&&\"function\"==typeof gt.getRandomValues)return gt.getRandomValues(new Uint8Array(t));throw new Error(\"crypto.getRandomValues must be defined\")}const[kt,Pt,xt]=[[],[],[]],Nt=BigInt(0),Bt=BigInt(1),It=BigInt(2),Ct=BigInt(7),Ot=BigInt(256),Rt=BigInt(113);for(let t=0,e=Bt,n=1,r=0;t<24;t++){[n,r]=[r,(2*n+3*r)%5],kt.push(2*(5*r+n)),Pt.push((t+1)*(t+2)/2%64);let s=Nt;for(let t=0;t<7;t++)e=(e<<Bt^(e>>Ct)*Rt)%Ot,e&It&&(s^=Bt<<(Bt<<BigInt(t))-Bt);xt.push(s)}const[Tt,St]=lt(xt,!0),Ft=(t,e,n)=>n>32?ft(t,e,n):ut(t,e,n),Ut=(t,e,n)=>n>32?dt(t,e,n):ht(t,e,n);class Dt extends At{constructor(t,e,n,r=!1,s=24){if(super(),this.blockLen=t,this.suffix=e,this.outputLen=n,this.enableXOF=r,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,et(n),0>=this.blockLen||this.blockLen>=200)throw new Error(\"Sha3 supports only keccak-f1600 function\");var i;this.state=new Uint8Array(200),this.state32=(i=this.state,new Uint32Array(i.buffer,i.byteOffset,Math.floor(i.byteLength/4)))}keccak(){!function(t,e=24){const n=new Uint32Array(10);for(let r=24-e;r<24;r++){for(let e=0;e<10;e++)n[e]=t[e]^t[e+10]^t[e+20]^t[e+30]^t[e+40];for(let e=0;e<10;e+=2){const r=(e+8)%10,s=(e+2)%10,i=n[s],o=n[s+1],a=Ft(i,o,1)^n[r],c=Ut(i,o,1)^n[r+1];for(let n=0;n<50;n+=10)t[e+n]^=a,t[e+n+1]^=c}let e=t[2],s=t[3];for(let n=0;n<24;n++){const r=Pt[n],i=Ft(e,s,r),o=Ut(e,s,r),a=kt[n];e=t[a],s=t[a+1],t[a]=i,t[a+1]=o}for(let e=0;e<50;e+=10){for(let r=0;r<10;r++)n[r]=t[e+r];for(let r=0;r<10;r++)t[e+r]^=~n[(r+2)%10]&n[(r+4)%10]}t[0]^=Tt[r],t[1]^=St[r]}n.fill(0)}(this.state32,this.rounds),this.posOut=0,this.pos=0}update(t){st(this);const{blockLen:e,state:n}=this,r=(t=bt(t)).length;for(let s=0;s<r;){const i=Math.min(e-this.pos,r-s);for(let e=0;e<i;e++)n[this.pos++]^=t[s++];this.pos===e&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:e,pos:n,blockLen:r}=this;t[n]^=e,0!=(128&e)&&n===r-1&&this.keccak(),t[r-1]^=128,this.keccak()}writeInto(t){st(this,!1),nt(t),this.finish();const e=this.state,{blockLen:n}=this;for(let r=0,s=t.length;r<s;){this.posOut>=n&&this.keccak();const i=Math.min(n-this.posOut,s-r);t.set(e.subarray(this.posOut,this.posOut+i),r),this.posOut+=i,r+=i}return t}xofInto(t){if(!this.enableXOF)throw new Error(\"XOF is not possible for this instance\");return this.writeInto(t)}xof(t){return et(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(it(t,this),this.finished)throw new Error(\"digest() was already called\");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){const{blockLen:e,suffix:n,outputLen:r,rounds:s,enableXOF:i}=this;return t||(t=new Dt(e,n,r,i,s)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=s,t.suffix=n,t.outputLen=r,t.enableXOF=i,t.destroyed=this.destroyed,t}}const Lt=(t,e,n)=>vt((()=>new Dt(e,t,n))),Mt=Lt(1,136,32);let Gt=!1;const Ht=function(t){return Mt(t)};let Qt=Ht;function jt(t){const e=y(t,\"data\");return E(Qt(e))}jt._=Ht,jt.lock=function(){Gt=!0},jt.register=function(t){if(Gt)throw new TypeError(\"keccak256 is locked\");Qt=t},Object.freeze(jt);const Vt=BigInt(0),Jt=BigInt(36);function zt(t){const e=(t=t.toLowerCase()).substring(2).split(\"\"),n=new Uint8Array(40);for(let t=0;t<40;t++)n[t]=e[t].charCodeAt(0);const r=y(jt(n));for(let t=0;t<40;t+=2)r[t>>1]>>4>=8&&(e[t]=e[t].toUpperCase()),(15&r[t>>1])>=8&&(e[t+1]=e[t+1].toUpperCase());return\"0x\"+e.join(\"\")}const Kt={};for(let t=0;t<10;t++)Kt[String(t)]=String(t);for(let t=0;t<26;t++)Kt[String.fromCharCode(65+t)]=String(10+t);const qt=15;function _t(t){let e=(t=(t=t.toUpperCase()).substring(4)+t.substring(0,2)+\"00\").split(\"\").map((t=>Kt[t])).join(\"\");for(;e.length>=qt;){let t=e.substring(0,qt);e=parseInt(t,10)%97+e.substring(t.length)}let n=String(98-parseInt(e,10)%97);for(;n.length<2;)n=\"0\"+n;return n}const Zt=function(){const t={};for(let e=0;e<36;e++){t[\"0123456789abcdefghijklmnopqrstuvwxyz\"[e]]=BigInt(e)}return t}();function Wt(t){if(f(\"string\"==typeof t,\"invalid address\",\"address\",t),t.match(/^(0x)?[0-9a-fA-F]{40}$/)){t.startsWith(\"0x\")||(t=\"0x\"+t);const e=zt(t);return f(!t.match(/([A-F].*[a-f])|([a-f].*[A-F])/)||e===t,\"bad address checksum\",\"address\",t),e}if(t.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)){f(t.substring(2,4)===_t(t),\"bad icap checksum\",\"address\",t);let e=function(t){t=t.toLowerCase();let e=Vt;for(let n=0;n<t.length;n++)e=e*Jt+Zt[t[n]];return e}(t.substring(4)).toString(16);for(;e.length<40;)e=\"0\"+e;return zt(\"0x\"+e)}f(!1,\"invalid address\",\"address\",t)}const Yt={};function Xt(t,e){let n=!1;return e<0&&(n=!0,e*=-1),new ee(Yt,`${n?\"\":\"u\"}int${e}`,t,{signed:n,width:e})}function $t(t,e){return new ee(Yt,`bytes${e||\"\"}`,t,{size:e})}const te=Symbol.for(\"_ethers_typed\");class ee{type;value;#u;_typedSymbol;constructor(t,e,n,r){null==r&&(r=null),g(Yt,t,\"Typed\"),o(this,{_typedSymbol:te,type:e,value:n}),this.#u=r,this.format()}format(){if(\"array\"===this.type)throw new Error(\"\");if(\"dynamicArray\"===this.type)throw new Error(\"\");return\"tuple\"===this.type?`tuple(${this.value.map((t=>t.format())).join(\",\")})`:this.type}defaultValue(){return 0}minValue(){return 0}maxValue(){return 0}isBigInt(){return!!this.type.match(/^u?int[0-9]+$/)}isData(){return this.type.startsWith(\"bytes\")}isString(){return\"string\"===this.type}get tupleName(){if(\"tuple\"!==this.type)throw TypeError(\"not a tuple\");return this.#u}get arrayLength(){if(\"array\"!==this.type)throw TypeError(\"not an array\");return!0===this.#u?-1:!1===this.#u?this.value.length:null}static from(t,e){return new ee(Yt,t,e)}static uint8(t){return Xt(t,8)}static uint16(t){return Xt(t,16)}static uint24(t){return Xt(t,24)}static uint32(t){return Xt(t,32)}static uint40(t){return Xt(t,40)}static uint48(t){return Xt(t,48)}static uint56(t){return Xt(t,56)}static uint64(t){return Xt(t,64)}static uint72(t){return Xt(t,72)}static uint80(t){return Xt(t,80)}static uint88(t){return Xt(t,88)}static uint96(t){return Xt(t,96)}static uint104(t){return Xt(t,104)}static uint112(t){return Xt(t,112)}static uint120(t){return Xt(t,120)}static uint128(t){return Xt(t,128)}static uint136(t){return Xt(t,136)}static uint144(t){return Xt(t,144)}static uint152(t){return Xt(t,152)}static uint160(t){return Xt(t,160)}static uint168(t){return Xt(t,168)}static uint176(t){return Xt(t,176)}static uint184(t){return Xt(t,184)}static uint192(t){return Xt(t,192)}static uint200(t){return Xt(t,200)}static uint208(t){return Xt(t,208)}static uint216(t){return Xt(t,216)}static uint224(t){return Xt(t,224)}static uint232(t){return Xt(t,232)}static uint240(t){return Xt(t,240)}static uint248(t){return Xt(t,248)}static uint256(t){return Xt(t,256)}static uint(t){return Xt(t,256)}static int8(t){return Xt(t,-8)}static int16(t){return Xt(t,-16)}static int24(t){return Xt(t,-24)}static int32(t){return Xt(t,-32)}static int40(t){return Xt(t,-40)}static int48(t){return Xt(t,-48)}static int56(t){return Xt(t,-56)}static int64(t){return Xt(t,-64)}static int72(t){return Xt(t,-72)}static int80(t){return Xt(t,-80)}static int88(t){return Xt(t,-88)}static int96(t){return Xt(t,-96)}static int104(t){return Xt(t,-104)}static int112(t){return Xt(t,-112)}static int120(t){return Xt(t,-120)}static int128(t){return Xt(t,-128)}static int136(t){return Xt(t,-136)}static int144(t){return Xt(t,-144)}static int152(t){return Xt(t,-152)}static int160(t){return Xt(t,-160)}static int168(t){return Xt(t,-168)}static int176(t){return Xt(t,-176)}static int184(t){return Xt(t,-184)}static int192(t){return Xt(t,-192)}static int200(t){return Xt(t,-200)}static int208(t){return Xt(t,-208)}static int216(t){return Xt(t,-216)}static int224(t){return Xt(t,-224)}static int232(t){return Xt(t,-232)}static int240(t){return Xt(t,-240)}static int248(t){return Xt(t,-248)}static int256(t){return Xt(t,-256)}static int(t){return Xt(t,-256)}static bytes1(t){return $t(t,1)}static bytes2(t){return $t(t,2)}static bytes3(t){return $t(t,3)}static bytes4(t){return $t(t,4)}static bytes5(t){return $t(t,5)}static bytes6(t){return $t(t,6)}static bytes7(t){return $t(t,7)}static bytes8(t){return $t(t,8)}static bytes9(t){return $t(t,9)}static bytes10(t){return $t(t,10)}static bytes11(t){return $t(t,11)}static bytes12(t){return $t(t,12)}static bytes13(t){return $t(t,13)}static bytes14(t){return $t(t,14)}static bytes15(t){return $t(t,15)}static bytes16(t){return $t(t,16)}static bytes17(t){return $t(t,17)}static bytes18(t){return $t(t,18)}static bytes19(t){return $t(t,19)}static bytes20(t){return $t(t,20)}static bytes21(t){return $t(t,21)}static bytes22(t){return $t(t,22)}static bytes23(t){return $t(t,23)}static bytes24(t){return $t(t,24)}static bytes25(t){return $t(t,25)}static bytes26(t){return $t(t,26)}static bytes27(t){return $t(t,27)}static bytes28(t){return $t(t,28)}static bytes29(t){return $t(t,29)}static bytes30(t){return $t(t,30)}static bytes31(t){return $t(t,31)}static bytes32(t){return $t(t,32)}static address(t){return new ee(Yt,\"address\",t)}static bool(t){return new ee(Yt,\"bool\",!!t)}static bytes(t){return new ee(Yt,\"bytes\",t)}static string(t){return new ee(Yt,\"string\",t)}static array(t,e){throw new Error(\"not implemented yet\")}static tuple(t,e){throw new Error(\"not implemented yet\")}static overrides(t){return new ee(Yt,\"overrides\",Object.assign({},t))}static isTyped(t){return t&&\"object\"==typeof t&&\"_typedSymbol\"in t&&t._typedSymbol===te}static dereference(t,e){if(ee.isTyped(t)){if(t.type!==e)throw new Error(`invalid type: expecetd ${e}, got ${t.type}`);return t.value}return t}}class ne extends X{constructor(t){super(\"address\",\"address\",t,!1)}defaultValue(){return\"******************************************\"}encode(t,e){let n=ee.dereference(e,\"string\");try{n=Wt(n)}catch(t){return this._throwError(t.message,e)}return t.writeValue(n)}decode(t){return Wt(M(t.readValue(),20))}}class re extends X{coder;constructor(t){super(t.name,t.type,\"_\",t.dynamic),this.coder=t}defaultValue(){return this.coder.defaultValue()}encode(t,e){return this.coder.encode(t,e)}decode(t){return this.coder.decode(t)}}function se(t,e,n){let r=[];if(Array.isArray(n))r=n;else if(n&&\"object\"==typeof n){let t={};r=e.map((e=>{const r=e.localName;return h(r,\"cannot encode object for signature with missing names\",\"INVALID_ARGUMENT\",{argument:\"values\",info:{coder:e},value:n}),h(!t[r],\"cannot encode object for signature with duplicate names\",\"INVALID_ARGUMENT\",{argument:\"values\",info:{coder:e},value:n}),t[r]=!0,n[r]}))}else f(!1,\"invalid tuple value\",\"tuple\",n);f(e.length===r.length,\"types/value length mismatch\",\"tuple\",n);let s=new $,i=new $,o=[];e.forEach(((t,e)=>{let n=r[e];if(t.dynamic){let e=i.length;t.encode(i,n);let r=s.writeUpdatableValue();o.push((t=>{r(t+e)}))}else t.encode(s,n)})),o.forEach((t=>{t(s.length)}));let a=t.appendWriter(s);return a+=t.appendWriter(i),a}function ie(t,e){let n=[],r=[],s=t.subReader(0);return e.forEach((e=>{let i=null;if(e.dynamic){let n=t.readIndex(),r=s.subReader(n);try{i=e.decode(r)}catch(t){if(c(t,\"BUFFER_OVERRUN\"))throw t;i=t,i.baseType=e.name,i.name=e.localName,i.type=e.type}}else try{i=e.decode(t)}catch(t){if(c(t,\"BUFFER_OVERRUN\"))throw t;i=t,i.baseType=e.name,i.name=e.localName,i.type=e.type}if(null==i)throw new Error(\"investigate\");n.push(i),r.push(e.localName||null)})),W.fromItems(n,r)}class oe extends X{coder;length;constructor(t,e,n){super(\"array\",t.type+\"[\"+(e>=0?e:\"\")+\"]\",n,-1===e||t.dynamic),o(this,{coder:t,length:e})}defaultValue(){const t=this.coder.defaultValue(),e=[];for(let n=0;n<this.length;n++)e.push(t);return e}encode(t,e){const n=ee.dereference(e,\"array\");Array.isArray(n)||this._throwError(\"expected array value\",n);let r=this.length;-1===r&&(r=n.length,t.writeValue(n.length)),d(n.length,r,\"coder array\"+(this.localName?\" \"+this.localName:\"\"));let s=[];for(let t=0;t<n.length;t++)s.push(this.coder);return se(t,s,n)}decode(t){let e=this.length;-1===e&&(e=t.readIndex(),h(e*Q<=t.dataLength,\"insufficient data length\",\"BUFFER_OVERRUN\",{buffer:t.bytes,offset:e*Q,length:t.dataLength}));let n=[];for(let t=0;t<e;t++)n.push(new re(this.coder));return ie(t,n)}}class ae extends X{constructor(t){super(\"bool\",\"bool\",t,!1)}defaultValue(){return!1}encode(t,e){const n=ee.dereference(e,\"bool\");return t.writeValue(n?1:0)}decode(t){return!!t.readValue()}}class ce extends X{constructor(t,e){super(t,t,e,!0)}defaultValue(){return\"0x\"}encode(t,e){e=w(e);let n=t.writeValue(e.length);return n+=t.writeBytes(e),n}decode(t){return t.readBytes(t.readIndex(),!0)}}class le extends ce{constructor(t){super(\"bytes\",t)}decode(t){return E(super.decode(t))}}class ue extends X{size;constructor(t,e){let n=\"bytes\"+String(t);super(n,n,e,!1),o(this,{size:t},{size:\"number\"})}defaultValue(){return\"******************************************000000000000000000000000\".substring(0,2+2*this.size)}encode(t,e){let n=w(ee.dereference(e,this.type));return n.length!==this.size&&this._throwError(\"incorrect data length\",e),t.writeBytes(n)}decode(t){return E(t.readBytes(this.size))}}const he=new Uint8Array([]);class fe extends X{constructor(t){super(\"null\",\"\",t,!1)}defaultValue(){return null}encode(t,e){return null!=e&&this._throwError(\"not null\",e),t.writeBytes(he)}decode(t){return t.readBytes(0),null}}const de=BigInt(0),pe=BigInt(1),ge=BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");class me extends X{size;signed;constructor(t,e,n){const r=(e?\"int\":\"uint\")+8*t;super(r,r,n,!1),o(this,{size:t,signed:e},{size:\"number\",signed:\"boolean\"})}defaultValue(){return 0}encode(t,e){let n=S(ee.dereference(e,this.type)),r=T(ge,256);if(this.signed){let t=T(r,8*this.size-1);(n>t||n<-(t+pe))&&this._throwError(\"value out-of-bounds\",e),n=R(n,256)}else(n<de||n>T(r,8*this.size))&&this._throwError(\"value out-of-bounds\",e);return t.writeValue(n)}decode(t){let e=T(t.readValue(),8*this.size);return this.signed&&(e=function(t,e){const n=F(t,\"value\"),r=BigInt(L(e,\"width\"));if(h(n>>r===I,\"overflow\",\"NUMERIC_FAULT\",{operation:\"fromTwos\",fault:\"overflow\",value:t}),n>>r-C)return-((~n&(C<<r)-C)+C);return n}(e,8*this.size)),e}}function ye(t,e,n,r,s){if(\"BAD_PREFIX\"===t||\"UNEXPECTED_CONTINUE\"===t){let t=0;for(let r=e+1;r<n.length&&n[r]>>6==2;r++)t++;return t}return\"OVERRUN\"===t?n.length-e-1:0}const we=Object.freeze({error:function(t,e,n,r,s){f(!1,`invalid codepoint at offset ${e}; ${t}`,\"bytes\",n)},ignore:ye,replace:function(t,e,n,r,s){return\"OVERLONG\"===t?(f(\"number\"==typeof s,\"invalid bad code point for replacement\",\"badCodepoint\",s),r.push(s),0):(r.push(65533),ye(t,e,n))}});function be(t,e){null==e&&(e=we.error);const n=y(t,\"bytes\"),r=[];let s=0;for(;s<n.length;){const t=n[s++];if(t>>7==0){r.push(t);continue}let i=null,o=null;if(192==(224&t))i=1,o=127;else if(224==(240&t))i=2,o=2047;else{if(240!=(248&t)){s+=e(128==(192&t)?\"UNEXPECTED_CONTINUE\":\"BAD_PREFIX\",s-1,n,r);continue}i=3,o=65535}if(s-1+i>=n.length){s+=e(\"OVERRUN\",s-1,n,r);continue}let a=t&(1<<8-i-1)-1;for(let t=0;t<i;t++){let t=n[s];if(128!=(192&t)){s+=e(\"MISSING_CONTINUE\",s,n,r),a=null;break}a=a<<6|63&t,s++}null!==a&&(a>1114111?s+=e(\"OUT_OF_RANGE\",s-1-i,n,r,a):a>=55296&&a<=57343?s+=e(\"UTF16_SURROGATE\",s-1-i,n,r,a):a<=o?s+=e(\"OVERLONG\",s-1-i,n,r,a):r.push(a))}return r}function Ae(t,e){f(\"string\"==typeof t,\"invalid string value\",\"str\",t),null!=e&&(!function(t){h(p.indexOf(t)>=0,\"platform missing String.prototype.normalize\",\"UNSUPPORTED_OPERATION\",{operation:\"String.prototype.normalize\",info:{form:t}})}(e),t=t.normalize(e));let n=[];for(let e=0;e<t.length;e++){const r=t.charCodeAt(e);if(r<128)n.push(r);else if(r<2048)n.push(r>>6|192),n.push(63&r|128);else if(55296==(64512&r)){e++;const s=t.charCodeAt(e);f(e<t.length&&56320==(64512&s),\"invalid surrogate pair\",\"str\",t);const i=65536+((1023&r)<<10)+(1023&s);n.push(i>>18|240),n.push(i>>12&63|128),n.push(i>>6&63|128),n.push(63&i|128)}else n.push(r>>12|224),n.push(r>>6&63|128),n.push(63&r|128)}return new Uint8Array(n)}function ve(t,e){return be(t,e).map((t=>t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10&1023),56320+(1023&t))))).join(\"\")}class Ee extends ce{constructor(t){super(\"string\",t)}defaultValue(){return\"\"}encode(t,e){return super.encode(t,Ae(ee.dereference(e,\"string\")))}decode(t){return ve(super.decode(t))}}class ke extends X{coders;constructor(t,e){let n=!1;const r=[];t.forEach((t=>{t.dynamic&&(n=!0),r.push(t.type)}));super(\"tuple\",\"tuple(\"+r.join(\",\")+\")\",e,n),o(this,{coders:Object.freeze(t.slice())})}defaultValue(){const t=[];this.coders.forEach((e=>{t.push(e.defaultValue())}));const e=this.coders.reduce(((t,e)=>{const n=e.localName;return n&&(t[n]||(t[n]=0),t[n]++),t}),{});return this.coders.forEach(((n,r)=>{let s=n.localName;s&&1===e[s]&&(\"length\"===s&&(s=\"_length\"),null==t[s]&&(t[s]=t[r]))})),Object.freeze(t)}encode(t,e){const n=ee.dereference(e,\"tuple\");return se(t,this.coders,n)}decode(t){return ie(t,this.coders)}}function Pe(t){return jt(Ae(t))}function xe(t){const e=new Set;return t.forEach((t=>e.add(t))),Object.freeze(e)}const Ne=xe(\"external public payable override\".split(\" \")),Be=\"constant external internal payable private public pure view override\",Ie=xe(Be.split(\" \")),Ce=\"constructor error event fallback function receive struct\",Oe=xe(Ce.split(\" \")),Re=\"calldata memory storage payable indexed\",Te=xe(Re.split(\" \")),Se=xe([Ce,Re,\"tuple returns\",Be].join(\" \").split(\" \")),Fe={\"(\":\"OPEN_PAREN\",\")\":\"CLOSE_PAREN\",\"[\":\"OPEN_BRACKET\",\"]\":\"CLOSE_BRACKET\",\",\":\"COMMA\",\"@\":\"AT\"},Ue=new RegExp(\"^(\\\\s*)\"),De=new RegExp(\"^([0-9]+)\"),Le=new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)\"),Me=new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)$\"),Ge=new RegExp(\"^(address|bool|bytes([0-9]*)|string|u?int([0-9]*))$\");class He{#s;#h;get offset(){return this.#s}get length(){return this.#h.length-this.#s}constructor(t){this.#s=0,this.#h=t.slice()}clone(){return new He(this.#h)}reset(){this.#s=0}#f(t=0,e=0){return new He(this.#h.slice(t,e).map((e=>Object.freeze(Object.assign({},e,{match:e.match-t,linkBack:e.linkBack-t,linkNext:e.linkNext-t})))))}popKeyword(t){const e=this.peek();if(\"KEYWORD\"!==e.type||!t.has(e.text))throw new Error(`expected keyword ${e.text}`);return this.pop().text}popType(t){if(this.peek().type!==t){const e=this.peek();throw new Error(`expected ${t}; got ${e.type} ${JSON.stringify(e.text)}`)}return this.pop().text}popParen(){const t=this.peek();if(\"OPEN_PAREN\"!==t.type)throw new Error(\"bad start\");const e=this.#f(this.#s+1,t.match+1);return this.#s=t.match+1,e}popParams(){const t=this.peek();if(\"OPEN_PAREN\"!==t.type)throw new Error(\"bad start\");const e=[];for(;this.#s<t.match-1;){const t=this.peek().linkNext;e.push(this.#f(this.#s+1,t)),this.#s=t}return this.#s=t.match+1,e}peek(){if(this.#s>=this.#h.length)throw new Error(\"out-of-bounds\");return this.#h[this.#s]}peekKeyword(t){const e=this.peekType(\"KEYWORD\");return null!=e&&t.has(e)?e:null}peekType(t){if(0===this.length)return null;const e=this.peek();return e.type===t?e.text:null}pop(){const t=this.peek();return this.#s++,t}toString(){const t=[];for(let e=this.#s;e<this.#h.length;e++){const n=this.#h[e];t.push(`${n.type}:${n.text}`)}return`<TokenString ${t.join(\" \")}>`}}function Qe(t){const e=[],n=e=>{const n=i<t.length?JSON.stringify(t[i]):\"$EOI\";throw new Error(`invalid token ${n} at ${i}: ${e}`)};let r=[],s=[],i=0;for(;i<t.length;){let o=t.substring(i),a=o.match(Ue);a&&(i+=a[1].length,o=t.substring(i));const c={depth:r.length,linkBack:-1,linkNext:-1,match:-1,type:\"\",text:\"\",offset:i,value:-1};e.push(c);let l=Fe[o[0]]||\"\";if(l){if(c.type=l,c.text=o[0],i++,\"OPEN_PAREN\"===l)r.push(e.length-1),s.push(e.length-1);else if(\"CLOSE_PAREN\"==l)0===r.length&&n(\"no matching open bracket\"),c.match=r.pop(),e[c.match].match=e.length-1,c.depth--,c.linkBack=s.pop(),e[c.linkBack].linkNext=e.length-1;else if(\"COMMA\"===l)c.linkBack=s.pop(),e[c.linkBack].linkNext=e.length-1,s.push(e.length-1);else if(\"OPEN_BRACKET\"===l)c.type=\"BRACKET\";else if(\"CLOSE_BRACKET\"===l){let t=e.pop().text;if(e.length>0&&\"NUMBER\"===e[e.length-1].type){const n=e.pop().text;t=n+t,e[e.length-1].value=L(n)}if(0===e.length||\"BRACKET\"!==e[e.length-1].type)throw new Error(\"missing opening bracket\");e[e.length-1].text+=t}}else if(a=o.match(Le),a){if(c.text=a[1],i+=c.text.length,Se.has(c.text)){c.type=\"KEYWORD\";continue}if(c.text.match(Ge)){c.type=\"TYPE\";continue}c.type=\"ID\"}else{if(a=o.match(De),!a)throw new Error(`unexpected token ${JSON.stringify(o[0])} at position ${i}`);c.text=a[1],c.type=\"NUMBER\",i+=c.text.length}}return new He(e.map((t=>Object.freeze(t))))}function je(t,e){let n=[];for(const r in e.keys())t.has(r)&&n.push(r);if(n.length>1)throw new Error(`conflicting types: ${n.join(\", \")}`)}function Ve(t,e){if(e.peekKeyword(Oe)){const n=e.pop().text;if(n!==t)throw new Error(`expected ${t}, got ${n}`)}return e.popType(\"ID\")}function Je(t,e){const n=new Set;for(;;){const r=t.peekType(\"KEYWORD\");if(null==r||e&&!e.has(r))break;if(t.pop(),n.has(r))throw new Error(`duplicate keywords: ${JSON.stringify(r)}`);n.add(r)}return Object.freeze(n)}function ze(t){let e=Je(t,Ie);return je(e,xe(\"constant payable nonpayable\".split(\" \"))),je(e,xe(\"pure view payable nonpayable\".split(\" \"))),e.has(\"view\")?\"view\":e.has(\"pure\")?\"pure\":e.has(\"payable\")?\"payable\":e.has(\"nonpayable\")?\"nonpayable\":e.has(\"constant\")?\"view\":\"nonpayable\"}function Ke(t,e){return t.popParams().map((t=>an.from(t,e)))}function qe(t){if(t.peekType(\"AT\")){if(t.pop(),t.peekType(\"NUMBER\"))return S(t.pop().text);throw new Error(\"invalid gas\")}return null}function _e(t){if(t.length)throw new Error(`unexpected tokens at offset ${t.offset}: ${t.toString()}`)}const Ze=new RegExp(/^(.*)\\[([0-9]*)\\]$/);function We(t){const e=t.match(Ge);if(f(e,\"invalid type\",\"type\",t),\"uint\"===t)return\"uint256\";if(\"int\"===t)return\"int256\";if(e[2]){const n=parseInt(e[2]);f(0!==n&&n<=32,\"invalid bytes length\",\"type\",t)}else if(e[3]){const n=parseInt(e[3]);f(0!==n&&n<=256&&n%8==0,\"invalid numeric width\",\"type\",t)}return t}const Ye={},Xe=Symbol.for(\"_ethers_internal\"),$e=\"_ParamTypeInternal\",tn=\"_ErrorInternal\",en=\"_EventInternal\",nn=\"_ConstructorInternal\",rn=\"_FallbackInternal\",sn=\"_FunctionInternal\",on=\"_StructInternal\";class an{name;type;baseType;indexed;components;arrayLength;arrayChildren;constructor(t,e,n,r,s,i,a,c){if(g(t,Ye,\"ParamType\"),Object.defineProperty(this,Xe,{value:$e}),i&&(i=Object.freeze(i.slice())),\"array\"===r){if(null==a||null==c)throw new Error(\"\")}else if(null!=a||null!=c)throw new Error(\"\");if(\"tuple\"===r){if(null==i)throw new Error(\"\")}else if(null!=i)throw new Error(\"\");o(this,{name:e,type:n,baseType:r,indexed:s,components:i,arrayLength:a,arrayChildren:c})}format(t){if(null==t&&(t=\"sighash\"),\"json\"===t){const e=this.name||\"\";if(this.isArray()){const t=JSON.parse(this.arrayChildren.format(\"json\"));return t.name=e,t.type+=`[${this.arrayLength<0?\"\":String(this.arrayLength)}]`,JSON.stringify(t)}const n={type:\"tuple\"===this.baseType?\"tuple\":this.type,name:e};return\"boolean\"==typeof this.indexed&&(n.indexed=this.indexed),this.isTuple()&&(n.components=this.components.map((e=>JSON.parse(e.format(t))))),JSON.stringify(n)}let e=\"\";return this.isArray()?(e+=this.arrayChildren.format(t),e+=`[${this.arrayLength<0?\"\":String(this.arrayLength)}]`):this.isTuple()?e+=\"(\"+this.components.map((e=>e.format(t))).join(\"full\"===t?\", \":\",\")+\")\":e+=this.type,\"sighash\"!==t&&(!0===this.indexed&&(e+=\" indexed\"),\"full\"===t&&this.name&&(e+=\" \"+this.name)),e}isArray(){return\"array\"===this.baseType}isTuple(){return\"tuple\"===this.baseType}isIndexable(){return null!=this.indexed}walk(t,e){if(this.isArray()){if(!Array.isArray(t))throw new Error(\"invalid array value\");if(-1!==this.arrayLength&&t.length!==this.arrayLength)throw new Error(\"array is wrong length\");const n=this;return t.map((t=>n.arrayChildren.walk(t,e)))}if(this.isTuple()){if(!Array.isArray(t))throw new Error(\"invalid tuple value\");if(t.length!==this.components.length)throw new Error(\"array is wrong length\");const n=this;return t.map(((t,r)=>n.components[r].walk(t,e)))}return e(this.type,t)}#d(t,e,n,r){if(this.isArray()){if(!Array.isArray(e))throw new Error(\"invalid array value\");if(-1!==this.arrayLength&&e.length!==this.arrayLength)throw new Error(\"array is wrong length\");const s=this.arrayChildren,i=e.slice();return i.forEach(((e,r)=>{s.#d(t,e,n,(t=>{i[r]=t}))})),void r(i)}if(this.isTuple()){const s=this.components;let i;if(Array.isArray(e))i=e.slice();else{if(null==e||\"object\"!=typeof e)throw new Error(\"invalid tuple value\");i=s.map((t=>{if(!t.name)throw new Error(\"cannot use object value with unnamed components\");if(!(t.name in e))throw new Error(`missing value for component ${t.name}`);return e[t.name]}))}if(i.length!==this.components.length)throw new Error(\"array is wrong length\");return i.forEach(((e,r)=>{s[r].#d(t,e,n,(t=>{i[r]=t}))})),void r(i)}const s=n(this.type,e);s.then?t.push(async function(){r(await s)}()):r(s)}async walkAsync(t,e){const n=[],r=[t];return this.#d(n,t,e,(t=>{r[0]=t})),n.length&&await Promise.all(n),r[0]}static from(t,e){if(an.isParamType(t))return t;if(\"string\"==typeof t)try{return an.from(Qe(t),e)}catch(e){f(!1,\"invalid param type\",\"obj\",t)}else if(t instanceof He){let n=\"\",r=\"\",s=null;Je(t,xe([\"tuple\"])).has(\"tuple\")||t.peekType(\"OPEN_PAREN\")?(r=\"tuple\",s=t.popParams().map((t=>an.from(t))),n=`tuple(${s.map((t=>t.format())).join(\",\")})`):(n=We(t.popType(\"TYPE\")),r=n);let i=null,o=null;for(;t.length&&t.peekType(\"BRACKET\");){const e=t.pop();i=new an(Ye,\"\",n,r,null,s,o,i),o=e.value,n+=e.text,r=\"array\",s=null}let a=null;if(Je(t,Te).has(\"indexed\")){if(!e)throw new Error(\"\");a=!0}const c=t.peekType(\"ID\")?t.pop().text:\"\";if(t.length)throw new Error(\"leftover tokens\");return new an(Ye,c,n,r,a,s,o,i)}const n=t.name;f(!n||\"string\"==typeof n&&n.match(Me),\"invalid name\",\"obj.name\",n);let r=t.indexed;null!=r&&(f(e,\"parameter cannot be indexed\",\"obj.indexed\",t.indexed),r=!!r);let s=t.type,i=s.match(Ze);if(i){const e=parseInt(i[2]||\"-1\"),o=an.from({type:i[1],components:t.components});return new an(Ye,n||\"\",s,\"array\",r,null,e,o)}if(\"tuple\"===s||s.startsWith(\"tuple(\")||s.startsWith(\"(\")){const e=null!=t.components?t.components.map((t=>an.from(t))):null;return new an(Ye,n||\"\",s,\"tuple\",r,e,null,null)}return s=We(t.type),new an(Ye,n||\"\",s,s,r,null,null,null)}static isParamType(t){return t&&t[Xe]===$e}}class cn{type;inputs;constructor(t,e,n){g(t,Ye,\"Fragment\"),o(this,{type:e,inputs:n=Object.freeze(n.slice())})}static from(t){if(\"string\"==typeof t){try{cn.from(JSON.parse(t))}catch(t){}return cn.from(Qe(t))}if(t instanceof He){switch(t.peekKeyword(Oe)){case\"constructor\":return dn.from(t);case\"error\":return hn.from(t);case\"event\":return fn.from(t);case\"fallback\":case\"receive\":return pn.from(t);case\"function\":return gn.from(t);case\"struct\":return mn.from(t)}}else if(\"object\"==typeof t){switch(t.type){case\"constructor\":return dn.from(t);case\"error\":return hn.from(t);case\"event\":return fn.from(t);case\"fallback\":case\"receive\":return pn.from(t);case\"function\":return gn.from(t);case\"struct\":return mn.from(t)}h(!1,`unsupported type: ${t.type}`,\"UNSUPPORTED_OPERATION\",{operation:\"Fragment.from\"})}f(!1,\"unsupported frgament object\",\"obj\",t)}static isConstructor(t){return dn.isFragment(t)}static isError(t){return hn.isFragment(t)}static isEvent(t){return fn.isFragment(t)}static isFunction(t){return gn.isFragment(t)}static isStruct(t){return mn.isFragment(t)}}class ln extends cn{name;constructor(t,e,n,r){super(t,e,r),f(\"string\"==typeof n&&n.match(Me),\"invalid identifier\",\"name\",n),r=Object.freeze(r.slice()),o(this,{name:n})}}function un(t,e){return\"(\"+e.map((e=>e.format(t))).join(\"full\"===t?\", \":\",\")+\")\"}class hn extends ln{constructor(t,e,n){super(t,\"error\",e,n),Object.defineProperty(this,Xe,{value:tn})}get selector(){return Pe(this.format(\"sighash\")).substring(0,10)}format(t){if(null==t&&(t=\"sighash\"),\"json\"===t)return JSON.stringify({type:\"error\",name:this.name,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[];return\"sighash\"!==t&&e.push(\"error\"),e.push(this.name+un(t,this.inputs)),e.join(\" \")}static from(t){if(hn.isFragment(t))return t;if(\"string\"==typeof t)return hn.from(Qe(t));if(t instanceof He){const e=Ve(\"error\",t),n=Ke(t);return _e(t),new hn(Ye,e,n)}return new hn(Ye,t.name,t.inputs?t.inputs.map(an.from):[])}static isFragment(t){return t&&t[Xe]===tn}}class fn extends ln{anonymous;constructor(t,e,n,r){super(t,\"event\",e,n),Object.defineProperty(this,Xe,{value:en}),o(this,{anonymous:r})}get topicHash(){return Pe(this.format(\"sighash\"))}format(t){if(null==t&&(t=\"sighash\"),\"json\"===t)return JSON.stringify({type:\"event\",anonymous:this.anonymous,name:this.name,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[];return\"sighash\"!==t&&e.push(\"event\"),e.push(this.name+un(t,this.inputs)),\"sighash\"!==t&&this.anonymous&&e.push(\"anonymous\"),e.join(\" \")}static getTopicHash(t,e){e=(e||[]).map((t=>an.from(t)));return new fn(Ye,t,e,!1).topicHash}static from(t){if(fn.isFragment(t))return t;if(\"string\"==typeof t)try{return fn.from(Qe(t))}catch(e){f(!1,\"invalid event fragment\",\"obj\",t)}else if(t instanceof He){const e=Ve(\"event\",t),n=Ke(t,!0),r=!!Je(t,xe([\"anonymous\"])).has(\"anonymous\");return _e(t),new fn(Ye,e,n,r)}return new fn(Ye,t.name,t.inputs?t.inputs.map((t=>an.from(t,!0))):[],!!t.anonymous)}static isFragment(t){return t&&t[Xe]===en}}class dn extends cn{payable;gas;constructor(t,e,n,r,s){super(t,e,n),Object.defineProperty(this,Xe,{value:nn}),o(this,{payable:r,gas:s})}format(t){if(h(null!=t&&\"sighash\"!==t,\"cannot format a constructor for sighash\",\"UNSUPPORTED_OPERATION\",{operation:\"format(sighash)\"}),\"json\"===t)return JSON.stringify({type:\"constructor\",stateMutability:this.payable?\"payable\":\"undefined\",payable:this.payable,gas:null!=this.gas?this.gas:void 0,inputs:this.inputs.map((e=>JSON.parse(e.format(t))))});const e=[`constructor${un(t,this.inputs)}`];return this.payable&&e.push(\"payable\"),null!=this.gas&&e.push(`@${this.gas.toString()}`),e.join(\" \")}static from(t){if(dn.isFragment(t))return t;if(\"string\"==typeof t)try{return dn.from(Qe(t))}catch(e){f(!1,\"invalid constuctor fragment\",\"obj\",t)}else if(t instanceof He){Je(t,xe([\"constructor\"]));const e=Ke(t),n=!!Je(t,Ne).has(\"payable\"),r=qe(t);return _e(t),new dn(Ye,\"constructor\",e,n,r)}return new dn(Ye,\"constructor\",t.inputs?t.inputs.map(an.from):[],!!t.payable,null!=t.gas?t.gas:null)}static isFragment(t){return t&&t[Xe]===nn}}class pn extends cn{payable;constructor(t,e,n){super(t,\"fallback\",e),Object.defineProperty(this,Xe,{value:rn}),o(this,{payable:n})}format(t){const e=0===this.inputs.length?\"receive\":\"fallback\";if(\"json\"===t){const t=this.payable?\"payable\":\"nonpayable\";return JSON.stringify({type:e,stateMutability:t})}return`${e}()${this.payable?\" payable\":\"\"}`}static from(t){if(pn.isFragment(t))return t;if(\"string\"==typeof t)try{return pn.from(Qe(t))}catch(e){f(!1,\"invalid fallback fragment\",\"obj\",t)}else if(t instanceof He){const e=t.toString();f(t.peekKeyword(xe([\"fallback\",\"receive\"])),\"type must be fallback or receive\",\"obj\",e);if(\"receive\"===t.popKeyword(xe([\"fallback\",\"receive\"]))){const e=Ke(t);return f(0===e.length,\"receive cannot have arguments\",\"obj.inputs\",e),Je(t,xe([\"payable\"])),_e(t),new pn(Ye,[],!0)}let n=Ke(t);n.length?f(1===n.length&&\"bytes\"===n[0].type,\"invalid fallback inputs\",\"obj.inputs\",n.map((t=>t.format(\"minimal\"))).join(\", \")):n=[an.from(\"bytes\")];const r=ze(t);if(f(\"nonpayable\"===r||\"payable\"===r,\"fallback cannot be constants\",\"obj.stateMutability\",r),Je(t,xe([\"returns\"])).has(\"returns\")){const e=Ke(t);f(1===e.length&&\"bytes\"===e[0].type,\"invalid fallback outputs\",\"obj.outputs\",e.map((t=>t.format(\"minimal\"))).join(\", \"))}return _e(t),new pn(Ye,n,\"payable\"===r)}if(\"receive\"===t.type)return new pn(Ye,[],!0);if(\"fallback\"===t.type){const e=[an.from(\"bytes\")],n=\"payable\"===t.stateMutability;return new pn(Ye,e,n)}f(!1,\"invalid fallback description\",\"obj\",t)}static isFragment(t){return t&&t[Xe]===rn}}class gn extends ln{constant;outputs;stateMutability;payable;gas;constructor(t,e,n,r,s,i){super(t,\"function\",e,r),Object.defineProperty(this,Xe,{value:sn});o(this,{constant:\"view\"===n||\"pure\"===n,gas:i,outputs:s=Object.freeze(s.slice()),payable:\"payable\"===n,stateMutability:n})}get selector(){return Pe(this.format(\"sighash\")).substring(0,10)}format(t){if(null==t&&(t=\"sighash\"),\"json\"===t)return JSON.stringify({type:\"function\",name:this.name,constant:this.constant,stateMutability:\"nonpayable\"!==this.stateMutability?this.stateMutability:void 0,payable:this.payable,gas:null!=this.gas?this.gas:void 0,inputs:this.inputs.map((e=>JSON.parse(e.format(t)))),outputs:this.outputs.map((e=>JSON.parse(e.format(t))))});const e=[];return\"sighash\"!==t&&e.push(\"function\"),e.push(this.name+un(t,this.inputs)),\"sighash\"!==t&&(\"nonpayable\"!==this.stateMutability&&e.push(this.stateMutability),this.outputs&&this.outputs.length&&(e.push(\"returns\"),e.push(un(t,this.outputs))),null!=this.gas&&e.push(`@${this.gas.toString()}`)),e.join(\" \")}static getSelector(t,e){e=(e||[]).map((t=>an.from(t)));return new gn(Ye,t,\"view\",e,[],null).selector}static from(t){if(gn.isFragment(t))return t;if(\"string\"==typeof t)try{return gn.from(Qe(t))}catch(e){f(!1,\"invalid function fragment\",\"obj\",t)}else if(t instanceof He){const e=Ve(\"function\",t),n=Ke(t),r=ze(t);let s=[];Je(t,xe([\"returns\"])).has(\"returns\")&&(s=Ke(t));const i=qe(t);return _e(t),new gn(Ye,e,r,n,s,i)}let e=t.stateMutability;return null==e&&(e=\"payable\",\"boolean\"==typeof t.constant?(e=\"view\",t.constant||(e=\"payable\",\"boolean\"!=typeof t.payable||t.payable||(e=\"nonpayable\"))):\"boolean\"!=typeof t.payable||t.payable||(e=\"nonpayable\")),new gn(Ye,t.name,e,t.inputs?t.inputs.map(an.from):[],t.outputs?t.outputs.map(an.from):[],null!=t.gas?t.gas:null)}static isFragment(t){return t&&t[Xe]===sn}}class mn extends ln{constructor(t,e,n){super(t,\"struct\",e,n),Object.defineProperty(this,Xe,{value:on})}format(){throw new Error(\"@TODO\")}static from(t){if(\"string\"==typeof t)try{return mn.from(Qe(t))}catch(e){f(!1,\"invalid struct fragment\",\"obj\",t)}else if(t instanceof He){const e=Ve(\"struct\",t),n=Ke(t);return _e(t),new mn(Ye,e,n)}return new mn(Ye,t.name,t.inputs?t.inputs.map(an.from):[])}static isFragment(t){return t&&t[Xe]===on}}const yn=new Map;yn.set(0,\"GENERIC_PANIC\"),yn.set(1,\"ASSERT_FALSE\"),yn.set(17,\"OVERFLOW\"),yn.set(18,\"DIVIDE_BY_ZERO\"),yn.set(33,\"ENUM_RANGE_ERROR\"),yn.set(34,\"BAD_STORAGE_DATA\"),yn.set(49,\"STACK_UNDERFLOW\"),yn.set(50,\"ARRAY_RANGE_ERROR\"),yn.set(65,\"OUT_OF_MEMORY\"),yn.set(81,\"UNINITIALIZED_FUNCTION_CALL\");const wn=new RegExp(/^bytes([0-9]*)$/),bn=new RegExp(/^(u?int)([0-9]*)$/);let An=null,vn=1024;class En{#p(t){if(t.isArray())return new oe(this.#p(t.arrayChildren),t.arrayLength,t.name);if(t.isTuple())return new ke(t.components.map((t=>this.#p(t))),t.name);switch(t.baseType){case\"address\":return new ne(t.name);case\"bool\":return new ae(t.name);case\"string\":return new Ee(t.name);case\"bytes\":return new le(t.name);case\"\":return new fe(t.name)}let e=t.type.match(bn);if(e){let n=parseInt(e[2]||\"256\");return f(0!==n&&n<=256&&n%8==0,\"invalid \"+e[1]+\" bit length\",\"param\",t),new me(n/8,\"int\"===e[1],t.name)}if(e=t.type.match(wn),e){let n=parseInt(e[1]);return f(0!==n&&n<=32,\"invalid bytes length\",\"param\",t),new ue(n,t.name)}f(!1,\"invalid type\",\"type\",t.type)}getDefaultValue(t){const e=t.map((t=>this.#p(an.from(t))));return new ke(e,\"_\").defaultValue()}encode(t,e){d(e.length,t.length,\"types/values length mismatch\");const n=t.map((t=>this.#p(an.from(t)))),r=new ke(n,\"_\"),s=new $;return r.encode(s,e),s.data}decode(t,e,n){const r=t.map((t=>this.#p(an.from(t))));return new ke(r,\"_\").decode(new tt(e,n,vn))}static _setDefaultMaxInflation(t){f(\"number\"==typeof t&&Number.isInteger(t),\"invalid defaultMaxInflation factor\",\"value\",t),vn=t}static defaultAbiCoder(){return null==An&&(An=new En),An}static getBuiltinCallException(t,e,n){return function(t,e,n,r){let s=\"missing revert data\",i=null,o=null;if(n){s=\"execution reverted\";const t=y(n);if(n=E(n),0===t.length)s+=\" (no data present; likely require(false) occurred\",i=\"require(false)\";else if(t.length%32!=4)s+=\" (could not decode reason; invalid data length)\";else if(\"0x08c379a0\"===E(t.slice(0,4)))try{i=r.decode([\"string\"],t.slice(4))[0],o={signature:\"Error(string)\",name:\"Error\",args:[i]},s+=`: ${JSON.stringify(i)}`}catch(t){s+=\" (could not decode reason; invalid string data)\"}else if(\"0x4e487b71\"===E(t.slice(0,4)))try{const e=Number(r.decode([\"uint256\"],t.slice(4))[0]);o={signature:\"Panic(uint256)\",name:\"Panic\",args:[e]},i=`Panic due to ${yn.get(e)||\"UNKNOWN\"}(${e})`,s+=`: ${i}`}catch(t){s+=\" (could not decode panic code)\"}else s+=\" (unknown custom error)\"}const a={to:e.to?Wt(e.to):null,data:e.data||\"0x\"};return e.from&&(a.from=Wt(e.from)),u(s,\"CALL_EXCEPTION\",{action:t,data:n,reason:i,transaction:a,invocation:null,revert:o})}(t,e,n,En.defaultAbiCoder())}}function kn(t){return t&&\"function\"==typeof t.getAddress}async function Pn(t,e){const n=await e;return null!=n&&\"******************************************\"!==n||(h(\"string\"!=typeof t,\"unconfigured name\",\"UNCONFIGURED_NAME\",{value:t}),f(!1,\"invalid AddressLike value; did not resolve to a value address\",\"target\",t)),Wt(n)}function xn(t,e){return\"string\"==typeof t?t.match(/^0x[0-9a-f]{40}$/i)?Wt(t):(h(null!=e,\"ENS resolution requires a provider\",\"UNSUPPORTED_OPERATION\",{operation:\"resolveName\"}),Pn(t,e.resolveName(t))):kn(t)?Pn(t,t.getAddress()):t&&\"function\"==typeof t.then?Pn(t,t):void f(!1,\"unsupported addressable value\",\"target\",t)}const Nn=new Uint8Array(32);Nn.fill(0);const Bn=BigInt(-1),In=BigInt(0),Cn=BigInt(1),On=BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");const Rn=M(Cn,32),Tn=M(In,32),Sn={name:\"string\",version:\"string\",chainId:\"uint256\",verifyingContract:\"address\",salt:\"bytes32\"},Fn=[\"name\",\"version\",\"chainId\",\"verifyingContract\",\"salt\"];function Un(t){return function(e){return f(\"string\"==typeof e,`invalid domain value for ${JSON.stringify(t)}`,`domain.${t}`,e),e}}const Dn={name:Un(\"name\"),version:Un(\"version\"),chainId:function(t){const e=S(t,\"domain.chainId\");return f(e>=0,\"invalid chain ID\",\"domain.chainId\",t),Number.isSafeInteger(e)?Number(e):H(e)},verifyingContract:function(t){try{return Wt(t).toLowerCase()}catch(t){}f(!1,'invalid domain value \"verifyingContract\"',\"domain.verifyingContract\",t)},salt:function(t){const e=y(t,\"domain.salt\");return f(32===e.length,'invalid domain value \"salt\"',\"domain.salt\",t),E(e)}};function Ln(t){{const e=t.match(/^(u?)int(\\d+)$/);if(e){const n=\"\"===e[1],r=parseInt(e[2]);f(r%8==0&&0!==r&&r<=256&&e[2]===String(r),\"invalid numeric width\",\"type\",t);const s=T(On,n?r-1:r),i=n?(s+Cn)*Bn:In;return function(e){const r=S(e,\"value\");return f(r>=i&&r<=s,`value out-of-bounds for ${t}`,\"value\",r),M(n?R(r,256):r,32)}}}{const e=t.match(/^bytes(\\d+)$/);if(e){const n=parseInt(e[1]);return f(0!==n&&n<=32&&e[1]===String(n),\"invalid bytes width\",\"type\",t),function(e){return f(y(e).length===n,`invalid length for ${t}`,\"value\",e),function(t){const e=y(t),n=e.length%32;return n?k([e,Nn.slice(n)]):E(e)}(e)}}}switch(t){case\"address\":return function(t){return B(Wt(t),32)};case\"bool\":return function(t){return t?Rn:Tn};case\"bytes\":return function(t){return jt(t)};case\"string\":return function(t){return Pe(t)}}return null}function Mn(t,e){return`${t}(${e.map((({name:t,type:e})=>e+\" \"+t)).join(\",\")})`}function Gn(t){const e=t.match(/^([^\\x5b]*)((\\x5b\\d*\\x5d)*)(\\x5b(\\d*)\\x5d)$/);return e?{base:e[1],index:e[2]+e[4],array:{base:e[1],prefix:e[1]+e[2],count:e[5]?parseInt(e[5]):-1}}:{base:t}}class Hn{primaryType;#g;get types(){return JSON.parse(this.#g)}#m;#y;constructor(t){this.#m=new Map,this.#y=new Map;const e=new Map,n=new Map,r=new Map,s={};Object.keys(t).forEach((i=>{s[i]=t[i].map((({name:e,type:n})=>{let{base:r,index:s}=Gn(n);return\"int\"!==r||t.int||(r=\"int256\"),\"uint\"!==r||t.uint||(r=\"uint256\"),{name:e,type:r+(s||\"\")}})),e.set(i,new Set),n.set(i,[]),r.set(i,new Set)})),this.#g=JSON.stringify(s);for(const r in s){const i=new Set;for(const o of s[r]){f(!i.has(o.name),`duplicate variable name ${JSON.stringify(o.name)} in ${JSON.stringify(r)}`,\"types\",t),i.add(o.name);const s=Gn(o.type).base;f(s!==r,`circular type reference to ${JSON.stringify(s)}`,\"types\",t);Ln(s)||(f(n.has(s),`unknown type ${JSON.stringify(s)}`,\"types\",t),n.get(s).push(r),e.get(r).add(s))}}const i=Array.from(n.keys()).filter((t=>0===n.get(t).length));f(0!==i.length,\"missing primary type\",\"types\",t),f(1===i.length,`ambiguous primary types or unused types: ${i.map((t=>JSON.stringify(t))).join(\", \")}`,\"types\",t),o(this,{primaryType:i[0]}),function s(i,o){f(!o.has(i),`circular type reference to ${JSON.stringify(i)}`,\"types\",t),o.add(i);for(const t of e.get(i))if(n.has(t)){s(t,o);for(const e of o)r.get(e).add(t)}o.delete(i)}(this.primaryType,new Set);for(const[t,e]of r){const n=Array.from(e);n.sort(),this.#m.set(t,Mn(t,s[t])+n.map((t=>Mn(t,s[t]))).join(\"\"))}}getEncoder(t){let e=this.#y.get(t);return e||(e=this.#w(t),this.#y.set(t,e)),e}#w(t){{const e=Ln(t);if(e)return e}const e=Gn(t).array;if(e){const t=e.prefix,n=this.getEncoder(t);return r=>{f(-1===e.count||e.count===r.length,`array length mismatch; expected length ${e.count}`,\"value\",r);let s=r.map(n);return this.#m.has(t)&&(s=s.map(jt)),jt(k(s))}}const n=this.types[t];if(n){const e=Pe(this.#m.get(t));return t=>{const r=n.map((({name:e,type:n})=>{const r=this.getEncoder(n)(t[e]);return this.#m.has(n)?jt(r):r}));return r.unshift(e),k(r)}}f(!1,`unknown type: ${t}`,\"type\",t)}encodeType(t){const e=this.#m.get(t);return f(e,`unknown type: ${JSON.stringify(t)}`,\"name\",t),e}encodeData(t,e){return this.getEncoder(t)(e)}hashStruct(t,e){return jt(this.encodeData(t,e))}encode(t){return this.encodeData(this.primaryType,t)}hash(t){return this.hashStruct(this.primaryType,t)}_visit(t,e,n){if(Ln(t))return n(t,e);const r=Gn(t).array;if(r)return f(-1===r.count||r.count===e.length,`array length mismatch; expected length ${r.count}`,\"value\",e),e.map((t=>this._visit(r.prefix,t,n)));const s=this.types[t];if(s)return s.reduce(((t,{name:r,type:s})=>(t[r]=this._visit(s,e[r],n),t)),{});f(!1,`unknown type: ${t}`,\"type\",t)}visit(t,e){return this._visit(this.primaryType,t,e)}static from(t){return new Hn(t)}static getPrimaryType(t){return Hn.from(t).primaryType}static hashStruct(t,e,n){return Hn.from(e).hashStruct(t,n)}static hashDomain(t){const e=[];for(const n in t){if(null==t[n])continue;const r=Sn[n];f(r,`invalid typed-data domain key: ${JSON.stringify(n)}`,\"domain\",t),e.push({name:n,type:r})}return e.sort(((t,e)=>Fn.indexOf(t.name)-Fn.indexOf(e.name))),Hn.hashStruct(\"EIP712Domain\",{EIP712Domain:e},t)}static encode(t,e,n){return k([\"0x1901\",Hn.hashDomain(t),Hn.from(e).hash(n)])}static hash(t,e,n){return jt(Hn.encode(t,e,n))}static async resolveNames(t,e,n,r){t=Object.assign({},t);for(const e in t)null==t[e]&&delete t[e];const s={};t.verifyingContract&&!b(t.verifyingContract,20)&&(s[t.verifyingContract]=\"0x\");const i=Hn.from(e);i.visit(n,((t,e)=>(\"address\"!==t||b(e,20)||(s[e]=\"0x\"),e)));for(const t in s)s[t]=await r(t);return t.verifyingContract&&s[t.verifyingContract]&&(t.verifyingContract=s[t.verifyingContract]),{domain:t,value:n=i.visit(n,((t,e)=>\"address\"===t&&s[e]?s[e]:e))}}static getPayload(t,e,n){Hn.hashDomain(t);const r={},s=[];Fn.forEach((e=>{const n=t[e];null!=n&&(r[e]=Dn[e](n),s.push({name:e,type:Sn[e]}))}));const i=Hn.from(e);e=i.types;const o=Object.assign({},e);return f(null==o.EIP712Domain,\"types must not contain EIP712Domain type\",\"types.EIP712Domain\",e),o.EIP712Domain=s,i.encode(n),{types:o,domain:r,primaryType:i.primaryType,message:i.visit(n,((t,e)=>{if(t.match(/^bytes(\\d*)/))return E(y(e));if(t.match(/^u?int/))return S(e).toString();switch(t){case\"address\":return e.toLowerCase();case\"bool\":return!!e;case\"string\":return f(\"string\"==typeof e,\"invalid string\",\"value\",e),e}f(!1,\"unsupported type\",\"type\",t)}))}}}function Qn(t,e){return{address:Wt(t),storageKeys:e.map(((t,e)=>(f(b(t,32),\"invalid slot\",`storageKeys[${e}]`,t),t.toLowerCase())))}}function jn(t){if(Array.isArray(t))return t.map(((e,n)=>Array.isArray(e)?(f(2===e.length,\"invalid slot set\",`value[${n}]`,e),Qn(e[0],e[1])):(f(null!=e&&\"object\"==typeof e,\"invalid address-slot set\",\"value\",t),Qn(e.address,e.storageKeys))));f(null!=t&&\"object\"==typeof t,\"invalid access list\",\"value\",t);const e=Object.keys(t).map((e=>{const n=t[e].reduce(((t,e)=>(t[e]=!0,t)),{});return Qn(e,Object.keys(n).sort())}));return e.sort(((t,e)=>t.address.localeCompare(e.address))),e}function Vn(t){return async function(t,e){h(null==e||!e.cancelled,\"request cancelled before sending\",\"CANCELLED\");const n=t.url.split(\":\")[0].toLowerCase();h(\"http\"===n||\"https\"===n,`unsupported protocol ${n}`,\"UNSUPPORTED_OPERATION\",{info:{protocol:n},operation:\"request\"}),h(\"https\"===n||!t.credentials||t.allowInsecureAuthentication,\"insecure authorized connections unsupported\",\"UNSUPPORTED_OPERATION\",{operation:\"request\"});let r=null;const s=new AbortController,i=setTimeout((()=>{r=u(\"request timeout\",\"TIMEOUT\"),s.abort()}),t.timeout);e&&e.addListener((()=>{r=u(\"request cancelled\",\"CANCELLED\"),s.abort()}));const o={method:t.method,headers:new Headers(Array.from(t)),body:t.body||void 0,signal:s.signal};let a;try{a=await fetch(t.url,o)}catch(t){if(clearTimeout(i),r)throw r;throw t}clearTimeout(i);const c={};a.headers.forEach(((t,e)=>{c[e.toLowerCase()]=t}));const l=await a.arrayBuffer(),f=null==l?null:new Uint8Array(l);return{statusCode:a.status,statusMessage:a.statusText,headers:c,body:f}}}Vn();let Jn=Vn();const zn=new RegExp(\"^data:([^;:]*)?(;base64)?,(.*)$\",\"i\"),Kn=new RegExp(\"^ipfs://(ipfs/)?(.*)$\",\"i\");let qn=!1;async function _n(t,e){try{const e=t.match(zn);if(!e)throw new Error(\"invalid data\");return new er(200,\"OK\",{\"content-type\":e[1]||\"text/plain\"},e[2]?function(t){t=atob(t);const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return y(e)}(e[3]):Ae(e[3].replace(/%([0-9a-f][0-9a-f])/gi,((t,e)=>String.fromCharCode(parseInt(e,16))))))}catch(e){return new er(599,\"BAD REQUEST (invalid data: URI)\",{},null,new tr(t))}}function Zn(t){return async function(e,n){try{const n=e.match(Kn);if(!n)throw new Error(\"invalid link\");return new tr(`${t}${n[2]}`)}catch(t){return new er(599,\"BAD REQUEST (invalid IPFS URI)\",{},null,new tr(e))}}}const Wn={data:_n,ipfs:Zn(\"https://gateway.ipfs.io/ipfs/\")},Yn=new WeakMap;class Xn{#b;#A;constructor(t){this.#b=[],this.#A=!1,Yn.set(t,(()=>{if(!this.#A){this.#A=!0;for(const t of this.#b)setTimeout((()=>{t()}),0);this.#b=[]}}))}addListener(t){h(!this.#A,\"singal already cancelled\",\"UNSUPPORTED_OPERATION\",{operation:\"fetchCancelSignal.addCancelListener\"}),this.#b.push(t)}get cancelled(){return this.#A}checkSignal(){h(!this.cancelled,\"cancelled\",\"CANCELLED\",{})}}function $n(t){if(null==t)throw new Error(\"missing signal; should not happen\");return t.checkSignal(),t}class tr{#v;#E;#k;#P;#x;#N;#B;#I;#C;#O;#R;#T;#S;#F;#U;get url(){return this.#N}set url(t){this.#N=String(t)}get body(){return null==this.#B?null:new Uint8Array(this.#B)}set body(t){if(null==t)this.#B=void 0,this.#I=void 0;else if(\"string\"==typeof t)this.#B=Ae(t),this.#I=\"text/plain\";else if(t instanceof Uint8Array)this.#B=t,this.#I=\"application/octet-stream\";else{if(\"object\"!=typeof t)throw new Error(\"invalid body\");this.#B=Ae(JSON.stringify(t)),this.#I=\"application/json\"}}hasBody(){return null!=this.#B}get method(){return this.#P?this.#P:this.hasBody()?\"POST\":\"GET\"}set method(t){null==t&&(t=\"\"),this.#P=String(t).toUpperCase()}get headers(){const t=Object.assign({},this.#k);return this.#C&&(t.authorization=`Basic ${function(t){const e=y(t);let n=\"\";for(let t=0;t<e.length;t++)n+=String.fromCharCode(e[t]);return btoa(n)}(Ae(this.#C))}`),this.allowGzip&&(t[\"accept-encoding\"]=\"gzip\"),null==t[\"content-type\"]&&this.#I&&(t[\"content-type\"]=this.#I),this.body&&(t[\"content-length\"]=String(this.body.length)),t}getHeader(t){return this.headers[t.toLowerCase()]}setHeader(t,e){this.#k[String(t).toLowerCase()]=String(e)}clearHeaders(){this.#k={}}[Symbol.iterator](){const t=this.headers,e=Object.keys(t);let n=0;return{next:()=>{if(n<e.length){const r=e[n++];return{value:[r,t[r]],done:!1}}return{value:void 0,done:!0}}}}get credentials(){return this.#C||null}setCredentials(t,e){f(!t.match(/:/),\"invalid basic authentication username\",\"username\",\"[REDACTED]\"),this.#C=`${t}:${e}`}get allowGzip(){return this.#E}set allowGzip(t){this.#E=!!t}get allowInsecureAuthentication(){return!!this.#v}set allowInsecureAuthentication(t){this.#v=!!t}get timeout(){return this.#x}set timeout(t){f(t>=0,\"timeout must be non-zero\",\"timeout\",t),this.#x=t}get preflightFunc(){return this.#O||null}set preflightFunc(t){this.#O=t}get processFunc(){return this.#R||null}set processFunc(t){this.#R=t}get retryFunc(){return this.#T||null}set retryFunc(t){this.#T=t}get getUrlFunc(){return this.#U||Jn}set getUrlFunc(t){this.#U=t}constructor(t){this.#N=String(t),this.#v=!1,this.#E=!0,this.#k={},this.#P=\"\",this.#x=3e5,this.#F={slotInterval:250,maxAttempts:12},this.#U=null}toString(){return`<FetchRequest method=${JSON.stringify(this.method)} url=${JSON.stringify(this.url)} headers=${JSON.stringify(this.headers)} body=${this.#B?E(this.#B):\"null\"}>`}setThrottleParams(t){null!=t.slotInterval&&(this.#F.slotInterval=t.slotInterval),null!=t.maxAttempts&&(this.#F.maxAttempts=t.maxAttempts)}async#D(t,e,n,r,s){if(t>=this.#F.maxAttempts)return s.makeServerError(\"exceeded maximum retry limit\");h(nr()<=e,\"timeout\",\"TIMEOUT\",{operation:\"request.send\",reason:\"timeout\",request:r}),n>0&&await function(t){return new Promise((e=>setTimeout(e,t)))}(n);let i=this.clone();const o=(i.url.split(\":\")[0]||\"\").toLowerCase();if(o in Wn){const t=await Wn[o](i.url,$n(r.#S));if(t instanceof er){let e=t;if(this.processFunc){$n(r.#S);try{e=await this.processFunc(i,e)}catch(t){null!=t.throttle&&\"number\"==typeof t.stall||e.makeServerError(\"error in post-processing function\",t).assertOk()}}return e}i=t}this.preflightFunc&&(i=await this.preflightFunc(i));const a=await this.getUrlFunc(i,$n(r.#S));let c=new er(a.statusCode,a.statusMessage,a.headers,a.body,r);if(301===c.statusCode||302===c.statusCode){try{const n=c.headers.location||\"\";return i.redirect(n).#D(t+1,e,0,r,c)}catch(t){}return c}if(429===c.statusCode&&(null==this.retryFunc||await this.retryFunc(i,c,t))){const n=c.headers[\"retry-after\"];let s=this.#F.slotInterval*Math.trunc(Math.random()*Math.pow(2,t));return\"string\"==typeof n&&n.match(/^[1-9][0-9]*$/)&&(s=parseInt(n)),i.clone().#D(t+1,e,s,r,c)}if(this.processFunc){$n(r.#S);try{c=await this.processFunc(i,c)}catch(n){null!=n.throttle&&\"number\"==typeof n.stall||c.makeServerError(\"error in post-processing function\",n).assertOk();let s=this.#F.slotInterval*Math.trunc(Math.random()*Math.pow(2,t));return n.stall>=0&&(s=n.stall),i.clone().#D(t+1,e,s,r,c)}}return c}send(){return h(null==this.#S,\"request already sent\",\"UNSUPPORTED_OPERATION\",{operation:\"fetchRequest.send\"}),this.#S=new Xn(this),this.#D(0,nr()+this.timeout,0,this,new er(0,\"\",{},null,this))}cancel(){h(null!=this.#S,\"request has not been sent\",\"UNSUPPORTED_OPERATION\",{operation:\"fetchRequest.cancel\"});const t=Yn.get(this);if(!t)throw new Error(\"missing signal; should not happen\");t()}redirect(t){const e=this.url.split(\":\")[0].toLowerCase(),n=t.split(\":\")[0].toLowerCase();h(\"GET\"===this.method&&(\"https\"!==e||\"http\"!==n)&&t.match(/^https?:/),\"unsupported redirect\",\"UNSUPPORTED_OPERATION\",{operation:`redirect(${this.method} ${JSON.stringify(this.url)} => ${JSON.stringify(t)})`});const r=new tr(t);return r.method=\"GET\",r.allowGzip=this.allowGzip,r.timeout=this.timeout,r.#k=Object.assign({},this.#k),this.#B&&(r.#B=new Uint8Array(this.#B)),r.#I=this.#I,r}clone(){const t=new tr(this.url);return t.#P=this.#P,this.#B&&(t.#B=this.#B),t.#I=this.#I,t.#k=Object.assign({},this.#k),t.#C=this.#C,this.allowGzip&&(t.allowGzip=!0),t.timeout=this.timeout,this.allowInsecureAuthentication&&(t.allowInsecureAuthentication=!0),t.#O=this.#O,t.#R=this.#R,t.#T=this.#T,t.#F=Object.assign({},this.#F),t.#U=this.#U,t}static lockConfig(){qn=!0}static getGateway(t){return Wn[t.toLowerCase()]||null}static registerGateway(t,e){if(\"http\"===(t=t.toLowerCase())||\"https\"===t)throw new Error(`cannot intercept ${t}; use registerGetUrl`);if(qn)throw new Error(\"gateways locked\");Wn[t]=e}static registerGetUrl(t){if(qn)throw new Error(\"gateways locked\");Jn=t}static createGetUrlFunc(t){return Vn()}static createDataGateway(){return _n}static createIpfsGatewayFunc(t){return Zn(t)}}class er{#L;#M;#k;#B;#G;#H;toString(){return`<FetchResponse status=${this.statusCode} body=${this.#B?E(this.#B):\"null\"}>`}get statusCode(){return this.#L}get statusMessage(){return this.#M}get headers(){return Object.assign({},this.#k)}get body(){return null==this.#B?null:new Uint8Array(this.#B)}get bodyText(){try{return null==this.#B?\"\":ve(this.#B)}catch(t){h(!1,\"response body is not valid UTF-8 data\",\"UNSUPPORTED_OPERATION\",{operation:\"bodyText\",info:{response:this}})}}get bodyJson(){try{return JSON.parse(this.bodyText)}catch(t){h(!1,\"response body is not valid JSON\",\"UNSUPPORTED_OPERATION\",{operation:\"bodyJson\",info:{response:this}})}}[Symbol.iterator](){const t=this.headers,e=Object.keys(t);let n=0;return{next:()=>{if(n<e.length){const r=e[n++];return{value:[r,t[r]],done:!1}}return{value:void 0,done:!0}}}}constructor(t,e,n,r,s){this.#L=t,this.#M=e,this.#k=Object.keys(n).reduce(((t,e)=>(t[e.toLowerCase()]=String(n[e]),t)),{}),this.#B=null==r?null:new Uint8Array(r),this.#G=s||null,this.#H={message:\"\"}}makeServerError(t,e){let n;n=t?`CLIENT ESCALATED SERVER ERROR (${this.statusCode} ${this.statusMessage}; ${t})`:`CLIENT ESCALATED SERVER ERROR (${t=`${this.statusCode} ${this.statusMessage}`})`;const r=new er(599,n,this.headers,this.body,this.#G||void 0);return r.#H={message:t,error:e},r}throwThrottleError(t,e){null==e?e=-1:f(Number.isInteger(e)&&e>=0,\"invalid stall timeout\",\"stall\",e);const n=new Error(t||\"throttling requests\");throw o(n,{stall:e,throttle:!0}),n}getHeader(t){return this.headers[t.toLowerCase()]}hasBody(){return null!=this.#B}get request(){return this.#G}ok(){return\"\"===this.#H.message&&this.statusCode>=200&&this.statusCode<300}assertOk(){if(this.ok())return;let{message:t,error:e}=this.#H;\"\"===t&&(t=`server response ${this.statusCode} ${this.statusMessage}`);let n=null;this.request&&(n=this.request.url);let r=null;try{this.#B&&(r=ve(this.#B))}catch(t){}h(!1,t,\"SERVER_ERROR\",{request:this.request||\"unknown request\",response:this,error:e,info:{requestUrl:n,responseBody:r,responseStatus:`${this.statusCode} ${this.statusMessage}`}})}}function nr(){return(new Date).getTime()}const rr=\"******************************************\";class sr{fragment;name;signature;topic;args;constructor(t,e,n){const r=t.name,s=t.format();o(this,{fragment:t,name:r,signature:s,topic:e,args:n})}}class ir{fragment;name;args;signature;selector;value;constructor(t,e,n,r){const s=t.name,i=t.format();o(this,{fragment:t,name:s,args:n,signature:i,selector:e,value:r})}}class or{fragment;name;args;signature;selector;constructor(t,e,n){const r=t.name,s=t.format();o(this,{fragment:t,name:r,args:n,signature:s,selector:e})}}class ar{hash;_isIndexed;static isIndexed(t){return!(!t||!t._isIndexed)}constructor(t){o(this,{hash:t,_isIndexed:!0})}}const cr={0:\"generic panic\",1:\"assert(false)\",17:\"arithmetic overflow\",18:\"division or modulo by zero\",33:\"enum overflow\",34:\"invalid encoded storage byte array accessed\",49:\"out-of-bounds array access; popping on an empty array\",50:\"out-of-bounds access of an array or bytesN\",65:\"out of memory\",81:\"uninitialized function\"},lr={\"0x08c379a0\":{signature:\"Error(string)\",name:\"Error\",inputs:[\"string\"],reason:t=>`reverted with reason string ${JSON.stringify(t)}`},\"0x4e487b71\":{signature:\"Panic(uint256)\",name:\"Panic\",inputs:[\"uint256\"],reason:t=>{let e=\"unknown panic code\";return t>=0&&t<=255&&cr[t.toString()]&&(e=cr[t.toString()]),`reverted with panic code 0x${t.toString(16)} (${e})`}}};class ur{fragments;deploy;fallback;receive;#Q;#j;#V;#J;constructor(t){let e=[];e=\"string\"==typeof t?JSON.parse(t):t,this.#V=new Map,this.#Q=new Map,this.#j=new Map;const n=[];for(const t of e)try{n.push(cn.from(t))}catch(e){console.log(`[Warning] Invalid Fragment ${JSON.stringify(t)}:`,e.message)}o(this,{fragments:Object.freeze(n)});let r=null,s=!1;this.#J=this.getAbiCoder(),this.fragments.forEach(((t,e)=>{let n;switch(t.type){case\"constructor\":return this.deploy?void console.log(\"duplicate definition - constructor\"):void o(this,{deploy:t});case\"fallback\":return void(0===t.inputs.length?s=!0:(f(!r||t.payable!==r.payable,\"conflicting fallback fragments\",`fragments[${e}]`,t),r=t,s=r.payable));case\"function\":n=this.#V;break;case\"event\":n=this.#j;break;case\"error\":n=this.#Q;break;default:return}const i=t.format();n.has(i)||n.set(i,t)})),this.deploy||o(this,{deploy:dn.from(\"constructor()\")}),o(this,{fallback:r,receive:s})}format(t){const e=t?\"minimal\":\"full\";return this.fragments.map((t=>t.format(e)))}formatJson(){const t=this.fragments.map((t=>t.format(\"json\")));return JSON.stringify(t.map((t=>JSON.parse(t))))}getAbiCoder(){return En.defaultAbiCoder()}#z(t,e,n){if(b(t)){const e=t.toLowerCase();for(const t of this.#V.values())if(e===t.selector)return t;return null}if(-1===t.indexOf(\"(\")){const r=[];for(const[e,n]of this.#V)e.split(\"(\")[0]===t&&r.push(n);if(e){const t=e.length>0?e[e.length-1]:null;let n=e.length,s=!0;ee.isTyped(t)&&\"overrides\"===t.type&&(s=!1,n--);for(let t=r.length-1;t>=0;t--){const e=r[t].inputs.length;e===n||s&&e===n-1||r.splice(t,1)}for(let t=r.length-1;t>=0;t--){const n=r[t].inputs;for(let s=0;s<e.length;s++)if(ee.isTyped(e[s])){if(s>=n.length){if(\"overrides\"===e[s].type)continue;r.splice(t,1);break}if(e[s].type!==n[s].baseType){r.splice(t,1);break}}}}if(1===r.length&&e&&e.length!==r[0].inputs.length){const t=e[e.length-1];(null==t||Array.isArray(t)||\"object\"!=typeof t)&&r.splice(0,1)}if(0===r.length)return null;if(r.length>1&&n){f(!1,`ambiguous function description (i.e. matches ${r.map((t=>JSON.stringify(t.format()))).join(\", \")})`,\"key\",t)}return r[0]}const r=this.#V.get(gn.from(t).format());return r||null}getFunctionName(t){const e=this.#z(t,null,!1);return f(e,\"no matching function\",\"key\",t),e.name}hasFunction(t){return!!this.#z(t,null,!1)}getFunction(t,e){return this.#z(t,e||null,!0)}forEachFunction(t){const e=Array.from(this.#V.keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(this.#V.get(r),n)}}#K(t,e,n){if(b(t)){const e=t.toLowerCase();for(const t of this.#j.values())if(e===t.topicHash)return t;return null}if(-1===t.indexOf(\"(\")){const r=[];for(const[e,n]of this.#j)e.split(\"(\")[0]===t&&r.push(n);if(e){for(let t=r.length-1;t>=0;t--)r[t].inputs.length<e.length&&r.splice(t,1);for(let t=r.length-1;t>=0;t--){const n=r[t].inputs;for(let s=0;s<e.length;s++)if(ee.isTyped(e[s])&&e[s].type!==n[s].baseType){r.splice(t,1);break}}}if(0===r.length)return null;if(r.length>1&&n){f(!1,`ambiguous event description (i.e. matches ${r.map((t=>JSON.stringify(t.format()))).join(\", \")})`,\"key\",t)}return r[0]}const r=this.#j.get(fn.from(t).format());return r||null}getEventName(t){const e=this.#K(t,null,!1);return f(e,\"no matching event\",\"key\",t),e.name}hasEvent(t){return!!this.#K(t,null,!1)}getEvent(t,e){return this.#K(t,e||null,!0)}forEachEvent(t){const e=Array.from(this.#j.keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(this.#j.get(r),n)}}getError(t,e){if(b(t)){const e=t.toLowerCase();if(lr[e])return hn.from(lr[e].signature);for(const t of this.#Q.values())if(e===t.selector)return t;return null}if(-1===t.indexOf(\"(\")){const e=[];for(const[n,r]of this.#Q)n.split(\"(\")[0]===t&&e.push(r);if(0===e.length)return\"Error\"===t?hn.from(\"error Error(string)\"):\"Panic\"===t?hn.from(\"error Panic(uint256)\"):null;if(e.length>1){f(!1,`ambiguous error description (i.e. ${e.map((t=>JSON.stringify(t.format()))).join(\", \")})`,\"name\",t)}return e[0]}if(\"Error(string)\"===(t=hn.from(t).format()))return hn.from(\"error Error(string)\");if(\"Panic(uint256)\"===t)return hn.from(\"error Panic(uint256)\");const n=this.#Q.get(t);return n||null}forEachError(t){const e=Array.from(this.#Q.keys());e.sort(((t,e)=>t.localeCompare(e)));for(let n=0;n<e.length;n++){const r=e[n];t(this.#Q.get(r),n)}}_decodeParams(t,e){return this.#J.decode(t,e)}_encodeParams(t,e){return this.#J.encode(t,e)}encodeDeploy(t){return this._encodeParams(this.deploy.inputs,t||[])}decodeErrorResult(t,e){if(\"string\"==typeof t){const e=this.getError(t);f(e,\"unknown error\",\"fragment\",t),t=e}return f(x(e,0,4)===t.selector,`data signature does not match error ${t.name}.`,\"data\",e),this._decodeParams(t.inputs,x(e,4))}encodeErrorResult(t,e){if(\"string\"==typeof t){const e=this.getError(t);f(e,\"unknown error\",\"fragment\",t),t=e}return k([t.selector,this._encodeParams(t.inputs,e||[])])}decodeFunctionData(t,e){if(\"string\"==typeof t){const e=this.getFunction(t);f(e,\"unknown function\",\"fragment\",t),t=e}return f(x(e,0,4)===t.selector,`data signature does not match function ${t.name}.`,\"data\",e),this._decodeParams(t.inputs,x(e,4))}encodeFunctionData(t,e){if(\"string\"==typeof t){const e=this.getFunction(t);f(e,\"unknown function\",\"fragment\",t),t=e}return k([t.selector,this._encodeParams(t.inputs,e||[])])}decodeFunctionResult(t,e){if(\"string\"==typeof t){const e=this.getFunction(t);f(e,\"unknown function\",\"fragment\",t),t=e}let n=\"invalid length for result data\";const r=w(e);if(r.length%32==0)try{return this.#J.decode(t.outputs,r)}catch(t){n=\"could not decode result data\"}h(!1,n,\"BAD_DATA\",{value:E(r),info:{method:t.name,signature:t.format()}})}makeError(t,e){const n=y(t,\"data\"),r=En.getBuiltinCallException(\"call\",e,n);if(r.message.startsWith(\"execution reverted (unknown custom error)\")){const t=E(n.slice(0,4)),e=this.getError(t);if(e)try{const t=this.#J.decode(e.inputs,n.slice(4));r.revert={name:e.name,signature:e.format(),args:t},r.reason=r.revert.signature,r.message=`execution reverted: ${r.reason}`}catch(t){r.message=\"execution reverted (coult not decode custom error)\"}}const s=this.parseTransaction(e);return s&&(r.invocation={method:s.name,signature:s.signature,args:s.args}),r}encodeFunctionResult(t,e){if(\"string\"==typeof t){const e=this.getFunction(t);f(e,\"unknown function\",\"fragment\",t),t=e}return E(this.#J.encode(t.outputs,e||[]))}encodeFilterTopics(t,e){if(\"string\"==typeof t){const e=this.getEvent(t);f(e,\"unknown event\",\"eventFragment\",t),t=e}h(e.length<=t.inputs.length,`too many arguments for ${t.format()}`,\"UNEXPECTED_ARGUMENT\",{count:e.length,expectedCount:t.inputs.length});const n=[];t.anonymous||n.push(t.topicHash);const r=(t,e)=>\"string\"===t.type?Pe(e):\"bytes\"===t.type?jt(E(e)):(\"bool\"===t.type&&\"boolean\"==typeof e?e=e?\"0x01\":\"0x00\":t.type.match(/^u?int/)?e=M(e):t.type.match(/^bytes/)?e=N(e,32,!1):\"address\"===t.type&&this.#J.encode([\"address\"],[e]),B(E(e),32));for(e.forEach(((e,s)=>{const i=t.inputs[s];i.indexed?null==e?n.push(null):\"array\"===i.baseType||\"tuple\"===i.baseType?f(!1,\"filtering with tuples or arrays not supported\",\"contract.\"+i.name,e):Array.isArray(e)?n.push(e.map((t=>r(i,t)))):n.push(r(i,e)):f(null==e,\"cannot filter non-indexed parameters; must be null\",\"contract.\"+i.name,e)}));n.length&&null===n[n.length-1];)n.pop();return n}encodeEventLog(t,e){if(\"string\"==typeof t){const e=this.getEvent(t);f(e,\"unknown event\",\"eventFragment\",t),t=e}const n=[],r=[],s=[];return t.anonymous||n.push(t.topicHash),f(e.length===t.inputs.length,\"event arguments/values mismatch\",\"values\",e),t.inputs.forEach(((t,i)=>{const o=e[i];if(t.indexed)if(\"string\"===t.type)n.push(Pe(o));else if(\"bytes\"===t.type)n.push(jt(o));else{if(\"tuple\"===t.baseType||\"array\"===t.baseType)throw new Error(\"not implemented\");n.push(this.#J.encode([t.type],[o]))}else r.push(t),s.push(o)})),{data:this.#J.encode(r,s),topics:n}}decodeEventLog(t,e,n){if(\"string\"==typeof t){const e=this.getEvent(t);f(e,\"unknown event\",\"eventFragment\",t),t=e}if(null!=n&&!t.anonymous){const e=t.topicHash;f(b(n[0],32)&&n[0].toLowerCase()===e,\"fragment/topic mismatch\",\"topics[0]\",n[0]),n=n.slice(1)}const r=[],s=[],i=[];t.inputs.forEach(((t,e)=>{t.indexed?\"string\"===t.type||\"bytes\"===t.type||\"tuple\"===t.baseType||\"array\"===t.baseType?(r.push(an.from({type:\"bytes32\",name:t.name})),i.push(!0)):(r.push(t),i.push(!1)):(s.push(t),i.push(!1))}));const o=null!=n?this.#J.decode(r,k(n)):null,a=this.#J.decode(s,e,!0),c=[],l=[];let u=0,h=0;return t.inputs.forEach(((t,e)=>{let n=null;if(t.indexed)if(null==o)n=new ar(null);else if(i[e])n=new ar(o[h++]);else try{n=o[h++]}catch(t){n=t}else try{n=a[u++]}catch(t){n=t}c.push(n),l.push(t.name||null)})),W.fromItems(c,l)}parseTransaction(t){const e=y(t.data,\"tx.data\"),n=S(null!=t.value?t.value:0,\"tx.value\"),r=this.getFunction(E(e.slice(0,4)));if(!r)return null;const s=this.#J.decode(r.inputs,e.slice(4));return new ir(r,r.selector,s,n)}parseCallResult(t){throw new Error(\"@TODO\")}parseLog(t){const e=this.getEvent(t.topics[0]);return!e||e.anonymous?null:new sr(e,e.topicHash,this.decodeEventLog(e,t.data,t.topics))}parseError(t){const e=E(t),n=this.getError(x(e,0,4));if(!n)return null;const r=this.#J.decode(n.inputs,x(e,4));return new or(n,n.selector,r)}static from(t){return t instanceof ur?t:\"string\"==typeof t?new ur(JSON.parse(t)):\"function\"==typeof t.formatJson?new ur(t.formatJson()):\"function\"==typeof t.format?new ur(t.format(\"json\")):new ur(t)}}const hr=BigInt(0);function fr(t){return null==t?null:t}function dr(t){return null==t?null:t.toString()}class pr{gasPrice;maxFeePerGas;maxPriorityFeePerGas;constructor(t,e,n){o(this,{gasPrice:fr(t),maxFeePerGas:fr(e),maxPriorityFeePerGas:fr(n)})}toJSON(){const{gasPrice:t,maxFeePerGas:e,maxPriorityFeePerGas:n}=this;return{_type:\"FeeData\",gasPrice:dr(t),maxFeePerGas:dr(e),maxPriorityFeePerGas:dr(n)}}}function gr(t){const e={};t.to&&(e.to=t.to),t.from&&(e.from=t.from),t.data&&(e.data=E(t.data));const n=\"chainId,gasLimit,gasPrice,maxFeePerBlobGas,maxFeePerGas,maxPriorityFeePerGas,value\".split(/,/);for(const r of n)r in t&&null!=t[r]&&(e[r]=S(t[r],`request.${r}`));const r=\"type,nonce\".split(/,/);for(const n of r)n in t&&null!=t[n]&&(e[n]=L(t[n],`request.${n}`));return t.accessList&&(e.accessList=jn(t.accessList)),\"blockTag\"in t&&(e.blockTag=t.blockTag),\"enableCcipRead\"in t&&(e.enableCcipRead=!!t.enableCcipRead),\"customData\"in t&&(e.customData=t.customData),\"blobVersionedHashes\"in t&&t.blobVersionedHashes&&(e.blobVersionedHashes=t.blobVersionedHashes.slice()),\"kzg\"in t&&(e.kzg=t.kzg),\"blobs\"in t&&t.blobs&&(e.blobs=t.blobs.map((t=>A(t)?E(t):Object.assign({},t)))),e}class mr{provider;number;hash;timestamp;parentHash;parentBeaconBlockRoot;nonce;difficulty;gasLimit;gasUsed;stateRoot;receiptsRoot;blobGasUsed;excessBlobGas;miner;prevRandao;extraData;baseFeePerGas;#q;constructor(t,e){this.#q=t.transactions.map((t=>\"string\"!=typeof t?new br(t,e):t)),o(this,{provider:e,hash:fr(t.hash),number:t.number,timestamp:t.timestamp,parentHash:t.parentHash,parentBeaconBlockRoot:t.parentBeaconBlockRoot,nonce:t.nonce,difficulty:t.difficulty,gasLimit:t.gasLimit,gasUsed:t.gasUsed,blobGasUsed:t.blobGasUsed,excessBlobGas:t.excessBlobGas,miner:t.miner,prevRandao:fr(t.prevRandao),extraData:t.extraData,baseFeePerGas:fr(t.baseFeePerGas),stateRoot:t.stateRoot,receiptsRoot:t.receiptsRoot})}get transactions(){return this.#q.map((t=>\"string\"==typeof t?t:t.hash))}get prefetchedTransactions(){const t=this.#q.slice();return 0===t.length?[]:(h(\"object\"==typeof t[0],\"transactions were not prefetched with block request\",\"UNSUPPORTED_OPERATION\",{operation:\"transactionResponses()\"}),t)}toJSON(){const{baseFeePerGas:t,difficulty:e,extraData:n,gasLimit:r,gasUsed:s,hash:i,miner:o,prevRandao:a,nonce:c,number:l,parentHash:u,parentBeaconBlockRoot:h,stateRoot:f,receiptsRoot:d,timestamp:p,transactions:g}=this;return{_type:\"Block\",baseFeePerGas:dr(t),difficulty:dr(e),extraData:n,gasLimit:dr(r),gasUsed:dr(s),blobGasUsed:dr(this.blobGasUsed),excessBlobGas:dr(this.excessBlobGas),hash:i,miner:o,prevRandao:a,nonce:c,number:l,parentHash:u,timestamp:p,parentBeaconBlockRoot:h,stateRoot:f,receiptsRoot:d,transactions:g}}[Symbol.iterator](){let t=0;const e=this.transactions;return{next:()=>t<this.length?{value:e[t++],done:!1}:{value:void 0,done:!0}}}get length(){return this.#q.length}get date(){return null==this.timestamp?null:new Date(1e3*this.timestamp)}async getTransaction(t){let e;if(\"number\"==typeof t)e=this.#q[t];else{const n=t.toLowerCase();for(const t of this.#q){if(\"string\"==typeof t){if(t!==n)continue;e=t;break}if(t.hash!==n){e=t;break}}}if(null==e)throw new Error(\"no such tx\");return\"string\"==typeof e?await this.provider.getTransaction(e):e}getPrefetchedTransaction(t){const e=this.prefetchedTransactions;if(\"number\"==typeof t)return e[t];t=t.toLowerCase();for(const n of e)if(n.hash===t)return n;f(!1,\"no matching transaction\",\"indexOrHash\",t)}isMined(){return!!this.hash}isLondon(){return!!this.baseFeePerGas}orphanedEvent(){if(!this.isMined())throw new Error(\"\");return{orphan:\"drop-block\",hash:(t=this).hash,number:t.number};var t}}class yr{provider;transactionHash;blockHash;blockNumber;removed;address;data;topics;index;transactionIndex;constructor(t,e){this.provider=e;const n=Object.freeze(t.topics.slice());o(this,{transactionHash:t.transactionHash,blockHash:t.blockHash,blockNumber:t.blockNumber,removed:t.removed,address:t.address,data:t.data,topics:n,index:t.index,transactionIndex:t.transactionIndex})}toJSON(){const{address:t,blockHash:e,blockNumber:n,data:r,index:s,removed:i,topics:o,transactionHash:a,transactionIndex:c}=this;return{_type:\"log\",address:t,blockHash:e,blockNumber:n,data:r,index:s,removed:i,topics:o,transactionHash:a,transactionIndex:c}}async getBlock(){const t=await this.provider.getBlock(this.blockHash);return h(!!t,\"failed to find transaction\",\"UNKNOWN_ERROR\",{}),t}async getTransaction(){const t=await this.provider.getTransaction(this.transactionHash);return h(!!t,\"failed to find transaction\",\"UNKNOWN_ERROR\",{}),t}async getTransactionReceipt(){const t=await this.provider.getTransactionReceipt(this.transactionHash);return h(!!t,\"failed to find transaction receipt\",\"UNKNOWN_ERROR\",{}),t}removedEvent(){return{orphan:\"drop-log\",log:{transactionHash:(t=this).transactionHash,blockHash:t.blockHash,blockNumber:t.blockNumber,address:t.address,data:t.data,topics:Object.freeze(t.topics.slice()),index:t.index}};var t}}class wr{provider;to;from;contractAddress;hash;index;blockHash;blockNumber;logsBloom;gasUsed;blobGasUsed;cumulativeGasUsed;gasPrice;blobGasPrice;type;status;root;#_;constructor(t,e){this.#_=Object.freeze(t.logs.map((t=>new yr(t,e))));let n=hr;null!=t.effectiveGasPrice?n=t.effectiveGasPrice:null!=t.gasPrice&&(n=t.gasPrice),o(this,{provider:e,to:t.to,from:t.from,contractAddress:t.contractAddress,hash:t.hash,index:t.index,blockHash:t.blockHash,blockNumber:t.blockNumber,logsBloom:t.logsBloom,gasUsed:t.gasUsed,cumulativeGasUsed:t.cumulativeGasUsed,blobGasUsed:t.blobGasUsed,gasPrice:n,blobGasPrice:t.blobGasPrice,type:t.type,status:t.status,root:t.root})}get logs(){return this.#_}toJSON(){const{to:t,from:e,contractAddress:n,hash:r,index:s,blockHash:i,blockNumber:o,logsBloom:a,logs:c,status:l,root:u}=this;return{_type:\"TransactionReceipt\",blockHash:i,blockNumber:o,contractAddress:n,cumulativeGasUsed:dr(this.cumulativeGasUsed),from:e,gasPrice:dr(this.gasPrice),blobGasUsed:dr(this.blobGasUsed),blobGasPrice:dr(this.blobGasPrice),gasUsed:dr(this.gasUsed),hash:r,index:s,logs:c,logsBloom:a,root:u,status:l,to:t}}get length(){return this.logs.length}[Symbol.iterator](){let t=0;return{next:()=>t<this.length?{value:this.logs[t++],done:!1}:{value:void 0,done:!0}}}get fee(){return this.gasUsed*this.gasPrice}async getBlock(){const t=await this.provider.getBlock(this.blockHash);if(null==t)throw new Error(\"TODO\");return t}async getTransaction(){const t=await this.provider.getTransaction(this.hash);if(null==t)throw new Error(\"TODO\");return t}async getResult(){return await this.provider.getTransactionResult(this.hash)}async confirmations(){return await this.provider.getBlockNumber()-this.blockNumber+1}removedEvent(){return vr(this)}reorderedEvent(t){return h(!t||t.isMined(),\"unmined 'other' transction cannot be orphaned\",\"UNSUPPORTED_OPERATION\",{operation:\"reorderedEvent(other)\"}),Ar(this,t)}}class br{provider;blockNumber;blockHash;index;hash;type;to;from;nonce;gasLimit;gasPrice;maxPriorityFeePerGas;maxFeePerGas;maxFeePerBlobGas;data;value;chainId;signature;accessList;blobVersionedHashes;#Z;constructor(t,e){this.provider=e,this.blockNumber=null!=t.blockNumber?t.blockNumber:null,this.blockHash=null!=t.blockHash?t.blockHash:null,this.hash=t.hash,this.index=t.index,this.type=t.type,this.from=t.from,this.to=t.to||null,this.gasLimit=t.gasLimit,this.nonce=t.nonce,this.data=t.data,this.value=t.value,this.gasPrice=t.gasPrice,this.maxPriorityFeePerGas=null!=t.maxPriorityFeePerGas?t.maxPriorityFeePerGas:null,this.maxFeePerGas=null!=t.maxFeePerGas?t.maxFeePerGas:null,this.maxFeePerBlobGas=null!=t.maxFeePerBlobGas?t.maxFeePerBlobGas:null,this.chainId=t.chainId,this.signature=t.signature,this.accessList=null!=t.accessList?t.accessList:null,this.blobVersionedHashes=null!=t.blobVersionedHashes?t.blobVersionedHashes:null,this.#Z=-1}toJSON(){const{blockNumber:t,blockHash:e,index:n,hash:r,type:s,to:i,from:o,nonce:a,data:c,signature:l,accessList:u,blobVersionedHashes:h}=this;return{_type:\"TransactionResponse\",accessList:u,blockNumber:t,blockHash:e,blobVersionedHashes:h,chainId:dr(this.chainId),data:c,from:o,gasLimit:dr(this.gasLimit),gasPrice:dr(this.gasPrice),hash:r,maxFeePerGas:dr(this.maxFeePerGas),maxPriorityFeePerGas:dr(this.maxPriorityFeePerGas),maxFeePerBlobGas:dr(this.maxFeePerBlobGas),nonce:a,signature:l,to:i,index:n,type:s,value:dr(this.value)}}async getBlock(){let t=this.blockNumber;if(null==t){const e=await this.getTransaction();e&&(t=e.blockNumber)}if(null==t)return null;const e=this.provider.getBlock(t);if(null==e)throw new Error(\"TODO\");return e}async getTransaction(){return this.provider.getTransaction(this.hash)}async confirmations(){if(null==this.blockNumber){const{tx:t,blockNumber:e}=await i({tx:this.getTransaction(),blockNumber:this.provider.getBlockNumber()});return null==t||null==t.blockNumber?0:e-t.blockNumber+1}return await this.provider.getBlockNumber()-this.blockNumber+1}async wait(t,e){const n=null==t?1:t,r=null==e?0:e;let s=this.#Z,o=-1,a=-1===s;const l=async()=>{if(a)return null;const{blockNumber:t,nonce:e}=await i({blockNumber:this.provider.getBlockNumber(),nonce:this.provider.getTransactionCount(this.from)});if(e<this.nonce)return void(s=t);if(a)return null;const r=await this.getTransaction();if(!r||null==r.blockNumber)for(-1===o&&(o=s-3,o<this.#Z&&(o=this.#Z));o<=t;){if(a)return null;const e=await this.provider.getBlock(o,!0);if(null==e)return;for(const t of e)if(t===this.hash)return;for(let r=0;r<e.length;r++){const i=await e.getTransaction(r);if(i.from===this.from&&i.nonce===this.nonce){if(a)return null;const e=await this.provider.getTransactionReceipt(i.hash);if(null==e)return;if(t-e.blockNumber+1<n)return;let r=\"replaced\";i.data===this.data&&i.to===this.to&&i.value===this.value?r=\"repriced\":\"0x\"===i.data&&i.from===i.to&&i.value===hr&&(r=\"cancelled\"),h(!1,\"transaction was replaced\",\"TRANSACTION_REPLACED\",{cancelled:\"replaced\"===r||\"cancelled\"===r,reason:r,replacement:i.replaceableTransaction(s),hash:i.hash,receipt:e})}}o++}},f=t=>{if(null==t||0!==t.status)return t;h(!1,\"transaction execution reverted\",\"CALL_EXCEPTION\",{action:\"sendTransaction\",data:null,reason:null,invocation:null,revert:null,transaction:{to:t.to,from:t.from,data:\"\"},receipt:t})},d=await this.provider.getTransactionReceipt(this.hash);if(0===n)return f(d);if(d){if(await d.confirmations()>=n)return f(d)}else if(await l(),0===n)return null;const p=new Promise(((t,e)=>{const i=[],o=()=>{i.forEach((t=>t()))};if(i.push((()=>{a=!0})),r>0){const t=setTimeout((()=>{o(),e(u(\"wait for transaction timeout\",\"TIMEOUT\"))}),r);i.push((()=>{clearTimeout(t)}))}const h=async r=>{if(await r.confirmations()>=n){o();try{t(f(r))}catch(t){e(t)}}};if(i.push((()=>{this.provider.off(this.hash,h)})),this.provider.on(this.hash,h),s>=0){const t=async()=>{try{await l()}catch(t){if(c(t,\"TRANSACTION_REPLACED\"))return o(),void e(t)}a||this.provider.once(\"block\",t)};i.push((()=>{this.provider.off(\"block\",t)})),this.provider.once(\"block\",t)}}));return await p}isMined(){return null!=this.blockHash}isLegacy(){return 0===this.type}isBerlin(){return 1===this.type}isLondon(){return 2===this.type}isCancun(){return 3===this.type}removedEvent(){return h(this.isMined(),\"unmined transaction canot be orphaned\",\"UNSUPPORTED_OPERATION\",{operation:\"removeEvent()\"}),vr(this)}reorderedEvent(t){return h(this.isMined(),\"unmined transaction canot be orphaned\",\"UNSUPPORTED_OPERATION\",{operation:\"removeEvent()\"}),h(!t||t.isMined(),\"unmined 'other' transaction canot be orphaned\",\"UNSUPPORTED_OPERATION\",{operation:\"removeEvent()\"}),Ar(this,t)}replaceableTransaction(t){f(Number.isInteger(t)&&t>=0,\"invalid startBlock\",\"startBlock\",t);const e=new br(this,this.provider);return e.#Z=t,e}}function Ar(t,e){return{orphan:\"reorder-transaction\",tx:t,other:e}}function vr(t){return{orphan:\"drop-transaction\",tx:t}}class Er{filter;emitter;#W;constructor(t,e,n){this.#W=e,o(this,{emitter:t,filter:n})}async removeListener(){null!=this.#W&&await this.emitter.off(this.filter,this.#W)}}class kr extends yr{interface;fragment;args;constructor(t,e,n){super(t,t.provider);o(this,{args:e.decodeEventLog(n,t.data,t.topics),fragment:n,interface:e})}get eventName(){return this.fragment.name}get eventSignature(){return this.fragment.format()}}class Pr extends yr{error;constructor(t,e){super(t,t.provider),o(this,{error:e})}}class xr extends wr{#Y;constructor(t,e,n){super(n,e),this.#Y=t}get logs(){return super.logs.map((t=>{const e=t.topics.length?this.#Y.getEvent(t.topics[0]):null;if(e)try{return new kr(t,this.#Y,e)}catch(e){return new Pr(t,e)}return t}))}}class Nr extends br{#Y;constructor(t,e,n){super(n,e),this.#Y=t}async wait(t,e){const n=await super.wait(t,e);return null==n?null:new xr(this.#Y,this.provider,n)}}class Br extends Er{log;constructor(t,e,n,r){super(t,e,n),o(this,{log:r})}async getBlock(){return await this.log.getBlock()}async getTransaction(){return await this.log.getTransaction()}async getTransactionReceipt(){return await this.log.getTransactionReceipt()}}class Ir extends Br{constructor(t,e,n,r,s){super(t,e,n,new kr(s,t.interface,r));o(this,{args:t.interface.decodeEventLog(r,this.log.data,this.log.topics),fragment:r})}get eventName(){return this.fragment.name}get eventSignature(){return this.fragment.format()}}const Cr=BigInt(0);function Or(t){return t&&\"function\"==typeof t.call}function Rr(t){return t&&\"function\"==typeof t.estimateGas}function Tr(t){return t&&\"function\"==typeof t.resolveName}function Sr(t){return t&&\"function\"==typeof t.sendTransaction}function Fr(t){if(null!=t){if(Tr(t))return t;if(t.provider)return t.provider}}class Ur{#X;fragment;constructor(t,e,n){if(o(this,{fragment:e}),e.inputs.length<n.length)throw new Error(\"too many arguments\");const r=Dr(t.runner,\"resolveName\"),s=Tr(r)?r:null;this.#X=async function(){const r=await Promise.all(e.inputs.map(((t,e)=>null==n[e]?null:t.walkAsync(n[e],((t,e)=>\"address\"===t?Array.isArray(e)?Promise.all(e.map((t=>xn(t,s)))):xn(e,s):e)))));return t.interface.encodeFilterTopics(e,r)}()}getTopicFilter(){return this.#X}}function Dr(t,e){return null==t?null:\"function\"==typeof t[e]?t:t.provider&&\"function\"==typeof t.provider[e]?t.provider:null}function Lr(t){return null==t?null:t.provider||null}async function Mr(t,e){const n=ee.dereference(t,\"overrides\");f(\"object\"==typeof n,\"invalid overrides parameter\",\"overrides\",t);const r=gr(n);return f(null==r.to||(e||[]).indexOf(\"to\")>=0,\"cannot override to\",\"overrides.to\",r.to),f(null==r.data||(e||[]).indexOf(\"data\")>=0,\"cannot override data\",\"overrides.data\",r.data),r.from&&(r.from=r.from),r}function Gr(t){const e=async function(e){const n=await Mr(e,[\"data\"]);n.to=await t.getAddress(),n.from&&(n.from=await xn(n.from,Fr(t.runner)));const r=t.interface,s=S(n.value||Cr,\"overrides.value\")===Cr,i=\"0x\"===(n.data||\"0x\");!r.fallback||r.fallback.payable||!r.receive||i||s||f(!1,\"cannot send data to receive or send value to non-payable fallback\",\"overrides\",e),f(r.fallback||i,\"cannot send data to receive-only contract\",\"overrides.data\",n.data);return f(r.receive||r.fallback&&r.fallback.payable||s,\"cannot send value to non-payable fallback\",\"overrides.value\",n.value),f(r.fallback||i,\"cannot send data to receive-only contract\",\"overrides.data\",n.data),n},n=async function(n){const r=t.runner;h(Sr(r),\"contract runner does not support sending transactions\",\"UNSUPPORTED_OPERATION\",{operation:\"sendTransaction\"});const s=await r.sendTransaction(await e(n)),i=Lr(t.runner);return new Nr(t.interface,i,s)},r=async t=>await n(t);return o(r,{_contract:t,estimateGas:async function(n){const r=Dr(t.runner,\"estimateGas\");return h(Rr(r),\"contract runner does not support gas estimation\",\"UNSUPPORTED_OPERATION\",{operation:\"estimateGas\"}),await r.estimateGas(await e(n))},populateTransaction:e,send:n,staticCall:async function(n){const r=Dr(t.runner,\"call\");h(Or(r),\"contract runner does not support calling\",\"UNSUPPORTED_OPERATION\",{operation:\"call\"});const s=await e(n);try{return await r.call(s)}catch(e){if(l(e)&&e.data)throw t.interface.makeError(e.data,s);throw e}}}),r}function Hr(t,e){const n=function(...n){const r=t.interface.getFunction(e,n);return h(r,\"no matching fragment\",\"UNSUPPORTED_OPERATION\",{operation:\"fragment\",info:{key:e,args:n}}),r},r=async function(...e){const r=n(...e);let s={};if(r.inputs.length+1===e.length&&(s=await Mr(e.pop()),s.from&&(s.from=await xn(s.from,Fr(t.runner)))),r.inputs.length!==e.length)throw new Error(\"internal error: fragment inputs doesn't match arguments; should not happen\");const o=await async function(t,e,n){const r=Dr(t,\"resolveName\"),s=Tr(r)?r:null;return await Promise.all(e.map(((t,e)=>t.walkAsync(n[e],((t,e)=>(e=ee.dereference(e,t),\"address\"===t?xn(e,s):e))))))}(t.runner,r.inputs,e);return Object.assign({},s,await i({to:t.getAddress(),data:t.interface.encodeFunctionData(r,o)}))},s=async function(...t){const e=await c(...t);return 1===e.length?e[0]:e},a=async function(...e){const n=t.runner;h(Sr(n),\"contract runner does not support sending transactions\",\"UNSUPPORTED_OPERATION\",{operation:\"sendTransaction\"});const s=await n.sendTransaction(await r(...e)),i=Lr(t.runner);return new Nr(t.interface,i,s)},c=async function(...e){const s=Dr(t.runner,\"call\");h(Or(s),\"contract runner does not support calling\",\"UNSUPPORTED_OPERATION\",{operation:\"call\"});const i=await r(...e);let o=\"0x\";try{o=await s.call(i)}catch(e){if(l(e)&&e.data)throw t.interface.makeError(e.data,i);throw e}const a=n(...e);return t.interface.decodeFunctionResult(a,o)},u=async(...t)=>n(...t).constant?await s(...t):await a(...t);return o(u,{name:t.interface.getFunctionName(e),_contract:t,_key:e,getFragment:n,estimateGas:async function(...e){const n=Dr(t.runner,\"estimateGas\");return h(Rr(n),\"contract runner does not support gas estimation\",\"UNSUPPORTED_OPERATION\",{operation:\"estimateGas\"}),await n.estimateGas(await r(...e))},populateTransaction:r,send:a,staticCall:s,staticCallResult:c}),Object.defineProperty(u,\"fragment\",{configurable:!1,enumerable:!0,get:()=>{const n=t.interface.getFunction(e);return h(n,\"no matching fragment\",\"UNSUPPORTED_OPERATION\",{operation:\"fragment\",info:{key:e}}),n}}),u}const Qr=Symbol.for(\"_ethersInternal_contract\"),jr=new WeakMap;function Vr(t){return jr.get(t[Qr])}async function Jr(t,e){let n,r=null;if(Array.isArray(e)){const r=function(e){if(b(e,32))return e;const n=t.interface.getEvent(e);return f(n,\"unknown fragment\",\"name\",e),n.topicHash};n=e.map((t=>null==t?null:Array.isArray(t)?t.map(r):r(t)))}else\"*\"===e?n=[null]:\"string\"==typeof e?b(e,32)?n=[e]:(r=t.interface.getEvent(e),f(r,\"unknown fragment\",\"event\",e),n=[r.topicHash]):(s=e)&&\"object\"==typeof s&&\"getTopicFilter\"in s&&\"function\"==typeof s.getTopicFilter&&s.fragment?n=await e.getTopicFilter():\"fragment\"in e?(r=e.fragment,n=[r.topicHash]):f(!1,\"unknown event name\",\"event\",e);var s;n=n.map((t=>{if(null==t)return null;if(Array.isArray(t)){const e=Array.from(new Set(t.map((t=>t.toLowerCase()))).values());return 1===e.length?e[0]:(e.sort(),e)}return t.toLowerCase()}));return{fragment:r,tag:n.map((t=>null==t?\"null\":Array.isArray(t)?t.join(\"|\"):t)).join(\"&\"),topics:n}}async function zr(t,e){const{subs:n}=Vr(t);return n.get((await Jr(t,e)).tag)||null}async function Kr(t,e,n){const r=Lr(t.runner);h(r,\"contract runner does not support subscribing\",\"UNSUPPORTED_OPERATION\",{operation:e});const{fragment:s,tag:i,topics:o}=await Jr(t,n),{addr:a,subs:c}=Vr(t);let l=c.get(i);if(!l){const e={address:a||t,topics:o},u=e=>{let r=s;if(null==r)try{r=t.interface.getEvent(e.topics[0])}catch(t){}if(r){const i=r,o=s?t.interface.decodeEventLog(s,e.data,e.topics):[];_r(t,n,o,(r=>new Ir(t,r,n,i,e)))}else _r(t,n,[],(r=>new Br(t,r,n,e)))};let h=[];l={tag:i,listeners:[],start:()=>{h.length||h.push(r.on(e,u))},stop:async()=>{if(0==h.length)return;let t=h;h=[],await Promise.all(t),r.off(e,u)}},c.set(i,l)}return l}let qr=Promise.resolve();async function _r(t,e,n,r){try{await qr}catch(t){}const s=async function(t,e,n,r){await qr;const s=await zr(t,e);if(!s)return!1;const i=s.listeners.length;return s.listeners=s.listeners.filter((({listener:e,once:s})=>{const i=Array.from(n);r&&i.push(r(s?null:e));try{e.call(t,...i)}catch(t){}return!s})),0===s.listeners.length&&(s.stop(),Vr(t).subs.delete(s.tag)),i>0}(t,e,n,r);return qr=s,await s}const Zr=[\"then\"];class Wr{target;interface;runner;filters;[Qr];fallback;constructor(t,e,n,r){f(\"string\"==typeof t||kn(t),\"invalid value for Contract target\",\"target\",t),null==n&&(n=null);const s=ur.from(e);let i;o(this,{target:t,runner:n,interface:s}),Object.defineProperty(this,Qr,{value:{}});let a=null,l=null;if(r){const t=Lr(n);l=new Nr(this.interface,t,r)}let h=new Map;if(\"string\"==typeof t)if(b(t))a=t,i=Promise.resolve(t);else{const e=Dr(n,\"resolveName\");if(!Tr(e))throw u(\"contract runner does not support name resolution\",\"UNSUPPORTED_OPERATION\",{operation:\"resolveName\"});i=e.resolveName(t).then((e=>{if(null==e)throw u(\"an ENS name used for a contract target must be correctly configured\",\"UNCONFIGURED_NAME\",{value:t});return Vr(this).addr=e,e}))}else i=t.getAddress().then((t=>{if(null==t)throw new Error(\"TODO\");return Vr(this).addr=t,t}));var d,p;d=this,p={addrPromise:i,addr:a,deployTx:l,subs:h},jr.set(d[Qr],p);const g=new Proxy({},{get:(t,e,n)=>{if(\"symbol\"==typeof e||Zr.indexOf(e)>=0)return Reflect.get(t,e,n);try{return this.getEvent(e)}catch(t){if(!c(t,\"INVALID_ARGUMENT\")||\"key\"!==t.argument)throw t}},has:(t,e)=>Zr.indexOf(e)>=0?Reflect.has(t,e):Reflect.has(t,e)||this.interface.hasEvent(String(e))});return o(this,{filters:g}),o(this,{fallback:s.receive||s.fallback?Gr(this):null}),new Proxy(this,{get:(t,e,n)=>{if(\"symbol\"==typeof e||e in t||Zr.indexOf(e)>=0)return Reflect.get(t,e,n);try{return t.getFunction(e)}catch(t){if(!c(t,\"INVALID_ARGUMENT\")||\"key\"!==t.argument)throw t}},has:(t,e)=>\"symbol\"==typeof e||e in t||Zr.indexOf(e)>=0?Reflect.has(t,e):t.interface.hasFunction(e)})}connect(t){return new Wr(this.target,this.interface,t)}attach(t){return new Wr(t,this.interface,this.runner)}async getAddress(){return await Vr(this).addrPromise}async getDeployedCode(){const t=Lr(this.runner);h(t,\"runner does not support .provider\",\"UNSUPPORTED_OPERATION\",{operation:\"getDeployedCode\"});const e=await t.getCode(await this.getAddress());return\"0x\"===e?null:e}async waitForDeployment(){const t=this.deploymentTransaction();if(t)return await t.wait(),this;if(null!=await this.getDeployedCode())return this;const e=Lr(this.runner);return h(null!=e,\"contract runner does not support .provider\",\"UNSUPPORTED_OPERATION\",{operation:\"waitForDeployment\"}),new Promise(((t,n)=>{const r=async()=>{try{if(null!=await this.getDeployedCode())return t(this);e.once(\"block\",r)}catch(t){n(t)}};r()}))}deploymentTransaction(){return Vr(this).deployTx}getFunction(t){\"string\"!=typeof t&&(t=t.format());return Hr(this,t)}getEvent(t){return\"string\"!=typeof t&&(t=t.format()),function(t,e){const n=function(...n){const r=t.interface.getEvent(e,n);return h(r,\"no matching fragment\",\"UNSUPPORTED_OPERATION\",{operation:\"fragment\",info:{key:e,args:n}}),r},r=function(...e){return new Ur(t,n(...e),e)};return o(r,{name:t.interface.getEventName(e),_contract:t,_key:e,getFragment:n}),Object.defineProperty(r,\"fragment\",{configurable:!1,enumerable:!0,get:()=>{const n=t.interface.getEvent(e);return h(n,\"no matching fragment\",\"UNSUPPORTED_OPERATION\",{operation:\"fragment\",info:{key:e}}),n}}),r}(this,t)}async queryTransaction(t){throw new Error(\"@TODO\")}async queryFilter(t,e,n){null==e&&(e=0),null==n&&(n=\"latest\");const{addr:r,addrPromise:s}=Vr(this),i=r||await s,{fragment:o,topics:a}=await Jr(this,t),c={address:i,topics:a,fromBlock:e,toBlock:n},l=Lr(this.runner);return h(l,\"contract runner does not have a provider\",\"UNSUPPORTED_OPERATION\",{operation:\"queryFilter\"}),(await l.getLogs(c)).map((t=>{let e=o;if(null==e)try{e=this.interface.getEvent(t.topics[0])}catch(t){}if(e)try{return new kr(t,this.interface,e)}catch(e){return new Pr(t,e)}return new yr(t,l)}))}async on(t,e){const n=await Kr(this,\"on\",t);return n.listeners.push({listener:e,once:!1}),n.start(),this}async once(t,e){const n=await Kr(this,\"once\",t);return n.listeners.push({listener:e,once:!0}),n.start(),this}async emit(t,...e){return await _r(this,t,e,null)}async listenerCount(t){if(t){const e=await zr(this,t);return e?e.listeners.length:0}const{subs:e}=Vr(this);let n=0;for(const{listeners:t}of e.values())n+=t.length;return n}async listeners(t){if(t){const e=await zr(this,t);return e?e.listeners.map((({listener:t})=>t)):[]}const{subs:e}=Vr(this);let n=[];for(const{listeners:t}of e.values())n=n.concat(t.map((({listener:t})=>t)));return n}async off(t,e){const n=await zr(this,t);if(!n)return this;if(e){const t=n.listeners.map((({listener:t})=>t)).indexOf(e);t>=0&&n.listeners.splice(t,1)}return null!=e&&0!==n.listeners.length||(n.stop(),Vr(this).subs.delete(n.tag)),this}async removeAllListeners(t){if(t){const e=await zr(this,t);if(!e)return this;e.stop(),Vr(this).subs.delete(e.tag)}else{const{subs:t}=Vr(this);for(const{tag:e,stop:n}of t.values())n(),t.delete(e)}return this}async addListener(t,e){return await this.on(t,e)}async removeListener(t,e){return await this.off(t,e)}static buildClass(t){return class extends Wr{constructor(e,n=null){super(e,t,n)}}}static from(t,e,n){null==n&&(n=null);return new this(t,e,n)}}class Yr extends(function(){return Wr}()){}var Xr=\"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\";const $r=new Map([[8217,\"apostrophe\"],[8260,\"fraction slash\"],[12539,\"middle dot\"]]),ts=4;function es(t){return function(t){let e=0;return()=>t[e++]}(function(t){let e=0;function n(){return t[e++]<<8|t[e++]}let r=n(),s=1,i=[0,1];for(let t=1;t<r;t++)i.push(s+=n());let o=n(),a=e;e+=o;let c=0,l=0;function u(){return 0==c&&(l=l<<8|t[e++],c=8),l>>--c&1}const h=2**31,f=h>>>1,d=h-1;let p=0;for(let t=0;t<31;t++)p=p<<1|u();let g=[],m=0,y=h;for(;;){let t=Math.floor(((p-m+1)*s-1)/y),e=0,n=r;for(;n-e>1;){let r=e+n>>>1;t<i[r]?n=r:e=r}if(0==e)break;g.push(e);let o=m+Math.floor(y*i[e]/s),a=m+Math.floor(y*i[e+1]/s)-1;for(;0==((o^a)&f);)p=p<<1&d|u(),o=o<<1&d,a=a<<1&d|1;for(;o&~a&536870912;)p=p&f|p<<1&d>>>1|u(),o=o<<1^f,a=(a^f)<<1|f|1;m=o,y=1+a-o}let w=r-4;return g.map((e=>{switch(e-w){case 3:return w+65792+(t[a++]<<16|t[a++]<<8|t[a++]);case 2:return w+256+(t[a++]<<8|t[a++]);case 1:return w+t[a++];default:return e-1}}))}(function(t){let e=[];[...\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\"].forEach(((t,n)=>e[t.charCodeAt(0)]=n));let n=t.length,r=new Uint8Array(6*n>>3);for(let s=0,i=0,o=0,a=0;s<n;s++)a=a<<6|e[t.charCodeAt(s)],o+=6,o>=8&&(r[i++]=a>>(o-=8));return r}(t)))}function ns(t){return 1&t?~t>>1:t>>1}function rs(t,e){let n=Array(t);for(let r=0,s=0;r<t;r++)n[r]=s+=ns(e());return n}function ss(t,e=0){let n=[];for(;;){let r=t(),s=t();if(!s)break;e+=r;for(let t=0;t<s;t++)n.push(e+t);e+=s+1}return n}function is(t){return as((()=>{let e=ss(t);if(e.length)return e}))}function os(t){let e=[];for(;;){let n=t();if(0==n)break;e.push(ls(n,t))}for(;;){let n=t()-1;if(n<0)break;e.push(us(n,t))}return e.flat()}function as(t){let e=[];for(;;){let n=t(e.length);if(!n)break;e.push(n)}return e}function cs(t,e,n){let r=Array(t).fill().map((()=>[]));for(let s=0;s<e;s++)rs(t,n).forEach(((t,e)=>r[e].push(t)));return r}function ls(t,e){let n=1+e(),r=e(),s=as(e);return cs(s.length,1+t,e).flatMap(((t,e)=>{let[i,...o]=t;return Array(s[e]).fill().map(((t,e)=>{let s=e*r;return[i+e*n,o.map((t=>t+s))]}))}))}function us(t,e){return cs(1+e(),1+t,e).map((t=>[t[0],t.slice(1)]))}function hs(t){return`{${function(t){return t.toString(16).toUpperCase().padStart(2,\"0\")}(t)}}`}function fs(t){let e=[];for(let n=0,r=t.length;n<r;){let r=t.codePointAt(n);n+=r<65536?1:2,e.push(r)}return e}function ds(t){let e=t.length;if(e<4096)return String.fromCodePoint(...t);let n=[];for(let r=0;r<e;)n.push(String.fromCodePoint(...t.slice(r,r+=4096)));return n.join(\"\")}function ps(t,e){let n=t.length,r=n-e.length;for(let s=0;0==r&&s<n;s++)r=t[s]-e[s];return r}var gs=\"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\";const ms=44032,ys=4352,ws=4449,bs=4519,As=28,vs=21*As,Es=ms+19*vs,ks=ys+19,Ps=ws+21,xs=bs+As;function Ns(t){return t>>24&255}function Bs(t){return 16777215&t}let Is,Cs,Os,Rs;function Ts(t){return t>=ms&&t<Es}function Ss(t,e){if(t>=ys&&t<ks&&e>=ws&&e<Ps)return ms+(t-ys)*vs+(e-ws)*As;if(Ts(t)&&e>bs&&e<xs&&(t-ms)%As==0)return t+(e-bs);{let n=Rs.get(t);return n&&(n=n.get(e),n)?n:-1}}function Fs(t){Is||function(){let t=es(gs);Is=new Map(is(t).flatMap(((t,e)=>t.map((t=>[t,e+1<<24]))))),Cs=new Set(ss(t)),Os=new Map,Rs=new Map;for(let[e,n]of os(t)){if(!Cs.has(e)&&2==n.length){let[t,r]=n,s=Rs.get(t);s||(s=new Map,Rs.set(t,s)),s.set(r,e)}Os.set(e,n.reverse())}}();let e=[],n=[],r=!1;function s(t){let n=Is.get(t);n&&(r=!0,t|=n),e.push(t)}for(let r of t)for(;;){if(r<128)e.push(r);else if(Ts(r)){let t=r-ms,e=t%vs/As|0,n=t%As;s(ys+(t/vs|0)),s(ws+e),n>0&&s(bs+n)}else{let t=Os.get(r);t?n.push(...t):s(r)}if(!n.length)break;r=n.pop()}if(r&&e.length>1){let t=Ns(e[0]);for(let n=1;n<e.length;n++){let r=Ns(e[n]);if(0==r||t<=r){t=r;continue}let s=n-1;for(;;){let n=e[s+1];if(e[s+1]=e[s],e[s]=n,!s)break;if(t=Ns(e[--s]),t<=r)break}t=Ns(e[n])}}return e}function Us(t){return Fs(t).map(Bs)}function Ds(t){return function(t){let e=[],n=[],r=-1,s=0;for(let i of t){let t=Ns(i),o=Bs(i);if(-1==r)0==t?r=o:e.push(o);else if(s>0&&s>=t)0==t?(e.push(r,...n),n.length=0,r=o):n.push(o),s=t;else{let i=Ss(r,o);i>=0?r=i:0==s&&0==t?(e.push(r),r=o):(n.push(o),s=t)}}return r>=0&&e.push(r,...n),e}(Fs(t))}const Ls=45,Ms=\".\",Gs=65039,Hs=1,Qs=t=>Array.from(t);function js(t,e){return t.P.has(e)||t.Q.has(e)}class Vs extends Array{get is_emoji(){return!0}}let Js,zs,Ks,qs,_s,Zs,Ws,Ys,Xs,$s,ti,ei;function ni(){if(Js)return;let t=es(Xr);const e=()=>ss(t),n=()=>new Set(e()),r=(t,e)=>e.forEach((e=>t.add(e)));Js=new Map(os(t)),zs=n(),Ks=e(),qs=new Set(e().map((t=>Ks[t]))),Ks=new Set(Ks),_s=n(),Zs=n();let s=is(t),i=t();const o=()=>{let t=new Set;return e().forEach((e=>r(t,s[e]))),r(t,e()),t};Ws=as((e=>{let n=as(t).map((t=>t+96));if(n.length){let r=e>=i;return n[0]-=32,n=ds(n),r&&(n=`Restricted[${n}]`),{N:n,P:o(),Q:o(),M:!t(),R:r}}})),Ys=n(),Xs=new Map;let a=e().concat(Qs(Ys)).sort(((t,e)=>t-e));a.forEach(((e,n)=>{let r=t(),s=a[n]=r?a[n-r]:{V:[],M:new Map};s.V.push(e),Ys.has(e)||Xs.set(e,s)}));for(let{V:t,M:e}of new Set(Xs.values())){let n=[];for(let e of t){let t=Ws.filter((t=>js(t,e))),s=n.find((({G:e})=>t.some((t=>e.has(t)))));s||(s={G:new Set,V:[]},n.push(s)),s.V.push(e),r(s.G,t)}let s=n.flatMap((t=>Qs(t.G)));for(let{G:t,V:r}of n){let n=new Set(s.filter((e=>!t.has(e))));for(let t of r)e.set(t,n)}}$s=new Set;let c=new Set;const l=t=>$s.has(t)?c.add(t):$s.add(t);for(let t of Ws){for(let e of t.P)l(e);for(let e of t.Q)l(e)}for(let t of $s)Xs.has(t)||c.has(t)||Xs.set(t,Hs);r($s,Us($s)),ti=function(t){let e=[],n=ss(t);return function t({S:n,B:r},s,i){if(!(4&n&&i===s[s.length-1])){2&n&&(i=s[s.length-1]),1&n&&e.push(s);for(let e of r)for(let n of e.Q)t(e,[...s,n],i)}}(function e(r){let s=t(),i=as((()=>{let r=ss(t).map((t=>n[t]));if(r.length)return e(r)}));return{S:s,B:i,Q:r}}([]),[]),e}(t).map((t=>Vs.from(t))).sort(ps),ei=new Map;for(let t of ti){let e=[ei];for(let n of t){let t=e.map((t=>{let e=t.get(n);return e||(e=new Map,t.set(n,e)),e}));n===Gs?e.push(...t):e=t}for(let n of e)n.V=t}}function ri(t){return(oi(t)?\"\":`${si(ii([t]))} `)+hs(t)}function si(t){return`\"${t}\"‎`}function ii(t,e=1/0,n=hs){let r=[];var s;s=t[0],ni(),Ks.has(s)&&r.push(\"◌\"),t.length>e&&(e>>=1,t=[...t.slice(0,e),8230,...t.slice(-e)]);let i=0,o=t.length;for(let e=0;e<o;e++){let s=t[e];oi(s)&&(r.push(ds(t.slice(i,e))),r.push(n(s)),i=e+1)}return r.push(ds(t.slice(i,o))),r.join(\"\")}function oi(t){return ni(),_s.has(t)}function ai(t,e,n){if(!t)return[];ni();let r=0;return t.split(Ms).map((t=>{let s=fs(t),i={input:s,offset:r};r+=s.length+1;try{let t,r=i.tokens=fi(s,e,n),o=r.length;if(!o)throw new Error(\"empty label\");let a=i.output=r.flat();if(function(t){for(let e=t.lastIndexOf(95);e>0;)if(95!==t[--e])throw new Error(\"underscore allowed only at start\")}(a),!(i.emoji=o>1||r[0].is_emoji)&&a.every((t=>t<128)))!function(t){if(t.length>=4&&t[2]==Ls&&t[3]==Ls)throw new Error(`invalid label extension: \"${ds(t.slice(0,4))}\"`)}(a),t=\"ASCII\";else{let e=r.flatMap((t=>t.is_emoji?[]:t));if(e.length){if(Ks.has(a[0]))throw hi(\"leading combining mark\");for(let t=1;t<o;t++){let e=r[t];if(!e.is_emoji&&Ks.has(e[0]))throw hi(`emoji + combining mark: \"${ds(r[t-1])} + ${ii([e[0]])}\"`)}!function(t){let e=t[0],n=$r.get(e);if(n)throw hi(`leading ${n}`);let r=t.length,s=-1;for(let i=1;i<r;i++){e=t[i];let r=$r.get(e);if(r){if(s==i)throw hi(`${n} + ${r}`);s=i+1,n=r}}if(s==r)throw hi(`trailing ${n}`)}(a);let n=Qs(new Set(e)),[s]=function(t){let e=Ws;for(let n of t){let t=e.filter((t=>js(t,n)));if(!t.length)throw Ws.some((t=>js(t,n)))?ui(e[0],n):li(n);if(e=t,1==t.length)break}return e}(n);!function(t,e){for(let n of e)if(!js(t,n))throw ui(t,n);if(t.M){let t=Us(e);for(let e=1,n=t.length;e<n;e++)if(qs.has(t[e])){let r=e+1;for(let s;r<n&&qs.has(s=t[r]);r++)for(let n=e;n<r;n++)if(t[n]==s)throw new Error(`duplicate non-spacing marks: ${ri(s)}`);if(r-e>ts)throw new Error(`excessive non-spacing marks: ${si(ii(t.slice(e-1,r)))} (${r-e}/${ts})`);e=r}}}(s,e),function(t,e){let n,r=[];for(let t of e){let e=Xs.get(t);if(e===Hs)return;if(e){let r=e.M.get(t);if(n=n?n.filter((t=>r.has(t))):Qs(r),!n.length)return}else r.push(t)}if(n)for(let e of n)if(r.every((t=>js(e,t))))throw new Error(`whole-script confusable: ${t.N}/${e.N}`)}(s,n),t=s.N}else t=\"Emoji\"}i.type=t}catch(t){i.error=t}return i}))}function ci(t){return t.map((({input:e,error:n,output:r})=>{if(n){let r=n.message;throw new Error(1==t.length?r:`Invalid label ${si(ii(e,63))}: ${r}`)}return ds(r)})).join(Ms)}function li(t){return new Error(`disallowed character: ${ri(t)}`)}function ui(t,e){let n=ri(e),r=Ws.find((t=>t.P.has(e)));return r&&(n=`${r.N} ${n}`),new Error(`illegal mixture: ${t.N} + ${n}`)}function hi(t){return new Error(`illegal placement: ${t}`)}function fi(t,e,n){let r=[],s=[];for(t=t.slice().reverse();t.length;){let i=pi(t);if(i)s.length&&(r.push(e(s)),s=[]),r.push(n(i));else{let e=t.pop();if($s.has(e))s.push(e);else{let t=Js.get(e);if(t)s.push(...t);else if(!zs.has(e))throw li(e)}}}return s.length&&r.push(e(s)),r}function di(t){return t.filter((t=>t!=Gs))}function pi(t,e){let n,r=ei,s=t.length;for(;s&&(r=r.get(t[--s]),r);){let{V:i}=r;i&&(n=i,e&&e.push(...t.slice(s).reverse()),t.length=s)}return n}const gi=new Uint8Array(32);function mi(t){return f(0!==t.length,\"invalid ENS name; empty component\",\"comp\",t),t}function yi(t){const e=Ae(function(t){try{if(0===t.length)throw new Error(\"empty label\");return function(t){return ci(ai(t,Ds,di))}(t)}catch(e){f(!1,`invalid ENS name (${e.message})`,\"name\",t)}}(t)),n=[];if(0===t.length)return n;let r=0;for(let t=0;t<e.length;t++){46===e[t]&&(n.push(mi(e.slice(r,t))),r=t+1)}return f(r<e.length,\"invalid ENS name; empty component\",\"name\",t),n.push(mi(e.slice(r))),n}function wi(t){f(\"string\"==typeof t,\"invalid ENS name; not a string\",\"name\",t),f(t.length,\"invalid ENS name (empty label)\",\"name\",t);let e=gi;const n=yi(t);for(;n.length;)e=jt(k([e,jt(n.pop())]));return E(e)}function bi(t,e){const n=null!=e?e:63;return f(n<=255,\"DNS encoded label cannot exceed 255\",\"length\",n),E(k(yi(t).map((e=>{f(e.length<=n,`label ${JSON.stringify(t)} exceeds ${n} bytes`,\"name\",t);const r=new Uint8Array(e.length+1);return r.set(e,1),r[0]=r.length-1,r}))))+\"00\"}gi.fill(0);class Ai extends At{constructor(t,e,n,r){super(),this.blockLen=t,this.outputLen=e,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(t),this.view=yt(this.buffer)}update(t){st(this);const{view:e,buffer:n,blockLen:r}=this,s=(t=bt(t)).length;for(let i=0;i<s;){const o=Math.min(r-this.pos,s-i);if(o!==r)n.set(t.subarray(i,i+o),this.pos),this.pos+=o,i+=o,this.pos===r&&(this.process(e,0),this.pos=0);else{const e=yt(t);for(;r<=s-i;i+=r)this.process(e,i)}}return this.length+=t.length,this.roundClean(),this}digestInto(t){st(this),it(t,this),this.finished=!0;const{buffer:e,view:n,blockLen:r,isLE:s}=this;let{pos:i}=this;e[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>r-i&&(this.process(n,0),i=0);for(let t=i;t<r;t++)e[t]=0;!function(t,e,n,r){if(\"function\"==typeof t.setBigUint64)return t.setBigUint64(e,n,r);const s=BigInt(32),i=BigInt(4294967295),o=Number(n>>s&i),a=Number(n&i),c=r?4:0,l=r?0:4;t.setUint32(e+c,o,r),t.setUint32(e+l,a,r)}(n,r-8,BigInt(8*this.length),s),this.process(n,0);const o=yt(t),a=this.outputLen;if(a%4)throw new Error(\"_sha2: outputLen should be aligned to 32bit\");const c=a/4,l=this.get();if(c>l.length)throw new Error(\"_sha2: outputLen bigger than state\");for(let t=0;t<c;t++)o.setUint32(4*t,l[t],s)}digest(){const{buffer:t,outputLen:e}=this;this.digestInto(t);const n=t.slice(0,e);return this.destroy(),n}_cloneInto(t){t||(t=new this.constructor),t.set(...this.get());const{blockLen:e,buffer:n,length:r,finished:s,destroyed:i,pos:o}=this;return t.length=r,t.pos=o,t.finished=s,t.destroyed=i,r%e&&t.buffer.set(n),t}}const vi=(t,e,n)=>t&e^~t&n,Ei=(t,e,n)=>t&e^t&n^e&n,ki=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Pi=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),xi=new Uint32Array(64);class Ni extends Ai{constructor(){super(64,32,8,!1),this.A=0|Pi[0],this.B=0|Pi[1],this.C=0|Pi[2],this.D=0|Pi[3],this.E=0|Pi[4],this.F=0|Pi[5],this.G=0|Pi[6],this.H=0|Pi[7]}get(){const{A:t,B:e,C:n,D:r,E:s,F:i,G:o,H:a}=this;return[t,e,n,r,s,i,o,a]}set(t,e,n,r,s,i,o,a){this.A=0|t,this.B=0|e,this.C=0|n,this.D=0|r,this.E=0|s,this.F=0|i,this.G=0|o,this.H=0|a}process(t,e){for(let n=0;n<16;n++,e+=4)xi[n]=t.getUint32(e,!1);for(let t=16;t<64;t++){const e=xi[t-15],n=xi[t-2],r=wt(e,7)^wt(e,18)^e>>>3,s=wt(n,17)^wt(n,19)^n>>>10;xi[t]=s+xi[t-7]+r+xi[t-16]|0}let{A:n,B:r,C:s,D:i,E:o,F:a,G:c,H:l}=this;for(let t=0;t<64;t++){const e=l+(wt(o,6)^wt(o,11)^wt(o,25))+vi(o,a,c)+ki[t]+xi[t]|0,u=(wt(n,2)^wt(n,13)^wt(n,22))+Ei(n,r,s)|0;l=c,c=a,a=o,o=i+e|0,i=s,s=r,r=n,n=e+u|0}n=n+this.A|0,r=r+this.B|0,s=s+this.C|0,i=i+this.D|0,o=o+this.E|0,a=a+this.F|0,c=c+this.G|0,l=l+this.H|0,this.set(n,r,s,i,o,a,c,l)}roundClean(){xi.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Bi=vt((()=>new Ni)),[Ii,Ci]=pt.split([\"0x428a2f98d728ae22\",\"0x7137449123ef65cd\",\"0xb5c0fbcfec4d3b2f\",\"0xe9b5dba58189dbbc\",\"0x3956c25bf348b538\",\"0x59f111f1b605d019\",\"0x923f82a4af194f9b\",\"0xab1c5ed5da6d8118\",\"0xd807aa98a3030242\",\"0x12835b0145706fbe\",\"0x243185be4ee4b28c\",\"0x550c7dc3d5ffb4e2\",\"0x72be5d74f27b896f\",\"0x80deb1fe3b1696b1\",\"0x9bdc06a725c71235\",\"0xc19bf174cf692694\",\"0xe49b69c19ef14ad2\",\"0xefbe4786384f25e3\",\"0x0fc19dc68b8cd5b5\",\"0x240ca1cc77ac9c65\",\"0x2de92c6f592b0275\",\"0x4a7484aa6ea6e483\",\"0x5cb0a9dcbd41fbd4\",\"0x76f988da831153b5\",\"0x983e5152ee66dfab\",\"0xa831c66d2db43210\",\"0xb00327c898fb213f\",\"0xbf597fc7beef0ee4\",\"0xc6e00bf33da88fc2\",\"0xd5a79147930aa725\",\"0x06ca6351e003826f\",\"0x142929670a0e6e70\",\"0x27b70a8546d22ffc\",\"0x2e1b21385c26c926\",\"0x4d2c6dfc5ac42aed\",\"0x53380d139d95b3df\",\"0x650a73548baf63de\",\"0x766a0abb3c77b2a8\",\"0x81c2c92e47edaee6\",\"0x92722c851482353b\",\"0xa2bfe8a14cf10364\",\"0xa81a664bbc423001\",\"0xc24b8b70d0f89791\",\"0xc76c51a30654be30\",\"0xd192e819d6ef5218\",\"0xd69906245565a910\",\"0xf40e35855771202a\",\"0x106aa07032bbd1b8\",\"0x19a4c116b8d2d0c8\",\"0x1e376c085141ab53\",\"0x2748774cdf8eeb99\",\"0x34b0bcb5e19b48a8\",\"0x391c0cb3c5c95a63\",\"0x4ed8aa4ae3418acb\",\"0x5b9cca4f7763e373\",\"0x682e6ff3d6b2b8a3\",\"0x748f82ee5defb2fc\",\"0x78a5636f43172f60\",\"0x84c87814a1f0ab72\",\"0x8cc702081a6439ec\",\"0x90befffa23631e28\",\"0xa4506cebde82bde9\",\"0xbef9a3f7b2c67915\",\"0xc67178f2e372532b\",\"0xca273eceea26619c\",\"0xd186b8c721c0c207\",\"0xeada7dd6cde0eb1e\",\"0xf57d4f7fee6ed178\",\"0x06f067aa72176fba\",\"0x0a637dc5a2c898a6\",\"0x113f9804bef90dae\",\"0x1b710b35131c471b\",\"0x28db77f523047d84\",\"0x32caab7b40c72493\",\"0x3c9ebe0a15c9bebc\",\"0x431d67c49c100d4c\",\"0x4cc5d4becb3e42b6\",\"0x597f299cfc657e2a\",\"0x5fcb6fab3ad6faec\",\"0x6c44198c4a475817\"].map((t=>BigInt(t)))),Oi=new Uint32Array(80),Ri=new Uint32Array(80);class Ti extends Ai{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:t,Al:e,Bh:n,Bl:r,Ch:s,Cl:i,Dh:o,Dl:a,Eh:c,El:l,Fh:u,Fl:h,Gh:f,Gl:d,Hh:p,Hl:g}=this;return[t,e,n,r,s,i,o,a,c,l,u,h,f,d,p,g]}set(t,e,n,r,s,i,o,a,c,l,u,h,f,d,p,g){this.Ah=0|t,this.Al=0|e,this.Bh=0|n,this.Bl=0|r,this.Ch=0|s,this.Cl=0|i,this.Dh=0|o,this.Dl=0|a,this.Eh=0|c,this.El=0|l,this.Fh=0|u,this.Fl=0|h,this.Gh=0|f,this.Gl=0|d,this.Hh=0|p,this.Hl=0|g}process(t,e){for(let n=0;n<16;n++,e+=4)Oi[n]=t.getUint32(e),Ri[n]=t.getUint32(e+=4);for(let t=16;t<80;t++){const e=0|Oi[t-15],n=0|Ri[t-15],r=pt.rotrSH(e,n,1)^pt.rotrSH(e,n,8)^pt.shrSH(e,n,7),s=pt.rotrSL(e,n,1)^pt.rotrSL(e,n,8)^pt.shrSL(e,n,7),i=0|Oi[t-2],o=0|Ri[t-2],a=pt.rotrSH(i,o,19)^pt.rotrBH(i,o,61)^pt.shrSH(i,o,6),c=pt.rotrSL(i,o,19)^pt.rotrBL(i,o,61)^pt.shrSL(i,o,6),l=pt.add4L(s,c,Ri[t-7],Ri[t-16]),u=pt.add4H(l,r,a,Oi[t-7],Oi[t-16]);Oi[t]=0|u,Ri[t]=0|l}let{Ah:n,Al:r,Bh:s,Bl:i,Ch:o,Cl:a,Dh:c,Dl:l,Eh:u,El:h,Fh:f,Fl:d,Gh:p,Gl:g,Hh:m,Hl:y}=this;for(let t=0;t<80;t++){const e=pt.rotrSH(u,h,14)^pt.rotrSH(u,h,18)^pt.rotrBH(u,h,41),w=pt.rotrSL(u,h,14)^pt.rotrSL(u,h,18)^pt.rotrBL(u,h,41),b=u&f^~u&p,A=h&d^~h&g,v=pt.add5L(y,w,A,Ci[t],Ri[t]),E=pt.add5H(v,m,e,b,Ii[t],Oi[t]),k=0|v,P=pt.rotrSH(n,r,28)^pt.rotrBH(n,r,34)^pt.rotrBH(n,r,39),x=pt.rotrSL(n,r,28)^pt.rotrBL(n,r,34)^pt.rotrBL(n,r,39),N=n&s^n&o^s&o,B=r&i^r&a^i&a;m=0|p,y=0|g,p=0|f,g=0|d,f=0|u,d=0|h,({h:u,l:h}=pt.add(0|c,0|l,0|E,0|k)),c=0|o,l=0|a,o=0|s,a=0|i,s=0|n,i=0|r;const I=pt.add3L(k,x,B);n=pt.add3H(I,E,P,N),r=0|I}({h:n,l:r}=pt.add(0|this.Ah,0|this.Al,0|n,0|r)),({h:s,l:i}=pt.add(0|this.Bh,0|this.Bl,0|s,0|i)),({h:o,l:a}=pt.add(0|this.Ch,0|this.Cl,0|o,0|a)),({h:c,l}=pt.add(0|this.Dh,0|this.Dl,0|c,0|l)),({h:u,l:h}=pt.add(0|this.Eh,0|this.El,0|u,0|h)),({h:f,l:d}=pt.add(0|this.Fh,0|this.Fl,0|f,0|d)),({h:p,l:g}=pt.add(0|this.Gh,0|this.Gl,0|p,0|g)),({h:m,l:y}=pt.add(0|this.Hh,0|this.Hl,0|m,0|y)),this.set(n,r,s,i,o,a,c,l,u,h,f,d,p,g,m,y)}roundClean(){Oi.fill(0),Ri.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const Si=vt((()=>new Ti));const Fi=function(){if(\"undefined\"!=typeof self)return self;if(\"undefined\"!=typeof window)return window;if(\"undefined\"!=typeof global)return global;throw new Error(\"unable to locate global object\")}();Fi.crypto||Fi.msCrypto;function Ui(t){switch(t){case\"sha256\":return Bi.create();case\"sha512\":return Si.create()}f(!1,\"invalid hashing algorithm name\",\"algorithm\",t)}const Di=function(t){return Ui(\"sha256\").update(t).digest()},Li=function(t){return Ui(\"sha512\").update(t).digest()};let Mi=Di,Gi=Li,Hi=!1,Qi=!1;function ji(t){const e=y(t,\"data\");return E(Mi(e))}function Vi(t){const e=y(t,\"data\");return E(Gi(e))}ji._=Di,ji.lock=function(){Hi=!0},ji.register=function(t){if(Hi)throw new Error(\"sha256 is locked\");Mi=t},Object.freeze(ji),Vi._=Li,Vi.lock=function(){Qi=!0},Vi.register=function(t){if(Qi)throw new Error(\"sha512 is locked\");Gi=t},Object.freeze(ji);const Ji=\"******************************************000000000000000000000000\",zi=BigInt(0),Ki=BigInt(1),qi=BigInt(2),_i=BigInt(27),Zi=BigInt(28),Wi=BigInt(35),Yi={};function Xi(t){return B(G(t),32)}class $i{#$;#tt;#et;#nt;get r(){return this.#$}set r(t){f(32===P(t),\"invalid r\",\"value\",t),this.#$=E(t)}get s(){return this.#tt}set s(t){f(32===P(t),\"invalid s\",\"value\",t);const e=E(t);f(parseInt(e.substring(0,3))<8,\"non-canonical s\",\"value\",e),this.#tt=e}get v(){return this.#et}set v(t){const e=L(t,\"value\");f(27===e||28===e,\"invalid v\",\"v\",t),this.#et=e}get networkV(){return this.#nt}get legacyChainId(){const t=this.networkV;return null==t?null:$i.getChainId(t)}get yParity(){return 27===this.v?0:1}get yParityAndS(){const t=y(this.s);return this.yParity&&(t[0]|=128),E(t)}get compactSerialized(){return k([this.r,this.yParityAndS])}get serialized(){return k([this.r,this.s,this.yParity?\"0x1c\":\"0x1b\"])}constructor(t,e,n,r){g(t,Yi,\"Signature\"),this.#$=e,this.#tt=n,this.#et=r,this.#nt=null}[Symbol.for(\"nodejs.util.inspect.custom\")](){return`Signature { r: \"${this.r}\", s: \"${this.s}\", yParity: ${this.yParity}, networkV: ${this.networkV} }`}clone(){const t=new $i(Yi,this.r,this.s,this.v);return this.networkV&&(t.#nt=this.networkV),t}toJSON(){const t=this.networkV;return{_type:\"signature\",networkV:null!=t?t.toString():null,r:this.r,s:this.s,v:this.v}}static getChainId(t){const e=S(t,\"v\");return e==_i||e==Zi?zi:(f(e>=Wi,\"invalid EIP-155 v\",\"v\",t),(e-Wi)/qi)}static getChainIdV(t,e){return S(t)*qi+BigInt(35+e-27)}static getNormalizedV(t){const e=S(t);return e===zi||e===_i?27:e===Ki||e===Zi?28:(f(e>=Wi,\"invalid v\",\"v\",t),e&Ki?27:28)}static from(t){function e(e,n){f(e,n,\"signature\",t)}if(null==t)return new $i(Yi,Ji,Ji,27);if(\"string\"==typeof t){const n=y(t,\"signature\");if(64===n.length){const t=E(n.slice(0,32)),e=n.slice(32,64),r=128&e[0]?28:27;return e[0]&=127,new $i(Yi,t,E(e),r)}if(65===n.length){const t=E(n.slice(0,32)),r=n.slice(32,64);e(0==(128&r[0]),\"non-canonical s\");const s=$i.getNormalizedV(n[64]);return new $i(Yi,t,E(r),s)}e(!1,\"invalid raw signature length\")}if(t instanceof $i)return t.clone();const n=t.r;e(null!=n,\"missing r\");const r=Xi(n),s=function(t,n){if(null!=t)return Xi(t);if(null!=n){e(b(n,32),\"invalid yParityAndS\");const t=y(n);return t[0]&=127,E(t)}e(!1,\"missing s\")}(t.s,t.yParityAndS);e(0==(128&y(s)[0]),\"non-canonical s\");const{networkV:i,v:o}=function(t,n,r){if(null!=t){const e=S(t);return{networkV:e>=Wi?e:void 0,v:$i.getNormalizedV(e)}}if(null!=n)return e(b(n,32),\"invalid yParityAndS\"),{v:128&y(n)[0]?28:27};if(null!=r){switch(L(r,\"sig.yParity\")){case 0:return{v:27};case 1:return{v:28}}e(!1,\"invalid yParity\")}e(!1,\"missing v\")}(t.v,t.yParityAndS,t.yParity),a=new $i(Yi,r,s,o);return i&&(a.#nt=i),e(null==t.yParity||L(t.yParity,\"sig.yParity\")===a.yParity,\"yParity mismatch\"),e(null==t.yParityAndS||t.yParityAndS===a.yParityAndS,\"yParityAndS mismatch\"),a}}BigInt(0);const to=BigInt(1),eo=BigInt(2),no=t=>t instanceof Uint8Array,ro=Array.from({length:256},((t,e)=>e.toString(16).padStart(2,\"0\")));function so(t){if(!no(t))throw new Error(\"Uint8Array expected\");let e=\"\";for(let n=0;n<t.length;n++)e+=ro[t[n]];return e}function io(t){if(\"string\"!=typeof t)throw new Error(\"hex string expected, got \"+typeof t);return BigInt(\"\"===t?\"0\":`0x${t}`)}function oo(t){if(\"string\"!=typeof t)throw new Error(\"hex string expected, got \"+typeof t);const e=t.length;if(e%2)throw new Error(\"padded hex string expected, got unpadded hex of length \"+e);const n=new Uint8Array(e/2);for(let e=0;e<n.length;e++){const r=2*e,s=t.slice(r,r+2),i=Number.parseInt(s,16);if(Number.isNaN(i)||i<0)throw new Error(\"Invalid byte sequence\");n[e]=i}return n}function ao(t){return io(so(t))}function co(t){if(!no(t))throw new Error(\"Uint8Array expected\");return io(so(Uint8Array.from(t).reverse()))}function lo(t,e){return oo(t.toString(16).padStart(2*e,\"0\"))}function uo(t,e){return lo(t,e).reverse()}function ho(t,e,n){let r;if(\"string\"==typeof e)try{r=oo(e)}catch(n){throw new Error(`${t} must be valid hex string, got \"${e}\". Cause: ${n}`)}else{if(!no(e))throw new Error(`${t} must be hex string or Uint8Array`);r=Uint8Array.from(e)}const s=r.length;if(\"number\"==typeof n&&s!==n)throw new Error(`${t} expected ${n} bytes, got ${s}`);return r}function fo(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let n=0;return t.forEach((t=>{if(!no(t))throw new Error(\"Uint8Array expected\");e.set(t,n),n+=t.length})),e}const po=t=>(eo<<BigInt(t-1))-to,go=t=>new Uint8Array(t),mo=t=>Uint8Array.from(t);function yo(t,e,n){if(\"number\"!=typeof t||t<2)throw new Error(\"hashLen must be a number\");if(\"number\"!=typeof e||e<2)throw new Error(\"qByteLen must be a number\");if(\"function\"!=typeof n)throw new Error(\"hmacFn must be a function\");let r=go(t),s=go(t),i=0;const o=()=>{r.fill(1),s.fill(0),i=0},a=(...t)=>n(s,r,...t),c=(t=go())=>{s=a(mo([0]),t),r=a(),0!==t.length&&(s=a(mo([1]),t),r=a())},l=()=>{if(i++>=1e3)throw new Error(\"drbg: tried 1000 values\");let t=0;const n=[];for(;t<e;){r=a();const e=r.slice();n.push(e),t+=r.length}return fo(...n)};return(t,e)=>{let n;for(o(),c(t);!(n=e(l()));)c();return o(),n}}const wo={bigint:t=>\"bigint\"==typeof t,function:t=>\"function\"==typeof t,boolean:t=>\"boolean\"==typeof t,string:t=>\"string\"==typeof t,stringOrUint8Array:t=>\"string\"==typeof t||t instanceof Uint8Array,isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>\"function\"==typeof t&&Number.isSafeInteger(t.outputLen)};function bo(t,e,n={}){const r=(e,n,r)=>{const s=wo[n];if(\"function\"!=typeof s)throw new Error(`Invalid validator \"${n}\", expected function`);const i=t[e];if(!(r&&void 0===i||s(i,t)))throw new Error(`Invalid param ${String(e)}=${i} (${typeof i}), expected ${n}`)};for(const[t,n]of Object.entries(e))r(t,n,!1);for(const[t,e]of Object.entries(n))r(t,e,!0);return t}const Ao=BigInt(0),vo=BigInt(1),Eo=BigInt(2),ko=BigInt(3),Po=BigInt(4),xo=BigInt(5),No=BigInt(8);BigInt(9),BigInt(16);function Bo(t,e){const n=t%e;return n>=Ao?n:e+n}function Io(t,e,n){if(n<=Ao||e<Ao)throw new Error(\"Expected power/modulo > 0\");if(n===vo)return Ao;let r=vo;for(;e>Ao;)e&vo&&(r=r*t%n),t=t*t%n,e>>=vo;return r}function Co(t,e,n){let r=t;for(;e-- >Ao;)r*=r,r%=n;return r}function Oo(t,e){if(t===Ao||e<=Ao)throw new Error(`invert: expected positive integers, got n=${t} mod=${e}`);let n=Bo(t,e),r=e,s=Ao,i=vo,o=vo,a=Ao;for(;n!==Ao;){const t=r/n,e=r%n,c=s-o*t,l=i-a*t;r=n,n=e,s=o,i=a,o=c,a=l}if(r!==vo)throw new Error(\"invert: does not exist\");return Bo(s,e)}function Ro(t){if(t%Po===ko){const e=(t+vo)/Po;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error(\"Cannot find square root\");return r}}if(t%No===xo){const e=(t-xo)/No;return function(t,n){const r=t.mul(n,Eo),s=t.pow(r,e),i=t.mul(n,s),o=t.mul(t.mul(i,Eo),s),a=t.mul(i,t.sub(o,t.ONE));if(!t.eql(t.sqr(a),n))throw new Error(\"Cannot find square root\");return a}}return function(t){const e=(t-vo)/Eo;let n,r,s;for(n=t-vo,r=0;n%Eo===Ao;n/=Eo,r++);for(s=Eo;s<t&&Io(s,e,t)!==t-vo;s++);if(1===r){const e=(t+vo)/Po;return function(t,n){const r=t.pow(n,e);if(!t.eql(t.sqr(r),n))throw new Error(\"Cannot find square root\");return r}}const i=(n+vo)/Eo;return function(t,o){if(t.pow(o,e)===t.neg(t.ONE))throw new Error(\"Cannot find square root\");let a=r,c=t.pow(t.mul(t.ONE,s),n),l=t.pow(o,i),u=t.pow(o,n);for(;!t.eql(u,t.ONE);){if(t.eql(u,t.ZERO))return t.ZERO;let e=1;for(let n=t.sqr(u);e<a&&!t.eql(n,t.ONE);e++)n=t.sqr(n);const n=t.pow(c,vo<<BigInt(a-e-1));c=t.sqr(n),l=t.mul(l,n),u=t.mul(u,c),a=e}return l}}(t)}const To=[\"create\",\"isValid\",\"is0\",\"neg\",\"inv\",\"sqrt\",\"sqr\",\"eql\",\"add\",\"sub\",\"mul\",\"pow\",\"div\",\"addN\",\"subN\",\"mulN\",\"sqrN\"];function So(t,e){const n=void 0!==e?e:t.toString(2).length;return{nBitLength:n,nByteLength:Math.ceil(n/8)}}function Fo(t){if(\"bigint\"!=typeof t)throw new Error(\"field order must be bigint\");const e=t.toString(2).length;return Math.ceil(e/8)}function Uo(t){const e=Fo(t);return e+Math.ceil(e/2)}class Do extends At{constructor(t,e){super(),this.finished=!1,this.destroyed=!1,rt(t);const n=bt(e);if(this.iHash=t.create(),\"function\"!=typeof this.iHash.update)throw new Error(\"Expected instance of class which extends utils.Hash\");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const r=this.blockLen,s=new Uint8Array(r);s.set(n.length>r?t.create().update(n).digest():n);for(let t=0;t<s.length;t++)s[t]^=54;this.iHash.update(s),this.oHash=t.create();for(let t=0;t<s.length;t++)s[t]^=106;this.oHash.update(s),s.fill(0)}update(t){return st(this),this.iHash.update(t),this}digestInto(t){st(this),nt(t,this.outputLen),this.finished=!0,this.iHash.digestInto(t),this.oHash.update(t),this.oHash.digestInto(t),this.destroy()}digest(){const t=new Uint8Array(this.oHash.outputLen);return this.digestInto(t),t}_cloneInto(t){t||(t=Object.create(Object.getPrototypeOf(this),{}));const{oHash:e,iHash:n,finished:r,destroyed:s,blockLen:i,outputLen:o}=this;return t.finished=r,t.destroyed=s,t.blockLen=i,t.outputLen=o,t.oHash=e._cloneInto(t.oHash),t.iHash=n._cloneInto(t.iHash),t}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Lo=(t,e,n)=>new Do(t,e).update(n).digest();Lo.create=(t,e)=>new Do(t,e);const Mo=BigInt(0),Go=BigInt(1);function Ho(t){return bo(t.Fp,To.reduce(((t,e)=>(t[e]=\"function\",t)),{ORDER:\"bigint\",MASK:\"bigint\",BYTES:\"isSafeInteger\",BITS:\"isSafeInteger\"})),bo(t,{n:\"bigint\",h:\"bigint\",Gx:\"field\",Gy:\"field\"},{nBitLength:\"isSafeInteger\",nByteLength:\"isSafeInteger\"}),Object.freeze({...So(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const{bytesToNumberBE:Qo,hexToBytes:jo}=n,Vo={Err:class extends Error{constructor(t=\"\"){super(t)}},_parseInt(t){const{Err:e}=Vo;if(t.length<2||2!==t[0])throw new e(\"Invalid signature integer tag\");const n=t[1],r=t.subarray(2,n+2);if(!n||r.length!==n)throw new e(\"Invalid signature integer: wrong length\");if(128&r[0])throw new e(\"Invalid signature integer: negative\");if(0===r[0]&&!(128&r[1]))throw new e(\"Invalid signature integer: unnecessary leading zero\");return{d:Qo(r),l:t.subarray(n+2)}},toSig(t){const{Err:e}=Vo,n=\"string\"==typeof t?jo(t):t;if(!(n instanceof Uint8Array))throw new Error(\"ui8a expected\");let r=n.length;if(r<2||48!=n[0])throw new e(\"Invalid signature tag\");if(n[1]!==r-2)throw new e(\"Invalid signature: incorrect length\");const{d:s,l:i}=Vo._parseInt(n.subarray(2)),{d:o,l:a}=Vo._parseInt(i);if(a.length)throw new e(\"Invalid signature: left bytes after parsing\");return{r:s,s:o}},hexFromSig(t){const e=t=>8&Number.parseInt(t[0],16)?\"00\"+t:t,n=t=>{const e=t.toString(16);return 1&e.length?`0${e}`:e},r=e(n(t.s)),s=e(n(t.r)),i=r.length/2,o=s.length/2,a=n(i),c=n(o);return`30${n(o+i+4)}02${c}${s}02${a}${r}`}},Jo=BigInt(0),zo=BigInt(1),Ko=(BigInt(2),BigInt(3));BigInt(4);function qo(t){const e=function(t){const e=Ho(t);bo(e,{a:\"field\",b:\"field\"},{allowedPrivateKeyLengths:\"array\",wrapPrivateKey:\"boolean\",isTorsionFree:\"function\",clearCofactor:\"function\",allowInfinityPoint:\"boolean\",fromBytes:\"function\",toBytes:\"function\"});const{endo:n,Fp:r,a:s}=e;if(n){if(!r.eql(s,r.ZERO))throw new Error(\"Endomorphism can only be defined for Koblitz curves that have a=0\");if(\"object\"!=typeof n||\"bigint\"!=typeof n.beta||\"function\"!=typeof n.splitScalar)throw new Error(\"Expected endomorphism with beta: bigint and splitScalar: function\")}return Object.freeze({...e})}(t),{Fp:n}=e,r=e.toBytes||((t,e,r)=>{const s=e.toAffine();return fo(Uint8Array.from([4]),n.toBytes(s.x),n.toBytes(s.y))}),s=e.fromBytes||(t=>{const e=t.subarray(1);return{x:n.fromBytes(e.subarray(0,n.BYTES)),y:n.fromBytes(e.subarray(n.BYTES,2*n.BYTES))}});function i(t){const{a:r,b:s}=e,i=n.sqr(t),o=n.mul(i,t);return n.add(n.add(o,n.mul(t,r)),s)}if(!n.eql(n.sqr(e.Gy),i(e.Gx)))throw new Error(\"bad generator point: equation left != right\");function o(t){return\"bigint\"==typeof t&&Jo<t&&t<e.n}function a(t){if(!o(t))throw new Error(\"Expected valid bigint: 0 < bigint < curve.n\")}function c(t){const{allowedPrivateKeyLengths:n,nByteLength:r,wrapPrivateKey:s,n:i}=e;if(n&&\"bigint\"!=typeof t){if(t instanceof Uint8Array&&(t=so(t)),\"string\"!=typeof t||!n.includes(t.length))throw new Error(\"Invalid key\");t=t.padStart(2*r,\"0\")}let o;try{o=\"bigint\"==typeof t?t:ao(ho(\"private key\",t,r))}catch(e){throw new Error(`private key must be ${r} bytes, hex or bigint, not ${typeof t}`)}return s&&(o=Bo(o,i)),a(o),o}const l=new Map;function u(t){if(!(t instanceof h))throw new Error(\"ProjectivePoint expected\")}class h{constructor(t,e,r){if(this.px=t,this.py=e,this.pz=r,null==t||!n.isValid(t))throw new Error(\"x required\");if(null==e||!n.isValid(e))throw new Error(\"y required\");if(null==r||!n.isValid(r))throw new Error(\"z required\")}static fromAffine(t){const{x:e,y:r}=t||{};if(!t||!n.isValid(e)||!n.isValid(r))throw new Error(\"invalid affine point\");if(t instanceof h)throw new Error(\"projective point not allowed\");const s=t=>n.eql(t,n.ZERO);return s(e)&&s(r)?h.ZERO:new h(e,r,n.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(t){const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}static fromHex(t){const e=h.fromAffine(s(ho(\"pointHex\",t)));return e.assertValidity(),e}static fromPrivateKey(t){return h.BASE.multiply(c(t))}_setWindowSize(t){this._WINDOW_SIZE=t,l.delete(this)}assertValidity(){if(this.is0()){if(e.allowInfinityPoint&&!n.is0(this.py))return;throw new Error(\"bad point: ZERO\")}const{x:t,y:r}=this.toAffine();if(!n.isValid(t)||!n.isValid(r))throw new Error(\"bad point: x or y not FE\");const s=n.sqr(r),o=i(t);if(!n.eql(s,o))throw new Error(\"bad point: equation left != right\");if(!this.isTorsionFree())throw new Error(\"bad point: not in prime-order subgroup\")}hasEvenY(){const{y:t}=this.toAffine();if(n.isOdd)return!n.isOdd(t);throw new Error(\"Field doesn't support isOdd\")}equals(t){u(t);const{px:e,py:r,pz:s}=this,{px:i,py:o,pz:a}=t,c=n.eql(n.mul(e,a),n.mul(i,s)),l=n.eql(n.mul(r,a),n.mul(o,s));return c&&l}negate(){return new h(this.px,n.neg(this.py),this.pz)}double(){const{a:t,b:r}=e,s=n.mul(r,Ko),{px:i,py:o,pz:a}=this;let c=n.ZERO,l=n.ZERO,u=n.ZERO,f=n.mul(i,i),d=n.mul(o,o),p=n.mul(a,a),g=n.mul(i,o);return g=n.add(g,g),u=n.mul(i,a),u=n.add(u,u),c=n.mul(t,u),l=n.mul(s,p),l=n.add(c,l),c=n.sub(d,l),l=n.add(d,l),l=n.mul(c,l),c=n.mul(g,c),u=n.mul(s,u),p=n.mul(t,p),g=n.sub(f,p),g=n.mul(t,g),g=n.add(g,u),u=n.add(f,f),f=n.add(u,f),f=n.add(f,p),f=n.mul(f,g),l=n.add(l,f),p=n.mul(o,a),p=n.add(p,p),f=n.mul(p,g),c=n.sub(c,f),u=n.mul(p,d),u=n.add(u,u),u=n.add(u,u),new h(c,l,u)}add(t){u(t);const{px:r,py:s,pz:i}=this,{px:o,py:a,pz:c}=t;let l=n.ZERO,f=n.ZERO,d=n.ZERO;const p=e.a,g=n.mul(e.b,Ko);let m=n.mul(r,o),y=n.mul(s,a),w=n.mul(i,c),b=n.add(r,s),A=n.add(o,a);b=n.mul(b,A),A=n.add(m,y),b=n.sub(b,A),A=n.add(r,i);let v=n.add(o,c);return A=n.mul(A,v),v=n.add(m,w),A=n.sub(A,v),v=n.add(s,i),l=n.add(a,c),v=n.mul(v,l),l=n.add(y,w),v=n.sub(v,l),d=n.mul(p,A),l=n.mul(g,w),d=n.add(l,d),l=n.sub(y,d),d=n.add(y,d),f=n.mul(l,d),y=n.add(m,m),y=n.add(y,m),w=n.mul(p,w),A=n.mul(g,A),y=n.add(y,w),w=n.sub(m,w),w=n.mul(p,w),A=n.add(A,w),m=n.mul(y,A),f=n.add(f,m),m=n.mul(v,A),l=n.mul(b,l),l=n.sub(l,m),m=n.mul(b,y),d=n.mul(v,d),d=n.add(d,m),new h(l,f,d)}subtract(t){return this.add(t.negate())}is0(){return this.equals(h.ZERO)}wNAF(t){return d.wNAFCached(this,l,t,(t=>{const e=n.invertBatch(t.map((t=>t.pz)));return t.map(((t,n)=>t.toAffine(e[n]))).map(h.fromAffine)}))}multiplyUnsafe(t){const r=h.ZERO;if(t===Jo)return r;if(a(t),t===zo)return this;const{endo:s}=e;if(!s)return d.unsafeLadder(this,t);let{k1neg:i,k1:o,k2neg:c,k2:l}=s.splitScalar(t),u=r,f=r,p=this;for(;o>Jo||l>Jo;)o&zo&&(u=u.add(p)),l&zo&&(f=f.add(p)),p=p.double(),o>>=zo,l>>=zo;return i&&(u=u.negate()),c&&(f=f.negate()),f=new h(n.mul(f.px,s.beta),f.py,f.pz),u.add(f)}multiply(t){a(t);let r,s,i=t;const{endo:o}=e;if(o){const{k1neg:t,k1:e,k2neg:a,k2:c}=o.splitScalar(i);let{p:l,f:u}=this.wNAF(e),{p:f,f:p}=this.wNAF(c);l=d.constTimeNegate(t,l),f=d.constTimeNegate(a,f),f=new h(n.mul(f.px,o.beta),f.py,f.pz),r=l.add(f),s=u.add(p)}else{const{p:t,f:e}=this.wNAF(i);r=t,s=e}return h.normalizeZ([r,s])[0]}multiplyAndAddUnsafe(t,e,n){const r=h.BASE,s=(t,e)=>e!==Jo&&e!==zo&&t.equals(r)?t.multiply(e):t.multiplyUnsafe(e),i=s(this,e).add(s(t,n));return i.is0()?void 0:i}toAffine(t){const{px:e,py:r,pz:s}=this,i=this.is0();null==t&&(t=i?n.ONE:n.inv(s));const o=n.mul(e,t),a=n.mul(r,t),c=n.mul(s,t);if(i)return{x:n.ZERO,y:n.ZERO};if(!n.eql(c,n.ONE))throw new Error(\"invZ was invalid\");return{x:o,y:a}}isTorsionFree(){const{h:t,isTorsionFree:n}=e;if(t===zo)return!0;if(n)return n(h,this);throw new Error(\"isTorsionFree() has not been declared for the elliptic curve\")}clearCofactor(){const{h:t,clearCofactor:n}=e;return t===zo?this:n?n(h,this):this.multiplyUnsafe(e.h)}toRawBytes(t=!0){return this.assertValidity(),r(h,this,t)}toHex(t=!0){return so(this.toRawBytes(t))}}h.BASE=new h(e.Gx,e.Gy,n.ONE),h.ZERO=new h(n.ZERO,n.ONE,n.ZERO);const f=e.nBitLength,d=function(t,e){const n=(t,e)=>{const n=e.negate();return t?n:e},r=t=>({windows:Math.ceil(e/t)+1,windowSize:2**(t-1)});return{constTimeNegate:n,unsafeLadder(e,n){let r=t.ZERO,s=e;for(;n>Mo;)n&Go&&(r=r.add(s)),s=s.double(),n>>=Go;return r},precomputeWindow(t,e){const{windows:n,windowSize:s}=r(e),i=[];let o=t,a=o;for(let t=0;t<n;t++){a=o,i.push(a);for(let t=1;t<s;t++)a=a.add(o),i.push(a);o=a.double()}return i},wNAF(e,s,i){const{windows:o,windowSize:a}=r(e);let c=t.ZERO,l=t.BASE;const u=BigInt(2**e-1),h=2**e,f=BigInt(e);for(let t=0;t<o;t++){const e=t*a;let r=Number(i&u);i>>=f,r>a&&(r-=h,i+=Go);const o=e,d=e+Math.abs(r)-1,p=t%2!=0,g=r<0;0===r?l=l.add(n(p,s[o])):c=c.add(n(g,s[d]))}return{p:c,f:l}},wNAFCached(t,e,n,r){const s=t._WINDOW_SIZE||1;let i=e.get(t);return i||(i=this.precomputeWindow(t,s),1!==s&&e.set(t,r(i))),this.wNAF(s,i,n)}}}(h,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:h,normPrivateKeyToScalar:c,weierstrassEquation:i,isWithinCurveOrder:o}}function _o(t){const e=function(t){const e=Ho(t);return bo(e,{hash:\"hash\",hmac:\"function\",randomBytes:\"function\"},{bits2int:\"function\",bits2int_modN:\"function\",lowS:\"boolean\"}),Object.freeze({lowS:!0,...e})}(t),{Fp:n,n:r}=e,s=n.BYTES+1,i=2*n.BYTES+1;function o(t){return Bo(t,r)}function a(t){return Oo(t,r)}const{ProjectivePoint:c,normPrivateKeyToScalar:l,weierstrassEquation:u,isWithinCurveOrder:h}=qo({...e,toBytes(t,e,r){const s=e.toAffine(),i=n.toBytes(s.x),o=fo;return r?o(Uint8Array.from([e.hasEvenY()?2:3]),i):o(Uint8Array.from([4]),i,n.toBytes(s.y))},fromBytes(t){const e=t.length,r=t[0],o=t.subarray(1);if(e!==s||2!==r&&3!==r){if(e===i&&4===r){return{x:n.fromBytes(o.subarray(0,n.BYTES)),y:n.fromBytes(o.subarray(n.BYTES,2*n.BYTES))}}throw new Error(`Point of length ${e} was invalid. Expected ${s} compressed bytes or ${i} uncompressed bytes`)}{const t=ao(o);if(!(Jo<(a=t)&&a<n.ORDER))throw new Error(\"Point is not on curve\");const e=u(t);let s=n.sqrt(e);return 1==(1&r)!==((s&zo)===zo)&&(s=n.neg(s)),{x:t,y:s}}var a}}),f=t=>so(lo(t,e.nByteLength));function d(t){return t>r>>zo}const p=(t,e,n)=>ao(t.slice(e,n));class g{constructor(t,e,n){this.r=t,this.s=e,this.recovery=n,this.assertValidity()}static fromCompact(t){const n=e.nByteLength;return t=ho(\"compactSignature\",t,2*n),new g(p(t,0,n),p(t,n,2*n))}static fromDER(t){const{r:e,s:n}=Vo.toSig(ho(\"DER\",t));return new g(e,n)}assertValidity(){if(!h(this.r))throw new Error(\"r must be 0 < r < CURVE.n\");if(!h(this.s))throw new Error(\"s must be 0 < s < CURVE.n\")}addRecoveryBit(t){return new g(this.r,this.s,t)}recoverPublicKey(t){const{r,s,recovery:i}=this,l=b(ho(\"msgHash\",t));if(null==i||![0,1,2,3].includes(i))throw new Error(\"recovery id invalid\");const u=2===i||3===i?r+e.n:r;if(u>=n.ORDER)throw new Error(\"recovery id 2 or 3 invalid\");const h=0==(1&i)?\"02\":\"03\",d=c.fromHex(h+f(u)),p=a(u),g=o(-l*p),m=o(s*p),y=c.BASE.multiplyAndAddUnsafe(d,g,m);if(!y)throw new Error(\"point at infinify\");return y.assertValidity(),y}hasHighS(){return d(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return oo(this.toDERHex())}toDERHex(){return Vo.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return oo(this.toCompactHex())}toCompactHex(){return f(this.r)+f(this.s)}}const m={isValidPrivateKey(t){try{return l(t),!0}catch(t){return!1}},normPrivateKeyToScalar:l,randomPrivateKey:()=>{const t=Uo(e.n);return function(t,e,n=!1){const r=t.length,s=Fo(e),i=Uo(e);if(r<16||r<i||r>1024)throw new Error(`expected ${i}-1024 bytes of input, got ${r}`);const o=Bo(n?ao(t):co(t),e-vo)+vo;return n?uo(o,s):lo(o,s)}(e.randomBytes(t),e.n)},precompute:(t=8,e=c.BASE)=>(e._setWindowSize(t),e.multiply(BigInt(3)),e)};function y(t){const e=t instanceof Uint8Array,n=\"string\"==typeof t,r=(e||n)&&t.length;return e?r===s||r===i:n?r===2*s||r===2*i:t instanceof c}const w=e.bits2int||function(t){const n=ao(t),r=8*t.length-e.nBitLength;return r>0?n>>BigInt(r):n},b=e.bits2int_modN||function(t){return o(w(t))},A=po(e.nBitLength);function v(t){if(\"bigint\"!=typeof t)throw new Error(\"bigint expected\");if(!(Jo<=t&&t<A))throw new Error(`bigint expected < 2^${e.nBitLength}`);return lo(t,e.nByteLength)}function E(t,r,s=k){if([\"recovered\",\"canonical\"].some((t=>t in s)))throw new Error(\"sign() legacy options not supported\");const{hash:i,randomBytes:u}=e;let{lowS:f,prehash:p,extraEntropy:m}=s;null==f&&(f=!0),t=ho(\"msgHash\",t),p&&(t=ho(\"prehashed msgHash\",i(t)));const y=b(t),A=l(r),E=[v(A),v(y)];if(null!=m){const t=!0===m?u(n.BYTES):m;E.push(ho(\"extraEntropy\",t))}const P=fo(...E),x=y;return{seed:P,k2sig:function(t){const e=w(t);if(!h(e))return;const n=a(e),r=c.BASE.multiply(e).toAffine(),s=o(r.x);if(s===Jo)return;const i=o(n*o(x+s*A));if(i===Jo)return;let l=(r.x===s?0:2)|Number(r.y&zo),u=i;return f&&d(i)&&(u=function(t){return d(t)?o(-t):t}(i),l^=1),new g(s,u,l)}}}const k={lowS:e.lowS,prehash:!1},P={lowS:e.lowS,prehash:!1};return c.BASE._setWindowSize(8),{CURVE:e,getPublicKey:function(t,e=!0){return c.fromPrivateKey(t).toRawBytes(e)},getSharedSecret:function(t,e,n=!0){if(y(t))throw new Error(\"first arg must be private key\");if(!y(e))throw new Error(\"second arg must be public key\");return c.fromHex(e).multiply(l(t)).toRawBytes(n)},sign:function(t,n,r=k){const{seed:s,k2sig:i}=E(t,n,r),o=e;return yo(o.hash.outputLen,o.nByteLength,o.hmac)(s,i)},verify:function(t,n,r,s=P){const i=t;if(n=ho(\"msgHash\",n),r=ho(\"publicKey\",r),\"strict\"in s)throw new Error(\"options.strict was renamed to lowS\");const{lowS:l,prehash:u}=s;let h,f;try{if(\"string\"==typeof i||i instanceof Uint8Array)try{h=g.fromDER(i)}catch(t){if(!(t instanceof Vo.Err))throw t;h=g.fromCompact(i)}else{if(\"object\"!=typeof i||\"bigint\"!=typeof i.r||\"bigint\"!=typeof i.s)throw new Error(\"PARSE\");{const{r:t,s:e}=i;h=new g(t,e)}}f=c.fromHex(r)}catch(t){if(\"PARSE\"===t.message)throw new Error(\"signature must be Signature instance, Uint8Array or hex string\");return!1}if(l&&h.hasHighS())return!1;u&&(n=e.hash(n));const{r:d,s:p}=h,m=b(n),y=a(p),w=o(m*y),A=o(d*y),v=c.BASE.multiplyAndAddUnsafe(f,w,A)?.toAffine();return!!v&&o(v.x)===d},ProjectivePoint:c,Signature:g,utils:m}}function Zo(t){return{hash:t,hmac:(e,...n)=>Lo(t,e,function(...t){const e=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));let n=0;return t.forEach((t=>{if(!mt(t))throw new Error(\"Uint8Array expected\");e.set(t,n),n+=t.length})),e}(...n)),randomBytes:Et}}const Wo=BigInt(\"0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f\"),Yo=BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\"),Xo=BigInt(1),$o=BigInt(2),ta=(t,e)=>(t+e/$o)/e;function ea(t){const e=Wo,n=BigInt(3),r=BigInt(6),s=BigInt(11),i=BigInt(22),o=BigInt(23),a=BigInt(44),c=BigInt(88),l=t*t*t%e,u=l*l*t%e,h=Co(u,n,e)*u%e,f=Co(h,n,e)*u%e,d=Co(f,$o,e)*l%e,p=Co(d,s,e)*d%e,g=Co(p,i,e)*p%e,m=Co(g,a,e)*g%e,y=Co(m,c,e)*m%e,w=Co(y,a,e)*g%e,b=Co(w,n,e)*u%e,A=Co(b,o,e)*p%e,v=Co(A,r,e)*l%e,E=Co(v,$o,e);if(!na.eql(na.sqr(E),t))throw new Error(\"Cannot find square root\");return E}const na=function(t,e,n=!1,r={}){if(t<=Ao)throw new Error(`Expected Field ORDER > 0, got ${t}`);const{nBitLength:s,nByteLength:i}=So(t,e);if(i>2048)throw new Error(\"Field lengths over 2048 bytes are not supported\");const o=Ro(t),a=Object.freeze({ORDER:t,BITS:s,BYTES:i,MASK:po(s),ZERO:Ao,ONE:vo,create:e=>Bo(e,t),isValid:e=>{if(\"bigint\"!=typeof e)throw new Error(\"Invalid field element: expected bigint, got \"+typeof e);return Ao<=e&&e<t},is0:t=>t===Ao,isOdd:t=>(t&vo)===vo,neg:e=>Bo(-e,t),eql:(t,e)=>t===e,sqr:e=>Bo(e*e,t),add:(e,n)=>Bo(e+n,t),sub:(e,n)=>Bo(e-n,t),mul:(e,n)=>Bo(e*n,t),pow:(t,e)=>function(t,e,n){if(n<Ao)throw new Error(\"Expected power > 0\");if(n===Ao)return t.ONE;if(n===vo)return e;let r=t.ONE,s=e;for(;n>Ao;)n&vo&&(r=t.mul(r,s)),s=t.sqr(s),n>>=vo;return r}(a,t,e),div:(e,n)=>Bo(e*Oo(n,t),t),sqrN:t=>t*t,addN:(t,e)=>t+e,subN:(t,e)=>t-e,mulN:(t,e)=>t*e,inv:e=>Oo(e,t),sqrt:r.sqrt||(t=>o(a,t)),invertBatch:t=>function(t,e){const n=new Array(e.length),r=e.reduce(((e,r,s)=>t.is0(r)?e:(n[s]=e,t.mul(e,r))),t.ONE),s=t.inv(r);return e.reduceRight(((e,r,s)=>t.is0(r)?e:(n[s]=t.mul(e,n[s]),t.mul(e,r))),s),n}(a,t),cmov:(t,e,n)=>n?e:t,toBytes:t=>n?uo(t,i):lo(t,i),fromBytes:t=>{if(t.length!==i)throw new Error(`Fp.fromBytes: expected ${i}, got ${t.length}`);return n?co(t):ao(t)}});return Object.freeze(a)}(Wo,void 0,void 0,{sqrt:ea}),ra=function(t,e){const n=e=>_o({...t,...Zo(e)});return Object.freeze({...n(e),create:n})}({a:BigInt(0),b:BigInt(7),Fp:na,n:Yo,Gx:BigInt(\"55066263022277343669578718895168534326250603453777594175500187360389116729240\"),Gy:BigInt(\"32670510020758816978083085130507043184471273380659243275938904335757337482424\"),h:BigInt(1),lowS:!0,endo:{beta:BigInt(\"0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee\"),splitScalar:t=>{const e=Yo,n=BigInt(\"0x3086d221a7d46bcde86c90e49284eb15\"),r=-Xo*BigInt(\"0xe4437ed6010e88286f547fa90abfe4c3\"),s=BigInt(\"0x114ca50f7a8e2f3f657c1108d9d44cfd8\"),i=n,o=BigInt(\"0x100000000000000000000000000000000\"),a=ta(i*t,e),c=ta(-r*t,e);let l=Bo(t-a*n-c*s,e),u=Bo(-a*r-c*i,e);const h=l>o,f=u>o;if(h&&(l=e-l),f&&(u=e-u),l>o||u>o)throw new Error(\"splitScalar: Endomorphism failed, k=\"+t);return{k1neg:h,k1:l,k2neg:f,k2:u}}}},Bi);BigInt(0);ra.ProjectivePoint;class sa{#rt;constructor(t){f(32===P(t),\"invalid private key\",\"privateKey\",\"[REDACTED]\"),this.#rt=E(t)}get privateKey(){return this.#rt}get publicKey(){return sa.computePublicKey(this.#rt)}get compressedPublicKey(){return sa.computePublicKey(this.#rt,!0)}sign(t){f(32===P(t),\"invalid digest length\",\"digest\",t);const e=ra.sign(w(t),w(this.#rt),{lowS:!0});return $i.from({r:M(e.r,32),s:M(e.s,32),v:e.recovery?28:27})}computeSharedSecret(t){const e=sa.computePublicKey(t);return E(ra.getSharedSecret(w(this.#rt),y(e),!1))}static computePublicKey(t,e){let n=y(t,\"key\");if(32===n.length){return E(ra.getPublicKey(n,!!e))}if(64===n.length){const t=new Uint8Array(65);t[0]=4,t.set(n,1),n=t}return E(ra.ProjectivePoint.fromHex(n).toRawBytes(e))}static recoverPublicKey(t,e){f(32===P(t),\"invalid digest length\",\"digest\",t);const n=$i.from(e);let r=ra.Signature.fromCompact(w(k([n.r,n.s])));r=r.addRecoveryBit(n.yParity);const s=r.recoverPublicKey(w(t));return f(null!=s,\"invalid signautre for digest\",\"signature\",e),\"0x\"+s.toHex(!1)}static addPoints(t,e,n){const r=ra.ProjectivePoint.fromHex(sa.computePublicKey(t).substring(2)),s=ra.ProjectivePoint.fromHex(sa.computePublicKey(e).substring(2));return\"0x\"+r.add(s).toHex(!!n)}}function ia(t){let e=t.toString(16);for(;e.length<2;)e=\"0\"+e;return\"0x\"+e}function oa(t,e,n){let r=0;for(let s=0;s<n;s++)r=256*r+t[e+s];return r}function aa(t,e,n,r){const s=[];for(;n<e+1+r;){const i=ca(t,n);s.push(i.result),h((n+=i.consumed)<=e+1+r,\"child data too short\",\"BUFFER_OVERRUN\",{buffer:t,length:r,offset:e})}return{consumed:1+r,result:s}}function ca(t,e){h(0!==t.length,\"data too short\",\"BUFFER_OVERRUN\",{buffer:t,length:0,offset:1});const n=e=>{h(e<=t.length,\"data short segment too short\",\"BUFFER_OVERRUN\",{buffer:t,length:t.length,offset:e})};if(t[e]>=248){const r=t[e]-247;n(e+1+r);const s=oa(t,e+1,r);return n(e+1+r+s),aa(t,e,e+1+r,r+s)}if(t[e]>=192){const r=t[e]-192;return n(e+1+r),aa(t,e,e+1,r)}if(t[e]>=184){const r=t[e]-183;n(e+1+r);const s=oa(t,e+1,r);n(e+1+r+s);return{consumed:1+r+s,result:E(t.slice(e+1+r,e+1+r+s))}}if(t[e]>=128){const r=t[e]-128;n(e+1+r);return{consumed:1+r,result:E(t.slice(e+1,e+1+r))}}return{consumed:1,result:ia(t[e])}}function la(t){const e=y(t,\"data\"),n=ca(e,0);return f(n.consumed===e.length,\"unexpected junk after rlp payload\",\"data\",t),n.result}function ua(t){const e=[];for(;t;)e.unshift(255&t),t>>=8;return e}function ha(t){if(Array.isArray(t)){let e=[];if(t.forEach((function(t){e=e.concat(ha(t))})),e.length<=55)return e.unshift(192+e.length),e;const n=ua(e.length);return n.unshift(247+n.length),n.concat(e)}const e=Array.prototype.slice.call(y(t,\"object\"));if(1===e.length&&e[0]<=127)return e;if(e.length<=55)return e.unshift(128+e.length),e;const n=ua(e.length);return n.unshift(183+n.length),n.concat(e)}const fa=\"0123456789abcdef\";function da(t){let e=\"0x\";for(const n of ha(t))e+=fa[n>>4],e+=fa[15&n];return e}function pa(t,e){return function(t){let e;return e=\"string\"==typeof t?sa.computePublicKey(t,!1):t.publicKey,Wt(jt(\"0x\"+e.substring(4)).substring(26))}(sa.recoverPublicKey(t,e))}const ga=BigInt(0),ma=BigInt(2),ya=BigInt(27),wa=BigInt(28),ba=BigInt(35),Aa=BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\"),va=131072;function Ea(t,e){let n=t.toString(16);for(;n.length<2;)n=\"0\"+n;return n+=ji(e).substring(4),\"0x\"+n}function ka(t){return\"0x\"===t?null:Wt(t)}function Pa(t,e){try{return jn(t)}catch(n){f(!1,n.message,e,t)}}function xa(t,e){return\"0x\"===t?0:L(t,e)}function Na(t,e){if(\"0x\"===t)return ga;const n=S(t,e);return f(n<=Aa,\"value exceeds uint size\",e,n),n}function Ba(t,e){const n=S(t,\"value\"),r=G(n);return f(r.length<=32,\"value too large\",`tx.${e}`,n),r}function Ia(t){return jn(t).map((t=>[t.address,t.storageKeys]))}function Ca(t,e){f(Array.isArray(t),`invalid ${e}`,\"value\",t);for(let e=0;e<t.length;e++)f(b(t[e],32),\"invalid ${ param } hash\",`value[${e}]`,t[e]);return t}function Oa(t,e){let n;try{if(n=xa(e[0],\"yParity\"),0!==n&&1!==n)throw new Error(\"bad yParity\")}catch(t){f(!1,\"invalid yParity\",\"yParity\",e[0])}const r=B(e[1],32),s=B(e[2],32),i=$i.from({r,s,yParity:n});t.signature=i}class Ra{#st;#it;#e;#ot;#at;#ct;#lt;#ut;#ht;#ft;#dt;#pt;#gt;#mt;#yt;#wt;get type(){return this.#st}set type(t){switch(t){case null:this.#st=null;break;case 0:case\"legacy\":this.#st=0;break;case 1:case\"berlin\":case\"eip-2930\":this.#st=1;break;case 2:case\"london\":case\"eip-1559\":this.#st=2;break;case 3:case\"cancun\":case\"eip-4844\":this.#st=3;break;default:f(!1,\"unsupported transaction type\",\"type\",t)}}get typeName(){switch(this.type){case 0:return\"legacy\";case 1:return\"eip-2930\";case 2:return\"eip-1559\";case 3:return\"eip-4844\"}return null}get to(){const t=this.#it;return null==t&&3===this.type?rr:t}set to(t){this.#it=null==t?null:Wt(t)}get nonce(){return this.#ot}set nonce(t){this.#ot=L(t,\"value\")}get gasLimit(){return this.#at}set gasLimit(t){this.#at=S(t)}get gasPrice(){const t=this.#ct;return null!=t||0!==this.type&&1!==this.type?t:ga}set gasPrice(t){this.#ct=null==t?null:S(t,\"gasPrice\")}get maxPriorityFeePerGas(){const t=this.#lt;return null==t?2===this.type||3===this.type?ga:null:t}set maxPriorityFeePerGas(t){this.#lt=null==t?null:S(t,\"maxPriorityFeePerGas\")}get maxFeePerGas(){const t=this.#ut;return null==t?2===this.type||3===this.type?ga:null:t}set maxFeePerGas(t){this.#ut=null==t?null:S(t,\"maxFeePerGas\")}get data(){return this.#e}set data(t){this.#e=E(t)}get value(){return this.#ht}set value(t){this.#ht=S(t,\"value\")}get chainId(){return this.#ft}set chainId(t){this.#ft=S(t)}get signature(){return this.#dt||null}set signature(t){this.#dt=null==t?null:$i.from(t)}get accessList(){const t=this.#pt||null;return null==t?1===this.type||2===this.type||3===this.type?[]:null:t}set accessList(t){this.#pt=null==t?null:jn(t)}get maxFeePerBlobGas(){const t=this.#gt;return null==t&&3===this.type?ga:t}set maxFeePerBlobGas(t){this.#gt=null==t?null:S(t,\"maxFeePerBlobGas\")}get blobVersionedHashes(){let t=this.#mt;return null==t&&3===this.type?[]:t}set blobVersionedHashes(t){if(null!=t){f(Array.isArray(t),\"blobVersionedHashes must be an Array\",\"value\",t),t=t.slice();for(let e=0;e<t.length;e++)f(b(t[e],32),\"invalid blobVersionedHash\",`value[${e}]`,t[e])}this.#mt=t}get blobs(){return null==this.#wt?null:this.#wt.map((t=>Object.assign({},t)))}set blobs(t){if(null==t)return void(this.#wt=null);const e=[],n=[];for(let r=0;r<t.length;r++){const s=t[r];if(A(s)){h(this.#yt,\"adding a raw blob requires a KZG library\",\"UNSUPPORTED_OPERATION\",{operation:\"set blobs()\"});let t=y(s);if(f(t.length<=va,\"blob is too large\",`blobs[${r}]`,s),t.length!==va){const e=new Uint8Array(va);e.set(t),t=e}const i=this.#yt.blobToKzgCommitment(t),o=E(this.#yt.computeBlobKzgProof(t,i));e.push({data:E(t),commitment:E(i),proof:o}),n.push(Ea(1,i))}else{const t=E(s.commitment);e.push({data:E(s.data),commitment:t,proof:E(s.proof)}),n.push(Ea(1,t))}}this.#wt=e,this.#mt=n}get kzg(){return this.#yt}set kzg(t){this.#yt=t}constructor(){this.#st=null,this.#it=null,this.#ot=0,this.#at=ga,this.#ct=null,this.#lt=null,this.#ut=null,this.#e=\"0x\",this.#ht=ga,this.#ft=ga,this.#dt=null,this.#pt=null,this.#gt=null,this.#mt=null,this.#wt=null,this.#yt=null}get hash(){return null==this.signature?null:jt(this.#bt(!0,!1))}get unsignedHash(){return jt(this.unsignedSerialized)}get from(){return null==this.signature?null:pa(this.unsignedHash,this.signature)}get fromPublicKey(){return null==this.signature?null:sa.recoverPublicKey(this.unsignedHash,this.signature)}isSigned(){return null!=this.signature}#bt(t,e){h(!t||null!=this.signature,\"cannot serialize unsigned transaction; maybe you meant .unsignedSerialized\",\"UNSUPPORTED_OPERATION\",{operation:\".serialized\"});const n=t?this.signature:null;switch(this.inferType()){case 0:return function(t,e){const n=[Ba(t.nonce,\"nonce\"),Ba(t.gasPrice||0,\"gasPrice\"),Ba(t.gasLimit,\"gasLimit\"),t.to||\"0x\",Ba(t.value,\"value\"),t.data];let r=ga;if(t.chainId!=ga)r=S(t.chainId,\"tx.chainId\"),f(!e||null==e.networkV||e.legacyChainId===r,\"tx.chainId/sig.v mismatch\",\"sig\",e);else if(t.signature){const e=t.signature.legacyChainId;null!=e&&(r=e)}if(!e)return r!==ga&&(n.push(G(r)),n.push(\"0x\"),n.push(\"0x\")),da(n);let s=BigInt(27+e.yParity);return r!==ga?s=$i.getChainIdV(r,e.v):BigInt(e.v)!==s&&f(!1,\"tx.chainId/sig.v mismatch\",\"sig\",e),n.push(G(s)),n.push(G(e.r)),n.push(G(e.s)),da(n)}(this,n);case 1:return function(t,e){const n=[Ba(t.chainId,\"chainId\"),Ba(t.nonce,\"nonce\"),Ba(t.gasPrice||0,\"gasPrice\"),Ba(t.gasLimit,\"gasLimit\"),t.to||\"0x\",Ba(t.value,\"value\"),t.data,Ia(t.accessList||[])];return e&&(n.push(Ba(e.yParity,\"recoveryParam\")),n.push(G(e.r)),n.push(G(e.s))),k([\"0x01\",da(n)])}(this,n);case 2:return function(t,e){const n=[Ba(t.chainId,\"chainId\"),Ba(t.nonce,\"nonce\"),Ba(t.maxPriorityFeePerGas||0,\"maxPriorityFeePerGas\"),Ba(t.maxFeePerGas||0,\"maxFeePerGas\"),Ba(t.gasLimit,\"gasLimit\"),t.to||\"0x\",Ba(t.value,\"value\"),t.data,Ia(t.accessList||[])];return e&&(n.push(Ba(e.yParity,\"yParity\")),n.push(G(e.r)),n.push(G(e.s))),k([\"0x02\",da(n)])}(this,n);case 3:return function(t,e,n){const r=[Ba(t.chainId,\"chainId\"),Ba(t.nonce,\"nonce\"),Ba(t.maxPriorityFeePerGas||0,\"maxPriorityFeePerGas\"),Ba(t.maxFeePerGas||0,\"maxFeePerGas\"),Ba(t.gasLimit,\"gasLimit\"),t.to||rr,Ba(t.value,\"value\"),t.data,Ia(t.accessList||[]),Ba(t.maxFeePerBlobGas||0,\"maxFeePerBlobGas\"),Ca(t.blobVersionedHashes||[],\"blobVersionedHashes\")];return e&&(r.push(Ba(e.yParity,\"yParity\")),r.push(G(e.r)),r.push(G(e.s)),n)?k([\"0x03\",da([r,n.map((t=>t.data)),n.map((t=>t.commitment)),n.map((t=>t.proof))])]):k([\"0x03\",da(r)])}(this,n,e?this.blobs:null)}h(!1,\"unsupported transaction type\",\"UNSUPPORTED_OPERATION\",{operation:\".serialized\"})}get serialized(){return this.#bt(!0,!0)}get unsignedSerialized(){return this.#bt(!1,!1)}inferType(){const t=this.inferTypes();return t.indexOf(2)>=0?2:t.pop()}inferTypes(){const t=null!=this.gasPrice,e=null!=this.maxFeePerGas||null!=this.maxPriorityFeePerGas,n=null!=this.accessList,r=null!=this.#gt||this.#mt;null!=this.maxFeePerGas&&null!=this.maxPriorityFeePerGas&&h(this.maxFeePerGas>=this.maxPriorityFeePerGas,\"priorityFee cannot be more than maxFee\",\"BAD_DATA\",{value:this}),h(!e||0!==this.type&&1!==this.type,\"transaction type cannot have maxFeePerGas or maxPriorityFeePerGas\",\"BAD_DATA\",{value:this}),h(0!==this.type||!n,\"legacy transaction cannot have accessList\",\"BAD_DATA\",{value:this});const s=[];return null!=this.type?s.push(this.type):e?s.push(2):t?(s.push(1),n||s.push(0)):n?(s.push(1),s.push(2)):(r&&this.to||(s.push(0),s.push(1),s.push(2)),s.push(3)),s.sort(),s}isLegacy(){return 0===this.type}isBerlin(){return 1===this.type}isLondon(){return 2===this.type}isCancun(){return 3===this.type}clone(){return Ra.from(this)}toJSON(){const t=t=>null==t?null:t.toString();return{type:this.type,to:this.to,data:this.data,nonce:this.nonce,gasLimit:t(this.gasLimit),gasPrice:t(this.gasPrice),maxPriorityFeePerGas:t(this.maxPriorityFeePerGas),maxFeePerGas:t(this.maxFeePerGas),value:t(this.value),chainId:t(this.chainId),sig:this.signature?this.signature.toJSON():null,accessList:this.accessList}}static from(t){if(null==t)return new Ra;if(\"string\"==typeof t){const e=y(t);if(e[0]>=127)return Ra.from(function(t){const e=la(t);f(Array.isArray(e)&&(9===e.length||6===e.length),\"invalid field count for legacy transaction\",\"data\",t);const n={type:0,nonce:xa(e[0],\"nonce\"),gasPrice:Na(e[1],\"gasPrice\"),gasLimit:Na(e[2],\"gasLimit\"),to:ka(e[3]),value:Na(e[4],\"value\"),data:E(e[5]),chainId:ga};if(6===e.length)return n;const r=Na(e[6],\"v\"),s=Na(e[7],\"r\"),i=Na(e[8],\"s\");if(s===ga&&i===ga)n.chainId=r;else{let t=(r-ba)/ma;t<ga&&(t=ga),n.chainId=t,f(t!==ga||r===ya||r===wa,\"non-canonical legacy v\",\"v\",e[6]),n.signature=$i.from({r:B(e[7],32),s:B(e[8],32),v:r})}return n}(e));switch(e[0]){case 1:return Ra.from(function(t){const e=la(y(t).slice(1));f(Array.isArray(e)&&(8===e.length||11===e.length),\"invalid field count for transaction type: 1\",\"data\",E(t));const n={type:1,chainId:Na(e[0],\"chainId\"),nonce:xa(e[1],\"nonce\"),gasPrice:Na(e[2],\"gasPrice\"),gasLimit:Na(e[3],\"gasLimit\"),to:ka(e[4]),value:Na(e[5],\"value\"),data:E(e[6]),accessList:Pa(e[7],\"accessList\")};return 8===e.length||Oa(n,e.slice(8)),n}(e));case 2:return Ra.from(function(t){const e=la(y(t).slice(1));f(Array.isArray(e)&&(9===e.length||12===e.length),\"invalid field count for transaction type: 2\",\"data\",E(t));const n={type:2,chainId:Na(e[0],\"chainId\"),nonce:xa(e[1],\"nonce\"),maxPriorityFeePerGas:Na(e[2],\"maxPriorityFeePerGas\"),maxFeePerGas:Na(e[3],\"maxFeePerGas\"),gasPrice:null,gasLimit:Na(e[4],\"gasLimit\"),to:ka(e[5]),value:Na(e[6],\"value\"),data:E(e[7]),accessList:Pa(e[8],\"accessList\")};return 9===e.length||Oa(n,e.slice(9)),n}(e));case 3:return Ra.from(function(t){let e=la(y(t).slice(1)),n=\"3\",r=null;if(4===e.length&&Array.isArray(e[0])){n=\"3 (network format)\";const t=e[1],s=e[2],i=e[3];f(Array.isArray(t),\"invalid network format: blobs not an array\",\"fields[1]\",t),f(Array.isArray(s),\"invalid network format: commitments not an array\",\"fields[2]\",s),f(Array.isArray(i),\"invalid network format: proofs not an array\",\"fields[3]\",i),f(t.length===s.length,\"invalid network format: blobs/commitments length mismatch\",\"fields\",e),f(t.length===i.length,\"invalid network format: blobs/proofs length mismatch\",\"fields\",e),r=[];for(let n=0;n<e[1].length;n++)r.push({data:t[n],commitment:s[n],proof:i[n]});e=e[0]}f(Array.isArray(e)&&(11===e.length||14===e.length),`invalid field count for transaction type: ${n}`,\"data\",E(t));const s={type:3,chainId:Na(e[0],\"chainId\"),nonce:xa(e[1],\"nonce\"),maxPriorityFeePerGas:Na(e[2],\"maxPriorityFeePerGas\"),maxFeePerGas:Na(e[3],\"maxFeePerGas\"),gasPrice:null,gasLimit:Na(e[4],\"gasLimit\"),to:ka(e[5]),value:Na(e[6],\"value\"),data:E(e[7]),accessList:Pa(e[8],\"accessList\"),maxFeePerBlobGas:Na(e[9],\"maxFeePerBlobGas\"),blobVersionedHashes:e[10]};r&&(s.blobs=r),f(null!=s.to,`invalid address for transaction type: ${n}`,\"data\",t),f(Array.isArray(s.blobVersionedHashes),\"invalid blobVersionedHashes: must be an array\",\"data\",t);for(let e=0;e<s.blobVersionedHashes.length;e++)f(b(s.blobVersionedHashes[e],32),`invalid blobVersionedHash at index ${e}: must be length 32`,\"data\",t);return 11===e.length||Oa(s,e.slice(11)),s}(e))}h(!1,\"unsupported transaction type\",\"UNSUPPORTED_OPERATION\",{operation:\"from\"})}const e=new Ra;return null!=t.type&&(e.type=t.type),null!=t.to&&(e.to=t.to),null!=t.nonce&&(e.nonce=t.nonce),null!=t.gasLimit&&(e.gasLimit=t.gasLimit),null!=t.gasPrice&&(e.gasPrice=t.gasPrice),null!=t.maxPriorityFeePerGas&&(e.maxPriorityFeePerGas=t.maxPriorityFeePerGas),null!=t.maxFeePerGas&&(e.maxFeePerGas=t.maxFeePerGas),null!=t.maxFeePerBlobGas&&(e.maxFeePerBlobGas=t.maxFeePerBlobGas),null!=t.data&&(e.data=t.data),null!=t.value&&(e.value=t.value),null!=t.chainId&&(e.chainId=t.chainId),null!=t.signature&&(e.signature=$i.from(t.signature)),null!=t.accessList&&(e.accessList=t.accessList),null!=t.blobVersionedHashes&&(e.blobVersionedHashes=t.blobVersionedHashes),null!=t.kzg&&(e.kzg=t.kzg),null!=t.blobs&&(e.blobs=t.blobs),null!=t.hash&&(f(e.isSigned(),\"unsigned transaction cannot define '.hash'\",\"tx\",t),f(e.hash===t.hash,\"hash mismatch\",\"tx\",t)),null!=t.from&&(f(e.isSigned(),\"unsigned transaction cannot define '.from'\",\"tx\",t),f(e.from.toLowerCase()===(t.from||\"\").toLowerCase(),\"from mismatch\",\"tx\",t)),e}}const Ta=\"123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz\";BigInt(0);const Sa=BigInt(58);function Fa(t){return t.match(/^ipfs:\\/\\/ipfs\\//i)?t=t.substring(12):t.match(/^ipfs:\\/\\//i)?t=t.substring(7):f(!1,\"unsupported IPFS format\",\"link\",t),`https://gateway.ipfs.io/ipfs/${t}`}class Ua{name;constructor(t){o(this,{name:t})}connect(t){return this}supportsCoinType(t){return!1}async encodeAddress(t,e){throw new Error(\"unsupported coin\")}async decodeAddress(t,e){throw new Error(\"unsupported coin\")}}const Da=new RegExp(\"^(ipfs)://(.*)$\",\"i\"),La=[new RegExp(\"^(https)://(.*)$\",\"i\"),new RegExp(\"^(data):(.*)$\",\"i\"),Da,new RegExp(\"^eip155:[0-9]+/(erc[0-9]+):(.*)$\",\"i\")];class Ma{provider;address;name;#At;#vt;constructor(t,e,n){o(this,{provider:t,address:e,name:n}),this.#At=null,this.#vt=new Yr(e,[\"function supportsInterface(bytes4) view returns (bool)\",\"function resolve(bytes, bytes) view returns (bytes)\",\"function addr(bytes32) view returns (address)\",\"function addr(bytes32, uint) view returns (bytes)\",\"function text(bytes32, string) view returns (string)\",\"function contenthash(bytes32) view returns (bytes)\"],t)}async supportsWildcard(){return null==this.#At&&(this.#At=(async()=>{try{return await this.#vt.supportsInterface(\"0x9061b923\")}catch(t){if(c(t,\"CALL_EXCEPTION\"))return!1;throw this.#At=null,t}})()),await this.#At}async#Et(t,e){e=(e||[]).slice();const n=this.#vt.interface;e.unshift(wi(this.name));let r=null;await this.supportsWildcard()&&(r=n.getFunction(t),h(r,\"missing fragment\",\"UNKNOWN_ERROR\",{info:{funcName:t}}),e=[bi(this.name,255),n.encodeFunctionData(r,e)],t=\"resolve(bytes,bytes)\"),e.push({enableCcipRead:!0});try{const s=await this.#vt[t](...e);return r?n.decodeFunctionResult(r,s)[0]:s}catch(t){if(!c(t,\"CALL_EXCEPTION\"))throw t}return null}async getAddress(t){if(null==t&&(t=60),60===t)try{const t=await this.#Et(\"addr(bytes32)\");return null==t||t===rr?null:t}catch(t){if(c(t,\"CALL_EXCEPTION\"))return null;throw t}if(t>=0&&t<**********){let e=t+**********;const n=await this.#Et(\"addr(bytes32,uint)\",[e]);if(b(n,20))return Wt(n)}let e=null;for(const n of this.provider.plugins)if(n instanceof Ua&&n.supportsCoinType(t)){e=n;break}if(null==e)return null;const n=await this.#Et(\"addr(bytes32,uint)\",[t]);if(null==n||\"0x\"===n)return null;const r=await e.decodeAddress(t,n);if(null!=r)return r;h(!1,\"invalid coin data\",\"UNSUPPORTED_OPERATION\",{operation:`getAddress(${t})`,info:{coinType:t,data:n}})}async getText(t){const e=await this.#Et(\"text(bytes32,string)\",[t]);return null==e||\"0x\"===e?null:e}async getContentHash(){const t=await this.#Et(\"contenthash(bytes32)\");if(null==t||\"0x\"===t)return null;const e=t.match(/^0x(e3010170|e5010172)(([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])([0-9a-f]*))$/);if(e){const t=\"e3010170\"===e[1]?\"ipfs\":\"ipns\",n=parseInt(e[4],16);if(e[5].length===2*n)return`${t}://${function(t){const e=y(t);let n=D(e),r=\"\";for(;n;)r=Ta[Number(n%Sa)]+r,n/=Sa;for(let t=0;t<e.length&&!e[t];t++)r=Ta[0]+r;return r}(\"0x\"+e[2])}`}const n=t.match(/^0xe40101fa011b20([0-9a-f]*)$/);if(n&&64===n[1].length)return`bzz://${n[1]}`;h(!1,\"invalid or unsupported content hash data\",\"UNSUPPORTED_OPERATION\",{operation:\"getContentHash()\",info:{data:t}})}async getAvatar(){return(await this._getAvatar()).url}async _getAvatar(){const t=[{type:\"name\",value:this.name}];try{const e=await this.getText(\"avatar\");if(null==e)return t.push({type:\"!avatar\",value:\"\"}),{url:null,linkage:t};t.push({type:\"avatar\",value:e});for(let n=0;n<La.length;n++){const r=e.match(La[n]);if(null==r)continue;const s=r[1].toLowerCase();switch(s){case\"https\":case\"data\":return t.push({type:\"url\",value:e}),{linkage:t,url:e};case\"ipfs\":{const n=Fa(e);return t.push({type:\"ipfs\",value:e}),t.push({type:\"url\",value:n}),{linkage:t,url:n}}case\"erc721\":case\"erc1155\":{const n=\"erc721\"===s?\"tokenURI(uint256)\":\"uri(uint256)\";t.push({type:s,value:e});const i=await this.getAddress();if(null==i)return t.push({type:\"!owner\",value:\"\"}),{url:null,linkage:t};const o=(r[2]||\"\").split(\"/\");if(2!==o.length)return t.push({type:`!${s}caip`,value:r[2]||\"\"}),{url:null,linkage:t};const a=o[1],c=new Yr(o[0],[\"function tokenURI(uint) view returns (string)\",\"function ownerOf(uint) view returns (address)\",\"function uri(uint) view returns (string)\",\"function balanceOf(address, uint256) view returns (uint)\"],this.provider);if(\"erc721\"===s){const e=await c.ownerOf(a);if(i!==e)return t.push({type:\"!owner\",value:e}),{url:null,linkage:t};t.push({type:\"owner\",value:e})}else if(\"erc1155\"===s){const e=await c.balanceOf(i,a);if(!e)return t.push({type:\"!balance\",value:\"0\"}),{url:null,linkage:t};t.push({type:\"balance\",value:e.toString()})}let l=await c[n](a);if(null==l||\"0x\"===l)return t.push({type:\"!metadata-url\",value:\"\"}),{url:null,linkage:t};t.push({type:\"metadata-url-base\",value:l}),\"erc1155\"===s&&(l=l.replace(\"{id}\",M(a,32).substring(2)),t.push({type:\"metadata-url-expanded\",value:l})),l.match(/^ipfs:/i)&&(l=Fa(l)),t.push({type:\"metadata-url\",value:l});let u={};const h=await new tr(l).send();h.assertOk();try{u=h.bodyJson}catch(e){try{t.push({type:\"!metadata\",value:h.bodyText})}catch(e){const n=h.body;return n&&t.push({type:\"!metadata\",value:E(n)}),{url:null,linkage:t}}return{url:null,linkage:t}}if(!u)return t.push({type:\"!metadata\",value:\"\"}),{url:null,linkage:t};t.push({type:\"metadata\",value:JSON.stringify(u)});let f=u.image;if(\"string\"!=typeof f)return t.push({type:\"!imageUrl\",value:\"\"}),{url:null,linkage:t};if(f.match(/^(https:\\/\\/|data:)/i));else{if(null==f.match(Da))return t.push({type:\"!imageUrl-ipfs\",value:f}),{url:null,linkage:t};t.push({type:\"imageUrl-ipfs\",value:f}),f=Fa(f)}return t.push({type:\"url\",value:f}),{linkage:t,url:f}}}}}catch(t){}return{linkage:t,url:null}}static async getEnsAddress(t){const e=await t.getNetwork(),n=e.getPlugin(\"org.ethers.plugins.network.Ens\");return h(n,\"network does not support ENS\",\"UNSUPPORTED_OPERATION\",{operation:\"getEnsAddress\",info:{network:e}}),n.address}static async#kt(t,e){const n=await Ma.getEnsAddress(t);try{const r=new Yr(n,[\"function resolver(bytes32) view returns (address)\"],t),s=await r.resolver(wi(e),{enableCcipRead:!0});return s===rr?null:s}catch(t){throw t}return null}static async fromName(t,e){let n=e;for(;;){if(\"\"===n||\".\"===n)return null;if(\"eth\"!==e&&\"eth\"===n)return null;const r=await Ma.#kt(t,n);if(null!=r){const s=new Ma(t,r,e);return n===e||await s.supportsWildcard()?s:null}n=n.split(\".\").slice(1).join(\".\")}}}const Ga=BigInt(0);function Ha(t,e){return function(n){return null==n?e:t(n)}}function Qa(t,e){return n=>{if(e&&null==n)return null;if(!Array.isArray(n))throw new Error(\"not an array\");return n.map((e=>t(e)))}}function ja(t,e){return n=>{const r={};for(const s in t){let i=s;if(e&&s in e&&!(i in n))for(const t of e[s])if(t in n){i=t;break}try{const e=t[s](n[i]);void 0!==e&&(r[s]=e)}catch(t){h(!1,`invalid value for value.${s} (${t instanceof Error?t.message:\"not-an-error\"})`,\"BAD_DATA\",{value:n})}}return r}}function Va(t){return f(b(t,!0),\"invalid data\",\"value\",t),t}function Ja(t){return f(b(t,32),\"invalid hash\",\"value\",t),t}const za=ja({address:Wt,blockHash:Ja,blockNumber:L,data:Va,index:L,removed:Ha((function(t){switch(t){case!0:case\"true\":return!0;case!1:case\"false\":return!1}f(!1,`invalid boolean; ${JSON.stringify(t)}`,\"value\",t)}),!1),topics:Qa(Ja),transactionHash:Ja,transactionIndex:L},{index:[\"logIndex\"]});const Ka=ja({hash:Ha(Ja),parentHash:Ja,parentBeaconBlockRoot:Ha(Ja,null),number:L,timestamp:L,nonce:Ha(Va),difficulty:S,gasLimit:S,gasUsed:S,stateRoot:Ha(Ja,null),receiptsRoot:Ha(Ja,null),blobGasUsed:Ha(S,null),excessBlobGas:Ha(S,null),miner:Ha(Wt),prevRandao:Ha(Ja,null),extraData:Va,baseFeePerGas:Ha(S)},{prevRandao:[\"mixHash\"]});const qa=ja({transactionIndex:L,blockNumber:L,transactionHash:Ja,address:Wt,topics:Qa(Ja),data:Va,index:L,blockHash:Ja},{index:[\"logIndex\"]});const _a=ja({to:Ha(Wt,null),from:Ha(Wt,null),contractAddress:Ha(Wt,null),index:L,root:Ha(E),gasUsed:S,blobGasUsed:Ha(S,null),logsBloom:Ha(Va),blockHash:Ja,hash:Ja,logs:Qa((function(t){return qa(t)})),blockNumber:L,cumulativeGasUsed:S,effectiveGasPrice:Ha(S),blobGasPrice:Ha(S,null),status:Ha(L),type:Ha(L,0)},{effectiveGasPrice:[\"gasPrice\"],hash:[\"transactionHash\"],index:[\"transactionIndex\"]});function Za(t){t.to&&S(t.to)===Ga&&(t.to=\"******************************************\");const e=ja({hash:Ja,index:Ha(L,void 0),type:t=>\"0x\"===t||null==t?0:L(t),accessList:Ha(jn,null),blobVersionedHashes:Ha(Qa(Ja,!0),null),blockHash:Ha(Ja,null),blockNumber:Ha(L,null),transactionIndex:Ha(L,null),from:Wt,gasPrice:Ha(S),maxPriorityFeePerGas:Ha(S),maxFeePerGas:Ha(S),maxFeePerBlobGas:Ha(S,null),gasLimit:S,to:Ha(Wt,null),value:S,nonce:L,data:Va,creates:Ha(Wt,null),chainId:Ha(S,null)},{data:[\"input\"],gasLimit:[\"gas\"],index:[\"transactionIndex\"]})(t);if(null==e.to&&null==e.creates&&(e.creates=function(t){const e=Wt(t.from);let n=S(t.nonce,\"tx.nonce\").toString(16);return n=\"0\"===n?\"0x\":n.length%2?\"0x0\"+n:\"0x\"+n,Wt(x(jt(da([e,n])),12))}(e)),1!==t.type&&2!==t.type||null!=t.accessList||(e.accessList=[]),t.signature?e.signature=$i.from(t.signature):e.signature=$i.from(t),null==e.chainId){const t=e.signature.legacyChainId;null!=t&&(e.chainId=t)}return e.blockHash&&S(e.blockHash)===Ga&&(e.blockHash=null),e}class Wa{name;constructor(t){o(this,{name:t})}clone(){return new Wa(this.name)}}class Ya extends Wa{effectiveBlock;txBase;txCreate;txDataZero;txDataNonzero;txAccessListStorageKey;txAccessListAddress;constructor(t,e){null==t&&(t=0),super(`org.ethers.network.plugins.GasCost#${t||0}`);const n={effectiveBlock:t};function r(t,r){let s=(e||{})[t];null==s&&(s=r),f(\"number\"==typeof s,`invalud value for ${t}`,\"costs\",e),n[t]=s}r(\"txBase\",21e3),r(\"txCreate\",32e3),r(\"txDataZero\",4),r(\"txDataNonzero\",16),r(\"txAccessListStorageKey\",1900),r(\"txAccessListAddress\",2400),o(this,n)}clone(){return new Ya(this.effectiveBlock,this)}}class Xa extends Wa{address;targetNetwork;constructor(t,e){super(\"org.ethers.plugins.network.Ens\"),o(this,{address:t||\"******************************************\",targetNetwork:null==e?1:e})}clone(){return new Xa(this.address,this.targetNetwork)}}class $a extends Wa{#N;#Pt;get url(){return this.#N}get processFunc(){return this.#Pt}constructor(t,e){super(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\"),this.#N=t,this.#Pt=e}clone(){return this}}const tc=new Map;class ec{#xt;#ft;#Nt;constructor(t,e){this.#xt=t,this.#ft=S(e),this.#Nt=new Map}toJSON(){return{name:this.name,chainId:String(this.chainId)}}get name(){return this.#xt}set name(t){this.#xt=t}get chainId(){return this.#ft}set chainId(t){this.#ft=S(t,\"chainId\")}matches(t){if(null==t)return!1;if(\"string\"==typeof t){try{return this.chainId===S(t)}catch(t){}return this.name===t}if(\"number\"==typeof t||\"bigint\"==typeof t){try{return this.chainId===S(t)}catch(t){}return!1}if(\"object\"==typeof t){if(null!=t.chainId){try{return this.chainId===S(t.chainId)}catch(t){}return!1}return null!=t.name&&this.name===t.name}return!1}get plugins(){return Array.from(this.#Nt.values())}attachPlugin(t){if(this.#Nt.get(t.name))throw new Error(`cannot replace existing plugin: ${t.name} `);return this.#Nt.set(t.name,t.clone()),this}getPlugin(t){return this.#Nt.get(t)||null}getPlugins(t){return this.plugins.filter((e=>e.name.split(\"#\")[0]===t))}clone(){const t=new ec(this.name,this.chainId);return this.plugins.forEach((e=>{t.attachPlugin(e.clone())})),t}computeIntrinsicGas(t){const e=this.getPlugin(\"org.ethers.plugins.network.GasCost\")||new Ya;let n=e.txBase;if(null==t.to&&(n+=e.txCreate),t.data)for(let r=2;r<t.data.length;r+=2)\"00\"===t.data.substring(r,r+2)?n+=e.txDataZero:n+=e.txDataNonzero;if(t.accessList){const r=jn(t.accessList);for(const t in r)n+=e.txAccessListAddress+e.txAccessListStorageKey*r[t].storageKeys.length}return n}static from(t){if(function(){if(sc)return;function t(t,e,n){const r=function(){const r=new ec(t,e);return null!=n.ensNetwork&&r.attachPlugin(new Xa(null,n.ensNetwork)),r.attachPlugin(new Ya),(n.plugins||[]).forEach((t=>{r.attachPlugin(t)})),r};ec.register(t,r),ec.register(e,r),n.altNames&&n.altNames.forEach((t=>{ec.register(t,r)}))}sc=!0,t(\"mainnet\",1,{ensNetwork:1,altNames:[\"homestead\"]}),t(\"ropsten\",3,{ensNetwork:3}),t(\"rinkeby\",4,{ensNetwork:4}),t(\"goerli\",5,{ensNetwork:5}),t(\"kovan\",42,{ensNetwork:42}),t(\"sepolia\",11155111,{ensNetwork:11155111}),t(\"holesky\",17e3,{ensNetwork:17e3}),t(\"classic\",61,{}),t(\"classicKotti\",6,{}),t(\"arbitrum\",42161,{ensNetwork:1}),t(\"arbitrum-goerli\",421613,{}),t(\"arbitrum-sepolia\",421614,{}),t(\"base\",8453,{ensNetwork:1}),t(\"base-goerli\",84531,{}),t(\"base-sepolia\",84532,{}),t(\"bnb\",56,{ensNetwork:1}),t(\"bnbt\",97,{}),t(\"linea\",59144,{ensNetwork:1}),t(\"linea-goerli\",59140,{}),t(\"linea-sepolia\",59141,{}),t(\"matic\",137,{ensNetwork:1,plugins:[rc(\"https://gasstation.polygon.technology/v2\")]}),t(\"matic-amoy\",80002,{}),t(\"matic-mumbai\",80001,{altNames:[\"maticMumbai\",\"maticmum\"],plugins:[rc(\"https://gasstation-testnet.polygon.technology/v2\")]}),t(\"optimism\",10,{ensNetwork:1,plugins:[]}),t(\"optimism-goerli\",420,{}),t(\"optimism-sepolia\",11155420,{}),t(\"xdai\",100,{ensNetwork:1})}(),null==t)return ec.from(\"mainnet\");if(\"number\"==typeof t&&(t=BigInt(t)),\"string\"==typeof t||\"bigint\"==typeof t){const e=tc.get(t);if(e)return e();if(\"bigint\"==typeof t)return new ec(\"unknown\",t);f(!1,\"unknown network\",\"network\",t)}if(\"function\"==typeof t.clone){return t.clone()}if(\"object\"==typeof t){f(\"string\"==typeof t.name&&\"number\"==typeof t.chainId,\"invalid network object name or chainId\",\"network\",t);const e=new ec(t.name,t.chainId);return(t.ensAddress||null!=t.ensNetwork)&&e.attachPlugin(new Xa(t.ensAddress,t.ensNetwork)),e}f(!1,\"invalid network\",\"network\",t)}static register(t,e){\"number\"==typeof t&&(t=BigInt(t));const n=tc.get(t);n&&f(!1,`conflicting network for ${JSON.stringify(n.name)}`,\"nameOrChainId\",t),tc.set(t,e)}}function nc(t,e){const n=String(t);if(!n.match(/^[0-9.]+$/))throw new Error(`invalid gwei value: ${t}`);const r=n.split(\".\");if(1===r.length&&r.push(\"\"),2!==r.length)throw new Error(`invalid gwei value: ${t}`);for(;r[1].length<e;)r[1]+=\"0\";if(r[1].length>9){let t=BigInt(r[1].substring(0,9));r[1].substring(9).match(/^0+$/)||t++,r[1]=t.toString()}return BigInt(r[0]+r[1])}function rc(t){return new $a(t,(async(t,e,n)=>{let r;n.setHeader(\"User-Agent\",\"ethers\");try{const[e,s]=await Promise.all([n.send(),t()]);r=e;const i=r.bodyJson.standard;return{gasPrice:s.gasPrice,maxFeePerGas:nc(i.maxFee,9),maxPriorityFeePerGas:nc(i.maxPriorityFee,9)}}catch(t){h(!1,`error encountered with polygon gas station (${JSON.stringify(n.url)})`,\"SERVER_ERROR\",{request:n,response:r,error:t})}}))}let sc=!1;function ic(t){return JSON.parse(JSON.stringify(t))}class oc{#Bt;#It;#Ct;#Ot;constructor(t){this.#Bt=t,this.#It=null,this.#Ct=4e3,this.#Ot=-2}get pollingInterval(){return this.#Ct}set pollingInterval(t){this.#Ct=t}async#Rt(){try{const t=await this.#Bt.getBlockNumber();if(-2===this.#Ot)return void(this.#Ot=t);if(t!==this.#Ot){for(let e=this.#Ot+1;e<=t;e++){if(null==this.#It)return;await this.#Bt.emit(\"block\",e)}this.#Ot=t}}catch(t){}null!=this.#It&&(this.#It=this.#Bt._setTimeout(this.#Rt.bind(this),this.#Ct))}start(){this.#It||(this.#It=this.#Bt._setTimeout(this.#Rt.bind(this),this.#Ct),this.#Rt())}stop(){this.#It&&(this.#Bt._clearTimeout(this.#It),this.#It=null)}pause(t){this.stop(),t&&(this.#Ot=-2)}resume(){this.start()}}class ac{#Bt;#Rt;#Tt;constructor(t){this.#Bt=t,this.#Tt=!1,this.#Rt=t=>{this._poll(t,this.#Bt)}}async _poll(t,e){throw new Error(\"sub-classes must override this\")}start(){this.#Tt||(this.#Tt=!0,this.#Rt(-2),this.#Bt.on(\"block\",this.#Rt))}stop(){this.#Tt&&(this.#Tt=!1,this.#Bt.off(\"block\",this.#Rt))}pause(t){this.stop()}resume(){this.start()}}class cc extends ac{#St;#Ft;constructor(t,e){super(t),this.#St=e,this.#Ft=-2}pause(t){t&&(this.#Ft=-2),super.pause(t)}async _poll(t,e){const n=await e.getBlock(this.#St);null!=n&&(-2===this.#Ft?this.#Ft=n.number:n.number>this.#Ft&&(e.emit(this.#St,n.number),this.#Ft=n.number))}}class lc extends ac{#X;constructor(t,e){super(t),this.#X=ic(e)}async _poll(t,e){throw new Error(\"@TODO\")}}class uc extends ac{#Ut;constructor(t,e){super(t),this.#Ut=e}async _poll(t,e){const n=await e.getTransactionReceipt(this.#Ut);n&&e.emit(this.#Ut,n)}}class hc{#Bt;#X;#It;#Tt;#Ot;constructor(t,e){this.#Bt=t,this.#X=ic(e),this.#It=this.#Rt.bind(this),this.#Tt=!1,this.#Ot=-2}async#Rt(t){if(-2===this.#Ot)return;const e=ic(this.#X);e.fromBlock=this.#Ot+1,e.toBlock=t;const n=await this.#Bt.getLogs(e);if(0!==n.length)for(const t of n)this.#Bt.emit(this.#X,t),this.#Ot=t.blockNumber;else this.#Ot<t-60&&(this.#Ot=t-60)}start(){this.#Tt||(this.#Tt=!0,-2===this.#Ot&&this.#Bt.getBlockNumber().then((t=>{this.#Ot=t})),this.#Bt.on(\"block\",this.#It))}stop(){this.#Tt&&(this.#Tt=!1,this.#Bt.off(\"block\",this.#It))}pause(t){this.stop(),t&&(this.#Ot=-2)}resume(){this.start()}}const fc=BigInt(2);function dc(t){return t&&\"function\"==typeof t.then}function pc(t,e){return t+\":\"+JSON.stringify(e,((t,e)=>{if(null==e)return\"null\";if(\"bigint\"==typeof e)return`bigint:${e.toString()}`;if(\"string\"==typeof e)return e.toLowerCase();if(\"object\"==typeof e&&!Array.isArray(e)){const t=Object.keys(e);return t.sort(),t.reduce(((t,n)=>(t[n]=e[n],t)),{})}return e}))}class gc{name;constructor(t){o(this,{name:t})}start(){}stop(){}pause(t){}resume(){}}function mc(t){return(t=Array.from(new Set(t).values())).sort(),t}async function yc(t,e){if(null==t)throw new Error(\"invalid event\");if(Array.isArray(t)&&(t={topics:t}),\"string\"==typeof t)switch(t){case\"block\":case\"debug\":case\"error\":case\"finalized\":case\"network\":case\"pending\":case\"safe\":return{type:t,tag:t}}if(b(t,32)){const e=t.toLowerCase();return{type:\"transaction\",tag:pc(\"tx\",{hash:e}),hash:e}}if(t.orphan){const e=t;return{type:\"orphan\",tag:pc(\"orphan\",e),filter:(n=e,JSON.parse(JSON.stringify(n)))}}var n;if(t.address||t.topics){const n=t,r={topics:(n.topics||[]).map((t=>null==t?null:Array.isArray(t)?mc(t.map((t=>t.toLowerCase()))):t.toLowerCase()))};if(n.address){const t=[],s=[],i=n=>{b(n)?t.push(n):s.push((async()=>{t.push(await xn(n,e))})())};Array.isArray(n.address)?n.address.forEach(i):i(n.address),s.length&&await Promise.all(s),r.address=mc(t.map((t=>t.toLowerCase())))}return{filter:r,tag:pc(\"event\",r),type:\"event\"}}f(!1,\"unknown ProviderEvent\",\"event\",t)}function wc(){return(new Date).getTime()}const bc={cacheTimeout:250,pollingInterval:4e3};class Ac{#Dt;#Nt;#Lt;#Mt;#Gt;#Ht;#Qt;#jt;#Vt;#Jt;#zt;#u;constructor(t,e){if(this.#u=Object.assign({},bc,e||{}),\"any\"===t)this.#Ht=!0,this.#Gt=null;else if(t){const e=ec.from(t);this.#Ht=!1,this.#Gt=Promise.resolve(e),setTimeout((()=>{this.emit(\"network\",e,null)}),0)}else this.#Ht=!1,this.#Gt=null;this.#jt=-1,this.#Qt=new Map,this.#Dt=new Map,this.#Nt=new Map,this.#Lt=null,this.#Mt=!1,this.#Vt=1,this.#Jt=new Map,this.#zt=!1}get pollingInterval(){return this.#u.pollingInterval}get provider(){return this}get plugins(){return Array.from(this.#Nt.values())}attachPlugin(t){if(this.#Nt.get(t.name))throw new Error(`cannot replace existing plugin: ${t.name} `);return this.#Nt.set(t.name,t.connect(this)),this}getPlugin(t){return this.#Nt.get(t)||null}get disableCcipRead(){return this.#zt}set disableCcipRead(t){this.#zt=!!t}async#Kt(t){const e=this.#u.cacheTimeout;if(e<0)return await this._perform(t);const n=pc(t.method,t);let r=this.#Qt.get(n);return r||(r=this._perform(t),this.#Qt.set(n,r),setTimeout((()=>{this.#Qt.get(n)===r&&this.#Qt.delete(n)}),e)),await r}async ccipReadFetch(t,e,n){if(this.disableCcipRead||0===n.length||null==t.to)return null;const r=t.to.toLowerCase(),s=e.toLowerCase(),i=[];for(let e=0;e<n.length;e++){const o=n[e],a=o.replace(\"{sender}\",r).replace(\"{data}\",s),c=new tr(a);-1===o.indexOf(\"{data}\")&&(c.body={data:s,sender:r}),this.emit(\"debug\",{action:\"sendCcipReadFetchRequest\",request:c,index:e,urls:n});let l=\"unknown error\";const u=await c.send();try{const t=u.bodyJson;if(t.data)return this.emit(\"debug\",{action:\"receiveCcipReadFetchResult\",request:c,result:t}),t.data;t.message&&(l=t.message),this.emit(\"debug\",{action:\"receiveCcipReadFetchError\",request:c,result:t})}catch(t){}h(u.statusCode<400||u.statusCode>=500,`response not found during CCIP fetch: ${l}`,\"OFFCHAIN_FAULT\",{reason:\"404_MISSING_RESOURCE\",transaction:t,info:{url:o,errorMessage:l}}),i.push(l)}h(!1,`error encountered during CCIP fetch: ${i.map((t=>JSON.stringify(t))).join(\", \")}`,\"OFFCHAIN_FAULT\",{reason:\"500_SERVER_ERROR\",transaction:t,info:{urls:n,errorMessages:i}})}_wrapBlock(t,e){return new mr(function(t){const e=Ka(t);return e.transactions=t.transactions.map((t=>\"string\"==typeof t?t:Za(t))),e}(t),this)}_wrapLog(t,e){return new yr(function(t){return za(t)}(t),this)}_wrapTransactionReceipt(t,e){return new wr(function(t){return _a(t)}(t),this)}_wrapTransactionResponse(t,e){return new br(Za(t),this)}_detectNetwork(){h(!1,\"sub-classes must implement this\",\"UNSUPPORTED_OPERATION\",{operation:\"_detectNetwork\"})}async _perform(t){h(!1,`unsupported method: ${t.method}`,\"UNSUPPORTED_OPERATION\",{operation:t.method,info:t})}async getBlockNumber(){const t=L(await this.#Kt({method:\"getBlockNumber\"}),\"%response\");return this.#jt>=0&&(this.#jt=t),t}_getAddress(t){return xn(t,this)}_getBlockTag(t){if(null==t)return\"latest\";switch(t){case\"earliest\":return\"0x0\";case\"finalized\":case\"latest\":case\"pending\":case\"safe\":return t}return b(t)?b(t,32)?t:H(t):(\"bigint\"==typeof t&&(t=L(t,\"blockTag\")),\"number\"==typeof t?t>=0?H(t):this.#jt>=0?H(this.#jt+t):this.getBlockNumber().then((e=>H(e+t))):void f(!1,\"invalid blockTag\",\"blockTag\",t))}_getFilter(t){const e=(t.topics||[]).map((t=>null==t?null:Array.isArray(t)?mc(t.map((t=>t.toLowerCase()))):t.toLowerCase())),n=\"blockHash\"in t?t.blockHash:void 0,r=(t,r,s)=>{let i;switch(t.length){case 0:break;case 1:i=t[0];break;default:t.sort(),i=t}if(n&&(null!=r||null!=s))throw new Error(\"invalid filter\");const o={};return i&&(o.address=i),e.length&&(o.topics=e),r&&(o.fromBlock=r),s&&(o.toBlock=s),n&&(o.blockHash=n),o};let s,i,o=[];if(t.address)if(Array.isArray(t.address))for(const e of t.address)o.push(this._getAddress(e));else o.push(this._getAddress(t.address));return\"fromBlock\"in t&&(s=this._getBlockTag(t.fromBlock)),\"toBlock\"in t&&(i=this._getBlockTag(t.toBlock)),o.filter((t=>\"string\"!=typeof t)).length||null!=s&&\"string\"!=typeof s||null!=i&&\"string\"!=typeof i?Promise.all([Promise.all(o),s,i]).then((t=>r(t[0],t[1],t[2]))):r(o,s,i)}_getTransactionRequest(t){const e=gr(t),n=[];if([\"to\",\"from\"].forEach((t=>{if(null==e[t])return;const r=xn(e[t],this);dc(r)?n.push(async function(){e[t]=await r}()):e[t]=r})),null!=e.blockTag){const t=this._getBlockTag(e.blockTag);dc(t)?n.push(async function(){e.blockTag=await t}()):e.blockTag=t}return n.length?async function(){return await Promise.all(n),e}():e}async getNetwork(){if(null==this.#Gt){const t=(async()=>{try{const t=await this._detectNetwork();return this.emit(\"network\",t,null),t}catch(e){throw this.#Gt===t&&(this.#Gt=null),e}})();return this.#Gt=t,(await t).clone()}const t=this.#Gt,[e,n]=await Promise.all([t,this._detectNetwork()]);return e.chainId!==n.chainId&&(this.#Ht?(this.emit(\"network\",n,e),this.#Gt===t&&(this.#Gt=Promise.resolve(n))):h(!1,`network changed: ${e.chainId} => ${n.chainId} `,\"NETWORK_ERROR\",{event:\"changed\"})),e.clone()}async getFeeData(){const t=await this.getNetwork(),e=async()=>{const{_block:e,gasPrice:n,priorityFee:r}=await i({_block:this.#qt(\"latest\",!1),gasPrice:(async()=>{try{return S(await this.#Kt({method:\"getGasPrice\"}),\"%response\")}catch(t){}return null})(),priorityFee:(async()=>{try{return S(await this.#Kt({method:\"getPriorityFee\"}),\"%response\")}catch(t){}return null})()});let s=null,o=null;const a=this._wrapBlock(e,t);return a&&a.baseFeePerGas&&(o=null!=r?r:BigInt(\"1000000000\"),s=a.baseFeePerGas*fc+o),new pr(n,s,o)},n=t.getPlugin(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\");if(n){const t=new tr(n.url),r=await n.processFunc(e,this,t);return new pr(r.gasPrice,r.maxFeePerGas,r.maxPriorityFeePerGas)}return await e()}async estimateGas(t){let e=this._getTransactionRequest(t);return dc(e)&&(e=await e),S(await this.#Kt({method:\"estimateGas\",transaction:e}),\"%response\")}async#_t(t,e,n){h(n<10,\"CCIP read exceeded maximum redirections\",\"OFFCHAIN_FAULT\",{reason:\"TOO_MANY_REDIRECTS\",transaction:Object.assign({},t,{blockTag:e,enableCcipRead:!0})});const r=gr(t);try{return E(await this._perform({method:\"call\",transaction:r,blockTag:e}))}catch(t){if(!this.disableCcipRead&&l(t)&&t.data&&n>=0&&\"latest\"===e&&null!=r.to&&\"0x556f1830\"===x(t.data,0,4)){const s=t.data,i=await xn(r.to,this);let o;try{o=function(t){const e={sender:\"\",urls:[],calldata:\"\",selector:\"\",extraData:\"\",errorArgs:[]};h(P(t)>=160,\"insufficient OffchainLookup data\",\"OFFCHAIN_FAULT\",{reason:\"insufficient OffchainLookup data\"});const n=x(t,0,32);h(x(n,0,12)===x(Bc,0,12),\"corrupt OffchainLookup sender\",\"OFFCHAIN_FAULT\",{reason:\"corrupt OffchainLookup sender\"}),e.sender=x(n,12);try{const n=[],r=L(x(t,32,64)),s=L(x(t,r,r+32)),i=x(t,r+32);for(let t=0;t<s;t++){const e=vc(i,32*t);if(null==e)throw new Error(\"abort\");n.push(e)}e.urls=n}catch(t){h(!1,\"corrupt OffchainLookup urls\",\"OFFCHAIN_FAULT\",{reason:\"corrupt OffchainLookup urls\"})}try{const n=Ec(t,64);if(null==n)throw new Error(\"abort\");e.calldata=n}catch(t){h(!1,\"corrupt OffchainLookup calldata\",\"OFFCHAIN_FAULT\",{reason:\"corrupt OffchainLookup calldata\"})}h(x(t,100,128)===x(Bc,0,28),\"corrupt OffchainLookup callbaackSelector\",\"OFFCHAIN_FAULT\",{reason:\"corrupt OffchainLookup callbaackSelector\"}),e.selector=x(t,96,100);try{const n=Ec(t,128);if(null==n)throw new Error(\"abort\");e.extraData=n}catch(t){h(!1,\"corrupt OffchainLookup extraData\",\"OFFCHAIN_FAULT\",{reason:\"corrupt OffchainLookup extraData\"})}return e.errorArgs=\"sender,urls,calldata,selector,extraData\".split(/,/).map((t=>e[t])),e}(x(t.data,4))}catch(t){h(!1,t.message,\"OFFCHAIN_FAULT\",{reason:\"BAD_DATA\",transaction:r,info:{data:s}})}h(o.sender.toLowerCase()===i.toLowerCase(),\"CCIP Read sender mismatch\",\"CALL_EXCEPTION\",{action:\"call\",data:s,reason:\"OffchainLookup\",transaction:r,invocation:null,revert:{signature:\"OffchainLookup(address,string[],bytes,bytes4,bytes)\",name:\"OffchainLookup\",args:o.errorArgs}});const a=await this.ccipReadFetch(r,o.calldata,o.urls);h(null!=a,\"CCIP Read failed to fetch data\",\"OFFCHAIN_FAULT\",{reason:\"FETCH_FAILED\",transaction:r,info:{data:t.data,errorArgs:o.errorArgs}});const c={to:i,data:k([o.selector,Nc([a,o.extraData])])};this.emit(\"debug\",{action:\"sendCcipReadCall\",transaction:c});try{const t=await this.#_t(c,e,n+1);return this.emit(\"debug\",{action:\"receiveCcipReadCallResult\",transaction:Object.assign({},c),result:t}),t}catch(t){throw this.emit(\"debug\",{action:\"receiveCcipReadCallError\",transaction:Object.assign({},c),error:t}),t}}throw t}}async#Zt(t){const{value:e}=await i({network:this.getNetwork(),value:t});return e}async call(t){const{tx:e,blockTag:n}=await i({tx:this._getTransactionRequest(t),blockTag:this._getBlockTag(t.blockTag)});return await this.#Zt(this.#_t(e,n,t.enableCcipRead?0:-1))}async#Wt(t,e,n){let r=this._getAddress(e),s=this._getBlockTag(n);return\"string\"==typeof r&&\"string\"==typeof s||([r,s]=await Promise.all([r,s])),await this.#Zt(this.#Kt(Object.assign(t,{address:r,blockTag:s})))}async getBalance(t,e){return S(await this.#Wt({method:\"getBalance\"},t,e),\"%response\")}async getTransactionCount(t,e){return L(await this.#Wt({method:\"getTransactionCount\"},t,e),\"%response\")}async getCode(t,e){return E(await this.#Wt({method:\"getCode\"},t,e))}async getStorage(t,e,n){const r=S(e,\"position\");return E(await this.#Wt({method:\"getStorage\",position:r},t,n))}async broadcastTransaction(t){const{blockNumber:e,hash:n,network:r}=await i({blockNumber:this.getBlockNumber(),hash:this._perform({method:\"broadcastTransaction\",signedTransaction:t}),network:this.getNetwork()}),s=Ra.from(t);if(s.hash!==n)throw new Error(\"@TODO: the returned hash did not match\");return this._wrapTransactionResponse(s,r).replaceableTransaction(e)}async#qt(t,e){if(b(t,32))return await this.#Kt({method:\"getBlock\",blockHash:t,includeTransactions:e});let n=this._getBlockTag(t);return\"string\"!=typeof n&&(n=await n),await this.#Kt({method:\"getBlock\",blockTag:n,includeTransactions:e})}async getBlock(t,e){const{network:n,params:r}=await i({network:this.getNetwork(),params:this.#qt(t,!!e)});return null==r?null:this._wrapBlock(r,n)}async getTransaction(t){const{network:e,params:n}=await i({network:this.getNetwork(),params:this.#Kt({method:\"getTransaction\",hash:t})});return null==n?null:this._wrapTransactionResponse(n,e)}async getTransactionReceipt(t){const{network:e,params:n}=await i({network:this.getNetwork(),params:this.#Kt({method:\"getTransactionReceipt\",hash:t})});if(null==n)return null;if(null==n.gasPrice&&null==n.effectiveGasPrice){const e=await this.#Kt({method:\"getTransaction\",hash:t});if(null==e)throw new Error(\"report this; could not find tx or effectiveGasPrice\");n.effectiveGasPrice=e.gasPrice}return this._wrapTransactionReceipt(n,e)}async getTransactionResult(t){const{result:e}=await i({network:this.getNetwork(),result:this.#Kt({method:\"getTransactionResult\",hash:t})});return null==e?null:E(e)}async getLogs(t){let e=this._getFilter(t);dc(e)&&(e=await e);const{network:n,params:r}=await i({network:this.getNetwork(),params:this.#Kt({method:\"getLogs\",filter:e})});return r.map((t=>this._wrapLog(t,n)))}_getProvider(t){h(!1,\"provider cannot connect to target network\",\"UNSUPPORTED_OPERATION\",{operation:\"_getProvider()\"})}async getResolver(t){return await Ma.fromName(this,t)}async getAvatar(t){const e=await this.getResolver(t);return e?await e.getAvatar():null}async resolveName(t){const e=await this.getResolver(t);return e?await e.getAddress():null}async lookupAddress(t){const e=wi((t=Wt(t)).substring(2).toLowerCase()+\".addr.reverse\");try{const n=await Ma.getEnsAddress(this),r=new Yr(n,[\"function resolver(bytes32) view returns (address)\"],this),s=await r.resolver(e);if(null==s||s===rr)return null;const i=new Yr(s,[\"function name(bytes32) view returns (string)\"],this),o=await i.name(e);return await this.resolveName(o)!==t?null:o}catch(t){if(c(t,\"BAD_DATA\")&&\"0x\"===t.value)return null;if(c(t,\"CALL_EXCEPTION\"))return null;throw t}return null}async waitForTransaction(t,e,n){const r=null!=e?e:1;return 0===r?this.getTransactionReceipt(t):new Promise((async(e,s)=>{let i=null;const o=async n=>{try{const s=await this.getTransactionReceipt(t);if(null!=s&&n-s.blockNumber+1>=r)return e(s),void(i&&(clearTimeout(i),i=null))}catch(t){console.log(\"EEE\",t)}this.once(\"block\",o)};null!=n&&(i=setTimeout((()=>{null!=i&&(i=null,this.off(\"block\",o),s(u(\"timeout\",\"TIMEOUT\",{reason:\"timeout\"})))}),n)),o(await this.getBlockNumber())}))}async waitForBlock(t){h(!1,\"not implemented yet\",\"NOT_IMPLEMENTED\",{operation:\"waitForBlock\"})}_clearTimeout(t){const e=this.#Jt.get(t);e&&(e.timer&&clearTimeout(e.timer),this.#Jt.delete(t))}_setTimeout(t,e){null==e&&(e=0);const n=this.#Vt++,r=()=>{this.#Jt.delete(n),t()};if(this.paused)this.#Jt.set(n,{timer:null,func:r,time:e});else{const t=setTimeout(r,e);this.#Jt.set(n,{timer:t,func:r,time:wc()})}return n}_forEachSubscriber(t){for(const e of this.#Dt.values())t(e.subscriber)}_getSubscriber(t){switch(t.type){case\"debug\":case\"error\":case\"network\":return new gc(t.type);case\"block\":{const t=new oc(this);return t.pollingInterval=this.pollingInterval,t}case\"safe\":case\"finalized\":return new cc(this,t.type);case\"event\":return new hc(this,t.filter);case\"transaction\":return new uc(this,t.hash);case\"orphan\":return new lc(this,t.filter)}throw new Error(`unsupported event: ${t.type}`)}_recoverSubscriber(t,e){for(const n of this.#Dt.values())if(n.subscriber===t){n.started&&n.subscriber.stop(),n.subscriber=e,n.started&&e.start(),null!=this.#Lt&&e.pause(this.#Lt);break}}async#Yt(t,e){let n=await yc(t,this);return\"event\"===n.type&&e&&e.length>0&&!0===e[0].removed&&(n=await yc({orphan:\"drop-log\",log:e[0]},this)),this.#Dt.get(n.tag)||null}async#Xt(t){const e=await yc(t,this),n=e.tag;let r=this.#Dt.get(n);if(!r){r={subscriber:this._getSubscriber(e),tag:n,addressableMap:new WeakMap,nameMap:new Map,started:!1,listeners:[]},this.#Dt.set(n,r)}return r}async on(t,e){const n=await this.#Xt(t);return n.listeners.push({listener:e,once:!1}),n.started||(n.subscriber.start(),n.started=!0,null!=this.#Lt&&n.subscriber.pause(this.#Lt)),this}async once(t,e){const n=await this.#Xt(t);return n.listeners.push({listener:e,once:!0}),n.started||(n.subscriber.start(),n.started=!0,null!=this.#Lt&&n.subscriber.pause(this.#Lt)),this}async emit(t,...e){const n=await this.#Yt(t,e);if(!n||0===n.listeners.length)return!1;const r=n.listeners.length;return n.listeners=n.listeners.filter((({listener:n,once:r})=>{const s=new Er(this,r?null:n,t);try{n.call(this,...e,s)}catch(t){}return!r})),0===n.listeners.length&&(n.started&&n.subscriber.stop(),this.#Dt.delete(n.tag)),r>0}async listenerCount(t){if(t){const e=await this.#Yt(t);return e?e.listeners.length:0}let e=0;for(const{listeners:t}of this.#Dt.values())e+=t.length;return e}async listeners(t){if(t){const e=await this.#Yt(t);return e?e.listeners.map((({listener:t})=>t)):[]}let e=[];for(const{listeners:t}of this.#Dt.values())e=e.concat(t.map((({listener:t})=>t)));return e}async off(t,e){const n=await this.#Yt(t);if(!n)return this;if(e){const t=n.listeners.map((({listener:t})=>t)).indexOf(e);t>=0&&n.listeners.splice(t,1)}return e&&0!==n.listeners.length||(n.started&&n.subscriber.stop(),this.#Dt.delete(n.tag)),this}async removeAllListeners(t){if(t){const{tag:e,started:n,subscriber:r}=await this.#Xt(t);n&&r.stop(),this.#Dt.delete(e)}else for(const[t,{started:e,subscriber:n}]of this.#Dt)e&&n.stop(),this.#Dt.delete(t);return this}async addListener(t,e){return await this.on(t,e)}async removeListener(t,e){return this.off(t,e)}get destroyed(){return this.#Mt}destroy(){this.removeAllListeners();for(const t of this.#Jt.keys())this._clearTimeout(t);this.#Mt=!0}get paused(){return null!=this.#Lt}set paused(t){!!t!==this.paused&&(this.paused?this.resume():this.pause(!1))}pause(t){if(this.#jt=-1,null!=this.#Lt){if(this.#Lt==!!t)return;h(!1,\"cannot change pause type; resume first\",\"UNSUPPORTED_OPERATION\",{operation:\"pause\"})}this._forEachSubscriber((e=>e.pause(t))),this.#Lt=!!t;for(const t of this.#Jt.values())t.timer&&clearTimeout(t.timer),t.time=wc()-t.time}resume(){if(null!=this.#Lt){this._forEachSubscriber((t=>t.resume())),this.#Lt=null;for(const t of this.#Jt.values()){let e=t.time;e<0&&(e=0),t.time=wc(),setTimeout(t.func,e)}}}}function vc(t,e){try{const n=Ec(t,e);if(n)return ve(n)}catch(t){}return null}function Ec(t,e){if(\"0x\"===t)return null;try{const n=L(x(t,e,e+32)),r=L(x(t,n,n+32));return x(t,n+32,n+32+r)}catch(t){}return null}function kc(t){const e=G(t);if(e.length>32)throw new Error(\"internal; should not happen\");const n=new Uint8Array(32);return n.set(e,32-e.length),n}function Pc(t){if(t.length%32==0)return t;const e=new Uint8Array(32*Math.ceil(t.length/32));return e.set(t),e}const xc=new Uint8Array([]);function Nc(t){const e=[];let n=0;for(let r=0;r<t.length;r++)e.push(xc),n+=32;for(let r=0;r<t.length;r++){const s=y(t[r]);e[r]=kc(n),e.push(kc(s.length)),e.push(Pc(s)),n+=32+32*Math.ceil(s.length/32)}return k(e)}const Bc=\"******************************************000000000000000000000000\";function Ic(t,e){if(t.provider)return t.provider;h(!1,\"missing provider\",\"UNSUPPORTED_OPERATION\",{operation:e})}async function Cc(t,e){let n=gr(e);if(null!=n.to&&(n.to=xn(n.to,t)),null!=n.from){const e=n.from;n.from=Promise.all([t.getAddress(),xn(e,t)]).then((([t,e])=>(f(t.toLowerCase()===e.toLowerCase(),\"transaction from mismatch\",\"tx.from\",e),t)))}else n.from=t.getAddress();return await i(n)}class Oc{provider;constructor(t){o(this,{provider:t||null})}async getNonce(t){return Ic(this,\"getTransactionCount\").getTransactionCount(await this.getAddress(),t)}async populateCall(t){return await Cc(this,t)}async populateTransaction(t){const e=Ic(this,\"populateTransaction\"),n=await Cc(this,t);null==n.nonce&&(n.nonce=await this.getNonce(\"pending\")),null==n.gasLimit&&(n.gasLimit=await this.estimateGas(n));const r=await this.provider.getNetwork();if(null!=n.chainId){f(S(n.chainId)===r.chainId,\"transaction chainId mismatch\",\"tx.chainId\",t.chainId)}else n.chainId=r.chainId;const s=null!=n.maxFeePerGas||null!=n.maxPriorityFeePerGas;if(null==n.gasPrice||2!==n.type&&!s?0!==n.type&&1!==n.type||!s||f(!1,\"pre-eip-1559 transaction do not support maxFeePerGas/maxPriorityFeePerGas\",\"tx\",t):f(!1,\"eip-1559 transaction do not support gasPrice\",\"tx\",t),2!==n.type&&null!=n.type||null==n.maxFeePerGas||null==n.maxPriorityFeePerGas)if(0===n.type||1===n.type){const t=await e.getFeeData();h(null!=t.gasPrice,\"network does not support gasPrice\",\"UNSUPPORTED_OPERATION\",{operation:\"getGasPrice\"}),null==n.gasPrice&&(n.gasPrice=t.gasPrice)}else{const t=await e.getFeeData();if(null==n.type)if(null!=t.maxFeePerGas&&null!=t.maxPriorityFeePerGas)if(n.type=2,null!=n.gasPrice){const t=n.gasPrice;delete n.gasPrice,n.maxFeePerGas=t,n.maxPriorityFeePerGas=t}else null==n.maxFeePerGas&&(n.maxFeePerGas=t.maxFeePerGas),null==n.maxPriorityFeePerGas&&(n.maxPriorityFeePerGas=t.maxPriorityFeePerGas);else null!=t.gasPrice?(h(!s,\"network does not support EIP-1559\",\"UNSUPPORTED_OPERATION\",{operation:\"populateTransaction\"}),null==n.gasPrice&&(n.gasPrice=t.gasPrice),n.type=0):h(!1,\"failed to get consistent fee data\",\"UNSUPPORTED_OPERATION\",{operation:\"signer.getFeeData\"});else 2!==n.type&&3!==n.type||(null==n.maxFeePerGas&&(n.maxFeePerGas=t.maxFeePerGas),null==n.maxPriorityFeePerGas&&(n.maxPriorityFeePerGas=t.maxPriorityFeePerGas))}else n.type=2;return await i(n)}async estimateGas(t){return Ic(this,\"estimateGas\").estimateGas(await this.populateCall(t))}async call(t){return Ic(this,\"call\").call(await this.populateCall(t))}async resolveName(t){const e=Ic(this,\"resolveName\");return await e.resolveName(t)}async sendTransaction(t){const e=Ic(this,\"sendTransaction\"),n=await this.populateTransaction(t);delete n.from;const r=Ra.from(n);return await e.broadcastTransaction(await this.signTransaction(r))}}class Rc{#Bt;#$t;#It;#Tt;#te;#ee;constructor(t){this.#Bt=t,this.#$t=null,this.#It=this.#Rt.bind(this),this.#Tt=!1,this.#te=null,this.#ee=!1}_subscribe(t){throw new Error(\"subclasses must override this\")}_emitResults(t,e){throw new Error(\"subclasses must override this\")}_recover(t){throw new Error(\"subclasses must override this\")}async#Rt(t){try{null==this.#$t&&(this.#$t=this._subscribe(this.#Bt));let t=null;try{t=await this.#$t}catch(t){if(!c(t,\"UNSUPPORTED_OPERATION\")||\"eth_newFilter\"!==t.operation)throw t}if(null==t)return this.#$t=null,void this.#Bt._recoverSubscriber(this,this._recover(this.#Bt));const e=await this.#Bt.getNetwork();if(this.#te||(this.#te=e),this.#te.chainId!==e.chainId)throw new Error(\"chaid changed\");if(this.#ee)return;const n=await this.#Bt.send(\"eth_getFilterChanges\",[t]);await this._emitResults(this.#Bt,n)}catch(t){console.log(\"@TODO\",t)}this.#Bt.once(\"block\",this.#It)}#ne(){const t=this.#$t;t&&(this.#$t=null,t.then((t=>{this.#Bt.destroyed||this.#Bt.send(\"eth_uninstallFilter\",[t])})))}start(){this.#Tt||(this.#Tt=!0,this.#Rt(-2))}stop(){this.#Tt&&(this.#Tt=!1,this.#ee=!0,this.#ne(),this.#Bt.off(\"block\",this.#It))}pause(t){t&&this.#ne(),this.#Bt.off(\"block\",this.#It)}resume(){this.start()}}class Tc extends Rc{#re;constructor(t,e){var n;super(t),this.#re=(n=e,JSON.parse(JSON.stringify(n)))}_recover(t){return new hc(t,this.#re)}async _subscribe(t){return await t.send(\"eth_newFilter\",[this.#re])}async _emitResults(t,e){for(const n of e)t.emit(this.#re,t._wrapLog(n,t._network))}}class Sc extends Rc{async _subscribe(t){return await t.send(\"eth_newPendingTransactionFilter\",[])}async _emitResults(t,e){for(const n of e)t.emit(\"pending\",n)}}const Fc=\"bigint,boolean,function,number,string,symbol\".split(/,/g);function Uc(t){if(null==t||Fc.indexOf(typeof t)>=0)return t;if(\"function\"==typeof t.getAddress)return t;if(Array.isArray(t))return t.map(Uc);if(\"object\"==typeof t)return Object.keys(t).reduce(((e,n)=>(e[n]=t[n],e)),{});throw new Error(`should not happen: ${t} (${typeof t})`)}function Dc(t){return new Promise((e=>{setTimeout(e,t)}))}function Lc(t){return t?t.toLowerCase():t}function Mc(t){return t&&\"number\"==typeof t.pollingInterval}const Gc={polling:!1,staticNetwork:null,batchStallTime:10,batchMaxSize:1<<20,batchMaxCount:100,cacheTimeout:250,pollingInterval:4e3};class Hc extends Oc{address;constructor(t,e){super(t),o(this,{address:e=Wt(e)})}connect(t){h(!1,\"cannot reconnect JsonRpcSigner\",\"UNSUPPORTED_OPERATION\",{operation:\"signer.connect\"})}async getAddress(){return this.address}async populateTransaction(t){return await this.populateCall(t)}async sendUncheckedTransaction(t){const e=Uc(t),n=[];if(e.from){const r=e.from;n.push((async()=>{const n=await xn(r,this.provider);f(null!=n&&n.toLowerCase()===this.address.toLowerCase(),\"from address mismatch\",\"transaction\",t),e.from=n})())}else e.from=this.address;if(null==e.gasLimit&&n.push((async()=>{e.gasLimit=await this.provider.estimateGas({...e,from:this.address})})()),null!=e.to){const t=e.to;n.push((async()=>{e.to=await xn(t,this.provider)})())}n.length&&await Promise.all(n);const r=this.provider.getRpcTransaction(e);return this.provider.send(\"eth_sendTransaction\",[r])}async sendTransaction(t){const e=await this.provider.getBlockNumber(),n=await this.sendUncheckedTransaction(t);return await new Promise(((t,r)=>{const s=[1e3,100];let i=0;const o=async()=>{try{const r=await this.provider.getTransaction(n);if(null!=r)return void t(r.replaceableTransaction(e))}catch(t){if(c(t,\"CANCELLED\")||c(t,\"BAD_DATA\")||c(t,\"NETWORK_ERROR\"))return null==t.info&&(t.info={}),t.info.sendTransactionHash=n,void r(t);if(c(t,\"INVALID_ARGUMENT\")&&(i++,null==t.info&&(t.info={}),t.info.sendTransactionHash=n,i>10))return void r(t);this.provider.emit(\"error\",u(\"failed to fetch transation after sending (will try again)\",\"UNKNOWN_ERROR\",{error:t}))}this.provider._setTimeout((()=>{o()}),s.pop()||4e3)};o()}))}async signTransaction(t){const e=Uc(t);if(e.from){const n=await xn(e.from,this.provider);f(null!=n&&n.toLowerCase()===this.address.toLowerCase(),\"from address mismatch\",\"transaction\",t),e.from=n}else e.from=this.address;const n=this.provider.getRpcTransaction(e);return await this.provider.send(\"eth_signTransaction\",[n])}async signMessage(t){const e=\"string\"==typeof t?Ae(t):t;return await this.provider.send(\"personal_sign\",[E(e),this.address.toLowerCase()])}async signTypedData(t,e,n){const r=Uc(n),s=await Hn.resolveNames(t,e,r,(async t=>{const e=await xn(t);return f(null!=e,\"TypedData does not support null address\",\"value\",t),e}));return await this.provider.send(\"eth_signTypedData_v4\",[this.address.toLowerCase(),JSON.stringify(Hn.getPayload(s.domain,e,s.value))])}async unlock(t){return this.provider.send(\"personal_unlockAccount\",[this.address.toLowerCase(),t,null])}async _legacySignMessage(t){const e=\"string\"==typeof t?Ae(t):t;return await this.provider.send(\"eth_sign\",[this.address.toLowerCase(),E(e)])}}class Qc extends Ac{#u;#se;#ie;#oe;#ae;#te;#ce;#le(){if(this.#oe)return;const t=1===this._getOption(\"batchMaxCount\")?0:this._getOption(\"batchStallTime\");this.#oe=setTimeout((()=>{this.#oe=null;const t=this.#ie;for(this.#ie=[];t.length;){const e=[t.shift()];for(;t.length&&e.length!==this.#u.batchMaxCount;){e.push(t.shift());if(JSON.stringify(e.map((t=>t.payload))).length>this.#u.batchMaxSize){t.unshift(e.pop());break}}(async()=>{const t=1===e.length?e[0].payload:e.map((t=>t.payload));this.emit(\"debug\",{action:\"sendRpcPayload\",payload:t});try{const n=await this._send(t);this.emit(\"debug\",{action:\"receiveRpcResult\",result:n});for(const{resolve:t,reject:r,payload:s}of e){if(this.destroyed){r(u(\"provider destroyed; cancelled request\",\"UNSUPPORTED_OPERATION\",{operation:s.method}));continue}const e=n.filter((t=>t.id===s.id))[0];if(null!=e)\"error\"in e?r(this.getRpcError(s,e)):t(e.result);else{const t=u(\"missing response for request\",\"BAD_DATA\",{value:n,info:{payload:s}});this.emit(\"error\",t),r(t)}}}catch(t){this.emit(\"debug\",{action:\"receiveRpcError\",error:t});for(const{reject:n}of e)n(t)}})()}}),t)}constructor(t,e){super(t,e),this.#se=1,this.#u=Object.assign({},Gc,e||{}),this.#ie=[],this.#oe=null,this.#te=null,this.#ce=null;{let t=null;const e=new Promise((e=>{t=e}));this.#ae={promise:e,resolve:t}}const n=this._getOption(\"staticNetwork\");\"boolean\"==typeof n?(f(!n||\"any\"!==t,\"staticNetwork cannot be used on special network 'any'\",\"options\",e),n&&null!=t&&(this.#te=ec.from(t))):n&&(f(null==t||n.matches(t),\"staticNetwork MUST match network object\",\"options\",e),this.#te=n)}_getOption(t){return this.#u[t]}get _network(){return h(this.#te,\"network is not available yet\",\"NETWORK_ERROR\"),this.#te}async _perform(t){if(\"call\"===t.method||\"estimateGas\"===t.method){let e=t.transaction;if(e&&null!=e.type&&S(e.type)&&null==e.maxFeePerGas&&null==e.maxPriorityFeePerGas){const n=await this.getFeeData();null==n.maxFeePerGas&&null==n.maxPriorityFeePerGas&&(t=Object.assign({},t,{transaction:Object.assign({},e,{type:void 0})}))}}const e=this.getRpcRequest(t);return null!=e?await this.send(e.method,e.args):super._perform(t)}async _detectNetwork(){const t=this._getOption(\"staticNetwork\");if(t){if(!0!==t)return t;if(this.#te)return this.#te}return this.#ce?await this.#ce:this.ready?(this.#ce=(async()=>{try{const t=ec.from(S(await this.send(\"eth_chainId\",[])));return this.#ce=null,t}catch(t){throw this.#ce=null,t}})(),await this.#ce):(this.#ce=(async()=>{const t={id:this.#se++,method:\"eth_chainId\",params:[],jsonrpc:\"2.0\"};let e;this.emit(\"debug\",{action:\"sendRpcPayload\",payload:t});try{e=(await this._send(t))[0],this.#ce=null}catch(t){throw this.#ce=null,this.emit(\"debug\",{action:\"receiveRpcError\",error:t}),t}if(this.emit(\"debug\",{action:\"receiveRpcResult\",result:e}),\"result\"in e)return ec.from(S(e.result));throw this.getRpcError(t,e)})(),await this.#ce)}_start(){null!=this.#ae&&null!=this.#ae.resolve&&(this.#ae.resolve(),this.#ae=null,(async()=>{for(;null==this.#te&&!this.destroyed;)try{this.#te=await this._detectNetwork()}catch(t){if(this.destroyed)break;console.log(\"JsonRpcProvider failed to detect network and cannot start up; retry in 1s (perhaps the URL is wrong or the node is not started)\"),this.emit(\"error\",u(\"failed to bootstrap network detection\",\"NETWORK_ERROR\",{event:\"initial-network-discovery\",info:{error:t}})),await Dc(1e3)}this.#le()})())}async _waitUntilReady(){if(null!=this.#ae)return await this.#ae.promise}_getSubscriber(t){return\"pending\"===t.type?new Sc(this):\"event\"===t.type?this._getOption(\"polling\")?new hc(this,t.filter):new Tc(this,t.filter):\"orphan\"===t.type&&\"drop-log\"===t.filter.orphan?new gc(\"orphan\"):super._getSubscriber(t)}get ready(){return null==this.#ae}getRpcTransaction(t){const e={};return[\"chainId\",\"gasLimit\",\"gasPrice\",\"type\",\"maxFeePerGas\",\"maxPriorityFeePerGas\",\"nonce\",\"value\"].forEach((n=>{if(null==t[n])return;let r=n;\"gasLimit\"===n&&(r=\"gas\"),e[r]=H(S(t[n],`tx.${n}`))})),[\"from\",\"to\",\"data\"].forEach((n=>{null!=t[n]&&(e[n]=E(t[n]))})),t.accessList&&(e.accessList=jn(t.accessList)),t.blobVersionedHashes&&(e.blobVersionedHashes=t.blobVersionedHashes.map((t=>t.toLowerCase()))),e}getRpcRequest(t){switch(t.method){case\"chainId\":return{method:\"eth_chainId\",args:[]};case\"getBlockNumber\":return{method:\"eth_blockNumber\",args:[]};case\"getGasPrice\":return{method:\"eth_gasPrice\",args:[]};case\"getPriorityFee\":return{method:\"eth_maxPriorityFeePerGas\",args:[]};case\"getBalance\":return{method:\"eth_getBalance\",args:[Lc(t.address),t.blockTag]};case\"getTransactionCount\":return{method:\"eth_getTransactionCount\",args:[Lc(t.address),t.blockTag]};case\"getCode\":return{method:\"eth_getCode\",args:[Lc(t.address),t.blockTag]};case\"getStorage\":return{method:\"eth_getStorageAt\",args:[Lc(t.address),\"0x\"+t.position.toString(16),t.blockTag]};case\"broadcastTransaction\":return{method:\"eth_sendRawTransaction\",args:[t.signedTransaction]};case\"getBlock\":if(\"blockTag\"in t)return{method:\"eth_getBlockByNumber\",args:[t.blockTag,!!t.includeTransactions]};if(\"blockHash\"in t)return{method:\"eth_getBlockByHash\",args:[t.blockHash,!!t.includeTransactions]};break;case\"getTransaction\":return{method:\"eth_getTransactionByHash\",args:[t.hash]};case\"getTransactionReceipt\":return{method:\"eth_getTransactionReceipt\",args:[t.hash]};case\"call\":return{method:\"eth_call\",args:[this.getRpcTransaction(t.transaction),t.blockTag]};case\"estimateGas\":return{method:\"eth_estimateGas\",args:[this.getRpcTransaction(t.transaction)]};case\"getLogs\":return t.filter&&null!=t.filter.address&&(Array.isArray(t.filter.address)?t.filter.address=t.filter.address.map(Lc):t.filter.address=Lc(t.filter.address)),{method:\"eth_getLogs\",args:[t.filter]}}return null}getRpcError(t,e){const{method:n}=t,{error:r}=e;if(\"eth_estimateGas\"===n&&r.message){const e=r.message;if(!e.match(/revert/i)&&e.match(/insufficient funds/i))return u(\"insufficient funds\",\"INSUFFICIENT_FUNDS\",{transaction:t.params[0],info:{payload:t,error:r}})}if(\"eth_call\"===n||\"eth_estimateGas\"===n){const e=Jc(r),s=En.getBuiltinCallException(\"eth_call\"===n?\"call\":\"estimateGas\",t.params[0],e?e.data:null);return s.info={error:r,payload:t},s}const s=JSON.stringify(function(t){const e=[];return zc(t,e),e}(r));if(\"string\"==typeof r.message&&r.message.match(/user denied|ethers-user-denied/i)){return u(\"user rejected action\",\"ACTION_REJECTED\",{action:{eth_sign:\"signMessage\",personal_sign:\"signMessage\",eth_signTypedData_v4:\"signTypedData\",eth_signTransaction:\"signTransaction\",eth_sendTransaction:\"sendTransaction\",eth_requestAccounts:\"requestAccess\",wallet_requestAccounts:\"requestAccess\"}[n]||\"unknown\",reason:\"rejected\",info:{payload:t,error:r}})}if(\"eth_sendRawTransaction\"===n||\"eth_sendTransaction\"===n){const e=t.params[0];if(s.match(/insufficient funds|base fee exceeds gas limit/i))return u(\"insufficient funds for intrinsic transaction cost\",\"INSUFFICIENT_FUNDS\",{transaction:e,info:{error:r}});if(s.match(/nonce/i)&&s.match(/too low/i))return u(\"nonce has already been used\",\"NONCE_EXPIRED\",{transaction:e,info:{error:r}});if(s.match(/replacement transaction/i)&&s.match(/underpriced/i))return u(\"replacement fee too low\",\"REPLACEMENT_UNDERPRICED\",{transaction:e,info:{error:r}});if(s.match(/only replay-protected/i))return u(\"legacy pre-eip-155 transactions not supported\",\"UNSUPPORTED_OPERATION\",{operation:n,info:{transaction:e,info:{error:r}}})}let i=!!s.match(/the method .* does not exist/i);return i||r&&r.details&&r.details.startsWith(\"Unauthorized method:\")&&(i=!0),i?u(\"unsupported operation\",\"UNSUPPORTED_OPERATION\",{operation:t.method,info:{error:r,payload:t}}):u(\"could not coalesce error\",\"UNKNOWN_ERROR\",{error:r,payload:t})}send(t,e){if(this.destroyed)return Promise.reject(u(\"provider destroyed; cancelled request\",\"UNSUPPORTED_OPERATION\",{operation:t}));const n=this.#se++,r=new Promise(((r,s)=>{this.#ie.push({resolve:r,reject:s,payload:{method:t,params:e,id:n,jsonrpc:\"2.0\"}})}));return this.#le(),r}async getSigner(t){null==t&&(t=0);const e=this.send(\"eth_accounts\",[]);if(\"number\"==typeof t){const n=await e;if(t>=n.length)throw new Error(\"no such account\");return new Hc(this,n[t])}const{accounts:n}=await i({network:this.getNetwork(),accounts:e});t=Wt(t);for(const e of n)if(Wt(e)===t)return new Hc(this,t);throw new Error(\"invalid account\")}async listAccounts(){return(await this.send(\"eth_accounts\",[])).map((t=>new Hc(this,t)))}destroy(){this.#oe&&(clearTimeout(this.#oe),this.#oe=null);for(const{payload:t,reject:e}of this.#ie)e(u(\"provider destroyed; cancelled request\",\"UNSUPPORTED_OPERATION\",{operation:t.method}));this.#ie=[],super.destroy()}}class jc extends Qc{#ue;constructor(t,e){super(t,e);let n=this._getOption(\"pollingInterval\");null==n&&(n=Gc.pollingInterval),this.#ue=n}_getSubscriber(t){const e=super._getSubscriber(t);return Mc(e)&&(e.pollingInterval=this.#ue),e}get pollingInterval(){return this.#ue}set pollingInterval(t){if(!Number.isInteger(t)||t<0)throw new Error(\"invalid interval\");this.#ue=t,this._forEachSubscriber((t=>{Mc(t)&&(t.pollingInterval=this.#ue)}))}}class Vc extends jc{#he;constructor(t,e,n){null==t&&(t=\"http://localhost:8545\"),super(e,n),this.#he=\"string\"==typeof t?new tr(t):t.clone()}_getConnection(){return this.#he.clone()}async send(t,e){return await this._start(),await super.send(t,e)}async _send(t){const e=this._getConnection();e.body=JSON.stringify(t),e.setHeader(\"content-type\",\"application/json\");const n=await e.send();n.assertOk();let r=n.bodyJson;return Array.isArray(r)||(r=[r]),r}}function Jc(t){if(null==t)return null;if(\"string\"==typeof t.message&&t.message.match(/revert/i)&&b(t.data))return{message:t.message,data:t.data};if(\"object\"==typeof t){for(const e in t){const n=Jc(t[e]);if(n)return n}return null}if(\"string\"==typeof t)try{return Jc(JSON.parse(t))}catch(t){}return null}function zc(t,e){if(null!=t){if(\"string\"==typeof t.message&&e.push(t.message),\"object\"==typeof t)for(const n in t)zc(t[n],e);if(\"string\"==typeof t)try{return zc(JSON.parse(t),e)}catch(t){}}}class Kc extends jc{#G;constructor(t,e,n){const r=Object.assign({},null!=n?n:{},{batchMaxCount:1});f(t&&t.request,\"invalid EIP-1193 provider\",\"ethereum\",t),super(e,r),this.#G=async(e,n)=>{const r={method:e,params:n};this.emit(\"debug\",{action:\"sendEip1193Request\",payload:r});try{const e=await t.request(r);return this.emit(\"debug\",{action:\"receiveEip1193Result\",result:e}),e}catch(t){const e=new Error(t.message);throw e.code=t.code,e.data=t.data,e.payload=r,this.emit(\"debug\",{action:\"receiveEip1193Error\",error:e}),e}}}async send(t,e){return await this._start(),await super.send(t,e)}async _send(t){f(!Array.isArray(t),\"EIP-1193 does not support batch request\",\"payload\",t);try{const e=await this.#G(t.method,t.params||[]);return[{id:t.id,result:e}]}catch(e){return[{id:t.id,error:{code:e.code,data:e.data,message:e.message}}]}}getRpcError(t,e){switch((e=JSON.parse(JSON.stringify(e))).error.code||-1){case 4001:e.error.message=`ethers-user-denied: ${e.error.message}`;break;case 4200:e.error.message=`ethers-unsupported: ${e.error.message}`}return super.getRpcError(t,e)}async hasSigner(t){null==t&&(t=0);const e=await this.send(\"eth_accounts\",[]);return\"number\"==typeof t?e.length>t:(t=t.toLowerCase(),0!==e.filter((e=>e.toLowerCase()===t)).length)}async getSigner(t){if(null==t&&(t=0),!await this.hasSigner(t))try{await this.#G(\"eth_requestAccounts\",[])}catch(t){const e=t.payload;throw this.getRpcError(e,{id:e.id,error:t})}return await super.getSigner(t)}}const qc=new Set;const _c=function(){if(\"undefined\"!=typeof self)return self;if(\"undefined\"!=typeof window)return window;if(\"undefined\"!=typeof global)return global;throw new Error(\"unable to locate global object\")}().WebSocket;class Zc{#Bt;#X;get filter(){return JSON.parse(this.#X)}#fe;#de;#pe;constructor(t,e){this.#Bt=t,this.#X=JSON.stringify(e),this.#fe=null,this.#de=null,this.#pe=null}start(){this.#fe=this.#Bt.send(\"eth_subscribe\",this.filter).then((t=>(this.#Bt._register(t,this),t)))}stop(){this.#fe.then((t=>{this.#Bt.destroyed||this.#Bt.send(\"eth_unsubscribe\",[t])})),this.#fe=null}pause(t){h(t,\"preserve logs while paused not supported by SocketSubscriber yet\",\"UNSUPPORTED_OPERATION\",{operation:\"pause(false)\"}),this.#de=!!t}resume(){this.#de=null}_handleMessage(t){if(null!=this.#fe&&null===this.#de){let e=this.#pe;e=null==e?this._emit(this.#Bt,t):e.then((async()=>{await this._emit(this.#Bt,t)})),this.#pe=e.then((()=>{this.#pe===e&&(this.#pe=null)}))}}async _emit(t,e){throw new Error(\"sub-classes must implemente this; _emit\")}}class Wc extends Zc{constructor(t){super(t,[\"newHeads\"])}async _emit(t,e){t.emit(\"block\",parseInt(e.number))}}class Yc extends Zc{constructor(t){super(t,[\"newPendingTransactions\"])}async _emit(t,e){t.emit(\"pending\",e)}}class Xc extends Zc{#ge;get logFilter(){return JSON.parse(this.#ge)}constructor(t,e){super(t,[\"logs\",e]),this.#ge=JSON.stringify(e)}async _emit(t,e){t.emit(this.logFilter,t._wrapLog(e,t._network))}}class $c extends Qc{#me;#Dt;#ye;constructor(t,e){const n=Object.assign({},null!=e?e:{});f(null==n.batchMaxCount||1===n.batchMaxCount,\"sockets-based providers do not support batches\",\"options.batchMaxCount\",e),n.batchMaxCount=1,null==n.staticNetwork&&(n.staticNetwork=!0),super(t,n),this.#me=new Map,this.#Dt=new Map,this.#ye=new Map}_getSubscriber(t){switch(t.type){case\"close\":return new gc(\"close\");case\"block\":return new Wc(this);case\"pending\":return new Yc(this);case\"event\":return new Xc(this,t.filter);case\"orphan\":if(\"drop-log\"===t.filter.orphan)return new gc(\"drop-log\")}return super._getSubscriber(t)}_register(t,e){this.#Dt.set(t,e);const n=this.#ye.get(t);if(n){for(const t of n)e._handleMessage(t);this.#ye.delete(t)}}async _send(t){f(!Array.isArray(t),\"WebSocket does not support batch send\",\"payload\",t);const e=new Promise(((e,n)=>{this.#me.set(t.id,{payload:t,resolve:e,reject:n})}));return await this._waitUntilReady(),await this._write(JSON.stringify(t)),[await e]}async _processMessage(t){const e=JSON.parse(t);if(e&&\"object\"==typeof e&&\"id\"in e){const t=this.#me.get(e.id);if(null==t)return void this.emit(\"error\",u(\"received result for unknown id\",\"UNKNOWN_ERROR\",{reasonCode:\"UNKNOWN_ID\",result:e}));this.#me.delete(e.id),t.resolve(e)}else{if(!e||\"eth_subscription\"!==e.method)return void this.emit(\"error\",u(\"received unexpected message\",\"UNKNOWN_ERROR\",{reasonCode:\"UNEXPECTED_MESSAGE\",result:e}));{const t=e.params.subscription,n=this.#Dt.get(t);if(n)n._handleMessage(e.params.result);else{let n=this.#ye.get(t);null==n&&(n=[],this.#ye.set(t,n)),n.push(e.params.result)}}}}async _write(t){throw new Error(\"sub-classes must override this\")}}class tl extends $c{#he;#we;get websocket(){if(null==this.#we)throw new Error(\"websocket closed\");return this.#we}constructor(t,e,n){super(e,n),\"string\"==typeof t?(this.#he=()=>new _c(t),this.#we=this.#he()):\"function\"==typeof t?(this.#he=t,this.#we=t()):(this.#he=null,this.#we=t),this.websocket.onopen=async()=>{try{await this._start(),this.resume()}catch(t){console.log(\"failed to start WebsocketProvider\",t)}},this.websocket.onmessage=t=>{this._processMessage(t.data)}}async _write(t){this.websocket.send(t)}async destroy(){null!=this.#we&&(this.#we.close(),this.#we=null),super.destroy()}}const el=\"84842078b09946638c03157f83405213\";class nl extends tl{projectId;projectSecret;constructor(t,e){const n=new rl(t,e),r=n._getConnection();h(!r.credentials,\"INFURA WebSocket project secrets unsupported\",\"UNSUPPORTED_OPERATION\",{operation:\"InfuraProvider.getWebSocketProvider()\"});super(r.url.replace(/^http/i,\"ws\").replace(\"/v3/\",\"/ws/v3/\"),n._network),o(this,{projectId:n.projectId,projectSecret:n.projectSecret})}isCommunityResource(){return this.projectId===el}}class rl extends Vc{projectId;projectSecret;constructor(t,e,n){null==t&&(t=\"mainnet\");const r=ec.from(t);null==e&&(e=el),null==n&&(n=null);super(rl.getRequest(r,e,n),r,{staticNetwork:r}),o(this,{projectId:e,projectSecret:n})}_getProvider(t){try{return new rl(t,this.projectId,this.projectSecret)}catch(t){}return super._getProvider(t)}isCommunityResource(){return this.projectId===el}static getWebSocketProvider(t,e){return new nl(t,e)}static getRequest(t,e,n){null==e&&(e=el),null==n&&(n=null);const r=new tr(`https://${function(t){switch(t){case\"mainnet\":return\"mainnet.infura.io\";case\"goerli\":return\"goerli.infura.io\";case\"sepolia\":return\"sepolia.infura.io\";case\"arbitrum\":return\"arbitrum-mainnet.infura.io\";case\"arbitrum-goerli\":return\"arbitrum-goerli.infura.io\";case\"arbitrum-sepolia\":return\"arbitrum-sepolia.infura.io\";case\"base\":return\"base-mainnet.infura.io\";case\"base-goerlia\":return\"base-goerli.infura.io\";case\"base-sepolia\":return\"base-sepolia.infura.io\";case\"bnb\":return\"bnbsmartchain-mainnet.infura.io\";case\"bnbt\":return\"bnbsmartchain-testnet.infura.io\";case\"linea\":return\"linea-mainnet.infura.io\";case\"linea-goerli\":return\"linea-goerli.infura.io\";case\"linea-sepolia\":return\"linea-sepolia.infura.io\";case\"matic\":return\"polygon-mainnet.infura.io\";case\"matic-amoy\":return\"polygon-amoy.infura.io\";case\"matic-mumbai\":return\"polygon-mumbai.infura.io\";case\"optimism\":return\"optimism-mainnet.infura.io\";case\"optimism-goerli\":return\"optimism-goerli.infura.io\";case\"optimism-sepolia\":return\"optimism-sepolia.infura.io\"}f(!1,\"unsupported network\",\"network\",t)}(t.name)}/v3/${e}`);return r.allowGzip=!0,n&&r.setCredentials(\"\",n),e===el&&(r.retryFunc=async(t,e,n)=>{var r;return r=\"InfuraProvider\",qc.has(r)||(qc.add(r),console.log(\"========= NOTICE =========\"),console.log(`Request-Rate Exceeded for ${r} (this message will not be repeated)`),console.log(\"\"),console.log(\"The default API keys for each service are provided as a highly-throttled,\"),console.log(\"community resource for low-traffic projects and early prototyping.\"),console.log(\"\"),console.log(\"While your application will continue to function, we highly recommended\"),console.log(\"signing up for your own API keys to improve performance, increase your\"),console.log(\"request rate/limit and enable other perks, such as metrics and advanced APIs.\"),console.log(\"\"),console.log(\"For more details: https://docs.ethers.org/api-keys/\"),console.log(\"==========================\")),!0}),r}}const sl=[\"eip155:1\",\"eip155:11155111\",\"eip155:17000\"],il=\"Ethereum Name Service\";const ol=async t=>{const{chainId:e,address:n,domain:r}=t;let s;const i=parseInt(e.split(\":\")[1]??\"1\",10);if(sl.includes(e))s=new Kc(ethereum,i);else{const t=\"992108d9a01d4bffb06df489a0a3f458\";if(!t)throw new Error(\"INFURA_PROJECT_ID is missing.\");s=new rl(1,t)}if(r){const t=await s.getResolver(r);if(sl.includes(e)){var o;const e=await(null===(o=t)||void 0===o?void 0:o.getAddress());if(e)return{resolvedAddresses:[{resolvedAddress:e,protocol:il,domainName:r}]}}else{var a,c;const e=await(null===(a=t)||void 0===a?void 0:a.getAddress(i));if(e)return{resolvedAddresses:[{resolvedAddress:e,protocol:il,domainName:r}]};const n=await(null===(c=t)||void 0===c?void 0:c.getAddress());if(n){const t=await async function(t,e){try{return\"0x\"!==await t.getCode(e,\"pending\")}catch(t){return console.error(\"Unable to determine if resolved ENS address is a contract. Assuming it is and returning nothing.\"),!0}}(s,n);if(!t){const t=(await s.getNetwork()).name;return{resolvedAddresses:[{resolvedAddress:n,protocol:`⚠️ ${il} (${t})`,domainName:r}]}}}}}else if(n){const t=await s.lookupAddress(n);if(t)return{resolvedDomains:[{resolvedDomain:t,protocol:il}]}}return null};var al=exports;for(var cl in e)al[cl]=e[cl];e.__esModule&&Object.defineProperty(al,\"__esModule\",{value:!0})})();"}], "removable": false}