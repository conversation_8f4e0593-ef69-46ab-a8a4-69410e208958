{"QRHardwareInvalidTransactionTitle": {"message": "Error"}, "QRHardwareMismatchedSignId": {"message": "Datos de transacción incongruentes. Compruebe los detalles."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "No hay más cuentas. Para acceder a otra cuenta que no figura en la lista, vuelva a conectar su cartera de hardware y selecciónela."}, "QRHardwareScanInstructions": {"message": "Coloque el código QR delante de la cámara. La pantalla está borrosa, pero no afectará la lectura."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "Después de firmar con su cartera, haga clic en \"Obtener firma\" para recibir la firma"}, "QRHardwareSignRequestGetSignature": {"message": "Obtener firma"}, "QRHardwareSignRequestSubtitle": {"message": "Escanee código QR con su cartera"}, "QRHardwareSignRequestTitle": {"message": "Solicitar firma"}, "QRHardwareUnknownQRCodeTitle": {"message": "Error"}, "QRHardwareUnknownWalletQRCode": {"message": "Código QR no válido. Escanee el QR sincronizado de la cartera de hardware."}, "QRHardwareWalletImporterTitle": {"message": "Escanear código QR"}, "QRHardwareWalletSteps1Description": {"message": "Conecte una cartera de hardware airgapped que se comunique por códigos QR. Las carteras de hardware con soporte oficial incluyen:"}, "QRHardwareWalletSteps1Title": {"message": "Cartera HW con QR"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "about": {"message": "Acerca de"}, "acceptTermsOfUse": {"message": "Leí y estoy de acuerdo con $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Accediendo a la cámara..."}, "account": {"message": "C<PERSON><PERSON>"}, "accountDetails": {"message": "Detalles de la cuenta"}, "accountName": {"message": "Nombre de la cuenta"}, "accountNameDuplicate": {"message": "Este nombre de cuenta ya existe", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountOptions": {"message": "Opciones de la cuenta"}, "accountSelectionRequired": {"message": "¡Debe seleccionar una cuenta!"}, "active": {"message": "Activo"}, "activity": {"message": "Actividad"}, "activityLog": {"message": "Registro de actividad"}, "add": {"message": "<PERSON><PERSON><PERSON>"}, "addANetwork": {"message": "Agregar una red"}, "addANickname": {"message": "Añadir un apodo"}, "addAcquiredTokens": {"message": "Agregar los tokens que adquirió con MetaMask"}, "addAlias": {"message": "<PERSON><PERSON><PERSON><PERSON> alias"}, "addContact": {"message": "Agregar contacto"}, "addFriendsAndAddresses": {"message": "Agregue amigos y direcciones de confianza"}, "addMemo": {"message": "<PERSON><PERSON><PERSON> memo"}, "addNetwork": {"message": "Agregar red"}, "addSuggestedTokens": {"message": "Agregar tokens sugeridos"}, "addToken": {"message": "Agregar token"}, "addTokenByContractAddress": {"message": "¿No encuentra un token? Para agregar un token, copie su dirección. Puede encontrar la dirección de contrato del token en $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "address": {"message": "Dirección"}, "advanced": {"message": "<PERSON><PERSON><PERSON>"}, "advancedBaseGasFeeToolTip": {"message": "Cuando su transacción se incluya en el bloque, se reembolsará cualquier diferencia entre su tarifa base máxima y la tarifa base real. El importe total se calcula como tarifa base máxima (en GWEI) * límite de gas."}, "advancedGasFeeModalTitle": {"message": "Tarifa de gas avanzada"}, "advancedGasPriceTitle": {"message": "Precio del gas"}, "advancedPriorityFeeToolTip": {"message": "La tarifa de prioridad (también llamada “propina del minero”) va directamente a los mineros para incentivarlos a priorizar su transacción."}, "alertDisableTooltip": {"message": "Esto se puede modificar en \"Configuración > Alertas\""}, "alerts": {"message": "<PERSON><PERSON><PERSON>"}, "allowWithdrawAndSpend": {"message": "Permitir que se retire $1 y gastar hasta el siguiente importe:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "Importe"}, "appDescription": {"message": "Una cartera de Ethereum en el explorador", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "Aprobar límite de gastos"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "approved": {"message": "Aprobado"}, "asset": {"message": "Activo"}, "assetOptions": {"message": "Opciones de activos"}, "attributions": {"message": "Atribuciones"}, "authorizedPermissions": {"message": "Ha autorizado los siguientes permisos"}, "autoLockTimeLimit": {"message": "Temporizador con bloqueo automático (minutos)"}, "autoLockTimeLimitDescription": {"message": "Establezca el tiempo de inactividad en minutos antes de que se bloquee MetaMask."}, "average": {"message": "Promedio"}, "back": {"message": "Volver"}, "backupApprovalInfo": {"message": "Este código secreto es necesario para que recupere la cartera en caso de que pierda el dispositivo, olvide su contraseña, tenga que volver a instalar MetaMask o quiera acceder a la cartera en otro dispositivo."}, "backupApprovalNotice": {"message": "Cree una copia de seguridad del código de recuperación secreto para mantener protegidos sus fondos y su cartera."}, "backupNow": {"message": "Crear copia de seguridad ahora"}, "balance": {"message": "<PERSON><PERSON>"}, "balanceOutdated": {"message": "Es posible que el saldo esté desactualizado"}, "baseFee": {"message": "Tarifa base"}, "basic": {"message": "Básico"}, "betaMetamaskVersion": {"message": "Versión Beta de MetaMask"}, "blockExplorerAccountAction": {"message": "C<PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "Activo", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "Dirección URL del explorador de bloques"}, "blockExplorerUrlDefinition": {"message": "La dirección URL que se utiliza como explorador de bloques de esta red."}, "blockExplorerView": {"message": "Ver cuenta en $1", "description": "$1 replaced by URL for custom block explorer"}, "browserNotSupported": {"message": "El explorador no es compatible..."}, "buildContactList": {"message": "Cree su lista de contactos"}, "builtAroundTheWorld": {"message": "MetaMask está diseñado y construido en todo el mundo."}, "busy": {"message": "Ocupado"}, "bytes": {"message": "Bytes"}, "canToggleInSettings": {"message": "Puede volver a activar esta notificación desde Configuración -> Alertas."}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "cancelPopoverTitle": {"message": "Cancelar transacción"}, "cancelSpeedUpLabel": {"message": "Esta tarifa de gas va a $1 el original.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Para $1 una transacción, la tarifa de gas debe aumentar al menos un 10% para que sea reconocida por la red.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "Cancelado"}, "chainId": {"message": "Identificador de cadena"}, "chainIdDefinition": {"message": "El identificador de cadena que se utiliza para firmar transacciones en esta red."}, "chainIdExistsErrorMsg": {"message": "En este momento, la red $1 está utilizando este identificador de cadena."}, "chromeRequiredForHardwareWallets": {"message": "Debe usar MetaMask en Google Chrome para poder conectarse a su cartera de hardware."}, "clickToConnectLedgerViaWebHID": {"message": "Haga clic aquí para conectar su Ledger a través de WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "confirm": {"message": "Confirmar"}, "confirmPassword": {"message": "Confirmar con<PERSON>"}, "confirmRecoveryPhrase": {"message": "Confirmar la frase secreta de recuperación"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confusableUnicode": {"message": "“$1” es similar a “$2”."}, "confusableZeroWidthUnicode": {"message": "Se encontró un carácter de ancho cero."}, "confusingEnsDomain": {"message": "Se detectó un carácter que puede confundirse con otro similar en el nombre de ENS. Verifique el nombre de ENS para evitar una posible estafa."}, "connect": {"message": "Conectar"}, "connectAccountOrCreate": {"message": "Conectar cuenta o crear nueva"}, "connectManually": {"message": "Conectarse manualmente al sitio actual"}, "connectWithMetaMask": {"message": "Conectarse con MetaMask"}, "connectedAccountsDescriptionPlural": {"message": "Tiene $1 cuentas conectadas a este sitio.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Tiene 1 cuenta conectada a este sitio."}, "connectedAccountsEmptyDescription": {"message": "MetaMask no está conectado a este sitio. Para conectarse a un sitio de Web3, busque el botón de conexión en su sitio."}, "connectedSites": {"message": "Sitios conectados"}, "connectedSitesDescription": {"message": "$1 está conectado a estos sitios. Pueden ver la dirección de su cuenta.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 no está conectado a ningún sitio.", "description": "$1 is the account name"}, "connecting": {"message": "Estableciendo conexión..."}, "connectingTo": {"message": "Estableciendo conexión a $1"}, "connectingToGoerli": {"message": "Estableciendo conexión a la red de prueba Goerli"}, "connectingToLineaGoerli": {"message": "Estableciendo conexión a la red de prueba Linea Goerli"}, "connectingToMainnet": {"message": "Estableciendo conexión a la red principal de Ethereum"}, "contactUs": {"message": "Cont<PERSON><PERSON><PERSON>s"}, "contacts": {"message": "Contactos"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contract": {"message": "Contrato"}, "contractAddress": {"message": "Dirección del contrato"}, "contractAddressError": {"message": "Está enviando tokens a la dirección de contrato del token. Esto puede provocar la pérdida de los tokens."}, "contractDeployment": {"message": "Implementación de contrato"}, "contractInteraction": {"message": "Interacción con el contrato"}, "copiedExclamation": {"message": "¡Copiado!"}, "copyAddress": {"message": "Copiar dirección al Portapapeles"}, "copyToClipboard": {"message": "Copiar al Portapapeles"}, "copyTransactionId": {"message": "Copiar ID de transacción"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "createPassword": {"message": "<PERSON><PERSON><PERSON> con<PERSON>"}, "currencyConversion": {"message": "Conversión de moneda"}, "currencySymbol": {"message": "Símbolo de moneda"}, "currencySymbolDefinition": {"message": "El símbolo bursátil que se muestra para la moneda de esta red."}, "currentAccountNotConnected": {"message": "La cuenta actual no está conectada"}, "currentExtension": {"message": "Página de extensión actual"}, "currentLanguage": {"message": "Idioma actual"}, "currentTitle": {"message": "Actual:"}, "currentlyUnavailable": {"message": "No disponible en esta red"}, "custom": {"message": "<PERSON><PERSON><PERSON>"}, "customSpendLimit": {"message": "Límite de gastos personalizado"}, "customToken": {"message": "Token personalizado"}, "dappSuggested": {"message": "Sitio sugerido"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 ha sugerido este precio.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedShortLabel": {"message": "Sitio"}, "dappSuggestedTooltip": {"message": "$1 ha recomendado este precio.", "description": "$1 represents the Dapp's origin"}, "data": {"message": "Datos"}, "decimal": {"message": "Decimales del token"}, "decimalsMustZerotoTen": {"message": "Los decimales deben ser al menos 0 y no más de 36."}, "decrypt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "decryptCopy": {"message": "<PERSON><PERSON><PERSON> mensaje cifrado"}, "decryptInlineError": {"message": "Este mensaje no se puede descifrar debido al error: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 quiere leer este mensaje para llevar a cabo la acción", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "decryptRequest": {"message": "<PERSON><PERSON><PERSON><PERSON> solicitud"}, "delete": {"message": "Eliminar"}, "description": {"message": "Descripción"}, "details": {"message": "Detalles"}, "disabledGasOptionToolTipMessage": {"message": "\"1$\" está desactivado porque no cumple el mínimo de un aumento del 10% respecto a la tarifa de gas original.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Desconectar"}, "disconnectAllAccounts": {"message": "Desconectar todas las cuentas"}, "disconnectAllAccountsConfirmationDescription": {"message": "¿Está seguro de que se quiere desconectar? Podría perder la funcionalidad del sitio."}, "disconnectPrompt": {"message": "Desconectar $1"}, "disconnectThisAccount": {"message": "Desconectar esta cuenta"}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "dismissReminderDescriptionField": {"message": "Active esta opción para ignorar el recordatorio de respaldo de la frase de recuperación. Le recomendamos que respalde su frase secreta de recuperación para evitar la pérdida de fondos"}, "dismissReminderField": {"message": "Ignorar el recordatorio de respaldo de la frase de recuperación"}, "domain": {"message": "<PERSON>inio"}, "done": {"message": "<PERSON><PERSON>"}, "dontShowThisAgain": {"message": "No volver a mostrar"}, "downloadGoogleChrome": {"message": "Descargar Google Chrome"}, "downloadStateLogs": {"message": "Descargar registros de estado"}, "dropped": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "edit": {"message": "<PERSON><PERSON>"}, "editANickname": {"message": "<PERSON><PERSON> a<PERSON>"}, "editAddressNickname": {"message": "Editar apodo de dirección"}, "editCancellationGasFeeModalTitle": {"message": "Editar tarifa de cancelación de gas"}, "editContact": {"message": "<PERSON><PERSON>"}, "editGasFeeModalTitle": {"message": "Editar tarifa de gas"}, "editGasLimitOutOfBounds": {"message": "El límite de gas debe ser al menos $1"}, "editGasLimitOutOfBoundsV2": {"message": "El límite de gas debe ser superior a $1 e inferior a $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "El límite de gas es el máximo de unidades de gas que está dispuesto a utilizar. Las unidades de gas son un multiplicador de la \"Tarifa de prioridad máxima\" y de la \"Tarifa máxima\"."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "La tarifa base máxima no puede ser inferior a la tarifa de prioridad"}, "editGasMaxBaseFeeHigh": {"message": "La tarifa base máxima es más alta de lo necesario"}, "editGasMaxBaseFeeLow": {"message": "La tarifa base máxima es baja para las condiciones actuales de la red"}, "editGasMaxFeeHigh": {"message": "La tarifa base máxima es más alta de lo necesario"}, "editGasMaxFeeLow": {"message": "Tarifa máxima demasiado baja para las condiciones de red"}, "editGasMaxFeePriorityImbalance": {"message": "La tarifa base máxima no puede ser inferior a la tarifa de prioridad máxima"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "La tarifa máxima de prioridad debe ser superior a 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "La tarifa de prioridad debe ser superior a 0."}, "editGasMaxPriorityFeeHigh": {"message": "La tarifa máxima de prioridad es más alta de lo necesario. Es posible que pague más de lo necesario."}, "editGasMaxPriorityFeeHighV2": {"message": "La tarifa de prioridad es más alta de lo necesario. Es posible que pague más de lo necesario"}, "editGasMaxPriorityFeeLow": {"message": "La tarifa de prioridad máxima es baja para las condiciones actuales de la red"}, "editGasMaxPriorityFeeLowV2": {"message": "La tarifa de prioridad es baja para las condiciones actuales de la red"}, "editGasPriceTooLow": {"message": "El precio del gas debe ser superior a 0"}, "editGasPriceTooltip": {"message": "Esta red requiere un campo \"Precio del gas\" cuando se envía una transacción. El precio del gas es la cantidad que se pagará por unidad de gas."}, "editGasSubTextFeeLabel": {"message": "<PERSON><PERSON><PERSON> m<PERSON>xi<PERSON>:"}, "editGasTitle": {"message": "Editar prioridad"}, "editGasTooLow": {"message": "Tiempo de procesamiento desconocido"}, "editNonceField": {"message": "<PERSON><PERSON>"}, "editNonceMessage": {"message": "Esta es una función avanzada, úsela con precaución."}, "editPermission": {"message": "<PERSON><PERSON> permiso"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Editar la tarifa de aceleración de gas"}, "enableAutoDetect": {"message": " Activar autodetección"}, "enableFromSettings": {"message": " Actívela en Configuración."}, "enableToken": {"message": "activar $1", "description": "$1 is a token symbol, e.g. ETH"}, "encryptionPublicKeyNotice": {"message": "$1 quisiera su clave pública de cifrado. Al aceptar, este sitio podrá redactar mensajes cifrados para usted.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Solicitar clave pública de cifrado"}, "endpointReturnedDifferentChainId": {"message": "El punto de conexión devolvió un id. de cadena diferente: $1", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "ensUnknownError": {"message": "Error al buscar ENS."}, "enterMaxSpendLimit": {"message": "Escribir límite máximo de gastos"}, "enterPasswordContinue": {"message": "Escribir contraseña para continuar"}, "errorCode": {"message": "Código: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorMessage": {"message": "Mensaje: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Código: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageTitle": {"message": "MetaMask encontró un error", "description": "Title of generic error page"}, "errorStack": {"message": "Pila:", "description": "Title for error stack, which is displayed for debugging purposes"}, "ethGasPriceFetchWarning": {"message": "Se muestra el precio del gas de respaldo, ya que el servicio para calcular el precio del gas principal no se encuentra disponible en este momento."}, "ethereumPublicAddress": {"message": "Dirección pública de Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Ver cuenta en Etherscan"}, "etherscanViewOn": {"message": "Ver en Etherscan"}, "expandView": {"message": "Expandir vista"}, "experimental": {"message": "Experimental"}, "externalExtension": {"message": "Extensión externa"}, "failed": {"message": "Con errores"}, "failedToFetchChainId": {"message": "No se pudo capturar el ID de cadena. ¿La dirección URL de RPC es correcta?"}, "failureMessage": {"message": "Se produjo un error y no pudimos finalizar la acción"}, "fast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "fileImportFail": {"message": "¿No funciona la importación del archivo? ¡Haga clic aquí!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "le recomendamos que desinstale esta extensión", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask es para que los desarrolladores experimenten con nuevas API inestables. A menos que usted sea desarrollador o probador beta, 1$.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "No garantizamos la seguridad o estabilidad de esta extensión. Las nuevas API ofrecidas por Flask no están protegidas contra los ataques de phishing, lo que significa que cualquier sitio o snap que requiera Flask podría ser un intento malicioso de robar sus activos.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Todas las API de Flask son experimentales. Se pueden cambiar o eliminadar sin previo aviso o pueden permanecer en Flask indefinidamente sin ser migradas a MetaMask estable. Úselas bajo su propia responsabilidad.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarningAcceptButton": {"message": "Acepto los riesgos", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "followUsOnTwitter": {"message": "Síganos en Twitter"}, "forbiddenIpfsGateway": {"message": "Puerta de enlace de IPFS prohibida: especifique una puerta de enlace de CID"}, "forgetDevice": {"message": "Olvidar este dispositivo"}, "from": {"message": "De"}, "fromAddress": {"message": "De: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "gas": {"message": "Gas"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Editar tarifa de gas sugerida"}, "gasDisplayDappWarning": {"message": "Esta tarifa de gas ha sido sugerida por $1. Anularla puede causar un problema con su transacción. Comuníquese con $1 si tiene preguntas.", "description": "$1 represents the Dapp's origin"}, "gasLimit": {"message": "Límite de gas"}, "gasLimitTooLow": {"message": "El límite de gas debe ser al menos 21 000"}, "gasLimitV2": {"message": "Límite de gas"}, "gasOption": {"message": "Opción de gas"}, "gasPriceExcessive": {"message": "Su tarifa de gas es demasiado alta. Considere reducir el importe."}, "gasPriceFetchFailed": {"message": "Se produjo un error al calcular el precio del gas debido a una falla en la red."}, "gasTimingHoursShort": {"message": "$1 horas", "description": "$1 represents a number of hours"}, "gasTimingMinutesShort": {"message": "$1 min", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 s", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gas usado"}, "general": {"message": "General"}, "goerli": {"message": "Red de prueba Goerli"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Hardware"}, "hardwareWalletConnected": {"message": "Cartera de hardware conectada"}, "hardwareWalletLegacyDescription": {"message": "(antiguo)", "description": "Text representing the MEW path"}, "hardwareWalletSupportLinkConversion": {"message": "haga clic aquí"}, "hardwareWallets": {"message": "Conectar una cartera de hardware"}, "hardwareWalletsMsg": {"message": "Seleccione una cartera de hardware que desee usar con MetaMask."}, "here": {"message": "aquí", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "Datos hexadecimales"}, "hide": {"message": "Ocultar"}, "hideTokenPrompt": {"message": "¿Ocultar token?"}, "hideTokenSymbol": {"message": "Ocultar $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Ocultar tokens sin saldo"}, "high": {"message": "Agresivo"}, "highLowercase": {"message": "alta"}, "history": {"message": "Historial"}, "import": {"message": "Importar", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Error al importar la cuenta."}, "importAccountMsg": {"message": "Las cuentas importadas no se asociarán con la frase secreta de recuperación de la cuenta original de MetaMask. Aprenda más sobre las cuentas importadas"}, "importTokenQuestion": {"message": "¿Desea importar el token?"}, "importTokenWarning": {"message": "Toda persona puede crear un token con cualquier nombre, incluso versiones falsas de tokens existentes. ¡Agréguelo y realice transacciones bajo su propio riesgo!"}, "importTokensCamelCase": {"message": "Importar tokens"}, "imported": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "status showing that an account has been fully loaded into the keyring"}, "infuraBlockedNotification": {"message": "MetaMask no se pudo conectar al host de la cadena de bloques. Revise las razones posibles $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "La red confirmó la transacción inicial. Haga clic en Aceptar para volver."}, "insufficientBalance": {"message": "<PERSON><PERSON> insuficiente."}, "insufficientFunds": {"message": "Fondos insuficientes."}, "insufficientFundsForGas": {"message": "Fondos insuficientes para el gas"}, "insufficientTokens": {"message": "Tokens insuficientes."}, "invalidAddress": {"message": "Dirección no válida"}, "invalidAddressRecipient": {"message": "La dirección del destinatario no es válida"}, "invalidChainIdTooBig": {"message": "ID de cadena no válido. El identificador de cadena es demasiado grande."}, "invalidCustomNetworkAlertContent1": {"message": "Es necesario volver a especificar el ID de la cadena para la red virtual “$1”.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Para protegerlo de proveedores de red malintencionados o defectuosos, ahora se requieren ID de cadena para todas las redes personalizadas."}, "invalidCustomNetworkAlertContent3": {"message": "Vaya a Configuración > Red y especifique el ID de cadena. Puede encontrar los ID de cadena de las redes más populares en $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Red personalizada no válida"}, "invalidHexNumber": {"message": "Número hexadecimal no válido."}, "invalidHexNumberLeadingZeros": {"message": "Número hexadecimal no válido. Quite todos los ceros iniciales."}, "invalidIpfsGateway": {"message": "Puerta de enlace de IPFS no válida: el valor debe ser una dirección URL válida"}, "invalidNumber": {"message": "Número no válido. Escriba un número decimal o un número hexadecimal con el prefijo “0x”."}, "invalidNumberLeadingZeros": {"message": "Número no válido. Quite todos los ceros iniciales."}, "invalidRPC": {"message": "Dirección URL de RPC no válida"}, "invalidSeedPhrase": {"message": "Frase secreta de recuperación no válida"}, "jsonFile": {"message": "Archivo JSON", "description": "format for importing an account"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Dirección de contrato conocida."}, "knownTokenWarning": {"message": "Esta acción editará tokens que ya estén enumerados en la cartera y que se pueden usar para engañarlo. Apruebe solo si está seguro de que quiere cambiar lo que representan estos tokens."}, "lastConnected": {"message": "Última conexión"}, "learnMore": {"message": "más información"}, "learnMoreAboutGas": {"message": "¿Quiere $1 sobre el gas?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreUpperCase": {"message": "Más información"}, "learnScamRisk": {"message": "estafas y riesgos de seguridad."}, "ledgerAccountRestriction": {"message": "Debe usar su última cuenta antes de poder agregar una nueva."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Cierre cualquier otro software conectado a su dispositivo y haga clic aquí para actualizar."}, "ledgerConnectionInstructionHeader": {"message": "Antes de hacer clic en Confirmar:"}, "ledgerConnectionInstructionStepFour": {"message": "Habilite \"datos de contrato inteligente\" o \"firma ciega\" en su dispositivo Ledger"}, "ledgerConnectionInstructionStepThree": {"message": "Conecte su dispositivo Ledger y seleccione la aplicación Ethereum"}, "ledgerDeviceOpenFailureMessage": {"message": "El dispositivo Ledger no pudo abrirse. Su Ledger podría estar conectado a otro software. Cierre Ledger Live u otras aplicaciones conectadas a su dispositivo Ledger, e intente conectarse de nuevo."}, "ledgerLiveApp": {"message": "Aplicación Ledger Live"}, "ledgerLocked": {"message": "No se pudo establecer la conexión con el dispositivo Ledger. Asegúrese de que el dispositivo está desbloqueado y que la aplicación de Ethereum está abierta."}, "ledgerTimeout": {"message": "Ledger Live tardó mucho en responder o se excedió el tiempo de espera de la conexión. Asegúrese de que la aplicación de Ledger Live está abierta y que su dispositivo está desbloqueado."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "El dispositivo Ledger no se ha conectado. Si desea conectar su Ledger, haga clic de nuevo en 'Continuar' y apruebe la conexión HID", "description": "An error message shown to the user during the hardware connect flow."}, "likeToImportTokens": {"message": "¿Quiere agregar estos tokens?"}, "lineaGoerli": {"message": "Red de prueba Linea Goerli"}, "link": {"message": "Enlace"}, "links": {"message": "Enlaces"}, "loadMore": {"message": "<PERSON>gar más"}, "loading": {"message": "Cargando..."}, "localhost": {"message": "Host local 8545"}, "lock": {"message": "Bloquear"}, "low": {"message": "Baja"}, "lowGasSettingToolTipMessage": {"message": "Utilice $1 para esperar un precio más bajo. Las estimaciones de tiempo son mucho menos precisas ya que los precios son algo imprevisibles.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "baja"}, "mainnet": {"message": "Red principal de Ethereum"}, "makeAnotherSwap": {"message": "Crear un nuevo swap"}, "makeSureNoOneWatching": {"message": "Asegúrese de que nadie está viendo su pantalla", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "max": {"message": "Máx."}, "maxBaseFee": {"message": "Tarifa base máxima"}, "maxFee": {"message": "<PERSON><PERSON><PERSON>"}, "maxPriorityFee": {"message": "Tarifa máxima de prioridad"}, "medium": {"message": "<PERSON><PERSON><PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Utilice $1 para un procesamiento rápido al precio actual del mercado.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "nota"}, "message": {"message": "Men<PERSON><PERSON>"}, "metaMaskConnectStatusParagraphOne": {"message": "Ahora tiene más control sobre las conexiones de su cuenta en MetaMask."}, "metaMaskConnectStatusParagraphThree": {"message": "Haga clic en él para administrar las cuentas conectadas."}, "metaMaskConnectStatusParagraphTwo": {"message": "El botón de estado de la conexión muestra si el sitio web que visita está conectado a la cuenta seleccionada actualmente."}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swaps está en mantenimiento. Vuelva a comprobarlo más tarde."}, "metamaskVersion": {"message": "Versión de MetaMask"}, "mismatchedChainLinkText": {"message": "verifique los detalles de la red", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mustSelectOne": {"message": "Debe seleccionar al menos 1 token."}, "name": {"message": "Nombre"}, "needHelp": {"message": "¿Necesita ayuda? Comuníquese con $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Comparta su opinión"}, "needHelpLinkText": {"message": "Soporte de MetaMask"}, "needHelpSubmitTicket": {"message": "Enviar un ticket"}, "needImportFile": {"message": "Debe seleccionar un archivo para la importación.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "No se pueden enviar cantidades negativas de ETH."}, "networkDetails": {"message": "Detalles de la red"}, "networkIsBusy": {"message": "La red está ocupada. Los precios del gas son altos y las estimaciones son menos precisas."}, "networkName": {"message": "Nombre de la red"}, "networkNameBSC": {"message": "BSC"}, "networkNameDefinition": {"message": "El nombre asociado a esta red."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameTestnet": {"message": "Red de prueba"}, "networkStatus": {"message": "Estado de la red"}, "networkStatusBaseFeeTooltip": {"message": "La tarifa base la fija la red y cambia cada 13-14 segundos. Nuestras opciones $1 y $2 dan cuenta de los aumentos repentinos.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Rango de tarifas de prioridad (también llamada “propina del minero”): esto va directamente a los mineros para incentivarlos a priorizar su transacción."}, "networkStatusStabilityFeeTooltip": {"message": "Las tarifas del gas son de $1 en relación con las últimas 72 horas.", "description": "$1 is networks stability value - stable, low, high"}, "networkURL": {"message": "URL de la red"}, "networkURLDefinition": {"message": "La dirección URL que se utilizó para acceder a esta red."}, "networks": {"message": "Redes"}, "nevermind": {"message": "No es importante"}, "newAccount": {"message": "Cuenta nueva"}, "newAccountNumberName": {"message": "Cuenta $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Contacto nuevo"}, "newContract": {"message": "Contrato nuevo"}, "newNetworkAdded": {"message": "¡\"$1\" se añadió con éxito!"}, "newNftAddedMessage": {"message": "¡El coleccionable fue añadido con éxito!"}, "newPassword": {"message": "Contraseña nueva (mín. de 8 caracteres)"}, "next": {"message": "Siguient<PERSON>"}, "nfts": {"message": "NFT"}, "nickname": {"message": "A<PERSON>do"}, "noAccountsFound": {"message": "No se encuentran cuentas para la consulta de búsqueda determinada"}, "noConversionRateAvailable": {"message": "No hay tasa de conversión disponible"}, "noWebcamFound": {"message": "No se encontró la cámara web del equipo. Vuelva a intentarlo."}, "noWebcamFoundTitle": {"message": "No se encontró cámara web"}, "nonce": {"message": "<PERSON><PERSON>"}, "notBusy": {"message": "No ocupado"}, "notCurrentAccount": {"message": "¿Esta es la cuenta correcta? No coincide con la cuenta seleccionada actualmente en la cartera"}, "notEnoughGas": {"message": "No hay gas suficiente"}, "ofTextNofM": {"message": "de"}, "off": {"message": "Desactivado"}, "offlineForMaintenance": {"message": "Sin conexión por mantenimiento"}, "ok": {"message": "Aceptar"}, "on": {"message": "Activado"}, "onboardingCreateWallet": {"message": "<PERSON><PERSON>r una nueva cartera"}, "onboardingImportWallet": {"message": "Importar una cartera existente"}, "onboardingPinExtensionDescription": {"message": "<PERSON><PERSON> en su navegador para que sea accesible y las confirmaciones de las transacciones se vean fácilmente."}, "onboardingPinExtensionDescription2": {"message": "Para abrir MetaMask haga clic en la extensión y acceda a su cartera con 1 clic."}, "onboardingPinExtensionDescription3": {"message": "Haga clic en el icono de la extensión del navegador para tener acceso instantáneo", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "¡Su instalación de MetaMask ha finalizado!"}, "onlyConnectTrust": {"message": "Conéctese solo con sitios de confianza.", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Abra MetaMask en pantalla completa para conectar su Ledger a través de WebHID.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "optional": {"message": "Opcional"}, "origin": {"message": "Origen"}, "participateInMetaMetrics": {"message": "Participar en MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Participe en MetaMetrics para ayudarnos a mejorar MetaMask"}, "password": {"message": "Contraseña"}, "passwordNotLongEnough": {"message": "La contraseña no es suficientemente larga"}, "passwordTermsWarning": {"message": "Entiendo que MetaMask no me puede recuperar esta contraseña. $1"}, "passwordsDontMatch": {"message": "Las contraseñas no coinciden"}, "pastePrivateKey": {"message": "Pegue aquí la cadena de clave privada:", "description": "For importing an account from a private key"}, "pending": {"message": "Pendiente"}, "permission_ethereumAccounts": {"message": "Ver dirección, saldo de cuenta, actividad e iniciar transacciones", "description": "The description for the `eth_accounts` permission"}, "permissions": {"message": "<PERSON><PERSON><PERSON>"}, "personalAddressDetected": {"message": "Se detectó una dirección personal. Ingrese la dirección de contrato del token."}, "plusXMore": {"message": "+ $1 más", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "prev": {"message": "Ant."}, "priorityFee": {"message": "Tarifa de prioridad"}, "priorityFeeProperCase": {"message": "Tarifa de prioridad"}, "privacyMsg": {"message": "Política de privacidad"}, "privateKey": {"message": "Clave privada", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Advertencia: No revele esta clave. Cualquier persona que tenga sus claves privadas podría robar los activos de su cuenta."}, "privateNetwork": {"message": "Red privada"}, "proceedWithTransaction": {"message": "<PERSON><PERSON><PERSON> continuar de todos modos"}, "proposedApprovalLimit": {"message": "Límite de aprobación propuesto"}, "provide": {"message": "Proporcionar"}, "publicAddress": {"message": "Dirección pública"}, "queued": {"message": "En cola"}, "readdToken": {"message": "Puede volver a agregar este token en el futuro desde “Agregar token” en el menú de opciones de las cuentas."}, "receive": {"message": "Recibir"}, "recommendedGasLabel": {"message": "Recomendado"}, "recoveryPhraseReminderBackupStart": {"message": "Iniciar aqu<PERSON>"}, "recoveryPhraseReminderConfirm": {"message": "Entendido"}, "recoveryPhraseReminderHasBackedUp": {"message": "Guarde siempre su frase secreta de recuperación en un lugar seguro y secreto"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "¿Necesita volver a crear una copia de seguridad de su frase secreta de recuperación?"}, "recoveryPhraseReminderItemOne": {"message": "No comparta nunca su frase secreta de recuperación con nadie"}, "recoveryPhraseReminderItemTwo": {"message": "El equipo de MetaMask nunca le pedirá su frase secreta de recuperación"}, "recoveryPhraseReminderSubText": {"message": "Mediante su frase secreta de recuperación, se controlan todas sus cuentas."}, "recoveryPhraseReminderTitle": {"message": "Proteja sus fondos"}, "refreshList": {"message": "Actualizar lista"}, "reject": {"message": "<PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "<PERSON><PERSON><PERSON> todo"}, "rejectTxsDescription": {"message": "Está a punto de rechazar $1 transacciones en lote."}, "rejectTxsN": {"message": "Rechazar $1 transacciones"}, "rejected": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "remove": {"message": "<PERSON><PERSON><PERSON>"}, "removeAccount": {"message": "<PERSON><PERSON><PERSON> cuenta"}, "removeAccountDescription": {"message": "Esta cuenta se quitará de la cartera. Antes de continuar, asegúrese de tener la frase secreta de recuperación original o la clave privada de esta cuenta importada. Puede importar o crear cuentas nuevamente en la lista desplegable de la cuenta. "}, "removeNFT": {"message": "Eliminar NFT"}, "replace": {"message": "reemplazar"}, "required": {"message": "Requerido"}, "reset": {"message": "Restablecer"}, "restore": {"message": "Restaurar"}, "retryTransaction": {"message": "Reintentar transacción"}, "reusedTokenNameWarning": {"message": "Un token reutiliza un símbolo de otro token que se le muestra. Esto puede ser confuso o engañoso."}, "revealSeedWords": {"message": "Revelar frase secreta de recuperación"}, "revealSeedWordsWarning": {"message": "<PERSON><PERSON>s palabras pueden usarse para robar todas sus cuentas.", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "rpcUrl": {"message": "Nueva dirección URL de RPC"}, "save": {"message": "Guardar"}, "scanInstructions": {"message": "Ponga el código QR frente a la cámara"}, "scanQrCode": {"message": "Escanear código QR"}, "scrollDown": {"message": "Desp<PERSON><PERSON><PERSON> hacia abajo"}, "search": {"message": "Buscar"}, "searchAccounts": {"message": "Buscar cuentas"}, "secretRecoveryPhrase": {"message": "Frase secreta de recuperación"}, "secureWallet": {"message": "<PERSON><PERSON> segura"}, "securityAndPrivacy": {"message": "Seguridad y privacidad"}, "seedPhraseIntroTitle": {"message": "Proteger su cartera"}, "seedPhraseReq": {"message": "Las frases secretas de recuperación contienen 12, 15, 18, 21 o 24 palabras"}, "selectAccounts": {"message": "Seleccione la(s) cuenta(s) a usar en este sitio"}, "selectAll": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo"}, "selectAnAccount": {"message": "Seleccionar una cuenta"}, "selectAnAccountAlreadyConnected": {"message": "Esta cuenta ya se conectó a MetaMask"}, "selectHdPath": {"message": "Seleccione la ruta de acceso al disco duro"}, "selectNFTPrivacyPreference": {"message": "Activar la detección de NFT en Configuración"}, "selectPathHelp": {"message": "Si no ve las cuentas previstas, intente cambiar la ruta HD."}, "selectType": {"message": "Seleccionar tipo"}, "selectingAllWillAllow": {"message": "Seleccionar todo permitirá que este sitio vea todas las cuentas actuales. Asegúrese de que este sitio sea de confianza."}, "send": {"message": "Enviar"}, "sendSpecifiedTokens": {"message": "Enviar $1", "description": "Symbol of the specified token"}, "sendingDisabled": {"message": "Todavía no se admite el envío de activos ERC-1155 NFT."}, "sendingNativeAsset": {"message": "Enviando $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "settings": {"message": "Configuración"}, "show": {"message": "Mostrar"}, "showFiatConversionInTestnets": {"message": "Mostrar conversión en redes de prueba"}, "showFiatConversionInTestnetsDescription": {"message": "Seleccione esta opción para mostrar la conversión fiduciaria en las redes de prueba"}, "showHexData": {"message": "Mostrar datos hexadecimales"}, "showHexDataDescription": {"message": "Seleccione esta opción para mostrar el campo de datos hexadecimales en la pantalla de envío"}, "showPermissions": {"message": "<PERSON>rar permisos"}, "showTestnetNetworks": {"message": "Mostrar redes de prueba"}, "showTestnetNetworksDescription": {"message": "Seleccione esta opción para mostrar las redes de prueba en la lista de redes"}, "sign": {"message": "<PERSON><PERSON><PERSON>"}, "signatureRequest": {"message": "Solicitud de firma"}, "signed": {"message": "<PERSON><PERSON><PERSON>"}, "simulationErrorMessageV2": {"message": "No pudimos estimar el gas. Podría haber un error en el contrato y esta transacción podría fallar."}, "skipAccountSecurity": {"message": "¿Omitir la seguridad de la cuenta?"}, "skipAccountSecurityDetails": {"message": "Entiendo que hasta que no haga una copia de seguridad de mi frase secreta de recuperación, puedo perder mis cuentas y todos los activos asociados."}, "somethingWentWrong": {"message": "Lo lamentamos, se produjo un error."}, "speedUp": {"message": "<PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "Acelerar esta cancelación"}, "speedUpExplanation": {"message": "Hemos actualizado la tarifa de gas en función de las condiciones actuales de la red y la hemos aumentado al menos un 10% (exigido por la red)."}, "speedUpPopoverTitle": {"message": "Acelerar la transacción"}, "speedUpTooltipText": {"message": "Nueva tarifa de gas"}, "speedUpTransaction": {"message": "Acelerar esta transacción"}, "spendLimitInsufficient": {"message": "Límite de gastos insuficiente"}, "spendLimitInvalid": {"message": "El límite de gastos no es válido, debe ser un número positivo"}, "spendLimitPermission": {"message": "Permiso de límite de gastos"}, "spendLimitRequestedBy": {"message": "Límite de gastos solicitado por $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "El límite de gastos es demasiado alto"}, "stable": {"message": "Estable"}, "stableLowercase": {"message": "estable"}, "stateLogError": {"message": "Error al recuperar los registros de estado."}, "stateLogFileName": {"message": "Registros de estado de MetaMask"}, "stateLogs": {"message": "Registros de estado"}, "stateLogsDescription": {"message": "Los registros de estado contienen sus direcciones de cuentas públicas y las transacciones enviadas."}, "statusNotConnected": {"message": "No conectado"}, "step1LatticeWallet": {"message": "Asegúrese de que su Lattice1 esté listo para conectarse"}, "step1LatticeWalletMsg": {"message": "Puede conectar MetaMask a su dispositivo Lattice1 una vez que esté configurado y en línea. Desbloquee su dispositivo y tenga a mano el ID correspondiente. Para más información sobre el uso de carteras de hardware, $1", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Descargar la aplicación de Ledger"}, "step1LedgerWalletMsg": {"message": "Descargue y configure la aplicación, e ingrese su contraseña para desbloquear $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Conectar la cartera Trezor"}, "step1TrezorWalletMsg": {"message": "Conecte la cartera directamente al equipo. Para más información sobre el uso de su dispositivo de cartera de hardware, $1", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Conectar la cartera Ledger"}, "step2LedgerWalletMsg": {"message": "Conecte la cartera directamente al equipo.  Desbloquee su Ledger y abra la aplicación de Ethereum. Para más información sobre el uso de su dispositivo de cartera de hardware, $1.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "submit": {"message": "Enviar"}, "submitted": {"message": "Enviado"}, "support": {"message": "Soporte técnico"}, "supportCenter": {"message": "Visite nuestro Centro de soporte técnico"}, "swap": {"message": "<PERSON><PERSON><PERSON>"}, "swapAggregator": {"message": "Agregador"}, "swapAllowSwappingOf": {"message": "Permitir canje de $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON>"}, "swapAmountReceivedInfo": {"message": "Se refiere al monto mínimo que recibirá. Puede recibir más en función del desfase."}, "swapApproval": {"message": "Aprobar $1 para canjes", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Necesita $1 más $2 para realizar este canje", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapConfirmWithHwWallet": {"message": "Confirmar con la cartera de hardware"}, "swapContractDataDisabledErrorDescription": {"message": "En la aplicación de Ethereum en su Ledger, diríjase a \"Configuración\" y habilite los datos de contrato. <PERSON><PERSON> intente canjear de nuevo."}, "swapContractDataDisabledErrorTitle": {"message": "Los datos de contrato no se habilitaron en su Ledger"}, "swapCustom": {"message": "personalizado"}, "swapDecentralizedExchange": {"message": "Intercambio descentralizado"}, "swapDirectContract": {"message": "Contrato directo"}, "swapEditLimit": {"message": "<PERSON>ar l<PERSON>"}, "swapEnableDescription": {"message": "Esta acción es obligatoria y le da permiso a MetaMask para canjear su $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Esto será $1 por intercambiar", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEstimatedNetworkFees": {"message": "Tarifas de red estimadas"}, "swapEstimatedNetworkFeesInfo": {"message": "Un estimado de la tarifa de red que se usará para realizar el intercambio. El monto real puede cambiar según las condiciones de la red."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "<PERSON><PERSON><PERSON> ocurrir fallas en las transacciones, por lo que estamos aquí para ayudarlo. Si el problema persiste, comuníquese con nuestro soporte al cliente al $1 para recibir ayuda adicional.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "<PERSON><PERSON><PERSON> al canjear"}, "swapFetchingQuotes": {"message": "Recuperando cotizaciones"}, "swapFetchingQuotesErrorDescription": {"message": "Se produjo un error. Vuelva a intentarlo o, si el error persiste, póngase en contacto con el soporte al cliente."}, "swapFetchingQuotesErrorTitle": {"message": "Error al capturar cotizaciones"}, "swapFromTo": {"message": "El canje de $1 por $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Las tarifas de gas son estimadas y fluctuarán en función del tráfico de la red y la complejidad de las transacciones."}, "swapGasFeesLearnMore": {"message": "Más información sobre las tarifas de gas"}, "swapGasFeesSplit": {"message": "Las tarifas de gas de la pantalla anterior se dividen entre estas dos transacciones."}, "swapGasFeesSummary": {"message": "Las tarifas de gas se pagan a los mineros de criptomonedas que procesan transacciones en la red $1. MetaMask no se beneficia de las tarifas de gas.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapIncludesMMFee": {"message": "Incluye una tasa de MetaMask del $1%.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapMaxSlippage": {"message": "<PERSON><PERSON><PERSON>"}, "swapMetaMaskFee": {"message": "Ta<PERSON><PERSON> de MetaMask"}, "swapMetaMaskFeeDescription": {"message": "Buscamos el mejor precio en las fuentes de liquidez más importantes, todo el tiempo. Se incorpora de manera automática a esta cotización una cuota del $1 %.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 cotizaciones.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Cotizaciones nuevas en $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapOnceTransactionHasProcess": {"message": "Su $1 se agregará a la cuenta una vez que se procese esta transacción.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Está por canjear $1 $2 (~$3) por $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Diferencia de precio de ~$1 %", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "No se pudo determinar el impacto sobre el precio debido a la falta de datos de los precios del mercado. Antes de realizar el canje, confirme que está de acuerdo con la cantidad de tokens que está a punto de recibir."}, "swapPriceUnavailableTitle": {"message": "Antes de continuar, verifique su tasa"}, "swapProcessing": {"message": "Procesamiento"}, "swapQuoteDetails": {"message": "Detalles de cotización"}, "swapQuoteSource": {"message": "Fuente de la cotización"}, "swapQuotesExpiredErrorDescription": {"message": "Solicite cotizaciones nuevas para tener los costos más recientes."}, "swapQuotesExpiredErrorTitle": {"message": "Tiempo de espera de cotizaciones"}, "swapQuotesNotAvailableErrorDescription": {"message": "Intente ajustar la configuración de monto o desfase y vuelva a intentarlo."}, "swapQuotesNotAvailableErrorTitle": {"message": "No hay cotizaciones disponibles"}, "swapRate": {"message": "<PERSON><PERSON><PERSON>"}, "swapReceiving": {"message": "Recibiendo"}, "swapReceivingInfoTooltip": {"message": "Este es un valor estimado. El monto exacto depende del desfase."}, "swapRequestForQuotation": {"message": "Solicitud de cotización"}, "swapSelect": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "swapSelectAQuote": {"message": "Seleccionar una cotización"}, "swapSelectAToken": {"message": "Seleccionar un token"}, "swapSelectQuotePopoverDescription": {"message": "A continuación se muestran todas las cotizaciones recopiladas de diversas fuentes de liquidez."}, "swapSource": {"message": "Fuente de liquidez"}, "swapSuggested": {"message": "Swap sugerido"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Los swaps son transacciones complejas y urgentes. Recomendamos esta tarifa de gas para lograr un buen equilibrio entre el costo y la garantía de un swap exitoso."}, "swapSwapFrom": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "swapSwapSwitch": {"message": "Intercambiar de y a tokens"}, "swapSwapTo": {"message": "Canjear a"}, "swapToConfirmWithHwWallet": {"message": "para confirmar con la cartera de hardware"}, "swapTokenAvailable": {"message": "Su $1 se agregó a la cuenta.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "No se pudo recuperar su saldo de $1", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenToToken": {"message": "Canjear $1 por $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTooManyDecimalsError": {"message": "$1 permite hasta $2 decimales", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transacción completa"}, "swapTwoTransactions": {"message": "2 transacciones"}, "swapUnknown": {"message": "Desconocido"}, "swapZeroSlippage": {"message": "0 % de desfase"}, "swapsMaxSlippage": {"message": "Tolerancia de desfase"}, "swapsViewInActivity": {"message": "Ver en actividad"}, "switchEthereumChainConfirmationDescription": {"message": "Esto cambiará la red seleccionada en MetaMask por una red agregada con anterioridad:"}, "switchEthereumChainConfirmationTitle": {"message": "¿Le permite a este sitio cambiar la red?"}, "switchNetwork": {"message": "Cambiar red"}, "switchNetworks": {"message": "Cambiar redes"}, "switchToThisAccount": {"message": "Cambiar a esta cuenta"}, "switchingNetworksCancelsPendingConfirmations": {"message": "El cambio de red cancelará todas las confirmaciones pendientes"}, "symbol": {"message": "Símbolo"}, "symbolBetweenZeroTwelve": {"message": "El símbolo debe tener 11 caracteres o menos."}, "tenPercentIncreased": {"message": "10% de aumento"}, "terms": {"message": "T<PERSON><PERSON><PERSON>s de uso"}, "termsOfService": {"message": "Términos de servicio"}, "time": {"message": "Tiempo"}, "to": {"message": "Para"}, "toAddress": {"message": "Para: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "token": {"message": "Token"}, "tokenAlreadyAdded": {"message": "Ya se agregó el token."}, "tokenContractAddress": {"message": "Dirección de contrato de token"}, "tokenDecimalFetchFailed": {"message": "Se requieren los decimales del token."}, "tokenId": {"message": "ID del token"}, "tokenSymbol": {"message": "Símbolo del token"}, "tooltipApproveButton": {"message": "Co<PERSON><PERSON><PERSON>"}, "total": {"message": "Total"}, "transaction": {"message": "transacción"}, "transactionCancelAttempted": {"message": "Se intentó cancelar la transacción con una tarifa de gas de $1 en $2"}, "transactionCancelSuccess": {"message": "La transacción se canceló correctamente en $2"}, "transactionConfirmed": {"message": "La transacción se confirmó en $2."}, "transactionCreated": {"message": "La transacción se creó con un valor de $1 en $2."}, "transactionDetailGasHeading": {"message": "Tarifa estimada de gas"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Monto + cargos"}, "transactionDropped": {"message": "La transacción se abandonó en $2."}, "transactionError": {"message": "Error de transacción. Excepción generada en el código de contrato."}, "transactionErrorNoContract": {"message": "Intentando llamar a una función en una dirección sin contrato."}, "transactionErrored": {"message": "La transacción encontró un error."}, "transactionHistoryBaseFee": {"message": "Tarifa base (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Tarifa total de gas L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Límite de gas L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Precio de gas L2"}, "transactionHistoryMaxFeePerGas": {"message": "Tarifa máxima por gas"}, "transactionHistoryPriorityFee": {"message": "Tarifa de prioridad (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Tarifa total de gas"}, "transactionResubmitted": {"message": "Transacción reenviada con la tarifa de gas aumentada a $1 en $2"}, "transactionSubmitted": {"message": "Transacción enviada con una tarifa de gas de $1 en $2."}, "transactionUpdated": {"message": "La transacción se actualizó en $2."}, "transfer": {"message": "Transferir"}, "transferFrom": {"message": "Transferir desde"}, "troubleConnectingToWallet": {"message": "Tuvimos problemas al conectar su $1. Pruebe revisar $2 e inténtelo de nuevo.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "tryAgain": {"message": "Vuelva a intentarlo"}, "turnOnTokenDetection": {"message": "Activar la detección mejorada de tokens"}, "twelveHrTitle": {"message": "12 horas:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "No aprobado"}, "units": {"message": "unidades"}, "unknown": {"message": "Desconocido"}, "unknownQrCode": {"message": "Error: No se pudo identificar ese código QR"}, "unlimited": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unlock": {"message": "Desb<PERSON>que<PERSON>"}, "unrecognizedChain": {"message": "No se reconoce esta red personalizada. Antes de continuar, le recomendamos que $1", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "El envío de tokens coleccionables (ERC-721) no se admite actualmente", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "urlErrorMsg": {"message": "Las direcciones URL requieren el prefijo HTTP/HTTPS adecuado."}, "useNftDetection": {"message": "Autodetectar NFT"}, "usePhishingDetection": {"message": "Usar detección de phishing"}, "usePhishingDetectionDescription": {"message": "Mostrar una advertencia respecto de los dominios de phishing dirigidos a los usuarios de Ethereum"}, "usedByClients": {"message": "Usado por una variedad de clientes distintos"}, "userName": {"message": "Nombre de usuario"}, "viewContact": {"message": "Ver contacto"}, "viewMore": {"message": "<PERSON>er más"}, "viewOnBlockExplorer": {"message": "Ver en el explorador de bloques"}, "viewOnCustomBlockExplorer": {"message": "Ver $1 en $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Ver $1 en Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnOpensea": {"message": "Ver en Opensea"}, "viewinExplorer": {"message": "Ver $1 en el explorador", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitWebSite": {"message": "Visite nuestro sitio web"}, "walletConnectionGuide": {"message": "nuestra guía de conexión de la cartera de hardware"}, "web3ShimUsageNotification": {"message": "Parece que el sitio web actual intentó utilizar la API de window.web3 que se eliminó. Si el sitio no funciona, haga clic en $1 para obtener más información.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "welcomeBack": {"message": "¡Bienvenido de nuevo!"}, "welcomeToMetaMask": {"message": "Comencemos"}, "whatsThis": {"message": "¿Qué es esto?"}, "youNeedToAllowCameraAccess": {"message": "Necesita permitir el acceso a la cámara para usar esta función."}, "yourPrivateSeedPhrase": {"message": "Su frase secreta de recuperación privada"}}