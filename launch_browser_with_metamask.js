const { chromium } = require('playwright');
const path = require('path');

async function launchBrowserWithMetaMask() {
    // MetaMask扩展路径
    const extensionPath = path.resolve('C:/Users/<USER>/AppData/Local/Temp/metamask_extension');
    
    console.log('Extension path:', extensionPath);
    
    // 启动带有MetaMask扩展的浏览器
    const context = await chromium.launchPersistentContext('', {
        headless: false,
        args: [
            `--disable-extensions-except=${extensionPath}`,
            `--load-extension=${extensionPath}`,
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    });
    
    console.log('Browser launched with MetaMask extension');
    
    // 等待一下让扩展加载
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 创建新页面
    const page = await context.newPage();
    
    // 导航到支付页面
    await page.goto('http://127.0.0.1:8000/orders/ORD2025072608FE2600/pay/');
    
    console.log('Navigated to payment page');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    console.log('Page loaded successfully');
    
    return { context, page };
}

// 运行函数
launchBrowserWithMetaMask().catch(console.error);
