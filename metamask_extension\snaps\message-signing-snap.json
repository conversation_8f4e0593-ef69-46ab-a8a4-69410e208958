{"snapId": "npm:@metamask/message-signing-snap", "manifest": {"version": "1.1.2", "description": "Provides public key and message signing used for signing in with MetaMask", "proposedName": "Sign in with MetaMask", "repository": {"type": "git", "url": "https://github.com/MetaMask/message-signing-snap.git"}, "source": {"shasum": "HWZ8hcJfaFq0sleE9d/eGuHTQou3kaBneWbSa24ubls=", "location": {"npm": {"filePath": "dist/bundle.js", "iconPath": "images/icon.svg", "packageName": "@metamask/message-signing-snap", "registry": "https://registry.npmjs.org/"}}}, "initialConnections": {"https://portfolio.metamask.io": {}, "https://portfolio-builds.metafi-dev.codefi.network": {}, "https://docs.metamask.io": {}, "https://developer.metamask.io": {}, "npm:@metamask/gator-permissions-snap": {}}, "initialPermissions": {"snap_getEntropy": {}, "endowment:rpc": {"dapps": true, "snaps": true}}, "platformVersion": "6.19.0", "manifestVersion": "0.1"}, "files": [{"path": "images/icon.svg", "value": "<svg width=\"500\" height=\"500\" viewBox=\"0 0 500 500\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<rect width=\"500\" height=\"500\" fill=\"black\"/>\n<g filter=\"url(#filter0_f_8_91)\">\n<path d=\"M249.444 373.519C249.444 373.519 364.118 343.192 364.118 257.188L378.333 248.251L364.118 239.314L378.333 230.378L364.118 221.441V203.568V185.695V149.948V114.202L301.639 132.596C267.566 142.628 231.323 142.628 197.25 132.596L134.771 114.202V149.948V167.821V185.695V203.568V221.441L120.556 230.378L134.771 239.314L120.556 248.251L134.771 257.188C134.771 343.192 249.444 373.519 249.444 373.519Z\" fill=\"#F1CA77\"/>\n</g>\n<path d=\"M249.445 406.215C249.445 406.215 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888L249.445 162.179L134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 406.215 249.445 406.215Z\" fill=\"#233447\"/>\n<path d=\"M249.445 399.107C249.445 399.107 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888L249.445 162.179L134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 399.107 249.445 399.107Z\" fill=\"#123AF0\"/>\n<path d=\"M249.445 392C249.445 392 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888L249.445 162.179L134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 392 249.445 392Z\" fill=\"#CD6116\"/>\n<path d=\"M249.445 382.049C249.445 382.049 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888L249.445 162.179L134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 382.049 249.445 382.049Z\" fill=\"#E4751F\"/>\n<path d=\"M249.445 370.676C249.445 370.676 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888L249.445 162.179L134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 370.676 249.445 370.676Z\" fill=\"#F6851B\"/>\n<path d=\"M249.445 352.196C249.445 352.196 364.118 348.879 364.118 262.874L378.333 253.937L364.118 245.001L378.333 236.064L364.118 227.127V209.254V191.381V155.634V119.888C290.105 147.184 208.784 147.184 134.771 119.888V155.634V173.508V191.381V209.254V227.127L120.556 236.064L134.771 245.001L120.556 253.937L134.771 262.874C134.771 348.879 249.445 352.196 249.445 352.196Z\" fill=\"url(#paint0_linear_8_91)\"/>\n<defs>\n<filter id=\"filter0_f_8_91\" x=\"94.9674\" y=\"88.6133\" width=\"308.954\" height=\"310.494\" filterUnits=\"userSpaceOnUse\" color-interpolation-filters=\"sRGB\">\n<feFlood flood-opacity=\"0\" result=\"BackgroundImageFix\"/>\n<feBlend mode=\"normal\" in=\"SourceGraphic\" in2=\"BackgroundImageFix\" result=\"shape\"/>\n<feGaussianBlur stdDeviation=\"12.7941\" result=\"effect1_foregroundBlur_8_91\"/>\n</filter>\n<linearGradient id=\"paint0_linear_8_91\" x1=\"144.012\" y1=\"132.09\" x2=\"425.482\" y2=\"446.967\" gradientUnits=\"userSpaceOnUse\">\n<stop stop-color=\"#F1CA77\"/>\n<stop offset=\"1\" stop-color=\"#F6851B\"/>\n</linearGradient>\n</defs>\n</svg>\n"}, {"path": "dist/bundle.js", "value": "(()=>{var e={251:(e,t)=>{t.read=function(e,t,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,c=u>>1,d=-7,f=r?i-1:0,l=r?-1:1,h=e[t+f];for(f+=l,s=h&(1<<-d)-1,h>>=-d,d+=a;d>0;s=256*s+e[t+f],f+=l,d-=8);for(o=s&(1<<-d)-1,s>>=-d,d+=n;d>0;o=256*o+e[t+f],f+=l,d-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(h?-1:1);o+=Math.pow(2,n),s-=c}return(h?-1:1)*o*Math.pow(2,s-n)},t.write=function(e,t,r,n,i,s){var o,a,u,c=8*s-i-1,d=(1<<c)-1,f=d>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:s-1,p=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=d):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),(t+=o+f>=1?l/u:l*Math.pow(2,1-f))*u>=2&&(o++,u/=2),o+f>=d?(a=0,o=d):o+f>=1?(a=(t*u-1)*Math.pow(2,i),o+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+h]=255&a,h+=p,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;e[r+h]=255&o,h+=p,o/=256,c-=8);e[r+h-p]|=128*g}},287:(e,t,r)=>{\"use strict\";const n=r(526),i=r(251),s=\"function\"==typeof Symbol&&\"function\"==typeof Symbol.for?Symbol.for(\"nodejs.util.inspect.custom\"):null;t.hp=u,t.IS=50;const o=2147483647;function a(e){if(e>o)throw new RangeError('The value \"'+e+'\" is invalid for option \"size\"');const t=new Uint8Array(e);return Object.setPrototypeOf(t,u.prototype),t}function u(e,t,r){if(\"number\"==typeof e){if(\"string\"==typeof t)throw new TypeError('The \"string\" argument must be of type string. Received type number');return f(e)}return c(e,t,r)}function c(e,t,r){if(\"string\"==typeof e)return function(e,t){\"string\"==typeof t&&\"\"!==t||(t=\"utf8\");if(!u.isEncoding(t))throw new TypeError(\"Unknown encoding: \"+t);const r=0|g(e,t);let n=a(r);const i=n.write(e,t);i!==r&&(n=n.slice(0,i));return n}(e,t);if(ArrayBuffer.isView(e))return function(e){if(G(e,Uint8Array)){const t=new Uint8Array(e);return h(t.buffer,t.byteOffset,t.byteLength)}return l(e)}(e);if(null==e)throw new TypeError(\"The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type \"+typeof e);if(G(e,ArrayBuffer)||e&&G(e.buffer,ArrayBuffer))return h(e,t,r);if(\"undefined\"!=typeof SharedArrayBuffer&&(G(e,SharedArrayBuffer)||e&&G(e.buffer,SharedArrayBuffer)))return h(e,t,r);if(\"number\"==typeof e)throw new TypeError('The \"value\" argument must not be of type number. Received type number');const n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return u.from(n,t,r);const i=function(e){if(u.isBuffer(e)){const t=0|p(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}if(void 0!==e.length)return\"number\"!=typeof e.length||J(e.length)?a(0):l(e);if(\"Buffer\"===e.type&&Array.isArray(e.data))return l(e.data)}(e);if(i)return i;if(\"undefined\"!=typeof Symbol&&null!=Symbol.toPrimitive&&\"function\"==typeof e[Symbol.toPrimitive])return u.from(e[Symbol.toPrimitive](\"string\"),t,r);throw new TypeError(\"The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type \"+typeof e)}function d(e){if(\"number\"!=typeof e)throw new TypeError('\"size\" argument must be of type number');if(e<0)throw new RangeError('The value \"'+e+'\" is invalid for option \"size\"')}function f(e){return d(e),a(e<0?0:0|p(e))}function l(e){const t=e.length<0?0:0|p(e.length),r=a(t);for(let n=0;n<t;n+=1)r[n]=255&e[n];return r}function h(e,t,r){if(t<0||e.byteLength<t)throw new RangeError('\"offset\" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw new RangeError('\"length\" is outside of buffer bounds');let n;return n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),Object.setPrototypeOf(n,u.prototype),n}function p(e){if(e>=o)throw new RangeError(\"Attempt to allocate Buffer larger than maximum size: 0x\"+o.toString(16)+\" bytes\");return 0|e}function g(e,t){if(u.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||G(e,ArrayBuffer))return e.byteLength;if(\"string\"!=typeof e)throw new TypeError('The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);const r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let i=!1;for(;;)switch(t){case\"ascii\":case\"latin1\":case\"binary\":return r;case\"utf8\":case\"utf-8\":return K(e).length;case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return 2*r;case\"hex\":return r>>>1;case\"base64\":return H(e).length;default:if(i)return n?-1:K(e).length;t=(\"\"+t).toLowerCase(),i=!0}}function y(e,t,r){let n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return\"\";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return\"\";if((r>>>=0)<=(t>>>=0))return\"\";for(e||(e=\"utf8\");;)switch(e){case\"hex\":return T(this,t,r);case\"utf8\":case\"utf-8\":return I(this,t,r);case\"ascii\":return S(this,t,r);case\"latin1\":case\"binary\":return O(this,t,r);case\"base64\":return k(this,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return N(this,t,r);default:if(n)throw new TypeError(\"Unknown encoding: \"+e);e=(e+\"\").toLowerCase(),n=!0}}function m(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function w(e,t,r,n,i){if(0===e.length)return-1;if(\"string\"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),J(r=+r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if(\"string\"==typeof t&&(t=u.from(t,n)),u.isBuffer(t))return 0===t.length?-1:v(e,t,r,n,i);if(\"number\"==typeof t)return t&=255,\"function\"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):v(e,[t],r,n,i);throw new TypeError(\"val must be string, number or Buffer\")}function v(e,t,r,n,i){let s,o=1,a=e.length,u=t.length;if(void 0!==n&&(\"ucs2\"===(n=String(n).toLowerCase())||\"ucs-2\"===n||\"utf16le\"===n||\"utf-16le\"===n)){if(e.length<2||t.length<2)return-1;o=2,a/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){let n=-1;for(s=r;s<a;s++)if(c(e,s)===c(t,-1===n?0:s-n)){if(-1===n&&(n=s),s-n+1===u)return n*o}else-1!==n&&(s-=s-n),n=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){let r=!0;for(let n=0;n<u;n++)if(c(e,s+n)!==c(t,n)){r=!1;break}if(r)return s}return-1}function b(e,t,r,n){r=Number(r)||0;const i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;const s=t.length;let o;for(n>s/2&&(n=s/2),o=0;o<n;++o){const n=parseInt(t.substr(2*o,2),16);if(J(n))return o;e[r+o]=n}return o}function _(e,t,r,n){return W(K(t,e.length-r),e,r,n)}function E(e,t,r,n){return W(function(e){const t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function x(e,t,r,n){return W(H(t),e,r,n)}function A(e,t,r,n){return W(function(e,t){let r,n,i;const s=[];for(let o=0;o<e.length&&!((t-=2)<0);++o)r=e.charCodeAt(o),n=r>>8,i=r%256,s.push(i),s.push(n);return s}(t,e.length-r),e,r,n)}function k(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function I(e,t,r){r=Math.min(e.length,r);const n=[];let i=t;for(;i<r;){const t=e[i];let s=null,o=t>239?4:t>223?3:t>191?2:1;if(i+o<=r){let r,n,a,u;switch(o){case 1:t<128&&(s=t);break;case 2:r=e[i+1],128==(192&r)&&(u=(31&t)<<6|63&r,u>127&&(s=u));break;case 3:r=e[i+1],n=e[i+2],128==(192&r)&&128==(192&n)&&(u=(15&t)<<12|(63&r)<<6|63&n,u>2047&&(u<55296||u>57343)&&(s=u));break;case 4:r=e[i+1],n=e[i+2],a=e[i+3],128==(192&r)&&128==(192&n)&&128==(192&a)&&(u=(15&t)<<18|(63&r)<<12|(63&n)<<6|63&a,u>65535&&u<1114112&&(s=u))}}null===s?(s=65533,o=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=o}return function(e){const t=e.length;if(t<=B)return String.fromCharCode.apply(String,e);let r=\"\",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=B));return r}(n)}u.TYPED_ARRAY_SUPPORT=function(){try{const e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),u.TYPED_ARRAY_SUPPORT||\"undefined\"==typeof console||\"function\"!=typeof console.error||console.error(\"This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.\"),Object.defineProperty(u.prototype,\"parent\",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,\"offset\",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(e,t,r){return c(e,t,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(e,t,r){return function(e,t,r){return d(e),e<=0?a(e):void 0!==t?\"string\"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)}(e,t,r)},u.allocUnsafe=function(e){return f(e)},u.allocUnsafeSlow=function(e){return f(e)},u.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==u.prototype},u.compare=function(e,t){if(G(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),G(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(e)||!u.isBuffer(t))throw new TypeError('The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,n=t.length;for(let i=0,s=Math.min(r,n);i<s;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},u.isEncoding=function(e){switch(String(e).toLowerCase()){case\"hex\":case\"utf8\":case\"utf-8\":case\"ascii\":case\"latin1\":case\"binary\":case\"base64\":case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return!0;default:return!1}},u.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('\"list\" argument must be an Array of Buffers');if(0===e.length)return u.alloc(0);let r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;const n=u.allocUnsafe(t);let i=0;for(r=0;r<e.length;++r){let t=e[r];if(G(t,Uint8Array))i+t.length>n.length?(u.isBuffer(t)||(t=u.from(t)),t.copy(n,i)):Uint8Array.prototype.set.call(n,t,i);else{if(!u.isBuffer(t))throw new TypeError('\"list\" argument must be an Array of Buffers');t.copy(n,i)}i+=t.length}return n},u.byteLength=g,u.prototype._isBuffer=!0,u.prototype.swap16=function(){const e=this.length;if(e%2!=0)throw new RangeError(\"Buffer size must be a multiple of 16-bits\");for(let t=0;t<e;t+=2)m(this,t,t+1);return this},u.prototype.swap32=function(){const e=this.length;if(e%4!=0)throw new RangeError(\"Buffer size must be a multiple of 32-bits\");for(let t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},u.prototype.swap64=function(){const e=this.length;if(e%8!=0)throw new RangeError(\"Buffer size must be a multiple of 64-bits\");for(let t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},u.prototype.toString=function(){const e=this.length;return 0===e?\"\":0===arguments.length?I(this,0,e):y.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(e){if(!u.isBuffer(e))throw new TypeError(\"Argument must be a Buffer\");return this===e||0===u.compare(this,e)},u.prototype.inspect=function(){let e=\"\";const r=t.IS;return e=this.toString(\"hex\",0,r).replace(/(.{2})/g,\"$1 \").trim(),this.length>r&&(e+=\" ... \"),\"<Buffer \"+e+\">\"},s&&(u.prototype[s]=u.prototype.inspect),u.prototype.compare=function(e,t,r,n,i){if(G(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(e))throw new TypeError('The \"target\" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError(\"out of range index\");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;let s=(i>>>=0)-(n>>>=0),o=(r>>>=0)-(t>>>=0);const a=Math.min(s,o),c=this.slice(n,i),d=e.slice(t,r);for(let e=0;e<a;++e)if(c[e]!==d[e]){s=c[e],o=d[e];break}return s<o?-1:o<s?1:0},u.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},u.prototype.indexOf=function(e,t,r){return w(this,e,t,r,!0)},u.prototype.lastIndexOf=function(e,t,r){return w(this,e,t,r,!1)},u.prototype.write=function(e,t,r,n){if(void 0===t)n=\"utf8\",r=this.length,t=0;else if(void 0===r&&\"string\"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error(\"Buffer.write(string, encoding, offset[, length]) is no longer supported\");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n=\"utf8\")):(n=r,r=void 0)}const i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError(\"Attempt to write outside buffer bounds\");n||(n=\"utf8\");let s=!1;for(;;)switch(n){case\"hex\":return b(this,e,t,r);case\"utf8\":case\"utf-8\":return _(this,e,t,r);case\"ascii\":case\"latin1\":case\"binary\":return E(this,e,t,r);case\"base64\":return x(this,e,t,r);case\"ucs2\":case\"ucs-2\":case\"utf16le\":case\"utf-16le\":return A(this,e,t,r);default:if(s)throw new TypeError(\"Unknown encoding: \"+n);n=(\"\"+n).toLowerCase(),s=!0}},u.prototype.toJSON=function(){return{type:\"Buffer\",data:Array.prototype.slice.call(this._arr||this,0)}};const B=4096;function S(e,t,r){let n=\"\";r=Math.min(e.length,r);for(let i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function O(e,t,r){let n=\"\";r=Math.min(e.length,r);for(let i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function T(e,t,r){const n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);let i=\"\";for(let n=t;n<r;++n)i+=Y[e[n]];return i}function N(e,t,r){const n=e.slice(t,r);let i=\"\";for(let e=0;e<n.length-1;e+=2)i+=String.fromCharCode(n[e]+256*n[e+1]);return i}function R(e,t,r){if(e%1!=0||e<0)throw new RangeError(\"offset is not uint\");if(e+t>r)throw new RangeError(\"Trying to access beyond buffer length\")}function C(e,t,r,n,i,s){if(!u.isBuffer(e))throw new TypeError('\"buffer\" argument must be a Buffer instance');if(t>i||t<s)throw new RangeError('\"value\" argument is out of bounds');if(r+n>e.length)throw new RangeError(\"Index out of range\")}function Z(e,t,r,n,i){z(t,n,i,e,r,7);let s=Number(t&BigInt(4294967295));e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s,s>>=8,e[r++]=s;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,r}function U(e,t,r,n,i){z(t,n,i,e,r,7);let s=Number(t&BigInt(4294967295));e[r+7]=s,s>>=8,e[r+6]=s,s>>=8,e[r+5]=s,s>>=8,e[r+4]=s;let o=Number(t>>BigInt(32)&BigInt(4294967295));return e[r+3]=o,o>>=8,e[r+2]=o,o>>=8,e[r+1]=o,o>>=8,e[r]=o,r+8}function j(e,t,r,n,i,s){if(r+n>e.length)throw new RangeError(\"Index out of range\");if(r<0)throw new RangeError(\"Index out of range\")}function P(e,t,r,n,s){return t=+t,r>>>=0,s||j(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function L(e,t,r,n,s){return t=+t,r>>>=0,s||j(e,0,r,8),i.write(e,t,r,n,52,8),r+8}u.prototype.slice=function(e,t){const r=this.length;(e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);const n=this.subarray(e,t);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||R(e,t,this.length);let n=this[e],i=1,s=0;for(;++s<t&&(i*=256);)n+=this[e+s]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||R(e,t,this.length);let n=this[e+--t],i=1;for(;t>0&&(i*=256);)n+=this[e+--t]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(e,t){return e>>>=0,t||R(e,1,this.length),this[e]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(e,t){return e>>>=0,t||R(e,2,this.length),this[e]|this[e+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(e,t){return e>>>=0,t||R(e,2,this.length),this[e]<<8|this[e+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(e,t){return e>>>=0,t||R(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(e,t){return e>>>=0,t||R(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},u.prototype.readBigUInt64LE=X((function(e){D(e>>>=0,\"offset\");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||q(e,this.length-8);const n=t+256*this[++e]+65536*this[++e]+this[++e]*2**24,i=this[++e]+256*this[++e]+65536*this[++e]+r*2**24;return BigInt(n)+(BigInt(i)<<BigInt(32))})),u.prototype.readBigUInt64BE=X((function(e){D(e>>>=0,\"offset\");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||q(e,this.length-8);const n=t*2**24+65536*this[++e]+256*this[++e]+this[++e],i=this[++e]*2**24+65536*this[++e]+256*this[++e]+r;return(BigInt(n)<<BigInt(32))+BigInt(i)})),u.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||R(e,t,this.length);let n=this[e],i=1,s=0;for(;++s<t&&(i*=256);)n+=this[e+s]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},u.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||R(e,t,this.length);let n=t,i=1,s=this[e+--n];for(;n>0&&(i*=256);)s+=this[e+--n]*i;return i*=128,s>=i&&(s-=Math.pow(2,8*t)),s},u.prototype.readInt8=function(e,t){return e>>>=0,t||R(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},u.prototype.readInt16LE=function(e,t){e>>>=0,t||R(e,2,this.length);const r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt16BE=function(e,t){e>>>=0,t||R(e,2,this.length);const r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},u.prototype.readInt32LE=function(e,t){return e>>>=0,t||R(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},u.prototype.readInt32BE=function(e,t){return e>>>=0,t||R(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},u.prototype.readBigInt64LE=X((function(e){D(e>>>=0,\"offset\");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||q(e,this.length-8);const n=this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24);return(BigInt(n)<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+this[++e]*2**24)})),u.prototype.readBigInt64BE=X((function(e){D(e>>>=0,\"offset\");const t=this[e],r=this[e+7];void 0!==t&&void 0!==r||q(e,this.length-8);const n=(t<<24)+65536*this[++e]+256*this[++e]+this[++e];return(BigInt(n)<<BigInt(32))+BigInt(this[++e]*2**24+65536*this[++e]+256*this[++e]+r)})),u.prototype.readFloatLE=function(e,t){return e>>>=0,t||R(e,4,this.length),i.read(this,e,!0,23,4)},u.prototype.readFloatBE=function(e,t){return e>>>=0,t||R(e,4,this.length),i.read(this,e,!1,23,4)},u.prototype.readDoubleLE=function(e,t){return e>>>=0,t||R(e,8,this.length),i.read(this,e,!0,52,8)},u.prototype.readDoubleBE=function(e,t){return e>>>=0,t||R(e,8,this.length),i.read(this,e,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){C(this,e,t,r,Math.pow(2,8*r)-1,0)}let i=1,s=0;for(this[t]=255&e;++s<r&&(i*=256);)this[t+s]=e/i&255;return t+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){C(this,e,t,r,Math.pow(2,8*r)-1,0)}let i=r-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,1,255,0),this[t]=255&e,t+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeBigUInt64LE=X((function(e,t=0){return Z(this,e,t,BigInt(0),BigInt(\"0xffffffffffffffff\"))})),u.prototype.writeBigUInt64BE=X((function(e,t=0){return U(this,e,t,BigInt(0),BigInt(\"0xffffffffffffffff\"))})),u.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);C(this,e,t,r,n-1,-n)}let i=0,s=1,o=0;for(this[t]=255&e;++i<r&&(s*=256);)e<0&&0===o&&0!==this[t+i-1]&&(o=1),this[t+i]=(e/s|0)-o&255;return t+r},u.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){const n=Math.pow(2,8*r-1);C(this,e,t,r,n-1,-n)}let i=r-1,s=1,o=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===o&&0!==this[t+i+1]&&(o=1),this[t+i]=(e/s|0)-o&255;return t+r},u.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},u.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},u.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},u.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},u.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||C(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},u.prototype.writeBigInt64LE=X((function(e,t=0){return Z(this,e,t,-BigInt(\"0x8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))})),u.prototype.writeBigInt64BE=X((function(e,t=0){return U(this,e,t,-BigInt(\"0x8000000000000000\"),BigInt(\"0x7fffffffffffffff\"))})),u.prototype.writeFloatLE=function(e,t,r){return P(this,e,t,!0,r)},u.prototype.writeFloatBE=function(e,t,r){return P(this,e,t,!1,r)},u.prototype.writeDoubleLE=function(e,t,r){return L(this,e,t,!0,r)},u.prototype.writeDoubleBE=function(e,t,r){return L(this,e,t,!1,r)},u.prototype.copy=function(e,t,r,n){if(!u.isBuffer(e))throw new TypeError(\"argument should be a Buffer\");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError(\"targetStart out of bounds\");if(r<0||r>=this.length)throw new RangeError(\"Index out of range\");if(n<0)throw new RangeError(\"sourceEnd out of bounds\");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);const i=n-r;return this===e&&\"function\"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),i},u.prototype.fill=function(e,t,r,n){if(\"string\"==typeof e){if(\"string\"==typeof t?(n=t,t=0,r=this.length):\"string\"==typeof r&&(n=r,r=this.length),void 0!==n&&\"string\"!=typeof n)throw new TypeError(\"encoding must be a string\");if(\"string\"==typeof n&&!u.isEncoding(n))throw new TypeError(\"Unknown encoding: \"+n);if(1===e.length){const t=e.charCodeAt(0);(\"utf8\"===n&&t<128||\"latin1\"===n)&&(e=t)}}else\"number\"==typeof e?e&=255:\"boolean\"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw new RangeError(\"Out of range index\");if(r<=t)return this;let i;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),\"number\"==typeof e)for(i=t;i<r;++i)this[i]=e;else{const s=u.isBuffer(e)?e:u.from(e,n),o=s.length;if(0===o)throw new TypeError('The value \"'+e+'\" is invalid for argument \"value\"');for(i=0;i<r-t;++i)this[i+t]=s[i%o]}return this};const $={};function M(e,t,r){$[e]=class extends r{constructor(){super(),Object.defineProperty(this,\"message\",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,\"code\",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function F(e){let t=\"\",r=e.length;const n=\"-\"===e[0]?1:0;for(;r>=n+4;r-=3)t=`_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function z(e,t,r,n,i,s){if(e>r||e<t){const n=\"bigint\"==typeof t?\"n\":\"\";let i;throw i=s>3?0===t||t===BigInt(0)?`>= 0${n} and < 2${n} ** ${8*(s+1)}${n}`:`>= -(2${n} ** ${8*(s+1)-1}${n}) and < 2 ** ${8*(s+1)-1}${n}`:`>= ${t}${n} and <= ${r}${n}`,new $.ERR_OUT_OF_RANGE(\"value\",i,e)}!function(e,t,r){D(t,\"offset\"),void 0!==e[t]&&void 0!==e[t+r]||q(t,e.length-(r+1))}(n,i,s)}function D(e,t){if(\"number\"!=typeof e)throw new $.ERR_INVALID_ARG_TYPE(t,\"number\",e)}function q(e,t,r){if(Math.floor(e)!==e)throw D(e,r),new $.ERR_OUT_OF_RANGE(r||\"offset\",\"an integer\",e);if(t<0)throw new $.ERR_BUFFER_OUT_OF_BOUNDS;throw new $.ERR_OUT_OF_RANGE(r||\"offset\",`>= ${r?1:0} and <= ${t}`,e)}M(\"ERR_BUFFER_OUT_OF_BOUNDS\",(function(e){return e?`${e} is outside of buffer bounds`:\"Attempt to access memory outside buffer bounds\"}),RangeError),M(\"ERR_INVALID_ARG_TYPE\",(function(e,t){return`The \"${e}\" argument must be of type number. Received type ${typeof t}`}),TypeError),M(\"ERR_OUT_OF_RANGE\",(function(e,t,r){let n=`The value of \"${e}\" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>2**32?i=F(String(r)):\"bigint\"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=F(i)),i+=\"n\"),n+=` It must be ${t}. Received ${i}`,n}),RangeError);const V=/[^+/0-9A-Za-z-_]/g;function K(e,t){let r;t=t||1/0;const n=e.length;let i=null;const s=[];for(let o=0;o<n;++o){if(r=e.charCodeAt(o),r>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(o+1===n){(t-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&s.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;s.push(r)}else if(r<2048){if((t-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error(\"Invalid code point\");if((t-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return s}function H(e){return n.toByteArray(function(e){if((e=(e=e.split(\"=\")[0]).trim().replace(V,\"\")).length<2)return\"\";for(;e.length%4!=0;)e+=\"=\";return e}(e))}function W(e,t,r,n){let i;for(i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function G(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function J(e){return e!=e}const Y=function(){const e=\"0123456789abcdef\",t=new Array(256);for(let r=0;r<16;++r){const n=16*r;for(let i=0;i<16;++i)t[n+i]=e[r]+e[i]}return t}();function X(e){return\"undefined\"==typeof BigInt?Q:e}function Q(){throw new Error(\"BigInt not supported\")}},463:e=>{e.exports=o,o.default=o,o.stable=d,o.stableStringify=d;var t=\"[...]\",r=\"[Circular]\",n=[],i=[];function s(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function o(e,t,r,o){var a;void 0===o&&(o=s()),u(e,\"\",0,[],void 0,0,o);try{a=0===i.length?JSON.stringify(e,t,r):JSON.stringify(e,l(t),r)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==n.length;){var c=n.pop();4===c.length?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return a}function a(e,t,r,s){var o=Object.getOwnPropertyDescriptor(s,r);void 0!==o.get?o.configurable?(Object.defineProperty(s,r,{value:e}),n.push([s,r,t,o])):i.push([t,r,e]):(s[r]=e,n.push([s,r,t]))}function u(e,n,i,s,o,c,d){var f;if(c+=1,\"object\"==typeof e&&null!==e){for(f=0;f<s.length;f++)if(s[f]===e)return void a(r,e,n,o);if(void 0!==d.depthLimit&&c>d.depthLimit)return void a(t,e,n,o);if(void 0!==d.edgesLimit&&i+1>d.edgesLimit)return void a(t,e,n,o);if(s.push(e),Array.isArray(e))for(f=0;f<e.length;f++)u(e[f],f,f,s,e,c,d);else{var l=Object.keys(e);for(f=0;f<l.length;f++){var h=l[f];u(e[h],h,f,s,e,c,d)}}s.pop()}}function c(e,t){return e<t?-1:e>t?1:0}function d(e,t,r,o){void 0===o&&(o=s());var a,u=f(e,\"\",0,[],void 0,0,o)||e;try{a=0===i.length?JSON.stringify(u,t,r):JSON.stringify(u,l(t),r)}catch(e){return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\")}finally{for(;0!==n.length;){var c=n.pop();4===c.length?Object.defineProperty(c[0],c[1],c[3]):c[0][c[1]]=c[2]}}return a}function f(e,i,s,o,u,d,l){var h;if(d+=1,\"object\"==typeof e&&null!==e){for(h=0;h<o.length;h++)if(o[h]===e)return void a(r,e,i,u);try{if(\"function\"==typeof e.toJSON)return}catch(e){return}if(void 0!==l.depthLimit&&d>l.depthLimit)return void a(t,e,i,u);if(void 0!==l.edgesLimit&&s+1>l.edgesLimit)return void a(t,e,i,u);if(o.push(e),Array.isArray(e))for(h=0;h<e.length;h++)f(e[h],h,h,o,e,d,l);else{var p={},g=Object.keys(e).sort(c);for(h=0;h<g.length;h++){var y=g[h];f(e[y],y,h,o,e,d,l),p[y]=e[y]}if(void 0===u)return p;n.push([u,i,e]),u[i]=p}o.pop()}}function l(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(i.length>0)for(var n=0;n<i.length;n++){var s=i[n];if(s[1]===t&&s[0]===r){r=s[2],i.splice(n,1);break}}return e.call(this,t,r)}}},526:(e,t)=>{\"use strict\";t.byteLength=function(e){var t=a(e),r=t[0],n=t[1];return 3*(r+n)/4-n},t.toByteArray=function(e){var t,r,s=a(e),o=s[0],u=s[1],c=new i(function(e,t,r){return 3*(t+r)/4-r}(0,o,u)),d=0,f=u>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[d++]=t>>16&255,c[d++]=t>>8&255,c[d++]=255&t;2===u&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[d++]=255&t);1===u&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[d++]=t>>8&255,c[d++]=255&t);return c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,s=[],o=16383,a=0,c=n-i;a<c;a+=o)s.push(u(e,a,a+o>c?c:a+o));1===i?(t=e[n-1],s.push(r[t>>2]+r[t<<4&63]+\"==\")):2===i&&(t=(e[n-2]<<8)+e[n-1],s.push(r[t>>10]+r[t>>4&63]+r[t<<2&63]+\"=\"));return s.join(\"\")};for(var r=[],n=[],i=\"undefined\"!=typeof Uint8Array?Uint8Array:Array,s=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",o=0;o<64;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function a(e){var t=e.length;if(t%4>0)throw new Error(\"Invalid string. Length must be a multiple of 4\");var r=e.indexOf(\"=\");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function u(e,t,n){for(var i,s,o=[],a=t;a<n;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(r[(s=i)>>18&63]+r[s>>12&63]+r[s>>6&63]+r[63&s]);return o.join(\"\")}n[\"-\".charCodeAt(0)]=62,n[\"_\".charCodeAt(0)]=63}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var n={};(()=>{\"use strict\";r.r(n),r.d(n,{INTERNAL_ORIGINS:()=>Oo,getSaltByOrigin:()=>To,onRpcRequest:()=>No});var e={};function t(e){return Boolean(e)&&\"object\"==typeof e&&!Array.isArray(e)}r.r(e),r.d(e,{aK:()=>ri,e8:()=>Mn,DO:()=>$n,dJ:()=>ni,OG:()=>ii,My:()=>zn,Ph:()=>Wn,lX:()=>Gn,Id:()=>Qn,fg:()=>ai,qj:()=>Xn,aT:()=>Hn,r4:()=>ti,aY:()=>Ln,x:()=>di,lq:()=>Jn,z:()=>Yn,zW:()=>Dn,Q5:()=>ci});const i=(e,t)=>Object.hasOwnProperty.call(e,t);var s;!function(e){e[e.Null=4]=\"Null\",e[e.Comma=1]=\"Comma\",e[e.Wrapper=1]=\"Wrapper\",e[e.True=4]=\"True\",e[e.False=5]=\"False\",e[e.Quote=1]=\"Quote\",e[e.Colon=1]=\"Colon\",e[e.Date=24]=\"Date\"}(s=s||(s={}));var o=r(463);class a extends TypeError{constructor(e,t){let r;const{message:n,explanation:i,...s}=e,{path:o}=e,a=0===o.length?n:`At path: ${o.join(\".\")} -- ${n}`;super(i??a),null!=i&&(this.cause=a),Object.assign(this,s),this.name=this.constructor.name,this.failures=()=>r??(r=[e,...t()])}}function u(e){return\"object\"==typeof e&&null!==e}function c(e){return\"symbol\"==typeof e?e.toString():\"string\"==typeof e?JSON.stringify(e):`${e}`}function d(e,t,r,n){if(!0===e)return;!1===e?e={}:\"string\"==typeof e&&(e={message:e});const{path:i,branch:s}=t,{type:o}=r,{refinement:a,message:u=`Expected a value of type \\`${o}\\`${a?` with refinement \\`${a}\\``:\"\"}, but received: \\`${c(n)}\\``}=e;return{value:n,type:o,refinement:a,key:i[i.length-1],path:i,branch:s,...e,message:u}}function*f(e,t,r,n){(function(e){return u(e)&&\"function\"==typeof e[Symbol.iterator]})(e)||(e=[e]);for(const i of e){const e=d(i,t,r,n);e&&(yield e)}}function*l(e,t,r={}){const{path:n=[],branch:i=[e],coerce:s=!1,mask:o=!1}=r,a={path:n,branch:i};if(s&&(e=t.coercer(e,a),o&&\"type\"!==t.type&&u(t.schema)&&u(e)&&!Array.isArray(e)))for(const r in e)void 0===t.schema[r]&&delete e[r];let c=\"valid\";for(const n of t.validator(e,a))n.explanation=r.message,c=\"not_valid\",yield[n,void 0];for(let[d,f,h]of t.entries(e,a)){const t=l(f,h,{path:void 0===d?n:[...n,d],branch:void 0===d?i:[...i,f],coerce:s,mask:o,message:r.message});for(const r of t)r[0]?(c=null===r[0].refinement||void 0===r[0].refinement?\"not_valid\":\"not_refined\",yield[r[0],void 0]):s&&(f=r[1],void 0===d?e=f:e instanceof Map?e.set(d,f):e instanceof Set?e.add(f):u(e)&&(void 0!==f||d in e)&&(e[d]=f))}if(\"not_valid\"!==c)for(const n of t.refiner(e,a))n.explanation=r.message,c=\"not_refined\",yield[n,void 0];\"valid\"===c&&(yield[void 0,e])}class h{constructor(e){const{type:t,schema:r,validator:n,refiner:i,coercer:s=e=>e,entries:o=function*(){}}=e;this.type=t,this.schema=r,this.entries=o,this.coercer=s,this.validator=n?(e,t)=>f(n(e,t),t,this,e):()=>[],this.refiner=i?(e,t)=>f(i(e,t),t,this,e):()=>[]}assert(e,t){return p(e,this,t)}create(e,t){return g(e,this,t)}is(e){return y(e,this)}mask(e,t){return function(e,t,r){const n=m(e,t,{coerce:!0,mask:!0,message:r});if(n[0])throw n[0];return n[1]}(e,this,t)}validate(e,t={}){return m(e,this,t)}}function p(e,t,r){const n=m(e,t,{message:r});if(n[0])throw n[0]}function g(e,t,r){const n=m(e,t,{coerce:!0,message:r});if(n[0])throw n[0];return n[1]}function y(e,t){return!m(e,t)[0]}function m(e,t,r={}){const n=l(e,t,r),i=function(e){const{done:t,value:r}=e.next();return t?void 0:r}(n);if(i[0]){return[new a(i[0],(function*(){for(const e of n)e[0]&&(yield e[0])})),void 0]}return[void 0,i[1]]}function w(e,t){return new h({type:e,schema:null,validator:t})}function v(e){let t;return new h({type:\"lazy\",schema:null,*entries(r,n){t??(t=e()),yield*t.entries(r,n)},validator:(r,n)=>(t??(t=e()),t.validator(r,n)),coercer:(r,n)=>(t??(t=e()),t.coercer(r,n)),refiner:(r,n)=>(t??(t=e()),t.refiner(r,n))})}function b(e){return new h({type:\"array\",schema:e,*entries(t){if(e&&Array.isArray(t))for(const[r,n]of t.entries())yield[r,n,e]},coercer:e=>Array.isArray(e)?e.slice():e,validator:e=>Array.isArray(e)||`Expected an array value, but received: ${c(e)}`})}function _(e){const t=c(e),r=typeof e;return new h({type:\"literal\",schema:\"string\"===r||\"number\"===r||\"boolean\"===r?e:null,validator:r=>r===e||`Expected the literal \\`${t}\\`, but received: ${c(r)}`})}function E(){return w(\"never\",(()=>!1))}function x(){return w(\"number\",(e=>\"number\"==typeof e&&!isNaN(e)||`Expected a number, but received: ${c(e)}`))}function A(e){const t=e?Object.keys(e):[],r=E();return new h({type:\"object\",schema:e??null,*entries(n){if(e&&u(n)){const i=new Set(Object.keys(n));for(const r of t)i.delete(r),yield[r,n[r],e[r]];for(const e of i)yield[e,n[e],r]}},validator:e=>u(e)||`Expected an object, but received: ${c(e)}`,coercer:e=>u(e)?{...e}:e})}function k(e){return new h({...e,validator:(t,r)=>void 0===t||e.validator(t,r),refiner:(t,r)=>void 0===t||e.refiner(t,r)})}function I(e,t){return new h({type:\"record\",schema:null,*entries(r){if(u(r))for(const n in r){const i=r[n];yield[n,n,e],yield[n,i,t]}},validator:e=>u(e)||`Expected an object, but received: ${c(e)}`})}function B(){return w(\"string\",(e=>\"string\"==typeof e||`Expected a string, but received: ${c(e)}`))}function S(e){const t=e.map((e=>e.type)).join(\" | \");return new h({type:\"union\",schema:null,coercer(t){for(const r of e){const[e,n]=r.validate(t,{coerce:!0});if(!e)return n}return t},validator(r,n){const i=[];for(const t of e){const[...e]=l(r,t,n),[s]=e;if(!s?.[0])return[];for(const[t]of e)t&&i.push(t)}return[`Expected the value to satisfy a union of \\`${t}\\`, but received: ${c(r)}`,...i]}})}function O(e,t,r){return new h({...e,coercer:(n,i)=>y(n,t)?e.coercer(r(n,i),i):e.coercer(n,i)})}Error;function T(e){return function(e){return function(e){return\"object\"==typeof e&&null!==e&&\"message\"in e}(e)&&\"string\"==typeof e.message?e.message:null==e?\"\":String(e)}(e).replace(/\\.$/u,\"\")}function N(e,t){return r=e,Boolean(\"string\"==typeof r?.prototype?.constructor?.name)?new e({message:t}):e({message:t});var r}class R extends Error{constructor(e){super(e.message),this.code=\"ERR_ASSERTION\"}}const C=e=>A(e);function Z({path:e,branch:t}){const r=e[e.length-1];return i(t[t.length-2],r)}function U(e){return new h({...e,type:`optional ${e.type}`,validator:(t,r)=>!Z(r)||e.validator(t,r),refiner:(t,r)=>!Z(r)||e.refiner(t,r)})}const j=S([_(null),w(\"boolean\",(e=>\"boolean\"==typeof e)),w(\"finite number\",(e=>y(e,x())&&Number.isFinite(e))),B(),b(v((()=>j))),I(B(),v((()=>j)))]),P=O(j,w(\"any\",(()=>!0)),(e=>(function(e,t,r=\"Assertion failed\",n=R){try{p(e,t)}catch(e){throw N(n,`${r}: ${T(e)}.`)}}(e,j),JSON.parse(JSON.stringify(e,((e,t)=>{if(\"__proto__\"!==e&&\"constructor\"!==e)return t}))))));function L(e){try{return function(e){g(e,P)}(e),!0}catch{return!1}}const $=_(\"2.0\"),M=(F=S([x(),B()]),new h({...F,validator:(e,t)=>null===e||F.validator(e,t),refiner:(e,t)=>null===e||F.refiner(e,t)}));var F;const z=C({code:w(\"integer\",(e=>\"number\"==typeof e&&!isNaN(e)&&Number.isInteger(e)||`Expected an integer, but received: ${c(e)}`)),message:B(),data:U(P),stack:U(B())}),D=S([I(B(),P),b(P)]);C({id:M,jsonrpc:$,method:B(),params:U(D)}),C({jsonrpc:$,method:B(),params:U(D)});A({id:M,jsonrpc:$,result:k(w(\"unknown\",(()=>!0))),error:k(z)});const q=C({id:M,jsonrpc:$,result:P}),V=C({id:M,jsonrpc:$,error:z});S([q,V]);const K={invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},H={\"-32700\":{standard:\"JSON RPC 2.0\",message:\"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.\"},\"-32600\":{standard:\"JSON RPC 2.0\",message:\"The JSON sent is not a valid Request object.\"},\"-32601\":{standard:\"JSON RPC 2.0\",message:\"The method does not exist / is not available.\"},\"-32602\":{standard:\"JSON RPC 2.0\",message:\"Invalid method parameter(s).\"},\"-32603\":{standard:\"JSON RPC 2.0\",message:\"Internal JSON-RPC error.\"},\"-32000\":{standard:\"EIP-1474\",message:\"Invalid input.\"},\"-32001\":{standard:\"EIP-1474\",message:\"Resource not found.\"},\"-32002\":{standard:\"EIP-1474\",message:\"Resource unavailable.\"},\"-32003\":{standard:\"EIP-1474\",message:\"Transaction rejected.\"},\"-32004\":{standard:\"EIP-1474\",message:\"Method not supported.\"},\"-32005\":{standard:\"EIP-1474\",message:\"Request limit exceeded.\"},4001:{standard:\"EIP-1193\",message:\"User rejected the request.\"},4100:{standard:\"EIP-1193\",message:\"The requested account and/or method has not been authorized by the user.\"},4200:{standard:\"EIP-1193\",message:\"The requested method is not supported by this Ethereum provider.\"},4900:{standard:\"EIP-1193\",message:\"The provider is disconnected from all chains.\"},4901:{standard:\"EIP-1193\",message:\"The provider is disconnected from the specified chain.\"}},W=K.internal,G=(J(W),\"Unspecified server error.\");function J(e,t=\"Unspecified error message. This is a bug, please report it.\"){if(function(e){return Number.isInteger(e)}(e)){const t=e.toString();if(i(H,t))return H[t].message;if(function(e){return e>=-32099&&e<=-32e3}(e))return G}return t}function Y(e){return Array.isArray(e)?e.map((e=>L(e)?e:t(e)?X(e):null)):t(e)?X(e):L(e)?e:null}function X(e){return Object.getOwnPropertyNames(e).reduce(((t,r)=>{const n=e[r];return L(n)&&(t[r]=n),t}),{})}const Q=function(e){return e?.__esModule?e.default:e}(o);class ee extends Error{constructor(e,r,n){if(!Number.isInteger(e))throw new Error('\"code\" must be an integer.');if(!r||\"string\"!=typeof r)throw new Error('\"message\" must be a non-empty string.');!function(e){return t(e)&&i(e,\"cause\")&&t(e.cause)}(n)?super(r):(super(r,{cause:n.cause}),i(this,\"cause\")||Object.assign(this,{cause:n.cause})),void 0!==n&&(this.data=n),this.code=e}serialize(){const e={code:this.code,message:this.message};return void 0!==this.data&&(e.data=this.data,function(e){if(\"object\"!=typeof e||null===e)return!1;try{let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}catch(e){return!1}}(this.data)&&(e.data.cause=Y(this.data.cause))),this.stack&&(e.stack=this.stack),e}toString(){return Q(this.serialize(),te,2)}}function te(e,t){if(\"[Circular]\"!==t)return t}const re=e=>ie(K.invalidParams,e),ne=e=>ie(K.methodNotFound,e);function ie(e,t){const[r,n]=se(t);return new ee(e,r??J(e),n)}function se(e){if(e){if(\"string\"==typeof e)return[e];if(\"object\"==typeof e&&!Array.isArray(e)){const{message:t,data:r}=e;if(t&&\"string\"!=typeof t)throw new Error(\"Must specify string message.\");return[t??void 0,r]}}return[]}var oe,ae;!function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const r of e)t[r]=r;return t},e.getValidEnumValues=t=>{const r=e.objectKeys(t).filter((e=>\"number\"!=typeof t[t[e]])),n={};for(const e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys=\"function\"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(const r of e)if(t(r))return r},e.isInteger=\"function\"==typeof Number.isInteger?e=>Number.isInteger(e):e=>\"number\"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=\" | \"){return e.map((e=>\"string\"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>\"bigint\"==typeof t?t.toString():t}(oe||(oe={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(ae||(ae={}));const ue=oe.arrayToEnum([\"string\",\"nan\",\"number\",\"integer\",\"float\",\"boolean\",\"date\",\"bigint\",\"symbol\",\"function\",\"undefined\",\"null\",\"array\",\"object\",\"unknown\",\"promise\",\"void\",\"never\",\"map\",\"set\"]),ce=e=>{switch(typeof e){case\"undefined\":return ue.undefined;case\"string\":return ue.string;case\"number\":return isNaN(e)?ue.nan:ue.number;case\"boolean\":return ue.boolean;case\"function\":return ue.function;case\"bigint\":return ue.bigint;case\"symbol\":return ue.symbol;case\"object\":return Array.isArray(e)?ue.array:null===e?ue.null:e.then&&\"function\"==typeof e.then&&e.catch&&\"function\"==typeof e.catch?ue.promise:\"undefined\"!=typeof Map&&e instanceof Map?ue.map:\"undefined\"!=typeof Set&&e instanceof Set?ue.set:\"undefined\"!=typeof Date&&e instanceof Date?ue.date:ue.object;default:return ue.unknown}},de=oe.arrayToEnum([\"invalid_type\",\"invalid_literal\",\"custom\",\"invalid_union\",\"invalid_union_discriminator\",\"invalid_enum_value\",\"unrecognized_keys\",\"invalid_arguments\",\"invalid_return_type\",\"invalid_date\",\"invalid_string\",\"too_small\",\"too_big\",\"invalid_intersection_types\",\"not_multiple_of\",\"not_finite\"]);class fe extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name=\"ZodError\",this.issues=e}format(e){const t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(const i of e.issues)if(\"invalid_union\"===i.code)i.unionErrors.map(n);else if(\"invalid_return_type\"===i.code)n(i.returnTypeError);else if(\"invalid_arguments\"===i.code)n(i.argumentsError);else if(0===i.path.length)r._errors.push(t(i));else{let e=r,n=0;for(;n<i.path.length;){const r=i.path[n];n===i.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(i))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof fe))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,oe.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},r=[];for(const n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}fe.create=e=>new fe(e);const le=(e,t)=>{let r;switch(e.code){case de.invalid_type:r=e.received===ue.undefined?\"Required\":`Expected ${e.expected}, received ${e.received}`;break;case de.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,oe.jsonStringifyReplacer)}`;break;case de.unrecognized_keys:r=`Unrecognized key(s) in object: ${oe.joinValues(e.keys,\", \")}`;break;case de.invalid_union:r=\"Invalid input\";break;case de.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${oe.joinValues(e.options)}`;break;case de.invalid_enum_value:r=`Invalid enum value. Expected ${oe.joinValues(e.options)}, received '${e.received}'`;break;case de.invalid_arguments:r=\"Invalid function arguments\";break;case de.invalid_return_type:r=\"Invalid function return type\";break;case de.invalid_date:r=\"Invalid date\";break;case de.invalid_string:\"object\"==typeof e.validation?\"includes\"in e.validation?(r=`Invalid input: must include \"${e.validation.includes}\"`,\"number\"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):\"startsWith\"in e.validation?r=`Invalid input: must start with \"${e.validation.startsWith}\"`:\"endsWith\"in e.validation?r=`Invalid input: must end with \"${e.validation.endsWith}\"`:oe.assertNever(e.validation):r=\"regex\"!==e.validation?`Invalid ${e.validation}`:\"Invalid\";break;case de.too_small:r=\"array\"===e.type?`Array must contain ${e.exact?\"exactly\":e.inclusive?\"at least\":\"more than\"} ${e.minimum} element(s)`:\"string\"===e.type?`String must contain ${e.exact?\"exactly\":e.inclusive?\"at least\":\"over\"} ${e.minimum} character(s)`:\"number\"===e.type?`Number must be ${e.exact?\"exactly equal to \":e.inclusive?\"greater than or equal to \":\"greater than \"}${e.minimum}`:\"date\"===e.type?`Date must be ${e.exact?\"exactly equal to \":e.inclusive?\"greater than or equal to \":\"greater than \"}${new Date(Number(e.minimum))}`:\"Invalid input\";break;case de.too_big:r=\"array\"===e.type?`Array must contain ${e.exact?\"exactly\":e.inclusive?\"at most\":\"less than\"} ${e.maximum} element(s)`:\"string\"===e.type?`String must contain ${e.exact?\"exactly\":e.inclusive?\"at most\":\"under\"} ${e.maximum} character(s)`:\"number\"===e.type?`Number must be ${e.exact?\"exactly\":e.inclusive?\"less than or equal to\":\"less than\"} ${e.maximum}`:\"bigint\"===e.type?`BigInt must be ${e.exact?\"exactly\":e.inclusive?\"less than or equal to\":\"less than\"} ${e.maximum}`:\"date\"===e.type?`Date must be ${e.exact?\"exactly\":e.inclusive?\"smaller than or equal to\":\"smaller than\"} ${new Date(Number(e.maximum))}`:\"Invalid input\";break;case de.custom:r=\"Invalid input\";break;case de.invalid_intersection_types:r=\"Intersection results could not be merged\";break;case de.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case de.not_finite:r=\"Number must be finite\";break;default:r=t.defaultError,oe.assertNever(e)}return{message:r}};let he=le;function pe(){return he}const ge=e=>{const{data:t,path:r,errorMaps:n,issueData:i}=e,s=[...r,...i.path||[]],o={...i,path:s};if(void 0!==i.message)return{...i,path:s,message:i.message};let a=\"\";const u=n.filter((e=>!!e)).slice().reverse();for(const e of u)a=e(o,{data:t,defaultError:a}).message;return{...i,path:s,message:a}};function ye(e,t){const r=pe(),n=ge({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===le?void 0:le].filter((e=>!!e))});e.common.issues.push(n)}class me{constructor(){this.value=\"valid\"}dirty(){\"valid\"===this.value&&(this.value=\"dirty\")}abort(){\"aborted\"!==this.value&&(this.value=\"aborted\")}static mergeArray(e,t){const r=[];for(const n of t){if(\"aborted\"===n.status)return we;\"dirty\"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){const r=[];for(const e of t){const t=await e.key,n=await e.value;r.push({key:t,value:n})}return me.mergeObjectSync(e,r)}static mergeObjectSync(e,t){const r={};for(const n of t){const{key:t,value:i}=n;if(\"aborted\"===t.status)return we;if(\"aborted\"===i.status)return we;\"dirty\"===t.status&&e.dirty(),\"dirty\"===i.status&&e.dirty(),\"__proto__\"===t.value||void 0===i.value&&!n.alwaysSet||(r[t.value]=i.value)}return{status:e.value,value:r}}}const we=Object.freeze({status:\"aborted\"}),ve=e=>({status:\"dirty\",value:e}),be=e=>({status:\"valid\",value:e}),_e=e=>\"aborted\"===e.status,Ee=e=>\"dirty\"===e.status,xe=e=>\"valid\"===e.status,Ae=e=>\"undefined\"!=typeof Promise&&e instanceof Promise;function ke(e,t,r,n){if(\"a\"===r&&!n)throw new TypeError(\"Private accessor was defined without a getter\");if(\"function\"==typeof t?e!==t||!n:!t.has(e))throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");return\"m\"===r?n:\"a\"===r?n.call(e):n?n.value:t.get(e)}function Ie(e,t,r,n,i){if(\"m\"===n)throw new TypeError(\"Private method is not writable\");if(\"a\"===n&&!i)throw new TypeError(\"Private accessor was defined without a setter\");if(\"function\"==typeof t?e!==t||!i:!t.has(e))throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");return\"a\"===n?i.call(e,r):i?i.value=r:t.set(e,r),r}var Be,Se,Oe;\"function\"==typeof SuppressedError&&SuppressedError,function(e){e.errToObj=e=>\"string\"==typeof e?{message:e}:e||{},e.toString=e=>\"string\"==typeof e?e:null==e?void 0:e.message}(Be||(Be={}));class Te{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ne=(e,t)=>{if(xe(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error(\"Validation failed but no issues detected.\");return{success:!1,get error(){if(this._error)return this._error;const t=new fe(e.common.issues);return this._error=t,this._error}}};function Re(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:n,description:i}=e;if(t&&(r||n))throw new Error('Can\\'t use \"invalid_type_error\" or \"required_error\" in conjunction with custom error map.');if(t)return{errorMap:t,description:i};return{errorMap:(t,i)=>{var s,o;const{message:a}=e;return\"invalid_enum_value\"===t.code?{message:null!=a?a:i.defaultError}:void 0===i.data?{message:null!==(s=null!=a?a:n)&&void 0!==s?s:i.defaultError}:\"invalid_type\"!==t.code?{message:i.defaultError}:{message:null!==(o=null!=a?a:r)&&void 0!==o?o:i.defaultError}},description:i}}class Ce{get description(){return this._def.description}_getType(e){return ce(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ce(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new me,ctx:{common:e.parent.common,data:e.data,parsedType:ce(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Ae(t))throw new Error(\"Synchronous parse encountered promise.\");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){var r;const n={common:{issues:[],async:null!==(r=null==t?void 0:t.async)&&void 0!==r&&r,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ce(e)},i=this._parseSync({data:e,path:n.path,parent:n});return Ne(n,i)}\"~validate\"(e){var t,r;const n={common:{issues:[],async:!!this[\"~standard\"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ce(e)};if(!this[\"~standard\"].async)try{const t=this._parseSync({data:e,path:[],parent:n});return xe(t)?{value:t.value}:{issues:n.common.issues}}catch(e){(null===(r=null===(t=null==e?void 0:e.message)||void 0===t?void 0:t.toLowerCase())||void 0===r?void 0:r.includes(\"encountered\"))&&(this[\"~standard\"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:n}).then((e=>xe(e)?{value:e.value}:{issues:n.common.issues}))}async parseAsync(e,t){const r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){const r={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ce(e)},n=this._parse({data:e,path:r.path,parent:r}),i=await(Ae(n)?n:Promise.resolve(n));return Ne(r,i)}refine(e,t){const r=e=>\"string\"==typeof t||void 0===t?{message:t}:\"function\"==typeof t?t(e):t;return this._refinement(((t,n)=>{const i=e(t),s=()=>n.addIssue({code:de.custom,...r(t)});return\"undefined\"!=typeof Promise&&i instanceof Promise?i.then((e=>!!e||(s(),!1))):!!i||(s(),!1)}))}refinement(e,t){return this._refinement(((r,n)=>!!e(r)||(n.addIssue(\"function\"==typeof t?t(r,n):t),!1)))}_refinement(e){return new Ct({schema:this,typeName:Kt.ZodEffects,effect:{type:\"refinement\",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this[\"~standard\"]={version:1,vendor:\"zod\",validate:e=>this[\"~validate\"](e)}}optional(){return Zt.create(this,this._def)}nullable(){return Ut.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return pt.create(this)}promise(){return Rt.create(this,this._def)}or(e){return mt.create([this,e],this._def)}and(e){return _t.create(this,e,this._def)}transform(e){return new Ct({...Re(this._def),schema:this,typeName:Kt.ZodEffects,effect:{type:\"transform\",transform:e}})}default(e){const t=\"function\"==typeof e?e:()=>e;return new jt({...Re(this._def),innerType:this,defaultValue:t,typeName:Kt.ZodDefault})}brand(){return new Mt({typeName:Kt.ZodBranded,type:this,...Re(this._def)})}catch(e){const t=\"function\"==typeof e?e:()=>e;return new Pt({...Re(this._def),innerType:this,catchValue:t,typeName:Kt.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return Ft.create(this,e)}readonly(){return zt.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ze=/^c[^\\s-]{8,}$/i,Ue=/^[0-9a-z]+$/,je=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Pe=/^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/i,Le=/^[a-z0-9_-]{21}$/i,$e=/^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/,Me=/^[-+]?P(?!$)(?:(?:[-+]?\\d+Y)|(?:[-+]?\\d+[.,]\\d+Y$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:(?:[-+]?\\d+W)|(?:[-+]?\\d+[.,]\\d+W$))?(?:(?:[-+]?\\d+D)|(?:[-+]?\\d+[.,]\\d+D$))?(?:T(?=[\\d+-])(?:(?:[-+]?\\d+H)|(?:[-+]?\\d+[.,]\\d+H$))?(?:(?:[-+]?\\d+M)|(?:[-+]?\\d+[.,]\\d+M$))?(?:[-+]?\\d+(?:[.,]\\d+)?S)?)??$/,Fe=/^(?!\\.)(?!.*\\.\\.)([A-Z0-9_'+\\-\\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\\-]*\\.)+[A-Z]{2,}$/i;let ze;const De=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,qe=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/,Ve=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ke=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,He=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,We=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ge=\"((\\\\d\\\\d[2468][048]|\\\\d\\\\d[13579][26]|\\\\d\\\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\\\d|30)|(02)-(0[1-9]|1\\\\d|2[0-8])))\",Je=new RegExp(`^${Ge}$`);function Ye(e){let t=\"([01]\\\\d|2[0-3]):[0-5]\\\\d:[0-5]\\\\d\";return e.precision?t=`${t}\\\\.\\\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\\\.\\\\d+)?`),t}function Xe(e){let t=`${Ge}T${Ye(e)}`;const r=[];return r.push(e.local?\"Z?\":\"Z\"),e.offset&&r.push(\"([+-]\\\\d{2}:?\\\\d{2})\"),t=`${t}(${r.join(\"|\")})`,new RegExp(`^${t}$`)}function Qe(e,t){if(!$e.test(e))return!1;try{const[r]=e.split(\".\"),n=r.replace(/-/g,\"+\").replace(/_/g,\"/\").padEnd(r.length+(4-r.length%4)%4,\"=\"),i=JSON.parse(atob(n));return\"object\"==typeof i&&null!==i&&(!(!i.typ||!i.alg)&&(!t||i.alg===t))}catch(e){return!1}}function et(e,t){return!(\"v4\"!==t&&t||!qe.test(e))||!(\"v6\"!==t&&t||!Ke.test(e))}class tt extends Ce{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==ue.string){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.string,received:t.parsedType}),we}const t=new me;let r;for(const s of this._def.checks)if(\"min\"===s.kind)e.data.length<s.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_small,minimum:s.value,type:\"string\",inclusive:!0,exact:!1,message:s.message}),t.dirty());else if(\"max\"===s.kind)e.data.length>s.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_big,maximum:s.value,type:\"string\",inclusive:!0,exact:!1,message:s.message}),t.dirty());else if(\"length\"===s.kind){const n=e.data.length>s.value,i=e.data.length<s.value;(n||i)&&(r=this._getOrReturnCtx(e,r),n?ye(r,{code:de.too_big,maximum:s.value,type:\"string\",inclusive:!0,exact:!0,message:s.message}):i&&ye(r,{code:de.too_small,minimum:s.value,type:\"string\",inclusive:!0,exact:!0,message:s.message}),t.dirty())}else if(\"email\"===s.kind)Fe.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"email\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"emoji\"===s.kind)ze||(ze=new RegExp(\"^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$\",\"u\")),ze.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"emoji\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"uuid\"===s.kind)Pe.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"uuid\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"nanoid\"===s.kind)Le.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"nanoid\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"cuid\"===s.kind)Ze.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"cuid\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"cuid2\"===s.kind)Ue.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"cuid2\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"ulid\"===s.kind)je.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"ulid\",code:de.invalid_string,message:s.message}),t.dirty());else if(\"url\"===s.kind)try{new URL(e.data)}catch(n){r=this._getOrReturnCtx(e,r),ye(r,{validation:\"url\",code:de.invalid_string,message:s.message}),t.dirty()}else if(\"regex\"===s.kind){s.regex.lastIndex=0;s.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"regex\",code:de.invalid_string,message:s.message}),t.dirty())}else if(\"trim\"===s.kind)e.data=e.data.trim();else if(\"includes\"===s.kind)e.data.includes(s.value,s.position)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),t.dirty());else if(\"toLowerCase\"===s.kind)e.data=e.data.toLowerCase();else if(\"toUpperCase\"===s.kind)e.data=e.data.toUpperCase();else if(\"startsWith\"===s.kind)e.data.startsWith(s.value)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:{startsWith:s.value},message:s.message}),t.dirty());else if(\"endsWith\"===s.kind)e.data.endsWith(s.value)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:{endsWith:s.value},message:s.message}),t.dirty());else if(\"datetime\"===s.kind){Xe(s).test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:\"datetime\",message:s.message}),t.dirty())}else if(\"date\"===s.kind){Je.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:\"date\",message:s.message}),t.dirty())}else if(\"time\"===s.kind){new RegExp(`^${Ye(s)}$`).test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{code:de.invalid_string,validation:\"time\",message:s.message}),t.dirty())}else\"duration\"===s.kind?Me.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"duration\",code:de.invalid_string,message:s.message}),t.dirty()):\"ip\"===s.kind?(n=e.data,(\"v4\"!==(i=s.version)&&i||!De.test(n))&&(\"v6\"!==i&&i||!Ve.test(n))&&(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"ip\",code:de.invalid_string,message:s.message}),t.dirty())):\"jwt\"===s.kind?Qe(e.data,s.alg)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"jwt\",code:de.invalid_string,message:s.message}),t.dirty()):\"cidr\"===s.kind?et(e.data,s.version)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"cidr\",code:de.invalid_string,message:s.message}),t.dirty()):\"base64\"===s.kind?He.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"base64\",code:de.invalid_string,message:s.message}),t.dirty()):\"base64url\"===s.kind?We.test(e.data)||(r=this._getOrReturnCtx(e,r),ye(r,{validation:\"base64url\",code:de.invalid_string,message:s.message}),t.dirty()):oe.assertNever(s);var n,i;return{status:t.value,value:e.data}}_regex(e,t,r){return this.refinement((t=>e.test(t)),{validation:t,code:de.invalid_string,...Be.errToObj(r)})}_addCheck(e){return new tt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:\"email\",...Be.errToObj(e)})}url(e){return this._addCheck({kind:\"url\",...Be.errToObj(e)})}emoji(e){return this._addCheck({kind:\"emoji\",...Be.errToObj(e)})}uuid(e){return this._addCheck({kind:\"uuid\",...Be.errToObj(e)})}nanoid(e){return this._addCheck({kind:\"nanoid\",...Be.errToObj(e)})}cuid(e){return this._addCheck({kind:\"cuid\",...Be.errToObj(e)})}cuid2(e){return this._addCheck({kind:\"cuid2\",...Be.errToObj(e)})}ulid(e){return this._addCheck({kind:\"ulid\",...Be.errToObj(e)})}base64(e){return this._addCheck({kind:\"base64\",...Be.errToObj(e)})}base64url(e){return this._addCheck({kind:\"base64url\",...Be.errToObj(e)})}jwt(e){return this._addCheck({kind:\"jwt\",...Be.errToObj(e)})}ip(e){return this._addCheck({kind:\"ip\",...Be.errToObj(e)})}cidr(e){return this._addCheck({kind:\"cidr\",...Be.errToObj(e)})}datetime(e){var t,r;return\"string\"==typeof e?this._addCheck({kind:\"datetime\",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:\"datetime\",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,local:null!==(r=null==e?void 0:e.local)&&void 0!==r&&r,...Be.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:\"date\",message:e})}time(e){return\"string\"==typeof e?this._addCheck({kind:\"time\",precision:null,message:e}):this._addCheck({kind:\"time\",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...Be.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:\"duration\",...Be.errToObj(e)})}regex(e,t){return this._addCheck({kind:\"regex\",regex:e,...Be.errToObj(t)})}includes(e,t){return this._addCheck({kind:\"includes\",value:e,position:null==t?void 0:t.position,...Be.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:\"startsWith\",value:e,...Be.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:\"endsWith\",value:e,...Be.errToObj(t)})}min(e,t){return this._addCheck({kind:\"min\",value:e,...Be.errToObj(t)})}max(e,t){return this._addCheck({kind:\"max\",value:e,...Be.errToObj(t)})}length(e,t){return this._addCheck({kind:\"length\",value:e,...Be.errToObj(t)})}nonempty(e){return this.min(1,Be.errToObj(e))}trim(){return new tt({...this._def,checks:[...this._def.checks,{kind:\"trim\"}]})}toLowerCase(){return new tt({...this._def,checks:[...this._def.checks,{kind:\"toLowerCase\"}]})}toUpperCase(){return new tt({...this._def,checks:[...this._def.checks,{kind:\"toUpperCase\"}]})}get isDatetime(){return!!this._def.checks.find((e=>\"datetime\"===e.kind))}get isDate(){return!!this._def.checks.find((e=>\"date\"===e.kind))}get isTime(){return!!this._def.checks.find((e=>\"time\"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>\"duration\"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>\"email\"===e.kind))}get isURL(){return!!this._def.checks.find((e=>\"url\"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>\"emoji\"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>\"uuid\"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>\"nanoid\"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>\"cuid\"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>\"cuid2\"===e.kind))}get isULID(){return!!this._def.checks.find((e=>\"ulid\"===e.kind))}get isIP(){return!!this._def.checks.find((e=>\"ip\"===e.kind))}get isCIDR(){return!!this._def.checks.find((e=>\"cidr\"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>\"base64\"===e.kind))}get isBase64url(){return!!this._def.checks.find((e=>\"base64url\"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)\"min\"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)\"max\"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function rt(e,t){const r=(e.toString().split(\".\")[1]||\"\").length,n=(t.toString().split(\".\")[1]||\"\").length,i=r>n?r:n;return parseInt(e.toFixed(i).replace(\".\",\"\"))%parseInt(t.toFixed(i).replace(\".\",\"\"))/Math.pow(10,i)}tt.create=e=>{var t;return new tt({checks:[],typeName:Kt.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...Re(e)})};class nt extends Ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==ue.number){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.number,received:t.parsedType}),we}let t;const r=new me;for(const n of this._def.checks)if(\"int\"===n.kind)oe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),ye(t,{code:de.invalid_type,expected:\"integer\",received:\"float\",message:n.message}),r.dirty());else if(\"min\"===n.kind){(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.too_small,minimum:n.value,type:\"number\",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty())}else if(\"max\"===n.kind){(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.too_big,maximum:n.value,type:\"number\",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty())}else\"multipleOf\"===n.kind?0!==rt(e.data,n.value)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):\"finite\"===n.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),ye(t,{code:de.not_finite,message:n.message}),r.dirty()):oe.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit(\"min\",e,!0,Be.toString(t))}gt(e,t){return this.setLimit(\"min\",e,!1,Be.toString(t))}lte(e,t){return this.setLimit(\"max\",e,!0,Be.toString(t))}lt(e,t){return this.setLimit(\"max\",e,!1,Be.toString(t))}setLimit(e,t,r,n){return new nt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:Be.toString(n)}]})}_addCheck(e){return new nt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:\"int\",message:Be.toString(e)})}positive(e){return this._addCheck({kind:\"min\",value:0,inclusive:!1,message:Be.toString(e)})}negative(e){return this._addCheck({kind:\"max\",value:0,inclusive:!1,message:Be.toString(e)})}nonpositive(e){return this._addCheck({kind:\"max\",value:0,inclusive:!0,message:Be.toString(e)})}nonnegative(e){return this._addCheck({kind:\"min\",value:0,inclusive:!0,message:Be.toString(e)})}multipleOf(e,t){return this._addCheck({kind:\"multipleOf\",value:e,message:Be.toString(t)})}finite(e){return this._addCheck({kind:\"finite\",message:Be.toString(e)})}safe(e){return this._addCheck({kind:\"min\",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Be.toString(e)})._addCheck({kind:\"max\",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Be.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)\"min\"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)\"max\"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>\"int\"===e.kind||\"multipleOf\"===e.kind&&oe.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if(\"finite\"===r.kind||\"int\"===r.kind||\"multipleOf\"===r.kind)return!0;\"min\"===r.kind?(null===t||r.value>t)&&(t=r.value):\"max\"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}nt.create=e=>new nt({checks:[],typeName:Kt.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...Re(e)});class it extends Ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(t){return this._getInvalidInput(e)}if(this._getType(e)!==ue.bigint)return this._getInvalidInput(e);let t;const r=new me;for(const n of this._def.checks)if(\"min\"===n.kind){(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.too_small,type:\"bigint\",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty())}else if(\"max\"===n.kind){(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.too_big,type:\"bigint\",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty())}else\"multipleOf\"===n.kind?e.data%n.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),ye(t,{code:de.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):oe.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.bigint,received:t.parsedType}),we}gte(e,t){return this.setLimit(\"min\",e,!0,Be.toString(t))}gt(e,t){return this.setLimit(\"min\",e,!1,Be.toString(t))}lte(e,t){return this.setLimit(\"max\",e,!0,Be.toString(t))}lt(e,t){return this.setLimit(\"max\",e,!1,Be.toString(t))}setLimit(e,t,r,n){return new it({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:Be.toString(n)}]})}_addCheck(e){return new it({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:\"min\",value:BigInt(0),inclusive:!1,message:Be.toString(e)})}negative(e){return this._addCheck({kind:\"max\",value:BigInt(0),inclusive:!1,message:Be.toString(e)})}nonpositive(e){return this._addCheck({kind:\"max\",value:BigInt(0),inclusive:!0,message:Be.toString(e)})}nonnegative(e){return this._addCheck({kind:\"min\",value:BigInt(0),inclusive:!0,message:Be.toString(e)})}multipleOf(e,t){return this._addCheck({kind:\"multipleOf\",value:e,message:Be.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)\"min\"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)\"max\"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}it.create=e=>{var t;return new it({checks:[],typeName:Kt.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...Re(e)})};class st extends Ce{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==ue.boolean){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.boolean,received:t.parsedType}),we}return be(e.data)}}st.create=e=>new st({typeName:Kt.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...Re(e)});class ot extends Ce{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==ue.date){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.date,received:t.parsedType}),we}if(isNaN(e.data.getTime())){return ye(this._getOrReturnCtx(e),{code:de.invalid_date}),we}const t=new me;let r;for(const n of this._def.checks)\"min\"===n.kind?e.data.getTime()<n.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:\"date\"}),t.dirty()):\"max\"===n.kind?e.data.getTime()>n.value&&(r=this._getOrReturnCtx(e,r),ye(r,{code:de.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:\"date\"}),t.dirty()):oe.assertNever(n);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ot({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:\"min\",value:e.getTime(),message:Be.toString(t)})}max(e,t){return this._addCheck({kind:\"max\",value:e.getTime(),message:Be.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)\"min\"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)\"max\"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ot.create=e=>new ot({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:Kt.ZodDate,...Re(e)});class at extends Ce{_parse(e){if(this._getType(e)!==ue.symbol){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.symbol,received:t.parsedType}),we}return be(e.data)}}at.create=e=>new at({typeName:Kt.ZodSymbol,...Re(e)});class ut extends Ce{_parse(e){if(this._getType(e)!==ue.undefined){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.undefined,received:t.parsedType}),we}return be(e.data)}}ut.create=e=>new ut({typeName:Kt.ZodUndefined,...Re(e)});class ct extends Ce{_parse(e){if(this._getType(e)!==ue.null){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.null,received:t.parsedType}),we}return be(e.data)}}ct.create=e=>new ct({typeName:Kt.ZodNull,...Re(e)});class dt extends Ce{constructor(){super(...arguments),this._any=!0}_parse(e){return be(e.data)}}dt.create=e=>new dt({typeName:Kt.ZodAny,...Re(e)});class ft extends Ce{constructor(){super(...arguments),this._unknown=!0}_parse(e){return be(e.data)}}ft.create=e=>new ft({typeName:Kt.ZodUnknown,...Re(e)});class lt extends Ce{_parse(e){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.never,received:t.parsedType}),we}}lt.create=e=>new lt({typeName:Kt.ZodNever,...Re(e)});class ht extends Ce{_parse(e){if(this._getType(e)!==ue.undefined){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.void,received:t.parsedType}),we}return be(e.data)}}ht.create=e=>new ht({typeName:Kt.ZodVoid,...Re(e)});class pt extends Ce{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==ue.array)return ye(t,{code:de.invalid_type,expected:ue.array,received:t.parsedType}),we;if(null!==n.exactLength){const e=t.data.length>n.exactLength.value,i=t.data.length<n.exactLength.value;(e||i)&&(ye(t,{code:e?de.too_big:de.too_small,minimum:i?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:\"array\",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(ye(t,{code:de.too_small,minimum:n.minLength.value,type:\"array\",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(ye(t,{code:de.too_big,maximum:n.maxLength.value,type:\"array\",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map(((e,r)=>n.type._parseAsync(new Te(t,e,t.path,r))))).then((e=>me.mergeArray(r,e)));const i=[...t.data].map(((e,r)=>n.type._parseSync(new Te(t,e,t.path,r))));return me.mergeArray(r,i)}get element(){return this._def.type}min(e,t){return new pt({...this._def,minLength:{value:e,message:Be.toString(t)}})}max(e,t){return new pt({...this._def,maxLength:{value:e,message:Be.toString(t)}})}length(e,t){return new pt({...this._def,exactLength:{value:e,message:Be.toString(t)}})}nonempty(e){return this.min(1,e)}}function gt(e){if(e instanceof yt){const t={};for(const r in e.shape){const n=e.shape[r];t[r]=Zt.create(gt(n))}return new yt({...e._def,shape:()=>t})}return e instanceof pt?new pt({...e._def,type:gt(e.element)}):e instanceof Zt?Zt.create(gt(e.unwrap())):e instanceof Ut?Ut.create(gt(e.unwrap())):e instanceof Et?Et.create(e.items.map((e=>gt(e)))):e}pt.create=(e,t)=>new pt({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Kt.ZodArray,...Re(t)});class yt extends Ce{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=oe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==ue.object){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.object,received:t.parsedType}),we}const{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),s=[];if(!(this._def.catchall instanceof lt&&\"strip\"===this._def.unknownKeys))for(const e in r.data)i.includes(e)||s.push(e);const o=[];for(const e of i){const t=n[e],i=r.data[e];o.push({key:{status:\"valid\",value:e},value:t._parse(new Te(r,i,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof lt){const e=this._def.unknownKeys;if(\"passthrough\"===e)for(const e of s)o.push({key:{status:\"valid\",value:e},value:{status:\"valid\",value:r.data[e]}});else if(\"strict\"===e)s.length>0&&(ye(r,{code:de.unrecognized_keys,keys:s}),t.dirty());else if(\"strip\"!==e)throw new Error(\"Internal ZodObject error: invalid unknownKeys value.\")}else{const e=this._def.catchall;for(const t of s){const n=r.data[t];o.push({key:{status:\"valid\",value:t},value:e._parse(new Te(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of o){const r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e})).then((e=>me.mergeObjectSync(t,e))):me.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return Be.errToObj,new yt({...this._def,unknownKeys:\"strict\",...void 0!==e?{errorMap:(t,r)=>{var n,i,s,o;const a=null!==(s=null===(i=(n=this._def).errorMap)||void 0===i?void 0:i.call(n,t,r).message)&&void 0!==s?s:r.defaultError;return\"unrecognized_keys\"===t.code?{message:null!==(o=Be.errToObj(e).message)&&void 0!==o?o:a}:{message:a}}}:{}})}strip(){return new yt({...this._def,unknownKeys:\"strip\"})}passthrough(){return new yt({...this._def,unknownKeys:\"passthrough\"})}extend(e){return new yt({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new yt({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Kt.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new yt({...this._def,catchall:e})}pick(e){const t={};return oe.objectKeys(e).forEach((r=>{e[r]&&this.shape[r]&&(t[r]=this.shape[r])})),new yt({...this._def,shape:()=>t})}omit(e){const t={};return oe.objectKeys(this.shape).forEach((r=>{e[r]||(t[r]=this.shape[r])})),new yt({...this._def,shape:()=>t})}deepPartial(){return gt(this)}partial(e){const t={};return oe.objectKeys(this.shape).forEach((r=>{const n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()})),new yt({...this._def,shape:()=>t})}required(e){const t={};return oe.objectKeys(this.shape).forEach((r=>{if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof Zt;)e=e._def.innerType;t[r]=e}})),new yt({...this._def,shape:()=>t})}keyof(){return Ot(oe.objectKeys(this.shape))}}yt.create=(e,t)=>new yt({shape:()=>e,unknownKeys:\"strip\",catchall:lt.create(),typeName:Kt.ZodObject,...Re(t)}),yt.strictCreate=(e,t)=>new yt({shape:()=>e,unknownKeys:\"strict\",catchall:lt.create(),typeName:Kt.ZodObject,...Re(t)}),yt.lazycreate=(e,t)=>new yt({shape:e,unknownKeys:\"strip\",catchall:lt.create(),typeName:Kt.ZodObject,...Re(t)});class mt extends Ce{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map((async e=>{const r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}}))).then((function(e){for(const t of e)if(\"valid\"===t.result.status)return t.result;for(const r of e)if(\"dirty\"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const r=e.map((e=>new fe(e.ctx.common.issues)));return ye(t,{code:de.invalid_union,unionErrors:r}),we}));{let e;const n=[];for(const i of r){const r={...t,common:{...t.common,issues:[]},parent:null},s=i._parseSync({data:t.data,path:t.path,parent:r});if(\"valid\"===s.status)return s;\"dirty\"!==s.status||e||(e={result:s,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const i=n.map((e=>new fe(e)));return ye(t,{code:de.invalid_union,unionErrors:i}),we}}get options(){return this._def.options}}mt.create=(e,t)=>new mt({options:e,typeName:Kt.ZodUnion,...Re(t)});const wt=e=>e instanceof Bt?wt(e.schema):e instanceof Ct?wt(e.innerType()):e instanceof St?[e.value]:e instanceof Tt?e.options:e instanceof Nt?oe.objectValues(e.enum):e instanceof jt?wt(e._def.innerType):e instanceof ut?[void 0]:e instanceof ct?[null]:e instanceof Zt?[void 0,...wt(e.unwrap())]:e instanceof Ut?[null,...wt(e.unwrap())]:e instanceof Mt||e instanceof zt?wt(e.unwrap()):e instanceof Pt?wt(e._def.innerType):[];class vt extends Ce{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==ue.object)return ye(t,{code:de.invalid_type,expected:ue.object,received:t.parsedType}),we;const r=this.discriminator,n=t.data[r],i=this.optionsMap.get(n);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(ye(t,{code:de.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),we)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){const n=new Map;for(const r of t){const t=wt(r.shape[e]);if(!t.length)throw new Error(`A discriminator value for key \\`${e}\\` could not be extracted from all schema options`);for(const i of t){if(n.has(i))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);n.set(i,r)}}return new vt({typeName:Kt.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...Re(r)})}}function bt(e,t){const r=ce(e),n=ce(t);if(e===t)return{valid:!0,data:e};if(r===ue.object&&n===ue.object){const r=oe.objectKeys(t),n=oe.objectKeys(e).filter((e=>-1!==r.indexOf(e))),i={...e,...t};for(const r of n){const n=bt(e[r],t[r]);if(!n.valid)return{valid:!1};i[r]=n.data}return{valid:!0,data:i}}if(r===ue.array&&n===ue.array){if(e.length!==t.length)return{valid:!1};const r=[];for(let n=0;n<e.length;n++){const i=bt(e[n],t[n]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return r===ue.date&&n===ue.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class _t extends Ce{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(_e(e)||_e(n))return we;const i=bt(e.value,n.value);return i.valid?((Ee(e)||Ee(n))&&t.dirty(),{status:t.value,value:i.data}):(ye(r,{code:de.invalid_intersection_types}),we)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then((([e,t])=>n(e,t))):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}_t.create=(e,t,r)=>new _t({left:e,right:t,typeName:Kt.ZodIntersection,...Re(r)});class Et extends Ce{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ue.array)return ye(r,{code:de.invalid_type,expected:ue.array,received:r.parsedType}),we;if(r.data.length<this._def.items.length)return ye(r,{code:de.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:\"array\"}),we;!this._def.rest&&r.data.length>this._def.items.length&&(ye(r,{code:de.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:\"array\"}),t.dirty());const n=[...r.data].map(((e,t)=>{const n=this._def.items[t]||this._def.rest;return n?n._parse(new Te(r,e,r.path,t)):null})).filter((e=>!!e));return r.common.async?Promise.all(n).then((e=>me.mergeArray(t,e))):me.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new Et({...this._def,rest:e})}}Et.create=(e,t)=>{if(!Array.isArray(e))throw new Error(\"You must pass an array of schemas to z.tuple([ ... ])\");return new Et({items:e,typeName:Kt.ZodTuple,rest:null,...Re(t)})};class xt extends Ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ue.object)return ye(r,{code:de.invalid_type,expected:ue.object,received:r.parsedType}),we;const n=[],i=this._def.keyType,s=this._def.valueType;for(const e in r.data)n.push({key:i._parse(new Te(r,e,r.path,e)),value:s._parse(new Te(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?me.mergeObjectAsync(t,n):me.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new xt(t instanceof Ce?{keyType:e,valueType:t,typeName:Kt.ZodRecord,...Re(r)}:{keyType:tt.create(),valueType:e,typeName:Kt.ZodRecord,...Re(t)})}}class At extends Ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ue.map)return ye(r,{code:de.invalid_type,expected:ue.map,received:r.parsedType}),we;const n=this._def.keyType,i=this._def.valueType,s=[...r.data.entries()].map((([e,t],s)=>({key:n._parse(new Te(r,e,r.path,[s,\"key\"])),value:i._parse(new Te(r,t,r.path,[s,\"value\"]))})));if(r.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const r of s){const n=await r.key,i=await r.value;if(\"aborted\"===n.status||\"aborted\"===i.status)return we;\"dirty\"!==n.status&&\"dirty\"!==i.status||t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const r of s){const n=r.key,i=r.value;if(\"aborted\"===n.status||\"aborted\"===i.status)return we;\"dirty\"!==n.status&&\"dirty\"!==i.status||t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}}}At.create=(e,t,r)=>new At({valueType:t,keyType:e,typeName:Kt.ZodMap,...Re(r)});class kt extends Ce{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ue.set)return ye(r,{code:de.invalid_type,expected:ue.set,received:r.parsedType}),we;const n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(ye(r,{code:de.too_small,minimum:n.minSize.value,type:\"set\",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(ye(r,{code:de.too_big,maximum:n.maxSize.value,type:\"set\",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());const i=this._def.valueType;function s(e){const r=new Set;for(const n of e){if(\"aborted\"===n.status)return we;\"dirty\"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}const o=[...r.data.values()].map(((e,t)=>i._parse(new Te(r,e,r.path,t))));return r.common.async?Promise.all(o).then((e=>s(e))):s(o)}min(e,t){return new kt({...this._def,minSize:{value:e,message:Be.toString(t)}})}max(e,t){return new kt({...this._def,maxSize:{value:e,message:Be.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}kt.create=(e,t)=>new kt({valueType:e,minSize:null,maxSize:null,typeName:Kt.ZodSet,...Re(t)});class It extends Ce{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==ue.function)return ye(t,{code:de.invalid_type,expected:ue.function,received:t.parsedType}),we;function r(e,r){return ge({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,pe(),le].filter((e=>!!e)),issueData:{code:de.invalid_arguments,argumentsError:r}})}function n(e,r){return ge({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,pe(),le].filter((e=>!!e)),issueData:{code:de.invalid_return_type,returnTypeError:r}})}const i={errorMap:t.common.contextualErrorMap},s=t.data;if(this._def.returns instanceof Rt){const e=this;return be((async function(...t){const o=new fe([]),a=await e._def.args.parseAsync(t,i).catch((e=>{throw o.addIssue(r(t,e)),o})),u=await Reflect.apply(s,this,a);return await e._def.returns._def.type.parseAsync(u,i).catch((e=>{throw o.addIssue(n(u,e)),o}))}))}{const e=this;return be((function(...t){const o=e._def.args.safeParse(t,i);if(!o.success)throw new fe([r(t,o.error)]);const a=Reflect.apply(s,this,o.data),u=e._def.returns.safeParse(a,i);if(!u.success)throw new fe([n(a,u.error)]);return u.data}))}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new It({...this._def,args:Et.create(e).rest(ft.create())})}returns(e){return new It({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new It({args:e||Et.create([]).rest(ft.create()),returns:t||ft.create(),typeName:Kt.ZodFunction,...Re(r)})}}class Bt extends Ce{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Bt.create=(e,t)=>new Bt({getter:e,typeName:Kt.ZodLazy,...Re(t)});class St extends Ce{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return ye(t,{received:t.data,code:de.invalid_literal,expected:this._def.value}),we}return{status:\"valid\",value:e.data}}get value(){return this._def.value}}function Ot(e,t){return new Tt({values:e,typeName:Kt.ZodEnum,...Re(t)})}St.create=(e,t)=>new St({value:e,typeName:Kt.ZodLiteral,...Re(t)});class Tt extends Ce{constructor(){super(...arguments),Se.set(this,void 0)}_parse(e){if(\"string\"!=typeof e.data){const t=this._getOrReturnCtx(e),r=this._def.values;return ye(t,{expected:oe.joinValues(r),received:t.parsedType,code:de.invalid_type}),we}if(ke(this,Se,\"f\")||Ie(this,Se,new Set(this._def.values),\"f\"),!ke(this,Se,\"f\").has(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return ye(t,{received:t.data,code:de.invalid_enum_value,options:r}),we}return be(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Tt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Tt.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}Se=new WeakMap,Tt.create=Ot;class Nt extends Ce{constructor(){super(...arguments),Oe.set(this,void 0)}_parse(e){const t=oe.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==ue.string&&r.parsedType!==ue.number){const e=oe.objectValues(t);return ye(r,{expected:oe.joinValues(e),received:r.parsedType,code:de.invalid_type}),we}if(ke(this,Oe,\"f\")||Ie(this,Oe,new Set(oe.getValidEnumValues(this._def.values)),\"f\"),!ke(this,Oe,\"f\").has(e.data)){const e=oe.objectValues(t);return ye(r,{received:r.data,code:de.invalid_enum_value,options:e}),we}return be(e.data)}get enum(){return this._def.values}}Oe=new WeakMap,Nt.create=(e,t)=>new Nt({values:e,typeName:Kt.ZodNativeEnum,...Re(t)});class Rt extends Ce{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==ue.promise&&!1===t.common.async)return ye(t,{code:de.invalid_type,expected:ue.promise,received:t.parsedType}),we;const r=t.parsedType===ue.promise?t.data:Promise.resolve(t.data);return be(r.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}Rt.create=(e,t)=>new Rt({type:e,typeName:Kt.ZodPromise,...Re(t)});class Ct extends Ce{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Kt.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,i={addIssue:e=>{ye(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),\"preprocess\"===n.type){const e=n.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then((async e=>{if(\"aborted\"===t.value)return we;const n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return\"aborted\"===n.status?we:\"dirty\"===n.status||\"dirty\"===t.value?ve(n.value):n}));{if(\"aborted\"===t.value)return we;const n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return\"aborted\"===n.status?we:\"dirty\"===n.status||\"dirty\"===t.value?ve(n.value):n}}if(\"refinement\"===n.type){const e=e=>{const t=n.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error(\"Async refinement encountered during synchronous parse operation. Use .parseAsync instead.\");return e};if(!1===r.common.async){const n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return\"aborted\"===n.status?we:(\"dirty\"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((r=>\"aborted\"===r.status?we:(\"dirty\"===r.status&&t.dirty(),e(r.value).then((()=>({status:t.value,value:r.value}))))))}if(\"transform\"===n.type){if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!xe(e))return e;const s=n.transform(e.value,i);if(s instanceof Promise)throw new Error(\"Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.\");return{status:t.value,value:s}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((e=>xe(e)?Promise.resolve(n.transform(e.value,i)).then((e=>({status:t.value,value:e}))):e))}oe.assertNever(n)}}Ct.create=(e,t,r)=>new Ct({schema:e,typeName:Kt.ZodEffects,effect:t,...Re(r)}),Ct.createWithPreprocess=(e,t,r)=>new Ct({schema:t,effect:{type:\"preprocess\",transform:e},typeName:Kt.ZodEffects,...Re(r)});class Zt extends Ce{_parse(e){return this._getType(e)===ue.undefined?be(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Zt.create=(e,t)=>new Zt({innerType:e,typeName:Kt.ZodOptional,...Re(t)});class Ut extends Ce{_parse(e){return this._getType(e)===ue.null?be(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ut.create=(e,t)=>new Ut({innerType:e,typeName:Kt.ZodNullable,...Re(t)});class jt extends Ce{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===ue.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}jt.create=(e,t)=>new jt({innerType:e,typeName:Kt.ZodDefault,defaultValue:\"function\"==typeof t.default?t.default:()=>t.default,...Re(t)});class Pt extends Ce{_parse(e){const{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Ae(n)?n.then((e=>({status:\"valid\",value:\"valid\"===e.status?e.value:this._def.catchValue({get error(){return new fe(r.common.issues)},input:r.data})}))):{status:\"valid\",value:\"valid\"===n.status?n.value:this._def.catchValue({get error(){return new fe(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}Pt.create=(e,t)=>new Pt({innerType:e,typeName:Kt.ZodCatch,catchValue:\"function\"==typeof t.catch?t.catch:()=>t.catch,...Re(t)});class Lt extends Ce{_parse(e){if(this._getType(e)!==ue.nan){const t=this._getOrReturnCtx(e);return ye(t,{code:de.invalid_type,expected:ue.nan,received:t.parsedType}),we}return{status:\"valid\",value:e.data}}}Lt.create=e=>new Lt({typeName:Kt.ZodNaN,...Re(e)});const $t=Symbol(\"zod_brand\");class Mt extends Ce{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class Ft extends Ce{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return\"aborted\"===e.status?we:\"dirty\"===e.status?(t.dirty(),ve(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})()}{const e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return\"aborted\"===e.status?we:\"dirty\"===e.status?(t.dirty(),{status:\"dirty\",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new Ft({in:e,out:t,typeName:Kt.ZodPipeline})}}class zt extends Ce{_parse(e){const t=this._def.innerType._parse(e),r=e=>(xe(e)&&(e.value=Object.freeze(e.value)),e);return Ae(t)?t.then((e=>r(e))):r(t)}unwrap(){return this._def.innerType}}function Dt(e,t){const r=\"function\"==typeof e?e(t):\"string\"==typeof e?{message:e}:e;return\"string\"==typeof r?{message:r}:r}function qt(e,t={},r){return e?dt.create().superRefine(((n,i)=>{var s,o;const a=e(n);if(a instanceof Promise)return a.then((e=>{var s,o;if(!e){const e=Dt(t,n),a=null===(o=null!==(s=e.fatal)&&void 0!==s?s:r)||void 0===o||o;i.addIssue({code:\"custom\",...e,fatal:a})}}));if(!a){const e=Dt(t,n),a=null===(o=null!==(s=e.fatal)&&void 0!==s?s:r)||void 0===o||o;i.addIssue({code:\"custom\",...e,fatal:a})}})):dt.create()}zt.create=(e,t)=>new zt({innerType:e,typeName:Kt.ZodReadonly,...Re(t)});const Vt={object:yt.lazycreate};var Kt;!function(e){e.ZodString=\"ZodString\",e.ZodNumber=\"ZodNumber\",e.ZodNaN=\"ZodNaN\",e.ZodBigInt=\"ZodBigInt\",e.ZodBoolean=\"ZodBoolean\",e.ZodDate=\"ZodDate\",e.ZodSymbol=\"ZodSymbol\",e.ZodUndefined=\"ZodUndefined\",e.ZodNull=\"ZodNull\",e.ZodAny=\"ZodAny\",e.ZodUnknown=\"ZodUnknown\",e.ZodNever=\"ZodNever\",e.ZodVoid=\"ZodVoid\",e.ZodArray=\"ZodArray\",e.ZodObject=\"ZodObject\",e.ZodUnion=\"ZodUnion\",e.ZodDiscriminatedUnion=\"ZodDiscriminatedUnion\",e.ZodIntersection=\"ZodIntersection\",e.ZodTuple=\"ZodTuple\",e.ZodRecord=\"ZodRecord\",e.ZodMap=\"ZodMap\",e.ZodSet=\"ZodSet\",e.ZodFunction=\"ZodFunction\",e.ZodLazy=\"ZodLazy\",e.ZodLiteral=\"ZodLiteral\",e.ZodEnum=\"ZodEnum\",e.ZodEffects=\"ZodEffects\",e.ZodNativeEnum=\"ZodNativeEnum\",e.ZodOptional=\"ZodOptional\",e.ZodNullable=\"ZodNullable\",e.ZodDefault=\"ZodDefault\",e.ZodCatch=\"ZodCatch\",e.ZodPromise=\"ZodPromise\",e.ZodBranded=\"ZodBranded\",e.ZodPipeline=\"ZodPipeline\",e.ZodReadonly=\"ZodReadonly\"}(Kt||(Kt={}));const Ht=tt.create,Wt=nt.create,Gt=Lt.create,Jt=it.create,Yt=st.create,Xt=ot.create,Qt=at.create,er=ut.create,tr=ct.create,rr=dt.create,nr=ft.create,ir=lt.create,sr=ht.create,or=pt.create,ar=yt.create,ur=yt.strictCreate,cr=mt.create,dr=vt.create,fr=_t.create,lr=Et.create,hr=xt.create,pr=At.create,gr=kt.create,yr=It.create,mr=Bt.create,wr=St.create,vr=Tt.create,br=Nt.create,_r=Rt.create,Er=Ct.create,xr=Zt.create,Ar=Ut.create,kr=Ct.createWithPreprocess,Ir=Ft.create,Br={string:e=>tt.create({...e,coerce:!0}),number:e=>nt.create({...e,coerce:!0}),boolean:e=>st.create({...e,coerce:!0}),bigint:e=>it.create({...e,coerce:!0}),date:e=>ot.create({...e,coerce:!0})},Sr=we;var Or=Object.freeze({__proto__:null,defaultErrorMap:le,setErrorMap:function(e){he=e},getErrorMap:pe,makeIssue:ge,EMPTY_PATH:[],addIssueToContext:ye,ParseStatus:me,INVALID:we,DIRTY:ve,OK:be,isAborted:_e,isDirty:Ee,isValid:xe,isAsync:Ae,get util(){return oe},get objectUtil(){return ae},ZodParsedType:ue,getParsedType:ce,ZodType:Ce,datetimeRegex:Xe,ZodString:tt,ZodNumber:nt,ZodBigInt:it,ZodBoolean:st,ZodDate:ot,ZodSymbol:at,ZodUndefined:ut,ZodNull:ct,ZodAny:dt,ZodUnknown:ft,ZodNever:lt,ZodVoid:ht,ZodArray:pt,ZodObject:yt,ZodUnion:mt,ZodDiscriminatedUnion:vt,ZodIntersection:_t,ZodTuple:Et,ZodRecord:xt,ZodMap:At,ZodSet:kt,ZodFunction:It,ZodLazy:Bt,ZodLiteral:St,ZodEnum:Tt,ZodNativeEnum:Nt,ZodPromise:Rt,ZodEffects:Ct,ZodTransformer:Ct,ZodOptional:Zt,ZodNullable:Ut,ZodDefault:jt,ZodCatch:Pt,ZodNaN:Lt,BRAND:$t,ZodBranded:Mt,ZodPipeline:Ft,ZodReadonly:zt,custom:qt,Schema:Ce,ZodSchema:Ce,late:Vt,get ZodFirstPartyTypeKind(){return Kt},coerce:Br,any:rr,array:or,bigint:Jt,boolean:Yt,date:Xt,discriminatedUnion:dr,effect:Er,enum:vr,function:yr,instanceof:(e,t={message:`Input not instance of ${e.name}`})=>qt((t=>t instanceof e),t),intersection:fr,lazy:mr,literal:wr,map:pr,nan:Gt,nativeEnum:br,never:ir,null:tr,nullable:Ar,number:Wt,object:ar,oboolean:()=>Yt().optional(),onumber:()=>Wt().optional(),optional:xr,ostring:()=>Ht().optional(),pipeline:Ir,preprocess:kr,promise:_r,record:hr,set:gr,strictObject:ur,string:Ht,symbol:Qt,transformer:Er,tuple:lr,undefined:er,union:cr,unknown:nr,void:sr,NEVER:Sr,ZodIssueCode:de,quotelessJson:e=>JSON.stringify(e,null,2).replace(/\"([^\"]+)\":/g,\"$1:\"),ZodError:fe});function Tr(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&\"Uint8Array\"===e.constructor.name}function Nr(e,t){return!!Array.isArray(t)&&(0===t.length||(e?t.every((e=>\"string\"==typeof e)):t.every((e=>Number.isSafeInteger(e)))))}function Rr(e){if(\"function\"!=typeof e)throw new Error(\"function expected\");return!0}function Cr(e,t){if(\"string\"!=typeof t)throw new Error(`${e}: string expected`);return!0}function Zr(e){if(!Number.isSafeInteger(e))throw new Error(`invalid integer: ${e}`)}function Ur(e){if(!Array.isArray(e))throw new Error(\"array expected\")}function jr(e,t){if(!Nr(!0,t))throw new Error(`${e}: array of strings expected`)}function Pr(e,t){if(!Nr(!1,t))throw new Error(`${e}: array of numbers expected`)}function Lr(...e){const t=e=>e,r=(e,t)=>r=>e(t(r));return{encode:e.map((e=>e.encode)).reduceRight(r,t),decode:e.map((e=>e.decode)).reduce(r,t)}}function $r(e){const t=\"string\"==typeof e?e.split(\"\"):e,r=t.length;jr(\"alphabet\",t);const n=new Map(t.map(((e,t)=>[e,t])));return{encode:n=>(Ur(n),n.map((n=>{if(!Number.isSafeInteger(n)||n<0||n>=r)throw new Error(`alphabet.encode: digit index outside alphabet \"${n}\". Allowed: ${e}`);return t[n]}))),decode:t=>(Ur(t),t.map((t=>{Cr(\"alphabet.decode\",t);const r=n.get(t);if(void 0===r)throw new Error(`Unknown letter: \"${t}\". Allowed: ${e}`);return r})))}}function Mr(e=\"\"){return Cr(\"join\",e),{encode:t=>(jr(\"join.decode\",t),t.join(e)),decode:t=>(Cr(\"join.decode\",t),t.split(e))}}function Fr(e,t=\"=\"){return Zr(e),Cr(\"padding\",t),{encode(r){for(jr(\"padding.encode\",r);r.length*e%8;)r.push(t);return r},decode(r){jr(\"padding.decode\",r);let n=r.length;if(n*e%8)throw new Error(\"padding: invalid, string should have whole number of bytes\");for(;n>0&&r[n-1]===t;n--){if((n-1)*e%8==0)throw new Error(\"padding: invalid, string has too much padding\")}return r.slice(0,n)}}}function zr(e){return Rr(e),{encode:e=>e,decode:t=>e(t)}}function Dr(e,t,r){if(t<2)throw new Error(`convertRadix: invalid from=${t}, base cannot be less than 2`);if(r<2)throw new Error(`convertRadix: invalid to=${r}, base cannot be less than 2`);if(Ur(e),!e.length)return[];let n=0;const i=[],s=Array.from(e,(e=>{if(Zr(e),e<0||e>=t)throw new Error(`invalid integer: ${e}`);return e})),o=s.length;for(;;){let e=0,a=!0;for(let i=n;i<o;i++){const o=s[i],u=t*e,c=u+o;if(!Number.isSafeInteger(c)||u/t!==e||c-o!==u)throw new Error(\"convertRadix: carry overflow\");const d=c/r;e=c%r;const f=Math.floor(d);if(s[i]=f,!Number.isSafeInteger(f)||f*r+e!==c)throw new Error(\"convertRadix: carry overflow\");a&&(f?a=!1:n=i)}if(i.push(e),a)break}for(let t=0;t<e.length-1&&0===e[t];t++)i.push(0);return i.reverse()}const qr=(e,t)=>0===t?e:qr(t,e%t),Vr=(e,t)=>e+(t-qr(e,t)),Kr=(()=>{let e=[];for(let t=0;t<40;t++)e.push(2**t);return e})();function Hr(e,t,r,n){if(Ur(e),t<=0||t>32)throw new Error(`convertRadix2: wrong from=${t}`);if(r<=0||r>32)throw new Error(`convertRadix2: wrong to=${r}`);if(Vr(t,r)>32)throw new Error(`convertRadix2: carry overflow from=${t} to=${r} carryBits=${Vr(t,r)}`);let i=0,s=0;const o=Kr[t],a=Kr[r]-1,u=[];for(const n of e){if(Zr(n),n>=o)throw new Error(`convertRadix2: invalid data word=${n} from=${t}`);if(i=i<<t|n,s+t>32)throw new Error(`convertRadix2: carry overflow pos=${s} from=${t}`);for(s+=t;s>=r;s-=r)u.push((i>>s-r&a)>>>0);const e=Kr[s];if(void 0===e)throw new Error(\"invalid carry\");i&=e-1}if(i=i<<r-s&a,!n&&s>=t)throw new Error(\"Excess padding\");if(!n&&i>0)throw new Error(`Non-zero padding: ${i}`);return n&&s>0&&u.push(i>>>0),u}function Wr(e){Zr(e);return{encode:t=>{if(!Tr(t))throw new Error(\"radix.encode input should be Uint8Array\");return Dr(Array.from(t),256,e)},decode:t=>(Pr(\"radix.decode\",t),Uint8Array.from(Dr(t,e,256)))}}function Gr(e,t=!1){if(Zr(e),e<=0||e>32)throw new Error(\"radix2: bits should be in (0..32]\");if(Vr(8,e)>32||Vr(e,8)>32)throw new Error(\"radix2: carry overflow\");return{encode:r=>{if(!Tr(r))throw new Error(\"radix2.encode input should be Uint8Array\");return Hr(Array.from(r),8,e,!t)},decode:r=>(Pr(\"radix2.decode\",r),Uint8Array.from(Hr(r,e,8,t)))}}function Jr(e){return Rr(e),function(...t){try{return e.apply(null,t)}catch(e){}}}const Yr=Lr(Gr(4),$r(\"0123456789ABCDEF\"),Mr(\"\")),Xr=Lr(Gr(5),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\"),Fr(5),Mr(\"\")),Qr=(Lr(Gr(5),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\"),Mr(\"\")),Lr(Gr(5),$r(\"0123456789ABCDEFGHIJKLMNOPQRSTUV\"),Fr(5),Mr(\"\")),Lr(Gr(5),$r(\"0123456789ABCDEFGHIJKLMNOPQRSTUV\"),Mr(\"\")),Lr(Gr(5),$r(\"0123456789ABCDEFGHJKMNPQRSTVWXYZ\"),Mr(\"\"),zr((e=>e.toUpperCase().replace(/O/g,\"0\").replace(/[IL]/g,\"1\")))),Lr(Gr(6),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\"),Fr(6),Mr(\"\"))),en=(Lr(Gr(6),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\"),Mr(\"\")),Lr(Gr(6),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\"),Fr(6),Mr(\"\"))),tn=(Lr(Gr(6),$r(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\"),Mr(\"\")),e=>Lr(Wr(58),$r(e),Mr(\"\"))),rn=tn(\"123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz\"),nn=(tn(\"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ\"),tn(\"rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz\"),[0,2,3,5,6,7,9,10,11]),sn={encode(e){let t=\"\";for(let r=0;r<e.length;r+=8){const n=e.subarray(r,r+8);t+=rn.encode(n).padStart(nn[n.length],\"1\")}return t},decode(e){let t=[];for(let r=0;r<e.length;r+=11){const n=e.slice(r,r+11),i=nn.indexOf(n.length),s=rn.decode(n);for(let e=0;e<s.length-i;e++)if(0!==s[e])throw new Error(\"base58xmr: wrong padding\");t=t.concat(Array.from(s.slice(s.length-i)))}return Uint8Array.from(t)}},on=Lr($r(\"qpzry9x8gf2tvdw0s3jn54khce6mua7l\"),Mr(\"\")),an=[996825010,642813549,513874426,1027748829,705979059];function un(e){const t=e>>25;let r=(33554431&e)<<5;for(let e=0;e<an.length;e++)1==(t>>e&1)&&(r^=an[e]);return r}function cn(e,t,r=1){const n=e.length;let i=1;for(let t=0;t<n;t++){const r=e.charCodeAt(t);if(r<33||r>126)throw new Error(`Invalid prefix (${e})`);i=un(i)^r>>5}i=un(i);for(let t=0;t<n;t++)i=un(i)^31&e.charCodeAt(t);for(let e of t)i=un(i)^e;for(let e=0;e<6;e++)i=un(i);return i^=r,on.encode(Hr([i%Kr[30]],30,5,!1))}function dn(e){const t=\"bech32\"===e?1:734539939,r=Gr(5),n=r.decode,i=r.encode,s=Jr(n);function o(e,r,n=90){Cr(\"bech32.encode prefix\",e),Tr(r)&&(r=Array.from(r)),Pr(\"bech32.encode\",r);const i=e.length;if(0===i)throw new TypeError(`Invalid prefix length ${i}`);const s=i+7+r.length;if(!1!==n&&s>n)throw new TypeError(`Length ${s} exceeds limit ${n}`);const o=e.toLowerCase(),a=cn(o,r,t);return`${o}1${on.encode(r)}${a}`}function a(e,r=90){Cr(\"bech32.decode input\",e);const n=e.length;if(n<8||!1!==r&&n>r)throw new TypeError(`invalid string length: ${n} (${e}). Expected (8..${r})`);const i=e.toLowerCase();if(e!==i&&e!==e.toUpperCase())throw new Error(\"String must be lowercase or uppercase\");const s=i.lastIndexOf(\"1\");if(0===s||-1===s)throw new Error('Letter \"1\" must be present between prefix and data only');const o=i.slice(0,s),a=i.slice(s+1);if(a.length<6)throw new Error(\"Data must be at least 6 characters long\");const u=on.decode(a).slice(0,-6),c=cn(o,u,t);if(!a.endsWith(c))throw new Error(`Invalid checksum in ${e}: expected \"${c}\"`);return{prefix:o,words:u}}return{encode:o,decode:a,encodeFromBytes:function(e,t){return o(e,i(t))},decodeToBytes:function(e){const{prefix:t,words:r}=a(e,!1);return{prefix:t,words:r,bytes:n(r)}},decodeUnsafe:Jr(a),fromWords:n,fromWordsUnsafe:s,toWords:i}}dn(\"bech32\"),dn(\"bech32m\");const fn={encode:e=>(new TextDecoder).decode(e),decode:e=>(new TextEncoder).encode(e)};Lr(Gr(4),$r(\"0123456789abcdef\"),Mr(\"\"),zr((e=>{if(\"string\"!=typeof e||e.length%2!=0)throw new TypeError(`hex.decode: expected string, got ${typeof e} with length ${e.length}`);return e.toLowerCase()})));function ln(e,t){return r=e,Boolean(\"string\"==typeof r?.prototype?.constructor?.name)?new e({message:t}):e({message:t});var r}class hn extends Error{constructor(e){super(e.message),this.code=\"ERR_ASSERTION\"}}function pn(e,t=\"Assertion failed.\",r=hn){if(!e){if(t instanceof Error)throw t;throw ln(r,t)}}function gn(e,t){return yn(e,\"pattern\",(r=>t.test(r)||`Expected a ${e.type} matching \\`/${t.source}/\\` but received \"${r}\"`))}function yn(e,t,r){return new h({...e,*refiner(n,i){yield*e.refiner(n,i);const s=f(r(n,i),i,e,n);for(const e of s)yield{...e,refinement:t}}})}const mn=gn(B(),/^(?:0x)?[0-9a-f]+$/iu);gn(B(),/^0x[0-9a-f]+$/iu),gn(B(),/^0x[0-9a-f]{40}$/u),gn(B(),/^0x[0-9a-fA-F]{40}$/u);function wn(e){pn(function(e){return y(e,mn)}(e),\"Value must be a hexadecimal string.\")}function vn(e){return e.startsWith(\"0x\")||e.startsWith(\"0X\")?e.substring(2):e}r(287).hp;const bn=function(){const e=[];return()=>{if(0===e.length)for(let t=0;t<256;t++)e.push(t.toString(16).padStart(2,\"0\"));return e}}();function _n(e){return e instanceof Uint8Array}function En(e){pn(_n(e),\"Value must be a Uint8Array.\")}function xn(e){if(En(e),0===e.length)return\"0x\";const t=bn(),r=new Array(e.length);for(let n=0;n<e.length;n++)r[n]=t[e[n]];return function(e){return e.startsWith(\"0x\")?e:e.startsWith(\"0X\")?`0x${e.substring(2)}`:`0x${e}`}(r.join(\"\"))}function An(e){if(\"0x\"===e?.toLowerCase?.())return new Uint8Array;wn(e);const t=vn(e).toLowerCase(),r=t.length%2==0?t:`0${t}`,n=new Uint8Array(r.length/2);for(let e=0;e<n.length;e++){const t=r.charCodeAt(2*e),i=r.charCodeAt(2*e+1),s=t-(t<58?48:87),o=i-(i<58?48:87);n[e]=16*s+o}return n}function kn(e){return pn(\"string\"==typeof e,\"Value must be a string.\"),Qr.decode(e)}const In=\"object\"==typeof globalThis&&\"crypto\"in globalThis?globalThis.crypto:void 0;function Bn(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(\"positive integer expected, got \"+e)}function Sn(e,...t){if(!((r=e)instanceof Uint8Array||ArrayBuffer.isView(r)&&\"Uint8Array\"===r.constructor.name))throw new Error(\"Uint8Array expected\");var r;if(t.length>0&&!t.includes(e.length))throw new Error(\"Uint8Array expected of length \"+t+\", got length=\"+e.length)}function On(e,t=!0){if(e.destroyed)throw new Error(\"Hash instance has been destroyed\");if(t&&e.finished)throw new Error(\"Hash#digest() has already been called\")}function Tn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Nn(e,t){return e<<32-t|e>>>t}Array.from({length:256},((e,t)=>t.toString(16).padStart(2,\"0\")));function Rn(e){return\"string\"==typeof e&&(e=function(e){if(\"string\"!=typeof e)throw new Error(\"utf8ToBytes expected string, got \"+typeof e);return new Uint8Array((new TextEncoder).encode(e))}(e)),Sn(e),e}class Cn{clone(){return this._cloneInto()}}function Zn(e=32){if(In&&\"function\"==typeof In.getRandomValues)return In.getRandomValues(new Uint8Array(e));if(In&&\"function\"==typeof In.randomBytes)return In.randomBytes(e);throw new Error(\"crypto.getRandomValues must be defined\")}const Un=BigInt(0),jn=BigInt(1),Pn=BigInt(2);function Ln(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&\"Uint8Array\"===e.constructor.name}function $n(e){if(!Ln(e))throw new Error(\"Uint8Array expected\")}function Mn(e,t){if(\"boolean\"!=typeof t)throw new Error(e+\" boolean expected, got \"+t)}const Fn=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,\"0\")));function zn(e){$n(e);let t=\"\";for(let r=0;r<e.length;r++)t+=Fn[e[r]];return t}function Dn(e){const t=e.toString(16);return 1&t.length?\"0\"+t:t}function qn(e){if(\"string\"!=typeof e)throw new Error(\"hex string expected, got \"+typeof e);return\"\"===e?Un:BigInt(\"0x\"+e)}const Vn={_0:48,_9:57,A:65,F:70,a:97,f:102};function Kn(e){return e>=Vn._0&&e<=Vn._9?e-Vn._0:e>=Vn.A&&e<=Vn.F?e-(Vn.A-10):e>=Vn.a&&e<=Vn.f?e-(Vn.a-10):void 0}function Hn(e){if(\"string\"!=typeof e)throw new Error(\"hex string expected, got \"+typeof e);const t=e.length,r=t/2;if(t%2)throw new Error(\"hex string expected, got unpadded hex of length \"+t);const n=new Uint8Array(r);for(let t=0,i=0;t<r;t++,i+=2){const r=Kn(e.charCodeAt(i)),s=Kn(e.charCodeAt(i+1));if(void 0===r||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character \"'+t+'\" at index '+i)}n[t]=16*r+s}return n}function Wn(e){return qn(zn(e))}function Gn(e){return $n(e),qn(zn(Uint8Array.from(e).reverse()))}function Jn(e,t){return Hn(e.toString(16).padStart(2*t,\"0\"))}function Yn(e,t){return Jn(e,t).reverse()}function Xn(e,t,r){let n;if(\"string\"==typeof t)try{n=Hn(t)}catch(t){throw new Error(e+\" must be hex string or Uint8Array, cause: \"+t)}else{if(!Ln(t))throw new Error(e+\" must be hex string or Uint8Array\");n=Uint8Array.from(t)}const i=n.length;if(\"number\"==typeof r&&i!==r)throw new Error(e+\" of length \"+r+\" expected, got \"+i);return n}function Qn(...e){let t=0;for(let r=0;r<e.length;r++){const n=e[r];$n(n),t+=n.length}const r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r}const ei=e=>\"bigint\"==typeof e&&Un<=e;function ti(e,t,r){return ei(e)&&ei(t)&&ei(r)&&t<=e&&e<r}function ri(e,t,r,n){if(!ti(t,r,n))throw new Error(\"expected valid \"+e+\": \"+r+\" <= n < \"+n+\", got \"+t)}function ni(e){let t;for(t=0;e>Un;e>>=jn,t+=1);return t}const ii=e=>(Pn<<BigInt(e-1))-jn,si=e=>new Uint8Array(e),oi=e=>Uint8Array.from(e);function ai(e,t,r){if(\"number\"!=typeof e||e<2)throw new Error(\"hashLen must be a number\");if(\"number\"!=typeof t||t<2)throw new Error(\"qByteLen must be a number\");if(\"function\"!=typeof r)throw new Error(\"hmacFn must be a function\");let n=si(e),i=si(e),s=0;const o=()=>{n.fill(1),i.fill(0),s=0},a=(...e)=>r(i,n,...e),u=(e=si())=>{i=a(oi([0]),e),n=a(),0!==e.length&&(i=a(oi([1]),e),n=a())},c=()=>{if(s++>=1e3)throw new Error(\"drbg: tried 1000 values\");let e=0;const r=[];for(;e<t;){n=a();const t=n.slice();r.push(t),e+=n.length}return Qn(...r)};return(e,t)=>{let r;for(o(),u(e);!(r=t(c()));)u();return o(),r}}const ui={bigint:e=>\"bigint\"==typeof e,function:e=>\"function\"==typeof e,boolean:e=>\"boolean\"==typeof e,string:e=>\"string\"==typeof e,stringOrUint8Array:e=>\"string\"==typeof e||Ln(e),isSafeInteger:e=>Number.isSafeInteger(e),array:e=>Array.isArray(e),field:(e,t)=>t.Fp.isValid(e),hash:e=>\"function\"==typeof e&&Number.isSafeInteger(e.outputLen)};function ci(e,t,r={}){const n=(t,r,n)=>{const i=ui[r];if(\"function\"!=typeof i)throw new Error(\"invalid validator function\");const s=e[t];if(!(n&&void 0===s||i(s,e)))throw new Error(\"param \"+String(t)+\" is invalid. Expected \"+r+\", got \"+s)};for(const[e,r]of Object.entries(t))n(e,r,!1);for(const[e,t]of Object.entries(r))n(e,t,!0);return e}function di(e){const t=new WeakMap;return(r,...n)=>{const i=t.get(r);if(void 0!==i)return i;const s=e(r,...n);return t.set(r,s),s}}const fi=BigInt(0),li=BigInt(1),hi=BigInt(2),pi=BigInt(3),gi=BigInt(4),yi=BigInt(5),mi=BigInt(8);BigInt(9),BigInt(16);function wi(e,t){const r=e%t;return r>=fi?r:t+r}function vi(e,t,r){if(t<fi)throw new Error(\"invalid exponent, negatives unsupported\");if(r<=fi)throw new Error(\"invalid modulus\");if(r===li)return fi;let n=li;for(;t>fi;)t&li&&(n=n*e%r),e=e*e%r,t>>=li;return n}function bi(e,t,r){let n=e;for(;t-- >fi;)n*=n,n%=r;return n}function _i(e,t){if(e===fi)throw new Error(\"invert: expected non-zero number\");if(t<=fi)throw new Error(\"invert: expected positive modulus, got \"+t);let r=wi(e,t),n=t,i=fi,s=li,o=li,a=fi;for(;r!==fi;){const e=n/r,t=n%r,u=i-o*e,c=s-a*e;n=r,r=t,i=o,s=a,o=u,a=c}if(n!==li)throw new Error(\"invert: does not exist\");return wi(i,t)}function Ei(e){if(e%gi===pi){const t=(e+li)/gi;return function(e,r){const n=e.pow(r,t);if(!e.eql(e.sqr(n),r))throw new Error(\"Cannot find square root\");return n}}if(e%mi===yi){const t=(e-yi)/mi;return function(e,r){const n=e.mul(r,hi),i=e.pow(n,t),s=e.mul(r,i),o=e.mul(e.mul(s,hi),i),a=e.mul(s,e.sub(o,e.ONE));if(!e.eql(e.sqr(a),r))throw new Error(\"Cannot find square root\");return a}}return function(e){const t=(e-li)/hi;let r,n,i;for(r=e-li,n=0;r%hi===fi;r/=hi,n++);for(i=hi;i<e&&vi(i,t,e)!==e-li;i++)if(i>1e3)throw new Error(\"Cannot find square root: likely non-prime P\");if(1===n){const t=(e+li)/gi;return function(e,r){const n=e.pow(r,t);if(!e.eql(e.sqr(n),r))throw new Error(\"Cannot find square root\");return n}}const s=(r+li)/hi;return function(e,o){if(e.pow(o,t)===e.neg(e.ONE))throw new Error(\"Cannot find square root\");let a=n,u=e.pow(e.mul(e.ONE,i),r),c=e.pow(o,s),d=e.pow(o,r);for(;!e.eql(d,e.ONE);){if(e.eql(d,e.ZERO))return e.ZERO;let t=1;for(let r=e.sqr(d);t<a&&!e.eql(r,e.ONE);t++)r=e.sqr(r);const r=e.pow(u,li<<BigInt(a-t-1));u=e.sqr(r),c=e.mul(c,r),d=e.mul(d,u),a=t}return c}}(e)}const xi=[\"create\",\"isValid\",\"is0\",\"neg\",\"inv\",\"sqrt\",\"sqr\",\"eql\",\"add\",\"sub\",\"mul\",\"pow\",\"div\",\"addN\",\"subN\",\"mulN\",\"sqrN\"];function Ai(e,t){const r=void 0!==t?t:e.toString(2).length;return{nBitLength:r,nByteLength:Math.ceil(r/8)}}function ki(e,t,r=!1,n={}){if(e<=fi)throw new Error(\"invalid field: expected ORDER > 0, got \"+e);const{nBitLength:i,nByteLength:s}=Ai(e,t);if(s>2048)throw new Error(\"invalid field: expected ORDER of <= 2048 bytes\");let o;const a=Object.freeze({ORDER:e,isLE:r,BITS:i,BYTES:s,MASK:ii(i),ZERO:fi,ONE:li,create:t=>wi(t,e),isValid:t=>{if(\"bigint\"!=typeof t)throw new Error(\"invalid field element: expected bigint, got \"+typeof t);return fi<=t&&t<e},is0:e=>e===fi,isOdd:e=>(e&li)===li,neg:t=>wi(-t,e),eql:(e,t)=>e===t,sqr:t=>wi(t*t,e),add:(t,r)=>wi(t+r,e),sub:(t,r)=>wi(t-r,e),mul:(t,r)=>wi(t*r,e),pow:(e,t)=>function(e,t,r){if(r<fi)throw new Error(\"invalid exponent, negatives unsupported\");if(r===fi)return e.ONE;if(r===li)return t;let n=e.ONE,i=t;for(;r>fi;)r&li&&(n=e.mul(n,i)),i=e.sqr(i),r>>=li;return n}(a,e,t),div:(t,r)=>wi(t*_i(r,e),e),sqrN:e=>e*e,addN:(e,t)=>e+t,subN:(e,t)=>e-t,mulN:(e,t)=>e*t,inv:t=>_i(t,e),sqrt:n.sqrt||(t=>(o||(o=Ei(e)),o(a,t))),invertBatch:e=>function(e,t){const r=new Array(t.length),n=t.reduce(((t,n,i)=>e.is0(n)?t:(r[i]=t,e.mul(t,n))),e.ONE),i=e.inv(n);return t.reduceRight(((t,n,i)=>e.is0(n)?t:(r[i]=e.mul(t,r[i]),e.mul(t,n))),i),r}(a,e),cmov:(e,t,r)=>r?t:e,toBytes:e=>r?Yn(e,s):Jn(e,s),fromBytes:e=>{if(e.length!==s)throw new Error(\"Field.fromBytes: expected \"+s+\" bytes, got \"+e.length);return r?Gn(e):Wn(e)}});return Object.freeze(a)}function Ii(e){if(\"bigint\"!=typeof e)throw new Error(\"field order must be bigint\");const t=e.toString(2).length;return Math.ceil(t/8)}function Bi(e){const t=Ii(e);return t+Math.ceil(t/2)}const Si=BigInt(0),Oi=BigInt(1);function Ti(e){const t=(ci(r=e,{a:\"bigint\"},{montgomeryBits:\"isSafeInteger\",nByteLength:\"isSafeInteger\",adjustScalarBytes:\"function\",domain:\"function\",powPminus2:\"function\",Gu:\"bigint\"}),Object.freeze({...r}));var r;const{P:n}=t,i=e=>wi(e,n),s=t.montgomeryBits,o=Math.ceil(s/8),a=t.nByteLength,u=t.adjustScalarBytes||(e=>e),c=t.powPminus2||(e=>vi(e,n-BigInt(2),n));function d(e,t,r){const n=i(e*(t-r));return[t=i(t-n),r=i(r+n)]}const f=(t.a-BigInt(2))/BigInt(4);function l(e){return Yn(i(e),o)}function h(e,t){const r=function(e){const t=Xn(\"u coordinate\",e,o);return 32===a&&(t[31]&=127),Gn(t)}(t),h=function(e){const t=Xn(\"scalar\",e),r=t.length;if(r!==o&&r!==a)throw new Error(\"invalid scalar, expected \"+o+\" or \"+a+\" bytes, got \"+r);return Gn(u(t))}(e),p=function(e,t){ri(\"u\",e,Si,n),ri(\"scalar\",t,Si,n);const r=t,o=e;let a,u=Oi,l=Si,h=e,p=Oi,g=Si;for(let e=BigInt(s-1);e>=Si;e--){const t=r>>e&Oi;g^=t,a=d(g,u,h),u=a[0],h=a[1],a=d(g,l,p),l=a[0],p=a[1],g=t;const n=u+l,s=i(n*n),c=u-l,y=i(c*c),m=s-y,w=h+p,v=i((h-p)*n),b=i(w*c),_=v+b,E=v-b;h=i(_*_),p=i(o*i(E*E)),u=i(s*y),l=i(m*(s+i(f*m)))}a=d(g,u,h),u=a[0],h=a[1],a=d(g,l,p),l=a[0],p=a[1];const y=c(l);return i(u*y)}(r,h);if(p===Si)throw new Error(\"invalid private or public key received\");return l(p)}const p=l(t.Gu);function g(e){return h(e,p)}return{scalarMult:h,scalarMultBase:g,getSharedSecret:(e,t)=>h(e,t),getPublicKey:e=>g(e),utils:{randomPrivateKey:()=>t.randomBytes(t.nByteLength)},GuBytes:p}}const Ni=BigInt(\"57896044618658097711785492504343953926634992332820282019728792003956564819949\"),Ri=(BigInt(0),BigInt(1)),Ci=BigInt(2),Zi=BigInt(3),Ui=BigInt(5);BigInt(8);function ji(e){const t=BigInt(10),r=BigInt(20),n=BigInt(40),i=BigInt(80),s=Ni,o=e*e%s*e%s,a=bi(o,Ci,s)*o%s,u=bi(a,Ri,s)*e%s,c=bi(u,Ui,s)*u%s,d=bi(c,t,s)*c%s,f=bi(d,r,s)*d%s,l=bi(f,n,s)*f%s,h=bi(l,i,s)*l%s,p=bi(h,i,s)*l%s,g=bi(p,t,s)*c%s;return{pow_p_5_8:bi(g,Ci,s)*e%s,b2:o}}function Pi(e){return e[0]&=248,e[31]&=127,e[31]|=64,e}const Li=Ti({P:Ni,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:e=>{const t=Ni,{pow_p_5_8:r,b2:n}=ji(e);return wi(bi(r,Zi,t)*n,t)},adjustScalarBytes:Pi,randomBytes:Zn});function $i(e,t,r){return e&t^e&r^t&r}class Mi extends Cn{constructor(e,t,r,n){super(),this.blockLen=e,this.outputLen=t,this.padOffset=r,this.isLE=n,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Tn(this.buffer)}update(e){On(this);const{view:t,buffer:r,blockLen:n}=this,i=(e=Rn(e)).length;for(let s=0;s<i;){const o=Math.min(n-this.pos,i-s);if(o!==n)r.set(e.subarray(s,s+o),this.pos),this.pos+=o,s+=o,this.pos===n&&(this.process(t,0),this.pos=0);else{const t=Tn(e);for(;n<=i-s;s+=n)this.process(t,s)}}return this.length+=e.length,this.roundClean(),this}digestInto(e){On(this),function(e,t){Sn(e);const r=t.outputLen;if(e.length<r)throw new Error(\"digestInto() expects output buffer of length at least \"+r)}(e,this),this.finished=!0;const{buffer:t,view:r,blockLen:n,isLE:i}=this;let{pos:s}=this;t[s++]=128,this.buffer.subarray(s).fill(0),this.padOffset>n-s&&(this.process(r,0),s=0);for(let e=s;e<n;e++)t[e]=0;!function(e,t,r,n){if(\"function\"==typeof e.setBigUint64)return e.setBigUint64(t,r,n);const i=BigInt(32),s=BigInt(4294967295),o=Number(r>>i&s),a=Number(r&s),u=n?4:0,c=n?0:4;e.setUint32(t+u,o,n),e.setUint32(t+c,a,n)}(r,n-8,BigInt(8*this.length),i),this.process(r,0);const o=Tn(e),a=this.outputLen;if(a%4)throw new Error(\"_sha2: outputLen should be aligned to 32bit\");const u=a/4,c=this.get();if(u>c.length)throw new Error(\"_sha2: outputLen bigger than state\");for(let e=0;e<u;e++)o.setUint32(4*e,c[e],i)}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const r=e.slice(0,t);return this.destroy(),r}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:t,buffer:r,length:n,finished:i,destroyed:s,pos:o}=this;return e.length=n,e.pos=o,e.finished=i,e.destroyed=s,n%t&&e.buffer.set(r),e}}const Fi=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),zi=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Di=new Uint32Array(64);class qi extends Mi{constructor(){super(64,32,8,!1),this.A=0|zi[0],this.B=0|zi[1],this.C=0|zi[2],this.D=0|zi[3],this.E=0|zi[4],this.F=0|zi[5],this.G=0|zi[6],this.H=0|zi[7]}get(){const{A:e,B:t,C:r,D:n,E:i,F:s,G:o,H:a}=this;return[e,t,r,n,i,s,o,a]}set(e,t,r,n,i,s,o,a){this.A=0|e,this.B=0|t,this.C=0|r,this.D=0|n,this.E=0|i,this.F=0|s,this.G=0|o,this.H=0|a}process(e,t){for(let r=0;r<16;r++,t+=4)Di[r]=e.getUint32(t,!1);for(let e=16;e<64;e++){const t=Di[e-15],r=Di[e-2],n=Nn(t,7)^Nn(t,18)^t>>>3,i=Nn(r,17)^Nn(r,19)^r>>>10;Di[e]=i+Di[e-7]+n+Di[e-16]|0}let{A:r,B:n,C:i,D:s,E:o,F:a,G:u,H:c}=this;for(let e=0;e<64;e++){const t=c+(Nn(o,6)^Nn(o,11)^Nn(o,25))+((d=o)&a^~d&u)+Fi[e]+Di[e]|0,f=(Nn(r,2)^Nn(r,13)^Nn(r,22))+$i(r,n,i)|0;c=u,u=a,a=o,o=s+t|0,s=i,i=n,n=r,r=t+f|0}var d;r=r+this.A|0,n=n+this.B|0,i=i+this.C|0,s=s+this.D|0,o=o+this.E|0,a=a+this.F|0,u=u+this.G|0,c=c+this.H|0,this.set(r,n,i,s,o,a,u,c)}roundClean(){Di.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Vi=function(e){const t=t=>e().update(Rn(t)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}((()=>new qi));class Ki extends Cn{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,function(e){if(\"function\"!=typeof e||\"function\"!=typeof e.create)throw new Error(\"Hash should be wrapped by utils.wrapConstructor\");Bn(e.outputLen),Bn(e.blockLen)}(e);const r=Rn(t);if(this.iHash=e.create(),\"function\"!=typeof this.iHash.update)throw new Error(\"Expected instance of class which extends utils.Hash\");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const n=this.blockLen,i=new Uint8Array(n);i.set(r.length>n?e.create().update(r).digest():r);for(let e=0;e<i.length;e++)i[e]^=54;this.iHash.update(i),this.oHash=e.create();for(let e=0;e<i.length;e++)i[e]^=106;this.oHash.update(i),i.fill(0)}update(e){return On(this),this.iHash.update(e),this}digestInto(e){On(this),Sn(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:r,finished:n,destroyed:i,blockLen:s,outputLen:o}=this;return e.finished=n,e.destroyed=i,e.blockLen=s,e.outputLen=o,e.oHash=t._cloneInto(e.oHash),e.iHash=r._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Hi=(e,t,r)=>new Ki(e,t).update(r).digest();Hi.create=(e,t)=>new Ki(e,t);const Wi=BigInt(0),Gi=BigInt(1);function Ji(e,t){const r=t.negate();return e?r:t}function Yi(e,t){if(!Number.isSafeInteger(e)||e<=0||e>t)throw new Error(\"invalid window size, expected [1..\"+t+\"], got W=\"+e)}function Xi(e,t){Yi(e,t);return{windows:Math.ceil(t/e)+1,windowSize:2**(e-1)}}function Qi(e,t){if(!Array.isArray(e))throw new Error(\"array expected\");e.forEach(((e,r)=>{if(!(e instanceof t))throw new Error(\"invalid point at index \"+r)}))}function es(e,t){if(!Array.isArray(e))throw new Error(\"array of scalars expected\");e.forEach(((e,r)=>{if(!t.isValid(e))throw new Error(\"invalid scalar at index \"+r)}))}const ts=new WeakMap,rs=new WeakMap;function ns(e){return rs.get(e)||1}function is(e){return function(e){const t=xi.reduce(((e,t)=>(e[t]=\"function\",e)),{ORDER:\"bigint\",MASK:\"bigint\",BYTES:\"isSafeInteger\",BITS:\"isSafeInteger\"});ci(e,t)}(e.Fp),ci(e,{n:\"bigint\",h:\"bigint\",Gx:\"field\",Gy:\"field\"},{nBitLength:\"isSafeInteger\",nByteLength:\"isSafeInteger\"}),Object.freeze({...Ai(e.n,e.nBitLength),...e,p:e.Fp.ORDER})}function ss(e){void 0!==e.lowS&&Mn(\"lowS\",e.lowS),void 0!==e.prehash&&Mn(\"prehash\",e.prehash)}const{Ph:os,aT:as}=e;class us extends Error{constructor(e=\"\"){super(e)}}const cs={Err:us,_tlv:{encode:(e,t)=>{const{Err:r}=cs;if(e<0||e>256)throw new r(\"tlv.encode: wrong tag\");if(1&t.length)throw new r(\"tlv.encode: unpadded data\");const n=t.length/2,i=Dn(n);if(i.length/2&128)throw new r(\"tlv.encode: long form length too big\");const s=n>127?Dn(i.length/2|128):\"\";return Dn(e)+s+i+t},decode(e,t){const{Err:r}=cs;let n=0;if(e<0||e>256)throw new r(\"tlv.encode: wrong tag\");if(t.length<2||t[n++]!==e)throw new r(\"tlv.decode: wrong tlv\");const i=t[n++];let s=0;if(!!(128&i)){const e=127&i;if(!e)throw new r(\"tlv.decode(long): indefinite length not supported\");if(e>4)throw new r(\"tlv.decode(long): byte length is too big\");const o=t.subarray(n,n+e);if(o.length!==e)throw new r(\"tlv.decode: length bytes not complete\");if(0===o[0])throw new r(\"tlv.decode(long): zero leftmost byte\");for(const e of o)s=s<<8|e;if(n+=e,s<128)throw new r(\"tlv.decode(long): not minimal encoding\")}else s=i;const o=t.subarray(n,n+s);if(o.length!==s)throw new r(\"tlv.decode: wrong value length\");return{v:o,l:t.subarray(n+s)}}},_int:{encode(e){const{Err:t}=cs;if(e<ds)throw new t(\"integer: negative integers are not allowed\");let r=Dn(e);if(8&Number.parseInt(r[0],16)&&(r=\"00\"+r),1&r.length)throw new t(\"unexpected DER parsing assertion: unpadded hex\");return r},decode(e){const{Err:t}=cs;if(128&e[0])throw new t(\"invalid signature integer: negative\");if(0===e[0]&&!(128&e[1]))throw new t(\"invalid signature integer: unnecessary leading zero\");return os(e)}},toSig(e){const{Err:t,_int:r,_tlv:n}=cs,i=\"string\"==typeof e?as(e):e;$n(i);const{v:s,l:o}=n.decode(48,i);if(o.length)throw new t(\"invalid signature: left bytes after parsing\");const{v:a,l:u}=n.decode(2,s),{v:c,l:d}=n.decode(2,u);if(d.length)throw new t(\"invalid signature: left bytes after parsing\");return{r:r.decode(a),s:r.decode(c)}},hexFromSig(e){const{_tlv:t,_int:r}=cs,n=t.encode(2,r.encode(e.r))+t.encode(2,r.encode(e.s));return t.encode(48,n)}},ds=BigInt(0),fs=BigInt(1),ls=(BigInt(2),BigInt(3));BigInt(4);function hs(e){const t=function(e){const t=is(e);ci(t,{a:\"field\",b:\"field\"},{allowedPrivateKeyLengths:\"array\",wrapPrivateKey:\"boolean\",isTorsionFree:\"function\",clearCofactor:\"function\",allowInfinityPoint:\"boolean\",fromBytes:\"function\",toBytes:\"function\"});const{endo:r,Fp:n,a:i}=t;if(r){if(!n.eql(i,n.ZERO))throw new Error(\"invalid endomorphism, can only be defined for Koblitz curves that have a=0\");if(\"object\"!=typeof r||\"bigint\"!=typeof r.beta||\"function\"!=typeof r.splitScalar)throw new Error(\"invalid endomorphism, expected beta: bigint and splitScalar: function\")}return Object.freeze({...t})}(e),{Fp:r}=t,n=ki(t.n,t.nBitLength),i=t.toBytes||((e,t,n)=>{const i=t.toAffine();return Qn(Uint8Array.from([4]),r.toBytes(i.x),r.toBytes(i.y))}),s=t.fromBytes||(e=>{const t=e.subarray(1);return{x:r.fromBytes(t.subarray(0,r.BYTES)),y:r.fromBytes(t.subarray(r.BYTES,2*r.BYTES))}});function o(e){const{a:n,b:i}=t,s=r.sqr(e),o=r.mul(s,e);return r.add(r.add(o,r.mul(e,n)),i)}if(!r.eql(r.sqr(t.Gy),o(t.Gx)))throw new Error(\"bad generator point: equation left != right\");function a(e){const{allowedPrivateKeyLengths:r,nByteLength:n,wrapPrivateKey:i,n:s}=t;if(r&&\"bigint\"!=typeof e){if(Ln(e)&&(e=zn(e)),\"string\"!=typeof e||!r.includes(e.length))throw new Error(\"invalid private key\");e=e.padStart(2*n,\"0\")}let o;try{o=\"bigint\"==typeof e?e:Wn(Xn(\"private key\",e,n))}catch(t){throw new Error(\"invalid private key, expected hex or \"+n+\" bytes, got \"+typeof e)}return i&&(o=wi(o,s)),ri(\"private key\",o,fs,s),o}function u(e){if(!(e instanceof f))throw new Error(\"ProjectivePoint expected\")}const c=di(((e,t)=>{const{px:n,py:i,pz:s}=e;if(r.eql(s,r.ONE))return{x:n,y:i};const o=e.is0();null==t&&(t=o?r.ONE:r.inv(s));const a=r.mul(n,t),u=r.mul(i,t),c=r.mul(s,t);if(o)return{x:r.ZERO,y:r.ZERO};if(!r.eql(c,r.ONE))throw new Error(\"invZ was invalid\");return{x:a,y:u}})),d=di((e=>{if(e.is0()){if(t.allowInfinityPoint&&!r.is0(e.py))return;throw new Error(\"bad point: ZERO\")}const{x:n,y:i}=e.toAffine();if(!r.isValid(n)||!r.isValid(i))throw new Error(\"bad point: x or y not FE\");const s=r.sqr(i),a=o(n);if(!r.eql(s,a))throw new Error(\"bad point: equation left != right\");if(!e.isTorsionFree())throw new Error(\"bad point: not in prime-order subgroup\");return!0}));class f{constructor(e,t,n){if(this.px=e,this.py=t,this.pz=n,null==e||!r.isValid(e))throw new Error(\"x required\");if(null==t||!r.isValid(t))throw new Error(\"y required\");if(null==n||!r.isValid(n))throw new Error(\"z required\");Object.freeze(this)}static fromAffine(e){const{x:t,y:n}=e||{};if(!e||!r.isValid(t)||!r.isValid(n))throw new Error(\"invalid affine point\");if(e instanceof f)throw new Error(\"projective point not allowed\");const i=e=>r.eql(e,r.ZERO);return i(t)&&i(n)?f.ZERO:new f(t,n,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(e){const t=r.invertBatch(e.map((e=>e.pz)));return e.map(((e,r)=>e.toAffine(t[r]))).map(f.fromAffine)}static fromHex(e){const t=f.fromAffine(s(Xn(\"pointHex\",e)));return t.assertValidity(),t}static fromPrivateKey(e){return f.BASE.multiply(a(e))}static msm(e,t){return function(e,t,r,n){if(Qi(r,e),es(n,t),r.length!==n.length)throw new Error(\"arrays of points and scalars must have equal length\");const i=e.ZERO,s=ni(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,u=new Array(a+1).fill(i);let c=i;for(let e=Math.floor((t.BITS-1)/o)*o;e>=0;e-=o){u.fill(i);for(let t=0;t<n.length;t++){const i=n[t],s=Number(i>>BigInt(e)&BigInt(a));u[s]=u[s].add(r[t])}let t=i;for(let e=u.length-1,r=i;e>0;e--)r=r.add(u[e]),t=t.add(r);if(c=c.add(t),0!==e)for(let e=0;e<o;e++)c=c.double()}return c}(f,n,e,t)}_setWindowSize(e){h.setWindowSize(this,e)}assertValidity(){d(this)}hasEvenY(){const{y:e}=this.toAffine();if(r.isOdd)return!r.isOdd(e);throw new Error(\"Field doesn't support isOdd\")}equals(e){u(e);const{px:t,py:n,pz:i}=this,{px:s,py:o,pz:a}=e,c=r.eql(r.mul(t,a),r.mul(s,i)),d=r.eql(r.mul(n,a),r.mul(o,i));return c&&d}negate(){return new f(this.px,r.neg(this.py),this.pz)}double(){const{a:e,b:n}=t,i=r.mul(n,ls),{px:s,py:o,pz:a}=this;let u=r.ZERO,c=r.ZERO,d=r.ZERO,l=r.mul(s,s),h=r.mul(o,o),p=r.mul(a,a),g=r.mul(s,o);return g=r.add(g,g),d=r.mul(s,a),d=r.add(d,d),u=r.mul(e,d),c=r.mul(i,p),c=r.add(u,c),u=r.sub(h,c),c=r.add(h,c),c=r.mul(u,c),u=r.mul(g,u),d=r.mul(i,d),p=r.mul(e,p),g=r.sub(l,p),g=r.mul(e,g),g=r.add(g,d),d=r.add(l,l),l=r.add(d,l),l=r.add(l,p),l=r.mul(l,g),c=r.add(c,l),p=r.mul(o,a),p=r.add(p,p),l=r.mul(p,g),u=r.sub(u,l),d=r.mul(p,h),d=r.add(d,d),d=r.add(d,d),new f(u,c,d)}add(e){u(e);const{px:n,py:i,pz:s}=this,{px:o,py:a,pz:c}=e;let d=r.ZERO,l=r.ZERO,h=r.ZERO;const p=t.a,g=r.mul(t.b,ls);let y=r.mul(n,o),m=r.mul(i,a),w=r.mul(s,c),v=r.add(n,i),b=r.add(o,a);v=r.mul(v,b),b=r.add(y,m),v=r.sub(v,b),b=r.add(n,s);let _=r.add(o,c);return b=r.mul(b,_),_=r.add(y,w),b=r.sub(b,_),_=r.add(i,s),d=r.add(a,c),_=r.mul(_,d),d=r.add(m,w),_=r.sub(_,d),h=r.mul(p,b),d=r.mul(g,w),h=r.add(d,h),d=r.sub(m,h),h=r.add(m,h),l=r.mul(d,h),m=r.add(y,y),m=r.add(m,y),w=r.mul(p,w),b=r.mul(g,b),m=r.add(m,w),w=r.sub(y,w),w=r.mul(p,w),b=r.add(b,w),y=r.mul(m,b),l=r.add(l,y),y=r.mul(_,b),d=r.mul(v,d),d=r.sub(d,y),y=r.mul(v,m),h=r.mul(_,h),h=r.add(h,y),new f(d,l,h)}subtract(e){return this.add(e.negate())}is0(){return this.equals(f.ZERO)}wNAF(e){return h.wNAFCached(this,e,f.normalizeZ)}multiplyUnsafe(e){const{endo:n,n:i}=t;ri(\"scalar\",e,ds,i);const s=f.ZERO;if(e===ds)return s;if(this.is0()||e===fs)return this;if(!n||h.hasPrecomputes(this))return h.wNAFCachedUnsafe(this,e,f.normalizeZ);let{k1neg:o,k1:a,k2neg:u,k2:c}=n.splitScalar(e),d=s,l=s,p=this;for(;a>ds||c>ds;)a&fs&&(d=d.add(p)),c&fs&&(l=l.add(p)),p=p.double(),a>>=fs,c>>=fs;return o&&(d=d.negate()),u&&(l=l.negate()),l=new f(r.mul(l.px,n.beta),l.py,l.pz),d.add(l)}multiply(e){const{endo:n,n:i}=t;let s,o;if(ri(\"scalar\",e,fs,i),n){const{k1neg:t,k1:i,k2neg:a,k2:u}=n.splitScalar(e);let{p:c,f:d}=this.wNAF(i),{p:l,f:p}=this.wNAF(u);c=h.constTimeNegate(t,c),l=h.constTimeNegate(a,l),l=new f(r.mul(l.px,n.beta),l.py,l.pz),s=c.add(l),o=d.add(p)}else{const{p:t,f:r}=this.wNAF(e);s=t,o=r}return f.normalizeZ([s,o])[0]}multiplyAndAddUnsafe(e,t,r){const n=f.BASE,i=(e,t)=>t!==ds&&t!==fs&&e.equals(n)?e.multiply(t):e.multiplyUnsafe(t),s=i(this,t).add(i(e,r));return s.is0()?void 0:s}toAffine(e){return c(this,e)}isTorsionFree(){const{h:e,isTorsionFree:r}=t;if(e===fs)return!0;if(r)return r(f,this);throw new Error(\"isTorsionFree() has not been declared for the elliptic curve\")}clearCofactor(){const{h:e,clearCofactor:r}=t;return e===fs?this:r?r(f,this):this.multiplyUnsafe(t.h)}toRawBytes(e=!0){return Mn(\"isCompressed\",e),this.assertValidity(),i(f,this,e)}toHex(e=!0){return Mn(\"isCompressed\",e),zn(this.toRawBytes(e))}}f.BASE=new f(t.Gx,t.Gy,r.ONE),f.ZERO=new f(r.ZERO,r.ONE,r.ZERO);const l=t.nBitLength,h=(p=f,g=t.endo?Math.ceil(l/2):l,{constTimeNegate:Ji,hasPrecomputes:e=>1!==ns(e),unsafeLadder(e,t,r=p.ZERO){let n=e;for(;t>Wi;)t&Gi&&(r=r.add(n)),n=n.double(),t>>=Gi;return r},precomputeWindow(e,t){const{windows:r,windowSize:n}=Xi(t,g),i=[];let s=e,o=s;for(let e=0;e<r;e++){o=s,i.push(o);for(let e=1;e<n;e++)o=o.add(s),i.push(o);s=o.double()}return i},wNAF(e,t,r){const{windows:n,windowSize:i}=Xi(e,g);let s=p.ZERO,o=p.BASE;const a=BigInt(2**e-1),u=2**e,c=BigInt(e);for(let e=0;e<n;e++){const n=e*i;let d=Number(r&a);r>>=c,d>i&&(d-=u,r+=Gi);const f=n,l=n+Math.abs(d)-1,h=e%2!=0,p=d<0;0===d?o=o.add(Ji(h,t[f])):s=s.add(Ji(p,t[l]))}return{p:s,f:o}},wNAFUnsafe(e,t,r,n=p.ZERO){const{windows:i,windowSize:s}=Xi(e,g),o=BigInt(2**e-1),a=2**e,u=BigInt(e);for(let e=0;e<i;e++){const i=e*s;if(r===Wi)break;let c=Number(r&o);if(r>>=u,c>s&&(c-=a,r+=Gi),0===c)continue;let d=t[i+Math.abs(c)-1];c<0&&(d=d.negate()),n=n.add(d)}return n},getPrecomputes(e,t,r){let n=ts.get(t);return n||(n=this.precomputeWindow(t,e),1!==e&&ts.set(t,r(n))),n},wNAFCached(e,t,r){const n=ns(e);return this.wNAF(n,this.getPrecomputes(n,e,r),t)},wNAFCachedUnsafe(e,t,r,n){const i=ns(e);return 1===i?this.unsafeLadder(e,t,n):this.wNAFUnsafe(i,this.getPrecomputes(i,e,r),t,n)},setWindowSize(e,t){Yi(t,g),rs.set(e,t),ts.delete(e)}});var p,g;return{CURVE:t,ProjectivePoint:f,normPrivateKeyToScalar:a,weierstrassEquation:o,isWithinCurveOrder:function(e){return ti(e,fs,t.n)}}}function ps(e){const t=function(e){const t=is(e);return ci(t,{hash:\"hash\",hmac:\"function\",randomBytes:\"function\"},{bits2int:\"function\",bits2int_modN:\"function\",lowS:\"boolean\"}),Object.freeze({lowS:!0,...t})}(e),{Fp:r,n}=t,i=r.BYTES+1,s=2*r.BYTES+1;function o(e){return wi(e,n)}function a(e){return _i(e,n)}const{ProjectivePoint:u,normPrivateKeyToScalar:c,weierstrassEquation:d,isWithinCurveOrder:f}=hs({...t,toBytes(e,t,n){const i=t.toAffine(),s=r.toBytes(i.x),o=Qn;return Mn(\"isCompressed\",n),n?o(Uint8Array.from([t.hasEvenY()?2:3]),s):o(Uint8Array.from([4]),s,r.toBytes(i.y))},fromBytes(e){const t=e.length,n=e[0],o=e.subarray(1);if(t!==i||2!==n&&3!==n){if(t===s&&4===n){return{x:r.fromBytes(o.subarray(0,r.BYTES)),y:r.fromBytes(o.subarray(r.BYTES,2*r.BYTES))}}throw new Error(\"invalid Point, expected length of \"+i+\", or uncompressed \"+s+\", got \"+t)}{const e=Wn(o);if(!ti(e,fs,r.ORDER))throw new Error(\"Point is not on curve\");const t=d(e);let i;try{i=r.sqrt(t)}catch(e){const t=e instanceof Error?\": \"+e.message:\"\";throw new Error(\"Point is not on curve\"+t)}return!(1&~n)!==((i&fs)===fs)&&(i=r.neg(i)),{x:e,y:i}}}}),l=e=>zn(Jn(e,t.nByteLength));function h(e){return e>n>>fs}const p=(e,t,r)=>Wn(e.slice(t,r));class g{constructor(e,t,r){this.r=e,this.s=t,this.recovery=r,this.assertValidity()}static fromCompact(e){const r=t.nByteLength;return e=Xn(\"compactSignature\",e,2*r),new g(p(e,0,r),p(e,r,2*r))}static fromDER(e){const{r:t,s:r}=cs.toSig(Xn(\"DER\",e));return new g(t,r)}assertValidity(){ri(\"r\",this.r,fs,n),ri(\"s\",this.s,fs,n)}addRecoveryBit(e){return new g(this.r,this.s,e)}recoverPublicKey(e){const{r:n,s:i,recovery:s}=this,c=v(Xn(\"msgHash\",e));if(null==s||![0,1,2,3].includes(s))throw new Error(\"recovery id invalid\");const d=2===s||3===s?n+t.n:n;if(d>=r.ORDER)throw new Error(\"recovery id 2 or 3 invalid\");const f=1&s?\"03\":\"02\",h=u.fromHex(f+l(d)),p=a(d),g=o(-c*p),y=o(i*p),m=u.BASE.multiplyAndAddUnsafe(h,g,y);if(!m)throw new Error(\"point at infinify\");return m.assertValidity(),m}hasHighS(){return h(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return Hn(this.toDERHex())}toDERHex(){return cs.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Hn(this.toCompactHex())}toCompactHex(){return l(this.r)+l(this.s)}}const y={isValidPrivateKey(e){try{return c(e),!0}catch(e){return!1}},normPrivateKeyToScalar:c,randomPrivateKey:()=>{const e=Bi(t.n);return function(e,t,r=!1){const n=e.length,i=Ii(t),s=Bi(t);if(n<16||n<s||n>1024)throw new Error(\"expected \"+s+\"-1024 bytes of input, got \"+n);const o=wi(r?Gn(e):Wn(e),t-li)+li;return r?Yn(o,i):Jn(o,i)}(t.randomBytes(e),t.n)},precompute:(e=8,t=u.BASE)=>(t._setWindowSize(e),t.multiply(BigInt(3)),t)};function m(e){const t=Ln(e),r=\"string\"==typeof e,n=(t||r)&&e.length;return t?n===i||n===s:r?n===2*i||n===2*s:e instanceof u}const w=t.bits2int||function(e){if(e.length>8192)throw new Error(\"input is too large\");const r=Wn(e),n=8*e.length-t.nBitLength;return n>0?r>>BigInt(n):r},v=t.bits2int_modN||function(e){return o(w(e))},b=ii(t.nBitLength);function _(e){return ri(\"num < 2^\"+t.nBitLength,e,ds,b),Jn(e,t.nByteLength)}function E(e,n,i=x){if([\"recovered\",\"canonical\"].some((e=>e in i)))throw new Error(\"sign() legacy options not supported\");const{hash:s,randomBytes:d}=t;let{lowS:l,prehash:p,extraEntropy:y}=i;null==l&&(l=!0),e=Xn(\"msgHash\",e),ss(i),p&&(e=Xn(\"prehashed msgHash\",s(e)));const m=v(e),b=c(n),E=[_(b),_(m)];if(null!=y&&!1!==y){const e=!0===y?d(r.BYTES):y;E.push(Xn(\"extraEntropy\",e))}const A=Qn(...E),k=m;return{seed:A,k2sig:function(e){const t=w(e);if(!f(t))return;const r=a(t),n=u.BASE.multiply(t).toAffine(),i=o(n.x);if(i===ds)return;const s=o(r*o(k+i*b));if(s===ds)return;let c=(n.x===i?0:2)|Number(n.y&fs),d=s;return l&&h(s)&&(d=function(e){return h(e)?o(-e):e}(s),c^=1),new g(i,d,c)}}}const x={lowS:t.lowS,prehash:!1},A={lowS:t.lowS,prehash:!1};return u.BASE._setWindowSize(8),{CURVE:t,getPublicKey:function(e,t=!0){return u.fromPrivateKey(e).toRawBytes(t)},getSharedSecret:function(e,t,r=!0){if(m(e))throw new Error(\"first arg must be private key\");if(!m(t))throw new Error(\"second arg must be public key\");return u.fromHex(t).multiply(c(e)).toRawBytes(r)},sign:function(e,r,n=x){const{seed:i,k2sig:s}=E(e,r,n),o=t;return ai(o.hash.outputLen,o.nByteLength,o.hmac)(i,s)},verify:function(e,r,n,i=A){const s=e;r=Xn(\"msgHash\",r),n=Xn(\"publicKey\",n);const{lowS:c,prehash:d,format:f}=i;if(ss(i),\"strict\"in i)throw new Error(\"options.strict was renamed to lowS\");if(void 0!==f&&\"compact\"!==f&&\"der\"!==f)throw new Error(\"format must be compact or der\");const l=\"string\"==typeof s||Ln(s),h=!l&&!f&&\"object\"==typeof s&&null!==s&&\"bigint\"==typeof s.r&&\"bigint\"==typeof s.s;if(!l&&!h)throw new Error(\"invalid signature, expected Uint8Array, hex string or Signature instance\");let p,y;try{if(h&&(p=new g(s.r,s.s)),l){try{\"compact\"!==f&&(p=g.fromDER(s))}catch(e){if(!(e instanceof cs.Err))throw e}p||\"der\"===f||(p=g.fromCompact(s))}y=u.fromHex(n)}catch(e){return!1}if(!p)return!1;if(c&&p.hasHighS())return!1;d&&(r=t.hash(r));const{r:m,s:w}=p,b=v(r),_=a(w),E=o(b*_),x=o(m*_),k=u.BASE.multiplyAndAddUnsafe(y,E,x)?.toAffine();return!!k&&o(k.x)===m},ProjectivePoint:u,Signature:g,utils:y}}function gs(e){return{hash:e,hmac:(t,...r)=>Hi(e,t,function(...e){let t=0;for(let r=0;r<e.length;r++){const n=e[r];Sn(n),t+=n.length}const r=new Uint8Array(t);for(let t=0,n=0;t<e.length;t++){const i=e[t];r.set(i,n),n+=i.length}return r}(...r)),randomBytes:Zn}}const ys=BigInt(\"0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f\"),ms=BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\"),ws=BigInt(1),vs=BigInt(2),bs=(e,t)=>(e+t/vs)/t;function _s(e){const t=ys,r=BigInt(3),n=BigInt(6),i=BigInt(11),s=BigInt(22),o=BigInt(23),a=BigInt(44),u=BigInt(88),c=e*e*e%t,d=c*c*e%t,f=bi(d,r,t)*d%t,l=bi(f,r,t)*d%t,h=bi(l,vs,t)*c%t,p=bi(h,i,t)*h%t,g=bi(p,s,t)*p%t,y=bi(g,a,t)*g%t,m=bi(y,u,t)*y%t,w=bi(m,a,t)*g%t,v=bi(w,r,t)*d%t,b=bi(v,o,t)*p%t,_=bi(b,n,t)*c%t,E=bi(_,vs,t);if(!Es.eql(Es.sqr(E),e))throw new Error(\"Cannot find square root\");return E}const Es=ki(ys,void 0,void 0,{sqrt:_s}),xs=function(e,t){const r=t=>ps({...e,...gs(t)});return{...r(t),create:r}}({a:BigInt(0),b:BigInt(7),Fp:Es,n:ms,Gx:BigInt(\"55066263022277343669578718895168534326250603453777594175500187360389116729240\"),Gy:BigInt(\"32670510020758816978083085130507043184471273380659243275938904335757337482424\"),h:BigInt(1),lowS:!0,endo:{beta:BigInt(\"0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee\"),splitScalar:e=>{const t=ms,r=BigInt(\"0x3086d221a7d46bcde86c90e49284eb15\"),n=-ws*BigInt(\"0xe4437ed6010e88286f547fa90abfe4c3\"),i=BigInt(\"0x114ca50f7a8e2f3f657c1108d9d44cfd8\"),s=r,o=BigInt(\"0x100000000000000000000000000000000\"),a=bs(s*e,t),u=bs(-n*e,t);let c=wi(e-a*r-u*i,t),d=wi(-a*n-u*s,t);const f=c>o,l=d>o;if(f&&(c=t-c),l&&(d=t-d),c>o||d>o)throw new Error(\"splitScalar: Endomorphism failed, k=\"+e);return{k1neg:f,k1:c,k2neg:l,k2:d}}}},Vi);BigInt(0);xs.ProjectivePoint;function As(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function ks(e){if(\"boolean\"!=typeof e)throw new Error(`boolean expected, not ${e}`)}function Is(e){return e instanceof Uint8Array||null!=e&&\"object\"==typeof e&&\"Uint8Array\"===e.constructor.name}function Bs(e,...t){if(!Is(e))throw new Error(\"Uint8Array expected\");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function Ss(e,t=!0){if(e.destroyed)throw new Error(\"Hash instance has been destroyed\");if(t&&e.finished)throw new Error(\"Hash#digest() has already been called\")}function Os(e,t){Bs(e);const r=t.outputLen;if(e.length<r)throw new Error(`digestInto() expects output buffer of length at least ${r}`)}const Ts=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4));if(!(68===new Uint8Array(new Uint32Array([287454020]).buffer)[0]))throw new Error(\"Non little-endian hardware is not supported\");const Ns=Array.from({length:256},((e,t)=>t.toString(16).padStart(2,\"0\")));function Rs(e){Bs(e);let t=\"\";for(let r=0;r<e.length;r++)t+=Ns[e[r]];return t}const Cs=48,Zs=57,Us=65,js=70,Ps=97,Ls=102;function $s(e){return e>=Cs&&e<=Zs?e-Cs:e>=Us&&e<=js?e-(Us-10):e>=Ps&&e<=Ls?e-(Ps-10):void 0}function Ms(e){if(\"string\"!=typeof e)throw new Error(\"hex string expected, got \"+typeof e);const t=e.length,r=t/2;if(t%2)throw new Error(\"padded hex string expected, got unpadded hex of length \"+t);const n=new Uint8Array(r);for(let t=0,i=0;t<r;t++,i+=2){const r=$s(e.charCodeAt(i)),s=$s(e.charCodeAt(i+1));if(void 0===r||void 0===s){const t=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character \"'+t+'\" at index '+i)}n[t]=16*r+s}return n}function Fs(e){if(\"string\"!=typeof e)throw new Error(\"string expected, got \"+typeof e);return new Uint8Array((new TextEncoder).encode(e))}function zs(e){if(\"string\"==typeof e)e=Fs(e);else{if(!Is(e))throw new Error(\"Uint8Array expected, got \"+typeof e);e=e.slice()}return e}\"object\"==typeof globalThis&&\"crypto\"in globalThis&&globalThis.crypto;const Ds=e=>Uint8Array.from(e.split(\"\").map((e=>e.charCodeAt(0)))),qs=Ds(\"expand 16-byte k\"),Vs=Ds(\"expand 32-byte k\"),Ks=Ts(qs),Hs=Ts(Vs);Hs.slice();function Ws(e,t){return e<<t|e>>>32-t}function Gs(e){return e.byteOffset%4==0}const Js=2**32-1,Ys=new Uint32Array;function Xs(e,t){const{allowShortKeys:r,extendNonceFn:n,counterLength:i,counterRight:s,rounds:o}=function(e,t){if(null==t||\"object\"!=typeof t)throw new Error(\"options must be defined\");return Object.assign(e,t)}({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},t);if(\"function\"!=typeof e)throw new Error(\"core must be a function\");return As(i),As(o),ks(s),ks(r),(t,a,u,c,d=0)=>{Bs(t),Bs(a),Bs(u);const f=u.length;if(c||(c=new Uint8Array(f)),Bs(c),As(d),d<0||d>=Js)throw new Error(\"arx: counter overflow\");if(c.length<f)throw new Error(`arx: output (${c.length}) is shorter than data (${f})`);const l=[];let h,p,g=t.length;if(32===g)h=t.slice(),l.push(h),p=Hs;else{if(16!==g||!r)throw new Error(`arx: invalid 32-byte key, got length=${g}`);h=new Uint8Array(32),h.set(t),h.set(t,16),p=Ks,l.push(h)}Gs(a)||(a=a.slice(),l.push(a));const y=Ts(h);if(n){if(24!==a.length)throw new Error(\"arx: extended nonce must be 24 bytes\");n(p,y,Ts(a.subarray(0,16)),y),a=a.subarray(16)}const m=16-i;if(m!==a.length)throw new Error(`arx: nonce must be ${m} or 16 bytes`);if(12!==m){const e=new Uint8Array(12);e.set(a,s?0:12-a.length),a=e,l.push(a)}const w=Ts(a);for(!function(e,t,r,n,i,s,o,a){const u=i.length,c=new Uint8Array(64),d=Ts(c),f=Gs(i)&&Gs(s),l=f?Ts(i):Ys,h=f?Ts(s):Ys;for(let p=0;p<u;o++){if(e(t,r,n,d,o,a),o>=Js)throw new Error(\"arx: counter overflow\");const g=Math.min(64,u-p);if(f&&64===g){const e=p/4;if(p%4!=0)throw new Error(\"arx: invalid block position\");for(let t,r=0;r<16;r++)t=e+r,h[t]=l[t]^d[r];p+=64}else{for(let e,t=0;t<g;t++)e=p+t,s[e]=i[e]^c[t];p+=g}}}(e,p,y,w,u,c,d,o);l.length>0;)l.pop().fill(0);return c}}const Qs=(e,t)=>255&e[t++]|(255&e[t++])<<8;class eo{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,Bs(e=zs(e),32);const t=Qs(e,0),r=Qs(e,2),n=Qs(e,4),i=Qs(e,6),s=Qs(e,8),o=Qs(e,10),a=Qs(e,12),u=Qs(e,14);this.r[0]=8191&t,this.r[1]=8191&(t>>>13|r<<3),this.r[2]=7939&(r>>>10|n<<6),this.r[3]=8191&(n>>>7|i<<9),this.r[4]=255&(i>>>4|s<<12),this.r[5]=s>>>1&8190,this.r[6]=8191&(s>>>14|o<<2),this.r[7]=8065&(o>>>11|a<<5),this.r[8]=8191&(a>>>8|u<<8),this.r[9]=u>>>5&127;for(let t=0;t<8;t++)this.pad[t]=Qs(e,16+2*t)}process(e,t,r=!1){const n=r?0:2048,{h:i,r:s}=this,o=s[0],a=s[1],u=s[2],c=s[3],d=s[4],f=s[5],l=s[6],h=s[7],p=s[8],g=s[9],y=Qs(e,t+0),m=Qs(e,t+2),w=Qs(e,t+4),v=Qs(e,t+6),b=Qs(e,t+8),_=Qs(e,t+10),E=Qs(e,t+12),x=Qs(e,t+14);let A=i[0]+(8191&y),k=i[1]+(8191&(y>>>13|m<<3)),I=i[2]+(8191&(m>>>10|w<<6)),B=i[3]+(8191&(w>>>7|v<<9)),S=i[4]+(8191&(v>>>4|b<<12)),O=i[5]+(b>>>1&8191),T=i[6]+(8191&(b>>>14|_<<2)),N=i[7]+(8191&(_>>>11|E<<5)),R=i[8]+(8191&(E>>>8|x<<8)),C=i[9]+(x>>>5|n),Z=0,U=Z+A*o+k*(5*g)+I*(5*p)+B*(5*h)+S*(5*l);Z=U>>>13,U&=8191,U+=O*(5*f)+T*(5*d)+N*(5*c)+R*(5*u)+C*(5*a),Z+=U>>>13,U&=8191;let j=Z+A*a+k*o+I*(5*g)+B*(5*p)+S*(5*h);Z=j>>>13,j&=8191,j+=O*(5*l)+T*(5*f)+N*(5*d)+R*(5*c)+C*(5*u),Z+=j>>>13,j&=8191;let P=Z+A*u+k*a+I*o+B*(5*g)+S*(5*p);Z=P>>>13,P&=8191,P+=O*(5*h)+T*(5*l)+N*(5*f)+R*(5*d)+C*(5*c),Z+=P>>>13,P&=8191;let L=Z+A*c+k*u+I*a+B*o+S*(5*g);Z=L>>>13,L&=8191,L+=O*(5*p)+T*(5*h)+N*(5*l)+R*(5*f)+C*(5*d),Z+=L>>>13,L&=8191;let $=Z+A*d+k*c+I*u+B*a+S*o;Z=$>>>13,$&=8191,$+=O*(5*g)+T*(5*p)+N*(5*h)+R*(5*l)+C*(5*f),Z+=$>>>13,$&=8191;let M=Z+A*f+k*d+I*c+B*u+S*a;Z=M>>>13,M&=8191,M+=O*o+T*(5*g)+N*(5*p)+R*(5*h)+C*(5*l),Z+=M>>>13,M&=8191;let F=Z+A*l+k*f+I*d+B*c+S*u;Z=F>>>13,F&=8191,F+=O*a+T*o+N*(5*g)+R*(5*p)+C*(5*h),Z+=F>>>13,F&=8191;let z=Z+A*h+k*l+I*f+B*d+S*c;Z=z>>>13,z&=8191,z+=O*u+T*a+N*o+R*(5*g)+C*(5*p),Z+=z>>>13,z&=8191;let D=Z+A*p+k*h+I*l+B*f+S*d;Z=D>>>13,D&=8191,D+=O*c+T*u+N*a+R*o+C*(5*g),Z+=D>>>13,D&=8191;let q=Z+A*g+k*p+I*h+B*l+S*f;Z=q>>>13,q&=8191,q+=O*d+T*c+N*u+R*a+C*o,Z+=q>>>13,q&=8191,Z=(Z<<2)+Z|0,Z=Z+U|0,U=8191&Z,Z>>>=13,j+=Z,i[0]=U,i[1]=j,i[2]=P,i[3]=L,i[4]=$,i[5]=M,i[6]=F,i[7]=z,i[8]=D,i[9]=q}finalize(){const{h:e,pad:t}=this,r=new Uint16Array(10);let n=e[1]>>>13;e[1]&=8191;for(let t=2;t<10;t++)e[t]+=n,n=e[t]>>>13,e[t]&=8191;e[0]+=5*n,n=e[0]>>>13,e[0]&=8191,e[1]+=n,n=e[1]>>>13,e[1]&=8191,e[2]+=n,r[0]=e[0]+5,n=r[0]>>>13,r[0]&=8191;for(let t=1;t<10;t++)r[t]=e[t]+n,n=r[t]>>>13,r[t]&=8191;r[9]-=8192;let i=(1^n)-1;for(let e=0;e<10;e++)r[e]&=i;i=~i;for(let t=0;t<10;t++)e[t]=e[t]&i|r[t];e[0]=65535&(e[0]|e[1]<<13),e[1]=65535&(e[1]>>>3|e[2]<<10),e[2]=65535&(e[2]>>>6|e[3]<<7),e[3]=65535&(e[3]>>>9|e[4]<<4),e[4]=65535&(e[4]>>>12|e[5]<<1|e[6]<<14),e[5]=65535&(e[6]>>>2|e[7]<<11),e[6]=65535&(e[7]>>>5|e[8]<<8),e[7]=65535&(e[8]>>>8|e[9]<<5);let s=e[0]+t[0];e[0]=65535&s;for(let r=1;r<8;r++)s=(e[r]+t[r]|0)+(s>>>16)|0,e[r]=65535&s}update(e){Ss(this);const{buffer:t,blockLen:r}=this,n=(e=zs(e)).length;for(let i=0;i<n;){const s=Math.min(r-this.pos,n-i);if(s!==r)t.set(e.subarray(i,i+s),this.pos),this.pos+=s,i+=s,this.pos===r&&(this.process(t,0,!1),this.pos=0);else for(;r<=n-i;i+=r)this.process(e,i)}return this}destroy(){this.h.fill(0),this.r.fill(0),this.buffer.fill(0),this.pad.fill(0)}digestInto(e){Ss(this),Os(e,this),this.finished=!0;const{buffer:t,h:r}=this;let{pos:n}=this;if(n){for(t[n++]=1;n<16;n++)t[n]=0;this.process(t,0,!0)}this.finalize();let i=0;for(let t=0;t<8;t++)e[i++]=r[t]>>>0,e[i++]=r[t]>>>8;return e}digest(){const{buffer:e,outputLen:t}=this;this.digestInto(e);const r=e.slice(0,t);return this.destroy(),r}}const to=function(e){const t=(t,r)=>e(r).update(zs(t)).digest(),r=e(new Uint8Array(32));return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=t=>e(t),t}((e=>new eo(e)));function ro(e,t,r,n,i,s=20){let o=e[0],a=t[0],u=t[1],c=t[2],d=t[3],f=e[1],l=r[0],h=r[1],p=i,g=e[2],y=t[4],m=t[5],w=t[6],v=t[7],b=e[3],_=o,E=a,x=u,A=c,k=d,I=f,B=l,S=h,O=p,T=0,N=g,R=y,C=m,Z=w,U=v,j=b;for(let e=0;e<s;e+=2)k^=Ws(_+C|0,7),O^=Ws(k+_|0,9),C^=Ws(O+k|0,13),_^=Ws(C+O|0,18),T^=Ws(I+E|0,7),Z^=Ws(T+I|0,9),E^=Ws(Z+T|0,13),I^=Ws(E+Z|0,18),U^=Ws(N+B|0,7),x^=Ws(U+N|0,9),B^=Ws(x+U|0,13),N^=Ws(B+x|0,18),A^=Ws(j+R|0,7),S^=Ws(A+j|0,9),R^=Ws(S+A|0,13),j^=Ws(R+S|0,18),E^=Ws(_+A|0,7),x^=Ws(E+_|0,9),A^=Ws(x+E|0,13),_^=Ws(A+x|0,18),B^=Ws(I+k|0,7),S^=Ws(B+I|0,9),k^=Ws(S+B|0,13),I^=Ws(k+S|0,18),R^=Ws(N+T|0,7),O^=Ws(R+N|0,9),T^=Ws(O+R|0,13),N^=Ws(T+O|0,18),C^=Ws(j+U|0,7),Z^=Ws(C+j|0,9),U^=Ws(Z+C|0,13),j^=Ws(U+Z|0,18);let P=0;n[P++]=o+_|0,n[P++]=a+E|0,n[P++]=u+x|0,n[P++]=c+A|0,n[P++]=d+k|0,n[P++]=f+I|0,n[P++]=l+B|0,n[P++]=h+S|0,n[P++]=p+O|0,n[P++]=0+T|0,n[P++]=g+N|0,n[P++]=y+R|0,n[P++]=m+C|0,n[P++]=w+Z|0,n[P++]=v+U|0,n[P++]=b+j|0}function no(e,t,r,n){let i=e[0],s=t[0],o=t[1],a=t[2],u=t[3],c=e[1],d=r[0],f=r[1],l=r[2],h=r[3],p=e[2],g=t[4],y=t[5],m=t[6],w=t[7],v=e[3];for(let e=0;e<20;e+=2)u^=Ws(i+y|0,7),l^=Ws(u+i|0,9),y^=Ws(l+u|0,13),i^=Ws(y+l|0,18),h^=Ws(c+s|0,7),m^=Ws(h+c|0,9),s^=Ws(m+h|0,13),c^=Ws(s+m|0,18),w^=Ws(p+d|0,7),o^=Ws(w+p|0,9),d^=Ws(o+w|0,13),p^=Ws(d+o|0,18),a^=Ws(v+g|0,7),f^=Ws(a+v|0,9),g^=Ws(f+a|0,13),v^=Ws(g+f|0,18),s^=Ws(i+a|0,7),o^=Ws(s+i|0,9),a^=Ws(o+s|0,13),i^=Ws(a+o|0,18),d^=Ws(c+u|0,7),f^=Ws(d+c|0,9),u^=Ws(f+d|0,13),c^=Ws(u+f|0,18),g^=Ws(p+h|0,7),l^=Ws(g+p|0,9),h^=Ws(l+g|0,13),p^=Ws(h+l|0,18),y^=Ws(v+w|0,7),m^=Ws(y+v|0,9),w^=Ws(m+y|0,13),v^=Ws(w+m|0,18);let b=0;n[b++]=i,n[b++]=c,n[b++]=p,n[b++]=v,n[b++]=d,n[b++]=f,n[b++]=l,n[b++]=h}Xs(ro,{allowShortKeys:!0,counterRight:!0});const io=Xs(ro,{counterRight:!0,extendNonceFn:no}),so=(oo={blockSize:64,nonceLength:24,tagLength:16},ao=(e,t)=>{const r=16;return Bs(e,32),Bs(t,24),{encrypt:(n,i)=>{Bs(n);const s=n.length+32;i?Bs(i,s):i=new Uint8Array(s),i.set(n,32),io(e,t,i,i);const o=i.subarray(0,32),a=to(i.subarray(32),o);return i.set(a,r),i.subarray(0,r).fill(0),i.subarray(r)},decrypt:n=>{Bs(n);const i=n.length;if(i<r)throw new Error(\"encrypted data should be at least 16 bytes\");const s=new Uint8Array(i+r);s.set(n,r);const o=io(e,t,new Uint8Array(32)),a=to(s.subarray(32),o);if(!function(e,t){if(e.length!==t.length)return!1;let r=0;for(let n=0;n<e.length;n++)r|=e[n]^t[n];return 0===r}(s.subarray(16,32),a))throw new Error(\"invalid tag\");const u=io(e,t,s);return u.subarray(0,32).fill(0),o.fill(0),u.subarray(32)}}},Object.assign(ao,oo),ao);var oo,ao;function uo(e,t){const r=so(e,t);return{seal:r.encrypt,open:r.decrypt}}const co=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]),fo=new Uint8Array(16),lo=(e,t)=>{const r=Li.getSharedSecret(t,e),n=new Uint32Array(8);return no(Ts(co),Ts(r),Ts(fo),n),i=n,new Uint8Array(i.buffer,i.byteOffset,i.byteLength);var i},ho=(e,t)=>{if(!(e instanceof Uint8Array))throw new TypeError(\"publicKey must be a Uint8Array\");if(!(t instanceof Uint8Array))throw new TypeError(\"secretKey must be a Uint8Array\")},po=(e,t)=>{if(32!==e.length)throw new TypeError(\"publicKey must be 32 bytes long\");if(32!==t.length)throw new TypeError(\"secretKey must be 32 bytes long\")},go={seal:(e,t,r,n)=>{ho(r,n),po(r,n);return uo(lo(r,n),t).seal(e)},open:(e,t,r,n)=>{ho(r,n),po(r,n);return uo(lo(r,n),t).open(e)},nonceLength:24,keyPair:()=>{const e=Li.utils.randomPrivateKey();return{secretKey:e,publicKey:Li.getPublicKey(e)}}},yo=(e,t)=>{if(\"x25519-xsalsa20-poly1305\"===e.version){let r;if(t instanceof Uint8Array)r=t;else try{r=An(t)}catch(e){throw new Error(\"Bad private key\")}const n=kn(e.nonce),i=kn(e.ciphertext),s=kn(e.ephemPublicKey);return function(e){return(new TextDecoder).decode(e)}(go.open(i,n,s,r))}throw new Error(`Encryption type/version not supported (${e.version}).`)};async function mo(){return await snap.request({method:\"snap_listEntropySources\"})}async function wo(e,t){return await snap.request({method:\"snap_getEntropy\",params:{version:1,source:e,...t?{salt:t}:{}}})}async function vo(e,t){const r=await wo(e,t);return Ms(vn(r))}async function bo(e,t){const r=await vo(e,t);return function(e){return`0x${Rs(e)}`}(xs.getPublicKey(r))}const _o=\"metamask:snaps:encryption\";async function Eo(e,t){const r=t?`${_o}${t}`:_o;return await wo(e,r)}async function xo(e,t){const r=await Eo(e,t);return xn(Li.getPublicKey(r.slice(2)))}const Ao=\"invalid tag\";const ko=Or.object({entropySourceId:Or.string().optional()}),Io=Or.object({entropySourceId:Or.string().optional()}),Bo=Or.object({message:Or.string().startsWith(\"metamask:\"),entropySourceId:Or.string().optional()}),So=Or.object({data:Or.object({version:Or.literal(\"x25519-xsalsa20-poly1305\"),nonce:Or.string().length(32).base64(),ephemPublicKey:Or.string().length(44).base64(),ciphertext:Or.string().base64()}),entropySourceId:Or.string().optional()});const Oo=[\"https://portfolio.metamask.io\",\"https://portfolio-builds.metafi-dev.codefi.network\",\"https://docs.metamask.io\",\"https://developer.metamask.io\",\"metamask\"];function To(e){if(e&&!Oo.includes(e))return e}const No=async({request:e,origin:t})=>{const r=To(t);switch(e.method){case\"getPublicKey\":{const{params:t}=e;if(!t)return bo(void 0,r);!function(e){try{ko.parse(e)}catch{throw re({message:\"`getPublicKey`, must take an optional `entropySourceId` parameter\"})}}(t);const{entropySourceId:n}=t;return bo(n,r)}case\"getAllPublicKeys\":return async function(e){const t=await mo(),r=[];return await Promise.all(t.map((async t=>{const n=await bo(t.id,e);r.push([t.id,n])}))),r}(r);case\"signMessage\":{const{params:t}=e;!function(e){try{Bo.parse(e)}catch{throw re({message:\"`signMessage`, must take a `message` parameter that must begin with `metamask:`\"})}}(t);const{message:n,entropySourceId:i}=t;return await async function(e,t,r){const n=await vo(t,r),i=Vi(e);return`0x${xs.sign(i,n).toCompactHex()}`}(n,i,r)}case\"getEncryptionPublicKey\":{const{params:t}=e;if(!t)return xo(void 0,r);!function(e){try{Io.parse(e)}catch(e){throw re({message:\"`getEncryptionPublicKey`, expects an optional `entropySourceId` parameter\"})}}(t);const{entropySourceId:n}=t;return xo(n,r)}case\"decryptMessage\":{const{params:t}=e;!function(e){try{So.parse(e)}catch(e){throw re({message:\"`decryptMessage`, expects a `data` parameter that must match the Eip1024EncryptedData schema, and an optional entropySourceId string parameter\"})}}(t);const{data:n,entropySourceId:i}=t;return await async function(e,t,r){if(t){const n=await Eo(t,r);return yo(e,n)}const n=await mo();let i=null;for(const t of n){const n=await Eo(t.id,r);try{return yo(e,n)}catch(e){e.message!==Ao&&(i=i??e)}}throw i??new Error(Ao)}(n,i,r)}default:throw ne({data:{method:e.method}})}}})(),module.exports=n})();"}], "removable": false}