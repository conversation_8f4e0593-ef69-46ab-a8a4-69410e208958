<svg id="Logo" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 85.5 94.59">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient);
      }

      .cls-2 {
        fill: url(#linear-gradient);
      }

      .cls-3 {
        fill: url(#linear-gradient-2);
      }

      .cls-4 {
        fill: url(#radial-gradient-2);
      }

      .cls-5 {
        fill: none;
        stroke-width: 2px;
        stroke: url(#New_Gradient_Swatch_1);
      }

      .cls-6 {
        stroke-width: 0.25px;
        fill: url(#radial-gradient-3);
        stroke: url(#Radial_latón);
      }
    </style>
    <radialGradient id="radial-gradient" cx="50.5" cy="50.74" r="44.16" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0076c0"/>
      <stop offset="0.16" stop-color="#0567b2"/>
      <stop offset="0.46" stop-color="#12418f"/>
      <stop offset="0.75" stop-color="#211566"/>
    </radialGradient>
    <linearGradient id="linear-gradient" x1="194.47" y1="-46.19" x2="235.62" y2="-46.19" gradientTransform="translate(244.37 4.55) rotate(180)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0076c0"/>
      <stop offset="0.18" stop-color="#0567b2"/>
      <stop offset="0.51" stop-color="#12418f"/>
      <stop offset="0.84" stop-color="#211566"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="206.57" y1="-46.19" x2="247.72" y2="-46.19" gradientTransform="matrix(1, 0, 0, -1, -155.6, 4.55)" xlink:href="#linear-gradient"/>
    <radialGradient id="radial-gradient-2" cx="175.88" cy="61.12" r="37.56" gradientTransform="matrix(-1, 0, 0, 1.02, 226.37, -0.01)" gradientUnits="userSpaceOnUse">
      <stop offset="0.15" stop-color="#f4e8d9"/>
      <stop offset="0.27" stop-color="#edd089"/>
      <stop offset="0.38" stop-color="#d3a351"/>
      <stop offset="0.46" stop-color="#c08329"/>
    </radialGradient>
    <radialGradient id="New_Gradient_Swatch_1" data-name="New Gradient Swatch 1" cx="47.99" cy="50.74" r="45.87" gradientTransform="translate(-4.12 -2.79) scale(0.98 0.99)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#f4e8d9"/>
      <stop offset="0.47" stop-color="#edd18d"/>
      <stop offset="0.89" stop-color="#edd089"/>
      <stop offset="0.89" stop-color="#ecce87"/>
      <stop offset="0.92" stop-color="#d7a959"/>
      <stop offset="0.96" stop-color="#c78e38"/>
      <stop offset="0.98" stop-color="#bd7d23"/>
      <stop offset="1" stop-color="#ba771c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-3" cx="116.84" cy="34.58" r="7.88" gradientTransform="translate(-32.56 12.36) scale(0.64 0.63)" gradientUnits="userSpaceOnUse">
      <stop offset="0.18" stop-color="#f4e8d9"/>
      <stop offset="0.75" stop-color="#edd089"/>
      <stop offset="0.8" stop-color="#ebcd85"/>
      <stop offset="0.85" stop-color="#e5c379"/>
      <stop offset="0.9" stop-color="#dcb264"/>
      <stop offset="0.96" stop-color="#ce9b48"/>
      <stop offset="1" stop-color="#c08329"/>
    </radialGradient>
    <radialGradient id="Radial_latón" data-name="Radial latón" cx="116.84" cy="34.58" r="8.01" gradientTransform="translate(-32.56 12.36) scale(0.64 0.63)" gradientUnits="userSpaceOnUse">
      <stop offset="0.18" stop-color="#f4e8d9"/>
      <stop offset="0.59" stop-color="#edd089"/>
      <stop offset="1" stop-color="#c08329"/>
    </radialGradient>
  </defs>
  <title>0xdfbc9050F5B01DF53512DCC39B4f2B2BBaCD517A</title>
  <path class="cls-1" d="M8.41,27.82V73.67a.28.28,0,0,0,.15.24l41.8,22.94a.34.34,0,0,0,.28,0l41.8-22.94a.28.28,0,0,0,.15-.24V27.82a.28.28,0,0,0-.15-.24L50.64,4.64a.34.34,0,0,0-.28,0L8.56,27.58A.28.28,0,0,0,8.41,27.82Z" transform="translate(-7.75 -3.45)"/>
  <path class="cls-2" d="M8.75,74,49.9,50.6Q29.33,39,8.75,27.46Z" transform="translate(-7.75 -3.45)"/>
  <path class="cls-3" d="M92.11,74,51,50.6,92.11,27.46Z" transform="translate(-7.75 -3.45)"/>
  <path id="Ambas" class="cls-4" d="M50.5,96.88c-.65-.65,2.77-31.38-17.38-51.44A114.76,114.76,0,0,0,9.44,28c-.06.09,7.14,3.78,38.52,21.55a5.17,5.17,0,0,0,5.08,0C84.42,31.81,91.61,28.12,91.56,28A114.76,114.76,0,0,0,67.88,45.44C47.73,65.5,51.15,96.23,50.5,96.88Z" transform="translate(-7.75 -3.45)"/>
  <polygon class="cls-5" points="1 24.22 1 70.37 42.75 93.45 84.5 70.37 84.5 24.22 42.75 1.14 1 24.22"/>
  <ellipse class="cls-6" cx="42.75" cy="34.04" rx="5.08" ry="4.94"/>
</svg>
