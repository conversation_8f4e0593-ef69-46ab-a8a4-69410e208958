{"QRHardwareSignRequestCancel": {"message": "Afwijzen"}, "accountDetails": {"message": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "accountName": {"message": "Account<PERSON><PERSON>"}, "addToken": {"message": "Voeg token toe"}, "amount": {"message": "Bedrag"}, "appDescription": {"message": "Ethereum Browser-extensie", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "approve": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "attributions": {"message": "Be<PERSON>egdh<PERSON>n"}, "back": {"message": "Terug"}, "balance": {"message": "Balans:"}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "confirm": {"message": "Bevestigen"}, "confirmPassword": {"message": "bevestig wachtwoord"}, "contractDeployment": {"message": "Contractimplementatie"}, "copiedExclamation": {"message": "Gekopieerd!"}, "copyToClipboard": {"message": "<PERSON><PERSON><PERSON> naar klembord"}, "create": {"message": "cre<PERSON><PERSON>"}, "decimal": {"message": "<PERSON><PERSON><PERSON> van precisie"}, "decimalsMustZerotoTen": {"message": "Decimalen moeten minimaal 0 en niet meer dan 36 zijn."}, "done": {"message": "<PERSON><PERSON><PERSON>"}, "downloadStateLogs": {"message": "Staatslogboeken downloaden"}, "edit": {"message": "Bewerk"}, "etherscanView": {"message": "Bekijk account op Etherscan"}, "failed": {"message": "mislukt"}, "fileImportFail": {"message": "Bestandsimport werkt niet? <PERSON><PERSON> hier!", "description": "Helps user import their account from a JSON file"}, "from": {"message": "<PERSON>"}, "gasLimit": {"message": "Gaslimiet"}, "gasLimitTooLow": {"message": "De gaslimiet moet minstens 21000 zijn"}, "here": {"message": "hier", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hide": {"message": "Verbergen"}, "hideTokenPrompt": {"message": "Token verbergen?"}, "import": {"message": "Importeren", "description": "Button to import an account from a selected file"}, "importAccountMsg": {"message": " Geïmporteerde accounts worden niet gekoppeld aan de seedphrase van uw oorspronkelijk gemaakte MetaMask-account. Meer informatie over geïmporteerde accounts"}, "imported": {"message": "geïmporteerd", "description": "status showing that an account has been fully loaded into the keyring"}, "insufficientFunds": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> fondsen."}, "insufficientTokens": {"message": "Onvoldoende tokens."}, "invalidAddress": {"message": "Ongeldig adres"}, "invalidAddressRecipient": {"message": "<PERSON><PERSON> <PERSON><PERSON> van de ontvanger is ongeldig"}, "invalidRPC": {"message": "Ongeldige RPC-URI"}, "jsonFile": {"message": "JSON-bestand", "description": "format for importing an account"}, "likeToImportTokens": {"message": "Wil je deze tokens toevoegen?"}, "loading": {"message": "Bezig met laden..."}, "lock": {"message": "Uitloggen"}, "mainnet": {"message": "Mainnetwerk"}, "message": {"message": "Bericht"}, "mustSelectOne": {"message": "Moet ten minste één token selecteren."}, "needImportFile": {"message": "U moet een bestand selecteren om te importeren.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Kan geen negatieve hoeveelheden ETH verzenden."}, "networks": {"message": "netwerken"}, "newAccount": {"message": "Nieuw account"}, "newContract": {"message": "Nieuw contract"}, "newPassword": {"message": "<PERSON><PERSON><PERSON> wacht<PERSON> (min 8 tekens)"}, "next": {"message": "volgende"}, "pastePrivateKey": {"message": "<PERSON><PERSON> hier uw privésleutelstring:", "description": "For importing an account from a private key"}, "personalAddressDetected": {"message": "Persoonlijk adres gedetecteerd. Voer het tokencontractadres in."}, "privacyMsg": {"message": "Privacybeleid"}, "privateKey": {"message": "<PERSON><PERSON> sleutel", "description": "select this type of file to use to import an account"}, "privateKeyWarning": {"message": "Waarschuwing: open deze sleutel nooit. <PERSON><PERSON><PERSON> met uw privésleutels kan stelen van alle items in uw account."}, "privateNetwork": {"message": "Prive netwerk"}, "readdToken": {"message": "U kunt dit token in de toekomst weer toevoegen door naar \"Token toevoegen\" te gaan in het menu met accountopties."}, "reject": {"message": "Afwijzen"}, "rejected": {"message": "Verworpen"}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "revealSeedWords": {"message": "Onthul back-up woorden"}, "revealSeedWordsWarning": {"message": "<PERSON>org dat je back-up woorden niet op een openbare plaats bekijkt! Deze woorden kunnen worden gebruikt om al uw accounts opnieuw te genereren (en dus uw account te stelen).", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "save": {"message": "Opsla<PERSON>"}, "search": {"message": "<PERSON><PERSON>"}, "seedPhraseReq": {"message": "Back-up woorden zijn 12 woorden lang"}, "selectType": {"message": "Selecteer type"}, "send": {"message": "Sturen"}, "settings": {"message": "instellingen"}, "sign": {"message": "Teken"}, "signatureRequest": {"message": "Ondertekeningsverzoek"}, "stateLogs": {"message": "Staatslogboeken"}, "stateLogsDescription": {"message": "Staatslogboeken bevatten uw openbare accountadressen en verzonden transacties."}, "supportCenter": {"message": "Bezoek ons ​​ondersteuningscentrum"}, "symbolBetweenZeroTwelve": {"message": "Symbool moet 11 tekens of minder zijn."}, "terms": {"message": "Gebruiksvoorwaarden"}, "to": {"message": "<PERSON>ar"}, "tokenAlreadyAdded": {"message": "Token is al toegevoegd."}, "total": {"message": "Totaal"}, "unknown": {"message": "Onbekend"}, "unlock": {"message": "Log in"}, "urlErrorMsg": {"message": "Voor URI's is het juiste HTTP / HTTPS-voorvoegsel vereist."}, "usedByClients": {"message": "Gebruikt door verschillende klanten"}, "visitWebSite": {"message": "Bezoek onze website"}}