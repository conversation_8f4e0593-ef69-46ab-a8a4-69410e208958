{"QRHardwareInvalidTransactionTitle": {"message": "<PERSON><PERSON>"}, "QRHardwareMismatchedSignId": {"message": "Uyumsuz işlem verisi. Lütfen işlem ayrıntılarını kontrol edin."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "Başka hesap yok. Aşağıda listelenmeyen başka bir hesaba erişmek istiyorsanız lütfen donanım cüzdanınızı yeniden bağlayın ve seçin."}, "QRHardwareScanInstructions": {"message": "QR kodunu kameranızın önüne getirin. Ekran bulanık, ancak bu okumayı etkilemeyecektir."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "Cüzdanınızla imzaladıktan sonra imzayı almak için \"İmza Al\" öğesine tıklayın"}, "QRHardwareSignRequestGetSignature": {"message": "İmza Al"}, "QRHardwareSignRequestSubtitle": {"message": "Cüzdanınızı QR kodu ile tarayın"}, "QRHardwareSignRequestTitle": {"message": "<PERSON><PERSON>za iste"}, "QRHardwareUnknownQRCodeTitle": {"message": "<PERSON><PERSON>"}, "QRHardwareUnknownWalletQRCode": {"message": "Geçersiz QR kodu. Lütfen donanım cüzdanının senkronizasyon QR kodunu tarayın."}, "QRHardwareWalletImporterTitle": {"message": "QR Kodunu Tara"}, "QRHardwareWalletSteps1Description": {"message": "Resmi olarak QR kod destekleyen ortakların listesinden aşağıda seçim yapabilirsiniz."}, "QRHardwareWalletSteps1Title": {"message": "QR donanım cüzdanınızı bağlayın"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "$1 hesabı gizle", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "1 hesabı gizle"}, "SrpListShowAccounts": {"message": "$1 hesabı göster", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "1 hesabı göster"}, "about": {"message": "Hakkında"}, "accept": {"message": "Kabul ediyorum"}, "acceptTermsOfUse": {"message": "$1 bölümünü okudum ve kabul ediyorum", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Kameranıza eri<PERSON><PERSON>..."}, "account": {"message": "<PERSON><PERSON><PERSON>"}, "accountActivity": {"message": "<PERSON><PERSON><PERSON> akt<PERSON>"}, "accountActivityText": {"message": "Hakkında bildirim almak istediğiniz hesapları seçin:"}, "accountDetails": {"message": "<PERSON><PERSON><PERSON>"}, "accountIdenticon": {"message": "Hesap Identicon"}, "accountIsntConnectedToastText": {"message": "$1 şuna bağlı değil: $2"}, "accountName": {"message": "<PERSON><PERSON><PERSON>"}, "accountNameDuplicate": {"message": "Bu hesap adı zaten mevcut", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "<PERSON>u hesap adı rezerve edilmiş\n", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "<PERSON><PERSON><PERSON>"}, "accountPermissionToast": {"message": "<PERSON><PERSON><PERSON>"}, "accountSelectionRequired": {"message": "Bir hesap seçmeniz gerekiyor!"}, "accountTypeNotSupported": {"message": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>"}, "accounts": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "accountsConnected": {"message": "Hesaplar bağlandı"}, "accountsPermissionsTitle": {"message": "Hesaplarınızı görmek ve işlem önermek"}, "accountsSmallCase": {"message": "hesaplar"}, "active": {"message": "Aktif"}, "activity": {"message": "E<PERSON>kin<PERSON>"}, "activityLog": {"message": "Etkinlik günlüğü"}, "add": {"message": "<PERSON><PERSON>"}, "addACustomNetwork": {"message": "<PERSON>zel bir ağ ekle"}, "addANetwork": {"message": "<PERSON><PERSON>"}, "addANickname": {"message": "Takma ad ekle"}, "addAUrl": {"message": "URL ekle"}, "addAccount": {"message": "<PERSON><PERSON><PERSON>"}, "addAccountFromNetwork": {"message": "$1 hesabı ekle", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "MetaM<PERSON>'e hesap ekle"}, "addAcquiredTokens": {"message": "MetaMask kullanarak elde ettiğiniz tokenleri ekleyin"}, "addAlias": {"message": "<PERSON><PERSON><PERSON> adı <PERSON>"}, "addBitcoinAccountLabel": {"message": "Bitcoin hesabı"}, "addBlockExplorer": {"message": "Bir blok gezgini ekle"}, "addBlockExplorerUrl": {"message": "Bir blok gezgini URL adresi ekle"}, "addContact": {"message": "<PERSON><PERSON><PERSON>"}, "addCustomNetwork": {"message": "<PERSON>zel ağ ekle"}, "addEthereumChainWarningModalHeader": {"message": "Bu RPC sağlayıcısını sadece ona güvenebileceğinizden eminseniz ekleyin. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Kötü amaçlı sağlayıcılar blokzincirinin durumu hakkında yalan söyleyebilir ve ağ aktivitenizi kayıt altına alabilir."}, "addEthereumChainWarningModalListHeader": {"message": "Şunları yapabileceği için sa<PERSON>ı<PERSON>ınızın güvenilir olması önemlidir:"}, "addEthereumChainWarningModalListPointOne": {"message": "Hesaplarınızı ve IP adreslerinizi görmek ve onları birbirleriyle ilişkilendirmek"}, "addEthereumChainWarningModalListPointThree": {"message": "<PERSON><PERSON><PERSON> baki<PERSON>lerini ve diğer zincir içi durumları göster"}, "addEthereumChainWarningModalListPointTwo": {"message": "İşlemlerinizi yayınlamak"}, "addEthereumChainWarningModalTitle": {"message": "Ethereum Ana Ağı için yeni bir RPC sağlayıcısı ekliyorsunuz"}, "addEthereumWatchOnlyAccount": {"message": "Bir Ethereum hesabını izle (Beta)"}, "addFriendsAndAddresses": {"message": "Güvendiğiniz arkadaşlarınızı ve adresleri ekleyin"}, "addHardwareWalletLabel": {"message": "Donanım cüzdanı"}, "addIPFSGateway": {"message": "Tercih ettiğiniz IPFS ağ geçidini ekleyin"}, "addImportAccount": {"message": "Hesap veya donanım cüzdanı ekleyin"}, "addMemo": {"message": "Not ekleyin"}, "addNetwork": {"message": "<PERSON><PERSON>"}, "addNetworkConfirmationTitle": {"message": "$1 ekle", "description": "$1 represents network name"}, "addNewAccount": {"message": "Yeni bir Ethereum hesabı ekle"}, "addNewEthereumAccountLabel": {"message": "Ethereum hesabı"}, "addNewSolanaAccountLabel": {"message": "<PERSON><PERSON> hesa<PERSON>ı"}, "addNft": {"message": "NFT ekleyin"}, "addNfts": {"message": "NFT ekleyin"}, "addNonEvmAccount": {"message": "$1 hesap ekle", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "$1 ağını etkinleştirmek için bir $2 hesabı oluşturmanız gerekir.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "RPC URL adresi ekle"}, "addSnapAccountToggle": {"message": "\"Snap hesabı ekle (Beta)\" özelliğini etkinleştir"}, "addSnapAccountsDescription": {"message": "Bu özelliğin açılması size doğrudan hesap listenizden bir Snap hesabı ekleme seçeneği verir. Bir Snap hesabı yüklüyorsanız bunun üçüncü taraf bir hizmet olduğunu unutmayın."}, "addSuggestedNFTs": {"message": "Önerilen NFT'leri <PERSON>"}, "addSuggestedTokens": {"message": "Önerilen Tokenleri ekle"}, "addToken": {"message": "<PERSON><PERSON> ekle"}, "addTokenByContractAddress": {"message": "Bir tokeni bulamadınız mı? Adresini yapıştırarak dilediğiniz tokeni manuel olarak ekleyebilirsiniz. Token sözleşme adreslerini $1 alanında bulabilirsiniz", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "URL ekle"}, "addingAccount": {"message": "<PERSON><PERSON><PERSON>"}, "addingCustomNetwork": {"message": "<PERSON><PERSON>"}, "additionalNetworks": {"message": "İlave ağlar"}, "address": {"message": "<PERSON><PERSON>"}, "addressCopied": {"message": "Adres kopyalandı!"}, "addressMismatch": {"message": "Site adresi uyumsuzluğu"}, "addressMismatchOriginal": {"message": "Mevcut URL: $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Punycode sürümü: $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Gelişmiş"}, "advancedBaseGasFeeToolTip": {"message": "İşleminiz bloka dahil edildiğinde maks. baz ücretiniz ile gerçek paz ücret arasındaki fark iade edilecektir. <PERSON>lam miktar, maks. baz ücret (GWEI'de) * gaz limiti olarak hesaplanacaktır."}, "advancedDetailsDataDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsHexDesc": {"message": "On Altılı"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Bir hesabın işlem numarasıdır. İlk işlem için nonce 0 olup sıralı olarak artar."}, "advancedGasFeeDefaultOptIn": {"message": "Bu değerleri $1 ağı için var<PERSON>ılanım olarak kaydet.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Gelişmiş gaz ücreti"}, "advancedGasPriceTitle": {"message": "Gaz fiyatı"}, "advancedPriorityFeeToolTip": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> ücreti (başka bir deyişle \"madenci bahşişi\") <PERSON><PERSON><PERSON><PERSON> madencilere gider ve işleminizin öncelikli olarak gerçekleştirilmesini teşvik eder."}, "airDropPatternDescription": {"message": "Token'ın zincir içi geçmişi, da<PERSON> önce şüpheli airdrop faaliyetlerinin olduğu durumları açığa çıkarır."}, "airDropPatternTitle": {"message": "Airdrop Modeli"}, "airgapVault": {"message": "AirGap Vault"}, "alert": {"message": "Uyarı"}, "alertAccountTypeUpgradeMessage": {"message": "Hesabınızı akıllı hesap olarak güncelliyorsunuz. Aynı hesap adresini korurken daha hızlı işlemlerin ve daha düşük ağ ücretlerinin kilidini açacaksınız. $1."}, "alertAccountTypeUpgradeTitle": {"message": "<PERSON><PERSON><PERSON>"}, "alertActionBuyWithNativeCurrency": {"message": "$1 Al"}, "alertActionUpdateGas": {"message": "Gaz limitini güncelle"}, "alertActionUpdateGasFee": {"message": "<PERSON><PERSON><PERSON>"}, "alertActionUpdateGasFeeLevel": {"message": "<PERSON><PERSON> <PERSON> g<PERSON>"}, "alertDisableTooltip": {"message": "\"Ayarlar > Uyarı<PERSON>\" kı<PERSON><PERSON><PERSON> değiştirilebilir"}, "alertMessageAddressMismatchWarning": {"message": "Saldırganlar bazen site adresinde küçük değişiklikler yaparak siteleri taklit edebilir. Devam etmeden önce planladığınız site ile etkileşim kurduğunuzdan emin olun."}, "alertMessageChangeInSimulationResults": {"message": "Bu iş<PERSON> için tahmin edilen değişiklikler güncellendi. Devam etmeden önce dikkatle inceleyin."}, "alertMessageFirstTimeInteraction": {"message": "Bu adresle ilk kez etkileşimde bulunuyorsunuz. Devam etmeden önce doğru olduğundan emin olun."}, "alertMessageGasEstimateFailed": {"message": "Kesin bir ücret sunamıyoruz ve bu tahmin yüksek olabilir. Özel bir gaz limiti girmenizi öneririz ancak işlemin yine de başarısız olma riski vardır."}, "alertMessageGasFeeLow": {"message": "Düşük bir ücret seçerken işlemlerin daha yavaş olmasını ve bekleme sürelerinin daha uzun olmasını bekleyebilirsiniz. Daha hızlı işlemler için Piyasa veya Agresif ücret seçeneklerini seçin."}, "alertMessageGasTooLow": {"message": "Bu işlemle devam etmek için gaz limitini 21000 veya üzeri olacak şekilde artırmanız gerekecek."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Hesabınızda ağ ücretlerini ödemek için yeterli $1 yok."}, "alertMessageNetworkBusy": {"message": "Gaz fiyatları yüksektir ve tahmin daha az kesindir."}, "alertMessageNoGasPrice": {"message": "<PERSON>z ücreti manuel o<PERSON>ak güncelleyene dek bu işleme devam edemiyoruz."}, "alertMessageSignInDomainMismatch": {"message": "Talepte bulunan site giriş yaptığınız site değil. Bu durum oturum açma bilgilerinizi çalma teşebbüsü olabilir."}, "alertMessageSignInWrongAccount": {"message": "Bu site sizden yanlış hesabı kullanarak giriş yapmanızı istiyor."}, "alertModalAcknowledge": {"message": "Riski anlıyor ve yine de ilerlemek istiyorum"}, "alertModalDetails": {"message": "Uyarı Ayrıntıları"}, "alertModalReviewAllAlerts": {"message": "Tüm uyarıları incele"}, "alertReasonChangeInSimulationResults": {"message": "Sonuç<PERSON>"}, "alertReasonFirstTimeInteraction": {"message": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "alertReasonGasEstimateFailed": {"message": "Ücret yanlış"}, "alertReasonGasFeeLow": {"message": "<PERSON><PERSON>z <PERSON>"}, "alertReasonGasTooLow": {"message": "Gaz limiti düşük"}, "alertReasonInsufficientBalance": {"message": "Para yetersiz"}, "alertReasonNetworkBusy": {"message": "<PERSON><PERSON>"}, "alertReasonNoGasPrice": {"message": "Ücret tahmini mevcut değil"}, "alertReasonPendingTransactions": {"message": "İşlem beklemede"}, "alertReasonSignIn": {"message": "Şü<PERSON><PERSON> g<PERSON>"}, "alertReasonWrongAccount": {"message": "Yanlış hesap"}, "alertSelectedAccountWarning": {"message": "B<PERSON> <PERSON>p, cüzdanınızda seçilenden farklı bir hesap içindir. Başka bir hesap kullanmak için hesabı siteye bağlayın."}, "alerts": {"message": "Uyarılar"}, "all": {"message": "Tümü"}, "allNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "allPermissions": {"message": "<PERSON><PERSON><PERSON>"}, "allTimeHigh": {"message": "<PERSON>ü<PERSON> z<PERSON>ın en yükseği"}, "allTimeLow": {"message": "<PERSON>ü<PERSON> z<PERSON>ın en düşüğü"}, "allowNotifications": {"message": "Bildirimlere izin ver"}, "allowWithdrawAndSpend": {"message": "$1 için şu tutara kadar para çekme ve harcama izni ver:", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "<PERSON><PERSON>"}, "amountReceived": {"message": "Alınan Tutar"}, "amountSent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "andForListItems": {"message": "$1 ve $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 ve $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "Dünyanın en güvenilir kripto cüzdanı", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "approve": {"message": "<PERSON><PERSON><PERSON> limitini on<PERSON>la"}, "approveButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "approveIncreaseAllowance": {"message": "$1 harcama üst limitini artır", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "$1 harcama üst limitini onayla", "description": "The token symbol that is being approved"}, "approved": {"message": "Onaylandı"}, "approvedOn": {"message": "$1 üzerinde onaylandı", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "$2 için $1 üzerinde onaylandı", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "Emin misin?"}, "asset": {"message": "Varlık"}, "assetChartNoHistoricalPrices": {"message": "Herhangi bir geçmiş veri getiremedik"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "Varlık seçenekleri"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Varlıklar"}, "assetsDescription": {"message": "Cüzdanınızdaki token'ler otomatik algılansın, NFT'ler gösterilsin ve toplu hesap bakiye güncellemeleri alınsın"}, "attemptToCancelSwapForFree": {"message": "Swap işlemini ücretsiz iptal etme girişimi"}, "attributes": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "attributions": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "auroraRpcDeprecationMessage": {"message": "Infura RPC URL adresi artık Aurora'yı desteklemiyor."}, "authorizedPermissions": {"message": "Aşağıdaki izinleri verdiniz"}, "autoDetectTokens": {"message": "Tokenleri otomatik algıla"}, "autoDetectTokensDescription": {"message": "Cüzdanınıza gönderilen yeni tokenleri algılamak ve göstermek için üçüncü taraf API'leri kullanırız. Uygulamanın bu hizmetlerden otomatik olarak veri çekmesini istemiyorsanız kapatın. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Otomatik Kilitleme Say<PERSON>ı (dakika)"}, "autoLockTimeLimitDescription": {"message": "MetaMask kilitlenmeden önce dakika olarak boşta kalma sü<PERSON> beli<PERSON>."}, "average": {"message": "Ortalama"}, "back": {"message": "<PERSON><PERSON>"}, "backupAndSync": {"message": "Yedekleme ve senkronizasyon"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "temel işlevsellik"}, "backupAndSyncEnable": {"message": "Yedekleme ve senkronizasyonu aç"}, "backupAndSyncEnableConfirmation": {"message": "Yedekleme ve senkronizasyonu açtığınızda $1 özelliğini de açarsanız. Devam etmek istiyor musunuz?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "Yedekleme ve senk<PERSON><PERSON>, <PERSON>zel ayarlarınız ve özellikleriniz için şifrelenmiş verileri depolamamızı sağlar. Bu, cihazlar arasında aynı MetaMask deneyimini yaşamanızı sağlar ve MetaMask'i yeniden yüklemeniz gerekirse ayarları ve özellikleri geri yükler. Bu işlem Gizli Kurtarma İfadenizi yedeklemez. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Tercihlerinizi dilediğiniz zaman $1 sayfasından güncelleyebilirsiniz", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Ayarlar > <PERSON><PERSON><PERSON><PERSON> ve senkron<PERSON>on."}, "backupAndSyncFeatureAccounts": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "backupAndSyncManageWhatYouSync": {"message": "<PERSON><PERSON>ri senkronize ettiğinizi yönetin"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Cihazlarınız arasında nelerin senkronize edildiğini açın."}, "backupAndSyncPrivacyLink": {"message": "Gizliliğinizi nasıl koruduğumuzu öğrenin"}, "backupAndSyncSlideDescription": {"message": "Hesaplarınızı yedekleyin ve ayarları senkronize edin."}, "backupAndSyncSlideTitle": {"message": "Yedekleme ve senkronizasyon hizmetinizde"}, "backupApprovalInfo": {"message": "B<PERSON> gizli kod, cihazınız<PERSON> ka<PERSON>bet<PERSON>iz, şifrenizi unutmanız, MetaMask'ı yeniden kurmanızın gerektiği ya da cüzdanınıza başka bir cihazdan oturum açmak istemeniz durumunda cüzdanınıza erişim sağlamak için gereklidir."}, "backupApprovalNotice": {"message": "Cüzdanınızı ve paranızı güvende tutmak için Gizli Kurtarma İfadenizi yedekleyin."}, "backupKeyringSnapReminder": {"message": "Snap'i kaldırmadan önce bu Snap tarafından oluşturulan tüm hesaplara kendi başınıza erişim sağlayabildiğinizden emin olun"}, "backupNow": {"message": "<PERSON><PERSON><PERSON>"}, "balance": {"message": "Bakiye"}, "balanceOutdated": {"message": "Bakiye güncel olmayabilir"}, "baseFee": {"message": "Baz ücret"}, "basic": {"message": "Temel"}, "basicConfigurationBannerTitle": {"message": "Temel işlevsellik kapalı"}, "basicConfigurationDescription": {"message": "MetaMask, internet hizmetleri üzerinden token bilgileri ve gaz ayarları gibi temel özellikler sunar. İnternet hizmetlerini kullandığınızda IP adresiniz, bu durumda MetaMask ile, paylaşılır. Bu tıpkı herhangi bir web sitesini ziyaret ettiğinizde olduğu gibidir. MetaMask bu verileri geçici olarak kullanır ve verilerinizi hiçbir zaman satmaz. Bir VPN kullanabilir veya bu hizmetleri kapatabilirsiniz ancak bu durum MetaMask deneyiminizi etkileyebilir. Daha fazla bilgi için $1 bölümümüzü okuyun.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "Temel işlevsellik"}, "basicConfigurationModalCheckbox": {"message": "Anlıyorum ve devam etmek istiyorum"}, "basicConfigurationModalDisclaimerOff": {"message": "<PERSON><PERSON>, MetaMask'te zamanınızı tamamen optimize edemeyeceğiniz anlamına gelir. <PERSON>mel özellikler (token bilgileri, en iyi gaz ayarları vb. gibi) sizin için sunulmayacaktır."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "<PERSON><PERSON>u kapattığınızdaşuradaki tüm özellikler de devre dışı bırakılır: $1 ve $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "güvenlik ve gizlilik, yedekleme ve senkronizasyon"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "basicConfigurationModalDisclaimerOn": {"message": "MetaMask'te zamanınızı tamamen optimize etmek için bu özelliği açmanız gerekecektir. Temel özellikler (token bilgileri, en iyi gaz ayarları vb. gibi) web3 deneyimi için önemlidir."}, "basicConfigurationModalHeadingOff": {"message": "Temel işlevselliği kapat"}, "basicConfigurationModalHeadingOn": {"message": "Temel işlevselliği aç"}, "bestPrice": {"message": "En iyi fiyat"}, "beta": {"message": "Beta"}, "betaHeaderText": {"message": "Bu bir beta sürümdür. Lütfen hataları bildirin $1"}, "betaMetamaskVersion": {"message": "MetaMask Beta Sürümü"}, "betaTerms": {"message": "Beta Kullanım koşulları"}, "billionAbbreviation": {"message": "MR", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "Varlık", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "Blok Gezgini URL Adresi"}, "blockExplorerUrlDefinition": {"message": "Bu ağ için blok gezgini olarak kullanılan URL adresi."}, "blockExplorerView": {"message": "Hesabı şurada görüntüleyin: $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "Blockaid"}, "blockaidAlertDescriptionBlur": {"message": "<PERSON><PERSON> ederseniz Blur'de yer alan tüm varlıklarınız riske girebilir."}, "blockaidAlertDescriptionMalicious": {"message": "Kötü niyetli bir siteyle etkileşimde bulunuyorsunuz. Devam ederseniz varlıklarınızı kaybedeceksiniz."}, "blockaidAlertDescriptionOpenSea": {"message": "Devam ederseniz OpenSea'de yer alan tüm varlıklarınız riske girebilir."}, "blockaidAlertDescriptionOthers": {"message": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz. Bu talebi iptal etmenizi öneririz."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Varlıklarınızı bir dolandırıcıya gönderiyorsunuz. Devam ederseniz bu varlıkları kaybedeceksiniz."}, "blockaidAlertDescriptionWithdraw": {"message": "Bu talebi onaylarsanız bir dolandırıcının varlıklarınızı çekmesine ve harcamasına izin verirsiniz. Varlıklarınızı geri alamazsınız."}, "blockaidDescriptionApproveFarming": {"message": "Bu talebi onaylarsanız dolandırıcılıkla ünlü üçüncü bir taraf tüm varlıklarınızı çalabilir."}, "blockaidDescriptionBlurFarming": {"message": "Bu talebi onaylarsanız birisi Blur üzerinde yer alan varlıklarınızı çalabilir."}, "blockaidDescriptionErrored": {"message": "Bir hatadan dolayı güvenlik uyarılarını kontrol edemedik. <PERSON><PERSON><PERSON> ilgili her adrese güveni<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "blockaidDescriptionMaliciousDomain": {"message": "Kötü niyetli bir alanla etkileşimde bulunuyorsunuz. Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz."}, "blockaidDescriptionMightLoseAssets": {"message": "Bu talebi onaylarsanız varlıklarınızı kaybedebilirsiniz."}, "blockaidDescriptionSeaportFarming": {"message": "Bu talebi onaylarsanız birisi OpenSea üzerinde yer alan varlıklarınızı çalabilir."}, "blockaidDescriptionTransferFarming": {"message": "Bu talebi onaylarsanız dolandırıcılıkla ünlü üçüncü bir taraf tüm varlıklarınızı çalar."}, "blockaidMessage": {"message": "Gizlilik koruması - hiçbir veri üçüncü taraflarla paylaşılmaz. Arbitrum, Avalanche, BNB chain, Ethereum Ana Ağı, Linea, Optimism, Polygon, Base ve Sepolia için sunulur."}, "blockaidTitleDeceptive": {"message": "Bu aldatıcı bir talep"}, "blockaidTitleMayNotBeSafe": {"message": "<PERSON><PERSON><PERSON><PERSON> olun"}, "blockaidTitleSuspicious": {"message": "<PERSON><PERSON> <PERSON><PERSON> bir talep"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "Ödünç Alındı"}, "boughtFor": {"message": "Satın alma amacı"}, "bridge": {"message": "Köprü"}, "bridgeAllowSwappingOf": {"message": "Köprü amacıyla $3 üzerinde tam $1 $2 için eri<PERSON>ime izin ver", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Köprü i<PERSON>: $1", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Belirtilen tutar olan $1 $2 için erişim izni veriyorsunuz. Sözleşme başka hiçbir paraya erişim sağlayamayacaktır."}, "bridgeApprovalWarningForHardware": {"message": "Köprü amacıyla $1 $2 için erişim izni vermeniz ve ardından $2 alanına köprüyü onaylamanız gerekecek. Bu işlem için iki onay gerekecek."}, "bridgeBlockExplorerLinkCopied": {"message": "Blok gezgini bağlantısı kopyalandı!"}, "bridgeCalculatingAmount": {"message": "Hesaplanıyor..."}, "bridgeConfirmTwoTransactions": {"message": "Donanım cüzdanınızda 2 işlemi onaylamanız gerekecek:"}, "bridgeCreateSolanaAccount": {"message": "Solana hesabı oluştur"}, "bridgeCreateSolanaAccountDescription": {"message": "Solana ağına swap gerçekleştirmek için bir hesaba ve alıcı adresine ihtiyacınız var."}, "bridgeCreateSolanaAccountTitle": {"message": "Önce bir Solana hesabına ihtiyacınız olacak."}, "bridgeDetailsTitle": {"message": "Köprü bilgileri", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeEnterAmountAndSelectAccount": {"message": "<PERSON><PERSON> girin ve hedef hesabı seçin"}, "bridgeExplorerLinkViewOn": {"message": "$1 üzerinde görüntüle"}, "bridgeFetchNewQuotes": {"message": "Yeni bir tanesi alınsın mı?"}, "bridgeFrom": {"message": "Şuradan köprü:"}, "bridgeFromTo": {"message": "$1 $2, $3 a<PERSON><PERSON><PERSON>", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Önceki ekranda teklif verilen tüm ağ ücretlerine her iki işlem de dahil olup bölünmeyecektir."}, "bridgeNetCost": {"message": "Net maliyet"}, "bridgeQuoteExpired": {"message": "Teklifiniz zaman aşımına uğradı."}, "bridgeSelectDestinationAccount": {"message": "<PERSON><PERSON><PERSON>"}, "bridgeSelectNetwork": {"message": "<PERSON><PERSON>"}, "bridgeSelectTokenAmountAndAccount": {"message": "<PERSON><PERSON>, tutar ve hedef hesabı seçin"}, "bridgeSelectTokenAndAmount": {"message": "Token ve miktar seçin"}, "bridgeSolanaAccountCreated": {"message": "Solana hesabı oluşturuldu"}, "bridgeStatusComplete": {"message": "Tamamlandı", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Başarısız oldu", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "<PERSON><PERSON>", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$2 üzerinde $1 alındı", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "$2 üzerinde $1 alınıyor", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "$1 ile $2 swap edildi", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "$1 ile $2 swap ediliyor", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Şartlar"}, "bridgeTimingMinutes": {"message": "$1 dk", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "Şuraya köprü:"}, "bridgeToChain": {"message": "Şuraya köprü: $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Bu token'ı manuel olarak eklediyseniz köprü yapmadan önce paranıza gelebilecek risklerin farkında olduğunuzdan emin olun."}, "bridgeTokenCannotVerifyTitle": {"message": "Bu token'ı doğrulayamıyoruz."}, "bridgeTransactionProgress": {"message": "İşlem $1 / 2"}, "bridgeTxDetailsBridging": {"message": "Köprü"}, "bridgeTxDetailsDelayedDescription": {"message": "<PERSON><PERSON>fle<PERSON>:"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "MetaMask Destek"}, "bridgeTxDetailsDelayedTitle": {"message": "3 saatten daha uzun süre geçti mi?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Durum"}, "bridgeTxDetailsTimestamp": {"message": "Zaman damgası"}, "bridgeTxDetailsTimestampValue": {"message": "$1, $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Toplam gaz ücreti"}, "bridgeTxDetailsYouReceived": {"message": "Aldığınız:"}, "bridgeTxDetailsYouSent": {"message": "Gönderdiğiniz:"}, "bridgeValidationInsufficientGasMessage": {"message": "Bu köprü için gaz ücretini ödemeye yetecek $1 yok. Daha küçük bir tutar girin veya daha fazla $1 satın alın."}, "bridgeValidationInsufficientGasTitle": {"message": "Gaz için daha fazla $1 gerekli"}, "bridging": {"message": "Köprü"}, "browserNotSupported": {"message": "Tarayıcınız desteklenmiyor..."}, "buildContactList": {"message": "<PERSON>şi listenizi o<PERSON>"}, "builtAroundTheWorld": {"message": "MetaMask dünya çapında geliştirilmiş ve yapılmıştır."}, "bulletpoint": {"message": "·"}, "busy": {"message": "Meş<PERSON>l"}, "buyAndSell": {"message": "Al/Sat"}, "buyMoreAsset": {"message": "Daha fazla $1 satın al", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Şimdi Satın Al"}, "bytes": {"message": "<PERSON><PERSON>"}, "canToggleInSettings": {"message": "Ayarlar -> <PERSON><PERSON><PERSON><PERSON> kısmında bu bildirimi yeniden etkinleştirebilirsiniz."}, "cancel": {"message": "İptal"}, "cancelPopoverTitle": {"message": "İşlemi iptal et"}, "cancelSpeedUpLabel": {"message": "Bu gaz ücreti orijinali $1 olacak.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Bir işleme $1 uygulamak için ağ tarafından tanınması amacıyla gaz ücreti en az %10 artırılmalıdır.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "İptal edildi"}, "chainId": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "chainIdDefinition": {"message": "<PERSON><PERSON><PERSON><PERSON>, bu ağda işlemleri imzalamak için k<PERSON>ılır."}, "chainIdExistsErrorMsg": {"message": "Bu Zincir Kimliği şu anda $1 ağı tarafından kullanılıyor."}, "chainListReturnedDifferentTickerSymbol": {"message": "Bu token sembolü, girilen ağ adı veya zincir kimliği ile uyumlu değil. Pek çok popüler token benzer sembolleri kullanır ve dolandırıcılar, karşılığında daha değerli bir token gönderecekleri yönünde sizi kandırmak için bunları kullanabilirler. Devam etmeden önce her şeyi doğrulayın."}, "chooseYourNetwork": {"message": "Ağınızı seçin"}, "chooseYourNetworkDescription": {"message": "Varsayılan ayarlarınızı ve yapılandırmalarınızı kullandığımızda, Ethereum verilerine mümkün olan en güvenilir ve gizli erişimi sunmak amacıyla varsayılan uzak yordam çağrısı (RPC) sağlayıcısı olarak Infura'yı kullanırız. Sınırlı durumlarda, kullanıcılarımıza en iyi deneyimi sağlamak amacıyla başka RPC sağlayıcıları kullanabiliriz. Kendi RPC'nizi seçebilirsiniz ancak her RPC'nin işlem yapmak için IP adresinize ve Ethereum cüzdanınıza ulaşacağını unutmayın. Infura'nın, EVM hesapları için verileri nasıl kullandığı hakkında bilgi edinmek için $1 bölümümüzü, Solana hesapları için $2 bölümümüzü okuyun.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "buraya tıklayın"}, "chromeRequiredForHardwareWallets": {"message": "Donanım Cüzdanınıza bağlamak için MetaMask'ı Google Chrome'da kullanmanız gerekir."}, "circulatingSupply": {"message": "Dolaşım<PERSON>i arz"}, "clear": {"message": "<PERSON><PERSON><PERSON>"}, "clearActivity": {"message": "Aktiviteyi ve nonce verilerini temizle"}, "clearActivityButton": {"message": "Aktivite sekme verilerini te<PERSON>zle"}, "clearActivityDescription": {"message": "Bu işlem hesabın nonce verilerini sıfırlar ve cüzdanınızdaki aktivite sekmesinden verileri siler. Sadece geçerli hesap ve ağ etkilenecektir. Bakiyeleriniz ve gelen işlemleriniz değişmeyecektir."}, "click": {"message": "<PERSON>ha fazla bilgi <PERSON>"}, "clickToConnectLedgerViaWebHID": {"message": "WebHID üzerinden Ledger'ınızı bağlamak için tıklayın", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "Ka<PERSON><PERSON>"}, "closeExtension": {"message": "Uzantıyı kapat"}, "closeWindowAnytime": {"message": "Dilediğiniz zaman bu pencereyi kapatabilirsiniz."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Koleksiyon adı"}, "comboNoOptions": {"message": "Hiçbir seçenek bulunamadı", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "Token arzının ç<PERSON>ğ<PERSON>luğu en fazla token sahibi olanlar tarafından tutulur ve merkezi fiyat manipülasyonu riski teşkil eder"}, "concentratedSupplyDistributionTitle": {"message": "Konsantre Arz Dağılımı"}, "configureSnapPopupDescription": {"message": "<PERSON>u anda bu snapi yapılandırmak için MetaMask'ten ayrılıyorsunuz."}, "configureSnapPopupInstallDescription": {"message": "<PERSON>u anda bu snapi yüklemek için MetaMask'ten ayrılıyorsunuz."}, "configureSnapPopupInstallTitle": {"message": "<PERSON><PERSON><PERSON>"}, "configureSnapPopupLink": {"message": "<PERSON><PERSON> etmek için bu bağlantıya tıklayın:"}, "configureSnapPopupTitle": {"message": "Snapi yapılandır"}, "confirm": {"message": "<PERSON><PERSON><PERSON>"}, "confirmAccountTypeSmartContract": {"message": "Akıllı hesap"}, "confirmAccountTypeStandard": {"message": "<PERSON><PERSON> hesap"}, "confirmAlertModalAcknowledgeMultiple": {"message": "Uyarıları kabul ediyorum ve yine de ilerlemek istiyorum"}, "confirmAlertModalAcknowledgeSingle": {"message": "Uyarıyı kabul ediyor ve yine de ilerlemek istiyorum"}, "confirmFieldPaymaster": {"message": "Ücreti ödeyen taraf"}, "confirmFieldTooltipPaymaster": {"message": "Bu işlemin ücreti paymaster akıll<PERSON> sözleşmesi tarafından ödenecektir."}, "confirmGasFeeTokenBalance": {"message": "Bak:"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Para yetersiz"}, "confirmGasFeeTokenMetaMaskFee": {"message": "$1 ücret dahildir"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "MetaMask bu işlemi tamamlamak için bakiyeye katkıda bulunuyor."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "<PERSON>ğ ücretini cüzdanınızdaki bakiyeyi kullanarak <PERSON>."}, "confirmGasFeeTokenModalPayETH": {"message": "ETH ile öde"}, "confirmGasFeeTokenModalPayToken": {"message": "Başka token'lerle öde"}, "confirmGasFeeTokenModalTitle": {"message": "Bir <PERSON> seçin"}, "confirmGasFeeTokenToast": {"message": "Bu ağ ücretini $1 ile ödüyorsunuz"}, "confirmGasFeeTokenTooltip": {"message": "<PERSON><PERSON> ü<PERSON>t, işleminizi gerçekleştirmek için ağa ödenir. ETH dışı token'ler veya önceden yüklenmiş ETH için $1 MetaMask ücreti dahildir."}, "confirmInfoAccountNow": {"message": "Ş<PERSON>di"}, "confirmInfoSwitchingTo": {"message": "Geçiş Yapılıyor"}, "confirmNestedTransactionTitle": {"message": "İşlem $1"}, "confirmPassword": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confirmRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON>arma İfadesini Onayla"}, "confirmSimulationApprove": {"message": "Onaylıyorsunuz"}, "confirmTitleAccountTypeSwitch": {"message": "<PERSON><PERSON><PERSON>"}, "confirmTitleApproveTransactionNFT": {"message": "<PERSON> çekme talebi"}, "confirmTitleDeployContract": {"message": "Bir sözleşme kurulumu gerçekleştirin"}, "confirmTitleDescApproveTransaction": {"message": "Bu site NFT'lerinizi çekmek için izin istiyor"}, "confirmTitleDescDelegationRevoke": {"message": "<PERSON><PERSON><PERSON> standart bir he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Akıllı bir hesaba geçiş yapıyorsunuz"}, "confirmTitleDescDeployContract": {"message": "Bu site sözleşme kurulumu gerçekleştirmenizi istiyor"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Bu site tokenlerinizi çekmek için izin istiyor"}, "confirmTitleDescPermitSignature": {"message": "Bu site token'le<PERSON><PERSON> harcamak için izin istiyor."}, "confirmTitleDescSIWESignature": {"message": "Bir site bu hesabın sahibi olduğunuzu kanıtlamak için giriş yapmanızı istiyor."}, "confirmTitleDescSign": {"message": "Onaylamadan önce talep bilgilerini inceleyin."}, "confirmTitlePermitTokens": {"message": "Harcama üst limiti talebi"}, "confirmTitleRevokeApproveTransaction": {"message": "İzni kaldır"}, "confirmTitleSIWESignature": {"message": "<PERSON><PERSON><PERSON>"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "İzni kaldır"}, "confirmTitleSignature": {"message": "<PERSON><PERSON><PERSON>"}, "confirmTitleTransaction": {"message": "İşlem talebi"}, "confirmationAlertDetails": {"message": "Varlıklarınızı korumak için talebi reddetmenizi öneririz."}, "confirmationAlertModalTitleDescription": {"message": "Varlıklarınız risk altında olabilir"}, "confirmed": {"message": "Onaylandı"}, "confusableUnicode": {"message": "\"$1\" ile \"$2\" benzer."}, "confusableZeroWidthUnicode": {"message": "Sıfır genişlikte karakter bulundu."}, "confusingEnsDomain": {"message": "ENS adında karıştırılabilir bir karakter tespit ettik. Olası bir dolandırıcılığı önlemek için ENS adını kontrol edin."}, "connect": {"message": "Bağla"}, "connectAccount": {"message": "<PERSON><PERSON>bı bağla"}, "connectAccountOrCreate": {"message": "<PERSON><PERSON><PERSON><PERSON> bağla ya da yeni hesap oluştur"}, "connectAccounts": {"message": "Hesapları bağla"}, "connectAnAccountHeader": {"message": "<PERSON><PERSON> hesabı bağla"}, "connectManually": {"message": "Mevcut siteye manuel o<PERSON> ba<PERSON>"}, "connectMoreAccounts": {"message": "<PERSON><PERSON> fazla hesap ba<PERSON>la"}, "connectSnap": {"message": "Şuna bağlanın: $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "MetaMask ile Bağlan"}, "connectedAccounts": {"message": "Bağlı hesaplar"}, "connectedAccountsDescriptionPlural": {"message": "Bu siteye bağlı $1 hesabınız var.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Bu siteye bağlı 1 hesabınız var."}, "connectedAccountsEmptyDescription": {"message": "MetaMask bu siteye bağlı değil. Bir web3 sitesine bağlanmak için siteyi bulun ve bağlan düğmesine tıklayın."}, "connectedAccountsListTooltip": {"message": "$1 hesap b<PERSON><PERSON><PERSON><PERSON>, ad<PERSON><PERSON>zi, aktivitenizi görebilir ve bağlı hesapları onaylamak için işlem önerebilir.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Bağlı hesaplar güncellendi"}, "connectedSites": {"message": "Bağlı siteler"}, "connectedSitesAndSnaps": {"message": "Bağlı siteler ve Snap'ler"}, "connectedSitesDescription": {"message": "$1 bu sitelere bağlanmış. Bu siteler hesap adresinizi görüntüleyebilir.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 herhangi bir siteye bağlanmamış.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMask bu siteye bağlı ancak henüz bağlı hesap yok"}, "connectedSnaps": {"message": "Bağlı Snap'ler"}, "connectedWithAccount": {"message": "$1 hesap bağlandı", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "$1 ile bağlandı", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 ağ bağlandı", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "$1 ile bağlandı", "description": "$1 represents network name"}, "connecting": {"message": "Bağlanıyor"}, "connectingTo": {"message": "Şuna bağlanılıyor: $1"}, "connectingToDeprecatedNetwork": {"message": "'$1' aşamalı olarak devre dışı bırakılıyor ve çalışmayabilir. Başka bir ağ deneyin."}, "connectingToGoerli": {"message": "Goerli Test Ağına Bağlanıyor"}, "connectingToLineaGoerli": {"message": "Linea Goerli test ağına bağlanılıyor"}, "connectingToLineaMainnet": {"message": "Linea Ana Ağına bağlanılıyor"}, "connectingToLineaSepolia": {"message": "Linea Sepolia test ağına bağlanılıyor"}, "connectingToMainnet": {"message": "Ethereum Ana Ağına bağlanıyor"}, "connectingToSepolia": {"message": "Sepolia test ağına bağlanılıyor"}, "connectionDescription": {"message": "Bu web sitesini MetaMask'e bağla"}, "connectionFailed": {"message": "Bağlantı başarısız oldu"}, "connectionFailedDescription": {"message": "$1 getirme başar<PERSON>s<PERSON>z oldu, ağınızı kontrol edip tekrar deneyin.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Bir siteye bağlanmak için bağlan düğmesini seçin. MetaMask yalnızca web3 sitelerine bağlanabilir."}, "connectionRequest": {"message": "Bağlantı talebi"}, "contactUs": {"message": "Bize ul<PERSON>şın"}, "contacts": {"message": "<PERSON><PERSON><PERSON>"}, "contentFromSnap": {"message": "$1 içeriği", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON> et"}, "contract": {"message": "S<PERSON>zleşme"}, "contractAddress": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>i"}, "contractAddressError": {"message": "<PERSON><PERSON>in s<PERSON>şme adresine token gönderiyorsunuz. Bu tokenlerin kaybedilmesine neden olabilir."}, "contractDeployment": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "contractInteraction": {"message": "Sözleşme Etkileşimi"}, "convertTokenToNFTDescription": {"message": "Bu varlığın bir NFT olduğunu tespit ettik. MetaMask artık NFT'ler için tam yerel desteğe sahiptir. Bunu token listenizden çıkarmak ve NFT olarak eklemek ister misiniz?"}, "convertTokenToNFTExistDescription": {"message": "Bu varlığın bir NFT olarak eklendiğini tespit ettik. Token listenizden çıkarmak ister misiniz?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "Kopyalandı."}, "copyAddress": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "copyAddressShort": {"message": "<PERSON><PERSON><PERSON>"}, "copyPrivateKey": {"message": "<PERSON><PERSON> k<PERSON>"}, "copyToClipboard": {"message": "<PERSON><PERSON> k<PERSON>"}, "copyTransactionId": {"message": "İşlem Kimliğini Kopyala"}, "create": {"message": "Oluştur"}, "createNewAccountHeader": {"message": "<PERSON><PERSON> bir he<PERSON><PERSON>"}, "createPassword": {"message": "<PERSON><PERSON><PERSON>"}, "createSnapAccountDescription": {"message": "$1 MetaMask'e yeni bir hesap eklemek istiyor."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON>"}, "createSolanaAccount": {"message": "Solana hesabı oluştur"}, "creatorAddress": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "crossChainSwapsLink": {"message": "MetaMask Portfolio ile ağlar arasında swap gerçekleştirin"}, "crossChainSwapsLinkNative": {"message": "Köprü ile ağlar arasında swap yapın"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "Para Birimi"}, "currencyRateCheckToggle": {"message": "Bakiyeyi ve token fiyat denetleyicisini g<PERSON>"}, "currencyRateCheckToggleDescription": {"message": "Bakiyenizi ve token fiyatınızı göstermek için $1 ve $2 API'lerini kullanırız. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Para Birimi <PERSON>ü"}, "currencySymbolDefinition": {"message": "Bu ağın para birimi için görüntülenen sembol."}, "currentAccountNotConnected": {"message": "Mevcut hesabınız bağlı değil"}, "currentExtension": {"message": "Mevcut uzantı sayfası"}, "currentLanguage": {"message": "Mevcut Dil"}, "currentNetwork": {"message": "Mevcut ağ", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "Bu ağ için mevcut rpc url artık kullanılmıyor."}, "currentTitle": {"message": "Mevcut:"}, "currentlyUnavailable": {"message": "Bu ağda kullanılamaz"}, "curveHighGasEstimate": {"message": "Agresif gaz tahmini grafiği"}, "curveLowGasEstimate": {"message": "Düşük gaz tahmini grafiği"}, "curveMediumGasEstimate": {"message": "<PERSON>yasa gaz tahmini gra<PERSON>i"}, "custom": {"message": "Gelişmiş"}, "customGasSettingToolTipMessage": {"message": "Gaz fiyatını özelleştirmek için $1 kullanın. Bu, bilgi sahibi değilseniz kafa karıştırıcı olabilir. Riski size ait olmak üzere kullanın.", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "<PERSON><PERSON>"}, "customSpendLimit": {"message": "<PERSON><PERSON>"}, "customToken": {"message": "<PERSON><PERSON>"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "Token algılama henüz bu ağda mevcut değil. Lütfen tokeni manuel olarak içe aktarın ve ona güvendiğinizden emin olun. $1 hakkında bilgi edinin"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Mevcut tokenlerin sahteleri de dahil olmak üzere herkes bir token oluşturabilir. $1 hakkında bilgi edinin"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Bir tokeni içe aktarmadan önce ona güvendiğinizden emin olun. $1 kaçınmayı öğrenin. Ayrıca token algılamayı $2 etkinleştirebilirsiniz."}, "customerSupport": {"message": "müşteri hizmetleri"}, "customizeYourNotifications": {"message": "Bildirimlerinizi özelleştirin"}, "customizeYourNotificationsText": {"message": "Almak istediğiniz bildirim türlerini açın:"}, "dappSuggested": {"message": "Site önerisi"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 bu fiyatı önerdi.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Önerilen site"}, "dappSuggestedHighShortLabel": {"message": "Site (yüksek)"}, "dappSuggestedShortLabel": {"message": "Site"}, "dappSuggestedTooltip": {"message": "$1 bu fiyatı önerdi.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "<PERSON><PERSON>"}, "data": {"message": "<PERSON><PERSON>"}, "dataCollectionForMarketing": {"message": "Pazarlama amacıyla veri toplama"}, "dataCollectionForMarketingDescription": {"message": "MetaMetrics'i, pazarlama iletişimlerimizle nasıl etkileşimde bulunduğunuzu öğrenmek için kullanacağız. İlgili haberleri (ürün özellikleri ve diğer materyaller gibi) paylaşabiliriz."}, "dataCollectionWarningPopoverButton": {"message": "<PERSON><PERSON>"}, "dataCollectionWarningPopoverDescription": {"message": "Pazarlama amacıyla veri toplama seçeneğini kapattınız. Bu, sadece bu cihaz için geçerlidir. MetaMask'i başka cihazlarda kullanırsanız bu cihazlarda da vazgeçtiğinizden emin olun."}, "dataUnavailable": {"message": "veri mevcut de<PERSON>"}, "dateCreated": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi"}, "dcent": {"message": "<PERSON><PERSON><PERSON>nt"}, "debitCreditPurchaseOptions": {"message": "Banka kartı veya kredi kartı ile satın alma seçenekleri"}, "decimal": {"message": "Token Ondalığı"}, "decimalsMustZerotoTen": {"message": "Ondalıklar en az 0, en fazla 36 olmalıdır."}, "decrypt": {"message": "<PERSON><PERSON><PERSON>"}, "decryptCopy": {"message": "Şifrelenmiş mesajı kopyala"}, "decryptInlineError": {"message": "Hata nedeniyle bu mesajın şifresi çözülemez: $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "Eyleminizi tamamlamak için $1 bu mesajı okumak istiyor", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "Mesajın <PERSON>"}, "decryptRequest": {"message": "<PERSON><PERSON><PERSON>"}, "defaultRpcUrl": {"message": "Varsayılan RPC URL adresi"}, "defaultSettingsSubTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, gü<PERSON>lik ve kullanım kolaylığını en iyi şekilde dengelemek için varsayılan ayarları kullanır. Gizliliğinizi daha fazla artırmak için bu ayarları değiştirin."}, "defaultSettingsTitle": {"message": "Varsayılan gizlilik ayarları"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "<PERSON><PERSON> sonra tekrar ziyaret et<PERSON>yi den<PERSON>."}, "defiTabErrorTitle": {"message": "Bu sayfayı yükleyemedik."}, "delete": {"message": "Sil"}, "deleteContact": {"message": "<PERSON><PERSON><PERSON><PERSON> sil"}, "deleteMetaMetricsData": {"message": "MetaMetrics verilerini sil"}, "deleteMetaMetricsDataDescription": {"message": "Bu i<PERSON><PERSON>, bu cihazdaki kullanımınızla ilişkili geçmiş MetaMetrics verilerini silecektir. Bu veriler silindikten sonra cüzdanınız ve hesaplarınız tam olarak şu anda oldukları gibi kalacaktır. Bu süreç 30 güne kadar sürebilir. $1 bölümümüzü görüntüleyin.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Bu talep analitik bir sistem sunucusu sorunundan dolayı şu anda tama<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin"}, "deleteMetaMetricsDataErrorTitle": {"message": "<PERSON>u anda bu verileri si<PERSON>"}, "deleteMetaMetricsDataModalDesc": {"message": "Tüm MetaMetrics verilerini kaldırmak üzeresiniz. <PERSON><PERSON> misiniz?"}, "deleteMetaMetricsDataModalTitle": {"message": "MetaMetrics verilerini sil?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "$1 tarihinde bir eylem başlattınız. Bu süreç 30 güne kadar sürebilir. $2 bölümünü görünt<PERSON><PERSON>in", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "Bu ağı silerseniz bu ağdaki varlıklarınızı görüntülemek için bu ağı tekrar eklemeniz gerekecek"}, "deleteNetworkTitle": {"message": "$1 ağını sil?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Cüzdan adresi veya QR kodu ile başka bir hesaptan kripto yatırın."}, "deprecatedGoerliNtwrkMsg": {"message": "Ethereum sistemindeki güncellemelerden dolayı Goerli test ağı yakında aşamalı olarak devre dışı bırakılacaktır."}, "deprecatedNetwork": {"message": "Bu ağ artık kullanılmıyor"}, "deprecatedNetworkButtonMsg": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deprecatedNetworkDescription": {"message": "Bağlanmaya çalıştığınız ağ artık Metamask tarafından desteklenmiyor. $1"}, "description": {"message": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "descriptionFromSnap": {"message": "$1 açıklaması", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "<PERSON>ygun hesap bulu<PERSON>ı"}, "destinationAccountPickerNoMatching": {"message": "Eşleşen hesap bulunamadı"}, "destinationAccountPickerReceiveAt": {"message": "<PERSON><PERSON><PERSON>:"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "Alıcı adres veya ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "Alıcı adres"}, "destinationTransactionIdLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Label for the destination transaction ID field."}, "details": {"message": "Ayrıntılar"}, "developerOptions": {"message": "Geliş<PERSON><PERSON><PERSON>"}, "disabledGasOptionToolTipMessage": {"message": "Orijinal gaz ücretinden minimum %10'luk bir artışı karşılamadığı için \"$1\" devre dışı bırakıldı.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Bağlantıyı kes"}, "disconnectAllAccounts": {"message": "<PERSON>ü<PERSON> hesap<PERSON><PERSON>n bağlantısını kes"}, "disconnectAllAccountsConfirmationDescription": {"message": "Bağlantıyı kesmek istediğinizden emin misiniz? Sitenin işlevselliğini kaybedebilirsiniz."}, "disconnectAllAccountsText": {"message": "hesaplar"}, "disconnectAllDescriptionText": {"message": "Bu site ile bağlantınızı keserseniz bu siteyi tekrar kullanabilmek için hesaplarınızı ve ağlarınızı tekrar bağlamanız gerekecek."}, "disconnectAllSnapsText": {"message": "Snap'ler"}, "disconnectMessage": {"message": "Bu işlem bu siteyle bağlantınızı kesecektir"}, "disconnectPrompt": {"message": "$1 bağlantısını kes"}, "disconnectThisAccount": {"message": "Bu hesabın bağlantısını kes"}, "disconnectedAllAccountsToast": {"message": "Tüm hesapların $1 ile bağlantısı kesildi", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 için $2 bağlantısı kesildi", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Keşfet"}, "discoverSnaps": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "<PERSON><PERSON><PERSON>"}, "dismissReminderDescriptionField": {"message": "Gizli Kurtarma İfadesi yedekleme hatırlatma mesajını yoksaymak için bunu açın. Paranızın kaybolmasını önlemek için Gizli Kurtarma İfadenizi yedeklemenizi kesinlikle tavsiye ederiz"}, "dismissReminderField": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadesi yedekleme hatırlatma uyarısını yoksay"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Artık herhangi bir hesapta \"Akıllı Hesaba Geç\" önerisini görmemek için bunu açın. Akıllı hesaplar daha hızlı işlemlerin, daha düşük ağ ücretlerinin ve bunlarda ödeme esnekliğinin kilidini açar."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "\"Akıllı Hesaba Geç\" önerisini yoksay"}, "displayNftMedia": {"message": "NFT medyasını göster"}, "displayNftMediaDescription": {"message": "NFT medyasının ve verilerin gösterilmesi IP adresinizin OpenSea veya diğer üçüncü taraflarla paylaşılmasına neden olur. Bu durum, saldırganların IP adresinizi Ethereum adresinizle ilişkilendirmesini sağlayabilir. NFT otomatik algılama bu ayara dayalıdır ve bu ayar kapatıldığında kullanılamaz olur."}, "doNotShare": {"message": "<PERSON><PERSON><PERSON> hiç kimseyle <PERSON>şmayın"}, "domain": {"message": "<PERSON>"}, "done": {"message": "<PERSON><PERSON>"}, "dontShowThisAgain": {"message": "<PERSON><PERSON><PERSON>"}, "downArrow": {"message": "aşağı ok"}, "downloadGoogleChrome": {"message": "Google Chrome'u indir"}, "downloadNow": {"message": "<PERSON><PERSON><PERSON>"}, "downloadStateLogs": {"message": "<PERSON><PERSON> Günlüklerini İndir"}, "dragAndDropBanner": {"message": "Yeniden sıralamak için ağları sürükleyebilirsiniz. "}, "dropped": {"message": "Bırakıldı"}, "duplicateContactTooltip": {"message": "Bu kişi adı mevcut bir hesap veya kişi ile çakışıyor"}, "duplicateContactWarning": {"message": "Yinelenen <PERSON>iler<PERSON>z var"}, "durationSuffixDay": {"message": "G", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "SA", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "DK", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "AY", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "SN", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "H", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "Y", "description": "Shortened form of 'year'"}, "earn": {"message": "Kazan"}, "edit": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "editANickname": {"message": "Takma adı düzenle"}, "editAccounts": {"message": "Hesapları düzenle"}, "editAddressNickname": {"message": "<PERSON><PERSON> takma adını düzenle"}, "editCancellationGasFeeModalTitle": {"message": "Gaz ücreti iptalini düzenle"}, "editContact": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "editGasFeeModalTitle": {"message": "Gaz ücretini düzenle"}, "editGasLimitOutOfBounds": {"message": "Gaz limiti en az $1 olmalıdır"}, "editGasLimitOutOfBoundsV2": {"message": "Gaz limiti en az $1, en fazla $2 olmalıdır", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "Gaz limiti, kullanmak istediğiniz maksimum gaz birimidir. Gaz birimleri \"Maks. öncelik ücreti\" ve \"Maks. ücretin\" bir çarpanıdır."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Maks<PERSON> baz <PERSON>, öncelik ücretinden daha düşük olamaz"}, "editGasMaxBaseFeeHigh": {"message": "Maks. baz ücret gerekenden daha yüksek"}, "editGasMaxBaseFeeLow": {"message": "Maks. baz ücret mevcut ağ koşulları için dü<PERSON>ük"}, "editGasMaxFeeHigh": {"message": "Maks<PERSON> ü<PERSON>t gerekenden daha yü<PERSON>"}, "editGasMaxFeeLow": {"message": "Maks. ücret ağ koşulları için çok düşük"}, "editGasMaxFeePriorityImbalance": {"message": "Maks. ücret maks. öncelik ücretinden daha düşük olamaz"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Maks. öncelik ücreti 0 GWEI üzerinde olmalıdır"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Öncelik ücreti 0'dan fazla olmalıdır."}, "editGasMaxPriorityFeeHigh": {"message": "Maks. öncelik ücreti gerekenden yüksek. Gerekenden fazla ödeme yapabilirsiniz."}, "editGasMaxPriorityFeeHighV2": {"message": "Öncelik ücreti gerekenden yüksek. Gerekenden fazla ödeme yapabilirsiniz"}, "editGasMaxPriorityFeeLow": {"message": "Maks. öncelik ücreti mevcut ağ koşulları için düşük"}, "editGasMaxPriorityFeeLowV2": {"message": "Öncelik ücreti mevcut ağ koşulları için düşük"}, "editGasPriceTooLow": {"message": "Gaz fiyatı 0'dan büyük olmalıdır"}, "editGasPriceTooltip": {"message": "<PERSON><PERSON> <PERSON><PERSON>, bir işlem gönderirken bir \"Gaz fiyatı\" alanı gerektirir. Gaz fiyatı, gaz birimi başına ödeyeceğiniz miktardır."}, "editGasSubTextFeeLabel": {"message": "<PERSON><PERSON><PERSON>:"}, "editGasTitle": {"message": "Önceliği düzenle"}, "editGasTooLow": {"message": "Bilinmeyen işlem süresi"}, "editInPortfolio": {"message": "Portfolio'<PERSON> d<PERSON>"}, "editNetworkLink": {"message": "orijinal ağı düzenle"}, "editNetworksTitle": {"message": "Ağları düzenle"}, "editNonceField": {"message": "Nonce'u düzenle"}, "editNonceMessage": {"message": "<PERSON>u gelişmiş bir <PERSON>, di<PERSON><PERSON><PERSON>."}, "editPermission": {"message": "İzni düzenle"}, "editPermissions": {"message": "İzinleri düzenle"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Hızlandırma gaz ücretini düzenle"}, "editSpendingCap": {"message": "Harcama üst limitini düzenle"}, "editSpendingCapAccountBalance": {"message": "<PERSON><PERSON><PERSON> b<PERSON>: $1 $2"}, "editSpendingCapDesc": {"message": "Sizin adınıza harcanması konusunda rahat hissedeceğiniz tutarı girin."}, "editSpendingCapError": {"message": "Harcama üst limiti $1 ondalık haneyi geçemez. Devam etmek için ondalık haneleri kaldırın."}, "editSpendingCapSpecialCharError": {"message": "<PERSON><PERSON><PERSON> rakam girin"}, "enableAutoDetect": {"message": " Otomatik algılamayı etkinleştir"}, "enableFromSettings": {"message": " <PERSON><PERSON><PERSON><PERSON>."}, "enableSnap": {"message": "Etkinleştir"}, "enableToken": {"message": "<PERSON><PERSON><PERSON> etkinleştir: $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Etkinleş<PERSON><PERSON>"}, "enabledNetworks": {"message": "Etkinleştirilen ağlar"}, "encryptionPublicKeyNotice": {"message": "$1 genel şifreleme anahtarınızı istiyor. Bunu onayladığınızda bu site sizin için şifrelenmiş mesajlar oluşturabilecektir.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Genel şifreleme anahtarı iste"}, "endpointReturnedDifferentChainId": {"message": "Girdiğiniz RPC URL farklı bir zincir kimliğine döndü ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "Gelişmiş token algılama şu anda $1 üzerinden kullanılabilir. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "<PERSON><PERSON><PERSON><PERSON>, ENS alanlarını doğrudan ta<PERSON>ı<PERSON>ınızın adres çubuğunda görmenizi sağlar. Şöyle çalışır:"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Bu özelliğin kullanılmasının IP adresinizi IPFS üçüncü taraf hizmetleriyle paylaştığını unutmayın."}, "ensDomainsSettingDescriptionPart1": {"message": "<PERSON><PERSON><PERSON><PERSON>, ENS adına bağlı kodu bulmak için Ethereum'un ENS sözleşmesi ile kontrol gerçekleştirir."}, "ensDomainsSettingDescriptionPart2": {"message": "Kod IPFS'ye bağlanıyorsa onunla ilişkili içeriği (genellikle bir web sitesi) görebilirsiniz."}, "ensDomainsSettingTitle": {"message": "ENS alanlarını adres çubuğunda göster"}, "ensUnknownError": {"message": "ENS arama başarısız oldu."}, "enterANameToIdentifyTheUrl": {"message": "URL adresini tanımlamak için bir ad girin"}, "enterChainId": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> girin"}, "enterMaxSpendLimit": {"message": "Ma<PERSON>. harcama limiti gir"}, "enterNetworkName": {"message": "<PERSON><PERSON> adını girin"}, "enterOptionalPassword": {"message": "İsteğe bağlı şifreyi girin"}, "enterPasswordContinue": {"message": "<PERSON><PERSON> et<PERSON> i<PERSON> girin"}, "enterRpcUrl": {"message": "RPC URL adresi girin"}, "enterSymbol": {"message": "Sembol girin"}, "enterTokenNameOrAddress": {"message": "Token adı girin veya adresi yapıştırın"}, "enterYourPassword": {"message": "Şifrenizi girin"}, "errorCode": {"message": "Kod: $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Güvenli zincir listesi alınırken hata oluştu, lütfen dikkatli bir şekilde devam edin."}, "errorMessage": {"message": "Mesaj: $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Kod: $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Destek b<PERSON>lü<PERSON><PERSON><PERSON>", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Ne olduğunu açıklayın", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Bilgileriniz gösterilemiyor. Endişelenmeyin, cüzdanınız ve paranız güvende.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "<PERSON>a mesajı", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Ne olduğunu açıklayın", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Hatayı nasıl tekrarlayabileceğimiz gibi ayrıntıların paylaşılması sorunu çözmemize yardımcı olacaktır.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Teşekkürler! En kısa sürede ele alacağız.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMask bir hata ile karşılaştı", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Yığın:", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "<PERSON>zel ağa bağlanırken hata oluştu."}, "errorWithSnap": {"message": "$1 ile hata oluştu", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "<PERSON><PERSON><PERSON>"}, "estimatedFeeTooltip": {"message": "Ağda işlemi gerçekleştirmek için ödenen tutar."}, "ethGasPriceFetchWarning": {"message": "Ana gaz tahmini hizmeti olarak sunulan yedek gaz fiyatı şu anda kullanılamıyor."}, "ethereumProviderAccess": {"message": "Ethereum sağlayıcısına $1 erişimi ver", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Ethereum genel adresi"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Hesabı Etherscan'de görü<PERSON>üle"}, "etherscanViewOn": {"message": "Etherscan'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "existingChainId": {"message": "Girdiğiniz bilgiler mevcut bir zincir kimliği ile ilişkilidir."}, "expandView": {"message": "Görünümü genişlet"}, "experimental": {"message": "Den<PERSON>sel"}, "exploreweb3": {"message": "Web3'ü keşfet"}, "exportYourData": {"message": "Verilerinizi dışa aktarın"}, "exportYourDataButton": {"message": "<PERSON><PERSON><PERSON>"}, "exportYourDataDescription": {"message": "Kişileriniz ve tercihleriniz gibi verileri dışa aktarabilirsiniz."}, "extendWalletWithSnaps": {"message": "web3 deneyiminizi kişiselleştirmek için topluluk tarafından oluşturulmuş Snapleri keşfedin", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "<PERSON><PERSON>"}, "externalExtension": {"message": "Harici uzantı"}, "externalNameSourcesSetting": {"message": "Önerilen takma adlar"}, "externalNameSourcesSettingDescription": {"message": "Etherscan, Infura ve Lens Protocol gibi üçüncü taraf kaynaklardan etkileşimde bulunduğunuz adeesler için önerilen takma adları alırız. Bu kaynaklar o adresleri ve sizin IP adresinizi görebilir. Hesap adresiniz üçüncü taraflarla paylaşılmaz."}, "failed": {"message": "Başarısız oldu"}, "failedToFetchChainId": {"message": "Zincir kimliği alınamadı. RPC URL adresiniz doğru mu?"}, "failover": {"message": "<PERSON><PERSON>"}, "failoverRpcUrl": {"message": "Yedek RPC URL Adresi"}, "failureMessage": {"message": "Bir şeyler ters gitti ve işlemi tamamlayamadık"}, "fast": {"message": "Hızlı"}, "feeDetails": {"message": "Ücret bilgileri"}, "fileImportFail": {"message": "Dosya içe aktarma çalışmıyor mu? Buraya tıklayın!", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "bu uzantıyı kaldırmalısın", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask, geliştiricilerin yeni kararsız API'leri denemeleri içindir. Geliştirici veya beta test kullanıcısı değilsen $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "Bu uzantının güvenliğini veya istikrarını garanti etmiyoruz. Flask tarafından sunulan yeni API'ler kimlik avı saldırılarına karşı güçlendirilmemiştir, yani Flask gerektiren herhangi bir site veya snap, varlıklarını çalmaya yönelik kötü niyetli bir girişim olabilir.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Tüm Flask API'leri deneyseldir. Önceden haber verilmeksizin değiştirilebilir veya kaldırılabilirler ya da kararlı MetaMask'e taşınmadan süresiz olarak Flask'ta kalabilirler. Bunları kendi sorumluluğunda kullan.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Flask kullanırken normal MetaMask uzantınızı devre dışı bıraktığınızdan emin olun.", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "Riskleri kabul ediyorum", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Token miktarı tam sayı olmalıdır"}, "followUsOnTwitter": {"message": "Bizi Twitter'da takip edin"}, "forbiddenIpfsGateway": {"message": "Yasaklı IPFS Ağ Geçidi: Lütfen bir CID ağ geçidi belirtin"}, "forgetDevice": {"message": "Bu cihazı unut"}, "forgotPassword": {"message": "Şifrenizi mi unuttunuz?"}, "form": {"message": "form"}, "from": {"message": "<PERSON><PERSON>"}, "fromAddress": {"message": "Kimden: $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "Token listelerinden: $1"}, "function": {"message": "İşlev: $1"}, "fundingMethod": {"message": "Para yatırma yöntemi"}, "gas": {"message": "Gaz"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Önerilen gaz ücretini düzenle"}, "gasDisplayDappWarning": {"message": "Bu gaz ücreti $1 tarafından önerilmiştir. Bu değerin başka bir değerle değiştirilmesi işleminizle ilgili bir soruna neden olabilir. Sorularınız olursa lütfen $1 ile iletişime geçin.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Gaz ücreti"}, "gasLimit": {"message": "Gaz limiti"}, "gasLimitRecommended": {"message": "Önerilen gaz limiti 1$ kadardır. Gaz limiti bundan daha düşükse başarısız olabilir."}, "gasLimitTooLow": {"message": "Gaz limiti en az 21000 olmalıdır"}, "gasLimitV2": {"message": "Gaz limiti"}, "gasOption": {"message": "Gaz seçeneği"}, "gasPriceExcessive": {"message": "Gaz ücretiniz gereksiz bir şekilde yüksek. Tutarı azaltmayı göz önünde bulundurun."}, "gasPriceFetchFailed": {"message": "Gaz fiyatı tahmini ağ hatası nedeniyle başarısız oldu."}, "gasTimingHoursShort": {"message": "$1 sa.", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "Yavaş"}, "gasTimingMinutesShort": {"message": "$1 dk", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 sn", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Kullanılan gaz"}, "general": {"message": "<PERSON><PERSON>"}, "generalCameraError": {"message": "Kameranıza erişemedik. Lütfen bir defa daha deneyin."}, "generalCameraErrorTitle": {"message": "<PERSON><PERSON> ters gitti...."}, "generalDescription": {"message": "Cihazlar genelindeki ayarları senkronize edin, ağ tercihlerini seçin ve token verilerini takip edin"}, "genericExplorerView": {"message": "Hesabı $1 üzerinde görüntüleyin"}, "goToSite": {"message": "Siteye git"}, "goerli": {"message": "Goerli test ağı"}, "gotIt": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "grantExactAccess": {"message": "<PERSON> er<PERSON><PERSON> ver"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "Donanım"}, "hardwareWalletConnected": {"message": "Donanım cüzdanı bağlandı"}, "hardwareWalletLegacyDescription": {"message": "(eski)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "$1 bağlı olduğundan emin olun ve Ethereum uygulamasını seçin."}, "hardwareWalletSubmissionWarningStep2": {"message": "$1 cihazınızda \"akıllı sözleşme verilerini\" ya da \"kör imzal<PERSON>\" özelliğini etkinleştirin."}, "hardwareWalletSubmissionWarningTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> tıklamadan önce:"}, "hardwareWalletSupportLinkConversion": {"message": "buraya tıklayın"}, "hardwareWallets": {"message": "Donanım cüzdanı bağla"}, "hardwareWalletsInfo": {"message": "Donanım cüzdanı entegrasyonları, IP adresinizi ve etkileşimde bulunduğunuz akıllı sözleşme adreslerini görebilen harici sunucular için API aramalarını kullanır."}, "hardwareWalletsMsg": {"message": "MetaMask ile kullanmak istediğiniz donanım cüzdanını seçin."}, "here": {"message": "burada", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "On altılı veri"}, "hiddenAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "hide": {"message": "<PERSON><PERSON><PERSON>"}, "hideAccount": {"message": "Hesabı gizle"}, "hideAdvancedDetails": {"message": "Gelişmiş bilgileri gizle"}, "hideSentitiveInfo": {"message": "<PERSON><PERSON><PERSON> bil<PERSON>i gizle"}, "hideTokenPrompt": {"message": "Tokeni gizle?"}, "hideTokenSymbol": {"message": "$1 ögesini gizle", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Bakiyesi olmayan <PERSON>i gizle"}, "high": {"message": "Agresif"}, "highGasSettingToolTipMessage": {"message": "Popüler NFT düşüşleri gibi şeyler nedeniyle ağ trafiğindeki dalgalanmaları kapsayacak şekilde $1 kullanın.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "highestCurrentBid": {"message": "Mevcut en yüksek teklif"}, "highestFloorPrice": {"message": "En yüksek taban fiyat"}, "history": {"message": "Geçmiş"}, "holdToRevealContent1": {"message": "G<PERSON>li Kurtarma İfadeniz: $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "cüzdanınıza ve paranıza tam erişim <PERSON>.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Hiç kimseyle paylaşmayın. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "MetaMask Destek bölümü bunu talep etmez", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "ancak dolandırıcılar talep edilebilir.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Özel Anahtarınız $1 sağlar", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "cüzdanınıza ve paranıza tam erişim.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "kilitli daireyi açığa çıkarmak için tut"}, "holdToRevealPrivateKey": {"message": "Özel Anahtarı açığa çıkarmak için tut"}, "holdToRevealPrivateKeyTitle": {"message": "<PERSON><PERSON> anahtarınızı güvende tutun"}, "holdToRevealSRP": {"message": "GKİ'yi açığa çıkarmak için tut"}, "holdToRevealSRPTitle": {"message": "GKİ'nizi güvende tutun"}, "holdToRevealUnlockedLabel": {"message": "kilidi açılmış daireyi açığa çıkarmak için tut"}, "honeypotDescription": {"message": "Bu token bir bal tuzağı riski taşıyor olabilir. Olası mali kayıpları önlemek için etkileşimde bulunmadan önce gerekli özeni göstermeniz tavsiye edilir."}, "honeypotTitle": {"message": "Bal Tuzağı"}, "howNetworkFeesWorkExplanation": {"message": "İşlemi gerçekleştirmek için tahmini ücret gereklidir. Maksimum ücret $1."}, "howQuotesWork": {"message": "Teklifler nasıl çalışır?"}, "howQuotesWorkExplanation": {"message": "Arama yaptığımız teklifler arasında en iyi getiriye sahip olan teklif budur. Bu, köprü ücretlerini ve $1 MetaMask ücreti eksi gaz ücretlerini içeren swap oranına dayanır. Gaz ücretleri, ağın ne kadar yoğun olduğuna ve işlemin ne kadar karmaşık olduğuna bağlıdır."}, "id": {"message": "<PERSON><PERSON>"}, "ignoreAll": {"message": "<PERSON><PERSON><PERSON>ü<PERSON><PERSON> yoksay"}, "ignoreTokenWarning": {"message": "Gizlediğiniz tokenler cüzdanınızda gösterilmez. <PERSON><PERSON>k, yine de onları bulup ekleyebilirsiniz."}, "imToken": {"message": "imToken"}, "import": {"message": "İçe Aktar", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Hesap içe aktarılırken hata oluştu."}, "importAccountErrorIsSRP": {"message": "Bir Gizli Kurtarma İfadesi (veya anımsatıcı ifade) girdiniz. Hesabı buraya aktarmak için 64 karakter uzunluğunda on altılı bir dizi olan özel bir anahtar girmelisiniz."}, "importAccountErrorNotAValidPrivateKey": {"message": "Bu geçerli bir özel anahtar değil. On altılık bir dizi girdiniz ancak bu dizi 64 karakter uzunluğunda olmalıdır."}, "importAccountErrorNotHexadecimal": {"message": "Bu geçerli bir özel anahtar değil. 64 karakter uzunluğunda on altılık bir dizi girmelisiniz."}, "importAccountJsonLoading1": {"message": "Bu JSON dosyasının birkaç dakika içinde içe aktarılmasını bekleyin ve MetaMask'i dondurun."}, "importAccountJsonLoading2": {"message": "<PERSON><PERSON><PERSON><PERSON>, bunu gelecekte daha hızlı hale get<PERSON>."}, "importAccountMsg": {"message": "İçe aktarılan hesaplar ilk olarak oluşturduğunuz MetaMask hesabı Gizli Kurtarma ifadenizle ilişkilendirilmez. İçe aktarılan hesaplar hakkında daha fazla bilgi edinin"}, "importNFT": {"message": "NFT'yi İçe Aktar"}, "importNFTAddressToolTip": {"message": "Örneğin OpenSea'de, NFT'nin Ayrıntılar altındaki sayfasında, 'Sözleşme Adresi' etiketli mavi bir köprülü değer vardır. Buna tıklarsan seni sözleşmenin Etherscan'daki adresine götürecektir; o sayfanın sol üst köşesinde 'Sözleşme' etiketli bir simge ve sağda uzun bir harf ve rakamlar dizisi olmalıdır. Bu, NFT'ni oluşturan sözleşmenin adresidir. Adresin sağındaki 'kopyala' simgesine tıkla, panonda olacaktır."}, "importNFTPage": {"message": "NFT sayfasını içe aktar"}, "importNFTTokenIdToolTip": {"message": "Hiçbir iki NFT kimliği birbiriyle aynı olmadığı için her NFT kimliği benzersiz bir tanımlayıcıdır. <PERSON><PERSON>, OpenSea'de bu sayı 'Detaylar' kısmının altında yer alır. Not alın veya panonuza kopyalayın."}, "importNWordSRP": {"message": "$1 sözcükten oluşan kurtarma ifadem var", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "<PERSON><PERSON>"}, "importSRPDescription": {"message": "12 veya 24 sözcükten oluşan gizli kurtarma ifadenizle mevcut bir cüzdanı içe aktarın."}, "importSRPNumberOfWordsError": {"message": "Gizli Kurtarma İfadeleri 12 veya 24 sözcükten oluşur"}, "importSRPWordError": {"message": "$1 sözcüğü yanlış veya yanlış yazılmış.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "$1 ve $2 sözcükleri yanlış veya yanlış yazılmış.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Gizli Kurtarma İfadesini İçe Aktar"}, "importSecretRecoveryPhraseUnknownError": {"message": "Bilinmeyen bir hata o<PERSON>."}, "importSelectedTokens": {"message": "Seçilen tokenleri içe aktar?"}, "importSelectedTokensDescription": {"message": "Sadece seçtiğiniz tokenler cüzdanınızda gösterilecek. Dilediğiniz zaman onları arayarak gizli tokenleri içe aktarabilirsiniz."}, "importTokenQuestion": {"message": "Tokeni içe aktar?"}, "importTokenWarning": {"message": "<PERSON><PERSON>, mevcut tokenlerin sahte sürümleri de dahil olmak üzere herhangi bir ada sahip bir token oluşturabilir. Riski tamamen size ait olacak şekikde token ekleyin ve işlem yapın!"}, "importTokensCamelCase": {"message": "Tokenleri içe aktar"}, "importTokensError": {"message": "Tokenleri içe aktaramadık. Lütfen daha sonra tekrar deneyin."}, "importWallet": {"message": "Cüzdanı içe aktar"}, "importWalletOrAccountHeader": {"message": "Bir cüzdanı veya hesabı içe aktar"}, "importWalletSuccess": {"message": "Gizli Kurtarma İfadesi $1 içe aktarıldı", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "$1 tokeni içe aktar", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "İçe Aktarıldı", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "<PERSON><PERSON><PERSON> kısmında"}, "included": {"message": "dahil"}, "includesXTransactions": {"message": "$1 işlem içerir"}, "infuraBlockedNotification": {"message": "MetaMask blokzinciri ana bilgisayarına bağlanamıyor. $1 olası nedenleri inceleyin.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "İlk işleminiz ağ tarafından onaylanmıştır. Geri gitmek için Tamam düğmesine tıklayın."}, "insightsFromSnap": {"message": "$1 kaynaklı içgörüler", "description": "$1 represents the name of the snap"}, "install": {"message": "<PERSON><PERSON><PERSON>"}, "installOrigin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "installRequest": {"message": "MetaM<PERSON>'e ekle"}, "installedOn": {"message": "$1 üzerinde yüklendi", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "Bakiye yetersiz."}, "insufficientFunds": {"message": "Para yetersiz."}, "insufficientFundsForGas": {"message": "Gaz için para yetersiz"}, "insufficientLockedLiquidityDescription": {"message": "Yeterince kilitlenmemiş veya kalıcı olarak kullanılmamış likidite eksikliği token'ı ani likidite çekimlerine karşı savunmasız bırakır ve potansiyel olarak piyasa istikrarsızlığına neden olur."}, "insufficientLockedLiquidityTitle": {"message": "<PERSON><PERSON><PERSON>ş Likidite"}, "insufficientTokens": {"message": "<PERSON><PERSON> yet<PERSON>."}, "interactWithSmartContract": {"message": "Akıllı sözleşme"}, "interactingWith": {"message": "Etkileşimde"}, "interactingWithTransactionDescription": {"message": "Bu sizin etkileşimde bulunduğunuz sözleşmedir. Bilgileri doğrulayarak kendinizi dolandırıcılardan koruyun."}, "interaction": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "invalidAddress": {"message": "<PERSON><PERSON> g<PERSON>iz"}, "invalidAddressRecipient": {"message": "Alıcı adresi geçersiz"}, "invalidAssetType": {"message": "Bu varlık bir NFT'dir ve NFT'ler sekmesi altında bulunan NFT'leri İçe Aktar sayfasına yeniden eklenmesi gerekir"}, "invalidChainIdTooBig": {"message": "Zincir kimliği geçersiz. Zincir kimliği çok büyük."}, "invalidCustomNetworkAlertContent1": {"message": "\"$1\" özel ağı için zincir kimliğinin yeniden girilmesi gerekiyor.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Sizi kötü niyetli ya da hatalı ağ sağlayıcılarından korumak amacıyla zincir kimlikleri artık tüm özel ağlar için gereklidir."}, "invalidCustomNetworkAlertContent3": {"message": "Ayarlar > <PERSON><PERSON> kısmına gidin ve zincir kimliğini girin. $1 alanında en popüler ağların zincir kimliklerini bulabilirsiniz.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "Özel ağ geçersiz"}, "invalidHexData": {"message": "On altılı veri geçersiz"}, "invalidHexNumber": {"message": "On altılı sayı geçersiz."}, "invalidHexNumberLeadingZeros": {"message": "On altılı sayı geçersiz. Başındaki tüm sıfırları kaldırın."}, "invalidIpfsGateway": {"message": "IPFS Ağ Geçidi geçersiz: Değer geçerli bir URL adresi olmalıdır"}, "invalidNumber": {"message": "Sayı geçersiz. Bir ondalık sayı ya da \"0x\"-<PERSON>n ekli on altılı sayı girin."}, "invalidNumberLeadingZeros": {"message": "Sayı geçersiz. Başındaki tüm sıfırları kaldırın."}, "invalidRPC": {"message": "RPC URL adresi geçersiz"}, "invalidSeedPhrase": {"message": "G<PERSON>li Kurtarma İfadesi geçersiz"}, "invalidSeedPhraseCaseSensitive": {"message": "Giriş geçersiz! Gizli Kurtarma İfadesi büyük/küçük harf duyarlıdır."}, "ipfsGateway": {"message": "IPFS ağ geçidi"}, "ipfsGatewayDescription": {"message": "<PERSON>aMask, IPFS'de depolanan NFT'lerinizin görüntülerini göstermek, tarayıcınızın adres çubuğuna girilen ENS adresleri ile ilgili bilgileri göstermek ve farklı tokenlerin simgelerini almak için üçüncü taraf hizmetleri kullanır. Siz bunları kullanırken IP adresiniz bu hizmetlerle paylaşılabilir."}, "ipfsToggleModalDescriptionOne": {"message": "IPFS'de depolanan NFT'lerinizin görüntülerini göstermek, tarayıcınızın adres çubuğuna girilen ENS adresleri ile ilgili bilgileri göstermek ve farklı tokenlerin simgelerini almak için üçüncü taraf hizmetleri kullanırız. Siz bunları kullanırken IP adresiniz bu hizmetlerle paylaşılabilir."}, "ipfsToggleModalDescriptionTwo": {"message": "Onayla seçeneği seçildiğinde IPFS çözünürlüğü açılır. Bunu Dilediğiniz zaman $1 alanında kapatabilirsiniz.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Ayarlar > Güvenlik ve gizlilik"}, "isSigningOrSubmitting": {"message": "Önceki bir iş<PERSON>in imzalanması veya gönderilmesi devam ediyor"}, "jazzAndBlockies": {"message": "Jazzicons ve Blockies, bir bakışta bir hesabı tanımlamana yardımcı olan iki farklı benzersiz simge stilidir."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "JSON Dosyası", "description": "format for importing an account"}, "keyringAccountName": {"message": "<PERSON><PERSON><PERSON> adı"}, "keyringAccountPublicAddress": {"message": "<PERSON><PERSON>"}, "keyringSnapRemovalResult1": {"message": "$1 $2kaldırıldı", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "<PERSON><PERSON><PERSON> ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Bu snap'i kaldırmak istediğinizi onaylamak için $1 yazın:", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "<PERSON>"}, "knownAddressRecipient": {"message": "Bilinen sözleşme adresi."}, "knownTokenWarning": {"message": "Bu eylem kimlik avı için kullanılabilecek şekilde cüzdanınızda zaten listelenmiş olan tokenleri düzenleyecektir. Sadece bu tokenlerin neyi temsil ettiğini değiştirmek istediğinizden eminseniz onaylayın. $1 hakkında daha fazla bilgi edinin"}, "l1Fee": {"message": "L1 ü<PERSON><PERSON>"}, "l1FeeTooltip": {"message": "L1 gaz ücreti"}, "l2Fee": {"message": "L2 ü<PERSON><PERSON>"}, "l2FeeTooltip": {"message": "L2 gaz ücreti"}, "lastConnected": {"message": "<PERSON> b<PERSON>"}, "lastSold": {"message": "<PERSON>"}, "lavaDomeCopyWarning": {"message": "Güvenliğiniz için bu metin şu anda seçilemez."}, "layer1Fees": {"message": "Katman 1 ücretleri"}, "layer2Fees": {"message": "Katman 2 ücretleri"}, "learnHow": {"message": "Na<PERSON><PERSON>l <PERSON>ğ<PERSON>u öğ<PERSON>"}, "learnMore": {"message": "daha fazla bilgi"}, "learnMoreAboutGas": {"message": "Gaz hakkında $1 istiyor musun?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "En iyi gizlilik uygulamaları hakkında daha fazla bilgi edinin."}, "learnMoreAboutSolanaAccounts": {"message": "Solana hesapları hakkında daha fazla bilgi edinin"}, "learnMoreKeystone": {"message": "<PERSON><PERSON>"}, "learnMoreUpperCase": {"message": "<PERSON><PERSON> fazla bilgi"}, "learnMoreUpperCaseWithDot": {"message": "Daha fazla bilgi edinin."}, "learnScamRisk": {"message": "dolandırıcılıklar ve güvenlik riskleri."}, "leaveMetaMask": {"message": "MetaMask'ten ayrıl?"}, "leaveMetaMaskDesc": {"message": "MetaMask'in dışında bir siteyi ziyaret etmek üzeresiniz. Devam etmeden önce URL adresini dikkatli bir şekilde kontrol edin."}, "ledgerAccountRestriction": {"message": "<PERSON>ni bir hesap ekleyebilmeniz için önce son hesab<PERSON><PERSON><PERSON><PERSON><PERSON> kullanmanız gerekir."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Cihazın<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>ı her türlü yazılımı kapatın ve ardından yenilemek için buraya tıklayın."}, "ledgerConnectionInstructionHeader": {"message": "<PERSON><PERSON><PERSON> düğ<PERSON>ine tıklamadan önce:"}, "ledgerConnectionInstructionStepFour": {"message": "Ledger cihazınızda \"akıllı sözleşme verilerini\" ya da \"kör imzalama\" özelliğini etkinleştirin."}, "ledgerConnectionInstructionStepThree": {"message": "Ledger'ınızın bağlı olduğundan emin olun ve Ethereum uygulamasını seçin."}, "ledgerDeviceOpenFailureMessage": {"message": "Ledger cihazı açılamadı. Ledger'ınız başka bir yazılıma bağlanmış olabilir. Lütfen Ledger Live'ı ya da Ledger cihazınızla bağlantılı diğer uygulamaları kapatıp tekrar bağlanmayı deneyin."}, "ledgerErrorConnectionIssue": {"message": "Ledger'ınızı tekrar ba<PERSON>ın, ETH uygulamasını açın ve tekrar deneyin."}, "ledgerErrorDevicedLocked": {"message": "Ledger'ın<PERSON>z kilitli. <PERSON><PERSON><PERSON>, ardı<PERSON><PERSON> tekrar den<PERSON>."}, "ledgerErrorEthAppNotOpen": {"message": "Sorunu çözmek için cihazınızda ETH uygulamasını açın ve tekrar deneyin."}, "ledgerErrorTransactionDataNotPadded": {"message": "Ethereum işleminin giriş verileri yeterli şekilde doldurulmamış."}, "ledgerLiveApp": {"message": "Ledger Live Uygulaması"}, "ledgerLocked": {"message": "Ledger cihazına bağlanılamıyor. Lütfen cihazınızın kilidinin açık ve Ethereum uygulamasının açık olduğundan emin olun."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Yeni bir cihaz bağlamak için önceki cihazın bağlantısını kesin."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Aynı anda yalnızca bir Ledger bağlayabilirsiniz"}, "ledgerTimeout": {"message": "Ledger Live'ın yanıt vermesi çok uzun sürdü ya da bağlantı zaman aşımına uğradı. Ledger Live uygulamasının açık olduğundan ve cihazınızın kilidinin açık olduğundan emin olun."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Ledger cihazı bağlı değil. <PERSON><PERSON>t Defterinizi bağlamak istiyorsanız lütfen \"Devam Et\" düğmesine yeniden tıklayın ve HID bağlantısını onaylayın", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "seviye oku"}, "lightTheme": {"message": "Aydınlık"}, "likeToImportToken": {"message": "Bu tokeni içe aktarmak ister misiniz?"}, "likeToImportTokens": {"message": "Bu tokenleri içe aktarmak ister misiniz?"}, "lineaGoerli": {"message": "Linea Goerli test ağı"}, "lineaMainnet": {"message": "Linea Ana Ağı"}, "lineaSepolia": {"message": "Linea Sepolia test ağı"}, "link": {"message": "Bağlantı"}, "linkCentralizedExchanges": {"message": "MetaMask'e ücretsiz kripto transferi yapmak için Coinbase veya Binance hesaplarınızı bağlayın."}, "links": {"message": "Bağlantılar"}, "loadMore": {"message": "<PERSON>ha fazlasını yükle"}, "loading": {"message": "Yükleniyor..."}, "loadingScreenSnapMessage": {"message": "Lütfen işlemi Snap üzerinde tamamlayın."}, "loadingTokenList": {"message": "Token listesi yü<PERSON>r"}, "localhost": {"message": "<PERSON><PERSON> 8545"}, "lock": {"message": "<PERSON><PERSON><PERSON>"}, "lockMetaMask": {"message": "MetaMask'i kilitle"}, "lockTimeInvalid": {"message": "Kilit süresi 0 ile 10080 arasında olmalıdır"}, "logo": {"message": "$1 logosu", "description": "$1 is the name of the ticker"}, "low": {"message": "Düşük"}, "lowEstimatedReturnTooltipMessage": {"message": "Ücretlerdeki başlangıç tutarınızdan %$1 daha fazla ödeyeceksiniz. Aldığınız tutarı ve ağ ücretlerini kontrol edin."}, "lowEstimatedReturnTooltipTitle": {"message": "Yüksek mali<PERSON>t"}, "lowGasSettingToolTipMessage": {"message": "Daha ucuz bir fiyat için beklemek için $1 kullanın. Fiyatlar bir şekilde öngörülemez oldukları için süre tahminleri çok daha az kesindir.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "düşük"}, "mainnet": {"message": "Ethereum Ana Ağı"}, "mainnetToken": {"message": "<PERSON><PERSON> adres, bilinen bir Ethereum Ana Ağı token adresiyle eşleşiyor. Eklemeye çalıştığınız token için sözleşme adresini ve ağı tekrar kontrol edin."}, "makeAnotherSwap": {"message": "Yeni bir swap oluştur"}, "makeSureNoOneWatching": {"message": "<PERSON><PERSON> kimsenin bakmadığından emin olun", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "Varsayılan gizlilik ayarlarını yönet"}, "manageInstitutionalWallets": {"message": "Institutional Cüzdanlarını Yönet"}, "manageInstitutionalWalletsDescription": {"message": "Institutional cüzdanlarını etkinleştirmek için bunu açın."}, "manageNetworksMenuHeading": {"message": "Ağları yönet"}, "managePermissions": {"message": "İzinleri yönet"}, "marketCap": {"message": "<PERSON><PERSON><PERSON>"}, "marketDetails": {"message": "<PERSON><PERSON><PERSON>i"}, "max": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "maxBaseFee": {"message": "Maks. baz ücret"}, "maxFee": {"message": "Maks. <PERSON>"}, "maxFeeTooltip": {"message": "İşlemi ödemek için sunulan maksimum ücret."}, "maxPriorityFee": {"message": "Maks. öncelik ücreti"}, "medium": {"message": "<PERSON><PERSON><PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Mevcut piyasa fiyatında hızlı işleme almak için $1 kullanın.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "not"}, "message": {"message": "<PERSON><PERSON>"}, "metaMaskConnectStatusParagraphOne": {"message": "Artık MetaMask'te hesap bağlantılarınızın üzerinde daha fazla kontrole sahipsiniz."}, "metaMaskConnectStatusParagraphThree": {"message": "Bağlı hesaplarınızı yönetmek için tıklayın."}, "metaMaskConnectStatusParagraphTwo": {"message": "Bağlantı durumu düğmesi ziyaret ettiğiniz web sitesinin şu anda seçilen hesabınıza bağlı olup olmadığını gösterir."}, "metaMetricsIdNotAvailableError": {"message": "Hiçbir zaman MetaMetrics'e dahil olmadığınız için burada silinecek veri yok."}, "metadataModalSourceTooltip": {"message": "$1 npm'de barındırılmaktadır ve $2 bu Snap'in eş<PERSON>z tanımlayıcısıdır.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "Cüzdan bildirimleri şu anda aktif <PERSON>."}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swap İşlemleri bakımda. Lütfen daha sonra tekrar kontrol edin."}, "metamaskVersion": {"message": "MetaMask Sürümü"}, "methodData": {"message": "Yöntem"}, "methodDataTransactionDesc": {"message": "Şifresi çözülmüş giriş verilerine göre gerçekleştirilen işlev."}, "methodNotSupported": {"message": "Bu hesap ile desteklenmez."}, "metrics": {"message": "<PERSON><PERSON><PERSON>"}, "millionAbbreviation": {"message": "MN", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "ağ bilgilerini doğrula", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "İlerlemeden önce şunu öneririz: $1.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Kayıtlarımıza gö<PERSON>, ağ adı bu zincir kimliği ile doğru bir şekilde uyumlu olmayabilir."}, "mismatchedNetworkSymbol": {"message": "Sunulan para birimi sembolü bu zincir kimliği için be<PERSON>iğimiz sembolle uyumlu değil."}, "mismatchedRpcChainId": {"message": "Özel ağ tarafından geri dönen Zincir kimliği gönderilen zincir kimliği ile eşleşmiyor."}, "mismatchedRpcUrl": {"message": "Kay<PERSON>tlar<PERSON>m<PERSON><PERSON> gö<PERSON>, sunulan RPC URL adresi değeri bu zincir kimliğinin bilinen bir sağlayıcısı ile uyumlu değil."}, "missingSetting": {"message": "Bir ayarı bulamıyor musun?"}, "missingSettingRequest": {"message": "<PERSON><PERSON><PERSON> et"}, "more": {"message": "daha fazla"}, "moreAccounts": {"message": "+ $1 hesap daha", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1 ağ daha", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "<PERSON><PERSON> fazla te<PERSON>"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Bu ağı MetaMask'e ekliyor ve bu siteye ağı kullanma izni veriyorsunuz."}, "multichainQuoteCardBridgingLabel": {"message": "Köprü"}, "multichainQuoteCardQuoteLabel": {"message": "<PERSON><PERSON><PERSON>"}, "multichainQuoteCardTimeLabel": {"message": "Zaman"}, "multipleSnapConnectionWarning": {"message": "$1 $2 Snap kullanmak istiyor", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "En az bir token seçilmeli."}, "name": {"message": "Ad<PERSON>"}, "nameAddressLabel": {"message": "<PERSON><PERSON>", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "İsim zaten kullanılıyor"}, "nameInstructionsNew": {"message": "Bu adresi biliyorsanız gelecekte tanıyabilmek için ona bir takma ad verin.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "Bu adresin var<PERSON>ılan bir takma adı var ancak onu düzenleyebilir veya diğer önerileri keşfedebilirsiniz.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Daha önce bu adres için bir takma ad eklediniz. Bu takma adı düzenleyebilir veya önerilen diğer takma adları görüntüleyebilirsiniz.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Takma Ad", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Belki: $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Bilinmeyen adres", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "<PERSON><PERSON><PERSON> adres", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "<PERSON><PERSON>", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "$1 tarafından önerilir", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Ethereum İsimlendirme Hizmeti (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "Bir takma ad seçin...", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 sizden <PERSON>un i<PERSON>in onay istiyor:", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Ağ bilgilerini düzenle"}, "nativeTokenScamWarningDescription": {"message": "<PERSON>rel token sembolü, zincir kimliği ile ilişkili ağın yerel token'inin beklenen sembolü ile uyumlu değil. Sizin girdiğiniz $1 iken beklenen sembol $2 idi. Lütfen doğru zincire bağlandığınızdan emin olun.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "baş<PERSON> bir şey", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Beklenmedik Yerli Token Sembolü", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Yardıma mı ihtiyacınız var? $1 bölümüne ulaşın", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "<PERSON><PERSON>"}, "needHelpLinkText": {"message": "MetaMask destek"}, "needHelpSubmitTicket": {"message": "<PERSON><PERSON><PERSON>"}, "needImportFile": {"message": "İçe aktarılacak dosyayı seçmelisiniz.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Negatif tutarlarda ETH gönderilemez."}, "negativeOrZeroAmountToken": {"message": "Eksi veya sıfır tutarlarda varlık gönderilemez."}, "network": {"message": "Ağ:"}, "networkChanged": {"message": "<PERSON><PERSON>"}, "networkChangedMessage": {"message": "Şu anda $1 üzerinde işlem yapıyorsunuz.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "Ağ bilgileri"}, "networkFee": {"message": "<PERSON><PERSON>"}, "networkIsBusy": {"message": "Ağ meşgul. Gaz fiyatları yüksektir ve tahminler daha az doğrudur."}, "networkMenu": {"message": "<PERSON><PERSON>"}, "networkMenuHeading": {"message": "<PERSON><PERSON>"}, "networkName": {"message": "<PERSON><PERSON> adı"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Temel"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "Bu ağ ile ilişkilendirilmiş ad."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "OP Ana Ağı"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Test ağı"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "<PERSON><PERSON>"}, "networkPermissionToast": {"message": "<PERSON><PERSON>i güncellendi"}, "networkProvider": {"message": "<PERSON>ğ sağlayıcısı"}, "networkStatus": {"message": "<PERSON><PERSON>"}, "networkStatusBaseFeeTooltip": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> belirlenen ve her 13-14 saniyede bir değişen baz ücret. $1 ve $2 seçeneklerimiz ani artışlar içindir.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Öncelik ücretleri (başka bir deyişle \"madenci bahşişi\") aralıkları. Bu ücretler doğrudan madencilere gider ve işleminizin öncelikli olarak gerçekleştirilmesini teşvik eder."}, "networkStatusStabilityFeeTooltip": {"message": "Son 72 saate göre gaz ücretleri $1.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "$1 ağına bağlanamıyoruz", "description": "$1 represents the network name"}, "networkURL": {"message": "Ağ URL adresi"}, "networkURLDefinition": {"message": "Bu ağa erişim sağlamak için kullanılan URL adresi."}, "networkUrlErrorWarning": {"message": "Saldırganlar bazen site adresinde küçük değişiklikler yaparak siteleri taklit edebilir. Devam etmeden önce planladığınız site ile etkileşim kurduğunuzdan emin olun. Punycode sürümü: $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "<PERSON><PERSON><PERSON>"}, "networksSmallCase": {"message": "ağ"}, "nevermind": {"message": "Boşver"}, "new": {"message": "Yeni!"}, "newAccount": {"message": "<PERSON><PERSON>"}, "newAccountNumberName": {"message": "Hesap $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "<PERSON><PERSON> k<PERSON>i"}, "newContract": {"message": "<PERSON><PERSON>"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Ayarlar > Güvenlik ve gizlilik"}, "newNFTDetectedInImportNFTsMsg": {"message": "Opensea'yi kullanmak için NFT'lerinize bakın, $1 üzerinde \"NFT Medyasını göster\" seçeneğini açın.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "MetaMask'ın otomatik olarak cüzdanınızdaki NFT'leri algılayıp göstermesine izin verin."}, "newNFTsAutodetected": {"message": "NFT otomatik algılama"}, "newNetworkAdded": {"message": "\"$1\" başarılı bir şekilde eklendi!"}, "newNetworkEdited": {"message": "“$1” başarılı bir şekilde düzenlendi!"}, "newNftAddedMessage": {"message": "NFT başarılı bir şekilde eklendi!"}, "newPassword": {"message": "<PERSON><PERSON> (min 8 karakter)"}, "newPrivacyPolicyActionButton": {"message": "Daha fazlasını oku"}, "newPrivacyPolicyTitle": {"message": "Gizlilik politikamızı güncelledik"}, "newRpcUrl": {"message": "Yeni RPC URL adresi"}, "newTokensImportedMessage": {"message": "$1 tokeni başarılı bir şekilde içe aktardın.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Token içe aktarıldı"}, "next": {"message": "<PERSON><PERSON><PERSON>"}, "nftAddFailedMessage": {"message": "Sahiplik bilgileri eşleşmediği için NFT eklenemiyor. Doğru bilgileri girdiğinizden emin olun."}, "nftAddressError": {"message": "Bu token bir NFT'dir. $1 üzerinde ekleyin", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "NFT zaten eklenmiş."}, "nftAutoDetectionEnabled": {"message": "NFT otomatik algılama etkinleştirildi"}, "nftDisclaimer": {"message": "Sorumluluğun Reddi: MetaMask medya dosyasını kaynak url adresinden çeker. Bu url adresi bazen NFT'nin mint edildiği pazar yeri tarafından değiştirilir."}, "nftOptions": {"message": "NFT Seçenekleri"}, "nftTokenIdPlaceholder": {"message": "Token kim<PERSON>ğini gir"}, "nftWarningContent": {"message": "Gelecekte sahip olabilecekleriniz de dahil olmak üzere $1 eriş<PERSON>i veri<PERSON>uz, <PERSON><PERSON><PERSON>, siz bu onayı iptal edene kadar size sormadan bu NFT'leri dilediği zaman cüzdanınızdan transfer edebilir. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "tüm $1 NFT'leriniz", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "<PERSON><PERSON><PERSON> il<PERSON>ley<PERSON>."}, "nfts": {"message": "NFT'ler"}, "nftsPreviouslyOwned": {"message": "Önceden Sahip Olunan"}, "nickname": {"message": "Tak<PERSON> ad"}, "noAccountsFound": {"message": "Belirtilen arama sorgusu için hesap bulu<PERSON>ı"}, "noActivity": {"message": "<PERSON><PERSON><PERSON><PERSON> bir aktivite yok"}, "noConnectedAccountTitle": {"message": "MetaMask bu siteye bağlı değil"}, "noConnectionDescription": {"message": "Bir siteye bağlanmak için \"ba<PERSON><PERSON>\" d<PERSON><PERSON><PERSON><PERSON> bularak seçin. MetaMask'in sadece web3 üzerindeki sitelere bağlanabildiğini unutmayın"}, "noConversionRateAvailable": {"message": "Dönüşüm oranı mevcut değil"}, "noDeFiPositions": {"message": "Henüz pozisyon yok"}, "noDomainResolution": {"message": "<PERSON> adı için <PERSON>ı<PERSON>."}, "noHardwareWalletOrSnapsSupport": {"message": "Geçerli tarayıcı sürümünüzle snap'ler ve çoğu donanım cüzdanı çalışmayacak."}, "noNFTs": {"message": "Henüz NFT yok"}, "noNetworksFound": {"message": "Belirtilen arama sorgusu için herhangi bir ağ bulunamadı"}, "noOptionsAvailableMessage": {"message": "Bu işlem yolu şu anda mevcut değil. <PERSON><PERSON><PERSON><PERSON>, ağı veya tokeni değiştirmeyi deneyin ve en iyi seçeneği bulalım."}, "noSnaps": {"message": "Yüklü snapiniz yok."}, "noThanks": {"message": "Hayır, istemiyorum"}, "noTransactions": {"message": "Herhangi bir işleminiz yok"}, "noWebcamFound": {"message": "Bilgisayarınızın web kamerası bulunamadı. Lütfen tekrar deneyin."}, "noWebcamFoundTitle": {"message": "Web kamerası bulunamadı"}, "nonContractAddressAlertDesc": {"message": "Sözleşme olmayan bir adrese çağrı verileri gönderiyorsunuz. Bu durum para kaybetmenize neden olabilir. Devam etmeden önce doğru adres ve ağı kullandığınızdan emin olun."}, "nonContractAddressAlertTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hata"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "Yok"}, "notBusy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notCurrentAccount": {"message": "Bu hesap doğru mu? Cüzdanınızdaki mevcut seçili hesaptan farklı"}, "notEnoughBalance": {"message": "Bakiye yet<PERSON>iz"}, "notEnoughGas": {"message": "Yeterli gaz yok"}, "notNow": {"message": "<PERSON><PERSON><PERSON>"}, "notificationDetail": {"message": "Ayrıntılar"}, "notificationDetailBaseFee": {"message": "<PERSON><PERSON> (GWEI)"}, "notificationDetailGasLimit": {"message": "Gaz limiti (birim)"}, "notificationDetailGasUsed": {"message": "Kullanılan gaz (birim)"}, "notificationDetailMaxFee": {"message": "Gaz başına maks. ücret"}, "notificationDetailNetwork": {"message": "Ağ"}, "notificationDetailNetworkFee": {"message": "<PERSON><PERSON>"}, "notificationDetailPriorityFee": {"message": "<PERSON><PERSON><PERSON>ti (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "BlockExplorer üzerinde kontrol et"}, "notificationItemCollection": {"message": "Tahsilat"}, "notificationItemConfirmed": {"message": "Onaylandı"}, "notificationItemError": {"message": "<PERSON><PERSON> anda ücretler alınamıyor"}, "notificationItemFrom": {"message": "<PERSON><PERSON>"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Para Çekmeye Hazır"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "<PERSON><PERSON> anda unstake $1 çekebilirsiniz"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "$1 unstake etme talebiniz gönderildi"}, "notificationItemNFTReceivedFrom": {"message": "NFT gönderen"}, "notificationItemNFTSentTo": {"message": "NFT alıcısı"}, "notificationItemNetwork": {"message": "Ağ"}, "notificationItemRate": {"message": "<PERSON><PERSON> (<PERSON><PERSON>t da<PERSON>)"}, "notificationItemReceived": {"message": "Alınan"}, "notificationItemReceivedFrom": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemSent": {"message": "Alıcı"}, "notificationItemSentTo": {"message": "Stake tama<PERSON>landı"}, "notificationItemStakeCompleted": {"message": "Stake edildi"}, "notificationItemStaked": {"message": "Stake edildi"}, "notificationItemStakingProvider": {"message": "Stake Sağlayıcısı"}, "notificationItemStatus": {"message": "Durum"}, "notificationItemSwapped": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemSwappedFor": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemTo": {"message": "<PERSON><PERSON>"}, "notificationItemTransactionId": {"message": "İşlem Numarası"}, "notificationItemUnStakeCompleted": {"message": "UnStake tamamlandı"}, "notificationItemUnStaked": {"message": "Unstake edildi"}, "notificationItemUnStakingRequested": {"message": "Unstake talep edildi"}, "notificationTransactionFailedMessage": {"message": "$1 işlemi başarısız oldu! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Başarısız işlem", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "$1 işlemi onaylandı!", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Onaylanan işlem", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "$1 üzerinde görüntüle", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notificationsFeatureToggle": {"message": "Cüzdan Bildirimleri Etkinleştir", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "<PERSON><PERSON>, para veya nft gönder/al gibi cüzdan bildirimlerini veya özellik uyarılarını etkinleştirir.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "Tümünü okundu olarak işaretle"}, "notificationsPageEmptyTitle": {"message": "Burada gösterilecek bir şey yok"}, "notificationsPageErrorContent": {"message": "Lütfen bu sayfayı tekrar ziyaret etmeyi deneyin."}, "notificationsPageErrorTitle": {"message": "<PERSON>ir hata o<PERSON>"}, "notificationsPageNoNotificationsContent": {"message": "<PERSON><PERSON><PERSON><PERSON> herhangi bir bildirim almadınız."}, "notificationsSettingsBoxError": {"message": "Bir şeyler ters gitti. Lütfen tekrar deneyin."}, "notificationsSettingsPageAllowNotifications": {"message": "Bildirimler ile cüzdanınızda neler olduğundan haberdar olun. Bildirimleri kullanmak için cihazlarınızdaki bazı ayarları senkronize etmek amacıyla bir profil kullanıyoruz. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Bu özelliği kullanırken gizliliğinizi nasıl koruduğumuzu öğrenin."}, "numberOfNewTokensDetectedPlural": {"message": "Bu hesapta $1 yeni token bulundu", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "Bu hesapta 1 yeni token bulundu"}, "numberOfTokens": {"message": "Token sayısı"}, "ofTextNofM": {"message": "/"}, "off": {"message": "<PERSON><PERSON><PERSON>"}, "offlineForMaintenance": {"message": "Bakım iç<PERSON>"}, "ok": {"message": "<PERSON><PERSON>"}, "on": {"message": "Açık"}, "onboardedMetametricsAccept": {"message": "Kabul ediyorum"}, "onboardedMetametricsDisagree": {"message": "Hayır, istemiyorum"}, "onboardedMetametricsKey1": {"message": "En yeni gel<PERSON>er"}, "onboardedMetametricsKey2": {"message": "<PERSON><PERSON><PERSON>n ö<PERSON>"}, "onboardedMetametricsKey3": {"message": "İlgili diğer promosyon materyalleri"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "$1 bağlantısına ek olarak pazarlama iletişimleri ile nasıl etkileşimde bulunduğunuzu anlamak amacıyla verileri kullanmak istiyoruz.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "<PERSON><PERSON>, si<PERSON>le neleri paylaşacağımızı kişiselleştirebilmemize yardımcı olur, örneğin:"}, "onboardedMetametricsParagraph3": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sunduğunuz verileri hiçbir zaman satmayız ve dilediğiniz zaman tercihinizi değiştirebilirsiniz."}, "onboardedMetametricsTitle": {"message": "Deneyiminizi iyileştirmemize yardımcı olun"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "IPFS ağ geçidi üçüncü tarafların barındırdığı veriler için erişim ve görüntülemeyi mümkün kılar. Özel bir IPFS ağ geçidi ekleyebilir veya varsayılanı kullanmaya devam edebilirsiniz."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Lütfen geçerli bir URL adresi girin"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Özel IPFD Ağ Geçidi ekle"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "IPFS ağ geçidi URL adresi geçerli"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Varsayılan ayarlarınızı ve yapılandırmalarınızı kullandığımızda, Ethereum verilerine mümkün olan en güvenilir ve gizli erişimi sunmak amacıyla varsayılan uzak yordam çağrısı (RPC) sağlayıcısı olarak Infura'yı kullanırız. Sınırlı durumlarda, kullanıcılarımıza en iyi deneyimi sağlamak amacıyla başka RPC sağlayıcıları kullanabiliriz. Kendi RPC'nizi seçebilirsiniz ancak her RPC'nin işlem yapmak için IP adresinize ve Ethereum cüzdanınıza ulaşacağını unutmayın. Infura'nın, EVM hesapları için verileri nasıl kullandığı hakkında bilgi edinmek için $1 bölümümüzü; Solana hesapları için ise $2 bölümümüzü okuyun."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "buraya tıklayın"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Ağınızı seçin"}, "onboardingCreateWallet": {"message": "<PERSON>ni bir cüzdan oluştur"}, "onboardingImportWallet": {"message": "Mevcut bir cüzdanı içe aktar"}, "onboardingMetametricsAgree": {"message": "Kabul ediyorum"}, "onboardingMetametricsDescription": {"message": "MetaMask'i iyileştirmek için temel kullanım ve tanılama verilerini toplamak istiyoruz. Burada sunduğunuz verileri asla satmadığımızı bilmenizi isteriz."}, "onboardingMetametricsInfuraTerms": {"message": "Bu verileri başka amaçlar için kullanmaya karar vermemiz durumunda sizi bilgilendireceğiz. Daha fazla bilgi için $1 bölümümüzü inceleyebilirsiniz. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dilediğiniz zaman ayarlar kısmına giderek vazgeçebilirsiniz.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Gizlilik Politikası"}, "onboardingMetametricsNeverCollect": {"message": "$1 uygulama üzerindeki tıklamalar ve görüntülemeler depolanır ancak diğer bilgiler (genel adresiniz gibi) depolanmaz.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Özel:"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 genel bir konumu (ülkeniz veya bölgeniz gibi) algılamak için geçici olarak IP adresinizi kullanırız ancak bu bilgi asla saklanmaz.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Genel:"}, "onboardingMetametricsNeverSellData": {"message": "$1 kullanım verilerinizi paylaşmak veya silmek isteyip istemediğinize ayarlar kısmından dilediğiniz zaman siz karar verirsiniz.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "İsteğe bağlı:"}, "onboardingMetametricsTitle": {"message": "MetaMask'i iyileştirmemize yardımcı olun"}, "onboardingMetametricsUseDataCheckbox": {"message": "<PERSON><PERSON> veril<PERSON>, pazarlama iletişimlerimizle nasıl etkileşimde bulunduğunuzu öğrenmek için kullanacağız. İlgili haberleri (ürün özellikleri gibi) paylaşabiliriz."}, "onboardingPinExtensionDescription": {"message": "İşlem onaylarını erişilebilir ve kolay görüntülenebilir hale getirmek için MetaMask'i tarayıcınıza sabitleyin."}, "onboardingPinExtensionDescription2": {"message": "Uzantıya tıklayarak MetaMask'i açabilir ve 1 tık ile cüzdanınıza erişim sağlayabilirsiniz."}, "onboardingPinExtensionDescription3": {"message": "<PERSON><PERSON> er<PERSON><PERSON><PERSON> sa<PERSON> için tarayıcı uzantısı simgesine tıklayın", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "MetaMask kurulumunuz tamamlandı!"}, "onekey": {"message": "OneKey"}, "only": {"message": "yalnızca"}, "onlyConnectTrust": {"message": "Sadece güvendiğiniz sitelere bağlayın. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Ledger'ınızı bağlamak için tam ekrana gidin.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Blok gezgininde aç"}, "optional": {"message": "İsteğe bağlı"}, "options": {"message": "Seçenekler"}, "origin": {"message": "Köken"}, "originChanged": {"message": "Site değişti"}, "originChangedMessage": {"message": "Şu anda $1 alanından bir talebi inceliyorsunuz.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Sistem"}, "other": {"message": "<PERSON><PERSON><PERSON>"}, "otherSnaps": {"message": "<PERSON><PERSON><PERSON> snapler", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "outdatedBrowserNotification": {"message": "Tarayıcınız güncel değil. Tarayıcınızı güncellemezseniz MetaMask'ten güvenlik yamalarını ve yeni özellikleri alamayacaksınız."}, "overrideContentSecurityPolicyHeader": {"message": "İçerik-Güvenlik-Politika başlığını geçersiz kıl"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Firefox'ta merkeziyetsiz bir uygulamanın İçerik-Güvenlik-Politika başlığının uzantının düzgün bir şekilde yüklenmesini önleyebildiği bilinen bir sorunun anlık çözümüdür. Belirli web sayfası uyumluluğu için gerekli olmadığı sürece bu seçeneğin devre dışı bırakılması önerilmez."}, "padlock": {"message": "<PERSON><PERSON>"}, "participateInMetaMetrics": {"message": "MetaMetrics'e Katıl"}, "participateInMetaMetricsDescription": {"message": "MetaMask'i daha iyi hale getirmemize yardımcı olmak için MetaMetrics'e katılın"}, "password": {"message": "Şifre"}, "passwordNotLongEnough": {"message": "Şifre yeterince uzun değil"}, "passwordStrength": {"message": "Şifre güvenlik düzeyi: $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Güçlü bir şif<PERSON>, cihazının çalınması veya güvenliğinin ihlal edilmesi durumunda cüzdanının güvenliğini artırabilir."}, "passwordTermsWarning": {"message": "MetaMask'in benim i<PERSON>in bu şifreyi kurtaramayacağını anlıyorum. $1"}, "passwordsDontMatch": {"message": "Şifreler uyumlu değil"}, "pastePrivateKey": {"message": "Özel anahtar dizinizi buraya yapıştırın:", "description": "For importing an account from a private key"}, "pending": {"message": "<PERSON><PERSON><PERSON>"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "Ağ güncellenirse bu siteden $1 bekleyen işlem iptal edilecektir.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "<PERSON><PERSON>ştirilirse bu siteden $1 bekleyen işlem iptal edilecektir.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "Bu işlem bir önceki işlem tamamlanana kadar gerçekleşmeyecek. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Bir işlemin nasıl iptal edileceğini veya hızlandırılacağını öğrenin.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "İzin ayrıntıları"}, "permissionFor": {"message": "<PERSON><PERSON><PERSON>:"}, "permissionFrom": {"message": "Şuradan izin:"}, "permissionRequested": {"message": "Şimdi talep edildi"}, "permissionRequestedForAccounts": {"message": "Şimdi $1 için talep edildi", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "<PERSON><PERSON> g<PERSON><PERSON><PERSON><PERSON>e iptal edildi"}, "permissionRevokedForAccounts": {"message": "$1 için bu güncellemede iptal edildi", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "$1 alanına ba<PERSON>ın.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "İnternete eriş<PERSON>.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "$1 adlı snap'in internete erişim sağlamasına izin verin. Üçüncü taraf sunucuları ile hem veri göndermek hem de veri almak için kullanılabilir.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "$1 snape ba<PERSON>lan.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Web sitesinin veya snapin $1 ile etkileşimde bulunmasına izin verin.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Hesap varlıklarını MetaMask'te göster.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "$1 adlı snap'in MetaMask müşterisine varlık bilgilerini sunmasına izin ver. Varlıklar zincir üstü veya zincir dışı olabilir.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Periyodik eylemleri planla ve gerçekleştir.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "$1 adlı snap'in sabit saat, tarih veya aralıklarda periyodik olarak çalışan eylemleri gerçekleştirmesine izin verin. Zamana duyarlı etkileşim ya da bildirimleri tetiklemek için kullanılabilir.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "MetaMask'te iletişim kutusu pencer<PERSON> gö<PERSON>.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "$1 adlı snap'in bir eylemi onaylamak ya da reddetmek için özel metin, giriş alanı ve düğmelerle MetaMask açılır pencerelerini göstermesine izin verin.\nBir snap için ör. uyarı, onay ve talep edilen akış oluşturmak için kullanılabilir.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, aktiviteye bakın ve işlemleri başlatın", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Ethereum sağlayıcısına ulaş.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Blok zincirinden veri okuyabilmesi ve mesaj ile işlemleri önerebilmesi için $1 adlı snap'in doğrudan MetaMask ile iletişim kurmasına izin verin.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "$1 için eş<PERSON>z rastgele anahtarları türetin.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "$1 adlı snap'in, paylaşmadan, $1 adlı snap'e özel rastgele anahtarlar türetmesine izin verin. Bu anahtarlar MetaMask hesap veya hesaplarınızdan ayrıdır ve özel anahtarlarınızla ya da Gizli Kurtarma İfadenizle bağlantılı değildir. Diğer snap'ler bu bilgilere erişemez.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "<PERSON><PERSON><PERSON> ettiğ<PERSON>z di<PERSON> gö<PERSON>.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "$1 adlı bu snap'in MetaMask ayarlarınızdan tercih ettiğiniz dile erişim sağlamasına izin verin. $1 içeriğini dilinizi kullanarak yerelleştirmek ve görüntülemek için bu özellik kullanılabilir.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "<PERSON><PERSON><PERSON> ettiğiniz dil ve itibari para gibi bilgileri görün.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "$1 adlı snap'in MetaMask ayarlarınızda tercih ettiğiniz dil ve fiat para gibi bilgilere erişim sağlamasına izin verin. Bu durum $1 adlı snap'in sizin tercihlerinize uygun içerikler göstermesine yardımcı olur. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Özel bir ekran göster", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "$1 snap'inin MetaMask'te özel bir ana sayfa ekranı göstermesine izin verin. Bu, kullanı<PERSON><PERSON><PERSON>, yapılandırma ve kontrol panelleri için kullanılabilir.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Ethereum hesaplarını ekleme ve kontrol etme taleplerine izin ver", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "$1 adlı snap'in hesap ekleme ve kaldırma talepleri almasına ve ayrıca bu hesapların adına imza ve işlem gerçekleştirmesine izin verin.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "<PERSON><PERSON><PERSON> dö<PERSON>ü<PERSON>ü kancalarını kullanın.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Bu yaşam döngüsü sırasında belirli zamanlarda kod çalıştırmak için $1 adlı snap'in yaşam döngüsü kancalarını kullanmasına izin verin.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Ethereum hesaplarını ekle ve kontrol et", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "$1 adlı snap'in Ethereum hesabı eklemesine veya kaldırmasına izin verin, ardından bu hesaplarla işlem ve imza gerçekleştirin.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "$1 hesaplarını yönet.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "$1 adlı snap'in talep edilen ağ üzerinde hesap ve varlıkları yönetmesine izin verin. <PERSON><PERSON> hesaplar, gizli kurtarma ifade<PERSON>zi kull<PERSON> (açığa çıkarmadan) türetilir ve yedeklenir. $1, anahtar türetme gücü ile Ethereum (EVM'ler) ötesinde çeşitli blok zinciri protokollerini destekleyebilir.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "$1 hesaplarını yönetin.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "Verilerini cihazında sakla ve yönet.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "$1 adlı snap'in şifreleme ile güvenli bir şekilde veri de<PERSON>, g<PERSON><PERSON><PERSON><PERSON><PERSON> ve almasına izin verin. Diğer snap'ler bu bilgilere erişim sağlayamaz.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "<PERSON> ve adres aramalarını sunun.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Snap'in MetaMask Arayüzünün farklı kısımlarında adres ve alan aramalarını almasına ve göstermesine izin verin.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Bildirimleri göster.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "$1 adlı snap'in MetaMask dahilinde bildirim göstermesine izin verin. Eyleme geçirilebilir ya da zamana duyarlı bilgiler için bir snap tarafından kısa bir bildirim metni tetiklenebilir.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Bir veya daha fazla zincir için protokol verileri sağlayın.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "$1 adlı snap'in MetaMask'e gaz tahminleri veya token bilgileri gibi protokol verilerini sağlamasına izin verin.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "$1 için $2 ile doğrudan iletişim kurmasına izin verin.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "$1 için $2 adlı snap'e mesaj gönderme ve $2 adlı snap'ten yanıt alma izni verin.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 ve $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "İmza içgörüleri kipini göster.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "$1 adlı snap'in onaydan önce imza taleplerinde içgörüleri içeren bir kipi göstermesine izin verin. Bu, kimlik avını önlemek ve güvenlik çözümleri için kullanılabilir.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "İmza tale<PERSON> başlatan web sitelerinin kökenlerini gör", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "$1 adlı snap'in imza talepleri başlatan web sitelerinin kökenini (URI) görmesine izin ver. Bu, kimlik avını önlemek ve güvenlik çözümleri için kullanılabilir.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "İşlem içgörülerini al ve görüntüle.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "$1 adlı snap'in işlemlerin şifresini çözmesine ve MetaMask Kullanıcı Arayüzü dahilinde içgörüleri göstermesine izin verin. Kimlik avından korunma ve güvenlik çözümleri için kullanılabilir.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "İşlem öneren web sitelerinin kökenlerini gör", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "$1 adlı snap'in işlem öneren web sitelerinin asıl ad<PERSON> (URI) görmesine izin verin. Kimlik avından korunma ve güvenlik çözümleri için kullanılabilir.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Bilinmeyen izin: $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "$1 ($2) için genel anahtarınızı görünt<PERSON><PERSON>in.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "$2 adlı snap'in $1 için genel anah<PERSON>ı (ve adresleri) görüntülemesine izin verin. <PERSON><PERSON>, her<PERSON><PERSON> bir <PERSON><PERSON><PERSON> he<PERSON>ın ya da varlıkların kontrolünü vermez.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "$1 için genel anahtarınızı görüntüleyin.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Etkinleştirilmiş ağlarınızı kullanın", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "WebAssembly desteği.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "$1 adlı snap'in WebAssembly aracılığıyla düşük seviye yürütme ortamlarına erişim sağlamasına izin verin.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "<PERSON><PERSON><PERSON>"}, "permissionsPageEmptyContent": {"message": "Burada gösterilecek bir şey yok"}, "permissionsPageEmptySubContent": {"message": "<PERSON><PERSON><PERSON>, yüklü Snap'lere veya bağlı sitelere verdiğiniz izinleri görebilirsiniz."}, "permitSimulationChange_approve": {"message": "Harcama ü<PERSON> limiti"}, "permitSimulationChange_bidding": {"message": "Teklifiniz"}, "permitSimulationChange_listing": {"message": "Listelediğiniz"}, "permitSimulationChange_nft_listing": {"message": "Liste fiyatı"}, "permitSimulationChange_receive": {"message": "Aldığınız:"}, "permitSimulationChange_revoke2": {"message": "İptal"}, "permitSimulationChange_transfer": {"message": "Gönderdiğiniz:"}, "permitSimulationDetailInfo": {"message": "Harcama yapan tarafa hesa<PERSON>ı<PERSON>ızdan bu kadar çok token'i harcama izni veriyorsunuz."}, "permittedChainToastUpdate": {"message": "$1 için $2 erişimi var."}, "personalAddressDetected": {"message": "<PERSON><PERSON><PERSON><PERSON> adres algılandı. Token sözleşme adresini girin."}, "pinToTop": {"message": "<PERSON><PERSON><PERSON><PERSON> sabitle"}, "pleaseConfirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "plusMore": {"message": "+ $1 tane daha", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 tane daha", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Bu ağların bazıları üçüncü taraflara dayalıdır. Bağlantılar daha az güvenilir olabilir veya üçüncü tarafların aktiviteleri takip etmesine olanak sağlayabilir.", "description": "Learn more link"}, "popularNetworks": {"message": "<PERSON><PERSON><PERSON>"}, "portfolio": {"message": "Portföy"}, "preparingSwap": {"message": "Swap hazırlanıyor..."}, "prev": {"message": "<PERSON><PERSON><PERSON>"}, "price": {"message": "<PERSON><PERSON><PERSON>"}, "priceUnavailable": {"message": "fiyat mevcut de<PERSON>"}, "primaryType": {"message": "Öncelikli tür"}, "priorityFee": {"message": "Öncelik ücreti"}, "priorityFeeProperCase": {"message": "Öncelik Ücreti"}, "privacy": {"message": "Gizlilik"}, "privacyMsg": {"message": "Gizlilik politikası"}, "privateKey": {"message": "<PERSON><PERSON>", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "$1 için <PERSON> an<PERSON>tar", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "<PERSON><PERSON> an<PERSON> gizli", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "<PERSON>zel anahtar g<PERSON>ini <PERSON>/gizle", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "<PERSON>zel anahtar gösteriliyor", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Uyarı: <PERSON><PERSON> anahtarı kimse ile paylaşmayın. <PERSON>zel anahtarlarınıza sahip herkes hesaplarınızıdaki tüm varlığınızı çalabilir."}, "privateNetwork": {"message": "<PERSON><PERSON> ağ"}, "proceedWithTransaction": {"message": "<PERSON><PERSON> de devam et<PERSON>k istiyorum"}, "productAnnouncements": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "proposedApprovalLimit": {"message": "Önerilen onay limiti"}, "provide": {"message": "Sun"}, "publicAddress": {"message": "<PERSON><PERSON> ad<PERSON>"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "$1 $2 aldınız"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "Bir miktar token aldınız"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "Para alındı"}, "pushPlatformNotificationsFundsSentDescription": {"message": "$1 $2 g<PERSON><PERSON><PERSON><PERSON>"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "Başarılı bir şekilde bir miktar token gönderdiniz"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Para gönderildi"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Yeni NFT'ler aldı<PERSON>ız"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT alındı"}, "pushPlatformNotificationsNftSentDescription": {"message": "Başarılı bir şekilde bir NFT gönderdiniz"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT gönderildi"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "Lido stake işleminiz başarılı oldu"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Stake tama<PERSON>landı"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Lido stake işleminiz şu anda para çekme işlemine hazır"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Stake para çekme işlemine hazır"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Lido para çekme işleminiz başarılı oldu"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Para çekme işlemi tamamlandı"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Lido para çekme talebiniz g<PERSON>il<PERSON>"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "Para çekme talep edildi"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "RocketPool stake işleminiz başarılı oldu"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Stake tama<PERSON>landı"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "RocketPool unstake işleminiz başarılı oldu"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Unstake tamamlandı"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "MetaMask Swap işleminiz başarılı oldu"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "Swap işlemi ta<PERSON>landı"}, "queued": {"message": "Kuyruğa alındı"}, "quoteRate": {"message": "Kota oranı"}, "quotedReceiveAmount": {"message": "$1 alınan tutar"}, "quotedTotalCost": {"message": "$1 toplam maliyet"}, "rank": {"message": "Sıralama"}, "rateIncludesMMFee": {"message": "Orana %$1 ücret dahildir"}, "reAddAccounts": {"message": "<PERSON><PERSON><PERSON> hesapları yeniden ekle"}, "reAdded": {"message": "yeniden eklendi"}, "readdToken": {"message": "Gelecekte bu tokeni hesap seçenekleri menüsünde “Tokeni İçe Aktar”' kısmına giderek tekrar ekleyebilirsiniz."}, "receive": {"message": "Al"}, "receiveCrypto": {"message": "Kripto al"}, "recipientAddressPlaceholderNew": {"message": "Genel adres (0x) veya alan adı gir"}, "recommendedGasLabel": {"message": "Önerilen"}, "recoveryPhraseReminderBackupStart": {"message": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>n"}, "recoveryPhraseReminderConfirm": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "recoveryPhraseReminderHasBackedUp": {"message": "<PERSON><PERSON><PERSON>fadenizi her zaman güvende ve gizli bir yerde tutun"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadenizi yeniden yedeklemeniz mi gerekiyor?"}, "recoveryPhraseReminderItemOne": {"message": "G<PERSON><PERSON> Kurtarma İfadenizi asla başkasıyla <PERSON>mayın"}, "recoveryPhraseReminderItemTwo": {"message": "MetaMask ekibi sizden asla Gizli Kurtarma İfadenizi istemeyecektir"}, "recoveryPhraseReminderSubText": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadeniz tüm hesaplarınızı kontrol eder."}, "recoveryPhraseReminderTitle": {"message": "Paranızı koruyun"}, "redeposit": {"message": "<PERSON><PERSON><PERSON> ya<PERSON>ı<PERSON><PERSON>n"}, "refreshList": {"message": "<PERSON><PERSON><PERSON> yenile"}, "reject": {"message": "<PERSON><PERSON>"}, "rejectAll": {"message": "Tümünü reddet"}, "rejectRequestsDescription": {"message": "$1 talebi toplu olarak reddetmek üzeresiniz."}, "rejectRequestsN": {"message": "$1 taleplerini reddet"}, "rejectTxsDescription": {"message": "$1 işlemi toplu olarak reddetmek üzeresiniz."}, "rejectTxsN": {"message": "$1 işlemi reddet"}, "rejected": {"message": "Reddedildi"}, "remove": {"message": "Kaldır"}, "removeAccount": {"message": "Hesabı kaldır"}, "removeAccountDescription": {"message": "Bu hesap cüzdanınızdan kaldırılacaktır. Devam etmeden önce içe aktarılmış olan bu hesap için ilk olarak oluşturulan Gizli Kurtarma İfadesine ya da özel anahtara sahip olduğunuzdan lütfen emin olun. Hesap açılır menüsünden hesapları yeniden içe aktarabilir ya da hesap oluşturabilirsiniz. "}, "removeKeyringSnap": {"message": "Bu Snap kaldırıldığında bu hesaplar MetaMask'ten kaldırılır:"}, "removeKeyringSnapToolTip": {"message": "Hesapları snap kontrol eder ve snap kaldırıldığında hesaplar MetaMask'ten de kaldılır, ancak blokzincirinde kalır."}, "removeNFT": {"message": "NFT'yi kaldır"}, "removeNftErrorMessage": {"message": "Bu NFT'yi ka<PERSON>yoruz."}, "removeNftMessage": {"message": "NFT başarılı bir şekilde kaldırıldı!"}, "removeSnap": {"message": "<PERSON><PERSON><PERSON> kaldır"}, "removeSnapAccountDescription": {"message": "İlerlerseniz bu hesap artık MetaMask'te kullanılamayacak."}, "removeSnapAccountTitle": {"message": "Hesabı kaldırın"}, "removeSnapConfirmation": {"message": "$1 snapini kaldırmak istediğinizden emin misiniz?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "<PERSON><PERSON> e<PERSON>, <PERSON><PERSON>, veril<PERSON>ni siler ve verdiğin izinleri iptal eder."}, "replace": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "reportIssue": {"message": "<PERSON>ir sorun bildir"}, "requestFrom": {"message": "<PERSON><PERSON>"}, "requestFromInfo": {"message": "İmzanızı isteyen site budur."}, "requestFromInfoSnap": {"message": "İmzanızı isteyen Snap budur."}, "requestFromTransactionDescription": {"message": "Bu site tarafından onayınız isteniyor."}, "requestingFor": {"message": "Talep:"}, "requestingForAccount": {"message": "$1 için talep ediliyor", "description": "Name of Account"}, "requestingForNetwork": {"message": "$1 için talep ediliyor", "description": "Name of Network"}, "required": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "reset": {"message": "Sıfırla"}, "resetWallet": {"message": "Cüzdanı sıfırla"}, "resetWalletSubHeader": {"message": "MetaMask, şifrenizin bir kopyasını tutmaz. Hesabınızın kilidini açarken sorun yaşıyorsanız cüzdanınızı sıfırlamanız gerekir. <PERSON><PERSON><PERSON>, cüzdanınızı kurarken kullandığınız Gizli Kurtarma İfadesini sunarak yapabilirsin."}, "resetWalletUsingSRP": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, düzenlediğiniz hesapların listesiyle birlikte mevcut cüzdanınızı ve Gizli Kurtarma İfadenizi bu cihazdan siler. Bir Gizli Kurtarma İfadesi ile sıfırlama yaptıktan sonra sıfırlamak için kullandığınız Gizli Kurtarma İfadesine dayalı bir hesap listesi göreceksiniz. Bu yeni liste otomatik olarak bakiyesi olan hesapları içerecektir. Ayrıca daha önce oluşturulmuş $1 hesabınızı oluşturabileceksiniz. İçe aktardığınız özel hesapların $2 olması ve bir hesaba eklediğiniz özel tokenlerin de $3 olması gerekir."}, "resetWalletWarning": {"message": "Devam etmeden önce doğru Gizli Kurtarma İfadesini kullandığından emin ol. <PERSON><PERSON><PERSON> geri al<PERSON>ın."}, "restartMetamask": {"message": "MetaMask'i yeniden ba<PERSON>"}, "restore": {"message": "<PERSON><PERSON>"}, "restoreUserData": {"message": "Kullanıcı verilerini geri <PERSON>"}, "resultPageError": {"message": "<PERSON><PERSON>"}, "resultPageErrorDefaultMessage": {"message": "İşlem başarısız oldu."}, "resultPageSuccess": {"message": "Başarılı"}, "resultPageSuccessDefaultMessage": {"message": "İşlem başarılı bir şekilde <PERSON>landı."}, "retryTransaction": {"message": "İşlemi tekrar dene"}, "reusedTokenNameWarning": {"message": "Buradaki bir token izlediğiniz başka bir tokenden sembol kullanıyor, bu kafa karıştırıcı veya aldatıcı olabilir."}, "revealSecretRecoveryPhrase": {"message": "Gizli Kurtarma İfadesini Açığa Çıkar"}, "revealSeedWords": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadesini Göster"}, "revealSeedWordsDescription1": {"message": "$1 $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMask $1. Yani GKİ'niz size aittir.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "cüzdanınıza ve paranıza tam erişim <PERSON>.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "gözetimsiz bir cüzdandır"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Gizli Kurtarma İfadesi (GKİ)"}, "revealSeedWordsText": {"message": "<PERSON><PERSON>"}, "revealSeedWordsWarning": {"message": "Ekranınıza hiç kimsenin bakmadığından emin olun. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "MetaMask Destek bölümü bunu asla talep etmez.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "<PERSON><PERSON>s içeriği açığa çıkar"}, "review": {"message": "<PERSON><PERSON><PERSON>"}, "reviewAlert": {"message": "Uyarıyı incele"}, "reviewAlerts": {"message": "Uyarıları incele"}, "reviewPendingTransactions": {"message": "Bekleyen işlemleri incele"}, "reviewPermissions": {"message": "İzinleri incele"}, "revokePermission": {"message": "İzni geri al"}, "revokePermissionTitle": {"message": "$1 iznini kaldır", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "<PERSON><PERSON><PERSON><PERSON> sizin hesabınızdan token harcama iznini kaldırıyorsunuz."}, "reward": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "rpcNameOptional": {"message": "RPC Adı (İsteğe Bağlı)"}, "rpcUrl": {"message": "RPC URL adresi"}, "safeTransferFrom": {"message": "Güvenli transfer kaynağı"}, "save": {"message": "<PERSON><PERSON>"}, "scanInstructions": {"message": "QR kodu kameranızın önüne getirin"}, "scanQrCode": {"message": "QR kodunu tara"}, "scrollDown": {"message": "Aşağı kaydır"}, "search": {"message": "Ara"}, "searchAccounts": {"message": "Hesapları ara"}, "searchNfts": {"message": "NFT ara"}, "searchTokens": {"message": "Token ara"}, "searchTokensByNameOrAddress": {"message": "Ada veya adrese göre token ara"}, "secretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON>"}, "secretRecoveryPhrasePlusNumber": {"message": "Gizli Kurtarma İfadesi $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>"}, "security": {"message": "Güvenlik"}, "securityAlert": {"message": "$1 ve $2 güvenlik uyarısı"}, "securityAlerts": {"message": "Güvenlik uyarıları"}, "securityAlertsDescription": {"message": "Bu özellik işlem ve imza taleplerini aktif bir şekilde inceleyerek kötü amaçlı veya olağan dışı aktivite konusunda sizi uyarır. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Güvenlik ve gizlilik"}, "securityDescription": {"message": "Güvensiz ağlara katılma şansınızı azaltın ve hesaplarınızı koruyun"}, "securityMessageLinkForNetworks": {"message": "ağ dolandırıcılıkları ve güvenlik riskleri"}, "securityProviderPoweredBy": {"message": "$1 hizmetidir", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "<PERSON><PERSON><PERSON> i<PERSON> gör", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "Ayrıntı<PERSON>a bakın"}, "seedPhraseIntroTitle": {"message": "Cüzdanınızı koruyun"}, "seedPhraseReq": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadeleri 12, 15, 18, 21 veya 24 keli<PERSON>en oluşur"}, "select": {"message": "Seç"}, "selectAccountToConnect": {"message": "Bağlamak için bir hesap seç"}, "selectAccounts": {"message": "Bu sitede kullanılacak hesap veya hesapları seçin"}, "selectAccountsForSnap": {"message": "Bu snap ile kullanılacak hesabı/hesapları seçin"}, "selectAll": {"message": "<PERSON>ü<PERSON>ü<PERSON><PERSON> seç"}, "selectAnAccount": {"message": "<PERSON><PERSON><PERSON>"}, "selectAnAccountAlreadyConnected": {"message": "Bu hesap zaten MetaMask'e bağlanmış"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "NFT medyasını göster seçeneğini açın"}, "selectHdPath": {"message": "HD yolunu seç"}, "selectNFTPrivacyPreference": {"message": "NFT Otomatik Algılamayı etkinleştir"}, "selectPathHelp": {"message": "Beklediğiniz hesapları görmüyorsanız HD yoluna veya geçerli seçilen ağa geçmeyi deneyin."}, "selectRpcUrl": {"message": "RPC URL adresini seçin"}, "selectSecretRecoveryPhrase": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadesi Seç"}, "selectType": {"message": "<PERSON><PERSON><PERSON>"}, "selectedAccountMismatch": {"message": "Farklı hesap se<PERSON>ildi"}, "selectingAllWillAllow": {"message": "Tümü seçimi bu sitenin mevcut tüm hesaplarınızı görüntülemesine izin verecektir. Bu siteye güvendiğinizden emin olun."}, "send": {"message": "<PERSON><PERSON><PERSON>"}, "sendBugReport": {"message": "Bize bir hata raporu g<PERSON>."}, "sendNoContactsConversionText": {"message": "buraya tıklayın"}, "sendNoContactsDescription": {"message": "<PERSON><PERSON><PERSON>, di<PERSON><PERSON> he<PERSON><PERSON> güvenli bir şekilde birden fazla kez işlem gönderebilmenize olanak sağlar. Bir kişi oluşturmak için $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "Henüz kişiniz yok"}, "sendSelectReceiveAsset": {"message": "Alınacak varlıkları seçin"}, "sendSelectSendAsset": {"message": "Gönderilecek varlıkları seçin"}, "sendSpecifiedTokens": {"message": "$1 <PERSON><PERSON>nder", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "Bu düğmeye tıklandığında swap işleminiz anında başlatılır. İlerlemeden önce lütfen işlem ayrıntılarınızı inceleyin."}, "sendTokenAsToken": {"message": "$2 olarak $1 gönder", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "$1 gönderiliyor"}, "sendingDisabled": {"message": "ERC-1155 NFT varlıklarının gönderilmesi henüz desteklenmemektedir."}, "sendingNativeAsset": {"message": "$1 Gönderiliyor", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Uyarı: Para kaybı ile sonuçlanabilecek bir token sözleşmesi göndermek üzeresiniz. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Sepolia test ağı"}, "setApprovalForAll": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> onay ver"}, "setApprovalForAllRedesignedTitle": {"message": "<PERSON> çekme talebi"}, "setApprovalForAllTitle": {"message": "$1 için harcama limiti olmadan onay ver", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Snap hesabı ekle"}, "settings": {"message": "<PERSON><PERSON><PERSON>"}, "settingsSearchMatchingNotFound": {"message": "Eşleşen sonuç bulunamadı."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "İmza ve işlem talepleri"}, "show": {"message": "<PERSON><PERSON><PERSON>"}, "showAccount": {"message": "Hesabı göster"}, "showAdvancedDetails": {"message": "Gelişmiş bilgileri göster"}, "showExtensionInFullSizeView": {"message": "Uzantıyı tam boyut görünümde göster"}, "showExtensionInFullSizeViewDescription": {"message": "Uzantı simgesine tıkladığınızda tam boyut görünümünüzü varsayılan yapmak için bunu açın."}, "showFiatConversionInTestnets": {"message": "Test ağlarında dönüşümü göster"}, "showFiatConversionInTestnetsDescription": {"message": "Test ağlarında fiat para dönüşümünü göstermek için bunu seçin"}, "showHexData": {"message": "On altılı verileri göster"}, "showHexDataDescription": {"message": "<PERSON><PERSON><PERSON> ekranında on altılı veri alanını göstermek için bunu seçin"}, "showLess": {"message": "<PERSON><PERSON> a<PERSON> g<PERSON>"}, "showMore": {"message": "Daha fazlasını göster"}, "showNativeTokenAsMainBalance": {"message": "<PERSON><PERSON><PERSON>i ana bakiye o<PERSON>"}, "showNft": {"message": "NFT'y<PERSON>"}, "showPermissions": {"message": "İzinleri göster"}, "showPrivateKey": {"message": "<PERSON><PERSON>"}, "showSRP": {"message": "<PERSON><PERSON><PERSON> Kurtarma İfadesini Göster"}, "showTestnetNetworks": {"message": "Test ağlarını göster"}, "showTestnetNetworksDescription": {"message": "Ağ listesinde test ağlarını göstermek için bunu seçin"}, "sign": {"message": "İmzala"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON>"}, "signature_decoding_bid_nft_tooltip": {"message": "NFT, teklif kabul edildiğinde cüzdanınıza yansıtılacak."}, "signature_decoding_list_nft_tooltip": {"message": "Sadece NFT'leriniz birisi tarafından satın alınırsa değişiklik olmasını bekleyin."}, "signed": {"message": "İmzalandı"}, "signing": {"message": "İmzalanıyor"}, "signingInWith": {"message": "<PERSON><PERSON>un<PERSON> giriş yap:"}, "signingWith": {"message": "Oturum açma aracı"}, "simulationApproveHeading": {"message": "Çek"}, "simulationDetailsApproveDesc": {"message": "Bir başkasına hesabınızdan NFT çekme izni veriyorsunuz."}, "simulationDetailsERC20ApproveDesc": {"message": "Bir başkasına sizin hesabınızdan bu tutarı harcama izni veriyorsunuz."}, "simulationDetailsFiatNotAvailable": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsIncomingHeading": {"message": "Aldığınız"}, "simulationDetailsNoChanges": {"message": "Değişiklik yok"}, "simulationDetailsOutgoingHeading": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Bir başkasının sizin hesabınızdan NFT çekme iznini kaldırıyorsunuz."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Bir başkasına sizin hesabınızdan NFT çekme izni veriyorsunuz."}, "simulationDetailsTitle": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsTitleTooltip": {"message": "<PERSON><PERSON><PERSON>iklikler bu işlemi gerçekleştirirseniz meydana gelebilecek değişikliklerdir. Bu bir g<PERSON><PERSON>, sadece bir tahm<PERSON>."}, "simulationDetailsTotalFiat": {"message": "Toplam = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON><PERSON>z olması muhtemel"}, "simulationDetailsUnavailable": {"message": "<PERSON><PERSON><PERSON>"}, "simulationErrorMessageV2": {"message": "Gaz tahmini ya<PERSON>. Sözleşmede bir hata olabilir ve bu işlem başarısız olabilir."}, "simulationsSettingDescription": {"message": "Onaylamadan önce işlemlerdeki ve imzalardaki bakiye değişikliklerini tahmin etmek için bunu açın. <PERSON><PERSON> sonucunu garanti etmez. $1"}, "simulationsSettingSubHeader": {"message": "Bakiye değişikliklerini tahmin edin"}, "singleNetwork": {"message": "1 ağ"}, "siweIssued": {"message": "D<PERSON><PERSON>lendi"}, "siweNetwork": {"message": "Ağ"}, "siweRequestId": {"message": "<PERSON><PERSON>"}, "siweResources": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "siweURI": {"message": "URL adresi"}, "skipAccountSecurity": {"message": "<PERSON><PERSON><PERSON> atla?"}, "skipAccountSecurityDetails": {"message": "G<PERSON><PERSON> Kurtarma İfademi yedekleyene kadar hesaplarımı ve tüm varlıkları kaybedebileceğimi anlıyorum."}, "slideBridgeDescription": {"message": "Tamamı cüzdanınız dahilinde olmak üzere 9 zincirde ilerleyin"}, "slideBridgeTitle": {"message": "<PERSON>ö<PERSON><PERSON><PERSON> yapmaya hazır mısınız?"}, "slideCashOutDescription": {"message": "Kriptonuzu satarak nakde dönüştürün"}, "slideCashOutTitle": {"message": "MetaMask ile paraya <PERSON>n"}, "slideDebitCardDescription": {"message": "<PERSON><PERSON><PERSON> bölgelerde mevcuttur"}, "slideDebitCardTitle": {"message": "MetaMask banka kartı"}, "slideFundWalletDescription": {"message": "Başlamak için token ekleyin veya transfer edin"}, "slideFundWalletTitle": {"message": "Cüzdanınıza para ekleyin"}, "slideMultiSrpDescription": {"message": "MetaMask'te birden fazla cüzdanı içe aktarın ve kullanın"}, "slideMultiSrpTitle": {"message": "Birden fazla Giz<PERSON>arma İfadesi ekleyin"}, "slideRemoteModeDescription": {"message": "Soğuk cüzdanınızı kablosuz olarak kullanın"}, "slideRemoteModeTitle": {"message": "<PERSON><PERSON><PERSON>, h<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON><PERSON>, daha akıllı özellikler"}, "slideSmartAccountUpgradeTitle": {"message": "Akıllı hesapları kullanmaya başlayın"}, "slideSolanaDescription": {"message": "Başlamak için bir Solana hesabı oluşturun"}, "slideSolanaTitle": {"message": "Solana artık destekleniyor"}, "slideSweepStakeDescription": {"message": "Kazanma şansı için şimdi bir NFT mint edin"}, "slideSweepStakeTitle": {"message": "$5000 USDC Çekilişine katılın!"}, "smartAccountAccept": {"message": "Akıllı hesap kull<PERSON>ın"}, "smartAccountBetterTransaction": {"message": "<PERSON><PERSON> hı<PERSON><PERSON><PERSON>, <PERSON><PERSON> dü<PERSON><PERSON>k ücretler"}, "smartAccountBetterTransactionDescription": {"message": "İşlemleri birlikte gerçekleştirerek vakitten ve nakitten tasarruf edin."}, "smartAccountFeaturesDescription": {"message": "Aynı hesap ad<PERSON>ini <PERSON>; dilediğiniz zaman tekrar geçebilirsiniz."}, "smartAccountLabel": {"message": "Akıllı Hesap"}, "smartAccountPayToken": {"message": "Dilediğiniz token ile dilediğiniz zaman ödeyin"}, "smartAccountPayTokenDescription": {"message": "Ağ ücretlerini karşılamak için zaten sahip olduğunuz token'ları kullanın."}, "smartAccountReject": {"message": "Akıllı hesap kull<PERSON>"}, "smartAccountRequestFor": {"message": "<PERSON><PERSON> et:"}, "smartAccountSameAccount": {"message": "<PERSON><PERSON><PERSON>, daha akıllı özellikler."}, "smartAccountSplashTitle": {"message": "Akıllı hesap kullanı<PERSON>ın mı?"}, "smartAccountUpgradeBannerDescription": {"message": "Aynı adres. Daha akıllı özellikler."}, "smartAccountUpgradeBannerTitle": {"message": "Akıllı hesaba geç"}, "smartContracts": {"message": "Akıllı sözleşmeler"}, "smartSwapsErrorNotEnoughFunds": {"message": "Akıllı swap için yeterli para yok."}, "smartSwapsErrorUnavailable": {"message": "Akıllı Swap'lar geçici olarak kullanılamıyor."}, "smartTransactionCancelled": {"message": "İşleminiz iptal edildi"}, "smartTransactionCancelledDescription": {"message": "İşleminiz tamamlanamadığından gereksiz gaz ücreti ödemenizi önlemek amacıyla iptal edildi."}, "smartTransactionError": {"message": "İşleminiz başarısız oldu"}, "smartTransactionErrorDescription": {"message": "Ani piyasa değişimleri başarısızlıklara sebep olabilir. <PERSON><PERSON> devam ederse MetaMask müşteri destek bölümüne ulaşın."}, "smartTransactionPending": {"message": "İşleminiz gönderildi"}, "smartTransactionSuccess": {"message": "İşleminiz ta<PERSON>landı"}, "smartTransactions": {"message": "Akıllı İşlemler"}, "smartTransactionsEnabledDescription": {"message": " ve MEV koruması. Şu anda varsayılan olarak açık."}, "smartTransactionsEnabledLink": {"message": "<PERSON>ha yüksek başarı oranları"}, "smartTransactionsEnabledTitle": {"message": "İşlemler artık daha akıllı hale geldi"}, "snapAccountCreated": {"message": "<PERSON><PERSON><PERSON>"}, "snapAccountCreatedDescription": {"message": "Yeni hesabın<PERSON>z kullanılmaya hazır!"}, "snapAccountCreationFailed": {"message": "Hesap oluşturma işlemi başarısız oldu"}, "snapAccountCreationFailedDescription": {"message": "$1 sizin için bir hesap oluşturamadı.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "İmzalama işlemini bitir"}, "snapAccountRedirectSiteDescription": {"message": "$1 talimatlarını izle"}, "snapAccountRemovalFailed": {"message": "<PERSON>sap kaldı<PERSON> işlemi başarısız oldu"}, "snapAccountRemovalFailedDescription": {"message": "$1 sizin için bu hesabı kaldıramadı.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "<PERSON><PERSON><PERSON> kaldır<PERSON>ı"}, "snapAccountRemovedDescription": {"message": "Bu hesap artık MetaMask'te kullanılamayacak."}, "snapAccounts": {"message": "Snap Hesapları"}, "snapAccountsDescription": {"message": "Üçüncü taraf Snapleri tarafından kontrol edilen hesaplar."}, "snapConnectTo": {"message": "$1 adresine ba<PERSON>lan", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Onayınız olmadan $1 otomatik olarak $2 adresine bağlansın.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 şunu kullanmak istiyor: $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Web Sitesi"}, "snapHomeMenu": {"message": "S<PERSON> <PERSON>"}, "snapInstallRequest": {"message": "$1 adlı snap'i yüklemek ona aşağıdaki izinleri verir.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ı"}, "snapInstallWarningCheck": {"message": "$1, şun<PERSON>ı yapmak için izin istiyor:", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "<PERSON><PERSON><PERSON> il<PERSON>ley<PERSON>"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "$1 adlı snap'in genel anahtarlarınızı (ve adreslerinizi) görüntülemesine izin verin. <PERSON><PERSON>, her<PERSON><PERSON> bir <PERSON><PERSON><PERSON> he<PERSON>pların ya da varlıkların kontrolünü vermez.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "$1 adlı Snap'in talep edilen ağlar üzerinde hesap ve varlıkları yönetmesine izin verin. Bu hesaplar, gizli kurtarma ifadenizi kull<PERSON>rak (açığa çıkarmadan) türetilir ve yedeklenir. $1, anahtar türetme gücü ile Ethereum (EVM'ler) ötesinde çeşitli blok zinciri protokollerini destekleyebilir.", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "$1 hesaplarını yönet", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "$1 için genel anahtarınızı görünt<PERSON><PERSON>in", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 yüklenemedi.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "Yükleme başarısız oldu", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "<PERSON><PERSON>"}, "snapResultSuccess": {"message": "Başarılı"}, "snapResultSuccessDescription": {"message": "$1 kullanıma hazır"}, "snapUIAssetSelectorTitle": {"message": "Bir varlık seç"}, "snapUpdateAlertDescription": {"message": "En son $1 g<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>n", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> mevcut"}, "snapUpdateErrorDescription": {"message": "$1 güncellenemedi.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarısız oldu", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "$1 adlı snap'i güncellemek ona aşağıdaki izinleri verir.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "snapUrlIsBlocked": {"message": "Bu Snap sizi bloke edilmiş bir siteye götürmek istiyor. $1."}, "snaps": {"message": "<PERSON><PERSON><PERSON>"}, "snapsConnected": {"message": "S<PERSON>'ler ba<PERSON>ı"}, "snapsNoInsight": {"message": "Gösterilecek içgörü yok"}, "snapsPrivacyWarningFirstMessage": {"message": "Yüklediğiniz her Snap'in Consensys $1 içinde tanımlanan şekilde, aksi belirtilmedikçe bir Üçüncü Taraf Hizmet olduğunu kabul edersiniz. Üçüncü Taraf Hizmetlerini kullanımınız Üçüncü Taraf Hizmet sağlayıcısı tarafından belirtilen ayrı şart ve koşullar tarafından yönetilir. Consensys herhangi bir Snapin herhangi belirli bir sebeple herhangi belirli bir şahıs tarafından kullanımını önermez. Üçüncü Taraf Hizmetine erişiminizin, güvenmenizin veya kullanımınızın riski size aittir. Consensys, Üçüncü Taraf Hizmetlerini kullanımınızdan kaynaklanan zararlar için her türlü sorumluluğu ve yükümlülüğü reddeder.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Üçüncü Taraf Hizmetleri ile paylaştığınız tüm bilgiler söz konusu Üçüncü Taraf Hizmetlerinin gizlilik politikalarına göre doğrudan üçüncü taraflar tarafından toplanacaktır. Daha fazla bilgi için lütfen üçüncü tarafların gizlilik politikalarına bakın.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys'un bu Üçüncü Taraf Hizmetler ile paylaştığınız bilgilere hiçbir erişimi bulunmamaktadır.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Snap ayarları"}, "snapsTermsOfUse": {"message": "Kullanım Şartları"}, "snapsToggle": {"message": "Bir snap yalnızca etkinleştirilmişse çalışır"}, "snapsUIError": {"message": "Daha fazla destek için $1 oluşturucuları ile iletişime geçin.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Bu site bir Solana hesabı talep ediyor."}, "solanaAccountRequired": {"message": "Bu siteye bağlanmak için bir Solana hesabı gereklidir."}, "solanaImportAccounts": {"message": "Solana hesaplarını içe aktar"}, "solanaImportAccountsDescription": {"message": "Başka bir cüzdandan Solana hesabınızı taşımak için bir Gizli Kurtarma İfadesini içe aktarın."}, "solanaMoreFeaturesComingSoon": {"message": "Daha fazla özellik çok yakında"}, "solanaMoreFeaturesComingSoonDescription": {"message": "N<PERSON>'<PERSON><PERSON>, don<PERSON><PERSON><PERSON> cüzdanı desteği ve daha fazlası çok yakında."}, "solanaOnMetaMask": {"message": "<PERSON><PERSON>te"}, "solanaSendReceiveSwapTokens": {"message": "<PERSON><PERSON> g<PERSON>, al ve <PERSON> yap"}, "solanaSendReceiveSwapTokensDescription": {"message": "SOL, USDC ve daha fazlası gibi token'lar ile transfer ve işlem gerçekleştir."}, "someNetworks": {"message": "$1 ağ"}, "somethingDoesntLookRight": {"message": "Doğru görünmeyen bir şeyler mi var? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Bir şeyler ters gitti. Sayfayı tekrar yüklemeyi deneyin."}, "somethingWentWrong": {"message": "Bu sayfayı yükleyemedik."}, "sortBy": {"message": "Sıralama ölçütü"}, "sortByAlphabetically": {"message": "Alfabetik (A-Z)"}, "sortByDecliningBalance": {"message": "<PERSON><PERSON><PERSON> ($1 yüksek-düşük)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "<PERSON><PERSON><PERSON>"}, "spamModalBlockedDescription": {"message": "Bu site 1 dakika boyunca engellenecek."}, "spamModalBlockedTitle": {"message": "Bu siteyi geçici olarak engellediniz"}, "spamModalDescription": {"message": "Birden fazla istenmeyen talep alıyorsanız siteyi geçici olarak engelleyebilirsiniz."}, "spamModalTemporaryBlockButton": {"message": "Bu siteyi geçici olarak engelle"}, "spamModalTitle": {"message": "<PERSON><PERSON> fazla talep fark ettik"}, "speed": {"message": "Hız"}, "speedUp": {"message": "Hızlandır"}, "speedUpCancellation": {"message": "Bu iptal işlemini hızlandır"}, "speedUpExplanation": {"message": "Mevcut ağ koşullarına göre gaz ücretini güncelledik ve en az %10 artırdık (ağ tarafından gereklidir)."}, "speedUpPopoverTitle": {"message": "İşlemi hızlandır"}, "speedUpTooltipText": {"message": "<PERSON>ni gaz ücreti"}, "speedUpTransaction": {"message": "Bu işlemi hızlandır"}, "spendLimitInsufficient": {"message": "Ha<PERSON>ma limiti <PERSON>"}, "spendLimitInvalid": {"message": "Harcama limiti geçersiz; pozitif bir sayı olmalıdır"}, "spendLimitPermission": {"message": "Harcama limiti izni"}, "spendLimitRequestedBy": {"message": "$1 tarafından talep edilen harcama limiti", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Harcama limiti çok büyük"}, "spender": {"message": "<PERSON><PERSON><PERSON>"}, "spenderTooltipDesc": {"message": "<PERSON><PERSON>, NFT'lerinizi çekebilecek adrestir."}, "spenderTooltipERC20ApproveDesc": {"message": "<PERSON><PERSON>, <PERSON>'le<PERSON><PERSON> sizin adınıza harcayabilecek adrestir."}, "spendingCap": {"message": "Harcama ü<PERSON> limiti"}, "spendingCaps": {"message": "Harcama üst limitleri"}, "srpInputNumberOfWords": {"message": "$1 sözcükten oluşan bir ifadem var", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Gizli Kurtarma İfadesi $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 hesap", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "Yeni hesabınızın oluşturulacağı Gizli Kurtarma İfadesi"}, "srpListSingleOrZero": {"message": "$1 hesap", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "24'ten fazla sözcük içerdiği için yapıştırma başarısız oldu. Gizli bir kurtarma ifadesi en fazla 24 sözcükten oluşabilir.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Gizli kurtarma ifadenizin tamamını herhangi bir alana ya<PERSON>ı<PERSON>ırabilirsiniz", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "Başlarken"}, "srpSecurityQuizImgAlt": {"message": "<PERSON>tada anahtar deliği ile bir göz ve <PERSON>ç kayan şifre alanı"}, "srpSecurityQuizIntroduction": {"message": "Gizli Kurtarma İfadenizi görmek için iki soruyu doğru cevaplamanız gerekmektedir"}, "srpSecurityQuizQuestionOneQuestion": {"message": "G<PERSON><PERSON> Kurtarma İfadenizi kaybederseniz MetaMask..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "Size yardımcı olamaz"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "<PERSON>ir yere yazın, bir metalin üzerine kazıyın veya asla kaybetmemeniz için birden fazla noktada saklayın. Kaybederseniz sonsuza dek kaybolur."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "Doğru! Hiç kimse Gizli Kurtarma İfadenizi geri almanıza yardımcı olamaz"}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Size onu tekrar verebilir"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "Gizli Kurtarma İfadenizi kaybederseniz sonsuza dek kaybolur. Söylediklerinin ne olduğuna bakılmaksızın hiç kimse onu geri almanıza yardımcı olamaz."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "Yanlış! Hiç kimse Gizli Kurtarma İfadenizi geri almanıza yardımcı olamaz"}, "srpSecurityQuizQuestionTwoQuestion": {"message": "<PERSON><PERSON><PERSON>, bir destek temsilcisi bile sizden Gizli Kurtarma İfadenizi isterse..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "Dolandırılıyorsunuzdur"}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Gizli Kurtarma İfadenizi isteyen kişi size yalan söylüyordur. Kendisi ile paylaşırsanız varlıklarınızı çalacaktır."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "Doğru! Gizli Kurtarma İfadenizi paylaşmak asla iyi bir fikir <PERSON>dir"}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "<PERSON><PERSON><PERSON> ve<PERSON>"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Gizli Kurtarma İfadenizi isteyen kişi size yalan söylüyordur. Kendisi ile paylaşırsanız varlıklarınızı çalacaktır."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "Hayır! Gizli Kurtarma İfadenizi asla hiç kimse ile paylaşmayın, asla"}, "srpSecurityQuizTitle": {"message": "Güvenlik testi"}, "srpToggleShow": {"message": "Gizli kurtarma ifadesinin bu sözcüğünü göster/gizle", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "<PERSON><PERSON> sözcük gizli", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Bu sözcük gösteriliyor", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Stabil"}, "stableLowercase": {"message": "stabil"}, "stake": {"message": "Pay"}, "staked": {"message": "Stake edildi"}, "standardAccountLabel": {"message": "<PERSON><PERSON>"}, "startEarning": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "stateLogError": {"message": "Durum günlükleri alınırken hata."}, "stateLogFileName": {"message": "MetaMask durum günlükleri"}, "stateLogs": {"message": "<PERSON>rum günlü<PERSON>"}, "stateLogsDescription": {"message": "Durum günlükleri genel hesap adreslerinizi ve gönderilen işlemleri içerir."}, "status": {"message": "Durum"}, "statusNotConnected": {"message": "Bağlanmadı"}, "statusNotConnectedAccount": {"message": "Her<PERSON><PERSON> bir hesap ba<PERSON><PERSON><PERSON>"}, "step1LatticeWallet": {"message": "Lattice1'inizi bağlayın"}, "step1LatticeWalletMsg": {"message": "Kurulduktan ve çevrimiçi olduktan sonra MetaMask'i Lattice1 cihazınıza bağlayabilirsiniz. Cihazınızın kilidini açın ve Cihaz Kimlliğinizi hazırlayın.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Ledger uygulamasını indir"}, "step1LedgerWalletMsg": {"message": "$1 kilidini açmak için indirin, kurun ve şifrenizi girin.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>n"}, "step1TrezorWalletMsg": {"message": "Cüzdanınızı doğrudan bilgisayara bağlayın ve kilidini açın. Doğru parolayı kullandığınızdan emin olun.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Ledger'ınızı bağlayın"}, "step2LedgerWalletMsg": {"message": "Ledger'ınızı doğrudan bil<PERSON> ba<PERSON>ın, ardından kilidini açın ve Ethereum uygulamasını açın.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Bu mesajı almaya devam mı ediyorsun?"}, "strong": {"message": "Güçlü"}, "stxCancelled": {"message": "Swap işlemi başarısız olurdu"}, "stxCancelledDescription": {"message": "İşleminiz başarısız oldu ve gereksiz gaz ücreti ödemenizi önlemek amacıyla iptal edildi."}, "stxCancelledSubDescription": {"message": "Swap işlemini tekrar deneyin. Bir dahaki sefere sizi benzer risklere karşı korumak için burada olacağız."}, "stxFailure": {"message": "Swap başarısız oldu"}, "stxFailureDescription": {"message": "Ani piyasa değişiklikleri başarısızlıklara neden olabilir. Sorun devam ederse lütfen $1 ile iletişime geç.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "Desteklenen ağlarda daha güvenilir ve güvenli işlemler için Akıllı İşlemleri açın. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Swap <PERSON><PERSON><PERSON><PERSON> olarak gönderiliyor..."}, "stxPendingPubliclySubmittingSwap": {"message": "Swap i<PERSON><PERSON>in herkese açık olarak gönderiliyor..."}, "stxSuccess": {"message": "Swap işlemi tamamlandı!"}, "stxSuccessDescription": {"message": "$1 artık kullanılabilir.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "Swap iş<PERSON><PERSON>n tama<PERSON>lanmasına kalan süre <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "İşlemin iptal edilmeye çalışılıyor..."}, "stxUnknown": {"message": "Durum bilinmiyor"}, "stxUnknownDescription": {"message": "Bir işlem başarılı oldu ama ne olduğundan emin de<PERSON>. <PERSON><PERSON><PERSON> neden<PERSON>, bu <PERSON> gerçekleştirilirken başka bir işlemin gönderilmesi olabilir."}, "stxUserCancelled": {"message": "Swap iptal edildi"}, "stxUserCancelledDescription": {"message": "İşleminiz iptal edildi ve gereksiz gaz ücreti ödemediniz."}, "submit": {"message": "<PERSON><PERSON><PERSON>"}, "submitted": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "suggestedBySnap": {"message": "$1 tarafından öneriliyor", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Önerilen para birimi sembolü:"}, "suggestedTokenName": {"message": "<PERSON><PERSON><PERSON>n isim:"}, "supplied": {"message": "Sağlandı"}, "support": {"message": "Destek"}, "supportCenter": {"message": "Destek Merkezi bölümümüzü ziyaret et"}, "supportMultiRpcInformation": {"message": "<PERSON><PERSON> anda tek bir ağ için birden fazla RPC destekliyoruz. Çelişkili bilgileri çözmek amacıyla en son RPC'niz var<PERSON>lan olarak seçilmiştir."}, "surveyConversion": {"message": "Anketimize katılın"}, "surveyTitle": {"message": "MetaMask'in geleceğini şekillendirin"}, "swap": {"message": "<PERSON><PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "Kaymayı ayarla"}, "swapAggregator": {"message": "Toplayıcı"}, "swapAllowSwappingOf": {"message": "$1 swap i<PERSON><PERSON>ine izin ver", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON><PERSON> edilen tutar"}, "swapAmountReceivedInfo": {"message": "<PERSON><PERSON>, alacağınız minimum tutardır. Kaymaya bağlı olarak daha fazla alabilirsiniz."}, "swapAndSend": {"message": "Swap Gerçekleştir ve Gönder"}, "swapAnyway": {"message": "Yine de <PERSON> gerçekleştir"}, "swapApproval": {"message": "Swap işlemleri için $1 onayla", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Bu swap i<PERSON><PERSON><PERSON> tamamlamak için $1 tane daha $2 gerekiyor", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Hala orada mısınız?"}, "swapAreYouStillThereDescription": {"message": "Devam etmek istediğinizde size en yeni kotaları göstermeye hazırız"}, "swapConfirmWithHwWallet": {"message": "Donanım cüzdanınızla on<PERSON>ın"}, "swapContinueSwapping": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> devam edin"}, "swapContractDataDisabledErrorDescription": {"message": "Ledger'ınızdaki Ethereum uygulamasında \"Ayarlar\" kısmına gidin ve sözleşme verilerine izin verin. Ardından swap işleminizi tekrar deneyin."}, "swapContractDataDisabledErrorTitle": {"message": "Sözleşme verileriniz Ledger'ınızda etkinleştirilmemiş"}, "swapCustom": {"message": "<PERSON><PERSON>"}, "swapDecentralizedExchange": {"message": "Merkeziyetsiz borsa"}, "swapDirectContract": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapEditLimit": {"message": "<PERSON><PERSON>"}, "swapEnableDescription": {"message": "Bu gereklidir ve MetaMask'e $1 için swap işlemi yapma izni verir.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Bu eylem swap için şunu yapacaktır: $1", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Bir tutar girin"}, "swapEstimatedNetworkFees": {"message": "<PERSON><PERSON><PERSON> ağ ü<PERSON>i"}, "swapEstimatedNetworkFeesInfo": {"message": "<PERSON><PERSON>, swap işleminizi tamamlamak için kullanılacak ağ ücretinin bir tahminidir. Gerçek miktar ağ koşullarına göre değişebilir."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "İşlemler başarısız olabilir ve size yardımcı olmak için buradayız. Bu sorun devam ederse daha fazla destek için $1 adresinden müşteri hizmetleri destek bölümü ile iletişime geçebilirsiniz.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "Swap başarısız oldu"}, "swapFetchingQuote": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapFetchingQuoteNofN": {"message": "$1 / $2 fiyat teklifi alınıyor", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Teklifler alınıyor..."}, "swapFetchingQuotesErrorDescription": {"message": "Hımmm... bir hata o<PERSON>. Te<PERSON>r deneyin veya sorun devam ederse müşteri hizmetleri destek bölümüyle iletişime geçin."}, "swapFetchingQuotesErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hata"}, "swapFromTo": {"message": "$1 ile $2 swap işlemi", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Gaz ücretleri tahmini olup ağ trafiği ve işlem karmaşıklığına göre dalgalanır."}, "swapGasFeesExplanation": {"message": "MetaMask, gaz ücretlerinden para kazanmaz. Bu ücretler tahminlerdir ve ağın yoğunluğuna ve işlemin karmaşıklığına göre değişebilir. $1 daha fazla bilgi edinin.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "<PERSON><PERSON><PERSON>", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "Gaz ücretleri hakkında daha fazla bilgi edinin"}, "swapGasFeesSplit": {"message": "Önceki ekrandaki gaz ücretleri bu iki işlem arasında bölünmüştür."}, "swapGasFeesSummary": {"message": "Gaz ücretleri, $1 ağında işlemleri gerçekleştiren kripto madencilerine ödenir. MetaMask gaz ücretlerinden herhangi bir kazanç elde etmemektedir.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "<PERSON><PERSON> <PERSON><PERSON>, g<PERSON><PERSON><PERSON>n veya alınan token tutarını ayarlayarak gaz ücretlerini dahil eder. Aktivite listenizde ayrı bir işlemde ETH alabilirsiniz."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "Gaz ücretleri hakkında daha fazla bilgi edinin"}, "swapHighSlippage": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>a"}, "swapIncludesGasAndMetaMaskFee": {"message": "Gaz ve %$1 MetaMask ücreti dahildir", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "%$1 MetaMask ücreti dahildir.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "Teklif $1% MetaMask ücretini yansıtır", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "%$1 MetaMask ücreti - $2 dahildir", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "Swap işlemleri hakkında daha fazla bilgi edinin"}, "swapLiquiditySourceInfo": {"message": "Dönüştürme oranlarını ve ağ ücretlerini karşılaştırmak için birden fazla likidite kaynağında (borsalar, toplayıcılar ve profesyonel piyasa yapıcıları) arama yaparız."}, "swapLowSlippage": {"message": "Düşük kayma"}, "swapMaxSlippage": {"message": "<PERSON><PERSON><PERSON> kayma"}, "swapMetaMaskFee": {"message": "MetaMask ücreti"}, "swapMetaMaskFeeDescription": {"message": "%$1 oranında bir ücret otomatik olarak bu teklife dahil edilmiştir. MetaMask'in likidite sağlayıcı bilgilerinin toplandığı yazılımı kullanma lisansı karşılığında ödersiniz.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 teklif.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "$1 içinde yeni teklifler sunulacak", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "$1 ile eşleşen token yok", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Bu işlem gerçekleştikten sonra $1 hesabınıza eklenecek.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "$4 $5 (~$6) için $1 $2 (~$3) swap gerçekleştirmek üzeresiniz.", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "~%$1 fiyat farkı", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "<PERSON><PERSON><PERSON>, piyasa fiyat verisinin mevcut olmaması nedeniyle belirlenememiştir. Swap işlemini gerçekleştirmeden önce lütfen almak üzere olduğunuz token tutarının sizin için uygun olduğunu onaylayın."}, "swapPriceUnavailableTitle": {"message": "<PERSON>am etmeden önce oranınızı kontrol edin"}, "swapProcessing": {"message": "Gerçekleştiriliyor"}, "swapQuoteDetails": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapQuoteNofM": {"message": "$1 / $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapQuotesExpiredErrorDescription": {"message": "En son oranları almak için lütfen yeni teklifler isteyin."}, "swapQuotesExpiredErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>ımı"}, "swapQuotesNotAvailableDescription": {"message": "Bu işlem yolu şu anda mevcut değil. <PERSON><PERSON><PERSON>, ağı veya token'ı değiştirmeyi deneyin ve en iyi seçeneği bulalım."}, "swapQuotesNotAvailableErrorDescription": {"message": "Lütfen tutarı veya kayma ayarlarını düzenlemeyi deneyin ve tekrar deneyin."}, "swapQuotesNotAvailableErrorTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> mev<PERSON> de<PERSON>"}, "swapRate": {"message": "<PERSON><PERSON>"}, "swapReceiving": {"message": "Alınıyor"}, "swapReceivingInfoTooltip": {"message": "Bu bir tahmindir. Gerçek miktar kaymaya göre belirlenecektir."}, "swapRequestForQuotation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapSelect": {"message": "Seç"}, "swapSelectAQuote": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "swapSelectAToken": {"message": "Token seç"}, "swapSelectQuotePopoverDescription": {"message": "Birden fazla likidite kaynağından alınmış tüm teklifler aşağıdadır."}, "swapSelectToken": {"message": "<PERSON><PERSON> seçin"}, "swapShowLatestQuotes": {"message": "En yeni kotaları göster"}, "swapSlippageHighDescription": {"message": "<PERSON><PERSON><PERSON> ka<PERSON> ($1%) çok yüksek kabul edilir ve kötü bir oranla sonuçlanabilir", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>a"}, "swapSlippageLowDescription": {"message": "Bu kadar düşük bir değer ($1%) başarısız bir swap ile sonuçlanabilir", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Düşük kayma"}, "swapSlippageNegativeDescription": {"message": "Kayma en az sıfır olmalıdır"}, "swapSlippageNegativeTitle": {"message": "Devam etmek için kaymayı artırın"}, "swapSlippageOverLimitDescription": {"message": "Kayma toleransı en fazla %15 olmalıdır. Daha yüksek olması kötü bir oranla sonuçlanır."}, "swapSlippageOverLimitTitle": {"message": "Kayma çok yüksek"}, "swapSlippagePercent": {"message": "%$1", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Emrinizi verdiğiniz zaman ile emrinizin onaylandığı zaman arasında geçen sürede fiyat değişirse buna \"kayma\" den<PERSON>. <PERSON><PERSON>, \"kayma toleransı\" ayarınızı aşarsa swap işleminiz otomatik olarak iptal edilir."}, "swapSlippageZeroDescription": {"message": "Daha az rekabetçi bir kota ile sonuçlanacak olan az sayıda sıfır kayma kotalı sağlayıcı var."}, "swapSlippageZeroTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kayma sunan sa<PERSON>ıcılar getiriliyor"}, "swapSource": {"message": "Likidite kaynağı"}, "swapSuggested": {"message": "Swap öneriliyor"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Swap işlemleri karmaşık ve zamana duyarlı işlemlerdir. Başarılı bir Swap için maliyet ve güven arasında iyi bir denge için bu gaz ücretini öneririz."}, "swapSwapFrom": {"message": "Şuradan swap gerçekleştir:"}, "swapSwapSwitch": {"message": "Token sıralamasını değiştir"}, "swapSwapTo": {"message": "Şununla swap gerçekleştir:"}, "swapToConfirmWithHwWallet": {"message": "don<PERSON><PERSON>m cüzdanınızla onaylamak için"}, "swapTokenAddedManuallyDescription": {"message": "Bu token'i $1 ile doğrulayın ve işlem yapmak istediğiniz token olduğundan emin olun.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "Token manuel o<PERSON>"}, "swapTokenAvailable": {"message": "$1 hesabınıza eklendi.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "$1 bakiyenizi alamadık", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Token bu bölgede swap işlemine uygun değildir"}, "swapTokenToToken": {"message": "$1 ile $2 swap gerçekleştir", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 sadece 1 kaynakta doğrulandı. İlerlemeden önce $2 üzerinde doğrulamayı deneyin.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "<PERSON><PERSON><PERSON> token olma <PERSON> var"}, "swapTokenVerifiedSources": {"message": "$1 kaynakları tarafından onaylandı. $2 üzerinde doğrulayın.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1, en fazla $2 ondal<PERSON>k basamağına izin verir", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "İşlem tamamlandı"}, "swapTwoTransactions": {"message": "2 işlem"}, "swapUnknown": {"message": "Bilinmiyor"}, "swapZeroSlippage": {"message": "%0 <PERSON><PERSON>"}, "swapsMaxSlippage": {"message": "<PERSON><PERSON> to<PERSON>"}, "swapsNotEnoughToken": {"message": "Yeterli $1 yok", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Aktivitede görüntüle"}, "switch": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "switchEthereumChainConfirmationDescription": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, MetaMask'te seçili bir ağı önceden eklenmiş bir ağ ile değiştirecek:"}, "switchEthereumChainConfirmationTitle": {"message": "Bu sitenin ağı değiştirmesine izin ver?"}, "switchInputCurrency": {"message": "Giriş para birimini değiştir"}, "switchNetwork": {"message": "<PERSON><PERSON><PERSON>ğiştir"}, "switchNetworks": {"message": "<PERSON><PERSON>"}, "switchToNetwork": {"message": "$1 ağına geçin", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Bu hesaba geç"}, "switchedNetworkToastDecline": {"message": "<PERSON><PERSON><PERSON>"}, "switchedNetworkToastMessage": {"message": "$1, $2 üzerinde aktif", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Şu anda $1 ağını kullanıyorsunuz", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "<PERSON><PERSON>ştirmek bekleyen tüm onayları iptal eder"}, "symbol": {"message": "Sembol"}, "symbolBetweenZeroTwelve": {"message": "Sembol en fazla 11 karakter olmalıdır."}, "tenPercentIncreased": {"message": "%10 artış"}, "terms": {"message": "Kullanım şartları"}, "termsOfService": {"message": "Hizmet şartları"}, "termsOfUseAgreeText": {"message": " MetaMask ve tüm özelliklerini kullanımım için geçerli olan Kullanım Şartları bölümünü kabul ediyorum"}, "termsOfUseFooterText": {"message": "<PERSON><PERSON><PERSON> bölümleri okumak için lütfen kaydırın"}, "termsOfUseTitle": {"message": "Kullanım Şartları bölümümüz güncellendi"}, "testNetworks": {"message": "Test ağları"}, "testnets": {"message": "<PERSON><PERSON>'ler"}, "theme": {"message": "<PERSON><PERSON>"}, "themeDescription": {"message": "Tercih ettiğin MetaMask temasını seç."}, "thirdPartySoftware": {"message": "Üçüncü taraf ya<PERSON>ı<PERSON>ı<PERSON> uyarısı", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Zaman"}, "tipsForUsingAWallet": {"message": "Cüzdan kullanma ipuçları"}, "tipsForUsingAWalletDescription": {"message": "Token eklemek web3'ü kullanmanın daha fazla yolunun kilidini açar."}, "to": {"message": "<PERSON><PERSON>"}, "toAddress": {"message": "Alıcı: $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Şifre çözmek ve daha okunabilir işlem verileri göstermek için 4byte.directory ve Sourcify hizmetlerini kullanıyoruz. Bu da, bekleyen ve geçmiş işlemlerinin sonucunu anlamanıza yardımcı olur ancak IP adresinizin paylaşılmasıyla sonuçlanabilir."}, "token": {"message": "Token"}, "tokenAddress": {"message": "<PERSON><PERSON> adresi"}, "tokenAlreadyAdded": {"message": "Token zaten eklenmiş."}, "tokenAutoDetection": {"message": "Otomatik token algılama"}, "tokenContractAddress": {"message": "<PERSON><PERSON> s<PERSON>şme ad<PERSON>i"}, "tokenDecimal": {"message": "Token ondalığı"}, "tokenDecimalFetchFailed": {"message": "Token ondalığı gereklidir. Şurada bulabilirsiniz: $1"}, "tokenDetails": {"message": "Token bilgileri"}, "tokenFoundTitle": {"message": "1 yeni token bulundu"}, "tokenId": {"message": "<PERSON><PERSON>"}, "tokenList": {"message": "Token listeleri:"}, "tokenMarketplace": {"message": "Token pazar yeri"}, "tokenScamSecurityRisk": {"message": "token dolandırıcılıkları ve güvenlik riskleri"}, "tokenStandard": {"message": "Token standardı"}, "tokenSymbol": {"message": "Token sembolü"}, "tokens": {"message": "<PERSON><PERSON><PERSON>"}, "tokensFoundTitle": {"message": "$1 yeni token bulundu", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tooltipApproveButton": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "tooltipSatusConnected": {"message": "bağlı"}, "tooltipSatusConnectedUpperCase": {"message": "Bağlandı"}, "tooltipSatusNotConnected": {"message": "bağlı değil"}, "total": {"message": "Toplam"}, "totalVolume": {"message": "<PERSON>lam hacim"}, "transaction": {"message": "işlem"}, "transactionCancelAttempted": {"message": "$2 itibariyle $1 tahmini gaz ücretiyle işlem iptali denemesinde bulunuldu"}, "transactionCancelSuccess": {"message": "İşlem $2 itibariyle başarılı bir şekilde iptal edildi"}, "transactionConfirmed": {"message": "İşlem $2 itibariyle onaylandı."}, "transactionCreated": {"message": "İşlem $2 itibariyle $1 değeriyle oluşturuldu."}, "transactionDataFunction": {"message": "<PERSON><PERSON><PERSON>"}, "transactionDetailGasHeading": {"message": "<PERSON><PERSON><PERSON>az <PERSON>"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Tutar + ücretler"}, "transactionDropped": {"message": "İşlem şu saatte bırakıldı: $2."}, "transactionError": {"message": "İşlem Hatası. Sözleşme kodundan istisna atıldı."}, "transactionErrorNoContract": {"message": "Sözleşmeli olmayan bir adreste bir fonksiyon çağrılmaya çalışıldı."}, "transactionErrored": {"message": "İşlem bir hatayla karşılaştı."}, "transactionFlowNetwork": {"message": "Ağ"}, "transactionHistoryBaseFee": {"message": "<PERSON><PERSON> (GEWI)"}, "transactionHistoryL1GasLabel": {"message": "Toplam L1 gaz ücreti"}, "transactionHistoryL2GasLimitLabel": {"message": "L2 gaz limiti"}, "transactionHistoryL2GasPriceLabel": {"message": "L2 gaz fiyatı"}, "transactionHistoryMaxFeePerGas": {"message": "Gaz başına maks. ücret"}, "transactionHistoryPriorityFee": {"message": "<PERSON><PERSON><PERSON>ti (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Toplam gaz ücreti"}, "transactionIdLabel": {"message": "İşlem Kimliği", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Bu işlem şunu içerir: $1."}, "transactionResubmitted": {"message": "İşlem, $2 itibariyle $1 olarak artırılan tahmini gaz ücreti ile yeniden gönderildi"}, "transactionSettings": {"message": "İşlem ayarları"}, "transactionSubmitted": {"message": "İşlem $2 itibariyle tahmini $1 gaz ücreti ile gönderildi."}, "transactionTotalGasFee": {"message": "Toplam gaz ücreti", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "İşlem $2 itibariyle güncellendi."}, "transactions": {"message": "İşlemler"}, "transfer": {"message": "Transfer et"}, "transferCrypto": {"message": "Kripto transfer et"}, "transferFrom": {"message": "Transfer kaynağı:"}, "transferRequest": {"message": "Transfer talebi"}, "trillionAbbreviation": {"message": "T", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Ledger'ınıza bağlanırken sorun yaşıyoruz. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Donanım cüzdanı bağlantı kılavuzumuzu inceleyip tekrar deneyin.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Firefox'un en son sürümündeyseniz Firefox'un U2F desteğini bırakması ile ilgili sorun yaşıyor olabilirsiniz. Bu sorunun nasıl düzeltileceğini $1 öğrenin.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "bura<PERSON>", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "$1 ile bağlantı kurmada sorun yaşıyoruz, $2 kısmını inceleyip tekrar deneyin.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "MetaMask başlatılırken bir sorun oldu. Bu hata sürekli bir hata olmayabilir; bu yüzden uzantıyı yeniden başlatmayı dene."}, "tryAgain": {"message": "<PERSON><PERSON><PERSON> dene"}, "turnOff": {"message": "Ka<PERSON><PERSON>"}, "turnOffMetamaskNotificationsError": {"message": "Bildirimler devre dışı bırakılırken bir hata oluştu. Lütfen daha sonra tekrar deneyin."}, "turnOn": {"message": "Aç"}, "turnOnMetamaskNotifications": {"message": "Bildirimleri aç"}, "turnOnMetamaskNotificationsButton": {"message": "Aç"}, "turnOnMetamaskNotificationsError": {"message": "Bildirimler oluşturulurken bir hata oluştu. Lütfen daha sonra tekrar deneyin."}, "turnOnMetamaskNotificationsMessageFirst": {"message": "Bildirimler ile cüzdanınızda neler olduğundan haberdar olun."}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "<PERSON><PERSON><PERSON><PERSON>."}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "Bu özelliği kullanırken gizliliğinizi nasıl koruduğumuzu öğrenin."}, "turnOnMetamaskNotificationsMessageSecond": {"message": "Cüzdan bildirimlerini kullanmak için cihazlarınız genelinde bazı ayarları senkronize etmek üzere bir profil kullanırız. $1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "Bildirimleri dilediğiniz zaman $1 kısmında kapatabilirsiniz"}, "turnOnTokenDetection": {"message": "Gelişmiş token algılamayı açın"}, "tutorial": {"message": "Öğretici"}, "twelveHrTitle": {"message": "12 sa.:"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "Onaylanmadı"}, "unexpectedBehavior": {"message": "Bu davran<PERSON>ş beklenmeyen bir davranıştı ve hesaplarınız düzgün bir şekilde geri yüklenmiş olsa bile hata olarak bildirilmelidir. Aşağıdaki bağlantıyı kullanarak MetaMask'e hata raporu gönderin."}, "units": {"message": "birim"}, "unknown": {"message": "Bilinmeyen"}, "unknownCollection": {"message": "İsimsiz koleksiyon"}, "unknownNetworkForKeyEntropy": {"message": "Bilinmeyen ağ", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Hata: Bu QR kodunu tanımlayamadık"}, "unlimited": {"message": "Sınırsız"}, "unlock": {"message": "<PERSON><PERSON><PERSON>"}, "unpin": {"message": "Sabitlemeyi kaldır"}, "unrecognizedChain": {"message": "Bu özel ağ tanınmadı", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "NFT (ERC-721) tokenlerin gönderil<PERSON>i şu anda desteklenmiyor", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "Bu token'ın USD cinsinden fiyatı son derece de<PERSON> olup bununla etkileşimde bulunulursa büyük ölçüde değer kaybetmeye yönelik yüksek risk gösteriyor."}, "unstableTokenPriceTitle": {"message": "İstikrarsız Token Fiyatı"}, "upArrow": {"message": "yukarı ok"}, "update": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "updateEthereumChainConfirmationDescription": {"message": "Bu site, varsayılan ağ URL adresinizi güncellemeyi talep ediyor. Varsayılanları ve ağ bilgilerini dilediğiniz zaman düzenleyebilirsiniz."}, "updateNetworkConfirmationTitle": {"message": "Güncelle: $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Bilgilerinizi güncelleyin veya"}, "updateRequest": {"message": "<PERSON><PERSON>"}, "updatedRpcForNetworks": {"message": "Ağ RPC'le<PERSON>"}, "uploadDropFile": {"message": "Dosyanızı buraya sürükleyin"}, "uploadFile": {"message": "<PERSON><PERSON><PERSON>"}, "urlErrorMsg": {"message": "URL adresleri için uygun HTTP/HTTPS ön eki gerekir."}, "use4ByteResolution": {"message": "Akıllı sözleşmelerin şifresini çöz"}, "useMultiAccountBalanceChecker": {"message": "<PERSON><PERSON> hesap b<PERSON><PERSON>"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Hesap bakiyesi taleplerini toplu ödeme olarak gerçekleştirerek daha hızlı bakiye güncellemeleri alın. Bu, hesap bakiyelerinizi bir arada almanızı sağlar, b<PERSON><PERSON>ce gelişmiş bir deneyim için daha hızlı güncellemeler alacaksınız. Bu özellik kapalı olduğunda üçüncü tarafların sizin hesaplarınızı birbirleriyle ilişkilendirme olasılığı daha düşük olur."}, "useNftDetection": {"message": "NFT'leri otomatik algıla"}, "useNftDetectionDescriptionText": {"message": "MetaMask'in üçüncü taraf hizmetleri kullanarak size ait olan NFT'leri eklemesine izin verin. NFT'leri otomatik algılama, IP adresinizi ve hesap adresinizi bu hizmetlerle paylaşır. Bu özelliğin etkinleştirilmesi IP adresinizi Ethereum adresinizle ilişkilendirebilir ve dolandırıcılar tarafından airdrop'u gerçekleştirilen sahte NFT'leri gösterebilir. Bu riski önlemek için tokenleri elle ekleyebilirsiniz."}, "usePhishingDetection": {"message": "Kimlik avı algılama kullan"}, "usePhishingDetectionDescription": {"message": "Ethereum kullanıcılarını hedefleyen kimlik avı alanları için bir uyarı gör<PERSON><PERSON><PERSON>ler"}, "useSafeChainsListValidation": {"message": "Ağ bilgileri kontrolü"}, "useSafeChainsListValidationDescription": {"message": "MetaMask, doğru ve standartlaştırılmış ağ bilgilerini göstermek için $1 adlı üçüncü taraf bir hizmet kullanır. Bu, kötü amaçlı veya yanlış ağa bağlanma şansınızı düşürür. Bu özelliği kullanırken IP adresiniz chainid.network ile paylaşılır."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "Hesabına gö<PERSON>ilen tokenlerin otomatik olarak görü<PERSON><PERSON>lenmesi, tokenin görüntülerini almak için üçüncü taraf sunucularla iletişimi içerir. Bu servislerin IP adresine erişimi olacaktır."}, "usedByClients": {"message": "Çeşitli müşteriler tarafından kullanılıyor"}, "userName": {"message": "Kullanıcı adı"}, "userOpContractDeployError": {"message": "Akıllı sözleşme hesabından sözleşme kurulumu desteklenmiyor"}, "version": {"message": "S<PERSON>r<PERSON><PERSON>"}, "view": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "viewActivity": {"message": "Aktiviteyi görü<PERSON>le"}, "viewAllQuotes": {"message": "tüm tek<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "viewContact": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "viewDetails": {"message": "Ayrıntıları görüntüle"}, "viewMore": {"message": "Daha fazlasını gör<PERSON>üle"}, "viewOnBlockExplorer": {"message": "Blok gezgininde görüntüle"}, "viewOnCustomBlockExplorer": {"message": "$1 ögesini $2 üzerinde görüntüle", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Etherscan'de $1 görü<PERSON>üle", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "viewOnOpensea": {"message": "Opensea'de gör<PERSON>le"}, "viewSolanaAccount": {"message": "Solana hesabını gö<PERSON><PERSON>le"}, "viewTransaction": {"message": "İşlemi görü<PERSON>üle"}, "viewinExplorer": {"message": "Explorer'da $1 g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "<PERSON><PERSON> ziyaret edin"}, "visitSupportDataConsentModalAccept": {"message": "<PERSON><PERSON><PERSON>"}, "visitSupportDataConsentModalDescription": {"message": "MetaMask Tanımlayıcınızı ve uygulama sürümünüzü Destek Merkezimizle paylaşmak ister misiniz? Probleminizi daha iyi bir şekilde çözmemize yardımcı olabilir ancak isteğe bağlıdır."}, "visitSupportDataConsentModalReject": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "visitSupportDataConsentModalTitle": {"message": "Cihaz bilgilerini destek ile paylaş"}, "visitWebSite": {"message": "Web sitemizi ziya<PERSON> et"}, "wallet": {"message": "Cüzdan"}, "walletConnectionGuide": {"message": "donanım cüzdanı bağlantı kılavuzumuz"}, "wantToAddThisNetwork": {"message": "Bu ağı eklemek istiyor musunuz?"}, "wantsToAddThisAsset": {"message": "<PERSON><PERSON>, aşağ<PERSON><PERSON>i varlığın cüzdanınıza eklenmesine izin verir."}, "warning": {"message": "Uyarı"}, "warningFromSnap": {"message": "$1 uyarısı", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "Bu seçeneği açtığınızda size genel adres veya ENS adı ile Ethereum hesaplarını izleme yeteneği verilir. Bu Beta özellik hakkında geri bildirim için lütfen bu $1 formunu doldurun.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Ethereum Hesaplarını İzle (Beta)"}, "watchOutMessage": {"message": "Şuraya dikkat edin: $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Mevcut web sitesinin kaldırılmış olan window.web3 API'sini kullanmaya çalıştığını fark ettik. Site bozuk görünüyorsa daha fazla bilgi için lütfen $1 bağlantısına tıklayın.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "web siteleri", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "welcomeToMetaMask": {"message": "Başlayalım"}, "whatsThis": {"message": "Bu nedir?"}, "willApproveAmountForBridging": {"message": "<PERSON><PERSON>, köprü için şunu onaylayacaktır: $1."}, "willApproveAmountForBridgingHardware": {"message": "Donanım cüzdanınızda iki işlemi onaylamanız gerekecektir."}, "withdrawing": {"message": "Çekiliyor"}, "wrongNetworkName": {"message": "Kayıtlarımıza göre ağ adı bu zincir kimliği ile doğru şekilde eşleşmiyor olabilir."}, "yes": {"message": "<PERSON><PERSON>"}, "you": {"message": "Siz"}, "youDeclinedTheTransaction": {"message": "İşlemi reddettiniz."}, "youNeedToAllowCameraAccess": {"message": "Bu özelliği kullanmak için kamera erişimine izin vermeniz gerekir."}, "youReceived": {"message": "Aldığınız", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Hesaplarınız"}, "yourActivity": {"message": "Aktiviteniz"}, "yourBalance": {"message": "Bakiyeniz"}, "yourNFTmayBeAtRisk": {"message": "NFT'niz tehlikede olabilir"}, "yourNetworks": {"message": "Ağlarınız"}, "yourPrivateSeedPhrase": {"message": "<PERSON><PERSON><PERSON>arma İfadeniz"}, "yourTransactionConfirmed": {"message": "İşlem zaten onaylandı"}, "yourTransactionJustConfirmed": {"message": "Blok zinciri üzerinde onaylanmadan işleminizi iptal edemedik."}, "yourWalletIsReady": {"message": "Cüzdanınız hazır"}}