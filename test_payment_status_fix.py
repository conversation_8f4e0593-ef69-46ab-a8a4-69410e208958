#!/usr/bin/env python3
"""
测试支付状态修复效果
验证二次扫码问题是否解决
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append('c:/Users/<USER>/Desktop/analyze_saas/saas_platform')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'saas_platform.settings')
django.setup()

from django.utils import timezone
from apps.payments.models import NowPaymentsPayment
from apps.orders.models import Order
from django.contrib.auth import get_user_model

User = get_user_model()

def test_payment_expiry_handling():
    """测试支付过期处理逻辑"""
    print("🧪 测试支付过期处理逻辑...")
    
    try:
        # 创建测试用户
        user, created = User.objects.get_or_create(
            username='test_payment_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # 创建测试订单
        order, created = Order.objects.get_or_create(
            order_sn='TEST_ORDER_001',
            defaults={
                'user': user,
                'total_amount': 100.00,
                'status': 'pending'
            }
        )
        
        # 创建过期的支付记录
        old_time = timezone.now() - timedelta(minutes=35)  # 35分钟前
        
        payment = NowPaymentsPayment.objects.create(
            user=user,
            order_id=order.order_sn,
            payment_id='test_payment_001',
            price_amount=100.00,
            pay_currency='FDUSDBSC',
            pay_address='0x1234567890abcdef',
            payment_status='waiting',
            created_at=old_time
        )
        
        print(f"✅ 创建测试支付记录: {payment.payment_id}")
        print(f"📅 创建时间: {payment.created_at}")
        print(f"⏰ 当前时间: {timezone.now()}")
        
        # 检查是否过期
        expiry_time = payment.created_at + timedelta(minutes=30)
        is_expired = timezone.now() > expiry_time
        
        print(f"⏳ 过期时间: {expiry_time}")
        print(f"❓ 是否过期: {is_expired}")
        
        if is_expired:
            print("✅ 过期检测正常工作")
            payment.payment_status = 'expired'
            payment.save()
            print("✅ 支付状态已更新为过期")
        else:
            print("❌ 过期检测异常")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_duplicate_payment_handling():
    """测试重复支付处理逻辑"""
    print("\n🧪 测试重复支付处理逻辑...")
    
    try:
        user = User.objects.get(username='test_payment_user')
        
        # 查找现有的等待中支付
        existing_payments = NowPaymentsPayment.objects.filter(
            order_id='TEST_ORDER_001',
            pay_currency='FDUSDBSC',
            payment_status__in=['waiting', 'confirming', 'confirmed', 'sending', 'partially_paid']
        )
        
        print(f"🔍 找到 {existing_payments.count()} 个活跃支付记录")
        
        for payment in existing_payments:
            expiry_time = payment.created_at + timedelta(minutes=30)
            is_expired = timezone.now() > expiry_time
            print(f"💳 支付 {payment.payment_id}: 状态={payment.payment_status}, 过期={is_expired}")
            
            if is_expired:
                payment.payment_status = 'expired'
                payment.save()
                print(f"✅ 支付 {payment.payment_id} 已标记为过期")
        
        # 重新查询活跃支付
        active_payments = NowPaymentsPayment.objects.filter(
            order_id='TEST_ORDER_001',
            pay_currency='FDUSDBSC',
            payment_status__in=['waiting', 'confirming', 'confirmed', 'sending', 'partially_paid']
        )
        
        print(f"🔍 清理后剩余 {active_payments.count()} 个活跃支付记录")
        
        if active_payments.count() == 0:
            print("✅ 过期支付清理成功，可以创建新支付")
        else:
            print("⚠️ 仍有活跃支付记录")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        # 删除测试支付记录
        NowPaymentsPayment.objects.filter(order_id='TEST_ORDER_001').delete()
        print("✅ 测试支付记录已删除")
        
        # 删除测试订单
        Order.objects.filter(order_sn='TEST_ORDER_001').delete()
        print("✅ 测试订单已删除")
        
        # 删除测试用户
        User.objects.filter(username='test_payment_user').delete()
        print("✅ 测试用户已删除")
        
    except Exception as e:
        print(f"⚠️ 清理数据时出错: {e}")

if __name__ == '__main__':
    print("🚀 开始测试支付状态修复效果...")
    print("=" * 50)
    
    # 运行测试
    test1_result = test_payment_expiry_handling()
    test2_result = test_duplicate_payment_handling()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"支付过期处理: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"重复支付处理: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！支付状态修复生效")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    # 清理测试数据
    cleanup_test_data()
    
    print("\n✅ 测试完成")
