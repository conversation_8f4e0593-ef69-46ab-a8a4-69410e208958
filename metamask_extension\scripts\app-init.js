!function e(s,o,t){function r(i,c){if(!o[i]){if(!s[i]){var a="function"==typeof require&&require;if(!c&&a)return a(i,!0);if(n)return n(i,!0);var l=new Error("Cannot find module '"+i+"'");throw l.code="MODULE_NOT_FOUND",l}var m=o[i]={exports:{}};s[i][0].call(m.exports,(function(e){return r(s[i][1][e]||e)}),m,m.exports,e,s,o,t)}return o[i].exports}for(var n="function"==typeof require&&require,i=0;i<t.length;i++)r(t[i]);return r}({1:[function(e,s,o){"use strict";var t=e("../../shared/lib/promise-with-resolvers");let r=!1;const{chrome:n}=globalThis;globalThis.stateHooks=globalThis.stateHooks||{};const i=[];function c(...e){try{const s=(new Date).getTime();importScripts(...e);const o=(new Date).getTime();return i.push({name:e[0],value:o-s,children:[],startTime:s,endTime:o}),!0}catch(e){console.error(e)}return!1}function a(){if(r)return;r=!0;const e=[],s=s=>{e.push(s)},o=Date.now();s("../scripts/sentry-install.js");!self.document||s("../scripts/snow.js"),s("../scripts/use-snow.js");s("../scripts/runtime-lavamoat.js"),s("../scripts/lockdown-more.js"),s("../scripts/policy-load.js");"../common-0.js,../common-1.js,../common-2.js,../common-3.js,../common-4.js,../common-5.js,../common-6.js,../common-7.js,../common-8.js,../common-9.js,../common-10.js,../common-11.js,../common-12.js,../common-13.js,../common-14.js,../background-0.js,../background-1.js,../background-2.js,../background-3.js,../background-4.js,../background-5.js,../background-6.js,../background-7.js,../background-8.js,../background-9.js".split(",").forEach((e=>s(e))),c(...e);Date.now();console.log("SCRIPTS IMPORT COMPLETE in Seconds: "+(Date.now()-o)/1e3)}self.addEventListener("install",a),n.runtime.onMessage.addListener((()=>(a(),!1))),"activated"===self.serviceWorker.state&&a();const l=(0,t.withResolvers)();globalThis.stateHooks.onInstalledListener=l.promise,n.runtime.onInstalled.addListener((function e(s){n.runtime.onInstalled.removeListener(e),l.resolve(s),delete globalThis.stateHooks.onInstalledListener})),(async()=>{try{await n.scripting.registerContentScripts([{id:"inpage",matches:["file://*/*","http://*/*","https://*/*"],js:["scripts/inpage.js"],runAt:"document_start",world:"MAIN",allFrames:!0}])}catch(e){console.warn(`Dropped attempt to register inpage content script. ${e}`)}})()},{"../../shared/lib/promise-with-resolvers":2}],2:[function(e,s,o){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.withResolvers=void 0;o.withResolvers=void 0===Promise.withResolvers?()=>{let e,s;return{promise:new Promise(((o,t)=>{e=o,s=t})),resolve:e,reject:s}}:Promise.withResolvers.bind(Promise)},{}]},{},[1]);