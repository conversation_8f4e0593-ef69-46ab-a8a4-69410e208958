{"QRHardwareInvalidTransactionTitle": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareMismatchedSignId": {"message": "Données de transaction incompatibles. Veuillez vérifier les détails de la transaction."}, "QRHardwarePubkeyAccountOutOfRange": {"message": "Il n’y a plus de comptes. Si vous souhaitez accéder à un autre compte non répertorié ci-dessous, veuillez reconnecter votre portefeuille matériel et le sélectionner."}, "QRHardwareScanInstructions": {"message": "Placez le code QR devant votre caméra. L’écran est flou, mais cela n’affectera pas la lecture."}, "QRHardwareSignRequestCancel": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareSignRequestDescription": {"message": "Après avoir signé avec votre portefeuille, cliquez sur « Obtenir la signature » pour recevoir la signature"}, "QRHardwareSignRequestGetSignature": {"message": "<PERSON><PERSON><PERSON><PERSON> la <PERSON>"}, "QRHardwareSignRequestSubtitle": {"message": "Veuillez scanner le code QR avec votre portefeuille"}, "QRHardwareSignRequestTitle": {"message": "De<PERSON><PERSON> la signature"}, "QRHardwareUnknownQRCodeTitle": {"message": "<PERSON><PERSON><PERSON>"}, "QRHardwareUnknownWalletQRCode": {"message": "Code QR invalide. <PERSON><PERSON>z le code QR de synchronisation du portefeuille matériel."}, "QRHardwareWalletImporterTitle": {"message": "Scannez le code QR"}, "QRHardwareWalletSteps1Description": {"message": "Vous pouvez choisir parmi une liste de partenaires officiels soutenant le code QR ci-dessous."}, "QRHardwareWalletSteps1Title": {"message": "Connectez votre portefeuille électronique QR"}, "QRHardwareWalletSteps2Description": {"message": "<PERSON><PERSON>"}, "SrpListHideAccounts": {"message": "Masquer $1 comptes", "description": "$1 is the number of accounts"}, "SrpListHideSingleAccount": {"message": "Masquer 1 compte"}, "SrpListShowAccounts": {"message": "Afficher $1 comptes", "description": "$1 is the number of accounts"}, "SrpListShowSingleAccount": {"message": "Afficher 1 compte"}, "about": {"message": "À propos"}, "accept": {"message": "Accepter"}, "acceptTermsOfUse": {"message": "J’ai lu et j’accepte les $1", "description": "$1 is the `terms` message"}, "accessingYourCamera": {"message": "Accès à votre appareil photo..."}, "account": {"message": "<PERSON><PERSON><PERSON>"}, "accountActivity": {"message": "Activité du compte"}, "accountActivityText": {"message": "Sélectionnez les comptes pour lesquels vous souhaitez recevoir des notifications :"}, "accountDetails": {"message": "<PERSON>é<PERSON> du compte"}, "accountIdenticon": {"message": "Identicon de compte"}, "accountIsntConnectedToastText": {"message": "$1 n’est pas connecté à $2"}, "accountName": {"message": "Nom du compte"}, "accountNameDuplicate": {"message": "Ce nom de compte existe déjà", "description": "This is an error message shown when the user enters a new account name that matches an existing account name"}, "accountNameReserved": {"message": "Ce nom de compte est réservé", "description": "This is an error message shown when the user enters a new account name that is reserved for future use"}, "accountOptions": {"message": "Options du compte"}, "accountPermissionToast": {"message": "Les autorisations accordées au compte ont été mises à jour"}, "accountSelectionRequired": {"message": "Vous devez sélectionner un compte !"}, "accountTypeNotSupported": {"message": "Ce type de compte n’est pas pris en charge"}, "accounts": {"message": "<PERSON><PERSON><PERSON>"}, "accountsConnected": {"message": "Comptes connectés"}, "accountsPermissionsTitle": {"message": "Consultez vos comptes et suggérez des transactions"}, "accountsSmallCase": {"message": "comptes"}, "active": {"message": "Actif"}, "activity": {"message": "Activité"}, "activityLog": {"message": "Log d’activité"}, "add": {"message": "Ajouter"}, "addACustomNetwork": {"message": "Ajouter un réseau personnalis<PERSON>"}, "addANetwork": {"message": "A<PERSON>ter un réseau"}, "addANickname": {"message": "Ajouter un pseudo"}, "addAUrl": {"message": "Ajouter une URL"}, "addAccount": {"message": "Ajouter un compte"}, "addAccountFromNetwork": {"message": "Ajouter un compte $1", "description": "$1 is the network name, e.g. Bitcoin or Solana"}, "addAccountToMetaMask": {"message": "Ajouter un compte à MetaMask"}, "addAcquiredTokens": {"message": "Ajouter les jetons que vous avez acquis par l’intermédiaire de MetaMask"}, "addAlias": {"message": "Ajouter un alias"}, "addBitcoinAccountLabel": {"message": "Compte Bitcoin"}, "addBlockExplorer": {"message": "Ajouter un explorateur de blocs"}, "addBlockExplorerUrl": {"message": "Ajouter une URL d’explorateur de blocs"}, "addContact": {"message": "Ajouter un contact"}, "addCustomNetwork": {"message": "Ajouter un réseau personnalis<PERSON>"}, "addEthereumChainWarningModalHeader": {"message": "N’ajoutez ce fournisseur de RPC que si vous lui faites confiance. $1", "description": "$1 is addEthereumChainWarningModalHeaderPartTwo passed separately so that it can be bolded"}, "addEthereumChainWarningModalHeaderPartTwo": {"message": "Un fournisseur malveillant peut mentir quant à l’état de la blockchain et enregistrer votre activité sur le réseau."}, "addEthereumChainWarningModalListHeader": {"message": "Il est important que votre fournisseur soit digne de confiance, car il peut :"}, "addEthereumChainWarningModalListPointOne": {"message": "Voir vos comptes et les associer à votre adresse IP"}, "addEthereumChainWarningModalListPointThree": {"message": "Afficher le solde des comptes et les opérations réalisées et inscrites sur le registre de la blockchain"}, "addEthereumChainWarningModalListPointTwo": {"message": "Diffuser des informations sur vos transactions"}, "addEthereumChainWarningModalTitle": {"message": "Vous êtes en train d’ajouter un nouveau fournisseur de RPC pour le réseau principal de la blockchain Ethereum"}, "addEthereumWatchOnlyAccount": {"message": "Surveiller un compte Ethereum (Bêta)"}, "addFriendsAndAddresses": {"message": "Ajou<PERSON>z <PERSON>ment des amis et des adresses de confiance"}, "addHardwareWalletLabel": {"message": "Portefeuille matériel"}, "addIPFSGateway": {"message": "Ajoutez votre passerelle IPFS préférée"}, "addImportAccount": {"message": "Ajouter un compte ou un portefeuille matériel"}, "addMemo": {"message": "Ajouter un mémo"}, "addNetwork": {"message": "A<PERSON>ter un réseau"}, "addNetworkConfirmationTitle": {"message": "Ajouter $1", "description": "$1 represents network name"}, "addNewAccount": {"message": "Ajouter un nouveau compte Ethereum"}, "addNewEthereumAccountLabel": {"message": "Compte Ethereum"}, "addNewSolanaAccountLabel": {"message": "<PERSON><PERSON><PERSON>"}, "addNft": {"message": "Ajouter un NFT"}, "addNfts": {"message": "Ajouter des NFT"}, "addNonEvmAccount": {"message": "Ajouter un compte $1", "description": "$1 is the non EVM network where the account is going to be created, e.g. Bitcoin or Solana"}, "addNonEvmAccountFromNetworkPicker": {"message": "Pour activer le réseau $1, vous devez créer un compte $2.", "description": "$1 is the non EVM network where the account is going to be created, e.g. Solana Mainnet or Solana Devnet. $2 is the account type, e.g. Bitcoin or Solana"}, "addRpcUrl": {"message": "Ajouter l’URL du RPC"}, "addSnapAccountToggle": {"message": "Activer « Ajouter un Snap de compte (bêta) »"}, "addSnapAccountsDescription": {"message": "En activant cette fonctionnalité, vous aurez la possibilité d’ajouter les nouveaux Snaps de compte bêta directement à partir de votre liste de comptes. Veuillez noter que les Snaps de compte sont des services fournis par des tiers."}, "addSuggestedNFTs": {"message": "Ajouter les NFT suggérés"}, "addSuggestedTokens": {"message": "Ajouter les jetons suggérés"}, "addToken": {"message": "<PERSON><PERSON><PERSON> le jeton"}, "addTokenByContractAddress": {"message": "Vous n’arrivez pas à trouver un jeton ? Vous pouvez ajouter manuellement n’importe quel jeton en copiant et collant son adresse. Les adresses des contrats de jetons sont disponibles sur $1", "description": "$1 is a blockchain explorer for a specific network, e.g. Etherscan for Ethereum"}, "addUrl": {"message": "Ajouter l'URL"}, "addingAccount": {"message": "Ajouter un compte"}, "addingCustomNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "additionalNetworks": {"message": "Réseaux supplémentaires"}, "address": {"message": "<PERSON><PERSON><PERSON>"}, "addressCopied": {"message": "Adresse copiée !"}, "addressMismatch": {"message": "Inadéquation de l’adresse du site"}, "addressMismatchOriginal": {"message": "URL actuelle : $1", "description": "$1 replaced by origin URL in confirmation request"}, "addressMismatchPunycode": {"message": "Version Punycode : $1", "description": "$1 replaced by punycode version of the URL in confirmation request"}, "advanced": {"message": "Paramètres avancés"}, "advancedBaseGasFeeToolTip": {"message": "Lorsque votre transaction est intégrée au bloc, toute différence entre vos frais de base maximaux et les frais de base réels vous sera remboursée. Le montant total est calculé comme suit : frais de base maximaux (en GWEI) × limite de carburant."}, "advancedDetailsDataDesc": {"message": "<PERSON><PERSON><PERSON>"}, "advancedDetailsHexDesc": {"message": "Hexa"}, "advancedDetailsNonceDesc": {"message": "<PERSON><PERSON>"}, "advancedDetailsNonceTooltip": {"message": "Il s’agit du nombre de transactions d’un compte. Le nonce de la première transaction est 0 et il augmente d’une manière séquentielle."}, "advancedGasFeeDefaultOptIn": {"message": "Enregistrer ces valeurs comme valeurs par défaut pour le réseau $1.", "description": "$1 is the current network name."}, "advancedGasFeeModalTitle": {"message": "Frais de carburant avancés"}, "advancedGasPriceTitle": {"message": "Prix du gaz"}, "advancedPriorityFeeToolTip": {"message": "Les frais de priorité (aussi appelés « pourboire du mineur ») vont directement aux mineurs et les incitent à accorder la priorité à votre transaction."}, "airDropPatternDescription": {"message": "L’historique du jeton sur la chaîne révèle des cas antérieurs d’activités d’airdrop suspectes."}, "airDropPatternTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>airdrop"}, "airgapVault": {"message": "Coffre-fort AirGap"}, "alert": {"message": "<PERSON><PERSON><PERSON>"}, "alertAccountTypeUpgradeMessage": {"message": "Vous passez d'un compte standard à un compte intelligent. Vous conserverez la même adresse de compte tout en bénéficiant de transactions plus rapides et de frais de réseau moins élevés. $1."}, "alertAccountTypeUpgradeTitle": {"message": "Type de compte"}, "alertActionBuyWithNativeCurrency": {"message": "Acheter $1"}, "alertActionUpdateGas": {"message": "Mettre à jour la limite de gaz"}, "alertActionUpdateGasFee": {"message": "Actualiser les frais"}, "alertActionUpdateGasFeeLevel": {"message": "Mettre à jour les options de gaz"}, "alertDisableTooltip": {"message": "Vous pouvez modifier ceci dans « Paramètres > Alertes »"}, "alertMessageAddressMismatchWarning": {"message": "Les pirates informatiques imitent parfois les sites en modifiant légèrement l’adresse du site. Assurez-vous que vous interagissez avec le site voulu avant de continuer."}, "alertMessageChangeInSimulationResults": {"message": "Les modifications estimées pour cette transaction ont été mises à jour. Examinez-les attentivement avant de poursuivre."}, "alertMessageFirstTimeInteraction": {"message": "Vous interagissez avec cette adresse pour la première fois. Assurez-vous qu’elle est correcte avant de continuer."}, "alertMessageGasEstimateFailed": {"message": "Nous ne sommes pas en mesure de déterminer le montant exact des frais et cette estimation peut être élevée. Nous vous suggérons d’entrer une limite de gaz personnalisée, mais la transaction pourrait quand même <PERSON>."}, "alertMessageGasFeeLow": {"message": "Si vous choisissez des frais peu élevés, attendez-vous à des transactions plus lentes et à des temps d’attente plus longs. Pour des transactions plus rapides, choisissez les options « Ordre au marché » ou « Agressif »."}, "alertMessageGasTooLow": {"message": "Pour effectuer cette transaction, vous devez augmenter la limite de gaz à 21 000 ou plus."}, "alertMessageInsufficientBalanceWithNativeCurrency": {"message": "Vous n’avez pas assez de $1 sur votre compte pour payer les frais de réseau."}, "alertMessageNetworkBusy": {"message": "Les prix du gaz sont élevés et les estimations sont moins précises."}, "alertMessageNoGasPrice": {"message": "Nous ne pouvons pas valider cette transaction tant que vous n’avez pas mis à jour manuellement les frais."}, "alertMessageSignInDomainMismatch": {"message": "Le site auquel vous êtes en train de vous connecter n’est pas le site à l’origine de la demande. Il pourrait s’agir d’une tentative de vol de vos identifiants de connexion."}, "alertMessageSignInWrongAccount": {"message": "Ce site vous demande de vous connecter en utilisant le mauvais compte."}, "alertModalAcknowledge": {"message": "Je suis conscient du risque et je souhaite quand même continuer"}, "alertModalDetails": {"message": "Détails de l’alerte"}, "alertModalReviewAllAlerts": {"message": "Examiner toutes les alertes"}, "alertReasonChangeInSimulationResults": {"message": "Les résultats ont changé"}, "alertReasonFirstTimeInteraction": {"message": "1re interaction"}, "alertReasonGasEstimateFailed": {"message": "Frais inexacts"}, "alertReasonGasFeeLow": {"message": "Vitesse lente"}, "alertReasonGasTooLow": {"message": "<PERSON>ite de gaz trop basse"}, "alertReasonInsufficientBalance": {"message": "Fonds insuffisants"}, "alertReasonNetworkBusy": {"message": "Le réseau est occupé"}, "alertReasonNoGasPrice": {"message": "Estimation des frais non disponible"}, "alertReasonPendingTransactions": {"message": "Transaction en attente"}, "alertReasonSignIn": {"message": "Demande de connexion suspecte"}, "alertReasonWrongAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> compte"}, "alertSelectedAccountWarning": {"message": "Cette demande concerne un compte différent de celui que vous avez sélectionné dans votre portefeuille. Pour utiliser un autre compte, connectez-le au site."}, "alerts": {"message": "<PERSON><PERSON><PERSON>"}, "all": {"message": "<PERSON>ut"}, "allNetworks": {"message": "Tous les réseaux"}, "allPermissions": {"message": "Toutes les autorisations"}, "allTimeHigh": {"message": "Le plus haut niveau jamais atteint"}, "allTimeLow": {"message": "Le plus bas niveau jamais atteint"}, "allowNotifications": {"message": "Autoriser les notifications"}, "allowWithdrawAndSpend": {"message": "Permettre à $1 de retirer et de dépenser jusqu’au montant suivant :", "description": "The url of the site that requested permission to 'withdraw and spend'"}, "amount": {"message": "<PERSON><PERSON>"}, "amountReceived": {"message": "<PERSON><PERSON>"}, "amountSent": {"message": "<PERSON><PERSON> envoy<PERSON>"}, "andForListItems": {"message": "$1, et $2", "description": "$1 is the first item, $2 is the last item in a list of items. Used in Snap Install Warning modal."}, "andForTwoItems": {"message": "$1 et $2", "description": "$1 is the first item, $2 is the second item. Used in Snap Install Warning modal."}, "appDescription": {"message": "Le portefeuille crypto le plus fiable au monde", "description": "The description of the application"}, "appName": {"message": "MetaMask", "description": "The name of the application"}, "appNameBeta": {"message": "MetaMask Beta", "description": "The name of the application (Beta)"}, "appNameFlask": {"message": "MetaMask Flask", "description": "The name of the application (Flask)"}, "apply": {"message": "Appliquer"}, "approve": {"message": "Approuver"}, "approveButtonText": {"message": "Approuver"}, "approveIncreaseAllowance": {"message": "Augmenter le plafond des dépenses de $1", "description": "The token symbol that is being approved"}, "approveSpendingCap": {"message": "Approuver le plafond de dépenses de $1", "description": "The token symbol that is being approved"}, "approved": {"message": "Approu<PERSON><PERSON>"}, "approvedOn": {"message": "Approuver le $1", "description": "$1 is the approval date for a permission"}, "approvedOnForAccounts": {"message": "Approuvé le $1 pour $2", "description": "$1 is the approval date for a permission. $2 is the AvatarGroup component displaying account images."}, "areYouSure": {"message": "En êtes-vous sûr(e) ?"}, "asset": {"message": "Actif"}, "assetChartNoHistoricalPrices": {"message": "Nous n’avons pas pu récupérer de données historiques"}, "assetMultipleNFTsBalance": {"message": "$1 NFT"}, "assetOptions": {"message": "Options d’actifs"}, "assetSingleNFTBalance": {"message": "$1 NFT"}, "assets": {"message": "Actifs"}, "assetsDescription": {"message": "Détection automatique des jetons dans votre portefeuille, affichage des NFT et mise à jour du solde de plusieurs comptes"}, "attemptToCancelSwapForFree": {"message": "Tentative d’annuler gratuitement le swap"}, "attributes": {"message": "Attributs"}, "attributions": {"message": "Attributions"}, "auroraRpcDeprecationMessage": {"message": "L’URL Infura RPC ne prend plus en charge Aurora."}, "authorizedPermissions": {"message": "Vous avez accordé les autorisations suivantes"}, "autoDetectTokens": {"message": "Détection automatique des jetons"}, "autoDetectTokensDescription": {"message": "Nous utilisons des API tierces pour détecter et afficher les nouveaux jetons ajoutés à votre portefeuille. Désactivez cette option si vous ne souhaitez pas que l’application extraie des données de ces services. $1", "description": "$1 is a link to a support article"}, "autoLockTimeLimit": {"message": "Minuterie de déconnexion automatique (minutes)"}, "autoLockTimeLimitDescription": {"message": "Réglez la durée d’inactivité en minutes avant que MetaMask ne se déconnecte automatiquement."}, "average": {"message": "<PERSON><PERSON><PERSON>"}, "back": {"message": "Retour"}, "backupAndSync": {"message": "Sauvegarder et synchroniser"}, "backupAndSyncBasicFunctionalityNameMention": {"message": "fonctionnalité de base"}, "backupAndSyncEnable": {"message": "Activer la sauvegarde et la synchronisation"}, "backupAndSyncEnableConfirmation": {"message": "Lorsque vous activez la sauvegarde et la synchronisation, vous activez également $1. Voulez-vous continuer ?", "description": "$1 is backupAndSyncBasicFunctionalityNameMention in bold."}, "backupAndSyncEnableDescription": {"message": "La sauvegarde et la synchronisation nous permettent de stocker des données chiffrées concernant vos paramètres et fonctionnalités personnalisés. Cela permet de maintenir une expérience MetaMask cohérente sur tous les appareils et de restaurer les paramètres et les fonctionnalités si jamais vous avez besoin de réinstaller MetaMask. Cela ne sauvegarde pas votre phrase secrète de récupération. $1.", "description": "$1 is link to the backup and sync privacy policy."}, "backupAndSyncEnableDescriptionUpdatePreferences": {"message": "Vous pouvez modifier vos préférences à tout moment dans $1", "description": "$1 is a bolded text that highlights the path to the settings page."}, "backupAndSyncEnableDescriptionUpdatePreferencesPath": {"message": "Paramètres > Sauvegarde et synchronisation."}, "backupAndSyncFeatureAccounts": {"message": "<PERSON><PERSON><PERSON>"}, "backupAndSyncManageWhatYouSync": {"message": "Sélectionnez ce que vous voulez synchroniser"}, "backupAndSyncManageWhatYouSyncDescription": {"message": "Sélectionnez ce que vous voulez synchroniser entre vos appareils."}, "backupAndSyncPrivacyLink": {"message": "Découvrez comment nous protégeons vos données personnelles"}, "backupAndSyncSlideDescription": {"message": "Sauvegardez vos comptes et synchronisez vos paramètres."}, "backupAndSyncSlideTitle": {"message": "Présentation de la fonction de sauvegarde et de synchronisation"}, "backupApprovalInfo": {"message": "Ce code secret est requis pour récupérer votre portefeuille si jamais vous perdez votre appareil, oubliez votre mot de passe, devez réinstaller MetaMask ou souhaitez accéder à votre portefeuille depuis un autre appareil."}, "backupApprovalNotice": {"message": "Sauvegardez votre phrase secrète de récupération pour garder votre portefeuille et vos fonds en sécurité."}, "backupKeyringSnapReminder": {"message": "Assurez-vous que vous pouvez accéder à tous les comptes créés par ce snap avant de le supprimer"}, "backupNow": {"message": "Sauvegarder maintenant"}, "balance": {"message": "Solde"}, "balanceOutdated": {"message": "Le solde n’est peut-être pas à jour"}, "baseFee": {"message": "Frais de base"}, "basic": {"message": "Général"}, "basicConfigurationBannerTitle": {"message": "La fonctionnalité de base est désactivée"}, "basicConfigurationDescription": {"message": "MetaMask offre des fonctionnalités de base telles que l’affichage des détails des jetons et des paramètres de gaz par le biais de services Internet. Lorsque vous utilisez des services Internet, votre adresse IP est partagée avec le fournisseur de ces services, dans ce cas MetaMask. C’est la même chose que lorsque vous visitez un site web. MetaMask conserve ces données temporairement et ne les vend jamais. Vous pouvez utiliser un VPN ou désactiver ces services, mais cela peut affecter votre expérience avec MetaMask. Pour en savoir plus, lisez notre $1.", "description": "$1 is to be replaced by the message for privacyMsg, and will link to https://consensys.io/privacy-policy"}, "basicConfigurationLabel": {"message": "Fonctionnalité de base"}, "basicConfigurationModalCheckbox": {"message": "Je comprends et je veux continuer"}, "basicConfigurationModalDisclaimerOff": {"message": "<PERSON>la signifie que vous n’optimiserez pas complètement votre temps sur MetaMask. Vous n’aurez pas accès aux fonctions de base (comme les détails des jetons, les réglages optimaux du gaz, et autres)."}, "basicConfigurationModalDisclaimerOffAdditionalText": {"message": "La désactivation de cette option entraîne également la désactivation de toutes les fonctionnalités liées $1, et $2.", "description": "$1 and $2 are bold text for basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst and basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast respectively"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesFirst": {"message": "à la sécurité et à la confidentialité, à la sauvegarde et à la synchronisation"}, "basicConfigurationModalDisclaimerOffAdditionalTextFeaturesLast": {"message": "aux notifications"}, "basicConfigurationModalDisclaimerOn": {"message": "Pour optimiser votre temps sur MetaMask, vous devez activer cette fonction. Les fonctions de base (comme les détails des jetons, les réglages optimaux du gaz, et autres) améliorent votre expérience Web3."}, "basicConfigurationModalHeadingOff": {"message": "Désactiver la fonctionnalité de base"}, "basicConfigurationModalHeadingOn": {"message": "Activer la fonctionnalité de base"}, "bestPrice": {"message": "Meilleur prix"}, "beta": {"message": "<PERSON><PERSON><PERSON>"}, "betaHeaderText": {"message": "Il s’agit d’une version bêta. Veuillez signaler les bogues $1"}, "betaMetamaskVersion": {"message": "Version MetaMask Beta"}, "betaTerms": {"message": "Conditions d’utilisation de la version bêta"}, "billionAbbreviation": {"message": "Mrd", "description": "Shortened form of 'billion'"}, "blockExplorerAccountAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Account in Explorer"}, "blockExplorerAssetAction": {"message": "Actif", "description": "This is used with viewOnEtherscan and viewInExplorer e.g View Asset in Explorer"}, "blockExplorerSwapAction": {"message": "<PERSON><PERSON><PERSON>", "description": "This is used with viewOnEtherscan e.g View Swap on Etherscan"}, "blockExplorerUrl": {"message": "URL de l’explorateur de blocs"}, "blockExplorerUrlDefinition": {"message": "L’URL utilisée comme explorateur de blocs pour ce réseau."}, "blockExplorerView": {"message": "Afficher le compte à $1", "description": "$1 replaced by URL for custom block explorer"}, "blockaid": {"message": "BlockAid"}, "blockaidAlertDescriptionBlur": {"message": "<PERSON> vous continuez, vous risquez de perdre tous les actifs que vous avez listés sur Blur."}, "blockaidAlertDescriptionMalicious": {"message": "Vous interagissez avec un site malveillant. <PERSON> vous continuez, vous risquez de perdre tous vos actifs."}, "blockaidAlertDescriptionOpenSea": {"message": "<PERSON> vous continuez, vous risquez de perdre tous les actifs que vous avez listés sur OpenSea."}, "blockaidAlertDescriptionOthers": {"message": "Si vous confirmez cette demande, vous risquez de perdre tous vos actifs. Nous vous recommandons d’annuler cette demande."}, "blockaidAlertDescriptionTokenTransfer": {"message": "Vous allez transférer vos actifs à un escroc. Si vous continuez, vous perdrez ces actifs."}, "blockaidAlertDescriptionWithdraw": {"message": "Si vous confirmez cette demande, un escroc pourra retirer et dépenser vos actifs. Ces derniers ne pourront pas être restitués."}, "blockaidDescriptionApproveFarming": {"message": "Si vous approuvez cette demande, un tiers connu pour ses activités frauduleuses pourrait s’emparer de tous vos actifs."}, "blockaidDescriptionBlurFarming": {"message": "Si vous approuvez cette demande, quelqu’un pourrait s'emparer de vos actifs répertoriés sur Blur."}, "blockaidDescriptionErrored": {"message": "En raison d’une erreur, nous n’avons pas pu vérifier les alertes de sécurité. Ne continuez que si vous faites confiance à toutes les adresses concernées."}, "blockaidDescriptionMaliciousDomain": {"message": "Vous interagissez avec un domaine malveillant. Si vous approuvez cette demande, vous risquez de perdre vos actifs."}, "blockaidDescriptionMightLoseAssets": {"message": "Si vous approuvez cette demande, vous risquez de perdre vos actifs."}, "blockaidDescriptionSeaportFarming": {"message": "Si vous approuvez cette demande, quelqu’un pourrait s'emparer de vos actifs répertoriés sur OpenSea."}, "blockaidDescriptionTransferFarming": {"message": "Si vous approuvez cette demande, un tiers connu pour ses activités frauduleuses pourrait s’emparer de tous vos actifs."}, "blockaidMessage": {"message": "Protection de la vie privée : aucune donnée n’est partagée avec des tiers. Disponible sur Arbitrum, Avalanche, BNB chain, Linea, Optimism, Polygon, Base, Sepolia et le réseau principal Ethereum."}, "blockaidTitleDeceptive": {"message": "<PERSON>tte demande trompeuse"}, "blockaidTitleMayNotBeSafe": {"message": "Soyez prudent"}, "blockaidTitleSuspicious": {"message": "<PERSON><PERSON> demande suspecte"}, "blockies": {"message": "Blockies"}, "borrowed": {"message": "A emprunté"}, "boughtFor": {"message": "Acheté pour"}, "bridge": {"message": "<PERSON>"}, "bridgeAllowSwappingOf": {"message": "Autoriser l’accès exact à $1 $2 sur $3 pour l’établissement d’une passerelle", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "bridgeApproval": {"message": "Approuver $1 pour la passerelle", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be bridged. $1 is the symbol of a token that has been approved."}, "bridgeApprovalWarning": {"message": "Vous autorisez l’accès au montant spécifié, $1 $2. Le contrat n’aura pas accès à d’autres fonds."}, "bridgeApprovalWarningForHardware": {"message": "Vous devrez autoriser l’accès à $1 $2 pour l’établissement d’une passerelle, puis approuver l’établissement d’une passerelle vers $2, ce qui nécessitera deux confirmations distinctes."}, "bridgeBlockExplorerLinkCopied": {"message": "Le lien de l’explorateur de blocs a été copié !"}, "bridgeCalculatingAmount": {"message": "Calcul en cours…"}, "bridgeConfirmTwoTransactions": {"message": "Vous de<PERSON>z confirmer 2 transactions sur votre portefeuille matériel :"}, "bridgeCreateSolanaAccount": {"message": "Créer un compte Solana"}, "bridgeCreateSolanaAccountDescription": {"message": "Pour échanger avec le réseau Solana, vous avez besoin d’un compte et d’une adresse de réception."}, "bridgeCreateSolanaAccountTitle": {"message": "<PERSON><PERSON> devez d’abord avoir un compte Solana."}, "bridgeDetailsTitle": {"message": "Détails concernant la passerelle", "description": "Title for the modal showing details about a bridge transaction."}, "bridgeEnterAmount": {"message": "Sélectionnez le montant"}, "bridgeEnterAmountAndSelectAccount": {"message": "Entrez le montant et sélectionnez le compte de destination"}, "bridgeExplorerLinkViewOn": {"message": "Consulter sur $1"}, "bridgeFetchNewQuotes": {"message": "Trouver une nouvelle cotation ?"}, "bridgeFrom": {"message": "<PERSON><PERSON><PERSON> de<PERSON><PERSON>"}, "bridgeFromTo": {"message": "Établir une passerelle pour transférer $1 $2 vers $3", "description": "Tells a user that they need to confirm on their hardware wallet a bridge. $1 is amount of source token, $2 is the source network, and $3 is the destination network"}, "bridgeGasFeesSplit": {"message": "Les frais de réseau indiqués sur l’écran précédent comprennent les deux transactions et seront divisés."}, "bridgeNetCost": {"message": "Coût net"}, "bridgeQuoteExpired": {"message": "Votre cotation a expiré."}, "bridgeSelectDestinationAccount": {"message": "Sélectionnez le compte de destination"}, "bridgeSelectNetwork": {"message": "Sélectionner un réseau"}, "bridgeSelectTokenAmountAndAccount": {"message": "Sélectionnez le jeton, le montant et le compte de destination"}, "bridgeSelectTokenAndAmount": {"message": "Sélectionnez le jeton et le montant"}, "bridgeSolanaAccountCreated": {"message": "Compte <PERSON>"}, "bridgeStatusComplete": {"message": "Effectuée", "description": "Status text indicating a bridge transaction has successfully completed."}, "bridgeStatusFailed": {"message": "Échec", "description": "Status text indicating a bridge transaction has failed."}, "bridgeStatusInProgress": {"message": "En cours de traitement", "description": "Status text indicating a bridge transaction is currently processing."}, "bridgeStepActionBridgeComplete": {"message": "$1 reçu sur $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionBridgePending": {"message": "Réception de $1 sur $2", "description": "$1 is the amount of the destination asset, $2 is the name of the destination network"}, "bridgeStepActionSwapComplete": {"message": "A échangé $1 contre $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeStepActionSwapPending": {"message": "Échange de $1 contre $2", "description": "$1 is the amount of the source asset, $2 is the amount of the destination asset"}, "bridgeTerms": {"message": "Conditions d’utilisation"}, "bridgeTimingMinutes": {"message": "$1 min", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "bridgeTo": {"message": "<PERSON>erelle vers"}, "bridgeToChain": {"message": "Passerelle vers $1"}, "bridgeTokenCannotVerifyDescription": {"message": "Si vous avez ajouté ce jeton manuellement, assurez-vous de bien comprendre les risques que cela peut représenter pour vos fonds avant d’établir une passerelle."}, "bridgeTokenCannotVerifyTitle": {"message": "Nous ne pouvons pas vérifier ce jeton."}, "bridgeTransactionProgress": {"message": "Transaction $1 sur 2"}, "bridgeTxDetailsBridging": {"message": "Établissement d’une passerelle"}, "bridgeTxDetailsDelayedDescription": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsDelayedDescriptionSupport": {"message": "Assistance MetaMask"}, "bridgeTxDetailsDelayedTitle": {"message": "<PERSON><PERSON> fait-il plus de 3 heures ?"}, "bridgeTxDetailsNonce": {"message": "<PERSON><PERSON>"}, "bridgeTxDetailsStatus": {"message": "Statut"}, "bridgeTxDetailsTimestamp": {"message": "Horodatage"}, "bridgeTxDetailsTimestampValue": {"message": "Le $1 à $2", "description": "$1 is the date, $2 is the time"}, "bridgeTxDetailsTokenAmountOnChain": {"message": "$1 $2 sur", "description": "$1 is the amount of the token, $2 is the ticker symbol of the token"}, "bridgeTxDetailsTotalGasFee": {"message": "Total des frais de transaction"}, "bridgeTxDetailsYouReceived": {"message": "<PERSON><PERSON> avez reçu"}, "bridgeTxDetailsYouSent": {"message": "<PERSON><PERSON> a<PERSON> envoyé"}, "bridgeValidationInsufficientGasMessage": {"message": "Vous n’avez pas assez de $1 pour payer les frais de gaz pour cette passerelle. Saisissez un montant plus petit ou achetez plus de $1."}, "bridgeValidationInsufficientGasTitle": {"message": "Vous avez besoin de plus de $1 pour payer les frais de gaz"}, "bridging": {"message": "Établissement d’une passerelle"}, "browserNotSupported": {"message": "Votre navigateur internet n’est pas compatible..."}, "buildContactList": {"message": "<PERSON><PERSON>ez votre liste de contacts"}, "builtAroundTheWorld": {"message": "MetaMask est conçu et établi dans le monde entier."}, "bulletpoint": {"message": "·"}, "busy": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "buyAndSell": {"message": "Acheter/vendre"}, "buyMoreAsset": {"message": "Acheter plus de $1", "description": "$1 is the ticker symbol of a an asset the user is being prompted to purchase"}, "buyNow": {"message": "Achetez maintenant"}, "bytes": {"message": "Octets"}, "canToggleInSettings": {"message": "Vous pouvez ré<PERSON>r cette notification dans Paramètres > <PERSON><PERSON><PERSON>."}, "cancel": {"message": "Annuler"}, "cancelPopoverTitle": {"message": "Annuler la transaction"}, "cancelSpeedUpLabel": {"message": "Ces gas fees vont $1 les frais initiaux.", "description": "$1 is text 'replace' in bold"}, "cancelSpeedUpTransactionTooltip": {"message": "Pour $1 la transaction, les gas fees doivent être augmentés d’au moins 10 % pour être reconnus par le réseau.", "description": "$1 is string 'cancel' or 'speed up'"}, "cancelled": {"message": "<PERSON><PERSON><PERSON>"}, "chainId": {"message": "ID de chaîne"}, "chainIdDefinition": {"message": "L’ID de chaîne utilisé pour signer les transactions pour ce réseau."}, "chainIdExistsErrorMsg": {"message": "Cet ID de chaîne est actuellement utilisé par le réseau $1."}, "chainListReturnedDifferentTickerSymbol": {"message": "Le symbole de ce jeton ne correspond pas au nom du réseau ou à l’ID de chaîne saisi. De nombreux jetons populaires utilisent des symboles similaires que les escrocs peuvent utiliser pour vous amener à leur envoyer un jeton de plus grande valeur en retour. Veuillez vérifier toutes les informations avant de continuer."}, "chooseYourNetwork": {"message": "Choisissez votre r<PERSON>"}, "chooseYourNetworkDescription": {"message": "Lorsque vous utilisez nos paramètres et configurations par défaut, nous utilisons Infura comme fournisseur de RPC (appel de procédure à distance) par défaut, afin d’offrir l’accès le plus fiable et le plus privé possible aux données Ethereum.\nDans certains cas limités, nous pouvons utiliser d’autres fournisseurs de RPC afin de garantir la meilleure expérience utilisateur possible. Vous pouvez choisir votre propre RPC, mais n’oubliez pas que tout RPC a besoin d’accéder à votre adresse IP et à l’adresse de votre portefeuille Ethereum pour valider les transactions. Pour en savoir plus sur la façon dont Infura gère les données pour les comptes EVM, lisez notre $1, et pour les comptes Solana, $2.", "description": "$1 is a link to the privacy policy, $2 is a link to Solana accounts support"}, "chooseYourNetworkDescriptionCallToAction": {"message": "cliquez ici"}, "chromeRequiredForHardwareWallets": {"message": "Pour connecter votre portefeuille matériel, vous devez utiliser MetaMask pour Google Chrome."}, "circulatingSupply": {"message": "Offre en circulation"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "clearActivity": {"message": "Effacer les données d’activité et de nonce"}, "clearActivityButton": {"message": "Effacer les données de l’onglet « Activité »"}, "clearActivityDescription": {"message": "Cela réinitialise le nonce du compte et efface les données de l’onglet « Activité » dans votre portefeuille. Seuls le compte et le réseau actuels seront affectés. Aucun changement ne sera apporté aux soldes ou transactions entrantes."}, "click": {"message": "Cliquez ici"}, "clickToConnectLedgerViaWebHID": {"message": "Cliquez ici pour connecter votre Ledger via WebHID", "description": "Text that can be clicked to open a browser popup for connecting the ledger device via webhid"}, "close": {"message": "<PERSON><PERSON><PERSON>"}, "closeExtension": {"message": "Fermer l’extension"}, "closeWindowAnytime": {"message": "Vous pouvez fermer cette fenêtre à tout moment."}, "coingecko": {"message": "CoinGecko"}, "collectionName": {"message": "Nom de la collection"}, "comboNoOptions": {"message": "Aucune option trouvée", "description": "Default text shown in the combo field dropdown if no options."}, "concentratedSupplyDistributionDescription": {"message": "La majorité de l’offre de jetons est détenue par les principaux détenteurs de jetons, ce qui présente un risque de manipulation centralisée des prix"}, "concentratedSupplyDistributionTitle": {"message": "Distribution concentrée de l'offre"}, "configureSnapPopupDescription": {"message": "V<PERSON> devez maintenant quitter MetaMask pour configurer ce snap."}, "configureSnapPopupInstallDescription": {"message": "V<PERSON> devez maintenant quitter MetaMask pour installer ce snap."}, "configureSnapPopupInstallTitle": {"message": "Installer le snap"}, "configureSnapPopupLink": {"message": "Cliquez sur ce lien pour continuer :"}, "configureSnapPopupTitle": {"message": "Configurer le snap"}, "confirm": {"message": "Confirmer"}, "confirmAccountTypeSmartContract": {"message": "<PERSON><PERSON><PERSON> intelligent"}, "confirmAccountTypeStandard": {"message": "Compte standard"}, "confirmAlertModalAcknowledgeMultiple": {"message": "J’ai pris connaissance des alertes, mais je souhaite quand même continuer"}, "confirmAlertModalAcknowledgeSingle": {"message": "J’ai pris connaissance de l’alerte, mais je souhaite quand même continuer"}, "confirmFieldPaymaster": {"message": "Frais payés par"}, "confirmFieldTooltipPaymaster": {"message": "Les frais de cette transaction seront payés par le contrat « Paymaster » intelligent."}, "confirmGasFeeTokenBalance": {"message": "Solde :"}, "confirmGasFeeTokenInsufficientBalance": {"message": "Fonds insuffisants"}, "confirmGasFeeTokenMetaMaskFee": {"message": "Comprend des frais de $1"}, "confirmGasFeeTokenModalNativeToggleMetaMask": {"message": "MetaMask complète le solde pour effectuer cette transaction."}, "confirmGasFeeTokenModalNativeToggleWallet": {"message": "Payer les frais de réseau en utilisant le solde de votre portefeuille."}, "confirmGasFeeTokenModalPayETH": {"message": "Payer en ETH"}, "confirmGasFeeTokenModalPayToken": {"message": "Payer avec d’autres jetons"}, "confirmGasFeeTokenModalTitle": {"message": "Sélectionner un jeton"}, "confirmGasFeeTokenToast": {"message": "Vous payez ces frais de réseau avec $1"}, "confirmGasFeeTokenTooltip": {"message": "Ce montant est versé au réseau pour traiter votre transaction. Il inclut $1 de frais payés à MetaMask pour les jetons autres que l’ETH ou pour l’ETH préfinancé."}, "confirmInfoAccountNow": {"message": "Maintenant"}, "confirmInfoSwitchingTo": {"message": "Passer à"}, "confirmNestedTransactionTitle": {"message": "Transaction $1"}, "confirmPassword": {"message": "Confirmer le mot de passe"}, "confirmRecoveryPhrase": {"message": "Confirmer la phrase secrète de récupération"}, "confirmSimulationApprove": {"message": "<PERSON><PERSON> approu<PERSON>"}, "confirmTitleAccountTypeSwitch": {"message": "Mise à jour du compte"}, "confirmTitleApproveTransactionNFT": {"message": "<PERSON><PERSON><PERSON> de retrait"}, "confirmTitleDeployContract": {"message": "Déployer un contrat"}, "confirmTitleDescApproveTransaction": {"message": "Ce site demande l’autorisation de retirer vos NFT"}, "confirmTitleDescDelegationRevoke": {"message": "Vous repassez à un compte standard (EOA)."}, "confirmTitleDescDelegationUpgrade": {"message": "Vous passez à un compte intelligent"}, "confirmTitleDescDeployContract": {"message": "Ce site veut que vous déployiez un contrat"}, "confirmTitleDescERC20ApproveTransaction": {"message": "Ce site demande l’autorisation de retirer vos jetons"}, "confirmTitleDescPermitSignature": {"message": "Ce site demande que vous lui accordiez l'autorisation de dépenser vos jetons."}, "confirmTitleDescSIWESignature": {"message": "Un site vous demande de vous connecter pour prouver que vous êtes le titulaire de ce compte."}, "confirmTitleDescSign": {"message": "Vérifiez les détails de la demande avant de confirmer."}, "confirmTitlePermitTokens": {"message": "De<PERSON>e de plafonnement des dépenses"}, "confirmTitleRevokeApproveTransaction": {"message": "Retirer l'autorisation"}, "confirmTitleSIWESignature": {"message": "Demande de connexion"}, "confirmTitleSetApprovalForAllRevokeTransaction": {"message": "Retirer l'autorisation"}, "confirmTitleSignature": {"message": "<PERSON><PERSON><PERSON> de <PERSON>"}, "confirmTitleTransaction": {"message": "Demande de transaction"}, "confirmationAlertDetails": {"message": "Pour protéger vos actifs, nous vous suggérons de rejeter la demande."}, "confirmationAlertModalTitleDescription": {"message": "Vous risquez de perdre une partie ou la totalité de vos actifs"}, "confirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "confusableUnicode": {"message": "« $1 » est similaire à « $2 »."}, "confusableZeroWidthUnicode": {"message": "Caractère de largeur nulle trouvé."}, "confusingEnsDomain": {"message": "Nous avons détecté un caractère pouvant prêter à confusion dans le nom de l’ENS. Vérifiez le nom de l’ENS pour éviter toute fraude potentielle."}, "connect": {"message": "Connecter"}, "connectAccount": {"message": "Connecter le compte"}, "connectAccountOrCreate": {"message": "Connecter un compte ou en créer un nouveau"}, "connectAccounts": {"message": "Connecter les comptes"}, "connectAnAccountHeader": {"message": "Connecter un compte"}, "connectManually": {"message": "Se connecter manuellement au site actuel"}, "connectMoreAccounts": {"message": "Connecter d’autres comptes"}, "connectSnap": {"message": "Connecter $1", "description": "$1 is the snap for which a connection is being requested."}, "connectWithMetaMask": {"message": "Connectez-vous avec MetaMask"}, "connectedAccounts": {"message": "Comptes connectés"}, "connectedAccountsDescriptionPlural": {"message": "Vous avez $1 comptes connectés à ce site.", "description": "$1 is the number of accounts"}, "connectedAccountsDescriptionSingular": {"message": "Vous avez 1 compte connecté à ce site."}, "connectedAccountsEmptyDescription": {"message": "MetaMask n’est pas connecté à ce site. Pour vous connecter à un site web3, cliquez sur le bouton de connexion."}, "connectedAccountsListTooltip": {"message": "$1 peut voir le solde, l’adresse et l’activité du compte, et suggérer des transactions à approuver pour les comptes connectés.", "description": "$1 is the origin name"}, "connectedAccountsToast": {"message": "Les comptes connectés ont été mis à jour"}, "connectedSites": {"message": "Sites connectés"}, "connectedSitesAndSnaps": {"message": "Sites et Snaps connectés"}, "connectedSitesDescription": {"message": "$1 est connecté à ces sites. Ils peuvent voir l’adresse de votre compte.", "description": "$1 is the account name"}, "connectedSitesEmptyDescription": {"message": "$1 n’est connecté à aucun site.", "description": "$1 is the account name"}, "connectedSnapAndNoAccountDescription": {"message": "MetaMask est connecté à ce site, mais aucun compte n’est encore connecté"}, "connectedSnaps": {"message": "Snaps connectés"}, "connectedWithAccount": {"message": "$1 comptes connectés", "description": "$1 represents account length"}, "connectedWithAccountName": {"message": "Connecté avec $1", "description": "$1 represents account name"}, "connectedWithNetwork": {"message": "$1 réseaux connectés", "description": "$1 represents network length"}, "connectedWithNetworkName": {"message": "Connecté avec $1", "description": "$1 represents network name"}, "connecting": {"message": "Connexion…"}, "connectingTo": {"message": "Connexion à $1"}, "connectingToDeprecatedNetwork": {"message": "Le réseau « $1 » sera bientôt abandonné et pourrait ne plus fonctionner. Essayez-en un autre."}, "connectingToGoerli": {"message": "Connexion au testnet Goerli"}, "connectingToLineaGoerli": {"message": "Connexion au réseau de test Linea Goerli"}, "connectingToLineaMainnet": {"message": "Connexion au réseau principal de Linea"}, "connectingToLineaSepolia": {"message": "Connexion au réseau de test Linea Sepolia"}, "connectingToMainnet": {"message": "Connexion au réseau principal Ethereum"}, "connectingToSepolia": {"message": "Connexion au réseau de test Sepolia"}, "connectionDescription": {"message": "Connecter ce site Web à MetaMask"}, "connectionFailed": {"message": "Échec de la connexion"}, "connectionFailedDescription": {"message": "L’extraction de $1 a échoué, vérifiez votre connexion réseau et réessayez.", "description": "$1 is the name of the snap being fetched."}, "connectionPopoverDescription": {"message": "Pour vous connecter à un site, sélectionnez le bouton de connexion. MetaMask ne peut se connecter qu’à des sites Web3."}, "connectionRequest": {"message": "Demande de connexion"}, "contactUs": {"message": "Nous contacter"}, "contacts": {"message": "Contacts"}, "contentFromSnap": {"message": "Contenu provenant de $1", "description": "$1 represents the name of the snap"}, "continue": {"message": "<PERSON><PERSON><PERSON>"}, "contract": {"message": "Contrat"}, "contractAddress": {"message": "<PERSON>resse du contrat"}, "contractAddressError": {"message": "Vous envoyez des jetons à l’adresse de contrat des jetons. <PERSON><PERSON> peut entraîner la perte des jetons en question."}, "contractDeployment": {"message": "Déploiement de contrat"}, "contractInteraction": {"message": "Interaction avec un contrat"}, "convertTokenToNFTDescription": {"message": "Nous avons détecté que cet actif est un NFT. MetaMask prend désormais nativement en charge les NFT. Voulez-vous le retirer de votre liste de jetons et l’ajouter en tant que NFT ?"}, "convertTokenToNFTExistDescription": {"message": "Nous avons détecté que cet actif a été ajouté en tant que NFT. Souhaitez-vous le retirer de votre liste de tokens ?"}, "coolWallet": {"message": "CoolWallet"}, "copiedExclamation": {"message": "<PERSON><PERSON><PERSON>."}, "copyAddress": {"message": "Copier l’addresse dans le presse-papier"}, "copyAddressShort": {"message": "<PERSON><PERSON><PERSON> l’adresse"}, "copyPrivateKey": {"message": "Co<PERSON>r la clé privée"}, "copyToClipboard": {"message": "Copier dans le presse-papier"}, "copyTransactionId": {"message": "Copier le numéro de transaction"}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "createNewAccountHeader": {"message": "<PERSON><PERSON>er un nouveau compte"}, "createPassword": {"message": "<PERSON><PERSON><PERSON> un mot de passe"}, "createSnapAccountDescription": {"message": "$1 veut ajouter un nouveau compte à MetaMask."}, "createSnapAccountTitle": {"message": "<PERSON><PERSON><PERSON> un compte"}, "createSolanaAccount": {"message": "Créer un compte Solana"}, "creatorAddress": {"message": "<PERSON><PERSON><PERSON> <PERSON>"}, "crossChainSwapsLink": {"message": "Échanges inter-réseaux avec MetaMask Portfolio"}, "crossChainSwapsLinkNative": {"message": "Échanges inter-réseaux avec Bridge"}, "cryptoCompare": {"message": "CryptoCompare"}, "currencyConversion": {"message": "<PERSON><PERSON>"}, "currencyRateCheckToggle": {"message": "Aff<PERSON><PERSON> le solde et le prix actuel du jeton"}, "currencyRateCheckToggleDescription": {"message": "Nous utilisons les API $1 et $2 pour afficher le solde de votre compte et le prix du jeton. $3", "description": "$1 represents Coingecko, $2 represents CryptoCompare and $3 represents Privacy Policy"}, "currencySymbol": {"message": "Symbole de la devise"}, "currencySymbolDefinition": {"message": "Le code mnémo affiché pour la devise de ce réseau."}, "currentAccountNotConnected": {"message": "Votre compte actuel n’est pas connecté"}, "currentExtension": {"message": "Page d’extension actuelle"}, "currentLanguage": {"message": "Langue actuelle"}, "currentNetwork": {"message": "Réseau actuel", "description": "Speicifies to token network filter to filter by current Network. Will render when network nickname is not available"}, "currentRpcUrlDeprecated": {"message": "L’URL actuelle du RPC pour ce réseau a été dépréciée."}, "currentTitle": {"message": "Actuel :"}, "currentlyUnavailable": {"message": "Indisponible sur ce réseau"}, "curveHighGasEstimate": {"message": "Graphique d’estimation de gas agressif"}, "curveLowGasEstimate": {"message": "Graphique d’estimation de gas basse"}, "curveMediumGasEstimate": {"message": "Graphique d’estimation de gas du marché"}, "custom": {"message": "Paramètres avancés"}, "customGasSettingToolTipMessage": {"message": "Utilisez $1 pour personnaliser le prix du carburant. <PERSON><PERSON> peut porter à confusion si vous n’en avez pas l’habitude. Agissez avec prudence !", "description": "$1 is key 'advanced' (text: 'Advanced') separated here so that it can be passed in with bold font-weight"}, "customSlippage": {"message": "Personnaliser"}, "customSpendLimit": {"message": "Limite de d<PERSON><PERSON> person<PERSON>"}, "customToken": {"message": "<PERSON><PERSON>"}, "customTokenWarningInNonTokenDetectionNetwork": {"message": "La détection de token n’est pas encore disponible sur ce réseau. Veuillez importer le token manuellement et vous assurer que vous lui faites bien confiance. En savoir plus sur $1"}, "customTokenWarningInTokenDetectionNetwork": {"message": "Tout un chacun peut créer un jeton, y compris créer de fausses copies de jetons existants. En savoir plus sur $1"}, "customTokenWarningInTokenDetectionNetworkWithTDOFF": {"message": "Veuillez vous assurer que le jeton est authentique avant de l’importer. Apprenez à éviter $1. Vous pouvez également activer la détection des jetons $2."}, "customerSupport": {"message": "service client"}, "customizeYourNotifications": {"message": "Personnalisez vos notifications"}, "customizeYourNotificationsText": {"message": "Activez les types de notifications que vous souhaitez recevoir :"}, "dappSuggested": {"message": "Site suggéré"}, "dappSuggestedGasSettingToolTipMessage": {"message": "$1 a suggéré ce prix.", "description": "$1 is url for the dapp that has suggested gas settings"}, "dappSuggestedHigh": {"message": "Site suggéré"}, "dappSuggestedHighShortLabel": {"message": "Site (élevé)"}, "dappSuggestedShortLabel": {"message": "Site"}, "dappSuggestedTooltip": {"message": "$1 a recommandé ce prix.", "description": "$1 represents the Dapp's origin"}, "darkTheme": {"message": "Sombre"}, "data": {"message": "<PERSON><PERSON><PERSON>"}, "dataCollectionForMarketing": {"message": "Collecte de données à des fins de marketing"}, "dataCollectionForMarketingDescription": {"message": "Nous utiliserons MetaMetrics pour savoir comment vous interagissez avec nos communications commerciales et pour partager avec vous des informations pertinentes (comme les caractéristiques des produits et d’autres contenus)."}, "dataCollectionWarningPopoverButton": {"message": "OK"}, "dataCollectionWarningPopoverDescription": {"message": "Vous avez désactivé la collecte de données à des fins de marketing. <PERSON><PERSON> ne s’applique qu’à cet appareil. Si vous utilisez MetaMask sur d’autres appareils, n’oubliez pas de désactiver cette fonctionnalité sur ces appareils aussi."}, "dataUnavailable": {"message": "données non disponibles"}, "dateCreated": {"message": "Date de création"}, "dcent": {"message": "D’Cent"}, "debitCreditPurchaseOptions": {"message": "Options d’achat par carte de débit ou de crédit"}, "decimal": {"message": "Nombre de décimales du jeton"}, "decimalsMustZerotoTen": {"message": "Les décimales doivent être plus grandes que 0 et inférieures à 36."}, "decrypt": {"message": "Décrypter"}, "decryptCopy": {"message": "<PERSON><PERSON>r le message crypté"}, "decryptInlineError": {"message": "Ce message ne peut pas être décrypté à la suite d’une erreur : $1", "description": "$1 is error message"}, "decryptMessageNotice": {"message": "$1 souhaite lire ce message pour compléter votre action", "description": "$1 is the web3 site name"}, "decryptMetamask": {"message": "Dé<PERSON>ry<PERSON><PERSON> le message"}, "decryptRequest": {"message": "Décrypter la demande"}, "defaultRpcUrl": {"message": "URL par défaut du RPC"}, "defaultSettingsSubTitle": {"message": "MetaMask utilise des paramètres par défaut pour établir un équilibre entre sécurité et facilité d’utilisation. Modifiez ces paramètres pour renforcer les mesures de protection de la confidentialité."}, "defaultSettingsTitle": {"message": "Paramètres de confidentialité par défaut"}, "defi": {"message": "<PERSON><PERSON><PERSON>"}, "defiTabErrorContent": {"message": "Essayez de revenir sur cette page plus tard."}, "defiTabErrorTitle": {"message": "Nous n’avons pas pu charger cette page."}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "deleteContact": {"message": "Supprimer le contact"}, "deleteMetaMetricsData": {"message": "Supprimer les données MetaMetrics"}, "deleteMetaMetricsDataDescription": {"message": "Cela supprimera les données historiques de MetaMetrics associées à votre utilisation de cet appareil. Aucun changement ne sera apporté à votre portefeuille et à vos comptes après la suppression de ces données. Ce processus peut prendre jusqu’à 30 jours. Veuillez consulter notre $1.", "description": "$1 will have text saying Privacy Policy "}, "deleteMetaMetricsDataErrorDesc": {"message": "Cette demande ne peut être traitée pour l’instant, car un problème est survenu avec le serveur du système d’analyse, veuil<PERSON>z réessayer plus tard"}, "deleteMetaMetricsDataErrorTitle": {"message": "Pour le moment, il nous est impossible de supprimer ces données"}, "deleteMetaMetricsDataModalDesc": {"message": "Nous sommes sur le point de supprimer toutes vos données MetaMetrics. Êtes-vous sûr(e) de vouloir continuer ?"}, "deleteMetaMetricsDataModalTitle": {"message": "Voulez-vous supprimer les données MetaMetrics ?"}, "deleteMetaMetricsDataRequestedDescription": {"message": "Vous avez initié cette action le $1. Ce processus peut prendre jusqu’à 30 jours. Veuillez consulter notre $2", "description": "$1 will be the date on which teh deletion is requested and $2 will have text saying Privacy Policy "}, "deleteNetworkIntro": {"message": "Si vous supprimez ce réseau, vous devrez l’ajouter à nouveau pour voir vos actifs sur ce réseau"}, "deleteNetworkTitle": {"message": "Supprimer le réseau $1 ?", "description": "$1 represents the name of the network"}, "depositCrypto": {"message": "Déposez des crypto-monnaies à partir d’un autre compte à l’aide d’une adresse de portefeuille ou d’un code QR."}, "deprecatedGoerliNtwrkMsg": {"message": "Les mises à jour du système Ethereum rendront bientôt le testnet Goerli obsolète."}, "deprecatedNetwork": {"message": "Ce réseau est obsolète"}, "deprecatedNetworkButtonMsg": {"message": "J’ai compris"}, "deprecatedNetworkDescription": {"message": "Le réseau auquel vous essayez de vous connecter n’est plus pris en charge par Metamask. $1"}, "description": {"message": "Description"}, "descriptionFromSnap": {"message": "Description provenant de $1", "description": "$1 represents the name of the snap"}, "destinationAccountPickerNoEligible": {"message": "Aucun compte admissible n’a été trouvé"}, "destinationAccountPickerNoMatching": {"message": "Aucun compte correspondant n’a été trouvé"}, "destinationAccountPickerReceiveAt": {"message": "Recevoir sur"}, "destinationAccountPickerSearchPlaceholderToMainnet": {"message": "<PERSON><PERSON><PERSON> <PERSON> réception ou ENS"}, "destinationAccountPickerSearchPlaceholderToSolana": {"message": "<PERSON><PERSON><PERSON>"}, "destinationTransactionIdLabel": {"message": "ID Tx de destination", "description": "Label for the destination transaction ID field."}, "details": {"message": "Détails"}, "developerOptions": {"message": "Options pour les développeurs"}, "disabledGasOptionToolTipMessage": {"message": "« $1 » est désactivé parce qu’il ne correspond pas au minimum d’augmentation de 10 % par rapport aux gas fees initiaux.", "description": "$1 is gas estimate type which can be market or aggressive"}, "disconnect": {"message": "Déconnecter"}, "disconnectAllAccounts": {"message": "Déconnecter tous les comptes"}, "disconnectAllAccountsConfirmationDescription": {"message": "Souhaitez-vous vraiment vous déconnecter ? Vous risquez de perdre certaines fonctionnalités du site."}, "disconnectAllAccountsText": {"message": "comptes"}, "disconnectAllDescriptionText": {"message": "Si vous vous déconnectez de ce site, vous devrez reconnecter vos comptes et réseaux pour pouvoir l’utiliser à nouveau."}, "disconnectAllSnapsText": {"message": "Snaps"}, "disconnectMessage": {"message": "Cela vous déconnectera de ce site"}, "disconnectPrompt": {"message": "Déconnecter $1"}, "disconnectThisAccount": {"message": "Déconnecter ce compte"}, "disconnectedAllAccountsToast": {"message": "Tous les comptes ont été déconnectés de $1", "description": "$1 is name of the dapp`"}, "disconnectedSingleAccountToast": {"message": "$1 déconnecté de $2", "description": "$1 is name of the name and $2 represents the dapp name`"}, "discover": {"message": "Découvrir"}, "discoverSnaps": {"message": "Découvrir des Snaps", "description": "Text that links to the Snaps website. Displayed in a banner on Snaps list page in settings."}, "dismiss": {"message": "Annuler"}, "dismissReminderDescriptionField": {"message": "Activez cette option pour annuler le message de rappel de sauvegarde de la phrase secrète de récupération. Nous vous recommandons fortement de sauvegarder votre phrase secrète de récupération pour éviter toute perte de fonds"}, "dismissReminderField": {"message": "Annuler le rappel de sauvegarde de la phrase secrète de récupération"}, "dismissSmartAccountSuggestionEnabledDescription": {"message": "Activez cette option si vous ne voulez plus voir la suggestion « Passez à un compte intelligent » apparaitre sur vos comptes. Les comptes intelligents vous permettent de bénéficier de transactions plus rapides, de frais de réseau moins élevés et de moyens de paiement flexibles."}, "dismissSmartAccountSuggestionEnabledTitle": {"message": "Désactive<PERSON> la suggestion « Passez à un compte intelligent »"}, "displayNftMedia": {"message": "Afficher les médias NFT"}, "displayNftMediaDescription": {"message": "L’affichage des médias et des données NFT expose votre adresse IP à OpenSea ou à d’autres fournisseurs de services tiers. Cela permet à des hackers d’associer votre adresse IP à votre adresse Ethereum. La détection automatique des NFT dépend de ce paramètre et ne sera pas disponible lorsque celui-ci est désactivé."}, "doNotShare": {"message": "Ne partagez ceci avec personne"}, "domain": {"message": "Domaine"}, "done": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "dontShowThisAgain": {"message": "Ne plus afficher ceci"}, "downArrow": {"message": "flèche vers le bas"}, "downloadGoogleChrome": {"message": "Télécharger Google Chrome"}, "downloadNow": {"message": "Télécharger maintenant"}, "downloadStateLogs": {"message": "Télécharger les journaux d’événements"}, "dragAndDropBanner": {"message": "Vous pouvez faire glisser les réseaux pour les réorganiser. "}, "dropped": {"message": "Déconnecté"}, "duplicateContactTooltip": {"message": "Ce nom de contact est en conflit avec un compte ou un contact existant"}, "duplicateContactWarning": {"message": "Vous avez des contacts en double"}, "durationSuffixDay": {"message": "J", "description": "Shortened form of 'day'"}, "durationSuffixHour": {"message": "H", "description": "Shortened form of 'hour'"}, "durationSuffixMillisecond": {"message": "MS", "description": "Shortened form of 'millisecond'"}, "durationSuffixMinute": {"message": "M", "description": "Shortened form of 'minute'"}, "durationSuffixMonth": {"message": "M", "description": "Shortened form of 'month'"}, "durationSuffixSecond": {"message": "S", "description": "Shortened form of 'second'"}, "durationSuffixWeek": {"message": "S", "description": "Shortened form of 'week'"}, "durationSuffixYear": {"message": "A", "description": "Shortened form of 'year'"}, "earn": {"message": "<PERSON><PERSON><PERSON>"}, "edit": {"message": "Modifier"}, "editANickname": {"message": "Modifier le pseudo"}, "editAccounts": {"message": "Modifier les comptes"}, "editAddressNickname": {"message": "Modifier le pseudo de l’adresse"}, "editCancellationGasFeeModalTitle": {"message": "Modifier les gas fees d’annulation"}, "editContact": {"message": "Modifier le contact"}, "editGasFeeModalTitle": {"message": "Modifier le prix du carburant"}, "editGasLimitOutOfBounds": {"message": "La limite de carburant doit être d’au moins $1"}, "editGasLimitOutOfBoundsV2": {"message": "La limite de carburant doit être supérieure à $1 et inférieure à $2", "description": "$1 is the minimum limit for gas and $2 is the maximum limit"}, "editGasLimitTooltip": {"message": "La limite de carburant correspond au maximum d’unités de carburant que vous consentez à utiliser. Celles-ci servent de multiplicateur aux « Frais de priorité maximaux » et aux « Frais maximaux »."}, "editGasMaxBaseFeeGWEIImbalance": {"message": "Les frais de base maximaux ne peuvent pas être inférieurs aux frais de priorité"}, "editGasMaxBaseFeeHigh": {"message": "Les frais de base maximaux sont plus élevés que nécessaire"}, "editGasMaxBaseFeeLow": {"message": "Les frais de base maximaux sont faibles par rapport aux conditions actuelles du réseau"}, "editGasMaxFeeHigh": {"message": "Les frais maximaux sont plus élevés que nécessaire"}, "editGasMaxFeeLow": {"message": "Les frais maximaux sont trop bas par rapport aux conditions du réseau"}, "editGasMaxFeePriorityImbalance": {"message": "Les frais maximaux ne peuvent pas être inférieurs aux frais de priorité maximaux"}, "editGasMaxPriorityFeeBelowMinimum": {"message": "Les frais de priorité maximaux doivent être supérieurs à 0 GWEI"}, "editGasMaxPriorityFeeBelowMinimumV2": {"message": "Les frais de priorité doivent être supérieurs à 0."}, "editGasMaxPriorityFeeHigh": {"message": "Les frais de priorité maximaux sont plus élevés que nécessaire. Vous risquez de payer plus que nécessaire."}, "editGasMaxPriorityFeeHighV2": {"message": "Les frais de priorité sont plus élevés que nécessaire. Vous risquez de payer plus que nécessaire"}, "editGasMaxPriorityFeeLow": {"message": "Les frais de priorité maximaux sont faibles par rapport aux conditions actuelles du réseau"}, "editGasMaxPriorityFeeLowV2": {"message": "Les frais de priorité sont faibles par rapport aux conditions actuelles du réseau"}, "editGasPriceTooLow": {"message": "Les frais de carburant doivent être supérieurs à 0"}, "editGasPriceTooltip": {"message": "Ce réseau exige un champ « Prix du carburant » lors de la soumission d’une transaction. Le prix du carburant correspond au montant que vous paierez par unité de carburant."}, "editGasSubTextFeeLabel": {"message": "<PERSON><PERSON> maximaux :"}, "editGasTitle": {"message": "Modifier la priorité"}, "editGasTooLow": {"message": "<PERSON><PERSON><PERSON> de traitement inconnu"}, "editInPortfolio": {"message": "Modifier dans Portfolio"}, "editNetworkLink": {"message": "modifier le réseau d’origine"}, "editNetworksTitle": {"message": "Modifier les réseaux"}, "editNonceField": {"message": "Modifier le nonce"}, "editNonceMessage": {"message": "Il s’agit d’une fonction avancée, à utiliser avec précaution."}, "editPermission": {"message": "Modifier l’autorisation"}, "editPermissions": {"message": "Modifier les autorisations"}, "editSpeedUpEditGasFeeModalTitle": {"message": "Modifier les gas fees d’accélération"}, "editSpendingCap": {"message": "Modifier le plafond des dépenses"}, "editSpendingCapAccountBalance": {"message": "Solde du compte : $1 $2"}, "editSpendingCapDesc": {"message": "In<PERSON>quez le montant maximum qui pourra être dépensé en votre nom."}, "editSpendingCapError": {"message": "Le plafond des dépenses ne peut contenir plus de $1 chiffres décimaux. Supprimez les chiffres décimaux excédentaires pour continuer."}, "editSpendingCapSpecialCharError": {"message": "Saisissez uniquement des chiffres"}, "enableAutoDetect": {"message": " Activer la détection automatique"}, "enableFromSettings": {"message": " Activez-la depuis les Paramètres."}, "enableSnap": {"message": "Activer"}, "enableToken": {"message": "activer $1", "description": "$1 is a token symbol, e.g. ETH"}, "enabled": {"message": "Activé"}, "enabledNetworks": {"message": "Réseaux activés"}, "encryptionPublicKeyNotice": {"message": "$1 aimerait avoir votre clé publique de cryptage. En y consentant, ce site sera en mesure de vous composer des messages cryptés.", "description": "$1 is the web3 site name"}, "encryptionPublicKeyRequest": {"message": "Demander la clé publique de cryptage"}, "endpointReturnedDifferentChainId": {"message": "L’URL de RPC que vous avez saisie a renvoyé un ID de chaîne différent ($1).", "description": "$1 is the return value of eth_chainId from an RPC endpoint"}, "enhancedTokenDetectionAlertMessage": {"message": "La détection améliorée des jetons est actuellement disponible sur $1. $2"}, "ensDomainsSettingDescriptionIntroduction": {"message": "MetaMask vous permet de voir les domaines ENS dans la barre d’adresse de votre navigateur. Voici comment cela fonctionne :"}, "ensDomainsSettingDescriptionOutroduction": {"message": "Veuillez noter que l’utilisation de cette fonctionnalité expose votre adresse IP à des services tiers IPFS."}, "ensDomainsSettingDescriptionPart1": {"message": "MetaMask vérifie le contrat ENS d’Ethereum pour trouver le code lié au nom de domaine ENS."}, "ensDomainsSettingDescriptionPart2": {"message": "Si le code renvoie à un IPFS, vous pouvez voir le contenu qui y est associé (généralement un site web)."}, "ensDomainsSettingTitle": {"message": "Afficher les domaines ENS dans la barre d’adresse"}, "ensUnknownError": {"message": "La recherche d’ENS a échoué."}, "enterANameToIdentifyTheUrl": {"message": "Saisissez un nom pour identifier l’URL"}, "enterChainId": {"message": "Saisissez l’ID de chaîne"}, "enterMaxSpendLimit": {"message": "Saisissez la limite de dépenses maximale"}, "enterNetworkName": {"message": "Saisissez le nom du réseau"}, "enterOptionalPassword": {"message": "Entrez le mot de passe facultatif"}, "enterPasswordContinue": {"message": "Entrez votre mot de passe pour continuer"}, "enterRpcUrl": {"message": "Saisissez l’URL du RPC"}, "enterSymbol": {"message": "Saisissez le symbole"}, "enterTokenNameOrAddress": {"message": "Sai<PERSON><PERSON>z le nom du jeton ou copiez-collez l’adresse"}, "enterYourPassword": {"message": "Saisissez votre mot de passe"}, "errorCode": {"message": "Code : $1", "description": "Displayed error code for debugging purposes. $1 is the error code"}, "errorGettingSafeChainList": {"message": "Erreur lors de l’obtention de la liste des chaînes sécurisées, veuillez continuer avec précaution."}, "errorMessage": {"message": "Message : $1", "description": "Displayed error message for debugging purposes. $1 is the error message"}, "errorName": {"message": "Code : $1", "description": "Displayed error name for debugging purposes. $1 is the error name"}, "errorPageContactSupport": {"message": "Contacter l’assistance", "description": "Button for contact MM support"}, "errorPageDescribeUsWhatHappened": {"message": "Décrivez ce qui s’est passé", "description": "<PERSON><PERSON> for submitting report to sentry"}, "errorPageInfo": {"message": "Vos informations ne peuvent pas être affichées. Ne vous inquiétez pas, votre portefeuille et vos fonds sont en sécurité.", "description": "Information banner shown in the error page"}, "errorPageMessageTitle": {"message": "Message d’erreur", "description": "Title for description, which is displayed for debugging purposes"}, "errorPageSentryFormTitle": {"message": "Décrivez ce qui s’est passé", "description": "In sentry feedback form, The title at the top of the feedback form."}, "errorPageSentryMessagePlaceholder": {"message": "Le fait de partager des détails tels que la façon dont nous pouvons reproduire le bogue nous aidera à résoudre le problème.", "description": "In sentry feedback form, The placeholder for the feedback description input field."}, "errorPageSentrySuccessMessageText": {"message": "Merci. Nous y jetterons un coup d’œil prochainement.", "description": "In sentry feedback form, The message displayed after a successful feedback submission."}, "errorPageTitle": {"message": "MetaMask a rencontré une erreur", "description": "Title of generic error page"}, "errorPageTryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> for try again"}, "errorStack": {"message": "Stack :", "description": "Title for error stack, which is displayed for debugging purposes"}, "errorWhileConnectingToRPC": {"message": "Une erreur s’est produite lors de la connexion au réseau personnalisé."}, "errorWithSnap": {"message": "Erreur avec $1", "description": "$1 represents the name of the snap"}, "estimatedFee": {"message": "Frais estimés"}, "estimatedFeeTooltip": {"message": "Montant payé pour traiter la transaction sur le réseau."}, "ethGasPriceFetchWarning": {"message": "Le prix de carburant de sauvegarde est fourni, car le service principal d’estimation du carburant est momentanément indisponible."}, "ethereumProviderAccess": {"message": "Accorder au fournisseur d’Ethereum l’autorisation d’accéder à $1", "description": "The parameter is the name of the requesting origin"}, "ethereumPublicAddress": {"message": "Adresse publique d’Ethereum"}, "etherscan": {"message": "Etherscan"}, "etherscanView": {"message": "Afficher le compte sur Etherscan"}, "etherscanViewOn": {"message": "Afficher sur Etherscan"}, "existingChainId": {"message": "Les informations que vous avez saisies sont associées à un ID de chaîne existant."}, "expandView": {"message": "Agrandir la vue"}, "experimental": {"message": "Expérimental"}, "exploreweb3": {"message": "Explorer le Web3"}, "exportYourData": {"message": "Exportez vos données"}, "exportYourDataButton": {"message": "Télécharger"}, "exportYourDataDescription": {"message": "Vous pouvez exporter des données telles que vos contacts et vos préférences."}, "extendWalletWithSnaps": {"message": "Explorez les Snaps créés par la communauté pour personnaliser votre expérience web3", "description": "Banner description displayed on Snaps list page in Settings when less than 6 Snaps is installed."}, "externalAccount": {"message": "Compte externe"}, "externalExtension": {"message": "Extension externe"}, "externalNameSourcesSetting": {"message": "Pseudonymes proposés"}, "externalNameSourcesSettingDescription": {"message": "Nous récupérons les pseudonymes proposés pour les adresses avec lesquelles vous interagissez auprès de sources tierces telles que Etherscan, Infura et Lens Protocol. Ces sources pourront voir ces adresses et votre adresse IP. L’adresse de votre compte ne sera pas divulguée à des tiers."}, "failed": {"message": "Échec"}, "failedToFetchChainId": {"message": "Impossible de récupérer l’ID de la chaîne. Votre URL de RPC est-elle correcte ?"}, "failover": {"message": "Basculement"}, "failoverRpcUrl": {"message": "URL du RPC de basculement"}, "failureMessage": {"message": "Un problème est survenu et nous n’avons pas pu mener à bien l’action"}, "fast": {"message": "Rapide"}, "feeDetails": {"message": "Détails des frais"}, "fileImportFail": {"message": "L’importation de fichier ne fonctionne pas ? Cliquez ici !", "description": "Helps user import their account from a JSON file"}, "flaskWelcomeUninstall": {"message": "vous devriez désinstaller cette extension", "description": "This request is shown on the Flask Welcome screen. It is intended for non-developers, and will be bolded."}, "flaskWelcomeWarning1": {"message": "Flask permet aux développeurs d’expérimenter de nouvelles API instables. À moins que vous ne soyez développeur(-se) ou bêta testeur(-se), $1.", "description": "This is a warning shown on the Flask Welcome screen, intended to encourage non-developers not to proceed any further. $1 is the bolded message 'flaskWelcomeUninstall'"}, "flaskWelcomeWarning2": {"message": "Nous ne garantissons ni la sécurité ni la stabilité de cette extension. Les nouvelles API proposées par Flask ne sont pas protégées contre les attaques d’hameçonnage, ce qui signifie que tout site ou snap nécessitant Flask pourrait être une tentative malveillante de voler vos actifs.", "description": "This explains the risks of using MetaMask Flask"}, "flaskWelcomeWarning3": {"message": "Toutes les API de Flask sont expérimentales. Elles peuvent être modifiées ou supprimées sans préavis. Elles peuvent aussi rester sur Flask indéfiniment sans jamais être migrées vers le MetaMask stable. Utilisez-les à vos risques et périls.", "description": "This message warns developers about unstable Flask APIs"}, "flaskWelcomeWarning4": {"message": "Assurez-vous de désactiver l’extension MetaMask que vous utilisez habituellement lorsque vous utilisez Flask.", "description": "This message calls to pay attention about multiple versions of MetaMask running on the same site (Flask + Prod)"}, "flaskWelcomeWarningAcceptButton": {"message": "J’accepte les risques", "description": "this text is shown on a button, which the user presses to confirm they understand the risks of using Flask"}, "floatAmountToken": {"message": "Le nombre de jetons doit être un nombre entier"}, "followUsOnTwitter": {"message": "Suivez-nous sur Twitter"}, "forbiddenIpfsGateway": {"message": "Passerelle IPFS interdite : ve<PERSON><PERSON>z spécifier une passerelle CID"}, "forgetDevice": {"message": "Oublier cet appareil"}, "forgotPassword": {"message": "Mot de passe oublié ?"}, "form": {"message": "formulaire"}, "from": {"message": "de"}, "fromAddress": {"message": "De : $1", "description": "$1 is the address to include in the From label. It is typically shortened first using shortenAddress"}, "fromTokenLists": {"message": "À partir des listes de tokens : $1"}, "function": {"message": "Fonction : $1"}, "fundingMethod": {"message": "Mode de financement"}, "gas": {"message": "Carburant"}, "gasDisplayAcknowledgeDappButtonText": {"message": "Modifier le prix de carburant suggéré"}, "gasDisplayDappWarning": {"message": "Ce prix de carburant a été suggéré par $1. Si vous n’en tenez pas compte, vous risquez de rencontrer des difficultés lors de votre transaction. Veuillez contacter $1 pour toute question.", "description": "$1 represents the Dapp's origin"}, "gasFee": {"message": "Frais de gaz"}, "gasLimit": {"message": "Montant maximal des frais de transaction"}, "gasLimitRecommended": {"message": "La limite recommandée de gas est de $1. Si la limite de gas est inférieure à cette valeur, l’opération peut échouer."}, "gasLimitTooLow": {"message": "La limite de carburant doit être d’au moins 21000"}, "gasLimitV2": {"message": "Limite de carburant"}, "gasOption": {"message": "Option de carburant"}, "gasPriceExcessive": {"message": "Vos frais de carburant sont inutilement élevés. Songez à les réduire."}, "gasPriceFetchFailed": {"message": "L’estimation du prix du carburant a échoué en raison d’une erreur de réseau."}, "gasTimingHoursShort": {"message": "$1 h", "description": "$1 represents a number of hours"}, "gasTimingLow": {"message": "<PERSON><PERSON>"}, "gasTimingMinutesShort": {"message": "$1 min", "description": "$1 represents a number of minutes"}, "gasTimingSecondsShort": {"message": "$1 s", "description": "$1 represents a number of seconds"}, "gasUsed": {"message": "Gaz utilisé"}, "general": {"message": "Général"}, "generalCameraError": {"message": "Impossible d’accéder à votre appareil photo. Veuillez réessayer."}, "generalCameraErrorTitle": {"message": "<PERSON><PERSON><PERSON> chose a mal tourné…"}, "generalDescription": {"message": "Synchronisez les paramètres entre différents appareils, sélectionnez les préférences réseau et suivez les données relatives aux jetons"}, "genericExplorerView": {"message": "Voir le compte sur $1"}, "goToSite": {"message": "Accéder au site"}, "goerli": {"message": "Testnet Goerli"}, "gotIt": {"message": "D’accord"}, "grantExactAccess": {"message": "Accorder un accès exact"}, "gwei": {"message": "GWEI"}, "hardware": {"message": "<PERSON><PERSON><PERSON>"}, "hardwareWalletConnected": {"message": "Portefeuille matériel connecté"}, "hardwareWalletLegacyDescription": {"message": "(hérité)", "description": "Text representing the MEW path"}, "hardwareWalletSubmissionWarningStep1": {"message": "Assurez-vous que votre $1 est branché et sélectionnez l'application Ethereum."}, "hardwareWalletSubmissionWarningStep2": {"message": "Activez les « données de contrat intelligent » ou la « signature aveugle » sur votre dispositif $1."}, "hardwareWalletSubmissionWarningTitle": {"message": "Avant de cliquer sur « Envoyer » :"}, "hardwareWalletSupportLinkConversion": {"message": "cliquez ici"}, "hardwareWallets": {"message": "Connecter un portefeuille matériel"}, "hardwareWalletsInfo": {"message": "Les intégrations de portefeuilles matériels utilisent des appels API vers des serveurs externes qui peuvent voir votre adresse IP et les adresses des contrats intelligents avec lesquels vous interagissez."}, "hardwareWalletsMsg": {"message": "Selectionnez le portefeuille matériel que vous voulez utiliser avec MetaMask"}, "here": {"message": "ici", "description": "as in -click here- for more information (goes with troubleTokenBalances)"}, "hexData": {"message": "<PERSON><PERSON><PERSON>"}, "hiddenAccounts": {"message": "<PERSON><PERSON><PERSON> cach<PERSON>"}, "hide": {"message": "Masquer"}, "hideAccount": {"message": "<PERSON><PERSON><PERSON> ca<PERSON>"}, "hideAdvancedDetails": {"message": "Masquer les détails avancés"}, "hideSentitiveInfo": {"message": "Masquer les informations sensibles"}, "hideTokenPrompt": {"message": "Masquer le jeton ?"}, "hideTokenSymbol": {"message": "Masquer $1", "description": "$1 is the symbol for a token (e.g. 'DAI')"}, "hideZeroBalanceTokens": {"message": "Masquer les jetons sans solde"}, "high": {"message": "Agress<PERSON>"}, "highGasSettingToolTipMessage": {"message": "Utilisez $1 pour couvrir les envolées du trafic réseau dues à des événements tels que les chutes de NFT populaires.", "description": "$1 is key 'high' (text: 'Aggressive') separated here so that it can be passed in with bold font-weight"}, "highLowercase": {"message": "élevé"}, "highestCurrentBid": {"message": "Offre actuelle la plus élevée"}, "highestFloorPrice": {"message": "Prix plancher le plus élevé"}, "history": {"message": "Historique"}, "holdToRevealContent1": {"message": "Votre phrase secrète de récupération donne $1", "description": "$1 is a bolded text with the message from 'holdToRevealContent2'"}, "holdToRevealContent2": {"message": "un accès complet à votre portefeuille et à vos fonds.", "description": "Is the bolded text in 'holdToRevealContent1'"}, "holdToRevealContent3": {"message": "Ne la partagez avec personne. $1 $2", "description": "$1 is a message from 'holdToRevealContent4' and $2 is a text link with the message from 'holdToRevealContent5'"}, "holdToRevealContent4": {"message": "Le service d’assistance de MetaMask ne vous la demandera jamais,", "description": "Part of 'holdToRevealContent3'"}, "holdToRevealContent5": {"message": "mais les hameçonneurs pourraient le faire.", "description": "The text link in 'holdToRevealContent3'"}, "holdToRevealContentPrivateKey1": {"message": "Votre clé privée vous donne $1", "description": "$1 is a bolded text with the message from 'holdToRevealContentPrivateKey2'"}, "holdToRevealContentPrivateKey2": {"message": "un accès illimité à votre portefeuille et à vos fonds.", "description": "Is the bolded text in 'holdToRevealContentPrivateKey2'"}, "holdToRevealLockedLabel": {"message": "appuyez longuement pour révéler le cercle verrouillé"}, "holdToRevealPrivateKey": {"message": "Appuyez longuement pour révéler la clé privée"}, "holdToRevealPrivateKeyTitle": {"message": "Conservez votre clé privée en lieu sûr"}, "holdToRevealSRP": {"message": "Appuyez longuement pour révéler la PSR"}, "holdToRevealSRPTitle": {"message": "Conservez votre PSR en lieu sûr"}, "holdToRevealUnlockedLabel": {"message": "appuyez longuement pour révéler le cercle déverrouillé"}, "honeypotDescription": {"message": "Ce jeton peut présenter un risque d’arnaque au pot de miel. Il est conseillé de faire preuve de diligence raisonnable avant d’interagir ce jeton afin d’éviter toute perte financière potentielle."}, "honeypotTitle": {"message": "Pot de miel"}, "howNetworkFeesWorkExplanation": {"message": "Estimation des frais requis pour traiter la transaction. Le montant maximum des frais est de $1."}, "howQuotesWork": {"message": "Comment fonctionnent les cotations"}, "howQuotesWorkExplanation": {"message": "Cette cotation a le meilleur rendement parmi toutes celles que nous avons recherchées. Ce rendement est basé sur le taux de swap, qui comprend les frais d’établissement de passerelles et une commission MetaMask de $1 %, moins les frais de gaz. Les frais de gaz dépendent de l’activité du réseau et de la complexité de la transaction."}, "id": {"message": "ID"}, "ignoreAll": {"message": "<PERSON>gno<PERSON> tout"}, "ignoreTokenWarning": {"message": "Si vous masquez des jetons, ils n’apparaîtront pas dans votre portefeuille. Cependant, vous pouvez toujours les ajouter en les recherchant."}, "imToken": {"message": "imToken"}, "import": {"message": "Importer", "description": "Button to import an account from a selected file"}, "importAccountError": {"message": "Erreur d’importation de compte."}, "importAccountErrorIsSRP": {"message": "Vous avez saisi une phrase secrète de récupération (ou mnémonique). Pour importer un compte ici, vous devez saisir une clé privée, qui est une chaîne hexadécimale de 64 caractères."}, "importAccountErrorNotAValidPrivateKey": {"message": "Il ne s’agit pas d’une clé privée valide. Vous avez saisi une chaîne hexadécimale, mais elle doit comporter 64 caractères."}, "importAccountErrorNotHexadecimal": {"message": "Il ne s’agit pas d’une clé privée valide. <PERSON><PERSON> de<PERSON> saisir une chaîne hexadécimale de 64 caractères."}, "importAccountJsonLoading1": {"message": "Cette importation JSON prendra quelques minutes et pourra faire planter MetaMask."}, "importAccountJsonLoading2": {"message": "<PERSON><PERSON><PERSON><PERSON>, nous essaierons d’accélérer ce processus à l’avenir."}, "importAccountMsg": {"message": "Les comptes importés ne seront pas associés à la phrase secrète de récupération que vous avez créée au départ dans MetaMask. En savoir plus sur les comptes importés"}, "importNFT": {"message": "Importer le NFT"}, "importNFTAddressToolTip": {"message": "Sur OpenSea, par exemple, sur la page des NFT, dans la section Détails, se trouve une valeur avec un hyperlien bleu intitulée « Adresse de contrat ». Si vous cliquez dessus, vous serez redirigé vers l’adresse du contrat sur Etherscan. En haut à gauche de cette page, il devrait y avoir une icône intitulée « Contrat » et à droite, une longue chaîne de lettres et de chiffres. C’est l’adresse du contrat qui a créé votre NFT. Cliquez sur l’icône « Copier » à droite de l’adresse pour la copier dans votre presse-papiers."}, "importNFTPage": {"message": "page Importer des NFT"}, "importNFTTokenIdToolTip": {"message": "L’ID d’un NFT est un identifiant unique puisqu’il n’y a pas deux NFT identiques. Encore une fois, sur OpenSea, ce numéro se trouve dans la section « Détails ». Prenez-en note ou copiez-le dans votre presse-papiers."}, "importNWordSRP": {"message": "J’ai une phrase de récupération de $1 mots", "description": "$1 is the number of words in the recovery phrase"}, "importPrivateKey": {"message": "Clé privée"}, "importSRPDescription": {"message": "Importez un portefeuille existant en utilisant votre phrase secrète de récupération de 12 ou 24 mots."}, "importSRPNumberOfWordsError": {"message": "Les phrases secrètes de récupération sont composées de 12 ou 24 mots"}, "importSRPWordError": {"message": "Le mot $1 est incorrect ou mal orthographié.", "description": "$1 is the word that is incorrect or misspelled"}, "importSRPWordErrorAlternative": {"message": "Les mots $1 et $2 sont incorrects ou mal orthographiés.", "description": "$1 and $2 are multiple words that are mispelled."}, "importSecretRecoveryPhrase": {"message": "Importer la phrase secrète de récupération"}, "importSecretRecoveryPhraseUnknownError": {"message": "Une erreur inconnue s’est produite."}, "importSelectedTokens": {"message": "Voulez-vous importer les jetons sélectionnés ?"}, "importSelectedTokensDescription": {"message": "Seuls les jetons que vous avez sélectionnés apparaîtront dans votre portefeuille. Vous pourrez toujours importer des jetons masqués en les recherchant."}, "importTokenQuestion": {"message": "Importer un jeton ?"}, "importTokenWarning": {"message": "Tout un chacun peut créer un jeton avec n’importe quel nom, y compris de fausses versions de jetons existants. Ajoutez et échangez avec prudence !"}, "importTokensCamelCase": {"message": "Importer des jetons"}, "importTokensError": {"message": "Impossible d’importer les jetons. Veuillez ressayer plus tard."}, "importWallet": {"message": "Importer le portefeuille"}, "importWalletOrAccountHeader": {"message": "Importer un portefeuille ou un compte"}, "importWalletSuccess": {"message": "La phrase secrète de récupération $1 a été importée", "description": "$1 is the index of the secret recovery phrase"}, "importWithCount": {"message": "Importer $1", "description": "$1 will the number of detected tokens that are selected for importing, if all of them are selected then $1 will be all"}, "imported": {"message": "Importé", "description": "status showing that an account has been fully loaded into the keyring"}, "inYourSettings": {"message": "dans vos paramètres"}, "included": {"message": "inclus"}, "includesXTransactions": {"message": "Inclut $1 transactions"}, "infuraBlockedNotification": {"message": "MetaMask ne peut pas se connecter à l’hôte de la blockchain. Vérifiez les éventuelles raisons $1.", "description": "$1 is a clickable link with with text defined by the 'here' key"}, "initialTransactionConfirmed": {"message": "Votre transaction initiale a été confirmée par le réseau. Cliquez sur OK pour retourner à l’écran précédent."}, "insightsFromSnap": {"message": "Aperçus $1", "description": "$1 represents the name of the snap"}, "install": {"message": "Installez"}, "installOrigin": {"message": "Installer Origin"}, "installRequest": {"message": "Ajouter à MetaMask"}, "installedOn": {"message": "Installé le $1", "description": "$1 is the date when the snap has been installed"}, "insufficientBalance": {"message": "Solde insuffisant."}, "insufficientFunds": {"message": "Fonds insuffisants."}, "insufficientFundsForGas": {"message": "Fonds insuffisants pour le carburant"}, "insufficientLockedLiquidityDescription": {"message": "L’absence de liquidités bloquées ou brûlées de manière adéquate rend le jeton vulnérable aux retraits soudains de liquidités, ce qui peut entraîner une instabilité du marché."}, "insufficientLockedLiquidityTitle": {"message": "Insuffisance de liquidités bloquées"}, "insufficientTokens": {"message": "Jetons insuffisants."}, "interactWithSmartContract": {"message": "Contrat intelligent"}, "interactingWith": {"message": "Interagir avec"}, "interactingWithTransactionDescription": {"message": "Ceci est le contrat avec lequel vous interagissez. Vérifiez les détails pour éviter les arnaques."}, "interaction": {"message": "Interaction"}, "invalidAddress": {"message": "<PERSON><PERSON><PERSON> invalide"}, "invalidAddressRecipient": {"message": "L’adresse du destinataire n’est pas valide"}, "invalidAssetType": {"message": "Cet actif est un NFT et doit être ajouté de nouveau à la page Importer des NFT qui se trouve sous l’onglet NFT"}, "invalidChainIdTooBig": {"message": "ID de chaîne invalide. L’ID de la chaîne est trop grand."}, "invalidCustomNetworkAlertContent1": {"message": "L’ID de la chaîne pour le réseau personnalisé « $1 » doit être saisi à nouveau.", "description": "$1 is the name/identifier of the network."}, "invalidCustomNetworkAlertContent2": {"message": "Pour vous protéger des fournisseurs de réseau malveillants ou défectueux, les ID de chaîne sont désormais obligatoires pour tous les réseaux personnalisés."}, "invalidCustomNetworkAlertContent3": {"message": "Allez dans Paramètres > Réseau et saisissez l’ID de chaîne. Vous trouverez les ID de chaîne des réseaux les plus courants sur $1.", "description": "$1 is a link to https://chainid.network"}, "invalidCustomNetworkAlertTitle": {"message": "<PERSON><PERSON><PERSON> invalide"}, "invalidHexData": {"message": "Données hexadécimales non valides"}, "invalidHexNumber": {"message": "Numéro hexadécimal invalide."}, "invalidHexNumberLeadingZeros": {"message": "Numéro hexadécimal invalide. Supprimez tous les zéros non significatifs."}, "invalidIpfsGateway": {"message": "Passerelle IPFS invalide : la valeur doit être une URL valide"}, "invalidNumber": {"message": "Numéro invalide. Saisissez un nombre décimal ou hexadécimal avec le préfixe « 0x »."}, "invalidNumberLeadingZeros": {"message": "Numéro invalide. Supprimez tous les zéros en tête."}, "invalidRPC": {"message": "URL RPC invalide"}, "invalidSeedPhrase": {"message": "Phrase secrète de récupération invalide"}, "invalidSeedPhraseCaseSensitive": {"message": "Entrée invalide ! La phrase secrète de récupération est sensible à la casse."}, "ipfsGateway": {"message": "Passerelle IPFS"}, "ipfsGatewayDescription": {"message": "MetaMask utilise des services tiers pour afficher des images de vos NFT stockés sur IPFS, des informations relatives aux adresses ENS saisies dans la barre d’adresse de votre navigateur et l’icône des différents jetons. Votre adresse IP peut être exposée si vous avez recours à ces services."}, "ipfsToggleModalDescriptionOne": {"message": "Nous utilisons des services tiers pour afficher des images de vos NFT stockés sur IPFS, affichons des informations relatives aux adresses ENS saisies dans la barre d’adresse de votre navigateur et récupérons les icônes des différents jetons. Votre adresse IP peut être exposée si vous avez recours à ces services."}, "ipfsToggleModalDescriptionTwo": {"message": "En sélectionnant « Confirmer », vous activez la résolution IPFS. Vous pouvez désactiver cette option à tout moment dans $1.", "description": "$1 is the method to turn off ipfs"}, "ipfsToggleModalSettings": {"message": "Paramètres > Sécurité et confidentialité"}, "isSigningOrSubmitting": {"message": "Une transaction antérieure est toujours en cours de signature ou d’envoi"}, "jazzAndBlockies": {"message": "Les Jazzicons et les Blockies sont deux styles différents d’icônes uniques qui vous aident à identifier un compte en un coup d’œil."}, "jazzicons": {"message": "Jazzicons"}, "jsonFile": {"message": "Fichier JSON", "description": "format for importing an account"}, "keyringAccountName": {"message": "Nom du compte"}, "keyringAccountPublicAddress": {"message": "Adresse publique"}, "keyringSnapRemovalResult1": {"message": "$1 $2 supprimé", "description": "Displays the result after removal of a keyring snap. $1 is the snap name, $2 is whether it is successful or not"}, "keyringSnapRemovalResultNotSuccessful": {"message": "pas ", "description": "Displays the `not` word in $2."}, "keyringSnapRemoveConfirmation": {"message": "Saisissez $1 pour confirmer que vous souhaitez supprimer ce snap :", "description": "Asks user to input the name nap prior to deleting the snap. $1 is the snap name"}, "keystone": {"message": "Keystone"}, "knownAddressRecipient": {"message": "Adresse contractuelle connue."}, "knownTokenWarning": {"message": "Cette action modifiera les jetons déjà présents dans votre portefeuille, et risque de favoriser les tentatives d’hameçonnage. N’approuvez que si vous êtes certain·e de vouloir modifier ce que ces jetons représentent. En savoir plus sur $1"}, "l1Fee": {"message": "Frais L1"}, "l1FeeTooltip": {"message": "Frais de gaz L1"}, "l2Fee": {"message": "Frais L2"}, "l2FeeTooltip": {"message": "Frais de gaz L2"}, "lastConnected": {"message": "Dernière connexion"}, "lastSold": {"message": "<PERSON><PERSON><PERSON> vente"}, "lavaDomeCopyWarning": {"message": "Pour votre sécurité, vous ne pouvez pas sélectionner ce texte actuellement."}, "layer1Fees": {"message": "Frais de couche 1 (L1)"}, "layer2Fees": {"message": "Frais de couche 2"}, "learnHow": {"message": "Découvrir comment"}, "learnMore": {"message": "En savoir plus"}, "learnMoreAboutGas": {"message": "Vous voulez $1 sur les frais de gaz ?", "description": "$1 will be replaced by the learnMore translation key"}, "learnMoreAboutPrivacy": {"message": "En savoir plus sur les bonnes pratiques en matière de protection des données personnelles."}, "learnMoreAboutSolanaAccounts": {"message": "En savoir plus sur les comptes Solana"}, "learnMoreKeystone": {"message": "En savoir plus"}, "learnMoreUpperCase": {"message": "En savoir plus"}, "learnMoreUpperCaseWithDot": {"message": "En savoir plus."}, "learnScamRisk": {"message": "hameçonnages et risques de sécurité."}, "leaveMetaMask": {"message": "Vou<PERSON>z-vous quitter <PERSON><PERSON><PERSON><PERSON> ?"}, "leaveMetaMaskDesc": {"message": "Vous allez visiter un site externe à MetaMask. Vérifiez l’URL avant de continuer."}, "ledgerAccountRestriction": {"message": "Vous devez d’abord utiliser le dernier compte que vous avez créé avant de pouvoir en ajouter un nouveau."}, "ledgerConnectionInstructionCloseOtherApps": {"message": "Fermez tout autre logiciel connecté à votre appareil, puis cliquez ici pour actualiser."}, "ledgerConnectionInstructionHeader": {"message": "Avant de cliquer sur confirmer :"}, "ledgerConnectionInstructionStepFour": {"message": "Activez les « données de contrat intelligent » ou la « signature aveugle » sur votre dispositif Ledger."}, "ledgerConnectionInstructionStepThree": {"message": "Assurez-vous que votre dispositif Ledger est branché et sélectionnez l'application Ethereum."}, "ledgerDeviceOpenFailureMessage": {"message": "Le dispositif Ledger n’a pas pu s’ouvrir. Il est peut-être connecté à d’autres logiciels. Veuillez fermer Ledger Live ou toute autre application déjà connectée à celui-ci, puis essayez de vous reconnecter."}, "ledgerErrorConnectionIssue": {"message": "Reconnectez votre Ledger, ouvrez l’application ETH et réessayez."}, "ledgerErrorDevicedLocked": {"message": "Votre Ledger est verrouillé. Déverrouillez-le et réessayez."}, "ledgerErrorEthAppNotOpen": {"message": "Pour résoudre le problème, ouvrez l’application ETH sur votre appareil et réessayez."}, "ledgerErrorTransactionDataNotPadded": {"message": "Les données d’entrée de la transaction Ethereum ne sont pas suffisamment étoffées."}, "ledgerLiveApp": {"message": "Appli Ledger Live"}, "ledgerLocked": {"message": "Impossible de se connecter au dispositif Ledger. Veuillez vous assurer que votre périphérique est déverrouillé et que l’application Ethereum est ouverte."}, "ledgerMultipleDevicesUnsupportedInfoDescription": {"message": "Pour connecter un nouvel appareil, il faut déconnecter l’ancien appareil."}, "ledgerMultipleDevicesUnsupportedInfoTitle": {"message": "Vous ne pouvez connecter qu’un seul appareil Ledger à la fois"}, "ledgerTimeout": {"message": "Ledger Live met trop de temps à répondre ou la connexion est interrompue. Assurez-vous que l’application Ledger Live est bien ouverte et que votre appareil est déverrouillé."}, "ledgerWebHIDNotConnectedErrorMessage": {"message": "Le dispositif Ledger n’est pas connecté. Si vous souhaitez le connecter, veuillez cliquer à nouveau sur « Continuer » et approuver la connexion au HID", "description": "An error message shown to the user during the hardware connect flow."}, "levelArrow": {"message": "flèche de niveau"}, "lightTheme": {"message": "<PERSON>"}, "likeToImportToken": {"message": "Voulez-vous importer ce jeton ?"}, "likeToImportTokens": {"message": "Souhaitez-vous ajouter ces jetons ?"}, "lineaGoerli": {"message": "Réseau de test Linea Goerli"}, "lineaMainnet": {"message": "Le réseau principal de Linea"}, "lineaSepolia": {"message": "Réseau de test Linea Sepolia"}, "link": {"message": "Associer"}, "linkCentralizedExchanges": {"message": "Liez votre compte Coinbase ou Binance pour transférer gratuitement des crypto-monnaies vers MetaMask."}, "links": {"message": "<PERSON><PERSON>"}, "loadMore": {"message": "Charger plus"}, "loading": {"message": "Chargement..."}, "loadingScreenSnapMessage": {"message": "Veuillez conclure la transaction sur le snap."}, "loadingTokenList": {"message": "Chargement de la liste des jetons"}, "localhost": {"message": "Localhost 8545"}, "lock": {"message": "Déconnexion"}, "lockMetaMask": {"message": "Verrouiller MetaMask"}, "lockTimeInvalid": {"message": "Le temps de verrouillage doit être un nombre compris entre 0 et 10 080"}, "logo": {"message": "Logo $1", "description": "$1 is the name of the ticker"}, "low": {"message": "Bas"}, "lowEstimatedReturnTooltipMessage": {"message": "Les frais s’élèveront à plus de $1 % du montant initial. Vérifiez le montant que vous recevrez et les frais de réseau."}, "lowEstimatedReturnTooltipTitle": {"message": "Coût élevé"}, "lowGasSettingToolTipMessage": {"message": "Utilisez $1 pour attendre un prix inférieur. Les estimations de temps sont nettement moins précises, car les prix sont relativement imprévisibles.", "description": "$1 is key 'low' separated here so that it can be passed in with bold font-weight"}, "lowLowercase": {"message": "bas"}, "mainnet": {"message": "Réseau principal Ethereum"}, "mainnetToken": {"message": "Cette adresse correspond à une adresse connue du token Ethereum Mainnet. Revérifiez l’adresse du contrat et le réseau en cherchant le token que vous essayez d’ajouter."}, "makeAnotherSwap": {"message": "Créer un nouveau swap"}, "makeSureNoOneWatching": {"message": "Assurez-vous que personne ne regarde votre écran", "description": "Warning to users to be care while creating and saving their new Secret Recovery Phrase"}, "manageDefaultSettings": {"message": "<PERSON><PERSON><PERSON> les paramètres de confidentialité par défaut"}, "manageInstitutionalWallets": {"message": "G<PERSON>rer les portefeuilles Institutional"}, "manageInstitutionalWalletsDescription": {"message": "Activez cette option pour activer les portefeuilles Institutional."}, "manageNetworksMenuHeading": {"message": "<PERSON><PERSON><PERSON> les réseaux"}, "managePermissions": {"message": "<PERSON><PERSON><PERSON> les autorisations"}, "marketCap": {"message": "Capitalisation boursière"}, "marketDetails": {"message": "<PERSON><PERSON><PERSON> du march<PERSON>"}, "max": {"message": "<PERSON>."}, "maxBaseFee": {"message": "Frais de base maximaux"}, "maxFee": {"message": "<PERSON><PERSON> maxi<PERSON>"}, "maxFeeTooltip": {"message": "Frais maximums prévus pour le traitement de la transaction."}, "maxPriorityFee": {"message": "Frais de priorité maximaux"}, "medium": {"message": "<PERSON><PERSON>"}, "mediumGasSettingToolTipMessage": {"message": "Utilisez $1 pour un traitement rapide au prix actuel du marché.", "description": "$1 is key 'medium' (text: 'Market') separated here so that it can be passed in with bold font-weight"}, "memo": {"message": "note"}, "message": {"message": "Message"}, "metaMaskConnectStatusParagraphOne": {"message": "Vous avez maintenant davantage de contrôle sur les connexions de vos comptes dans MetaMask."}, "metaMaskConnectStatusParagraphThree": {"message": "Cliquez pour gérer vos comptes connectés."}, "metaMaskConnectStatusParagraphTwo": {"message": "Le bouton d’état de connexion indique si le site Web que vous visitez est connecté à votre compte actuellement sélectionné."}, "metaMetricsIdNotAvailableError": {"message": "Comme vous n’avez jamais adhéré à MetaMetrics, il n’y a pas de données à supprimer ici."}, "metadataModalSourceTooltip": {"message": "$1 est hébergé sur npm et $2 est l’identifiant unique de ce Snap.", "description": "$1 is the snap name and $2 is the snap NPM id."}, "metamaskNotificationsAreOff": {"message": "Les notifications du portefeuille ne sont actuellement pas activées."}, "metamaskSwapsOfflineDescription": {"message": "MetaMask Swaps est en cours de maintenance. Nous vous invitons à revenir plus tard."}, "metamaskVersion": {"message": "Version de MetaMask"}, "methodData": {"message": "Méthode"}, "methodDataTransactionDesc": {"message": "Fonction exécutée en fonction des données d’entrée décodées."}, "methodNotSupported": {"message": "Non pris en charge par ce compte."}, "metrics": {"message": "Indicateurs"}, "millionAbbreviation": {"message": "M", "description": "Shortened form of 'million'"}, "mismatchedChainLinkText": {"message": "vérifier les détails du réseau", "description": "Serves as link text for the 'mismatched<PERSON><PERSON><PERSON>' key. This text will be embedded inside the translation for that key."}, "mismatchedChainRecommendation": {"message": "Nous vous recommandons de $1 avant de continuer.", "description": "$1 is a clickable link with text defined by the 'mismatchedChainLinkText' key. The link will open to instructions for users to validate custom network details."}, "mismatchedNetworkName": {"message": "Selon nos informations, le nom du réseau peut ne pas correspondre exactement à l’ID de la chaîne."}, "mismatchedNetworkSymbol": {"message": "Le symbole monétaire soumis ne correspond à cet ID de chaîne."}, "mismatchedRpcChainId": {"message": "L’ID de chaîne renvoyé par le réseau personnalisé ne correspond pas à l’ID de chaîne fourni."}, "mismatchedRpcUrl": {"message": "Selon nos informations, la valeur de l’URL RPC soumise ne correspond pas à un fournisseur connu pour cet ID de chaîne."}, "missingSetting": {"message": "Vous ne trouvez pas un paramètre ?"}, "missingSettingRequest": {"message": "<PERSON><PERSON><PERSON> ici"}, "more": {"message": "plus"}, "moreAccounts": {"message": "+ $1 comptes supplémentaires", "description": "$1 is the number of accounts"}, "moreNetworks": {"message": "+ $1 réseaux supplémentaires", "description": "$1 is the number of networks"}, "moreQuotes": {"message": "Plus de cotations"}, "multichainAddEthereumChainConfirmationDescription": {"message": "Vous allez ajouter ce réseau à MetaMask et donner à ce site l’autorisation de l’utiliser."}, "multichainQuoteCardBridgingLabel": {"message": "Établissement d’une passerelle"}, "multichainQuoteCardQuoteLabel": {"message": "Cotation"}, "multichainQuoteCardTimeLabel": {"message": "Temps"}, "multipleSnapConnectionWarning": {"message": "$1 veut utiliser $2 Snaps", "description": "$1 is the dapp and $2 is the number of snaps it wants to connect to."}, "mustSelectOne": {"message": "<PERSON><PERSON> devez sélectionner au moins 1 jeton."}, "name": {"message": "Nom"}, "nameAddressLabel": {"message": "<PERSON><PERSON><PERSON>", "description": "Label above address field in name component modal."}, "nameAlreadyInUse": {"message": "Ce nom est déjà utilisé"}, "nameInstructionsNew": {"message": "Si vous connaissez cette adresse, donnez-lui un pseudonyme pour l’identifier plus facilement à l’avenir.", "description": "Instruction text in name component modal when value is not recognised."}, "nameInstructionsRecognized": {"message": "<PERSON>tte adresse a un pseudonyme par dé<PERSON>ut, mais vous pouvez le modifier ou consulter la liste des pseudonymes proposés.", "description": "Instruction text in name component modal when value is recognized but not saved."}, "nameInstructionsSaved": {"message": "Vous avez déjà choisi un pseudonyme pour cette adresse. Vous pouvez le modifier ou consulter la liste des pseudonymes proposés.", "description": "Instruction text in name component modal when value is saved."}, "nameLabel": {"message": "Pseudonyme", "description": "Label above name input field in name component modal."}, "nameModalMaybeProposedName": {"message": "Peut-être : $1", "description": "$1 is the proposed name"}, "nameModalTitleNew": {"message": "Adresse inconnue", "description": "Title of the modal created by the name component when value is not recognised."}, "nameModalTitleRecognized": {"message": "<PERSON>resse reconnue", "description": "Title of the modal created by the name component when value is recognized but not saved."}, "nameModalTitleSaved": {"message": "<PERSON><PERSON><PERSON> en<PERSON><PERSON><PERSON>", "description": "Title of the modal created by the name component when value is saved."}, "nameProviderProposedBy": {"message": "Proposé par $1", "description": "$1 is the name of the provider"}, "nameProvider_ens": {"message": "Service de noms Ethereum (ENS)"}, "nameProvider_etherscan": {"message": "Etherscan"}, "nameProvider_lens": {"message": "Lens Protocol"}, "nameProvider_token": {"message": "MetaMask"}, "nameSetPlaceholder": {"message": "Choisissez un pseudonyme…", "description": "Placeholder text for name input field in name component modal."}, "nativeNetworkPermissionRequestDescription": {"message": "$1 vous demande votre approbation pour :", "description": "$1 represents dapp name"}, "nativeTokenScamWarningConversion": {"message": "Modifier les détails du réseau"}, "nativeTokenScamWarningDescription": {"message": "Le symbole du jeton natif ne correspond pas au symbole attendu pour le jeton natif du réseau associé à cet ID de chaîne. Vous avez saisi $1 alors que le symbole attendu pour le jeton est $2. Veuillez vérifier que vous êtes connecté à la bonne chaîne.", "description": "$1 represents the currency name, $2 represents the expected currency symbol"}, "nativeTokenScamWarningDescriptionExpectedTokenFallback": {"message": "autre chose", "description": "graceful fallback for when token symbol isn't found"}, "nativeTokenScamWarningTitle": {"message": "Symbole inattendu de jeton natif", "description": "Title for nativeTokenScamWarningDescription"}, "needHelp": {"message": "Vous avez besoin d’aide ? Contactez $1", "description": "$1 represents `needHelpLinkText`, the text which goes in the help link"}, "needHelpFeedback": {"message": "Partagez vos commentaires"}, "needHelpLinkText": {"message": "Assistance MetaMask"}, "needHelpSubmitTicket": {"message": "Envoyer un ticket"}, "needImportFile": {"message": "<PERSON><PERSON> de<PERSON> sélectionner un fichier à importer.", "description": "User is important an account and needs to add a file to continue"}, "negativeETH": {"message": "Vous ne pouvez envoyer des montants négatifs d’ETH."}, "negativeOrZeroAmountToken": {"message": "Impossible d’envoyer des montants négatifs ou nuls."}, "network": {"message": "Réseau :"}, "networkChanged": {"message": "Le réseau a été modifié"}, "networkChangedMessage": {"message": "Vous effectuez maintenant une transaction sur $1.", "description": "$1 is the name of the network"}, "networkDetails": {"message": "<PERSON><PERSON><PERSON> du réseau"}, "networkFee": {"message": "<PERSON><PERSON> r<PERSON>"}, "networkIsBusy": {"message": "Le réseau est occupé. Les gas fees sont élevés et les estimations sont moins précises."}, "networkMenu": {"message": "<PERSON><PERSON> <PERSON> r<PERSON>"}, "networkMenuHeading": {"message": "Sélectionner un réseau"}, "networkName": {"message": "Nom du réseau"}, "networkNameArbitrum": {"message": "Arbitrum"}, "networkNameAvalanche": {"message": "Avalanche"}, "networkNameBSC": {"message": "BSC"}, "networkNameBase": {"message": "Base"}, "networkNameBitcoin": {"message": "Bitcoin"}, "networkNameDefinition": {"message": "Le nom associé à ce réseau."}, "networkNameEthereum": {"message": "Ethereum"}, "networkNameGoerli": {"message": "<PERSON><PERSON><PERSON>"}, "networkNameLinea": {"message": "Linea"}, "networkNameOpMainnet": {"message": "Réseau principal OP"}, "networkNamePolygon": {"message": "Polygon"}, "networkNameSolana": {"message": "Solana"}, "networkNameTestnet": {"message": "Testnet"}, "networkNameZkSyncEra": {"message": "zkSync Era"}, "networkOptions": {"message": "Options du réseau"}, "networkPermissionToast": {"message": "Les autorisations réseau ont été mises à jour"}, "networkProvider": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>"}, "networkStatus": {"message": "Statut du réseau"}, "networkStatusBaseFeeTooltip": {"message": "Les frais de base sont fixés par le réseau et varient toutes les 13-14 secondes. Nos options $1 et $2 tiennent compte des augmentations soudaines.", "description": "$1 and $2 are bold text for Medium and Aggressive respectively."}, "networkStatusPriorityFeeTooltip": {"message": "Éventail de frais de priorité (aussi appelés « pourboire du mineur »). Ils sont versés aux mineurs et les incitent à accorder la priorité à votre transaction."}, "networkStatusStabilityFeeTooltip": {"message": "Le prix du carburant est de $1 au regard des 72 dernières heures.", "description": "$1 is networks stability value - stable, low, high"}, "networkSwitchConnectionError": {"message": "Nous ne pouvons pas nous connecter à $1", "description": "$1 represents the network name"}, "networkURL": {"message": "URL du réseau"}, "networkURLDefinition": {"message": "L’URL utilisée pour accéder à ce réseau."}, "networkUrlErrorWarning": {"message": "Les pirates informatiques imitent parfois les sites en modifiant légèrement l’adresse du site. Assurez-vous que vous interagissez avec le site voulu avant de continuer. Version Punycode : $1", "description": "$1 replaced by RPC URL for network"}, "networks": {"message": "Réseaux"}, "networksSmallCase": {"message": "réseaux"}, "nevermind": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "new": {"message": "Nouveau !"}, "newAccount": {"message": "Nouveau compte"}, "newAccountNumberName": {"message": "Compte $1", "description": "Default name of next account to be created on create account screen"}, "newContact": {"message": "Nouveau contact"}, "newContract": {"message": "Nouveau contrat"}, "newNFTDetectedInImportNFTsMessageStrongText": {"message": "Paramètres > Sécurité et confidentialité"}, "newNFTDetectedInImportNFTsMsg": {"message": "Pour afficher vos NFT en utilisant Opensea, activez l’option « Afficher les supports NFT » dans $1.", "description": "$1 is used for newNFTDetectedInImportNFTsMessageStrongText"}, "newNFTDetectedInNFTsTabMessage": {"message": "Laissez MetaMask détecter et afficher automatiquement les NFT dans votre portefeuille."}, "newNFTsAutodetected": {"message": "Détection automatique des NFT"}, "newNetworkAdded": {"message": "« $1 » a été ajouté avec succès !"}, "newNetworkEdited": {"message": "« $1 » a été modifié !"}, "newNftAddedMessage": {"message": "Le NFT a été ajouté avec succès !"}, "newPassword": {"message": "Nouveau mot de passe (min 8 caractères)"}, "newPrivacyPolicyActionButton": {"message": "En savoir plus"}, "newPrivacyPolicyTitle": {"message": "Nous avons mis à jour notre politique de confidentialité"}, "newRpcUrl": {"message": "Nouvelle URL de RPC"}, "newTokensImportedMessage": {"message": "Vous avez importé avec succès $1.", "description": "$1 is the string of symbols of all the tokens imported"}, "newTokensImportedTitle": {"message": "Jeton importé"}, "next": {"message": "Suivant"}, "nftAddFailedMessage": {"message": "Ce NFT ne peut pas être ajouté, car les informations de propriété ne correspondent pas. Vérifiez que votre saisie est correcte."}, "nftAddressError": {"message": "Ce token est un NFT. Ajouter sur la $1", "description": "$1 is a clickable link with text defined by the 'importNFTPage' key"}, "nftAlreadyAdded": {"message": "Le NFT a déjà été ajouté."}, "nftAutoDetectionEnabled": {"message": "Détection automatique des NFT activée"}, "nftDisclaimer": {"message": "Avertissement : MetaMask importe le fichier multimédia de l'URL source. Cette URL est parfois modifiée par la place de marché sur laquelle le NFT a été minté."}, "nftOptions": {"message": "Options NFT"}, "nftTokenIdPlaceholder": {"message": "Saisissez l’identifiant du jeton"}, "nftWarningContent": {"message": "Vous êtes en train d’accorder à un tiers l’autorisation d’accéder à $1 que vous possédez actuellement ou que vous pourrez posséder dans le futur. Tant que vous n’aurez pas révoqué cette autorisation, l’autre partie pourra transférer ces NFT de votre portefeuille à tout moment et sans demander votre consentement. $2", "description": "$1 is nftWarningContentBold bold part, $2 is Learn more link"}, "nftWarningContentBold": {"message": "tous les NFT $1", "description": "$1 is name of the collection"}, "nftWarningContentGrey": {"message": "Agissez avec prudence."}, "nfts": {"message": "NFT"}, "nftsPreviouslyOwned": {"message": "Précédemment p<PERSON>"}, "nickname": {"message": "Pseudonyme"}, "noAccountsFound": {"message": "Aucun compte trouvé pour la demande de recherche effectuée"}, "noActivity": {"message": "Aucune activité pour le moment"}, "noConnectedAccountTitle": {"message": "MetaMask n’est pas connecté à ce site"}, "noConnectionDescription": {"message": "Pour vous connecter à un site, trouvez et sélectionnez le bouton « connecter ». N’oubliez pas que MetaMask ne peut se connecter qu’à des sites Web3"}, "noConversionRateAvailable": {"message": "Aucun taux de conversion disponible"}, "noDeFiPositions": {"message": "Aucune position pour l’instant"}, "noDomainResolution": {"message": "Aucune résolution n’a été fournie pour le domaine."}, "noHardwareWalletOrSnapsSupport": {"message": "Les Snaps et la plupart des portefeuilles matériels ne fonctionneront pas avec la version actuelle de votre navigateur."}, "noNFTs": {"message": "Aucun NFT pour le moment"}, "noNetworksFound": {"message": "Aucun réseau trouvé pour la requête donnée"}, "noOptionsAvailableMessage": {"message": "Ce moyen d’échange n’est pas disponible pour le moment. Essayez de modifier le montant, le réseau ou le jeton et nous vous trouverons la meilleure option."}, "noSnaps": {"message": "<PERSON><PERSON><PERSON>"}, "noThanks": {"message": "Non merci"}, "noTransactions": {"message": "Vous n’avez aucune transaction"}, "noWebcamFound": {"message": "La caméra de votre ordinateur n’a pas été trouvée. Veuillez réessayer."}, "noWebcamFoundTitle": {"message": "Webcam introuvable"}, "nonContractAddressAlertDesc": {"message": "Vous envoyez des données d’appel à une adresse qui n’est pas un contrat. <PERSON><PERSON> pourrait entraîner une perte de fonds. Assurez-vous que vous êtes en train d’utiliser la bonne adresse et le bon réseau avant de continuer."}, "nonContractAddressAlertTitle": {"message": "<PERSON><PERSON><PERSON> potent<PERSON>"}, "nonce": {"message": "<PERSON><PERSON>"}, "none": {"message": "Aucun"}, "notBusy": {"message": "<PERSON><PERSON> occupé"}, "notCurrentAccount": {"message": "S’agit-il du bon compte ? Il est différent de celui actuellement sélectionné dans votre portefeuille"}, "notEnoughBalance": {"message": "Solde insuffisant"}, "notEnoughGas": {"message": "Pas assez de gaz"}, "notNow": {"message": "Pas maintenant"}, "notificationDetail": {"message": "Détails"}, "notificationDetailBaseFee": {"message": "Frais de base (GWEI)"}, "notificationDetailGasLimit": {"message": "Limite de gaz (unités)"}, "notificationDetailGasUsed": {"message": "Gaz utilisé (unités)"}, "notificationDetailMaxFee": {"message": "Frais maximaux par unité de gaz"}, "notificationDetailNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "notificationDetailNetworkFee": {"message": "<PERSON><PERSON> r<PERSON>"}, "notificationDetailPriorityFee": {"message": "Frais de priorité (GWEI)"}, "notificationItemCheckBlockExplorer": {"message": "Vérifier sur l’explorateur de blocs"}, "notificationItemCollection": {"message": "Collection"}, "notificationItemConfirmed": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "notificationItemError": {"message": "Impossible de consulter la liste des frais pour l’instant"}, "notificationItemFrom": {"message": "De la part de"}, "notificationItemLidoStakeReadyToBeWithdrawn": {"message": "Retrait prêt à être effectué"}, "notificationItemLidoStakeReadyToBeWithdrawnMessage": {"message": "Vous pouvez maintenant retirer vos $1 non stakés"}, "notificationItemLidoWithdrawalRequestedMessage": {"message": "La demande que vous avez soumise pour déstaker vos $1 a été envoyée"}, "notificationItemNFTReceivedFrom": {"message": "A reçu un NFT de"}, "notificationItemNFTSentTo": {"message": "A envoyé un NFT à"}, "notificationItemNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemRate": {"message": "<PERSON><PERSON> (frais inclus)"}, "notificationItemReceived": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemReceivedFrom": {"message": "Reçu de la part de"}, "notificationItemSent": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemSentTo": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemStakeCompleted": {"message": "Staking terminé"}, "notificationItemStaked": {"message": "Staké"}, "notificationItemStakingProvider": {"message": "Fournisseur de services de staking"}, "notificationItemStatus": {"message": "Statut"}, "notificationItemSwapped": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemSwappedFor": {"message": "contre"}, "notificationItemTo": {"message": "<PERSON><PERSON><PERSON>"}, "notificationItemTransactionId": {"message": "ID de transaction"}, "notificationItemUnStakeCompleted": {"message": "Annulation du staking terminée"}, "notificationItemUnStaked": {"message": "Déstaké"}, "notificationItemUnStakingRequested": {"message": "Demande d’annulation du staking"}, "notificationTransactionFailedMessage": {"message": "La transaction $1 a échoué ! $2", "description": "Content of the browser notification that appears when a transaction fails"}, "notificationTransactionFailedTitle": {"message": "Transaction non conclue", "description": "Title of the browser notification that appears when a transaction fails"}, "notificationTransactionSuccessMessage": {"message": "La transaction $1 a été confirmée !", "description": "Content of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessTitle": {"message": "Transaction confirmée", "description": "Title of the browser notification that appears when a transaction is confirmed"}, "notificationTransactionSuccessView": {"message": "Consulter sur $1", "description": "Additional content in a notification that appears when a transaction is confirmed and has a block explorer URL."}, "notifications": {"message": "Notifications"}, "notificationsFeatureToggle": {"message": "Activer les notifications du portefeuille", "description": "Experimental feature title"}, "notificationsFeatureToggleDescription": {"message": "Cette option permet d’activer les notifications du portefeuille telles que l’envoi/la réception de fonds ou de NFT et les annonces liées aux fonctionnalités.", "description": "Description of the experimental notifications feature"}, "notificationsMarkAllAsRead": {"message": "<PERSON><PERSON> tout comme lu"}, "notificationsPageEmptyTitle": {"message": "Rien à voir ici"}, "notificationsPageErrorContent": {"message": "Essayez de visiter à nouveau cette page."}, "notificationsPageErrorTitle": {"message": "Une erreur s’est produite"}, "notificationsPageNoNotificationsContent": {"message": "Vous n’avez pas encore reçu de notifications."}, "notificationsSettingsBoxError": {"message": "Un problème est survenu, veuillez réessayer."}, "notificationsSettingsPageAllowNotifications": {"message": "Restez au courant de ce qui se passe dans votre portefeuille grâce aux notifications. Pour activer les notifications, nous utilisons un profil pour synchroniser certains paramètres entre vos appareils. $1"}, "notificationsSettingsPageAllowNotificationsLink": {"message": "Découvrez comment nous protégeons vos données personnelles lorsque vous utilisez cette fonctionnalité."}, "numberOfNewTokensDetectedPlural": {"message": "$1 nouveaux jetons trouvés dans ce compte", "description": "$1 is the number of new tokens detected"}, "numberOfNewTokensDetectedSingular": {"message": "1 nouveau jeton trouvé dans ce compte"}, "numberOfTokens": {"message": "Nombre de jetons"}, "ofTextNofM": {"message": "de"}, "off": {"message": "Désactivé"}, "offlineForMaintenance": {"message": "Hors ligne à des fins de maintenance"}, "ok": {"message": "OK"}, "on": {"message": "Activé"}, "onboardedMetametricsAccept": {"message": "J’accepte"}, "onboardedMetametricsDisagree": {"message": "Non, merci"}, "onboardedMetametricsKey1": {"message": "Dernières avancées"}, "onboardedMetametricsKey2": {"message": "Caractéristiques du produit"}, "onboardedMetametricsKey3": {"message": "Autres matériels promotionnels pertinents"}, "onboardedMetametricsLink": {"message": "MetaMetrics"}, "onboardedMetametricsParagraph1": {"message": "En plus de $1, nous aimerions utiliser les données pour comprendre comment vous interagissez avec les communications commerciales.", "description": "$1 represents the 'onboardedMetametricsLink' locale string"}, "onboardedMetametricsParagraph2": {"message": "<PERSON><PERSON> nous aide à personnaliser les informations que nous partageons avec vous, comme :"}, "onboardedMetametricsParagraph3": {"message": "N’oubliez pas que nous ne vendons jamais les données que vous nous fournissez et que vous pouvez vous désinscrire à tout moment."}, "onboardedMetametricsTitle": {"message": "Aidez-nous à améliorer votre expérience utilisateur"}, "onboardingAdvancedPrivacyIPFSDescription": {"message": "La passerelle IPFS vous permet d’accéder aux données hébergées par des tiers et de les visualiser. Vous pouvez ajouter une passerelle IPFS personnalisée ou continuer à utiliser la passerelle par défaut."}, "onboardingAdvancedPrivacyIPFSInvalid": {"message": "Veuillez saisir une URL valide"}, "onboardingAdvancedPrivacyIPFSTitle": {"message": "Ajouter une passerelle IPFS personnalisée"}, "onboardingAdvancedPrivacyIPFSValid": {"message": "L’URL de la passerelle IPFS est valide"}, "onboardingAdvancedPrivacyNetworkDescription": {"message": "Lorsque vous utilisez nos paramètres et configurations par défaut, nous utilisons Infura comme fournisseur de RPC (appel de procédure à distance) par défaut, afin d’offrir l’accès le plus fiable et le plus privé possible aux données Ethereum.\nDans certains cas limités, nous pouvons utiliser d’autres fournisseurs de RPC afin de garantir la meilleure expérience utilisateur possible. Vous pouvez choisir votre propre RPC, mais n’oubliez pas que tout RPC a besoin d’accéder à votre adresse IP et à l’adresse de votre portefeuille Ethereum pour valider les transactions. Pour en savoir plus sur la façon dont Infura gère les données pour les comptes EVM, lisez notre $1, et pour les comptes Solana, $2."}, "onboardingAdvancedPrivacyNetworkDescriptionCallToAction": {"message": "cliquez ici"}, "onboardingAdvancedPrivacyNetworkTitle": {"message": "Choisissez votre r<PERSON>"}, "onboardingCreateWallet": {"message": "Créer un nouveau portefeuille"}, "onboardingImportWallet": {"message": "Importer un portefeuille existant"}, "onboardingMetametricsAgree": {"message": "J’accepte"}, "onboardingMetametricsDescription": {"message": "Nous aimerions recueillir des données d’utilisation et de diagnostic de base afin d’améliorer MetaMask. Sachez que nous ne vendons jamais les données que vous nous fournissez ici."}, "onboardingMetametricsInfuraTerms": {"message": "Nous vous informerons si nous décidons d’utiliser ces données à d’autres fins. Pour plus d’informations, vous pouvez consulter notre $1. N’oubliez pas que vous pouvez aller dans les paramètres et vous désinscrire à tout moment.", "description": "$1 represents `onboardingMetametricsInfuraTermsPolicy`"}, "onboardingMetametricsInfuraTermsPolicy": {"message": "Politique de confidentialité"}, "onboardingMetametricsNeverCollect": {"message": "$1 les clics et les contenus visualisés sur l’application sont enregistrés, mais d’autres renseignements (comme votre adresse publique) ne le sont pas.", "description": "$1 represents `onboardingMetametricsNeverCollectEmphasis`"}, "onboardingMetametricsNeverCollectEmphasis": {"message": "Privé :"}, "onboardingMetametricsNeverCollectIP": {"message": "$1 nous utilisons votre adresse IP de temps en temps pour détecter votre emplacement général (comme votre pays ou votre région), mais nous ne l’enregistrons pas.", "description": "$1 represents `onboardingMetametricsNeverCollectIPEmphasis`"}, "onboardingMetametricsNeverCollectIPEmphasis": {"message": "Général :"}, "onboardingMetametricsNeverSellData": {"message": "$1 vous pouvez décider à tout moment de partager ou de supprimer vos données d’utilisation dans les paramètres.", "description": "$1 represents `onboardingMetametricsNeverSellDataEmphasis`"}, "onboardingMetametricsNeverSellDataEmphasis": {"message": "Facultatif :"}, "onboardingMetametricsTitle": {"message": "Aidez-nous à améliorer MetaMask"}, "onboardingMetametricsUseDataCheckbox": {"message": "Nous utiliserons ces données pour savoir comment vous interagissez avec nos communications commerciales et pour partager avec vous des informations pertinentes (comme les caractéristiques des produits)."}, "onboardingPinExtensionDescription": {"message": "Épinglez MetaMask dans votre navigateur pour qu’il soit accessible et qu’il soit facile de voir les confirmations de transaction."}, "onboardingPinExtensionDescription2": {"message": "Vous pouvez ouvrir <PERSON> en cliquant sur l’extension pour accéder à votre portefeuille en un seul clic."}, "onboardingPinExtensionDescription3": {"message": "Cliquez sur l’icône d’extension du navigateur pour y accéder instantanément", "description": "$1 is the browser name"}, "onboardingPinExtensionTitle": {"message": "Votre installation de MetaMask est terminée !"}, "onekey": {"message": "OneKey"}, "only": {"message": "seulement"}, "onlyConnectTrust": {"message": "Ne vous connectez qu’aux sites auxquels vous faites confiance. $1", "description": "Text displayed above the buttons for connection confirmation. $1 is the link to the learn more web page."}, "openFullScreenForLedgerWebHid": {"message": "Passez en plein écran pour connecter votre Ledger.", "description": "Shown to the user on the confirm screen when they are viewing MetaMask in a popup window but need to connect their ledger via webhid."}, "openInBlockExplorer": {"message": "Ouvrir dans l’explorateur de blocs"}, "optional": {"message": "Facultatif"}, "options": {"message": "Options"}, "origin": {"message": "Origine"}, "originChanged": {"message": "Le site a été modifié"}, "originChangedMessage": {"message": "Vous êtes en train d’examiner une demande de $1.", "description": "$1 is the name of the origin"}, "osTheme": {"message": "Système"}, "other": {"message": "autre"}, "otherSnaps": {"message": "autres Snaps", "description": "Used in the 'permission_rpc' message."}, "others": {"message": "autres"}, "outdatedBrowserNotification": {"message": "Votre navigateur n’est pas à jour. Si vous ne mettez pas à jour votre navigateur, vous ne pourrez pas obtenir les correctifs de sécurité et profiter des nouvelles fonctionnalités de MetaMask."}, "overrideContentSecurityPolicyHeader": {"message": "Remplacer l’en-tête Content-Security-Policy"}, "overrideContentSecurityPolicyHeaderDescription": {"message": "Cette option est une solution de contournement pour un problème connu dans Firefox, où l’en-tête Content-Security-Policy d’une dapp peut empêcher l’extension de se charger correctement. La désactivation de cette option n’est pas recommandée, à moins qu’elle ne soit nécessaire pour assurer la compatibilité d’une page web spécifique."}, "padlock": {"message": "Cadenas"}, "participateInMetaMetrics": {"message": "Participer à MetaMetrics"}, "participateInMetaMetricsDescription": {"message": "Participez à MetaMetrics pour nous aider à améliorer MetaMask"}, "password": {"message": "Mot de passe"}, "passwordNotLongEnough": {"message": "Mot de passe trop court"}, "passwordStrength": {"message": "Robustesse du mot de passe : $1", "description": "Return password strength to the user when user wants to create password."}, "passwordStrengthDescription": {"message": "Un mot de passe robuste peut améliorer la sécurité de votre portefeuille en cas de vol ou de compromission de votre appareil."}, "passwordTermsWarning": {"message": "Je comprends que Meta<PERSON>ask ne peut pas me récupérer ce mot de passe. $1"}, "passwordsDontMatch": {"message": "Les mots de passe ne correspondent pas"}, "pastePrivateKey": {"message": "Collez votre clé privée ici:", "description": "For importing an account from a private key"}, "pending": {"message": "En attente"}, "pendingConfirmationAddNetworkAlertMessage": {"message": "La mise à jour du réseau annulera $1 transactions en attente sur ce site.", "description": "Number of transactions."}, "pendingConfirmationSwitchNetworkAlertMessage": {"message": "Le changement de réseau annulera $1 transactions en attente sur ce site.", "description": "Number of transactions."}, "pendingTransactionAlertMessage": {"message": "La transaction précédente doit être finalisée avant que celle-ci ne soit traitée. $1", "description": "$1 represents the words 'how to cancel or speed up a transaction' in a hyperlink"}, "pendingTransactionAlertMessageHyperlink": {"message": "Découvrez comment annuler ou accélérer une transaction.", "description": "The text for the hyperlink in the pending transaction alert message"}, "permissionDetails": {"message": "Informations sur les autorisations"}, "permissionFor": {"message": "Autorisation pour"}, "permissionFrom": {"message": "Autorisation de"}, "permissionRequested": {"message": "<PERSON><PERSON><PERSON> maintenant"}, "permissionRequestedForAccounts": {"message": "Demandée maintenant pour $1", "description": "Permission cell status for requested permission including accounts, rendered as AvatarGroup which is $1."}, "permissionRevoked": {"message": "Révoqué dans cette mise à jour"}, "permissionRevokedForAccounts": {"message": "Révoquée dans cette mise à jour pour $1", "description": "Permission cell status for revoked permission including accounts, rendered as AvatarGroup which is $1."}, "permission_accessNamedSnap": {"message": "Se connecter à $1.", "description": "The description for the `wallet_snap` permission. $1 is the human-readable name of the snap."}, "permission_accessNetwork": {"message": "Accéder à internet.", "description": "The description of the `endowment:network-access` permission."}, "permission_accessNetworkDescription": {"message": "Autoriser le Snap $1 à accéder à l’internet. Cela permet d’effectuer des transferts de données avec des serveurs tiers.", "description": "An extended description of the `endowment:network-access` permission. $1 is the snap name."}, "permission_accessSnap": {"message": "Connexion au Snap $1.", "description": "The description for the `wallet_snap` permission. $1 is the name of the snap."}, "permission_accessSnapDescription": {"message": "Autoriser le site Web ou le snap à interagir avec $1.", "description": "The description for the `wallet_snap_*` permission. $1 is the name of the Snap."}, "permission_assets": {"message": "Afficher les actifs du compte dans MetaMask.", "description": "The description for the `endowment:assets` permission."}, "permission_assetsDescription": {"message": "Autorisez $1 à fournir des informations sur les actifs au client MetaMask. Les actifs peuvent être sur chaîne ou hors chaîne.", "description": "An extended description for the `endowment:assets` permission. $1 is the name of the Snap."}, "permission_cronjob": {"message": "Planifiez et exécutez des actions périodiques.", "description": "The description for the `snap_cronjob` permission"}, "permission_cronjobDescription": {"message": "Autoriser le Snap $1 à effectuer des actions qui s’exécutent périodiquement à des heures, des dates ou des intervalles fixes. <PERSON><PERSON> permet d’envoyer des messages ou des notifications urgentes.", "description": "An extended description for the `snap_cronjob` permission. $1 is the snap name."}, "permission_dialog": {"message": "Afficher les boîtes de dialogue dans MetaMask.", "description": "The description for the `snap_dialog` permission"}, "permission_dialogDescription": {"message": "Autoriser le Snap $1 à afficher des fenêtres contextuelles MetaMask contenant du texte personnalisé, un champ de saisie et des boutons pour approuver ou rejeter une action.\nCela permet de créer par exemple des alertes, des confirmations et des flux d'adhésion pour un snap.", "description": "An extended description for the `snap_dialog` permission. $1 is the snap name."}, "permission_ethereumAccounts": {"message": "Consultez l’adresse, le solde du compte et l’activité, et lancez des transactions", "description": "The description for the `eth_accounts` permission"}, "permission_ethereumProvider": {"message": "Accéder au fournisseur d’Ethereum.", "description": "The description for the `endowment:ethereum-provider` permission"}, "permission_ethereumProviderDescription": {"message": "Autorisez le Snap $1 à communiquer directement avec MetaMask, afin qu'il puisse lire les données de la blockchain et suggérer des messages et des transactions.", "description": "An extended description for the `endowment:ethereum-provider` permission. $1 is the snap name."}, "permission_getEntropy": {"message": "Dériver des clés arbitraires uniques au Snap $1.", "description": "The description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getEntropyDescription": {"message": "Autoriser le Snap $1 à dériver des clés arbitraires uniques au Snap $1, sans les divulguer. Ces clés sont distinctes de votre ou vos comptes MetaMask et ne sont pas liées à vos clés privées ou à votre phrase secrète de récupération. Les autres snaps ne peuvent pas accéder à ces informations.", "description": "An extended description for the `snap_getEntropy` permission. $1 is the snap name."}, "permission_getLocale": {"message": "Afficher votre langue préférée.", "description": "The description for the `snap_getLocale` permission"}, "permission_getLocaleDescription": {"message": "Autoriser le Snap $1 à accéder à vos paramètres MetaMask pour qu’il puisse identifier votre langue préférée. Cela permet de localiser et d’afficher le contenu du Snap $1 dans votre langue.", "description": "An extended description for the `snap_getLocale` permission. $1 is the snap name."}, "permission_getPreferences": {"message": "V<PERSON> pouvez consulter des informations telles que votre langue et votre monnaie fiat préférées.", "description": "The description for the `snap_getPreferences` permission"}, "permission_getPreferencesDescription": {"message": "Autorisez $1 à accéder à des informations telles que votre langue et votre monnaie fiat préférées dans les paramètres de votre compte MetaMask. Cela permet à $1 d’afficher un contenu adapté à vos préférences. ", "description": "An extended description for the `snap_getPreferences` permission. $1 is the snap name."}, "permission_homePage": {"message": "Afficher un écran personnalisé", "description": "The description for the `endowment:page-home` permission"}, "permission_homePageDescription": {"message": "Laissez $1 afficher un écran d’accueil personnalisé dans MetaMask. Celui-ci peut être utilisé pour les interfaces utilisateur, la configuration et les tableaux de bord.", "description": "An extended description for the `endowment:page-home` permission. $1 is the snap name."}, "permission_keyring": {"message": "Autoriser les demandes d’ajout et de gestion de comptes Ethereum", "description": "The description for the `endowment:keyring` permission"}, "permission_keyringDescription": {"message": "Autoriser le Snap $1 à recevoir des demandes d’ajout ou de suppression de comptes et à signer des documents et effectuer des transactions au nom des détenteurs de ces comptes.", "description": "An extended description for the `endowment:keyring` permission. $1 is the snap name."}, "permission_lifecycleHooks": {"message": "Utilisez les hooks de cycle de vie.", "description": "The description for the `endowment:lifecycle-hooks` permission"}, "permission_lifecycleHooksDescription": {"message": "Autoriser le Snap $1 à utiliser des hooks de cycle de vie pour exécuter des codes à des moments spécifiques de son cycle de vie.", "description": "An extended description for the `endowment:lifecycle-hooks` permission. $1 is the snap name."}, "permission_manageAccounts": {"message": "Ajouter et gérer des comptes Ethereum", "description": "The description for `snap_manageAccounts` permission"}, "permission_manageAccountsDescription": {"message": "Autoriser le Snap $1 à ajouter ou supprimer des comptes Ethereum qui peuvent être utilisés pour effectuer des transactions et signer des documents.", "description": "An extended description for the `snap_manageAccounts` permission. $1 is the snap name."}, "permission_manageBip32Keys": {"message": "G<PERSON>rer les comptes du chemin $1.", "description": "The description for the `snap_getBip32Entropy` permission. $1 is a derivation path, e.g. 'm/44'/0'/0' (secp256k1)'."}, "permission_manageBip44AndBip32KeysDescription": {"message": "Autoriser le Snap $1 à gérer des comptes et des actifs sur le réseau demandé. Ces comptes sont dérivés et sauvegardés à l’aide de votre phrase secrète de récupération (sans la divulguer). Grâce à sa capacité à dériver des clés, le Snap $1 peut prendre en charge des protocoles blockchain autres que les protocoles de la blockchain Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions. $1 is the snap name."}, "permission_manageBip44Keys": {"message": "G<PERSON>rer les comptes du protocole $1.", "description": "The description for the `snap_getBip44Entropy` permission. $1 is the name of a protocol, e.g. 'Filecoin'."}, "permission_manageState": {"message": "<PERSON><PERSON> et gérez ses données sur votre appareil.", "description": "The description for the `snap_manageState` permission"}, "permission_manageStateDescription": {"message": "Autoriser le Snap $1 à stocker, mettre à jour et récupérer des données en toute sécurité en les chiffrant. Les autres snaps ne peuvent pas accéder à ces informations.", "description": "An extended description for the `snap_manageState` permission. $1 is the snap name."}, "permission_nameLookup": {"message": "Permet de rechercher les noms de domaine et les adresses.", "description": "The description for the `endowment:name-lookup` permission."}, "permission_nameLookupDescription": {"message": "Autorisez le Snap à récupérer et à afficher les adresses et les noms de domaine dans différentes parties de l’interface utilisateur de MetaMask.", "description": "An extended description for the `endowment:name-lookup` permission."}, "permission_notifications": {"message": "Afficher les notifications.", "description": "The description for the `snap_notify` permission"}, "permission_notificationsDescription": {"message": "Autoriser le Snap $1 à afficher des notifications dans MetaMask. Un court texte de notification peut être déclenché par un snap pour fournir des informations exploitables ou sensibles au facteur temps.", "description": "An extended description for the `snap_notify` permission. $1 is the snap name."}, "permission_protocol": {"message": "Fournir des données de protocole pour une ou plusieurs chaînes.", "description": "The description for the `endowment:protocol` permission."}, "permission_protocolDescription": {"message": "Autoriser $1 à fournir à MetaMask des données de protocole telles que des estimations des frais de gaz ou des informations sur les jetons.", "description": "An extended description for the `endowment:protocol` permission. $1 is the name of the Snap."}, "permission_rpc": {"message": "Autoriser $1 à communiquer directement avec le Snap $2.", "description": "The description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescription": {"message": "Autoriser $1 à envoyer des messages au Snap $2 et à recevoir une réponse du Snap $2.", "description": "An extended description for the `endowment:rpc` permission. $1 is 'other snaps' or 'websites', $2 is the snap name."}, "permission_rpcDescriptionOriginList": {"message": "$1 et $2", "description": "A list of allowed origins where $2 is the last origin of the list and $1 is the rest of the list separated by ','."}, "permission_signatureInsight": {"message": "Afficher la fenêtre modale contenant des informations sur les demandes de signature.", "description": "The description for the `endowment:signature-insight` permission"}, "permission_signatureInsightDescription": {"message": "Autoriser $1 à afficher une fenêtre modale contenant des informations sur toute demande de signature avant son approbation. <PERSON><PERSON> peut être utilisé pour mettre en place des mesures de sécurité et de lutte contre l’hameçonnage.", "description": "An extended description for the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_signatureInsightOrigin": {"message": "Voir l’origine des sites web qui soumettent une demande de signature", "description": "The description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission"}, "permission_signatureInsightOriginDescription": {"message": "Autoriser $1 à voir l’origine (URI) des sites web qui soumettent des demandes de signature. Cela peut être utilisé pour mettre en place des mesures de sécurité et de lutte contre l’hameçonnage.", "description": "An extended description for the `signatureOrigin` caveat, to be used with the `endowment:signature-insight` permission. $1 is the snap name."}, "permission_transactionInsight": {"message": "Récupérez et affichez les aperçus de transaction.", "description": "The description for the `endowment:transaction-insight` permission"}, "permission_transactionInsightDescription": {"message": "Autoriser le Snap $1 à décoder les transactions et à afficher des informations dans l'interface utilisateur de MetaMask. Cela peut être utilisé pour des solutions de sécurité et de lutte contre l'hameçonnage.", "description": "An extended description for the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_transactionInsightOrigin": {"message": "Voir les sites web à l’origine des demandes de transaction", "description": "The description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission"}, "permission_transactionInsightOriginDescription": {"message": "Autoriser le Snap $1 à voir l'origine (URI) des sites Web qui suggèrent des transactions. Cela permet de développer des solutions de sécurité et de lutte contre l'hameçonnage.", "description": "An extended description for the `transactionOrigin` caveat, to be used with the `endowment:transaction-insight` permission. $1 is the snap name."}, "permission_unknown": {"message": "Autorisation inconnue : $1", "description": "$1 is the name of a requested permission that is not recognized."}, "permission_viewBip32PublicKeys": {"message": "Consultez votre clé publique pour $1 ($2).", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a derivation path, e.g. 'm/44'/0'/0''. $2 is the elliptic curve name, e.g. 'secp256k1'."}, "permission_viewBip32PublicKeysDescription": {"message": "Autoriser le Snap $2 à consulter vos clés publiques (et vos adresses) pour $1. Cela n’accorde pas l’autorisation de gérer les comptes ou les actifs.", "description": "An extended description for the `snap_getBip32PublicKey` permission. $1 is a derivation path (name). $2 is the snap name."}, "permission_viewNamedBip32PublicKeys": {"message": "Afficher votre clé publique pour $1.", "description": "The description for the `snap_getBip32PublicKey` permission. $1 is a name for the derivation path, e.g., 'Ethereum accounts'."}, "permission_walletSwitchEthereumChain": {"message": "Utilisez vos réseaux activés", "description": "The label for the `wallet_switchEthereumChain` permission"}, "permission_webAssembly": {"message": "Prise en charge de WebAssembly.", "description": "The description of the `endowment:webassembly` permission."}, "permission_webAssemblyDescription": {"message": "Autoriser le Snap $1 à accéder à des environnements d’exécution de faible niveau via WebAssembly.", "description": "An extended description of the `endowment:webassembly` permission. $1 is the snap name."}, "permissions": {"message": "Autorisations"}, "permissionsPageEmptyContent": {"message": "Rien à voir ici"}, "permissionsPageEmptySubContent": {"message": "<PERSON><PERSON>, vous pouvez voir les autorisations que vous avez accordées aux Snaps installés ou aux sites connectés."}, "permitSimulationChange_approve": {"message": "Plafond de <PERSON>"}, "permitSimulationChange_bidding": {"message": "<PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "permitSimulationChange_listing": {"message": "Vous mettez en vente"}, "permitSimulationChange_nft_listing": {"message": "Prix de vente"}, "permitSimulationChange_receive": {"message": "<PERSON><PERSON> rece<PERSON>"}, "permitSimulationChange_revoke2": {"message": "Révoquer"}, "permitSimulationChange_transfer": {"message": "Vous envoyez"}, "permitSimulationDetailInfo": {"message": "Vous autorisez la dépenseur à dépenser ce nombre de jetons de votre compte."}, "permittedChainToastUpdate": {"message": "$1 a accès à $2."}, "personalAddressDetected": {"message": "Votre adresse personnelle a été détectée. Veuillez saisir à la place l’adresse du contrat du jeton."}, "pinToTop": {"message": "<PERSON><PERSON><PERSON> en haut de la page"}, "pleaseConfirm": {"message": "<PERSON><PERSON><PERSON><PERSON> confirmer"}, "plusMore": {"message": "+ $1 de plus", "description": "$1 is the number of additional items"}, "plusXMore": {"message": "+ $1 de plus", "description": "$1 is a number of additional but unshown items in a list- this message will be shown in place of those items"}, "popularNetworkAddToolTip": {"message": "Certains de ces réseaux dépendent de services tiers. Les connexions peuvent être moins fiables ou permettre à des tiers de suivre l’activité des utilisateurs.", "description": "Learn more link"}, "popularNetworks": {"message": "Réseaux populaires"}, "portfolio": {"message": "Portefeuille"}, "preparingSwap": {"message": "Préparation du swap..."}, "prev": {"message": "Préc."}, "price": {"message": "Prix"}, "priceUnavailable": {"message": "prix non disponible"}, "primaryType": {"message": "Type principal"}, "priorityFee": {"message": "Frais de priorité"}, "priorityFeeProperCase": {"message": "Frais de priorité"}, "privacy": {"message": "Confidentialité"}, "privacyMsg": {"message": "Politique de confidentialité"}, "privateKey": {"message": "Clé privée", "description": "select this type of file to use to import an account"}, "privateKeyCopyWarning": {"message": "Clé privée pour $1", "description": "$1 represents the account name"}, "privateKeyHidden": {"message": "La clé privée est masquée", "description": "Explains that the private key input is hidden"}, "privateKeyShow": {"message": "Afficher/masquer le champ de saisie de la clé privée", "description": "Describes a toggle that is used to show or hide the private key input"}, "privateKeyShown": {"message": "Cette clé privée est affichée", "description": "Explains that the private key input is being shown"}, "privateKeyWarning": {"message": "Avertissement : ne divulguez jamais cette clé, quiconque possède vos clés privées peut voler tous les actifs de votre compte."}, "privateNetwork": {"message": "<PERSON><PERSON><PERSON> privé"}, "proceedWithTransaction": {"message": "Je veux tout de même continuer"}, "productAnnouncements": {"message": "Annonces de produits"}, "proposedApprovalLimit": {"message": "Limite d’approbation proposée"}, "provide": {"message": "Fournir"}, "publicAddress": {"message": "Adresse publique"}, "pushPlatformNotificationsFundsReceivedDescription": {"message": "Vous avez reçu $1 $2"}, "pushPlatformNotificationsFundsReceivedDescriptionDefault": {"message": "<PERSON><PERSON> avez reçu des jetons"}, "pushPlatformNotificationsFundsReceivedTitle": {"message": "<PERSON><PERSON><PERSON> reçus"}, "pushPlatformNotificationsFundsSentDescription": {"message": "V<PERSON> avez envoyé avec succès $1 $2"}, "pushPlatformNotificationsFundsSentDescriptionDefault": {"message": "<PERSON><PERSON> avez envoyé avec succès des jetons"}, "pushPlatformNotificationsFundsSentTitle": {"message": "Fonds envoyés"}, "pushPlatformNotificationsNftReceivedDescription": {"message": "Vous avez reçu de nouveaux NFT"}, "pushPlatformNotificationsNftReceivedTitle": {"message": "NFT reçu"}, "pushPlatformNotificationsNftSentDescription": {"message": "<PERSON><PERSON> avez envoyé avec succès un NFT"}, "pushPlatformNotificationsNftSentTitle": {"message": "NFT envoyé"}, "pushPlatformNotificationsStakingLidoStakeCompletedDescription": {"message": "V<PERSON>re staking Lido a été effectué avec succès"}, "pushPlatformNotificationsStakingLidoStakeCompletedTitle": {"message": "Staking terminé"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnDescription": {"message": "Vous pouvez d<PERSON> retirer vos récompenses de staking Lido"}, "pushPlatformNotificationsStakingLidoStakeReadyToBeWithdrawnTitle": {"message": "Retirer les récompenses de staking Lido"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedDescription": {"message": "Votre retrait Lido a été effectué avec succès"}, "pushPlatformNotificationsStakingLidoWithdrawalCompletedTitle": {"message": "Retrait effectué"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedDescription": {"message": "Votre demande de retrait Lido a été envoyée"}, "pushPlatformNotificationsStakingLidoWithdrawalRequestedTitle": {"message": "La demande de retrait a été envoyée"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedDescription": {"message": "Votre staking RocketPool a été effectué avec succès"}, "pushPlatformNotificationsStakingRocketpoolStakeCompletedTitle": {"message": "Staking terminé"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedDescription": {"message": "L’annulation du staking RocketPool a été effectuée avec succès"}, "pushPlatformNotificationsStakingRocketpoolUnstakeCompletedTitle": {"message": "Annulation du staking terminée"}, "pushPlatformNotificationsSwapCompletedDescription": {"message": "Votre swap MetaMask a été effectué avec succès"}, "pushPlatformNotificationsSwapCompletedTitle": {"message": "<PERSON><PERSON><PERSON> terminé"}, "queued": {"message": "En attente"}, "quoteRate": {"message": "Cotation"}, "quotedReceiveAmount": {"message": "$1 reçoit le montant"}, "quotedTotalCost": {"message": "Coût total de $1"}, "rank": {"message": "<PERSON>ng"}, "rateIncludesMMFee": {"message": "Le taux inclut des frais de $1 %"}, "reAddAccounts": {"message": "ajouter de nouveau tous les autres comptes"}, "reAdded": {"message": "ajouté à nouveau"}, "readdToken": {"message": "Vous pourrez ajouter à nouveau ce jeton en allant sur « Ajouter un jeton » dans le menu des options de votre compte."}, "receive": {"message": "Recevoir"}, "receiveCrypto": {"message": "Recevoir des crypto-monnaies"}, "recipientAddressPlaceholderNew": {"message": "Saisissez l’adresse publique (0x) ou le nom de domaine"}, "recommendedGasLabel": {"message": "Recommandé"}, "recoveryPhraseReminderBackupStart": {"message": "Commencez ici"}, "recoveryPhraseReminderConfirm": {"message": "C’est compris !"}, "recoveryPhraseReminderHasBackedUp": {"message": "Conservez toujours votre phrase secrète de récupération dans un endroit sûr et secret"}, "recoveryPhraseReminderHasNotBackedUp": {"message": "Vous avez besoin de sauvegarder à nouveau votre phrase secrète de récupération ?"}, "recoveryPhraseReminderItemOne": {"message": "Ne partagez jamais votre phrase secrète de récupération avec qui que ce soit"}, "recoveryPhraseReminderItemTwo": {"message": "L’équipe <PERSON> ne vous demandera jamais votre phrase secrète de récupération"}, "recoveryPhraseReminderSubText": {"message": "Votre phrase secrète de récupération contrôle tous vos comptes."}, "recoveryPhraseReminderTitle": {"message": "Protégez vos fonds"}, "redeposit": {"message": "Déposer à nouveau"}, "refreshList": {"message": "Ra<PERSON><PERSON><PERSON><PERSON>"}, "reject": {"message": "<PERSON><PERSON><PERSON>"}, "rejectAll": {"message": "Refuser tout"}, "rejectRequestsDescription": {"message": "Vous êtes sur le point de rejeter une série de demandes de $1."}, "rejectRequestsN": {"message": "Rejeter les demandes de $1"}, "rejectTxsDescription": {"message": "Vous êtes sur le point de rejeter en groupe les transactions $1."}, "rejectTxsN": {"message": "Rejeter les transactions $1"}, "rejected": {"message": "<PERSON><PERSON><PERSON>"}, "remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "removeAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> le compte"}, "removeAccountDescription": {"message": "Ce compte va être supprimé de votre portefeuille. Veuillez vérifier que vous avez la phrase secrète de récupération originale de ce compte ou la clé privée pour ce compte importé avant de continuer. Vous pouvez importer ou créer à nouveau des comptes à partir du menu des comptes. "}, "removeKeyringSnap": {"message": "En supprimant ce snap, vous supprimerez ces comptes de MetaMask :"}, "removeKeyringSnapToolTip": {"message": "Les comptes sont gérés par ce snap. Si vous le supprimez, les comptes seront supprimés de MetaMask, mais pas de la blockchain."}, "removeNFT": {"message": "Supprimer le NFT"}, "removeNftErrorMessage": {"message": "Nous n’avons pas pu supprimer ce NFT."}, "removeNftMessage": {"message": "Le NFT a été supprimé avec succès !"}, "removeSnap": {"message": "<PERSON><PERSON><PERSON><PERSON> le Snap"}, "removeSnapAccountDescription": {"message": "<PERSON> vous continuez, ce compte ne sera plus disponible dans MetaMask."}, "removeSnapAccountTitle": {"message": "Supprimer le compte"}, "removeSnapConfirmation": {"message": "Voulez-vous vraiment supprimer $1 ?", "description": "$1 represents the name of the snap"}, "removeSnapDescription": {"message": "Cette action supprimera le snap, ses données et révoquera vos autorisations."}, "replace": {"message": "remplacer"}, "reportIssue": {"message": "Signaler un problème"}, "requestFrom": {"message": "<PERSON><PERSON><PERSON> de"}, "requestFromInfo": {"message": "C'est le site qui vous demande votre signature."}, "requestFromInfoSnap": {"message": "Il s’agit du Snap qui demande votre signature."}, "requestFromTransactionDescription": {"message": "Il s’agit du site qui demande votre confirmation."}, "requestingFor": {"message": "<PERSON><PERSON><PERSON> de"}, "requestingForAccount": {"message": "Demande de $1", "description": "Name of Account"}, "requestingForNetwork": {"message": "Demande pour $1", "description": "Name of Network"}, "required": {"message": "Obligatoire"}, "reset": {"message": "Reinitialiser"}, "resetWallet": {"message": "Réinitialiser le portefeuille"}, "resetWalletSubHeader": {"message": "MetaMask ne conserve pas de copie de votre mot de passe. Si vous avez des difficultés à déverrouiller votre compte, vous devrez réinitialiser votre portefeuille. Vous pouvez le faire en fournissant la phrase de récupération secrète que vous avez saisie lors de la configuration de votre portefeuille."}, "resetWalletUsingSRP": {"message": "Cette action supprimera votre portefeuille actuel et votre phrase de récupération secrète de cet appareil, ainsi que la liste des comptes que vous avez sélectionnés. Après la réinitialisation avec une phrase de récupération secrète, vous verrez une liste de comptes basés sur la phrase de récupération secrète que vous utilisez pour réinitialiser. Cette nouvelle liste comprendra automatiquement les comptes qui ont un solde. Vous pourrez également $1 créé auparavant. Les comptes personnalisés que vous avez importés devront être $2 et tous les jetons personnalisés que vous avez ajoutés à un compte devront également être $3."}, "resetWalletWarning": {"message": "Vérifiez que vous avez saisi correctement la phrase de récupération secrète avant de continuer. Vous ne pourrez pas annuler cette action."}, "restartMetamask": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "restore": {"message": "<PERSON><PERSON><PERSON>"}, "restoreUserData": {"message": "Restaurer les données de l’utilisateur"}, "resultPageError": {"message": "<PERSON><PERSON><PERSON>"}, "resultPageErrorDefaultMessage": {"message": "L’opération a échoué."}, "resultPageSuccess": {"message": "Réussite"}, "resultPageSuccessDefaultMessage": {"message": "L’opération a été effectuée avec succès."}, "retryTransaction": {"message": "Retenter la transaction"}, "reusedTokenNameWarning": {"message": "L’un de ces jetons réutilise le symbole d’un autre jeton que vous surveillez, ce qui peut être déroutant ou trompeur."}, "revealSecretRecoveryPhrase": {"message": "Révéler la phrase secrète de récupération"}, "revealSeedWords": {"message": "Révéler la phrase secrète de récupération"}, "revealSeedWordsDescription1": {"message": "La $1 donne $2", "description": "This is a sentence consisting of link using 'revealSeedWordsSRPName' as $1 and bolded text using 'revealSeedWordsDescription3' as $2."}, "revealSeedWordsDescription2": {"message": "MetaMask est un $1. Cela signifie que vous êtes le seul à connaître votre PSR.", "description": "$1 is text link with the message from 'revealSeedWordsNonCustodialWallet'"}, "revealSeedWordsDescription3": {"message": "un accès complet à votre portefeuille et à vos fonds.\n"}, "revealSeedWordsNonCustodialWallet": {"message": "portefeuille non dépositaire"}, "revealSeedWordsQR": {"message": "QR"}, "revealSeedWordsSRPName": {"message": "Phrase secrète de récupération (PSR)"}, "revealSeedWordsText": {"message": "Texte"}, "revealSeedWordsWarning": {"message": "Assurez-vous que personne ne regarde votre écran. $1", "description": "$1 is bolded text using the message from 'revealSeedWordsWarning2'"}, "revealSeedWordsWarning2": {"message": "Le service d’assistance de MetaMask ne vous la demandera jamais.", "description": "The bolded texted in the second part of 'revealSeedWordsWarning'"}, "revealSensitiveContent": {"message": "Ré<PERSON><PERSON><PERSON> les contenus sensibles"}, "review": {"message": "Examiner"}, "reviewAlert": {"message": "Examiner l<PERSON>alerte"}, "reviewAlerts": {"message": "Examiner les alertes"}, "reviewPendingTransactions": {"message": "Revoir les transactions en attente"}, "reviewPermissions": {"message": "Revoir les autorisations"}, "revokePermission": {"message": "Retirer l'autorisation"}, "revokePermissionTitle": {"message": "Révoquer l’autorisation de $1", "description": "The token symbol that is being revoked"}, "revokeSimulationDetailsDesc": {"message": "Vous révoquez l’autorisation que vous avez accordée à quelqu’un d’autre de dépenser des jetons à partir de votre compte."}, "reward": {"message": "Récompense"}, "rpcNameOptional": {"message": "Nom du RPC (facultatif)"}, "rpcUrl": {"message": "URL de RPC"}, "safeTransferFrom": {"message": "Transfert sécuris<PERSON> depuis"}, "save": {"message": "Enregistrer"}, "scanInstructions": {"message": "Placez le code QR devant votre appareil photo"}, "scanQrCode": {"message": "Scannez le code QR"}, "scrollDown": {"message": "Faites défiler vers le bas"}, "search": {"message": "<PERSON><PERSON><PERSON>"}, "searchAccounts": {"message": "Rechercher des comptes"}, "searchNfts": {"message": "Recherche de NFT"}, "searchTokens": {"message": "Rechercher des jetons"}, "searchTokensByNameOrAddress": {"message": "Recherche de jetons par nom ou par adresse"}, "secretRecoveryPhrase": {"message": "Phrase secrète de récupération"}, "secretRecoveryPhrasePlusNumber": {"message": "Phrase secrète de récupération $1", "description": "The $1 is the order of the Secret Recovery Phrase"}, "secureWallet": {"message": "Portefeuille sécurisé"}, "security": {"message": "Sécurité"}, "securityAlert": {"message": "Alerte de sécurité provenant de $1 et de $2"}, "securityAlerts": {"message": "<PERSON><PERSON><PERSON> de sécurité"}, "securityAlertsDescription": {"message": "Cette fonctionnalité vous alerte en cas d’activité malveillante ou inhabituelle en examinant activement les demandes de transaction et de signature. $1", "description": "Link to learn more about security alerts"}, "securityAndPrivacy": {"message": "Sécurité et confidentialité"}, "securityDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> le risque de rejoindre des réseaux dangereux et protégez vos comptes"}, "securityMessageLinkForNetworks": {"message": "arnaques et risques de piratage informatique"}, "securityProviderPoweredBy": {"message": "Service fourni par $1", "description": "The security provider that is providing data"}, "seeAllPermissions": {"message": "Voir toutes les autorisations", "description": "Used for revealing more content (e.g. permission list, etc.)"}, "seeDetails": {"message": "Voir les détails"}, "seedPhraseIntroTitle": {"message": "Sécuriser votre portefeuille"}, "seedPhraseReq": {"message": "Les phrases secrètes de récupération sont composées de 12, 15, 18, 21 ou 24 mots"}, "select": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selectAccountToConnect": {"message": "Sélectionner un compte à connecter"}, "selectAccounts": {"message": "Sélectionnez le(s) compte(s) à utiliser sur ce site"}, "selectAccountsForSnap": {"message": "Sélectionnez le(s) compte(s) à utiliser avec ce snap"}, "selectAll": {"message": "<PERSON><PERSON>"}, "selectAnAccount": {"message": "Sélectionner un compte"}, "selectAnAccountAlreadyConnected": {"message": "Ce compte a déjà été connecté à MetaMask"}, "selectEnableDisplayMediaPrivacyPreference": {"message": "Activer l’option « Afficher les supports NFT »"}, "selectHdPath": {"message": "Sélectionner le chemin HD"}, "selectNFTPrivacyPreference": {"message": "Activez la détection automatique des NFT"}, "selectPathHelp": {"message": "Si vous ne voyez pas les comptes auxquels vous vous attendez, essayez de changer le chemin d’accès au portefeuille HD ou le réseau sélectionné."}, "selectRpcUrl": {"message": "Sélectionner l’URL du RPC"}, "selectSecretRecoveryPhrase": {"message": "Sélectionnez la phrase secrète de récupération"}, "selectType": {"message": "Sélectionner le type"}, "selectedAccountMismatch": {"message": "Compte différent sélectionné"}, "selectingAllWillAllow": {"message": "En sélectionnant tout, vous autorisez ce site à voir tous vos comptes actuels. Assurez-vous de bien avoir confiance en ce site."}, "send": {"message": "Envoyer"}, "sendBugReport": {"message": "Envoyez-nous un rapport de bogue."}, "sendNoContactsConversionText": {"message": "cliquez ici"}, "sendNoContactsDescription": {"message": "Les contacts vous permettent d’envoyer en toute sécurité plusieurs transactions vers un autre compte. Pour créer un contact, $1", "description": "$1 represents the action text 'click here'"}, "sendNoContactsTitle": {"message": "Vous n’avez aucun contact"}, "sendSelectReceiveAsset": {"message": "Sélectionnez l’actif que vous recevrez"}, "sendSelectSendAsset": {"message": "Sélectionnez l’actif que vous voulez envoyer"}, "sendSpecifiedTokens": {"message": "Envoyer $1", "description": "Symbol of the specified token"}, "sendSwapSubmissionWarning": {"message": "En cliquant sur ce bouton, vous entamez immédiatement l’opération d’échange. Veuillez vérifier les détails de votre transaction avant de continuer."}, "sendTokenAsToken": {"message": "Échanger des $1 contre des $2 et les envoyer", "description": "Used in the transaction display list to describe a swap and send. $1 and $2 are the symbols of tokens in involved in the swap."}, "sendingAsset": {"message": "Envoi de $1"}, "sendingDisabled": {"message": "L’envoi d’actifs ERC-1155 NFT n’est pas encore pris en charge."}, "sendingNativeAsset": {"message": "Envoi de $1", "description": "$1 represents the native currency symbol for the current network (e.g. ETH or BNB)"}, "sendingToTokenContractWarning": {"message": "Attention : vous êtes sur le point d’envoyer des jetons à l’adresse d’un contrat de jetons qui pourrait entraîner une perte de fonds. $1", "description": "$1 is a clickable link with text defined by the 'learnMoreUpperCase' key. The link will open to a support article regarding the known contract address warning"}, "sepolia": {"message": "Réseau de test Sepolia"}, "setApprovalForAll": {"message": "Définir l’approbation pour tous"}, "setApprovalForAllRedesignedTitle": {"message": "<PERSON><PERSON><PERSON> de retrait"}, "setApprovalForAllTitle": {"message": "Approuver $1 sans limite de dépenses", "description": "The token symbol that is being approved"}, "settingAddSnapAccount": {"message": "Ajouter un compte Snap"}, "settings": {"message": "Paramètres"}, "settingsSearchMatchingNotFound": {"message": "Aucun résultat correspondant trouvé."}, "settingsSubHeadingSignaturesAndTransactions": {"message": "Demandes de signature et de transaction"}, "show": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "showAccount": {"message": "<PERSON><PERSON><PERSON><PERSON> le compte"}, "showAdvancedDetails": {"message": "A<PERSON><PERSON><PERSON> les détails avancés"}, "showExtensionInFullSizeView": {"message": "Afficher l’extension en taille réelle"}, "showExtensionInFullSizeViewDescription": {"message": "Activez cette option si vous voulez que l’extension s’affiche en taille réelle lorsque vous cliquez sur l’icône de l’extension."}, "showFiatConversionInTestnets": {"message": "Afficher la conversion sur les testnets"}, "showFiatConversionInTestnetsDescription": {"message": "Sélectionnez cette option pour afficher la conversion des monnaies fiduciaires sur Testnets."}, "showHexData": {"message": "Afficher les données Hex"}, "showHexDataDescription": {"message": "Selectionner ici pour afficher le champs de données hex dans l’écran d’envoi"}, "showLess": {"message": "Affiche<PERSON> moins"}, "showMore": {"message": "Afficher plus"}, "showNativeTokenAsMainBalance": {"message": "Aff<PERSON>r le solde principal en jeton natif"}, "showNft": {"message": "Afficher le NFT"}, "showPermissions": {"message": "Afficher les autorisations"}, "showPrivateKey": {"message": "Afficher la clé privée"}, "showSRP": {"message": "Afficher la phrase secrète de récupération"}, "showTestnetNetworks": {"message": "Afficher les réseaux de test"}, "showTestnetNetworksDescription": {"message": "Sélectionnez ceci pour afficher les réseaux de test dans la liste des réseaux"}, "sign": {"message": "Signer"}, "signatureRequest": {"message": "<PERSON><PERSON><PERSON> de Signature"}, "signature_decoding_bid_nft_tooltip": {"message": "Le NFT sera affiché dans votre portefeuille si l’offre est acceptée."}, "signature_decoding_list_nft_tooltip": {"message": "Ne vous attendez à des changements que si quelqu’un achète vos NFT."}, "signed": {"message": "<PERSON><PERSON>"}, "signing": {"message": "Signature"}, "signingInWith": {"message": "Se connecter avec"}, "signingWith": {"message": "Signer avec"}, "simulationApproveHeading": {"message": "<PERSON><PERSON><PERSON>"}, "simulationDetailsApproveDesc": {"message": "Vous donnez à quelqu’un d’autre l’autorisation de retirer des NFT de votre compte."}, "simulationDetailsERC20ApproveDesc": {"message": "Vous accordez à quelqu’un d’autre l’autorisation de dépenser ce montant qui sera débité de votre compte."}, "simulationDetailsFiatNotAvailable": {"message": "Non disponible"}, "simulationDetailsIncomingHeading": {"message": "<PERSON><PERSON> rece<PERSON>"}, "simulationDetailsNoChanges": {"message": "Aucun changement"}, "simulationDetailsOutgoingHeading": {"message": "Vous envoyez"}, "simulationDetailsRevokeSetApprovalForAllDesc": {"message": "Vous révoquez l’autorisation que vous avez accordée à quelqu’un d’autre de retirer des NFT de votre compte."}, "simulationDetailsSetApprovalForAllDesc": {"message": "Vous accordez à quelqu’un d’autre l’autorisation de retirer des NFT de votre compte."}, "simulationDetailsTitle": {"message": "Changements estimés"}, "simulationDetailsTitleTooltip": {"message": "Les changements estimés représentent ce qui pourrait se produire si vous effectuez cette transaction. Il s’agit juste d’une estimation fournie à des fins d’information."}, "simulationDetailsTotalFiat": {"message": "Total = $1", "description": "$1 is the total amount in fiat currency on one side of the transaction"}, "simulationDetailsTransactionReverted": {"message": "Cette transaction va probablement échouer"}, "simulationDetailsUnavailable": {"message": "Non disponible"}, "simulationErrorMessageV2": {"message": "Nous n’avons pas pu estimer le prix de carburant. Par conséquent, il se peut qu’il y ait une erreur dans le contrat et que cette transaction échoue."}, "simulationsSettingDescription": {"message": "Activez cette option pour estimer les changements de solde des transactions et des signatures avant de les confirmer. Ces estimations sont fournies à titre indicatif et n’engagent en rien les parties à la transaction. $1"}, "simulationsSettingSubHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> les changements de solde"}, "singleNetwork": {"message": "1 réseau"}, "siweIssued": {"message": "<PERSON><PERSON>"}, "siweNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "siweRequestId": {"message": "Demander un ID"}, "siweResources": {"message": "Ressources"}, "siweURI": {"message": "URL"}, "skipAccountSecurity": {"message": "Sauter le réglage des paramètres de sécurité du compte ?"}, "skipAccountSecurityDetails": {"message": "Je suis conscient(e) que tant que je n’aurai pas sauvegardé ma phrase secrète de récupération, je risque de perdre mes comptes et tous leurs actifs."}, "slideBridgeDescription": {"message": "Faites le tour des 9 chaînes, depuis votre portefeuille"}, "slideBridgeTitle": {"message": "<PERSON>r<PERSON><PERSON> à établir une passerelle ?"}, "slideCashOutDescription": {"message": "Vendez vos crypto-monnaies et recevez de l’argent"}, "slideCashOutTitle": {"message": "Effectuez des retraits avec MetaMask"}, "slideDebitCardDescription": {"message": "Disponible dans certaines régions"}, "slideDebitCardTitle": {"message": "Carte de débit MetaMask"}, "slideFundWalletDescription": {"message": "<PERSON><PERSON><PERSON><PERSON> ou transférez des jetons pour commencer"}, "slideFundWalletTitle": {"message": "Approvisionnez votre portefeuille"}, "slideMultiSrpDescription": {"message": "Importer et utiliser plusieurs portefeuilles dans MetaMask"}, "slideMultiSrpTitle": {"message": "Ajouter plusieurs phrases secrètes de récupération"}, "slideRemoteModeDescription": {"message": "Utiliser votre portefeuille déconnecté à distance"}, "slideRemoteModeTitle": {"message": "Stockage déconnecté, accès rapide"}, "slideSmartAccountUpgradeDescription": {"message": "<PERSON><PERSON><PERSON>, des fonctionnalités plus intelligentes"}, "slideSmartAccountUpgradeTitle": {"message": "Commencez à utiliser des comptes intelligents"}, "slideSolanaDescription": {"message": "Créez un compte Solana pour commencer"}, "slideSolanaTitle": {"message": "Le Solana est maintenant pris en charge"}, "slideSweepStakeDescription": {"message": "Mintez un NFT maintenant pour avoir une chance de gagner"}, "slideSweepStakeTitle": {"message": "Participez au tirage au sort qui vous permet de gagner $5000 USDC !"}, "smartAccountAccept": {"message": "Utiliser un compte intelligent"}, "smartAccountBetterTransaction": {"message": "Transactions plus rapides, frais réduits"}, "smartAccountBetterTransactionDescription": {"message": "Gagnez du temps et de l’argent en traitant plusieurs transactions en même temps."}, "smartAccountFeaturesDescription": {"message": "Vous conserverez la même adresse de compte et pourrez repasser à un compte standard à tout moment."}, "smartAccountLabel": {"message": "<PERSON><PERSON><PERSON> intelligent"}, "smartAccountPayToken": {"message": "Payez en utilisant n’importe quel jeton, à tout moment"}, "smartAccountPayTokenDescription": {"message": "Utilisez les jetons que vous possédez déjà pour couvrir les frais de réseau."}, "smartAccountReject": {"message": "Ne pas utiliser un compte intelligent"}, "smartAccountRequestFor": {"message": "<PERSON><PERSON><PERSON> de"}, "smartAccountSameAccount": {"message": "<PERSON><PERSON><PERSON> compte, mais des fonctionnalités plus intelligentes."}, "smartAccountSplashTitle": {"message": "Voulez-vous utiliser un compte intelligent ?"}, "smartAccountUpgradeBannerDescription": {"message": "<PERSON><PERSON><PERSON> adresse. Des fonctionnalités plus intelligentes."}, "smartAccountUpgradeBannerTitle": {"message": "Passez à un compte intelligent"}, "smartContracts": {"message": "Contrats intelligents"}, "smartSwapsErrorNotEnoughFunds": {"message": "Fonds insuffisants pour souscrire un contrat de swap intelligent."}, "smartSwapsErrorUnavailable": {"message": "Les contrats de swap intelligents sont temporairement indisponibles."}, "smartTransactionCancelled": {"message": "Votre transaction a été annulée"}, "smartTransactionCancelledDescription": {"message": "Votre transaction n’a pas pu être effectuée, elle a donc été annulée pour vous éviter de payer inutilement des frais de gaz."}, "smartTransactionError": {"message": "Votre transaction a échoué"}, "smartTransactionErrorDescription": {"message": "Des changements soudains sur le marché peuvent entraîner des échecs de transaction. Si le problème persiste, contactez le service d’assistance à la clientèle de MetaMask."}, "smartTransactionPending": {"message": "Votre transaction a été soumise"}, "smartTransactionSuccess": {"message": "Votre transaction est terminée"}, "smartTransactions": {"message": "Transactions intelligentes"}, "smartTransactionsEnabledDescription": {"message": " et la protection de la MEV. Désormais activées par défaut."}, "smartTransactionsEnabledLink": {"message": "Taux de réussite plus élevés"}, "smartTransactionsEnabledTitle": {"message": "Les transactions sont devenues plus intelligentes"}, "snapAccountCreated": {"message": "Le compte a été créé"}, "snapAccountCreatedDescription": {"message": "Votre nouveau compte est prêt à être utilisé !"}, "snapAccountCreationFailed": {"message": "La création du compte a échoué"}, "snapAccountCreationFailedDescription": {"message": "$1 n’a pas réussi à créer un compte pour vous.", "description": "$1 is the snap name"}, "snapAccountRedirectFinishSigningTitle": {"message": "Finaliser la signature"}, "snapAccountRedirectSiteDescription": {"message": "Su<PERSON>z les instructions de $1"}, "snapAccountRemovalFailed": {"message": "La suppression du compte a échoué"}, "snapAccountRemovalFailedDescription": {"message": "$1 n’a pas réussi à supprimer ce compte pour vous.", "description": "$1 is the snap name"}, "snapAccountRemoved": {"message": "Compte supprimé"}, "snapAccountRemovedDescription": {"message": "Ce compte ne pourra plus être utilisé dans MetaMask."}, "snapAccounts": {"message": "Snaps de compte"}, "snapAccountsDescription": {"message": "Comptes contrôlés par des Snaps tiers."}, "snapConnectTo": {"message": "Connexion à $1", "description": "$1 is the website URL or a Snap name. Used for Snaps pre-approved connections."}, "snapConnectionPermissionDescription": {"message": "Laissez $1 se connecter automatiquement à $2 sans votre approbation.", "description": "Used for Snap pre-approved connections. $1 is the Snap name, $2 is a website URL."}, "snapConnectionWarning": {"message": "$1 veut utiliser $2", "description": "$2 is the snap and $1 is the dapp requesting connection to the snap."}, "snapDetailWebsite": {"message": "Site web"}, "snapHomeMenu": {"message": "<PERSON>u d<PERSON>acc<PERSON><PERSON>"}, "snapInstallRequest": {"message": "L’installation de $1 lui accorde les autorisations suivantes.", "description": "$1 is the snap name."}, "snapInstallSuccess": {"message": "Installation terminée"}, "snapInstallWarningCheck": {"message": "Le Snap $1 demande l’autorisation de faire ce qui suit :", "description": "Warning message used in popup displayed on snap install. $1 is the snap name."}, "snapInstallWarningHeading": {"message": "Agissez avec prudence"}, "snapInstallWarningPermissionDescriptionForBip32View": {"message": "Autoriser le Snap $1 à consulter vos clés publiques (et vos adresses). Cela n’accorde pas l’autorisation de gérer les comptes ou les actifs.", "description": "An extended description for the `snap_getBip32PublicKey` permission used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionDescriptionForEntropy": {"message": "Autoriser le Snap $1 à gérer des comptes et des actifs sur le(s) réseau(x) demandé(s). Ces comptes sont dérivés et sauvegardés à l'aide de votre phrase secrète de récupération (sans la divulguer). Grâce à sa capacité à dériver des clés, le Snap $1 peut prendre en charge des protocoles blockchain autres que les protocoles de la blockchain Ethereum (EVM).", "description": "An extended description for the `snap_getBip44Entropy` and `snap_getBip44Entropy` permissions used for tooltip on Snap Install Warning screen (popup/modal). $1 is the snap name."}, "snapInstallWarningPermissionNameForEntropy": {"message": "Gérer les comptes $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallWarningPermissionNameForViewPublicKey": {"message": "Afficher la clé publique pour $1", "description": "Permission name used for the Permission Cell component displayed on warning popup when installing a Snap. $1 is list of account types."}, "snapInstallationErrorDescription": {"message": "$1 n’a pas pu être installé.", "description": "Error description used when snap installation fails. $1 is the snap name."}, "snapInstallationErrorTitle": {"message": "L’installation a échoué", "description": "Error title used when snap installation fails."}, "snapResultError": {"message": "<PERSON><PERSON><PERSON>"}, "snapResultSuccess": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "snapResultSuccessDescription": {"message": "$1 est prêt à être utilisé"}, "snapUIAssetSelectorTitle": {"message": "Sélectionner un actif"}, "snapUpdateAlertDescription": {"message": "Obtenez la dernière version de $1", "description": "Description used in Snap update alert banner when snap update is available. $1 is the Snap name."}, "snapUpdateAvailable": {"message": "Une mise à jour est disponible"}, "snapUpdateErrorDescription": {"message": "La mise à jour de $1 a échoué.", "description": "Error description used when snap update fails. $1 is the snap name."}, "snapUpdateErrorTitle": {"message": "La mise à jour a échoué", "description": "Error title used when snap update fails."}, "snapUpdateRequest": {"message": "La mise à jour de $1 lui accorde les autorisations suivantes.", "description": "$1 is the Snap name."}, "snapUpdateSuccess": {"message": "Mise à jour terminée"}, "snapUrlIsBlocked": {"message": "Ce Snap veut vous emmener sur un site bloqué. $1."}, "snaps": {"message": "Snaps"}, "snapsConnected": {"message": "Snaps connectés"}, "snapsNoInsight": {"message": "Aucun aperçu à montrer"}, "snapsPrivacyWarningFirstMessage": {"message": "Vous reconnaissez que tout Snap que vous installez (sauf indication contraire) est un « Service tiers » tel que défini dans les $1 de Consensys. L’utilisation des services tiers est régie par des conditions distinctes définies par les fournisseurs de services tiers. Consensys ne recommande pas à des personnes particulières d’utiliser un Snap pour des raisons particulières. Si vous décidez d’utiliser un service tiers, vous le faites à vos propres risques. Consensys décline toute responsabilité pour toute perte liée à l’utilisation des services tiers.", "description": "First part of a message in popup modal displayed when installing a snap for the first time. $1 is terms of use link."}, "snapsPrivacyWarningSecondMessage": {"message": "Toute information que vous partagez avec des services tiers sera collectée directement par ces services tiers conformément à leur politique de confidentialité. Pour plus d’informations, veuillez consulter leur politique de confidentialité.", "description": "Second part of a message in popup modal displayed when installing a snap for the first time."}, "snapsPrivacyWarningThirdMessage": {"message": "Consensys n’a pas accès aux informations que vous partagez avec les fournisseurs de services tiers.", "description": "Third part of a message in popup modal displayed when installing a snap for the first time."}, "snapsSettings": {"message": "Paramètres du Snap"}, "snapsTermsOfUse": {"message": "Conditions d’utilisation"}, "snapsToggle": {"message": "Un snap ne s’exécute que s’il est activé"}, "snapsUIError": {"message": "L’interface utilisateur (IU) spécifiée par le snap n’est pas valide.", "description": "This is shown when the insight snap throws an error. $1 is the snap name"}, "solanaAccountRequested": {"message": "Ce site requiert un compte Solana."}, "solanaAccountRequired": {"message": "Un compte Solana est nécessaire pour se connecter à ce site."}, "solanaImportAccounts": {"message": "Importer des comptes Solana"}, "solanaImportAccountsDescription": {"message": "Importez une phrase secrète de récupération pour faire migrer votre compte Solana depuis un autre portefeuille."}, "solanaMoreFeaturesComingSoon": {"message": "D’autres fonctionnalités seront bientôt disponibles"}, "solanaMoreFeaturesComingSoonDescription": {"message": "Les NFT, la prise en charge des portefeuilles matériels, etc., seront bientôt disponibles."}, "solanaOnMetaMask": {"message": "Solana sur MetaMask"}, "solanaSendReceiveSwapTokens": {"message": "Envoyez, recevez et échangez des jetons"}, "solanaSendReceiveSwapTokensDescription": {"message": "Transférez et effectuez des transactions avec des jetons tels que SOL, USDC, etc."}, "someNetworks": {"message": "$1 réseaux"}, "somethingDoesntLookRight": {"message": "On dirait que quelque chose ne va pas ? $1", "description": "A false positive message for users to contact support. $1 is a link to the support page."}, "somethingIsWrong": {"message": "Un problème est survenu. Essayez de recharger la page."}, "somethingWentWrong": {"message": "Nous n’avons pas pu charger cette page."}, "sortBy": {"message": "Trier par"}, "sortByAlphabetically": {"message": "Ordre alphabétique (de A à Z)"}, "sortByDecliningBalance": {"message": "Solde décroissant (du solde le plus élevé au solde le moins élevé en $1)", "description": "Indicates a descending order based on token fiat balance. $1 is the preferred currency symbol"}, "source": {"message": "Source"}, "spamModalBlockedDescription": {"message": "Ce site sera bloqué pendant 1 minute."}, "spamModalBlockedTitle": {"message": "Vous avez temporairement bloqué ce site"}, "spamModalDescription": {"message": "Si vous êtes inondé de demandes, vous pouvez bloquer temporairement le site."}, "spamModalTemporaryBlockButton": {"message": "Bloquer temporairement ce site"}, "spamModalTitle": {"message": "Nous avons remarqué un afflux de demandes"}, "speed": {"message": "Vitesse"}, "speedUp": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "speedUpCancellation": {"message": "Accélérer cette annulation"}, "speedUpExplanation": {"message": "Nous avons mis à jour le prix du carburant selon les conditions actuelles du réseau et l’avons augmenté d’au moins 10 % (requis par le réseau)."}, "speedUpPopoverTitle": {"message": "Accélérer la transaction"}, "speedUpTooltipText": {"message": "Nouveau prix de carburant"}, "speedUpTransaction": {"message": "Accélérez cette transaction"}, "spendLimitInsufficient": {"message": "Limite de dépenses insuffisante"}, "spendLimitInvalid": {"message": "<PERSON><PERSON> de d<PERSON><PERSON>ses invalide ; cela doit être une valeur positive"}, "spendLimitPermission": {"message": "Autorisation de limite de dépenses"}, "spendLimitRequestedBy": {"message": "Limite de dépenses demandée par $1", "description": "Origin of the site requesting the spend limit"}, "spendLimitTooLarge": {"message": "Limite de dépenses trop élevée"}, "spender": {"message": "Dépenseur"}, "spenderTooltipDesc": {"message": "Il s’agit de l’adresse qui pourra retirer vos NFT."}, "spenderTooltipERC20ApproveDesc": {"message": "Il s’agit de l’adresse qui pourra dépenser vos jetons en votre nom."}, "spendingCap": {"message": "Plafond de <PERSON>"}, "spendingCaps": {"message": "Plafonds de dépenses"}, "srpInputNumberOfWords": {"message": "J’ai une phrase de $1 mots", "description": "This is the text for each option in the dropdown where a user selects how many words their secret recovery phrase has during import. The $1 is the number of words (either 12, 15, 18, 21, or 24)."}, "srpListName": {"message": "Phrase secrète de récupération $1", "description": "$1 is the order of the Secret Recovery Phrase"}, "srpListNumberOfAccounts": {"message": "$1 comptes", "description": "$1 is the number of accounts in the list"}, "srpListSelectionDescription": {"message": "La phrase secrète de récupération à partir de laquelle votre nouveau compte sera généré"}, "srpListSingleOrZero": {"message": "$1 compte", "description": "$1 is the number of accounts in the list, it is either 1 or 0"}, "srpPasteFailedTooManyWords": {"message": "Le collage a échoué parce que la phrase contenait plus de 24 mots. Une phrase secrète de récupération peut contenir un maximum de 24 mots.", "description": "Description of SRP paste error when the pasted content has too many words"}, "srpPasteTip": {"message": "Vous pouvez coller toute votre phrase de récupération secrète dans n’importe quel champ", "description": "Our secret recovery phrase input is split into one field per word. This message explains to users that they can paste their entire secrete recovery phrase into any field, and we will handle it correctly."}, "srpSecurityQuizGetStarted": {"message": "Commencer"}, "srpSecurityQuizImgAlt": {"message": "Un œil avec un trou de serrure au centre et trois champs de mots de passe flottants"}, "srpSecurityQuizIntroduction": {"message": "Pour révéler votre Phrase secrète de récupération, vous devez répondre correctement à deux questions"}, "srpSecurityQuizQuestionOneQuestion": {"message": "Si vous perdez votre Phrase secrète de récupération, MetaMask..."}, "srpSecurityQuizQuestionOneRightAnswer": {"message": "<PERSON>e pourra pas vous aider"}, "srpSecurityQuizQuestionOneRightAnswerDescription": {"message": "Gravez-la sur une plaque en métal ou inscrivez-la sur plusieurs bouts de papier et cachez-les dans différents endroits secrets pour ne jamais la perdre. Si vous la perdez, il n'y a aucun moyen de la récupérer."}, "srpSecurityQuizQuestionOneRightAnswerTitle": {"message": "En effet ! Personne ne peut vous aider à récupérer votre Phrase secrète de récupération."}, "srpSecurityQuizQuestionOneWrongAnswer": {"message": "Pourra la récupérer pour vous"}, "srpSecurityQuizQuestionOneWrongAnswerDescription": {"message": "<PERSON>ne ne peut vous aider à récupérer votre Phrase secrète de récupération si jamais vous la perdez."}, "srpSecurityQuizQuestionOneWrongAnswerTitle": {"message": "C'est faux ! Personne ne peut vous aider à récupérer votre Phrase secrète de récupération."}, "srpSecurityQuizQuestionTwoQuestion": {"message": "Si un membre du service d'assistance ou toute autre personne vous demande votre Phrase secrète de récupération..."}, "srpSecurityQuizQuestionTwoRightAnswer": {"message": "<PERSON>e la lui fournissez pas, car cette personne essaie de vous arnaquer."}, "srpSecurityQuizQuestionTwoRightAnswerDescription": {"message": "Toute personne qui vous demande votre Phrase secrète de récupération, que ce soit pour des raisons de sécurité ou autre, essaie de vous arnaquer."}, "srpSecurityQuizQuestionTwoRightAnswerTitle": {"message": "C'est exact ! Vous ne devez jamais partager votre Phrase secrète de récupération avec qui que ce soit."}, "srpSecurityQuizQuestionTwoWrongAnswer": {"message": "<PERSON><PERSON> devez la lui fournir"}, "srpSecurityQuizQuestionTwoWrongAnswerDescription": {"message": "Toute personne qui vous demande votre Phrase secrète de récupération, que ce soit pour des raisons de sécurité ou autre, essaie de vous arnaquer."}, "srpSecurityQuizQuestionTwoWrongAnswerTitle": {"message": "C'est faux ! Vous ne devez jamais partager votre Phrase secrète de récupération avec qui que ce soit."}, "srpSecurityQuizTitle": {"message": "Quiz sur la sécurité"}, "srpToggleShow": {"message": "Afficher / Masquer ce mot de la phrase de récupération secrète", "description": "Describes a toggle that is used to show or hide a single word of the secret recovery phrase"}, "srpWordHidden": {"message": "Ce mot est caché", "description": "Explains that a word in the secret recovery phrase is hidden"}, "srpWordShown": {"message": "Ce mot est affiché", "description": "Explains that a word in the secret recovery phrase is being shown"}, "stable": {"message": "Stable"}, "stableLowercase": {"message": "stable"}, "stake": {"message": "<PERSON><PERSON>"}, "staked": {"message": "A staké"}, "standardAccountLabel": {"message": "Compte standard"}, "startEarning": {"message": "Commencer à générer des revenus"}, "stateLogError": {"message": "Erreur lors du chargement des journaux d’état."}, "stateLogFileName": {"message": "Journaux d’événements de MetaMask"}, "stateLogs": {"message": "Journaux d’événements"}, "stateLogsDescription": {"message": "Les journaux d’état contiennent les adresses publiques de vos comptes et vos transactions envoyées."}, "status": {"message": "État"}, "statusNotConnected": {"message": "Non connecté"}, "statusNotConnectedAccount": {"message": "Aucun compte connecté"}, "step1LatticeWallet": {"message": "Connectez votre Lattice1"}, "step1LatticeWalletMsg": {"message": "Vous pouvez connecter MetaMask à votre dispositif Lattice1 une fois qu’il est configuré et en ligne. Déverrouillez votre appareil et préparez son ID.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step1LedgerWallet": {"message": "Télécharger l’application Ledger"}, "step1LedgerWalletMsg": {"message": "T<PERSON><PERSON><PERSON><PERSON><PERSON>, configurez et saisissez votre mot de passe pour déverrouiller $1.", "description": "$1 represents the `ledgerLiveApp` localization value"}, "step1TrezorWallet": {"message": "Connecter le portefeuille Trezor"}, "step1TrezorWalletMsg": {"message": "Connecter votre Trezor directement sur votre ordinateur et déverrouillez-le. Assurez-vous que vous utilisez la bonne phrase de chiffrement.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "step2LedgerWallet": {"message": "Connecter le portefeuille Ledger"}, "step2LedgerWalletMsg": {"message": "Connectez votre portefeuille directement à votre ordinateur. Déverrouillez votre Ledger et ouvrez l’application Ethereum.", "description": "$1 represents the `hardwareWalletSupportLinkConversion` localization key"}, "stillGettingMessage": {"message": "Vous recevez toujours ce message ?"}, "strong": {"message": "<PERSON><PERSON><PERSON>"}, "stxCancelled": {"message": "Le swap aurait <PERSON>"}, "stxCancelledDescription": {"message": "Votre transaction aurait échoué et a été annulée pour vous éviter de payer inutilement des frais de transaction."}, "stxCancelledSubDescription": {"message": "R<PERSON><PERSON>ez le swap. Nous serons là pour vous protéger contre des risques similaires la prochaine fois."}, "stxFailure": {"message": "Échec du swap"}, "stxFailureDescription": {"message": "Les fluctuations soudaines du marché peuvent provoquer des échecs. Si le problème persiste, veuillez contacter $1.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "stxOptInSupportedNetworksDescription": {"message": "Activez les transactions intelligentes pour profiter de transactions plus fiables et plus sûres sur les réseaux pris en charge. $1"}, "stxPendingPrivatelySubmittingSwap": {"message": "Soumission privée de votre Swap..."}, "stxPendingPubliclySubmittingSwap": {"message": "Soumission publique de votre Swap..."}, "stxSuccess": {"message": "Swap terminé !"}, "stxSuccessDescription": {"message": "Votre $1 est maintenant disponible.", "description": "$1 is a token symbol, e.g. ETH"}, "stxSwapCompleteIn": {"message": "Le Swap sera effectué dans <", "description": "'<' means 'less than', e.g. <PERSON><PERSON><PERSON> will complete in < 2:59"}, "stxTryingToCancel": {"message": "Tentative d’annulation de votre transaction..."}, "stxUnknown": {"message": "État inconnu"}, "stxUnknownDescription": {"message": "Une transaction a ré<PERSON><PERSON>, mais nous ne la reconnaissons pas. <PERSON><PERSON> peut être dû à la soumission d’une autre transaction pendant le traitement de ce swap."}, "stxUserCancelled": {"message": "<PERSON><PERSON><PERSON> annu<PERSON>"}, "stxUserCancelledDescription": {"message": "Votre transaction a été annulée et vous avez évité ainsi de payer inutilement des frais de transaction."}, "submit": {"message": "Envoyer"}, "submitted": {"message": "<PERSON><PERSON><PERSON>"}, "suggestedBySnap": {"message": "Suggéré par $1", "description": "$1 is the snap name"}, "suggestedCurrencySymbol": {"message": "Symbole monétaire suggéré :"}, "suggestedTokenName": {"message": "Nom suggéré :"}, "supplied": {"message": "A fourni"}, "support": {"message": "Assistance"}, "supportCenter": {"message": "Visitez notre centre d’aide"}, "supportMultiRpcInformation": {"message": "Nous prenons désormais en charge plusieurs RPC pour un même réseau. Votre RPC le plus récent a été sélectionné par défaut afin de résoudre les problèmes liés à des informations contradictoires."}, "surveyConversion": {"message": "Répondez à notre sondage"}, "surveyTitle": {"message": "Façonnez l’avenir de MetaMask"}, "swap": {"message": "<PERSON><PERSON><PERSON>"}, "swapAdjustSlippage": {"message": "Ajuster l’effet de glissement"}, "swapAggregator": {"message": "Agré<PERSON>ur"}, "swapAllowSwappingOf": {"message": "Autoriser le swap de $1", "description": "Shows a user that they need to allow a token for swapping on their hardware wallet"}, "swapAmountReceived": {"message": "<PERSON><PERSON>"}, "swapAmountReceivedInfo": {"message": "Il s’agit du montant minimal que vous recevrez. Vous pouvez recevoir plus en fonction du glissement."}, "swapAndSend": {"message": "É<PERSON><PERSON> et envoyer"}, "swapAnyway": {"message": "Procéder de toute façon au swap"}, "swapApproval": {"message": "Approuver $1 pour les swaps", "description": "Used in the transaction display list to describe a transaction that is an approve call on a token that is to be swapped.. $1 is the symbol of a token that has been approved."}, "swapApproveNeedMoreTokens": {"message": "Vous avez besoin de $1 $2 de plus pour effectuer ce swap", "description": "Tells the user how many more of a given token they need for a specific swap. $1 is an amount of tokens and $2 is the token symbol."}, "swapAreYouStillThere": {"message": "Êtes-vous toujours là ?"}, "swapAreYouStillThereDescription": {"message": "Si vous le souhaitez, nous sommes prêts à vous présenter les dernières cotations"}, "swapConfirmWithHwWallet": {"message": "Confirmez avec votre portefeuille matériel"}, "swapContinueSwapping": {"message": "Continuer à faire des swaps"}, "swapContractDataDisabledErrorDescription": {"message": "Dans l’application Ethereum de votre Ledger, allez dans « Paramètres » et autorisez les données de contrat. Ensuite, retentez votre swap."}, "swapContractDataDisabledErrorTitle": {"message": "Les données de contrat ne sont pas activées sur votre Ledger"}, "swapCustom": {"message": "personnaliser"}, "swapDecentralizedExchange": {"message": "Échange d<PERSON>"}, "swapDirectContract": {"message": "Contrat direct"}, "swapEditLimit": {"message": "Modifier la limite"}, "swapEnableDescription": {"message": "Cette information est nécessaire et autorise MetaMask à effectuer le swap de vos $1.", "description": "Gives the user info about the required approval transaction for swaps. $1 will be the symbol of a token being approved for swaps."}, "swapEnableTokenForSwapping": {"message": "Ce sera $1 pour le swap", "description": "$1 is for the 'enableToken' key, e.g. 'enable ETH'"}, "swapEnterAmount": {"message": "Saisissez le montant"}, "swapEstimatedNetworkFees": {"message": "Frais de réseau estimés"}, "swapEstimatedNetworkFeesInfo": {"message": "Il s’agit d’une estimation des frais de réseau qui seront utilisés pour effectuer votre swap. Le montant réel peut varier en fonction des conditions du réseau."}, "swapFailedErrorDescriptionWithSupportLink": {"message": "Sachez que les transactions peuvent échouer et que nous sommes là pour vous aider. Si ce problème persiste, vous pouvez contacter notre service clientèle au $1 pour plus d’assistance.", "description": "This message is shown to a user if their swap fails. The $1 will be replaced by support.metamask.io"}, "swapFailedErrorTitle": {"message": "Échec du swap"}, "swapFetchingQuote": {"message": "Récupération de la cotation"}, "swapFetchingQuoteNofN": {"message": "Récupération de cotation $1 sur $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapFetchingQuotes": {"message": "Récupération des cotations…"}, "swapFetchingQuotesErrorDescription": {"message": "<PERSON>m… un problème est survenu. Réessayez et si les erreurs persistent, contactez le service client."}, "swapFetchingQuotesErrorTitle": {"message": "Erreur lors de la récupération des cotations"}, "swapFromTo": {"message": "Le swap de $1 vers $2", "description": "Tells a user that they need to confirm on their hardware wallet a swap of 2 tokens. $1 is a source token and $2 is a destination token"}, "swapGasFeesDetails": {"message": "Les frais de carburant sont estimés et fluctueront selon le trafic réseau et la complexité de la transaction."}, "swapGasFeesExplanation": {"message": "MetaMask ne tire aucun profit des frais de gaz. Ces frais sont des estimations et peuvent changer en fonction de l’activité du réseau et de la complexité de la transaction. En savoir plus $1.", "description": "$1 is a link (text in link can be found at 'swapGasFeesSummaryLinkText')"}, "swapGasFeesExplanationLinkText": {"message": "ici", "description": "Text for link in swapGasFeesExplanation"}, "swapGasFeesLearnMore": {"message": "En savoir plus sur les frais de carburant"}, "swapGasFeesSplit": {"message": "Les frais de carburant indiqués dans l’écran précédent sont répartis entre ces deux transactions."}, "swapGasFeesSummary": {"message": "Les frais de carburant sont payés aux mineurs de cryptomonnaies qui traitent les transactions sur le réseau $1. MetaMask ne tire aucun profit des frais de carburant.", "description": "$1 is the selected network, e.g. Ethereum or BSC"}, "swapGasIncludedTooltipExplanation": {"message": "Cette cotation incorpore les frais de gaz en ajustant le nombre de jetons envoyés ou reçus. Vous pouvez recevoir des ETH dans une transaction séparée sur votre liste d’activités."}, "swapGasIncludedTooltipExplanationLinkText": {"message": "En savoir plus sur les frais de gaz"}, "swapHighSlippage": {"message": "Important effet de glissement"}, "swapIncludesGasAndMetaMaskFee": {"message": "Inclut les frais de gaz et les frais MetaMask qui s’élèvent à $1 %", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFee": {"message": "Comprend des frais MetaMask à hauteur de $1 %.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapIncludesMMFeeAlt": {"message": "La cotation inclut les frais de change de MetaMask qui s’élèvent à $1 %", "description": "Provides information about the fee that metamask takes for swaps using the latest copy. $1 is a decimal number."}, "swapIncludesMetaMaskFeeViewAllQuotes": {"message": "Comprend des frais MetaMask à hauteur de $1 % – $2", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number and $2 is a link to view all quotes."}, "swapLearnMore": {"message": "En savoir plus sur les swaps"}, "swapLiquiditySourceInfo": {"message": "Nous consultons différentes sources de liquidité (bourses, agrégateurs et teneurs de marché professionnels) pour comparer les taux de change et les frais de réseau."}, "swapLowSlippage": {"message": "Faible effet de glissement"}, "swapMaxSlippage": {"message": "Glissement maximal"}, "swapMetaMaskFee": {"message": "<PERSON><PERSON>"}, "swapMetaMaskFeeDescription": {"message": "Des frais de $1 % sont automatiquement ajoutés à ce devis. Ces frais vous sont facturés en échange d'une licence d'utilisation du logiciel d'agrégation d'informations sur les fournisseurs de liquidités de MetaMask.", "description": "Provides information about the fee that metamask takes for swaps. $1 is a decimal number."}, "swapNQuotesWithDot": {"message": "$1 cotations.", "description": "$1 is the number of quotes that the user can select from when opening the list of quotes on the 'view quote' screen"}, "swapNewQuoteIn": {"message": "Nouvelles cotations dans $1", "description": "Tells the user the amount of time until the currently displayed quotes are update. $1 is a time that is counting down from 1:00 to 0:00"}, "swapNoTokensAvailable": {"message": "Aucun jeton disponible correspondant à $1", "description": "Tells the user that a given search string does not match any tokens in our token lists. $1 can be any string of text"}, "swapOnceTransactionHasProcess": {"message": "Vos $1 seront ajoutés à votre compte une fois que cette transaction sera traitée.", "description": "This message communicates the token that is being transferred. It is shown on the awaiting swap screen. The $1 will be a token symbol."}, "swapPriceDifference": {"message": "Vous êtes sur le point d’effectuer un swap de $1 $2 (~$3) contre $4 $5 (~$6).", "description": "This message represents the price slippage for the swap.  $1 and $4 are a number (ex: 2.89), $2 and $5 are symbols (ex: ETH), and $3 and $6 are fiat currency amounts."}, "swapPriceDifferenceTitle": {"message": "Différence de prix de ~$1", "description": "$1 is a number (ex: 1.23) that represents the price difference."}, "swapPriceUnavailableDescription": {"message": "L’incidence sur les prix n’a pas pu être déterminée faute de données suffisantes sur les prix du marché. <PERSON><PERSON><PERSON><PERSON> confirmer que vous êtes satisfait·e du nombre de jetons que vous êtes sur le point de recevoir avant de procéder au swap."}, "swapPriceUnavailableTitle": {"message": "Vérifiez votre taux avant de poursuivre"}, "swapProcessing": {"message": "Traitement en cours"}, "swapQuoteDetails": {"message": "Détails de la cotation"}, "swapQuoteNofM": {"message": "$1 sur $2", "description": "A count of possible quotes shown to the user while they are waiting for quotes to be fetched. $1 is the number of quotes already loaded, and $2 is the total number of resources that we check for quotes. Keep in mind that not all resources will have a quote for a particular swap."}, "swapQuoteSource": {"message": "Origine de la cotation"}, "swapQuotesExpiredErrorDescription": {"message": "Veuillez demander de nouvelles cotations pour obtenir les derniers taux."}, "swapQuotesExpiredErrorTitle": {"message": "Les cotations ont expiré"}, "swapQuotesNotAvailableDescription": {"message": "Ce moyen d’échange n’est pas disponible pour le moment. Essayez de modifier le montant, le réseau ou le jeton et nous vous trouverons la meilleure option."}, "swapQuotesNotAvailableErrorDescription": {"message": "Essayez d’ajuster le montant ou les paramètres de glissement, puis réessayez."}, "swapQuotesNotAvailableErrorTitle": {"message": "Aucune cotation disponible"}, "swapRate": {"message": "<PERSON><PERSON>"}, "swapReceiving": {"message": "Ré<PERSON>"}, "swapReceivingInfoTooltip": {"message": "Il s’agit d’une estimation. Le montant exact dépend du glissement."}, "swapRequestForQuotation": {"message": "<PERSON><PERSON><PERSON> de co<PERSON>"}, "swapSelect": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "swapSelectAQuote": {"message": "Sélectionnez une cotation"}, "swapSelectAToken": {"message": "Sélectionnez un jeton"}, "swapSelectQuotePopoverDescription": {"message": "Vous trouverez ci-dessous toutes les cotations obtenues auprès de multiples sources de liquidité."}, "swapSelectToken": {"message": "Sé<PERSON><PERSON><PERSON> le jeton"}, "swapShowLatestQuotes": {"message": "Afficher les dernières cotations"}, "swapSlippageHighDescription": {"message": "L’effet de glissement saisi ($1 %) est considéré comme très élevé et peut donner lieu à un taux de change désavantageux", "description": "$1 is the amount of % for slippage"}, "swapSlippageHighTitle": {"message": "Important effet de glissement"}, "swapSlippageLowDescription": {"message": "Une valeur aussi faible ($1 %) peut entraîner l’échec de l’accord de swap", "description": "$1 is the amount of % for slippage"}, "swapSlippageLowTitle": {"message": "Faible effet de glissement"}, "swapSlippageNegativeDescription": {"message": "Le slippage doit être supérieur ou égal à zéro"}, "swapSlippageNegativeTitle": {"message": "Augmentez le slippage pour continuer"}, "swapSlippageOverLimitDescription": {"message": "La tolérance au slippage doit être inférieure ou égale à 15 %. Une tolérance plus élevée peut se traduire par un taux de change désavantageux."}, "swapSlippageOverLimitTitle": {"message": "Slippage très <PERSON>"}, "swapSlippagePercent": {"message": "$1 %", "description": "$1 is the amount of % for slippage"}, "swapSlippageTooltip": {"message": "Si le prix fluctue entre le moment où vous placez un ordre et le moment où il est exécuté, on parle alors d’un « effet de glissement » ou « slippage ». Votre swap sera automatiquement annulé si ce phénomène dépasse le « seuil de glissement toléré » que vous avez fixé."}, "swapSlippageZeroDescription": {"message": "Il y a moins de prestataires de services d’investissement sans slippage, ce qui se traduit par une cotation moins compétitive."}, "swapSlippageZeroTitle": {"message": "Recherche de prestataires de services d’investissement sans slippage"}, "swapSource": {"message": "Source de liquidité"}, "swapSuggested": {"message": "<PERSON><PERSON><PERSON> proposé"}, "swapSuggestedGasSettingToolTipMessage": {"message": "Les swaps sont des transactions complexes et soumises à une contrainte de temps. Nous recommandons ce prix de carburant pour assurer un bon équilibre entre le coût et la garantie d’un swap réussi."}, "swapSwapFrom": {"message": "<PERSON>wa<PERSON> de"}, "swapSwapSwitch": {"message": "Inverser l’échange de jetons"}, "swapSwapTo": {"message": "Swap vers"}, "swapToConfirmWithHwWallet": {"message": "pour confirmer avec votre portefeuille matériel"}, "swapTokenAddedManuallyDescription": {"message": "Vérifiez ce jeton sur $1 et assurez-vous qu’il s’agit bien du jeton que vous souhaitez échanger.", "description": "$1 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenAddedManuallyTitle": {"message": "<PERSON><PERSON> a<PERSON> manuel<PERSON>"}, "swapTokenAvailable": {"message": "Votre $1 a été ajouté à votre compte.", "description": "This message is shown after a swap is successful and communicates the exact amount of tokens the user has received for a swap. The $1 is a decimal number of tokens followed by the token symbol."}, "swapTokenBalanceUnavailable": {"message": "Nous n’avons pas pu récupérer votre solde de $1", "description": "This message communicates to the user that their balance of a given token is currently unavailable. $1 will be replaced by a token symbol"}, "swapTokenNotAvailable": {"message": "Impossible d’échanger ce jeton dans cette région"}, "swapTokenToToken": {"message": "Swap de $1 vers $2", "description": "Used in the transaction display list to describe a swap. $1 and $2 are the symbols of tokens in involved in a swap."}, "swapTokenVerifiedOn1SourceDescription": {"message": "$1 n’a été vérifié que par 1 source. Envisagez de le vérifier auprès de $2 sources avant de continuer.", "description": "$1 is a token name, $2 points the user to etherscan as a place they can verify information about a token. $1 is replaced with the translation for \"etherscan\""}, "swapTokenVerifiedOn1SourceTitle": {"message": "Jeton potentiellement inauthentique"}, "swapTokenVerifiedSources": {"message": "Confirmé par $1 sources. Vérifier sur $2.", "description": "$1 the number of sources that have verified the token, $2 points the user to a block explorer as a place they can verify information about the token."}, "swapTooManyDecimalsError": {"message": "$1 accepte jusqu’à $2 décimales", "description": "$1 is a token symbol and $2 is the max. number of decimals allowed for the token"}, "swapTransactionComplete": {"message": "Transaction terminée"}, "swapTwoTransactions": {"message": "2 transactions"}, "swapUnknown": {"message": "Inconnu"}, "swapZeroSlippage": {"message": "0 % de glissement"}, "swapsMaxSlippage": {"message": "Tolérance au slippage"}, "swapsNotEnoughToken": {"message": "Pas assez de $1", "description": "Tells the user that they don't have enough of a token for a proposed swap. $1 is a token symbol"}, "swapsViewInActivity": {"message": "Afficher dans l’activité"}, "switch": {"message": "Changer"}, "switchEthereumChainConfirmationDescription": {"message": "Ceci permet de remplacer le réseau sélectionné dans MetaMask par un réseau précédemment ajouté :"}, "switchEthereumChainConfirmationTitle": {"message": "Autoriser ce site à changer de réseau ?"}, "switchInputCurrency": {"message": "Changer la devise"}, "switchNetwork": {"message": "<PERSON><PERSON> <PERSON>"}, "switchNetworks": {"message": "<PERSON><PERSON> <PERSON>"}, "switchToNetwork": {"message": "Passer à $1", "description": "$1 represents the custom network that has previously been added"}, "switchToThisAccount": {"message": "Basculer vers ce compte"}, "switchedNetworkToastDecline": {"message": "Ne plus afficher"}, "switchedNetworkToastMessage": {"message": "$1 est maintenant actif sur $2", "description": "$1 represents the account name, $2 represents the network name"}, "switchedNetworkToastMessageNoOrigin": {"message": "Vous êtes en train d’utiliser $1", "description": "$1 represents the network name"}, "switchingNetworksCancelsPendingConfirmations": {"message": "Le changement de réseau annulera toutes les confirmations en attente"}, "symbol": {"message": "Symbole"}, "symbolBetweenZeroTwelve": {"message": "Le symbole doit comporter 11 caractères ou moins."}, "tenPercentIncreased": {"message": "Augmentation de 10 %"}, "terms": {"message": "Conditions d’utilisation"}, "termsOfService": {"message": "Conditions de service"}, "termsOfUseAgreeText": {"message": " J’accepte les conditions d’utilisation de MetaMask et de ses fonctionnalités"}, "termsOfUseFooterText": {"message": "Faites défiler pour lire toutes les sections"}, "termsOfUseTitle": {"message": "Nos conditions d’utilisation ont été mises à jour"}, "testNetworks": {"message": "Réseaux de test"}, "testnets": {"message": "Testnets"}, "theme": {"message": "Thème"}, "themeDescription": {"message": "Choisissez votre thème MetaMask préféré."}, "thirdPartySoftware": {"message": "Avis sur les logiciels développés par des tiers", "description": "Title of a popup modal displayed when installing a snap for the first time."}, "time": {"message": "Temps"}, "tipsForUsingAWallet": {"message": "Conseils pour l’utilisation d’un portefeuille"}, "tipsForUsingAWalletDescription": {"message": "L’ajout de jetons permet de débloquer d’autres moyens d’utiliser le Web3."}, "to": {"message": "<PERSON><PERSON><PERSON>"}, "toAddress": {"message": "Vers : $1", "description": "$1 is the address to include in the To label. It is typically shortened first using shortenAddress"}, "toggleDecodeDescription": {"message": "Nous utilisons les services de 4byte.directory et de Sourcify pour décoder et afficher des données de transaction plus compréhensibles. Cela vous aide à comprendre le résultat des transactions en cours et passées, mais peut entraîner le partage de votre adresse IP."}, "token": {"message": "<PERSON><PERSON>"}, "tokenAddress": {"message": "<PERSON><PERSON><PERSON>"}, "tokenAlreadyAdded": {"message": "Ce jeton a déjà été ajouté."}, "tokenAutoDetection": {"message": "Détection automatique des jetons"}, "tokenContractAddress": {"message": "Adresse du contrat de jeton"}, "tokenDecimal": {"message": "Nombre de décimales du jeton"}, "tokenDecimalFetchFailed": {"message": "La décimale du jeton est requise. Trouvez-la sur : $1"}, "tokenDetails": {"message": "<PERSON><PERSON><PERSON> du token"}, "tokenFoundTitle": {"message": "1 nouveau jeton trouvé"}, "tokenId": {"message": "ID de token"}, "tokenList": {"message": "Listes de jetons"}, "tokenMarketplace": {"message": "<PERSON><PERSON>"}, "tokenScamSecurityRisk": {"message": "les arnaques et les risques de piratage informatique"}, "tokenStandard": {"message": "Jeton standard"}, "tokenSymbol": {"message": "Symbole du jeton"}, "tokens": {"message": "Jet<PERSON>"}, "tokensFoundTitle": {"message": "$1 nouveaux jetons trouvés", "description": "$1 is the number of new tokens detected"}, "tokensInCollection": {"message": "Jetons dans la collection"}, "tooltipApproveButton": {"message": "Je comprends"}, "tooltipSatusConnected": {"message": "connecté"}, "tooltipSatusConnectedUpperCase": {"message": "Connecté"}, "tooltipSatusNotConnected": {"message": "non connecté"}, "total": {"message": "Total"}, "totalVolume": {"message": "Volume total"}, "transaction": {"message": "transaction"}, "transactionCancelAttempted": {"message": "Tentative d’annulation de la transaction avec un prix de carburant de $1 à $2"}, "transactionCancelSuccess": {"message": "Transaction annulée avec succès à $2"}, "transactionConfirmed": {"message": "Transaction confirmée sur $2."}, "transactionCreated": {"message": "Transaction créée avec une valeur de $1 sur $2."}, "transactionDataFunction": {"message": "Fonction"}, "transactionDetailGasHeading": {"message": "Frais de carburant estimés"}, "transactionDetailMultiLayerTotalSubtitle": {"message": "Montant + frais"}, "transactionDropped": {"message": "Transaction abandonnée sur $2."}, "transactionError": {"message": "Erreur de transaction. Une exception a été rencontrée dans l’exécution du code du contrat."}, "transactionErrorNoContract": {"message": "Tentative d’appel de fonction sur une adresse qui n’apparaît pas dans le contrat."}, "transactionErrored": {"message": "La transaction a rencontré une erreur."}, "transactionFlowNetwork": {"message": "<PERSON><PERSON><PERSON>"}, "transactionHistoryBaseFee": {"message": "Frais de base (GWEI)"}, "transactionHistoryL1GasLabel": {"message": "Total des frais de transaction L1"}, "transactionHistoryL2GasLimitLabel": {"message": "Montant maximal des frais de transaction L2"}, "transactionHistoryL2GasPriceLabel": {"message": "Prix du gaz L2"}, "transactionHistoryMaxFeePerGas": {"message": "Frais maximaux par unité de gaz"}, "transactionHistoryPriorityFee": {"message": "Frais de priorité (GWEI)"}, "transactionHistoryTotalGasFee": {"message": "Total des frais de transaction"}, "transactionIdLabel": {"message": "ID de transaction", "description": "Label for the source transaction ID field."}, "transactionIncludesTypes": {"message": "Cette transaction comprend : $1."}, "transactionResubmitted": {"message": "La transaction a été soumise à nouveau avec une augmentation du prix du gaz, désormais de $1 à $2"}, "transactionSettings": {"message": "Paramètres de la transaction"}, "transactionSubmitted": {"message": "Transaction envoyée sur $2."}, "transactionTotalGasFee": {"message": "Montant total des frais de gaz", "description": "Label for the total gas fee incurred in the transaction."}, "transactionUpdated": {"message": "Transaction mise à jour sur $2."}, "transactions": {"message": "Transactions"}, "transfer": {"message": "Transfert"}, "transferCrypto": {"message": "Transférer des crypto-monnaies"}, "transferFrom": {"message": "Transfert depuis"}, "transferRequest": {"message": "<PERSON><PERSON><PERSON>"}, "trillionAbbreviation": {"message": "B", "description": "Shortened form of 'trillion'"}, "troubleConnectingToLedgerU2FOnFirefox": {"message": "Nous avons des difficultés à connecter votre Ledger. $1", "description": "$1 is a link to the wallet connection guide;"}, "troubleConnectingToLedgerU2FOnFirefox2": {"message": "Consultez notre guide de connexion au portefeuille matériel et réessayez.", "description": "$1 of the ledger wallet connection guide"}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution": {"message": "Si vous utilisez la dernière version de Firefox, il se peut que vous rencontriez un problème, car Firefox ne prend plus en charge la norme d’authentification U2F. Découvrez $1 comment vous pouvez résoudre ce problème.", "description": "It is a link to the ledger website for the workaround."}, "troubleConnectingToLedgerU2FOnFirefoxLedgerSolution2": {"message": "ici", "description": "Second part of the error message; It is a link to the ledger website for the workaround."}, "troubleConnectingToWallet": {"message": "Nous avons eu des difficultés à nous connecter à votre $1. Essayez de vérifier votre $2 et réessayez.", "description": "$1 is the wallet device name; $2 is a link to wallet connection guide"}, "troubleStarting": {"message": "Impossible de démarrer MetaMask. Cette erreur peut être occasionnelle, essayez donc de redémarrer lextension."}, "tryAgain": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "turnOff": {"message": "Désactiver"}, "turnOffMetamaskNotificationsError": {"message": "Une erreur s’est produite lors de la désactivation des notifications. Veuillez réessayer plus tard."}, "turnOn": {"message": "Activer"}, "turnOnMetamaskNotifications": {"message": "Activer les notifications"}, "turnOnMetamaskNotificationsButton": {"message": "Activer"}, "turnOnMetamaskNotificationsError": {"message": "Une erreur s’est produite lors de la création des notifications. Veuillez réessayer plus tard."}, "turnOnMetamaskNotificationsMessageFirst": {"message": "Restez au courant de ce qui se passe dans votre portefeuille grâce aux notifications."}, "turnOnMetamaskNotificationsMessagePrivacyBold": {"message": "paramètres de notification."}, "turnOnMetamaskNotificationsMessagePrivacyLink": {"message": "Découvrez comment nous protégeons vos données personnelles lorsque vous utilisez cette fonctionnalité."}, "turnOnMetamaskNotificationsMessageSecond": {"message": "Pour activer les notifications du portefeuille, nous utilisons un profil pour synchroniser certains paramètres entre vos appareils. $1"}, "turnOnMetamaskNotificationsMessageThird": {"message": "Vous pouvez désactiver les notifications à tout moment dans les $1"}, "turnOnTokenDetection": {"message": "Activer la détection améliorée des jetons"}, "tutorial": {"message": "<PERSON><PERSON><PERSON>"}, "twelveHrTitle": {"message": "12 h :"}, "u2f": {"message": "U2F", "description": "A name on an API for the browser to interact with devices that support the U2F protocol. On some browsers we use it to connect MetaMask to Ledger devices."}, "unapproved": {"message": "Non autorisé"}, "unexpectedBehavior": {"message": "Ce comportement est inattendu et doit être signalé comme un bogue, même si vos comptes sont restaurés correctement. Utilisez le lien ci-dessous pour envoyer un signalement de bogue à MetaMask."}, "units": {"message": "unités"}, "unknown": {"message": "Inconnu"}, "unknownCollection": {"message": "Collection sans nom"}, "unknownNetworkForKeyEntropy": {"message": "<PERSON><PERSON><PERSON> inconnu", "description": "Displayed on places like Snap install warning when regular name is not available."}, "unknownQrCode": {"message": "Erreur : nous n’avons pas pu identifier le code QR"}, "unlimited": {"message": "Illimité"}, "unlock": {"message": "Déverrouiller"}, "unpin": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "unrecognizedChain": {"message": "Ce réseau personnalisé n’est pas reconnu", "description": "$1 is a clickable link with text defined by the 'unrecognizedChanLinkText' key. The link will open to instructions for users to validate custom network details."}, "unsendableAsset": {"message": "L’envoi de jetons NFT (ERC-721) n’est pas pris en charge actuellement", "description": "This is an error message we show the user if they attempt to send an NFT asset type, for which currently don't support sending"}, "unstableTokenPriceDescription": {"message": "Le prix de ce jeton en USD est extrêmement volatil, ce qui signifie qu’il peut perdre rapidement de sa valeur."}, "unstableTokenPriceTitle": {"message": "Prix du jeton instable"}, "upArrow": {"message": "flèche vers le haut"}, "update": {"message": "Mise à jour"}, "updateEthereumChainConfirmationDescription": {"message": "Ce site demande à mettre à jour l’URL par défaut de votre réseau. Vous pouvez modifier les paramètres par défaut et les informations du réseau à tout moment."}, "updateNetworkConfirmationTitle": {"message": "Mettre à jour $1", "description": "$1 represents network name"}, "updateOrEditNetworkInformations": {"message": "Mettez à jour vos informations ou"}, "updateRequest": {"message": "<PERSON><PERSON><PERSON> de mise à jour"}, "updatedRpcForNetworks": {"message": "Les RPC du réseau ont été mis à jour"}, "uploadDropFile": {"message": "<PERSON><PERSON><PERSON>z votre fichier ici"}, "uploadFile": {"message": "Télécharger le fichier"}, "urlErrorMsg": {"message": "Les URLs requièrent un préfixe HTTP/HTTPS approprié."}, "use4ByteResolution": {"message": "Décoder les contrats intelligents"}, "useMultiAccountBalanceChecker": {"message": "Demandes d’informations concernant le solde de plusieurs comptes"}, "useMultiAccountBalanceCheckerSettingDescription": {"message": "Bénéficiez d’une mise à jour plus rapide des soldes en regroupant les demandes d’informations concernant le solde des comptes. Cela nous permet de récupérer plus rapidement les informations dont nous avons besoin pour mettre à jour le solde de vos comptes. En désactivant cette fonctionnalité, vous limitez la capacité des tiers à établir un lien entre vos différents comptes."}, "useNftDetection": {"message": "Détection automatique des NFT"}, "useNftDetectionDescriptionText": {"message": "Laissez MetaMask ajouter les NFT que vous possédez en utilisant des services tiers. La détection automatique des NFT révèle votre adresse IP et l’adresse de votre compte à ces services. Si vous activez cette fonctionnalité, un lien pourrait être établi entre votre adresse IP et votre adresse Ethereum, et entrainer l’affichage de faux NFT parachutés par des arnaqueurs. Vous pouvez ajouter des jetons manuellement pour éviter ce risque."}, "usePhishingDetection": {"message": "Utiliser la fonction anti-hameçonnage"}, "usePhishingDetectionDescription": {"message": "<PERSON><PERSON> permet d’afficher un avertissement pour les domaines d’hameçonnage ciblant les utilisateurs d’Ethereum"}, "useSafeChainsListValidation": {"message": "Vérification des détails du réseau"}, "useSafeChainsListValidationDescription": {"message": "MetaMask utilise un service tiers appelé $1 pour afficher des détails précis et standardisés concernant les réseaux. Vous limitez ainsi les risques de vous connecter à un réseau malveillant ou au mauvais réseau. En utilisant cette fonctionnalité, vous exposez votre adresse IP à chainid.network."}, "useSafeChainsListValidationWebsite": {"message": "chainid.network", "description": "useSafeChainsListValidationWebsite is separated from the rest of the text so that we can bold the third party service name in the middle of them"}, "useTokenDetectionPrivacyDesc": {"message": "L’affichage automatique des tokens envoyés sur votre compte implique une communication avec des serveurs externes afin de récupérer les images des tokens. Ces serveurs auront accès à votre adresse IP."}, "usedByClients": {"message": "Utilisé par plusieurs clients différents"}, "userName": {"message": "Nom d’utilisateur"}, "userOpContractDeployError": {"message": "Le déploiement de contrats à partir d’un compte de contrat intelligent n’est pas pris en charge"}, "version": {"message": "Version"}, "view": {"message": "<PERSON><PERSON><PERSON>z"}, "viewActivity": {"message": "Voir l’activité"}, "viewAllQuotes": {"message": "afficher toutes les cotations"}, "viewContact": {"message": "Voir le contact"}, "viewDetails": {"message": "Aff<PERSON>r les détails"}, "viewMore": {"message": "Afficher plus"}, "viewOnBlockExplorer": {"message": "Afficher sur l’explorateur de blocs"}, "viewOnCustomBlockExplorer": {"message": "Afficher $1 à $2", "description": "$1 is the action type. e.g (Account, Transaction, Swap) and $2 is the Custom Block Explorer URL"}, "viewOnEtherscan": {"message": "Afficher $1 sur Etherscan", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "viewOnExplorer": {"message": "Afficher sur l’explorateur"}, "viewOnOpensea": {"message": "Afficher sur Opensea"}, "viewSolanaAccount": {"message": "Voir le compte Solana"}, "viewTransaction": {"message": "Voir la transaction"}, "viewinExplorer": {"message": "Voir $1 dans l’explorateur", "description": "$1 is the action type. e.g (Account, Transaction, Swap)"}, "visitSite": {"message": "Visiter le site"}, "visitSupportDataConsentModalAccept": {"message": "Confirmer"}, "visitSupportDataConsentModalDescription": {"message": "Voulez-vous partager votre identifiant MetaMask et la version de l’application que vous utilisez actuellement avec notre centre d’assistance ? Cela peut nous aider à résoudre le problème, mais vous n’êtes pas obligé à le faire."}, "visitSupportDataConsentModalReject": {"message": "Ne pas partager"}, "visitSupportDataConsentModalTitle": {"message": "Partager les détails de l’appareil avec le centre d’assistance"}, "visitWebSite": {"message": "Visitez notre site web"}, "wallet": {"message": "Portefeuille"}, "walletConnectionGuide": {"message": "notre guide de connexion des portefeuilles matériels"}, "wantToAddThisNetwork": {"message": "Voulez-vous ajouter ce réseau ?"}, "wantsToAddThisAsset": {"message": "$1 veut ajouter cet actif à votre portefeuille."}, "warning": {"message": "Avertissement"}, "warningFromSnap": {"message": "Avertissement provenant de $1", "description": "$1 represents the name of the snap"}, "watchEthereumAccountsDescription": {"message": "En activant cette option, vous pourrez surveiller des comptes Ethereum via une adresse publique ou un nom ENS. Pour nous faire part de vos commentaires sur cette fonctionnalité bêta, veuillez remplir ce $1.", "description": "$1 is the link to a product feedback form"}, "watchEthereumAccountsToggle": {"message": "Surveiller des comptes Ethereum (Bêta)"}, "watchOutMessage": {"message": "Méfiez-vous de $1.", "description": "$1 is a link with text that is provided by the 'securityMessageLinkForNetworks' key"}, "weak": {"message": "Faible"}, "web3": {"message": "Web3"}, "web3ShimUsageNotification": {"message": "Nous avons remarqué que ce site Web a essayé d’utiliser l’API window.web3 supprimée. Si le site semble être défectueux, veuillez cliquer sur $1 pour plus d’informations.", "description": "$1 is a clickable link."}, "webhid": {"message": "WebHID", "description": "Refers to a interface for connecting external devices to the browser. Used for connecting ledger to the browser. Read more here https://developer.mozilla.org/en-US/docs/Web/API/WebHID_API"}, "websites": {"message": "sites Web", "description": "Used in the 'permission_rpc' message."}, "welcomeBack": {"message": "Nous sommes heureux de vous revoir"}, "welcomeToMetaMask": {"message": "C’est parti !"}, "whatsThis": {"message": "Qu’est-ce que c’est ?"}, "willApproveAmountForBridging": {"message": "<PERSON><PERSON> per<PERSON> d’approuver $1 pour l’établissement d’une passerelle."}, "willApproveAmountForBridgingHardware": {"message": "V<PERSON> de<PERSON>z confirmer deux transactions sur votre portefeuille matériel."}, "withdrawing": {"message": "Retrait en cours"}, "wrongNetworkName": {"message": "Selon nos informations, il se peut que le nom du réseau ne corresponde pas exactement à l’ID de chaîne."}, "yes": {"message": "O<PERSON>"}, "you": {"message": "Vous"}, "youDeclinedTheTransaction": {"message": "<PERSON><PERSON> avez refusé la transaction."}, "youNeedToAllowCameraAccess": {"message": "V<PERSON> devez autoriser l’accès à votre appareil pour utiliser cette fonctionnalité."}, "youReceived": {"message": "<PERSON><PERSON> avez reçu", "description": "Label indicating the amount and asset the user received."}, "youSent": {"message": "<PERSON><PERSON> a<PERSON> envoyé", "description": "Label indicating the amount and asset the user sent."}, "yourAccounts": {"message": "Vos comptes"}, "yourActivity": {"message": "Votre activité"}, "yourBalance": {"message": "Votre solde"}, "yourNFTmayBeAtRisk": {"message": "Il peut y avoir des risques associés à votre NFT"}, "yourNetworks": {"message": "<PERSON><PERSON> r<PERSON>"}, "yourPrivateSeedPhrase": {"message": "Votre phrase secrète de récupération privée"}, "yourTransactionConfirmed": {"message": "Transaction déjà confirmée"}, "yourTransactionJustConfirmed": {"message": "Nous n'avons pas pu annuler votre transaction avant qu'elle ne soit confirmée sur la blockchain."}, "yourWalletIsReady": {"message": "Votre portefeuille est prêt"}}