"""
NowPayments URL configuration.
"""

from django.urls import path
from . import api_views

app_name = 'nowpayments'

urlpatterns = [
    # IPN webhook endpoint
    path('ipn/', api_views.NowPaymentsIPNView.as_view(), name='ipn'),

    # API endpoints
    path('currencies/', api_views.NowPaymentsCurrenciesView.as_view(), name='currencies'),
    path('currencies/list/', api_views.NowPaymentsCurrenciesAPIView.as_view(), name='currencies_list'),
    path('estimate/', api_views.NowPaymentsEstimateView.as_view(), name='estimate'),
    path('create/', api_views.NowPaymentsCreateView.as_view(), name='create'),
    path('status/<str:payment_id>/', api_views.NowPaymentsStatusView.as_view(), name='status'),
    path('payment-options/', api_views.NowPaymentsPaymentOptionsView.as_view(), name='payment_options'),
    path('regenerate-qr/', api_views.RegenerateQRCodeView.as_view(), name='regenerate_qr'),
]
