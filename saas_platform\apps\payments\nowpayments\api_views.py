"""
NowPayments API views for handling webhooks and payment processing.
"""

import json
import logging
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.utils.decorators import method_decorator
from django.utils import timezone
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from .processors import NowPaymentsProcessor
from .client import NowPaymentsClient
from ..models import PaymentMethod, PaymentWebhook, NowPaymentsPayment

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class NowPaymentsIPNView(View):
    """Handle NowPayments IPN (Instant Payment Notifications)."""
    
    def post(self, request):
        """Process IPN from NowPayments."""
        try:
            # Get IPN signature from headers
            signature = request.META.get('HTTP_X_NOWPAYMENTS_SIG', '')
            if not signature:
                logger.error("Missing IPN signature")
                return HttpResponse("Missing signature", status=400)
            
            # Parse IPN data
            try:
                ipn_data = json.loads(request.body.decode('utf-8'))
            except json.JSONDecodeError:
                logger.error("Invalid JSON in IPN payload")
                return HttpResponse("Invalid JSON", status=400)
            
            # Get NowPayments payment method
            try:
                payment_method = PaymentMethod.objects.get(
                    code='nowpayments',
                    is_active=True
                )
            except PaymentMethod.DoesNotExist:
                logger.error("NowPayments payment method not found or inactive")
                return HttpResponse("Payment method not found", status=404)
            
            # Create webhook record
            webhook = PaymentWebhook.objects.create(
                payment_method=payment_method,
                event_type='payment_status_update',
                event_id=ipn_data.get('payment_id', ''),
                raw_data_encrypted=''  # Will be set by set_raw_data
            )
            webhook.set_data(ipn_data)
            
            # Process IPN
            processor = NowPaymentsProcessor(payment_method)
            success = processor.process_ipn(ipn_data, signature)
            
            if success:
                webhook.mark_as_processed()
                logger.info(f"Successfully processed IPN for payment {ipn_data.get('payment_id')}")
                return HttpResponse("OK", status=200)
            else:
                webhook.error_message = "Failed to process IPN"
                webhook.save()
                logger.error(f"Failed to process IPN for payment {ipn_data.get('payment_id')}")
                return HttpResponse("Processing failed", status=400)
                
        except Exception as e:
            logger.error(f"Error handling NowPayments IPN: {e}")
            return HttpResponse("Internal error", status=500)


class NowPaymentsCurrenciesView(APIView):
    """Get available cryptocurrencies for NowPayments."""

    permission_classes = [IsAuthenticated]

    def _get_currency_display_name(self, currency_code):
        """获取币种显示名称"""
        # 常见币种的友好名称映射
        currency_names = {
            'btc': 'Bitcoin (BTC)',
            'eth': 'Ethereum (ETH)',
            'usdttrc20': 'Tether (USDT TRC20)',
            'usdcmatic': 'USD Coin (USDC Polygon)',
            'ltc': 'Litecoin (LTC)',
            'fdusdbsc': 'First Digital USD (FDUSD BSC)',
            'usdterc20': 'Tether (USDT ERC20)',
            'sol': 'Solana (SOL)',
            'bnbbsc': 'Binance Coin (BNB BSC)',
            'doge': 'Dogecoin (DOGE)',
            'usdt': 'Tether (USDT)',
            'usdc': 'USD Coin (USDC)',
            'bnb': 'Binance Coin (BNB)',
            'ada': 'Cardano (ADA)',
            'dot': 'Polkadot (DOT)',
            'matic': 'Polygon (MATIC)',
            'avax': 'Avalanche (AVAX)',
            'link': 'Chainlink (LINK)',
            'uni': 'Uniswap (UNI)',
            'xlm': 'Stellar (XLM)',
            'xrp': 'Ripple (XRP)',
            'trx': 'TRON (TRX)',
            'bch': 'Bitcoin Cash (BCH)',
            'etc': 'Ethereum Classic (ETC)',
            'xmr': 'Monero (XMR)',
            'zec': 'Zcash (ZEC)',
            'dash': 'Dash (DASH)',
            'atom': 'Cosmos (ATOM)',
            'algo': 'Algorand (ALGO)',
            'vet': 'VeChain (VET)',
            'icp': 'Internet Computer (ICP)',
            'fil': 'Filecoin (FIL)',
            'theta': 'Theta Network (THETA)',
            'xtz': 'Tezos (XTZ)',
            'eos': 'EOS (EOS)',
            'aave': 'Aave (AAVE)',
            'mkr': 'Maker (MKR)',
            'comp': 'Compound (COMP)',
            'snx': 'Synthetix (SNX)',
            'crv': 'Curve DAO Token (CRV)',
            'yfi': 'yearn.finance (YFI)',
            'sushi': 'SushiSwap (SUSHI)',
            '1inch': '1inch Network (1INCH)',
            'grt': 'The Graph (GRT)',
            'bat': 'Basic Attention Token (BAT)',
            'zrx': '0x (ZRX)',
            'omg': 'OMG Network (OMG)',
            'lrc': 'Loopring (LRC)',
            'ren': 'Ren (REN)',
            'knc': 'Kyber Network Crystal (KNC)',
            'mana': 'Decentraland (MANA)',
            'sand': 'The Sandbox (SAND)',
            'enj': 'Enjin Coin (ENJ)',
            'chz': 'Chiliz (CHZ)',
            'hot': 'Holo (HOT)',
            'rvn': 'Ravencoin (RVN)',
            'dgb': 'DigiByte (DGB)',
            'sc': 'Siacoin (SC)',
            'zen': 'Horizen (ZEN)',
            'qtum': 'Qtum (QTUM)',
            'icx': 'ICON (ICX)',
            'zil': 'Zilliqa (ZIL)',
            'ont': 'Ontology (ONT)',
            'nano': 'Nano (NANO)',
            'waves': 'Waves (WAVES)',
            'kmd': 'Komodo (KMD)',
            'dcr': 'Decred (DCR)',
            'lsk': 'Lisk (LSK)',
            'ark': 'Ark (ARK)',
            'strat': 'Stratis (STRAT)',
            'rep': 'Augur (REP)',
            'storj': 'Storj (STORJ)',
            'gno': 'Gnosis (GNO)',
            'ant': 'Aragon (ANT)',
            'fun': 'FunFair (FUN)',
            'salt': 'SALT (SALT)',
            'rdn': 'Raiden Network Token (RDN)',
            'pay': 'TenX (PAY)',
            'cvc': 'Civic (CVC)',
            'mco': 'MCO (MCO)',
            'mtl': 'Metal (MTL)',
            'fuel': 'Etherparty (FUEL)',
            'req': 'Request Network (REQ)',
            'sub': 'Substratum (SUB)',
            'powr': 'Power Ledger (POWR)',
            'eng': 'Enigma (ENG)',
            'bnt': 'Bancor (BNT)',
            'kin': 'Kin (KIN)',
            'nuls': 'Nuls (NULS)',
            'pivx': 'PIVX (PIVX)',
            'via': 'Viacoin (VIA)',
            'blk': 'BlackCoin (BLK)',
            'bay': 'BitBay (BAY)',
            'fair': 'FairCoin (FAIR)',
            'mona': 'MonaCoin (MONA)',
            'bsd': 'BitSend (BSD)',
            'nxt': 'Nxt (NXT)',
            'burst': 'Burst (BURST)',
            'sys': 'Syscoin (SYS)',
            'emc': 'Emercoin (EMC)',
            'game': 'GameCredits (GAME)',
            'exp': 'Expanse (EXP)',
            'omni': 'Omni (OMNI)',
            'cloak': 'CloakCoin (CLOAK)',
            'pot': 'PotCoin (POT)',
            'start': 'StartCoin (START)',
            'kore': 'Kore (KORE)',
            'trust': 'TrustPlus (TRUST)',
            'nav': 'NavCoin (NAV)',
            'xst': 'Stealth (XST)',
            'part': 'Particl (PART)',
            'rby': 'Rubycoin (RBY)',
            'bry': 'Berry (BRY)',
            'pot': 'PotCoin (POT)',
            'dmd': 'Diamond (DMD)',
            'grc': 'GridCoin (GRC)',
            'fldc': 'FoldingCoin (FLDC)',
            'nsr': 'NuShares (NSR)',
            'nbt': 'NuBits (NBT)',
            'byc': 'Bytecent (BYC)',
            'dgc': 'Digitalcoin (DGC)',
            'blitz': 'Blitzcash (BLITZ)',
            'bay': 'BitBay (BAY)',
        }

        # 如果有映射则使用，否则使用大写的币种代码
        return currency_names.get(currency_code.lower(), currency_code.upper())

    def _is_stablecoin_static(self, currency):
        """静态稳定币检测（用于API响应）"""
        currency_upper = currency.upper()

        # 稳定币识别模式
        stablecoin_patterns = [
            'USDT',    # Tether USD
            'USDC',    # USD Coin
            'FDUSD',   # First Digital USD
            'DAI',     # MakerDAO DAI
            'BUSD',    # Binance USD
            'TUSD',    # TrueUSD
            'USDD',    # USDD
            'USDJ',    # USDJ
            'USDR',    # USDR
            'USDP',    # Pax Dollar
        ]

        # 检查是否匹配任何稳定币模式
        for pattern in stablecoin_patterns:
            if pattern in currency_upper:
                return True

        return False

    def _get_currency_icon(self, currency_code):
        """获取币种图标CSS类"""
        currency_lower = currency_code.lower()

        # 根据币种类型返回合适的图标
        if any(stable in currency_lower for stable in ['usdt', 'usdc', 'fdusd', 'dai', 'busd', 'tusd']):
            return 'fas fa-dollar-sign text-success'  # 稳定币用绿色美元符号
        elif currency_lower == 'btc':
            return 'fab fa-bitcoin text-warning'
        elif currency_lower == 'eth':
            return 'fab fa-ethereum text-primary'
        elif currency_lower == 'ltc':
            return 'fab fa-litecoin text-info'
        elif currency_lower in ['doge']:
            return 'fas fa-dog text-warning'
        elif currency_lower in ['sol']:
            return 'fas fa-sun text-warning'
        elif currency_lower in ['bnb', 'bnbbsc']:
            return 'fas fa-coins text-warning'
        elif currency_lower in ['ada']:
            return 'fas fa-heart text-primary'
        elif currency_lower in ['dot']:
            return 'fas fa-circle text-danger'
        elif currency_lower in ['matic', 'usdcmatic']:
            return 'fas fa-coins text-info'
        elif currency_lower in ['avax']:
            return 'fas fa-mountain text-danger'
        elif currency_lower in ['link']:
            return 'fas fa-link text-primary'
        elif currency_lower in ['uni']:
            return 'fas fa-unicorn text-danger'
        elif currency_lower in ['xlm']:
            return 'fas fa-star text-info'
        elif currency_lower in ['xrp']:
            return 'fas fa-water text-primary'
        elif currency_lower in ['trx']:
            return 'fas fa-bolt text-danger'
        elif currency_lower in ['bch']:
            return 'fab fa-bitcoin text-success'
        elif currency_lower in ['etc']:
            return 'fab fa-ethereum text-success'
        elif currency_lower in ['xmr']:
            return 'fas fa-eye-slash text-dark'
        elif currency_lower in ['zec']:
            return 'fas fa-shield-alt text-warning'
        elif currency_lower in ['dash']:
            return 'fas fa-tachometer-alt text-primary'
        elif currency_lower in ['atom']:
            return 'fas fa-atom text-info'
        elif currency_lower in ['algo']:
            return 'fas fa-calculator text-success'
        elif currency_lower in ['vet']:
            return 'fas fa-check-circle text-primary'
        elif currency_lower in ['icp']:
            return 'fas fa-infinity text-primary'
        elif currency_lower in ['fil']:
            return 'fas fa-folder text-info'
        elif currency_lower in ['theta']:
            return 'fas fa-play-circle text-warning'
        elif currency_lower in ['xtz']:
            return 'fas fa-cube text-primary'
        elif currency_lower in ['eos']:
            return 'fas fa-globe text-dark'
        else:
            return 'fas fa-coins text-secondary'  # 默认图标

    def get(self, request):
        """Get list of supported cryptocurrencies for payment."""
        try:
            # Get NowPayments payment method
            payment_method = PaymentMethod.objects.get(
                code='nowpayments',
                is_active=True
            )

            processor = NowPaymentsProcessor(payment_method)
            all_currencies = processor.get_supported_currencies()

            # 获取配置信息
            config = payment_method.get_config()

            # 使用所有NowPayments支持的币种
            # 构建币种信息，包含显示名称和是否为稳定币
            currency_info = []
            for currency in sorted(all_currencies):  # 按字母顺序排序
                currency_info.append({
                    'code': currency,
                    'name': self._get_currency_display_name(currency),
                    'is_stablecoin': self._is_stablecoin_static(currency),
                    'icon_class': self._get_currency_icon(currency)
                })

            return Response({
                'success': True,
                'data': {
                    'currencies': currency_info,  # 支持的币种信息
                    'price_currencies': ['USD', 'EUR'],  # 支持的计价货币
                    'payout_currency': config.get('payout_currency', 'BTC').upper(),  # 最终收款货币
                    'payout_address': config.get('payout_address', ''),  # 收款地址
                    'auto_convert': True,  # 自动转换功能
                    'description': 'Pay with any cryptocurrency, automatically converted to TRC20 USDT'
                }
            }, status=status.HTTP_200_OK)

        except PaymentMethod.DoesNotExist:
            return Response({
                'success': False,
                'error': 'NowPayments not configured'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error getting NowPayments currencies: {e}")
            return Response({
                'success': False,
                'error': 'Failed to get currencies'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NowPaymentsEstimateView(APIView):
    """Get payment estimate for NowPayments."""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Get payment estimate."""
        try:
            # Support both 'amount' and 'price_amount' for compatibility
            amount = request.data.get('price_amount') or request.data.get('amount')
            currency_from = request.data.get('price_currency') or request.data.get('currency_from', 'USD')
            currency_to = request.data.get('pay_currency') or request.data.get('currency_to', 'BTC')

            if not amount:
                return Response({
                    'success': False,
                    'error': 'Amount (price_amount or amount) is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 在开发环境中使用模拟数据，避免SSL问题
            from django.conf import settings

            if getattr(settings, 'DEBUG', False):
                # 模拟估算数据
                mock_rates = {
                    'BTC': 0.000025,
                    'ETH': 0.0004,
                    'USDT': 1.0,
                    'LTC': 0.012,
                    'BCH': 0.0035,
                    'ADA': 2.5,
                    'DOT': 0.15
                }

                rate = mock_rates.get(currency_to, 0.001)
                estimate = {
                    'estimated_amount': str(float(amount) * rate),
                    'currency_from': currency_from,
                    'currency_to': currency_to,
                    'amount': str(amount),
                    'min_amount': str(rate * 10),
                    'max_amount': str(rate * 10000),
                    'network_fee': '0.0001'
                }
            else:
                # 生产环境使用真实API
                client = NowPaymentsClient()
                estimate = client.get_estimate(
                    amount=amount,
                    currency_from=currency_from,
                    currency_to=currency_to
                )

            return Response({
                'success': True,
                'data': estimate
            }, status=status.HTTP_200_OK)
            
        except PaymentMethod.DoesNotExist:
            return Response({
                'success': False,
                'error': 'NowPayments not configured'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error getting NowPayments estimate: {e}")
            return Response({
                'success': False,
                'error': 'Failed to get estimate'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NowPaymentsPaymentOptionsView(APIView):
    """Get payment options for a specific amount."""

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Get payment options for amount in USD/EUR."""
        try:
            # Support both 'amount' and 'price_amount' for compatibility
            amount = request.data.get('price_amount') or request.data.get('amount')
            currency = request.data.get('price_currency') or request.data.get('currency', 'USD')

            if not amount:
                return Response({
                    'success': False,
                    'error': 'Amount (price_amount or amount) is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            if currency not in ['USD', 'EUR']:
                return Response({
                    'success': False,
                    'error': 'Only USD and EUR are supported'
                }, status=status.HTTP_400_BAD_REQUEST)

            # For testing purposes, return mock data
            # In production, you would use the actual NowPayments API
            popular_currencies = ['BTC', 'ETH', 'USDT', 'LTC', 'BCH', 'ADA', 'DOT']
            payment_options = []

            # Mock exchange rates for testing
            mock_rates = {
                'BTC': 0.000025,
                'ETH': 0.0004,
                'USDT': 1.0,
                'LTC': 0.012,
                'BCH': 0.0035,
                'ADA': 2.5,
                'DOT': 0.15
            }

            for crypto in popular_currencies:
                rate = mock_rates.get(crypto, 0.001)
                payment_options.append({
                    'currency': crypto,
                    'amount': str(float(amount) * rate),
                    'currency_name': self._get_currency_name(crypto),
                    'network_fee': '0.0001',
                    'min_amount': str(rate * 10)
                })

            return Response({
                'success': True,
                'data': payment_options
            }, status=status.HTTP_200_OK)

        except PaymentMethod.DoesNotExist:
            return Response({
                'success': False,
                'error': 'NowPayments not configured'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error getting payment options: {e}")
            return Response({
                'success': False,
                'error': 'Failed to get payment options'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_currency_name(self, currency_code):
        """Get human-readable currency name."""
        currency_names = {
            'BTC': 'Bitcoin',
            'ETH': 'Ethereum',
            'USDT': 'Tether USD',
            'LTC': 'Litecoin',
            'BCH': 'Bitcoin Cash',
            'ADA': 'Cardano',
            'DOT': 'Polkadot',
            'DOGE': 'Dogecoin',
            'XMR': 'Monero',
            'TRX': 'TRON'
        }
        return currency_names.get(currency_code, currency_code)


class NowPaymentsCreateView(APIView):
    """Create a new payment with NowPayments."""

    permission_classes = [IsAuthenticated]

    def _is_stablecoin(self, currency):
        """
        检查币种是否为稳定币
        基于币种名称模式识别稳定币
        """
        currency_upper = currency.upper().replace(' ', '').replace('-', '')

        # 稳定币识别模式
        stablecoin_patterns = [
            'USDT',    # Tether USD
            'USDC',    # USD Coin
            'FDUSD',   # First Digital USD
            'DAI',     # MakerDAO DAI
            'BUSD',    # Binance USD
            'TUSD',    # TrueUSD
            'USDD',    # USDD
            'USDJ',    # USDJ
            'USDR',    # USDR
            'USDP',    # Pax Dollar
        ]

        # 检查是否匹配任何稳定币模式
        for pattern in stablecoin_patterns:
            if pattern in currency_upper:
                return True

        return False

    def post(self, request):
        """Create a new payment."""
        try:
            # Get request data
            price_amount = request.data.get('price_amount')
            price_currency = request.data.get('price_currency', 'USD')
            pay_currency = request.data.get('pay_currency', 'BTC')
            order_id = request.data.get('order_id')
            order_description = request.data.get('order_description', '')

            # Validate required fields
            if not price_amount or not order_id:
                return Response({
                    'success': False,
                    'error': 'price_amount and order_id are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查是否为稳定币支付，如果是则使用精确支付模式
            is_stablecoin = self._is_stablecoin(pay_currency)

            # 对于稳定币，如果价格货币是USD，使用精确支付模式避免汇率费用
            use_exact_payment = is_stablecoin and price_currency.upper() == 'USD'

            # 🔧 修复：检查是否已有该订单和币种的有效支付记录，并处理过期订单
            from apps.payments.models import NowPaymentsPayment
            from django.utils import timezone
            from datetime import timedelta

            try:
                # 查找现有的有效支付记录
                existing_payment = NowPaymentsPayment.objects.filter(
                    order_id=order_id,
                    pay_currency=pay_currency,
                    payment_status__in=['waiting', 'confirming', 'confirmed', 'sending', 'partially_paid']
                ).order_by('-created_at').first()

                if existing_payment:
                    # 检查支付是否过期（超过30分钟）
                    expiry_time = existing_payment.created_at + timedelta(minutes=30)
                    if timezone.now() > expiry_time:
                        logger.info(f"Payment {existing_payment.payment_id} has expired, marking as expired")
                        existing_payment.payment_status = 'expired'
                        existing_payment.save()
                        existing_payment = None  # 重置为None，创建新支付
                    else:
                        logger.info(f"Found valid existing payment for order {order_id} with currency {pay_currency}: {existing_payment.payment_id}")

                # 如果找到现有有效支付，返回现有支付信息
                if existing_payment:
                    # 生成二维码
                qr_code_data = None
                try:
                    import qrcode
                    import io
                    import base64

                    # 根据币种创建正确的支付URI
                    pay_currency = existing_payment.pay_currency.upper()
                    pay_address = existing_payment.pay_address
                    pay_amount = existing_payment.pay_amount

                    # 🔍 DEBUG: 打印URI生成信息
                    print(f"🔍 DEBUG: 生成二维码 - 币种: {pay_currency}, 地址: {pay_address}, 金额: {pay_amount}")

                    # 根据币种格式化金额，优化主流钱包兼容性
                    def format_amount(currency, amount):
                        """
                        根据币种格式化金额，确保MetaMask、Trust Wallet等主流钱包兼容性

                        主流钱包兼容性要求：
                        1. 避免过多尾随零（影响显示）
                        2. 保持合理的精度（避免精度丢失）
                        3. 使用标准小数格式（不使用科学计数法）
                        """
                        try:
                            amount_float = float(amount)

                            # 避免极小金额的科学计数法
                            if amount_float == 0:
                                return "0"

                            if currency == 'BTC':
                                # Bitcoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'ETH':
                                # Ethereum: 最多18位小数，但限制显示精度避免过长
                                if amount_float >= 1:
                                    # 大于1 ETH，显示6位小数
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    # 小于1 ETH，显示更多精度但不超过12位
                                    formatted = f"{amount_float:.12f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency in ['USDTTRC20', 'USDTERC20', 'USDCMATIC']:
                                # 稳定币: 6位小数，移除尾随零
                                formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'LTC':
                                # Litecoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'SOL':
                                # Solana: 9位小数，但限制显示精度
                                if amount_float >= 1:
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    formatted = f"{amount_float:.9f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'DOGE':
                                # Dogecoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency in ['BNBBSC', 'FDUSDBSC']:
                                # BSC代币: 18位小数，但限制显示精度
                                if amount_float >= 1:
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    formatted = f"{amount_float:.12f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            else:
                                # 未知币种: 保持原格式但确保是字符串
                                return str(amount)

                        except (ValueError, TypeError):
                            return str(amount)  # 如果转换失败，保持原格式

                    formatted_amount = format_amount(pay_currency, pay_amount)
                    print(f"🔍 DEBUG: 格式化后金额: {formatted_amount}")

                    # 生成钱包兼容的URI格式 - 优先考虑主流钱包兼容性
                    def generate_wallet_compatible_uri(currency, address, amount):
                        """
                        生成主流钱包兼容的URI格式
                        优先级：MetaMask > Trust Wallet > 币安钱包 > 其他
                        """
                        currency = currency.upper()

                        if currency == 'BTC':
                            # Bitcoin标准URI - 所有钱包都支持
                            return f"bitcoin:{address}?amount={amount}"

                        elif currency == 'ETH':
                            # Ethereum原生币 - 使用简单格式，最大兼容性
                            return f"ethereum:{address}?value={amount}"

                        elif currency == 'USDTTRC20':
                            # USDT TRC20 - TRON网络，TronLink等钱包支持
                            return f"tron:{address}?amount={amount}"

                        elif currency in ['USDTERC20', 'USDC', 'DAI']:
                            # ERC20代币 - 使用简化格式，避免复杂的合约调用
                            # 大多数钱包会自动识别代币类型
                            return f"ethereum:{address}?amount={amount}"

                        elif currency == 'USDCMATIC':
                            # USDC Polygon - 使用简化格式
                            return f"ethereum:{address}?amount={amount}"

                        elif currency in ['FDUSDBSC', 'USDTBSC']:
                            # BSC代币 - 使用简化格式
                            return f"ethereum:{address}?amount={amount}"

                        elif currency == 'BNBBSC':
                            # BNB BSC - 原生币格式
                            return f"ethereum:{address}?value={amount}"

                        elif currency == 'LTC':
                            # Litecoin标准URI
                            return f"litecoin:{address}?amount={amount}"

                        elif currency == 'SOL':
                            # Solana标准URI
                            return f"solana:{address}?amount={amount}"

                        elif currency == 'DOGE':
                            # Dogecoin标准URI
                            return f"dogecoin:{address}?amount={amount}"

                        else:
                            # 未知币种 - 只返回地址，让用户手动输入金额
                            return address

                    # 生成主要URI（简化格式，最大兼容性）
                    payment_uri = generate_wallet_compatible_uri(pay_currency, pay_address, formatted_amount)

                    # 🔍 DEBUG: 打印生成的URI
                    print(f"🔍 DEBUG: 生成的支付URI: {payment_uri}")
                    print(f"🔍 DEBUG: URI长度: {len(payment_uri)}")

                    # 生成多种格式的URI以提高钱包兼容性
                    uri_formats = {
                        'standard': payment_uri,
                        'address_only': pay_address,  # 纯地址，适用于所有钱包
                    }

                    # 为特定币种生成额外的兼容格式
                    if pay_currency.upper() in ['USDTERC20', 'USDCMATIC', 'FDUSDBSC']:
                        # 为ERC20代币添加简化格式
                        uri_formats['simple'] = f"ethereum:{pay_address}"

                    # 标记需要前端创建WalletConnect v2会话
                    def generate_walletconnect_placeholder(currency, address, amount):
                        # 不再在后端生成假的URI，而是返回一个标记
                        # 前端会检测到这个标记并创建真正的WalletConnect v2会话

                        # 根据币种确定链ID
                        chain_configs = {
                            'DAI': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDC': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDTERC20': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDCMATIC': {'chainId': 137, 'name': 'Polygon Mainnet'},
                            'FDUSDBSC': {'chainId': 56, 'name': 'BNB Smart Chain'},
                            'USDTBSC': {'chainId': 56, 'name': 'BNB Smart Chain'},
                        }

                        config = chain_configs.get(currency.upper(), {'chainId': 1, 'name': 'Ethereum Mainnet'})

                        # 返回一个特殊的标记，告诉前端需要创建真正的WalletConnect会话
                        placeholder_uri = f"FRONTEND_CREATE_WC_SESSION:{currency}:{address}:{amount}:{config['chainId']}"

                        # 🔍 DEBUG: 打印信息
                        print(f"🔍 DEBUG: 生成WalletConnect占位符")
                        print(f"🔍 DEBUG: Chain: {config['name']} (ID: {config['chainId']})")
                        print(f"🔍 DEBUG: Currency: {currency}, Amount: {amount}, Address: {address}")
                        print(f"🔍 DEBUG: Placeholder: {placeholder_uri}")

                        return placeholder_uri

                    # 生成WalletConnect占位符（前端将创建真正的会话）
                    walletconnect_uri = generate_walletconnect_placeholder(pay_currency, pay_address, pay_amount)

                    # 🔍 DEBUG: 打印生成的WalletConnect URI
                    print(f"🔍 DEBUG: 生成的WalletConnect URI: {walletconnect_uri[:50]}...")
                    print(f"🔍 DEBUG: WalletConnect URI长度: {len(walletconnect_uri)}")

                    # 生成支付二维码（使用支付URI而不是WalletConnect URI）
                    qr = qrcode.QRCode(
                        version=1,          # 自动选择版本
                        error_correction=qrcode.constants.ERROR_CORRECT_M,  # 中等容错级别
                        box_size=10,        # 每个方块的像素大小
                        border=4,           # 边框大小
                    )
                    # 🔍 DEBUG: 使用支付URI而不是WalletConnect URI
                    print(f"🔍 DEBUG: 现有支付二维码使用支付URI: {payment_uri}")
                    qr.add_data(payment_uri)
                    qr.make(fit=True)

                    # 创建二维码图片
                    img = qr.make_image(fill_color="black", back_color="white")

                    # 转换为base64
                    buffer = io.BytesIO()
                    img.save(buffer, format='PNG')
                    qr_code_data = base64.b64encode(buffer.getvalue()).decode()

                    # 生成备用二维码（纯地址）
                    qr_address = qrcode.QRCode(
                        version=1,
                        error_correction=qrcode.constants.ERROR_CORRECT_M,
                        box_size=10,
                        border=4,
                    )
                    qr_address.add_data(pay_address)
                    qr_address.make(fit=True)

                    img_address = qr_address.make_image(fill_color="black", back_color="white")
                    buffer_address = io.BytesIO()
                    img_address.save(buffer_address, format='PNG')
                    qr_code_address_only = base64.b64encode(buffer_address.getvalue()).decode()

                except ImportError:
                    logger.warning("qrcode library not installed, QR code will not be generated")
                    qr_code_data = None
                    qr_code_address_only = None
                except Exception as e:
                    logger.error(f"Error generating QR code: {e}")
                    qr_code_data = None
                    qr_code_address_only = None

                return Response({
                    'success': True,
                    'data': {
                        'payment_id': existing_payment.payment_id,
                        'pay_address': existing_payment.pay_address,
                        'pay_amount': existing_payment.pay_amount,
                        'pay_currency': existing_payment.pay_currency,
                        'price_amount': existing_payment.price_amount,
                        'price_currency': existing_payment.price_currency,
                        'order_id': existing_payment.order_id,
                        'payment_status': existing_payment.payment_status,
                        'qr_code': qr_code_data,
                        'qr_code_address_only': qr_code_address_only if 'qr_code_address_only' in locals() else None,
                        'payment_uri': payment_uri if 'payment_uri' in locals() else None,
                        'walletconnect_uri': walletconnect_uri if 'walletconnect_uri' in locals() else None,
                        'payment_url': existing_payment.payment_url,
                        'wallet_compatibility': {
                            'metamask': 'supported' if pay_currency.upper() in ['ETH', 'USDTERC20', 'USDC', 'DAI'] else 'limited',
                            'trust_wallet': 'supported',
                            'binance_wallet': 'supported' if pay_currency.upper() in ['BTC', 'ETH', 'BNBBSC'] else 'limited'
                        },
                        'is_existing': True  # 标记这是现有支付
                    }
                }, status=status.HTTP_200_OK)

            except NowPaymentsPayment.DoesNotExist:
                # 没有找到现有支付，继续创建新支付
                pass
            except Exception as db_error:
                if 'no such table' in str(db_error).lower():
                    # 表不存在，继续创建新支付
                    pass
                else:
                    logger.error(f"Database error checking existing payment: {db_error}")
                    # 继续创建新支付，不因为数据库错误而失败

            # Initialize NowPayments client
            client = NowPaymentsClient()

            # Create payment
            payment_data = {
                'price_amount': float(price_amount),
                'price_currency': price_currency,
                'pay_currency': pay_currency,
                'order_id': str(order_id),
                'order_description': order_description,
                'ipn_callback_url': f"{request.build_absolute_uri('/').rstrip('/')}/api/v1/payments/nowpayments/ipn/",
                'success_url': request.data.get('success_url', ''),
                'cancel_url': request.data.get('cancel_url', ''),
            }

            # 调用真实的NowPayments API
            from django.conf import settings

            # 构建回调URL
            site_url = getattr(settings, 'SITE_URL', 'http://localhost:8000')
            ipn_callback_url = f"{site_url}/api/v1/payments/nowpayments/callback/"
            success_url = f"{site_url}/orders/?payment=success"
            cancel_url = f"{site_url}/orders/?payment=cancelled"

            # 获取自动转换配置
            payout_address = getattr(settings, 'NOWPAYMENTS_PAYOUT_ADDRESS', '')
            payout_currency = getattr(settings, 'NOWPAYMENTS_PAYOUT_CURRENCY', '')

            try:
                # 准备基本参数
                if use_exact_payment:
                    # 稳定币精确支付模式：使用pay_amount避免汇率费用
                    payment_params = {
                        'pay_amount': price_amount,  # 用户支付的精确稳定币金额
                        'pay_currency': pay_currency,
                        'order_id': order_id,
                        'order_description': order_description,
                        'ipn_callback_url': ipn_callback_url,
                        'success_url': success_url,
                        'cancel_url': cancel_url,
                    }
                    logger.info(f"🔍 使用稳定币精确支付模式: {pay_currency} {price_amount}")
                else:
                    # 传统模式：使用price_amount进行汇率转换
                    payment_params = {
                        'price_amount': price_amount,
                        'price_currency': price_currency,
                        'pay_currency': pay_currency,
                        'order_id': order_id,
                        'order_description': order_description,
                        'ipn_callback_url': ipn_callback_url,
                        'success_url': success_url,
                        'cancel_url': cancel_url,
                    }
                    logger.info(f"🔍 使用传统汇率转换模式: {price_currency} {price_amount} -> {pay_currency}")

                # 只有在配置了payout时才添加payout参数
                if payout_address and payout_currency:
                    payment_params['payout_address'] = payout_address
                    payment_params['payout_currency'] = payout_currency

                # 🔍 DEBUG: 打印即将发送到NOWPayments的payload
                logger.info(f"🔍 DEBUG: 即将发送到NOWPayments的Payload: {payment_params}")
                print(f"🔍 DEBUG: 即将发送到NOWPayments的Payload: {payment_params}")

                # 调用真实的NowPayments API
                response = client.create_payment(**payment_params)

                # 确保响应格式正确
                if 'payment_id' in response:
                    # NowPayments直接返回数据，需要包装成我们的格式
                    response = {
                        'success': True,
                        'data': response
                    }

            except Exception as e:
                logger.error(f"NowPayments API error: {e}")

                # 检查是否是最小金额错误
                if 'AMOUNT_MINIMAL_ERROR' in str(e) or 'is less than minimal' in str(e):
                    return Response({
                        'success': False,
                        'error': f'Payment amount is too small for {pay_currency}. Please try a larger amount or different cryptocurrency.',
                        'error_type': 'amount_minimal_error',
                        'technical_details': str(e)
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 检查是否是SSL/网络连接问题
                elif 'SSL' in str(e) or 'Connection' in str(e):
                    return Response({
                        'success': False,
                        'error': 'Unable to connect to payment service. This may be due to network restrictions or SSL configuration issues. Please try again later or contact support.',
                        'error_type': 'connection_error',
                        'technical_details': str(e)
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                else:
                    return Response({
                        'success': False,
                        'error': f'Payment service error: {str(e)}',
                        'error_type': 'api_error'
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            if response.get('success'):
                # Save payment to database
                from apps.payments.models import NowPaymentsPayment
                from django.db import connection

                # Check if table exists and create if needed
                try:
                    payment = NowPaymentsPayment.objects.create(
                        user=request.user,
                        payment_id=response['data']['payment_id'],
                        order_id=order_id,
                        price_amount=price_amount,
                        price_currency=price_currency,
                        pay_currency=pay_currency,
                        pay_amount=response['data'].get('pay_amount'),
                        pay_address=response['data'].get('pay_address'),
                        payment_status='waiting',
                        order_description=order_description,
                        created_at=timezone.now()
                    )
                except Exception as db_error:
                    if 'no such table' in str(db_error).lower():
                        # Create table manually
                        with connection.cursor() as cursor:
                            cursor.execute('''
                                CREATE TABLE IF NOT EXISTS payments_nowpayments_payment (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    created_at DATETIME NOT NULL,
                                    updated_at DATETIME NOT NULL,
                                    is_deleted BOOLEAN NOT NULL DEFAULT 0,
                                    deleted_at DATETIME,
                                    payment_id VARCHAR(100) NOT NULL UNIQUE,
                                    order_id VARCHAR(100) NOT NULL,
                                    order_description TEXT,
                                    price_amount DECIMAL(10,2) NOT NULL,
                                    price_currency VARCHAR(10) NOT NULL DEFAULT 'USD',
                                    pay_amount DECIMAL(20,8),
                                    pay_currency VARCHAR(10) NOT NULL,
                                    pay_address VARCHAR(200),
                                    payment_status VARCHAR(50) NOT NULL DEFAULT 'waiting',
                                    actually_paid DECIMAL(20,8),
                                    valid_until DATETIME,
                                    payment_url TEXT,
                                    raw_response_encrypted TEXT,
                                    user_id INTEGER NOT NULL,
                                    FOREIGN KEY (user_id) REFERENCES users_user (id) ON DELETE CASCADE
                                );
                            ''')
                            cursor.execute('''
                                INSERT OR IGNORE INTO django_migrations (app, name, applied)
                                VALUES ('payments', '0002_nowpaymentspayment', datetime('now'))
                            ''')

                        # Try creating the payment again
                        payment = NowPaymentsPayment.objects.create(
                            user=request.user,
                            payment_id=response['data']['payment_id'],
                            order_id=order_id,
                            price_amount=price_amount,
                            price_currency=price_currency,
                            pay_currency=pay_currency,
                            pay_amount=response['data'].get('pay_amount'),
                            pay_address=response['data'].get('pay_address'),
                            payment_status='waiting',
                            order_description=order_description,
                            created_at=timezone.now()
                        )
                    else:
                        raise db_error

                logger.info(f"Payment created successfully: {payment.payment_id}")

                # 生成二维码
                qr_code_data = None
                qr_code_address_only = None
                try:
                    import qrcode
                    import io
                    import base64

                    # 根据币种创建正确的支付URI
                    pay_address = response['data'].get('pay_address')
                    pay_amount = response['data'].get('pay_amount')
                    currency = pay_currency.upper()

                    # 🔍 DEBUG: 打印URI生成信息
                    print(f"🔍 DEBUG: 新支付生成二维码 - 币种: {currency}, 地址: {pay_address}, 金额: {pay_amount}")

                    # 根据币种格式化金额，优化主流钱包兼容性
                    def format_amount(currency, amount):
                        """
                        根据币种格式化金额，确保MetaMask、Trust Wallet等主流钱包兼容性

                        主流钱包兼容性要求：
                        1. 避免过多尾随零（影响显示）
                        2. 保持合理的精度（避免精度丢失）
                        3. 使用标准小数格式（不使用科学计数法）
                        """
                        try:
                            amount_float = float(amount)

                            # 避免极小金额的科学计数法
                            if amount_float == 0:
                                return "0"

                            if currency == 'BTC':
                                # Bitcoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'ETH':
                                # Ethereum: 最多18位小数，但限制显示精度避免过长
                                if amount_float >= 1:
                                    # 大于1 ETH，显示6位小数
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    # 小于1 ETH，显示更多精度但不超过12位
                                    formatted = f"{amount_float:.12f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency in ['USDTTRC20', 'USDTERC20', 'USDCMATIC']:
                                # 稳定币: 6位小数，移除尾随零
                                formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'LTC':
                                # Litecoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'SOL':
                                # Solana: 9位小数，但限制显示精度
                                if amount_float >= 1:
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    formatted = f"{amount_float:.9f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency == 'DOGE':
                                # Dogecoin: 8位小数，移除尾随零
                                formatted = f"{amount_float:.8f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            elif currency in ['BNBBSC', 'FDUSDBSC']:
                                # BSC代币: 18位小数，但限制显示精度
                                if amount_float >= 1:
                                    formatted = f"{amount_float:.6f}".rstrip('0').rstrip('.')
                                else:
                                    formatted = f"{amount_float:.12f}".rstrip('0').rstrip('.')
                                return formatted if formatted else "0"

                            else:
                                # 未知币种: 保持原格式但确保是字符串
                                return str(amount)

                        except (ValueError, TypeError):
                            return str(amount)  # 如果转换失败，保持原格式

                    formatted_amount = format_amount(currency, pay_amount)
                    print(f"🔍 DEBUG: 新支付格式化后金额: {formatted_amount}")

                    # 生成钱包兼容的URI格式 - 优先考虑主流钱包兼容性
                    def generate_wallet_compatible_uri(currency, address, amount):
                        """
                        生成主流钱包兼容的URI格式
                        优先级：MetaMask > Trust Wallet > 币安钱包 > 其他
                        """
                        currency = currency.upper()

                        if currency == 'BTC':
                            # Bitcoin标准URI - 所有钱包都支持
                            return f"bitcoin:{address}?amount={amount}"

                        elif currency == 'ETH':
                            # Ethereum原生币 - 使用简单格式，最大兼容性
                            return f"ethereum:{address}?value={amount}"

                        elif currency == 'USDTTRC20':
                            # USDT TRC20 - TRON网络，TronLink等钱包支持
                            return f"tron:{address}?amount={amount}"

                        elif currency in ['USDTERC20', 'USDC', 'DAI']:
                            # ERC20代币 - 使用简化格式，避免复杂的合约调用
                            # 大多数钱包会自动识别代币类型
                            return f"ethereum:{address}?amount={amount}"

                        elif currency == 'USDCMATIC':
                            # USDC Polygon - 使用简化格式
                            return f"ethereum:{address}?amount={amount}"

                        elif currency in ['FDUSDBSC', 'USDTBSC']:
                            # BSC代币 - 使用简化格式
                            return f"ethereum:{address}?amount={amount}"

                        elif currency == 'BNBBSC':
                            # BNB BSC - 原生币格式
                            return f"ethereum:{address}?value={amount}"

                        elif currency == 'LTC':
                            # Litecoin标准URI
                            return f"litecoin:{address}?amount={amount}"

                        elif currency == 'SOL':
                            # Solana标准URI
                            return f"solana:{address}?amount={amount}"

                        elif currency == 'DOGE':
                            # Dogecoin标准URI
                            return f"dogecoin:{address}?amount={amount}"

                        else:
                            # 未知币种 - 只返回地址，让用户手动输入金额
                            return address

                    # 生成主要URI（简化格式，最大兼容性）
                    payment_uri = generate_wallet_compatible_uri(currency, pay_address, formatted_amount)

                    # 🔍 DEBUG: 打印生成的URI
                    print(f"🔍 DEBUG: 新支付生成的支付URI: {payment_uri}")

                    # 生成多种格式的URI以提高钱包兼容性
                    uri_formats = {
                        'standard': payment_uri,
                        'address_only': pay_address,  # 纯地址，适用于所有钱包
                    }

                    # 为特定币种生成额外的兼容格式
                    if currency.upper() in ['USDTERC20', 'USDCMATIC', 'FDUSDBSC']:
                        # 为ERC20代币添加简化格式
                        uri_formats['simple'] = f"ethereum:{pay_address}"

                    # 生成WalletConnect占位符
                    def generate_walletconnect_placeholder(currency, address, amount):
                        # 不再在后端生成假的URI，而是返回一个标记
                        # 前端会检测到这个标记并创建真正的WalletConnect v2会话

                        # 根据币种确定链ID
                        chain_configs = {
                            'DAI': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDC': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDTERC20': {'chainId': 1, 'name': 'Ethereum Mainnet'},
                            'USDCMATIC': {'chainId': 137, 'name': 'Polygon Mainnet'},
                            'FDUSDBSC': {'chainId': 56, 'name': 'BNB Smart Chain'},
                            'USDTBSC': {'chainId': 56, 'name': 'BNB Smart Chain'},
                        }

                        config = chain_configs.get(currency.upper(), {'chainId': 1, 'name': 'Ethereum Mainnet'})

                        # 返回一个特殊的标记，告诉前端需要创建真正的WalletConnect会话
                        placeholder_uri = f"FRONTEND_CREATE_WC_SESSION:{currency}:{address}:{amount}:{config['chainId']}"

                        # 🔍 DEBUG: 打印信息
                        print(f"🔍 DEBUG: 新支付生成WalletConnect占位符")
                        print(f"🔍 DEBUG: Chain: {config['name']} (ID: {config['chainId']})")
                        print(f"🔍 DEBUG: Currency: {currency}, Amount: {amount}, Address: {address}")
                        print(f"🔍 DEBUG: Placeholder: {placeholder_uri}")

                        return placeholder_uri

                    walletconnect_uri = generate_walletconnect_placeholder(currency, pay_address, formatted_amount)

                    # 🔍 DEBUG: 打印生成的WalletConnect URI
                    print(f"🔍 DEBUG: 新支付生成的WalletConnect URI: {walletconnect_uri[:50]}...")
                    print(f"🔍 DEBUG: 新支付WalletConnect URI长度: {len(walletconnect_uri)}")

                    # 🔧 修改：先生成支付二维码，前端会用WalletConnect URI替换
                    qr = qrcode.QRCode(
                        version=1,          # 自动选择版本
                        error_correction=qrcode.constants.ERROR_CORRECT_M,  # 中等容错级别
                        box_size=10,        # 每个方块的像素大小
                        border=4,           # 边框大小
                    )
                    # 🔧 先使用支付URI，前端会替换为WalletConnect URI
                    print(f"🔍 DEBUG: 新支付二维码先使用支付URI，前端会替换: {payment_uri}")
                    qr.add_data(payment_uri)
                    qr.make(fit=True)

                    # 创建二维码图片
                    img = qr.make_image(fill_color="black", back_color="white")

                    # 转换为base64
                    buffer = io.BytesIO()
                    img.save(buffer, format='PNG')
                    qr_code_data = base64.b64encode(buffer.getvalue()).decode()

                    # 生成备用二维码（纯地址）
                    qr_address = qrcode.QRCode(
                        version=1,
                        error_correction=qrcode.constants.ERROR_CORRECT_M,
                        box_size=10,
                        border=4,
                    )
                    qr_address.add_data(pay_address)
                    qr_address.make(fit=True)

                    img_address = qr_address.make_image(fill_color="black", back_color="white")
                    buffer_address = io.BytesIO()
                    img_address.save(buffer_address, format='PNG')
                    qr_code_address_only = base64.b64encode(buffer_address.getvalue()).decode()

                except ImportError as e:
                    logger.warning(f"qrcode library not installed, QR code will not be generated: {e}")
                    qr_code_data = None
                    qr_code_address_only = None
                except Exception as e:
                    logger.error(f"❌ Error generating QR code for new payment: {e}")
                    logger.error(f"❌ Exception type: {type(e).__name__}")
                    import traceback
                    logger.error(f"❌ Traceback: {traceback.format_exc()}")
                    qr_code_data = None
                    qr_code_address_only = None

                # 🔍 DEBUG: 打印二维码生成结果
                logger.info(f"🔍 DEBUG: 新支付二维码生成结果 - qr_code_data: {qr_code_data is not None}, qr_code_address_only: {qr_code_address_only is not None}")
                if qr_code_data:
                    logger.info(f"🔍 DEBUG: 二维码数据长度: {len(qr_code_data)}")
                else:
                    logger.error(f"❌ DEBUG: 新支付二维码生成失败 - qr_code_data为None")

                return Response({
                    'success': True,
                    'data': {
                        'payment_id': payment.payment_id,
                        'pay_address': response['data'].get('pay_address'),
                        'pay_amount': response['data'].get('pay_amount'),
                        'pay_currency': pay_currency,
                        'price_amount': price_amount,
                        'price_currency': price_currency,
                        'order_id': order_id,
                        'payment_status': 'waiting',
                        'qr_code': qr_code_data,
                        'qr_code_address_only': qr_code_address_only if 'qr_code_address_only' in locals() else None,
                        'payment_uri': payment_uri if 'payment_uri' in locals() else None,
                        'walletconnect_uri': walletconnect_uri if 'walletconnect_uri' in locals() else None,
                        'payment_url': response['data'].get('payment_url'),
                        'wallet_compatibility': {
                            'metamask': 'supported' if currency.upper() in ['ETH', 'USDTERC20', 'USDC', 'DAI'] else 'limited',
                            'trust_wallet': 'supported',
                            'binance_wallet': 'supported' if currency.upper() in ['BTC', 'ETH', 'BNBBSC'] else 'limited'
                        },
                    }
                }, status=status.HTTP_201_CREATED)
            else:
                logger.error(f"Failed to create payment: {response}")
                return Response({
                    'success': False,
                    'error': response.get('error', 'Failed to create payment')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error creating payment: {e}")
            return Response({
                'success': False,
                'error': 'Failed to create payment'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NowPaymentsStatusView(APIView):
    """Get payment status from NowPayments."""

    permission_classes = [IsAuthenticated]

    def get(self, request, payment_id):
        """Get payment status."""
        try:
            # Get payment from database
            from apps.payments.models import NowPaymentsPayment

            try:
                # 管理员可以查看所有支付，普通用户只能查看自己的支付
                if request.user.is_staff or request.user.is_superuser:
                    payment = NowPaymentsPayment.objects.get(payment_id=payment_id)
                else:
                    payment = NowPaymentsPayment.objects.get(
                        payment_id=payment_id,
                        user=request.user
                    )
            except NowPaymentsPayment.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Payment not found'
                }, status=status.HTTP_404_NOT_FOUND)
            except Exception as db_error:
                if 'no such table' in str(db_error).lower():
                    return Response({
                        'success': False,
                        'error': 'Payment table not found. Please create a payment first.'
                    }, status=status.HTTP_404_NOT_FOUND)
                else:
                    raise db_error

            # Initialize NowPayments client
            client = NowPaymentsClient()

            # Get payment status from NowPayments
            response = client.get_payment_status(payment_id)

            if response.get('success'):
                # Update local payment status
                payment_data = response['data']
                payment.payment_status = payment_data.get('payment_status', payment.payment_status)
                payment.pay_amount = payment_data.get('pay_amount', payment.pay_amount)
                if hasattr(payment, 'actually_paid'):
                    payment.actually_paid = payment_data.get('actually_paid', payment.actually_paid)
                payment.save()

                return Response({
                    'success': True,
                    'data': {
                        'payment_id': payment.payment_id,
                        'payment_status': payment.payment_status,
                        'pay_address': payment.pay_address,
                        'pay_amount': payment.pay_amount,
                        'pay_currency': payment.pay_currency,
                        'price_amount': payment.price_amount,
                        'price_currency': payment.price_currency,
                        'order_id': payment.order_id,
                        'actually_paid': getattr(payment, 'actually_paid', None),
                        'created_at': payment.created_at,
                        'updated_at': payment.updated_at,
                    }
                }, status=status.HTTP_200_OK)
            else:
                logger.error(f"Failed to get payment status: {response}")
                return Response({
                    'success': False,
                    'error': response.get('error', 'Failed to get payment status')
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error getting payment status: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return Response({
                'success': False,
                'error': f'Failed to get payment status: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class NowPaymentsCurrenciesAPIView(APIView):
    """API endpoint to get supported currencies from NowPayments."""

    def get(self, request):
        """Get list of supported currencies from NowPayments."""
        try:
            # Get NowPayments payment method
            try:
                payment_method = PaymentMethod.objects.get(
                    code='nowpayments',
                    is_active=True
                )
            except PaymentMethod.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'NowPayments payment method not found or not active'
                }, status=status.HTTP_404_NOT_FOUND)

            # Initialize NowPayments client
            client = NowPaymentsClient(
                api_key=payment_method.config.get('api_key'),
                sandbox=payment_method.config.get('sandbox', False)
            )

            # Get merchant configured currencies
            currencies_response = client.get_merchant_coins()

            if not currencies_response.get('success'):
                return Response({
                    'success': False,
                    'error': 'Failed to get currencies from NowPayments'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            currencies = currencies_response.get('data', {}).get('selectedCurrencies', [])

            logger.info(f"Retrieved {len(currencies)} currencies from NowPayments")

            # 🔧 修复故障D：为每个币种添加icon_class字段
            processed_currencies = []
            for currency in currencies:
                currency_code = currency.get('code', '')
                processed_currency = {
                    'code': currency_code,
                    'name': currency.get('name', currency_code),
                    'is_stablecoin': currency.get('is_stablecoin', self._is_stablecoin_static(currency_code)),
                    'icon_class': self._get_currency_icon(currency_code)  # 添加图标类
                }
                # 保留原始数据的其他字段
                for key, value in currency.items():
                    if key not in processed_currency:
                        processed_currency[key] = value

                processed_currencies.append(processed_currency)

            return Response({
                'success': True,
                'currencies': processed_currencies,
                'count': len(processed_currencies)
            })

        except Exception as e:
            logger.error(f"Error getting NowPayments currencies: {str(e)}")
            return Response({
                'success': False,
                'error': f'Failed to get currencies: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RegenerateQRCodeView(APIView):
    """重新生成二维码的API端点"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        重新生成包含真正WalletConnect URI的二维码

        请求参数:
        - payment_id: 支付ID
        - walletconnect_uri: 真正的WalletConnect URI
        """
        try:
            payment_id = request.data.get('payment_id')
            walletconnect_uri = request.data.get('walletconnect_uri')

            if not payment_id:
                return Response({
                    'success': False,
                    'error': 'payment_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            if not walletconnect_uri:
                return Response({
                    'success': False,
                    'error': 'walletconnect_uri is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证WalletConnect URI格式
            if not walletconnect_uri.startswith('wc:'):
                return Response({
                    'success': False,
                    'error': 'Invalid WalletConnect URI format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 查找支付记录
            try:
                payment = NowPaymentsPayment.objects.get(
                    payment_id=payment_id,
                    user=request.user
                )
            except NowPaymentsPayment.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Payment not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # 生成新的二维码
            import qrcode
            import io
            import base64

            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=10,
                border=4,
            )
            qr.add_data(walletconnect_uri)
            qr.make(fit=True)

            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            qr_code_data = base64.b64encode(buffer.getvalue()).decode()

            # 🔍 DEBUG: 打印信息
            print(f"🔍 DEBUG: 重新生成二维码")
            print(f"🔍 DEBUG: Payment ID: {payment_id}")
            print(f"🔍 DEBUG: WalletConnect URI: {walletconnect_uri[:50]}...")
            print(f"🔍 DEBUG: QR Code length: {len(qr_code_data)}")

            return Response({
                'success': True,
                'data': {
                    'payment_id': payment_id,
                    'qr_code': qr_code_data,
                    'walletconnect_uri': walletconnect_uri
                }
            })

        except Exception as e:
            logger.error(f"Error regenerating QR code: {str(e)}")
            return Response({
                'success': False,
                'error': f'Failed to regenerate QR code: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
